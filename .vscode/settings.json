{"editor.defaultFormatter": "dbaeumer.vscode-eslint", "eslint.format.enable": true, "eslint.validate": ["javascript", "typescript", "vue"], "[jsonc]": {"editor.defaultFormatter": "vscode.json-language-features"}, "i18n-ally.enabledFrameworks": ["vue"], "i18n-ally.displayLanguage": "zh-TW", "i18n-ally.sourceLanguage": "zh-TW", "i18n-ally.extract.autoDetect": true, "i18n-ally.extract.keygenStyle": "camelCase", "i18n-ally.pathMatcher": "{namespace}/{locale}.json", "i18n-ally.namespace": true, "i18n-ally.localesPaths": ["src/locales"], "i18n-ally.sortKeys": true, "i18n-ally.keepFulfilled": true, "i18n-ally.dirStructure": "dir", "i18n-ally.keystyle": "nested", "vue.suggest.componentNameCasing": "alwaysPascalCase"}