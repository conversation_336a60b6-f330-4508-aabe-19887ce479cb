const functionNameWfgIdMap = {
	// 系統角色權限審核
	admRole: 'WFG20121005008',
	// 系統使用者管理審核
	userAccount: 'WFG20141112001',
	// 活動審核
	mktCamp: 'WFG20241118001',
	// 商品資料審核
	proMgt: 'WFG20241203001',
	// 新商品臨時上架審核
	proNew: 'WFG20241203002',
	// 精選推薦商品審核
	proPfcats: 'WFG20241203003'
};

const wkf = Object.keys(functionNameWfgIdMap).map(functionName => ({
	path: `/wkf/${functionName}`,
	redirect() {
		const wfgId = functionNameWfgIdMap[functionName];
		// 如果 functionName 有對應的 wfgId，則重定向到 wkfProcessor
		if (wfgId) return { name: `wkfProcessor-${functionName}`, params: { wfgId } };
		// 否則返回 401 頁面
		else return { name: '401' };
	},
	children: [
		{
			path: `:wfgId`,
			name: `wkfProcessor-${functionName}`,
			component: () => import('../views/wkf/wkfProcessor.vue'),
			beforeEnter: (to) => {
				const allowedId = functionNameWfgIdMap[functionName]; // 合法的 wfgId
				const wfgId = to.params.wfgId;
				if (allowedId !== wfgId) return { name: '401' };
			}
		}
	]
}));

export default wkf;
