const adm = [
	{
		// 系統功能選單設定
		path: 'adm/programSet',
		name: 'programSet',
		component: () => import('../views/adm/ADM0110/programSet.vue')
	},
	{
		// 系統角色權限維護
		path: 'adm/admRole',
		name: 'admRole',
		component: () => import('../views/adm/ADM0101/admRole.vue')
	},
	{
		// 系統角色權限維護
		path: '/adm/admRole/AdmRoleAuthority/:rolecode',
		name: 'admRoleAuthority',
		component: () => import('../views/adm/ADM0101/include/admRoleAuthority.vue')
	},
	{
		// 系統選單預覽
		path: '/adm/admMenuPreview',
		name: 'admMenuPreview',
		component: () => import('../views/adm/ADM0103/admMenuPreview.vue')
	},
	{
		// 系統使用者管理
		path: '/adm/userAccount',
		name: 'userAccount',
		component: () => import('../views/adm/ADM0104/userAccount.vue')
	},
	{
		// 區域人員設定
		path: '/adm/fcStep',
		name: 'fcStep',
		component: () => import('../views/adm/ADM0107/fcStep.vue')
	},
	{
		// 分行分區設定
		path: '/adm/groupBran',
		name: 'groupBran',
		component: () => import('../views/adm/ADM0108/groupBran.vue')
	},
	// {
	//   // 事件通知參數
	//   path: '/adm/cfgWobConfig',
	//   name: 'cfgWobConfig',
	//   component: () => import('../views/adm/ADM0109/cfgWobConfig.vue')
	// },
	// {
	//   // 客戶資產等級
	//   path: '/adm/rmLevels',
	//   name: 'rmLevels',
	//   component: () => import('../views/adm/ADM0111/rmLevels.vue')
	// },
	{
		// 客戶資產等級
		path: '/adm/cfgCus',
		name: 'cfgCus',
		component: () => import('../views/adm/ADM0202/cfgCus.vue')
	},
	{
		// 事件通知狀態設定
		path: '/adm/cfgWOBTaskSetup',
		name: 'cfgWOBTaskSetup',
		component: () => import('../views/adm/ADM0203/cfgWOBTaskSetup.vue')
	},
	// {
	//   // 事件通知TOP10設定
	//   path: '/adm/top10TdItmesSetup',
	//   name: 'top10TdItmesSetup',
	//   component: () => import('../views/adm/ADM0204/top10TdItmesSetup.vue')
	// },
	{
		// 事件通知參數
		path: '/adm/cfgWobConfig',
		name: 'cfgWobConfig',
		component: () => import('../views/adm/ADM0205/cfgWobConfig.vue')
	},
	{
		// 理財規劃模組參數
		path: '/adm/cfgFPNGeneral',
		name: 'cfgFPNGeneral',
		component: () => import('../views/adm/ADM0206/cfgFPNSetting.vue')
	},
	{
		// CA管轄分行設定
		path: '/adm/cfgCa',
		name: 'cfgCa',
		meta: {
		},
		component: () => import('../views/adm/ADM0206/cfgCa.vue')
	},
	{
		// 使用者使用記錄
		path: '/adm/auditUsage',
		name: 'auditUsage',
		component: () => import('../views/adm/ADM0301/auditUsage.vue')
	},
	{
		// 分行使用紀錄
		path: '/adm/auditDeptUsage',
		name: 'auditDeptUsage',
		component: () => import('../views/adm/ADM0302/auditDeptUsage.vue')
	},
	{
		// 使用者存取客戶紀錄
		path: '/adm/auditCusAccessLogs',
		name: 'auditCusAccessLogs',
		component: () => import('../views/adm/ADM0303/auditCusAccessLogs.vue')
	},
	{
		// 使用者使用明細記錄
		path: '/adm/auditUsageDetail',
		name: 'auditUsageDetail',
		component: () => import('../views/adm/ADM0304/auditUsageDetail.vue')
	},
	{
		// 使用者資料產生記錄
		path: '/adm/auditUsageDataProduce',
		name: 'auditUsageDataProduce',
		component: () => import('../views/adm/ADM0305/auditUsageDataProduce.vue')
	},
	{
		// 代理人設定
		path: '/adm/userDeputy',
		name: 'userDeputy',
		component: () => import('../views/adm/ADM0401/userDeputy.vue')
	},
	{
		// 代理人設定紀錄
		path: '/adm/auditDeputy',
		name: 'auditDeputy',
		component: () => import('../views/adm/ADM0402/auditDeputy.vue')
	},
	{
		// 批次程式監控
		path: '/adm/batchJobStatus',
		name: 'batchJobStatus',
		component: () => import('../views/adm/ADM0501/batchJobStatus.vue')
	},
	{
		// 停機公告
		path: '/adm/stopSysPost',
		name: 'stopSysPost',
		component: () => import('../views/adm/ADM0501/stopSysPost.vue')
	},
	{
		// 批次重跑
		path: '/adm/reRunGroupView',
		name: 'reRunGroupView',
		component: () => import('../views/adm/ADM0502/reRunGroupView.vue')
	},
	{
		// 批次重跑-Job
		path: '/adm/reRunJobView',
		name: 'reRunJobView',
		component: () => import('../views/adm/ADM0502/reRunJobView.vue')
	},
	{
		// 常用句維護
		path: '/adm/reuseWords',
		name: 'reuseWords',
		component: () => import('../views/adm/ADM0207/reuseWords.vue')
	}

];

export default adm;
