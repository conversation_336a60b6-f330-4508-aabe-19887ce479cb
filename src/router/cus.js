const cus = [
	{
		// 單一條件客戶查詢
		path: '/cus/cusSearch',
		name: 'cusSearch',
		component: () => import('../views/cus/CUS0201/cusSearch.vue')
	},
	{
		// 綜合條件客戶查詢
		path: '/cus/complexSearch',
		name: 'complexSearch',
		component: () => import('../views/cus/CUS0202/complexSearch.vue')
	},
	{
		// 資產報告書清單(首頁)
		path: '/cus/cusDoc/assetReportHome',
		name: 'assetReportHome',
		component: () => import('../views/cus/CUS07004/assetReportHome.vue')
	},
	{
		// 編輯報告書
		path: '/cus/cusDoc/assetReport/:id?',
		name: 'createAssetReport',
		component: () => import('../views/cus/CUS07004/createAssetReport.vue')
	},
	{
		// 編輯重點客戶
		path: '/cus/favCusSetup',
		name: 'favCusSetup',
		component: () => import('../views/cus/CUS0501/favCusSetup.vue')
	},
	{
		// 客戶異動紀錄查詢
		path: '/cus/cusTransferQuery',
		name: 'cusTransferQuery',
		component: () => import('../views/cus/CUS1301/cusTransferQuery.vue')
	},
	{
		// 客戶移轉分派
		path: '/cus/cusTransfer',
		name: 'cusTransfer',
		component: () => import('../views/cus/CUS1302/cusTransferPage.vue')
	},
	{
		// 客戶異動申請
		path: '/cus/cusTransferApply',
		name: 'cusTransferApply',
		component: () => import('../views/cus/CUS1304/cusTransferApply.vue')
	},
	{
		// 客戶總覽
		path: '/cus/clientOverview/:cusCode',
		name: 'clientOverview',
		component: () => import('@/views/cus/CUS0212/clientOverview.vue'),
		props: true
	}
];

export default cus;
