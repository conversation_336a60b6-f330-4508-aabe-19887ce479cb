<script setup>

const isOpen = defineModel({ type: Boolean });
const emits = defineEmits(['click']);

function onClick() {
	isOpen.value = !isOpen.value;
	emits('click');
}

</script>

<template>
	<div @click="onClick">
		<div class="burger" :class="{ 'active': isOpen }">
			<span />
			<span />
			<span />
		</div>
	</div>
</template>

<style lang="css" scoped>
.burger {
  position: relative;
  font-size: 1em;
  cursor: pointer;
  z-index: 1;
  margin-top: 10px;
}

.burger span {
  display: block;
  width: 20px;
  height: 2px;
  background: #eee;
  margin-bottom: 5px;
  border-radius: 5px;
  transition-property: transform opacity;
  transition: ease-in 0.3s;
}

.burger span:nth-child(1) {
  transform: rotate(45deg) translateY(10px);
}

.burger span:nth-child(2) {
  opacity: 0;
  width: 0;
}

.burger span:nth-child(3) {
  transform: rotate(-45deg) translateY(-10px);
}

.burger.active span:nth-child(1) {
  transform: rotate(0deg) translateY(0px);
}

.burger.active span:nth-child(2) {
  opacity: 1;
  width: 20px;
}

.burger.active span:nth-child(3) {
  transform: rotate(0deg) translateY(0px);
}
</style>
