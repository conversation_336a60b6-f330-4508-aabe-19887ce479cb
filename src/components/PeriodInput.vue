<script setup>
import moment from 'moment';
import { watch } from 'vue';

defineProps({
	maxDays: {
		type: Number
	},
	invalid: Boolean
});

const from = defineModel('from', { type: String });
const to = defineModel('to', { type: String });

watch(from, (val) => {
	if (!val || !to.value) return;
	const fromMoment = moment(val);
	if (fromMoment.isAfter(to.value)) to.value = val;
});

</script>

<template>
	<div class="input-group">
		<span class="input-group-text">{{ $t('gen.from') }}</span>
		<DateInput v-model="from" v-bind="$attrs" :invalid />
		<span class="input-group-text">~</span>
		<span class="input-group-text">{{ $t('gen.to') }}</span>
		<DateInput v-model="to" :min="from" :invalid />
	</div>
</template>
