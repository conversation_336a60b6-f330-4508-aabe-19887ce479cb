<script setup>
import { ref, watch } from 'vue';

/**
 * Props
 * - modelValue: v-model binding
 * - placeholder, disabled, maxlength: pass-through
 */
const props = defineProps({
	modelValue: {
		type: String,
		default: ''
	},
	placeholder: {
		type: String,
		default: ''
	},
	disabled: {
		type: Boolean,
		default: false
	},
	maxlength: {
		type: [Number, String],
		default: null
	},
	invalid: Boolean
});

const emit = defineEmits(['update:modelValue', 'input']);

// local refs/state
const inputRef = ref(null);
const isComposing = ref(false);
const internalValue = ref(String(props.modelValue || ''));

// sanitize function: keep only digits and hyphen
const sanitize = (s) => {
	// remove anything not 0-9 or hyphen
	return String(s).replace(/[^0-9-]+/g, '');
};

// watch incoming modelValue changes and update internalValue
watch(
	() => props.modelValue,
	(nv) => {
		if (nv === undefined || nv === null) {
			internalValue.value = '';
		}
		else if (nv !== internalValue.value) {
			internalValue.value = String(nv);
		}
	}
);

// preserve caret after replacing invalid characters
function replaceAndPreserveCaret(nextValue) {
	const el = inputRef.value;
	if (!el) return;

	const old = el.value;
	const oldSelectionStart = el.selectionStart ?? old.length;

	// sanitized value
	const sanitized = sanitize(nextValue);

	// compute how many invalid chars before oldSelectionStart were removed.
	// We'll walk characters up to oldSelectionStart and count removed.
	let removedBeforeCaret = 0;
	for (let i = 0, j = 0; i < old.length && j < oldSelectionStart; i++, j++) {
		const ch = old[i];
		if (/[^0-9-]/.test(ch)) {
			// this char will be removed; decrement j (skip)
			removedBeforeCaret++;
			j--; // keep j same for next iteration until we've advanced selection index
		}
	}

	// set value and adjust caret
	internalValue.value = sanitized;
	// schedule caret update on next tick to ensure DOM updated
	requestAnimationFrame(() => {
		// compute new caret pos
		let newPos = Math.max(0, oldSelectionStart - removedBeforeCaret);
		// bound by new length
		newPos = Math.min(newPos, sanitized.length);
		el.setSelectionRange(newPos, newPos);
	});

	// emit v-model update
	updateValue(sanitized);
}

function onInput(e) {
	// if composing (IME), don't sanitize yet
	if (isComposing.value) {
		// update internal value but don't emit sanitized model (let composition finish)
		internalValue.value = e.target.value;
		return;
	}

	const raw = e.target.value;
	// Fast path: if value already valid, just propagate
	if (/^[0-9-]*$/.test(raw)) {
		internalValue.value = raw;
		updateValue(raw);
		return;
	}

	// otherwise sanitize and preserve caret
	replaceAndPreserveCaret(raw);
}

function onKeydown(e) {
	// allow control keys, arrows, backspace, delete, tab, etc.
	const allowedControlKeys = [
		'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
		'Home', 'End', 'Tab', 'Enter'
	];
	if (allowedControlKeys.includes(e.key) || e.metaKey || e.ctrlKey || e.altKey) {
		return;
	}

	// allow digits and hyphen
	if (/^[0-9-]$/.test(e.key)) {
		return;
	}

	// otherwise block
	e.preventDefault();
}

function onPaste(e) {
	e.preventDefault();
	const clipboard = (e.clipboardData || window.clipboardData).getData('text') || '';
	const sanitized = sanitize(clipboard);

	const el = inputRef.value;
	if (!el) return;

	// insert sanitized text at caret position
	const start = el.selectionStart ?? el.value.length;
	const end = el.selectionEnd ?? el.value.length;
	const before = el.value.slice(0, start);
	const after = el.value.slice(end);
	const next = before + sanitized + after;

	replaceAndPreserveCaret(next);
}

function onCompositionStart() {
	isComposing.value = true;
}

function onCompositionEnd(e) {
	isComposing.value = false;
	// composition ended — sanitize final value
	const raw = e.target.value;
	if (/^[0-9-]*$/.test(raw)) {
		internalValue.value = raw;
		updateValue(raw);
	}
	else {
		replaceAndPreserveCaret(raw);
	}
}

function updateValue(value) {
	const val = value?.trim() ? value.trim() : null;
	emit('update:modelValue', val);
	emit('input', val);
}
</script>

<template>
	<input
		ref="inputRef"
		:value="internalValue"
		:placeholder="placeholder"
		:disabled="disabled"
		:maxlength="maxlength"
		class="form-control"
		:class="{ 'is-invalid': invalid }"
		inputmode="numeric"
		@input="onInput"
		@keydown="onKeydown"
		@paste="onPaste"
		@compositionstart="onCompositionStart"
		@compositionend="onCompositionEnd"
	>
</template>
