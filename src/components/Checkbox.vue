<script setup>
const model = defineModel({ type: [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>] });

const props = defineProps({
	value: [String, Number, Boolean],
	label: String,
	invalid: <PERSON>olean
});

function onSelect() {
	if (!model.value) {
		model.value = [props.value];
	}
	else {
		const index = model.value.indexOf(props.value);
		if (index === -1) model.value.push(props.value);
		else model.value.splice(index, 1);
	}
}

</script>

<template>
	<div :class="{'form-check': label || $slots['content']}">
		<input
			v-model="model"
			class="form-check-input"
			:aria-label="label"
			:value
			type="checkbox"
			:class="{ 'is-invalid': invalid }"
		>
		<label
			v-if="label || $slots['content']"
			class="form-check-label d-inline-flex align-items-center gap-1"
			:class="{ 'cursor-pointer': !$slots['content'] }"
			@click="!$slots['content'] && onSelect()"
		>
			<slot name="content" :on-select>
				{{ label }}
			</slot>
		</label>
	</div>
</template>
