<script setup>
import { Field } from 'vee-validate';

// eslint-disable-next-line vue/require-prop-types
const model = defineModel({ required: true });

/**
 * @typedef {Object} FormFieldProps
 * @property {boolean} [required] - Whether the field is required
 * @property {string} [label] - Label text for the field
 * @property {boolean} [vertical] - vertical layout
 */

/**
 * @type {FormFieldProps & InstanceType<typeof Field>["$props"]}
 */
const { required, hideLabel, label, vertical, middle, ...otherProps } = defineProps({
	required: Boolean,
	vertical: Boolean,
	hideLabel: Boolean,
	middle: Boolean,
	label: String,
	as: [Object, String],
	name: {
		type: String,
		required: true
	},
	rules: [String, Object],
	validateOnMount: Boolean,
	validateOnBlur: Boolean,
	validateOnChange: Boolean,
	validateOnInput: Boolean,
	validateOnModelUpdate: <PERSON><PERSON><PERSON>,
	bails: <PERSON><PERSON><PERSON>,
	label: String,
	standalone: <PERSON><PERSON><PERSON>,
	keepValue: <PERSON>ole<PERSON>
});

</script>

<template>
	<div class="form-group gap-2" :class="{ 'flex-column align-items-stretch': vertical, middle }">
		<label v-if="label && !hideLabel" class="form-label" :class="{'tx-require': required}">{{ label }}</label>
		<Field
			v-slot="{meta, errorMessage, field}"
			v-model="model"
			v-bind="otherProps"
			:label
		>
			<div>
				<slot :meta="meta" :field="{modelValue: field.value, 'onUpdate:modelValue': field['onUpdate:modelValue'], onBlur: field.onBlur}" :invalid="!meta.valid && meta.validated" />
				<span class="text-danger" :class="{'active': !meta.valid && meta.validated && errorMessage}">
					{{ errorMessage }}
				</span>
			</div>
		</Field>
	</div>
</template>

<style lang="css" scoped>

.form-label {
    max-width: 110px;
	white-space: normal !important;
}

/* form-row 內或者單獨自己且有 Error 時 */
.form-row > .form-group, .form-group:has(> div > span.text-danger.active) {
    margin-bottom: calc(1rem + 8px);
}

.form-group {
	margin-bottom: 0;
	transition: margin-bottom 300ms ease-in;
}

.form-group > div {
    position: relative;
    flex: 1 1 0%;
}

.form-group > div > span.text-danger {
    position: absolute;
    opacity: 0;
    transition: 300ms ease-in;
    transition-property: opacity top;
    top: 100%;
}

.form-group > div > span.text-danger.active {
    position: absolute;
    opacity: 100;
    top: calc(100% + 4px);
}

</style>
