<script setup>

const emits = defineEmits(['click:attachment']);

defineProps({
	document: {
		type: Object,
		required: true
	}
});

function onAttachmentClick(file) {
	emits('click:attachment', file);
}

</script>

<template>
	<div>
		<div class="caption">
			{{ $t('core.documentCategory') }}
		</div>
		<table class="table table-bordered">
			<tbody>
				<tr>
					<th class="wd-15p">
						{{ $t('core.documentType') }}
					</th>
					<td>{{ document.docCatName }}</td>
				</tr>
			</tbody>
		</table>
		<div class="caption">
			{{ $t('core.documentContent') }}
		</div>
		<table class="table table-bordered">
			<tbody>
				<tr>
					<th class="wd-15p">
						{{ $t('core.documentTitle') }}
					</th>
					<td>{{ document.docName }}</td>
				</tr>
				<tr>
					<th>{{ $t('gen.effectiveDate') }}</th>
					<td>{{ document.validDt }}</td>
				</tr>
				<tr>
					<th>{{ $t('gen.expiryDate') }}</th>
					<td>{{ document.expireDt }}</td>
				</tr>
				<tr>
					<th>{{ $t('gen.urgencyLevel') }}</th>
					<td>{{ document.priorityName }}</td>
				</tr>
				<tr>
					<th>{{ $t('gen.canProvideToCustomers') }}</th>
					<td>{{ document.showCusYn === 'Y' ? $t('gen.canProvideToCustomer') : $t('gen.internalUseOnly') }}</td>
				</tr>
				<tr>
					<th>{{ $t('gen.summary') }}</th>
					<td>{{ document.docDesc }}</td>
				</tr>
				<tr>
					<th>{{ $t('gen.additionalDocuments') }}</th>
					<td>
						<ColoredLink
							v-for="file in document.fileInfo"
							:key="file.docFileId"
							@click="onAttachmentClick(file)"
						>
							{{ file.showName }}
						</ColoredLink>
					</td>
				</tr>
				<slot name="eatra-field" />
			</tbody>
		</table>
		<div class="caption">
			{{ $t('gen.maintenanceInfo') }}
		</div>
		<table class="table table-bordered">
			<tbody>
				<tr>
					<th class="wd-15p">
						{{ $t('gen.creator') }}
					</th>
					<td>{{ document.createBy }}</td>
				</tr>
				<tr>
					<th>{{ $t('gen.createDate') }}</th>
					<td>{{ document.createDt }}</td>
				</tr>
				<tr>
					<th>{{ $t('gen.lastMaintainer') }}</th>
					<td>{{ document.modifyBy }}</td>
				</tr>
				<tr>
					<th>{{ $t('gen.lastMaintenanceDate') }}</th>
					<td>{{ document.modifyDt }}</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>
