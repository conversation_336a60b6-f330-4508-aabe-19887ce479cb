<template>
	<vue-form
		ref="userConditionForm"
		class="user-condition-field"
	>
		<vue-field
			v-if="isShow.area"
			v-slot="{field, meta, errors}"
			v-model="selectedArea"
			role="area"
			name="area"
			as="div"
			:label="label.area"
			:rules="required.area ? 'required' : undefined"
		>
			<label class="form-label" :class="{ 'tx-require': required.area }">
				{{ label.area }}
			</label>
			<Select
				:model-value="field.value"
				:options="areaOptions"
				:invalid="!meta.valid && meta.validated"
				@update:model-value="field['onUpdate:modelValue']"
			/>
			<span v-if="!meta.valid && meta.validated" class="text-danger">
				{{ errors[0] }}
			</span>
		</vue-field>
		<vue-field
			v-if="isShow.bran"
			v-slot="{field, meta, errors}"
			v-model="selectedBran"
			role="bran"
			name="bran"
			as="div"
			:label="label.bran"
			:rules="required.bran ? 'required' : undefined"
		>
			<label class="form-label" :class="{ 'tx-require': required.bran }">
				{{ label.bran }}
			</label>
			<Select
				:model-value="field.value"
				:options="branOptions"
				:invalid="!meta.valid && meta.validated"
				@update:model-value="field['onUpdate:modelValue']"
			/>
			<span v-if="!meta.valid && meta.validated" class="text-danger">
				{{ errors[0] }}
			</span>
		</vue-field>
		<vue-field
			v-if="isShow.user"
			v-slot="{field, meta, errors}"
			v-model="selectedUser"
			role="user"
			name="user"
			as="div"
			:label="label.user"
			:rules="required.user ? 'required' : undefined"
		>
			<label class="form-label" :class="{ 'tx-require': required.user }">
				{{ label.user }}
			</label>
			<Select
				:model-value="field.value"
				:options="userOptions"
				:invalid="!meta.valid && meta.validated"
				@update:model-value="field['onUpdate:modelValue']"
			/>
			<span v-if="!meta.valid && meta.validated" class="text-danger">
				{{ errors[0] }}
			</span>
		</vue-field>
		<div role="append">
			<slot name="append" />
		</div>
	</vue-form>
</template>
<script>
import { Field, Form } from 'vee-validate';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	props: {
		isRequired: {
			type: [Boolean, String],
			default: false
		},
		areaLabel: String,
		branLabel: String,
		userLabel: String,
		isInline: {
			type: Boolean,
			default: false
		},
		customCondition: {
			type: Object,
			default: () => ({
				areaCode: '',
				branCode: '',
				userCode: ''
			})
		},
		show: {
			type: String,
			default: 'user'
		}
	},
	data: function () {
		return {
			selectedArea: '',
			selectedBran: '',
			selectedUser: '',
			areaMenu: [],
			branMenu: [],
			userMenu: [],
			firstLoad: true,
			firstCondition: {
				branMenu: [],
				userMenu: [],
				selectedArea: '',
				selectedBran: '',
				selectedUser: ''
			},

			isLoadCustom: false,
			watchEnabled: true,

			isAreaShow: true,
			isBranShow: true,
			isUserShow: true
		};
	},
	computed: {
		areaOptions() {
			return [
				{ value: '', label: this.required.area ? '請選擇' : '全部' },
				...this.areaMenu.map(({ branCode, branName }) => ({ value: branCode + '_' + branName, label: branName }))
			];
		},
		branOptions() {
			return [
				{ value: '', label: this.required.bran ? '請選擇' : '全部' },
				...this.branMenu.map(({ branCode, branName }) => ({ value: branCode + '_' + branName, label: branName }))
			];
		},
		userOptions() {
			return [
				{ value: '', label: this.required.user ? '請選擇' : '全部' },
				...this.userMenu.map(({ userCode, userName }) => ({ value: userCode + '_' + userName, label: userName }))
			];
		},
		required() {
			const data = {
				area: true,
				bran: true,
				user: true
			};
			if (this.isRequired === 'bran') {
				data.user = false;
			}
			else if (this.isRequired === 'area') {
				data.user = false;
				data.bran = false;
			}
			return data;
		},
		isShow() {
			const data = {
				area: true,
				bran: true,
				user: true
			};
			if (this.show === 'bran') {
				data.user = false;
			}
			else if (this.show === 'area') {
				data.user = false;
				data.bran = false;
			}
			return data;
		},
		label() {
			return {
				area: this.areaLabel ?? this.$t('cus.region'),
				bran: this.branLabel ?? this.$t('cus.branch'),
				user: this.userLabel ?? this.$t('cus.advisor')
			};
		}
	},
	watch: {
		customCondition: {
			handler: function (newVal) {
				const self = this;
				if (newVal && !self.isLoadCustom && !self.firstLoad) {
					self.loadCustomCondition();
				}
			}
		},
		firstLoad: function (newVal) {
			if (newVal || this.isLoadCustom) return;
			this.loadCustomCondition();
		},
		selectedArea: function (newVal) {
			const self = this;
			const [branCode, branName] = newVal.split('_');
			if (self.watchEnabled) {
				self.branMenu = [];
				self.userMenu = [];
				self.selectedBran = '';
				self.selectedUser = '';
				if (newVal) {
					self.getBranMenu(branCode);
				}
			}
			self.$emit('change-area', branCode);
			self.$emit('change-area-name', branName || '');
		},
		selectedBran: function (newVal) {
			const self = this;
			const [branCode, branName] = newVal.split('_');
			if (self.watchEnabled) {
				self.userMenu = [];
				self.selectedUser = '';
				if (newVal) {
					self.getUserMenu(branCode);
				}
			}
			self.$emit('change-bran', branCode);
			self.$emit('change-bran-name', branName || '');
		},
		selectedUser: function (newVal) {
			const self = this;
			const [userCode, userName] = newVal.split('_');
			self.$emit('change-user', userCode);
			self.$emit('change-user-name', userName || '');
		}
	},
	mounted: function () {
		const self = this;
		self.getAreaMenu();
	},
	methods: {
		// 下拉選單
		getAreaMenu: async function () {
			const self = this;
			const ret = await self.$api.getAreaMenu();
			self.areaMenu = ret.data;
			if (self.areaMenu.length == 1) {
				self.selectedArea = self.areaMenu[0].branCode + '_' + self.areaMenu[0].branName;
			}
			else {
				if (self.firstLoad) {
					self.$emit('loaded');
					self.firstLoad = false;
				}
			}
		},
		getBranMenu: async function (branCode) {
			const self = this;
			const ret = await self.$api.getBranchesApi({
				minorCode: branCode
			});
			self.branMenu = ret.data;
			if (self.branMenu.length == 1) {
				self.selectedBran = self.branMenu[0].branCode + '_' + self.branMenu[0].branName;
			}
			else {
				if (self.firstLoad) {
					self.$nextTick(() => {
						self.$emit('loaded');
						self.firstLoad = false;
						self.firstCondition.branMenu = self.branMenu;
						self.firstCondition.selectedArea = self.selectedArea;
					});
				}
			}
		},
		getUserMenu: async function (branCode) {
			const self = this;
			const ret = await self.$api.getBranEmployeeApi({
				branCode: branCode
			});
			self.userMenu = ret.data;
			if (self.userMenu.length == 1) {
				self.selectedUser = self.userMenu[0].userCode + '_' + self.userMenu[0].userName;
			}
			if (self.firstLoad) {
				self.$nextTick(() => {
					self.$emit('loaded');
					self.firstLoad = false;
					self.firstCondition.branMenu = self.branMenu;
					self.firstCondition.userMenu = self.userMenu;
					self.firstCondition.selectedArea = self.selectedArea;
					self.firstCondition.selectedBran = self.selectedBran;
					self.firstCondition.selectedUser = self.selectedUser;
				});
			}
		},
		validate: async function () {
			return await this.$refs.userConditionForm.validate();
		},
		reset: function () {
			this.watchEnabled = false;
			this.selectedArea = this.firstCondition.selectedArea;
			this.branMenu = this.firstCondition.branMenu;
			this.selectedBran = this.firstCondition.selectedBran;
			this.userMenu = this.firstCondition.userMenu;
			this.selectedUser = this.firstCondition.selectedUser;
			this.$nextTick(() => {
				this.watchEnabled = true;
			});
		},
		loadCustomCondition: async function () {
			if (this.customCondition.areaCode) {
				this.watchEnabled = false;
				const targetArea = this.areaMenu.find(item => item.branCode == this.customCondition.areaCode);
				if (targetArea) {
					const targetAreaFormat = targetArea.branCode + '_' + targetArea.branName;
					if (targetAreaFormat != this.selectedArea) {
						this.selectedArea = targetArea.branCode + '_' + targetArea.branName;
						const branMenuRes = await this.$api.getBranchesApi({
							minorCode: this.customCondition.areaCode
						});
						this.branMenu = branMenuRes.data;
					}
					if (this.customCondition.branCode) {
						const targetBran = this.branMenu.find(item => item.branCode == this.customCondition.branCode);
						if (targetBran) {
							const targetBranFormat = targetBran.branCode + '_' + targetBran.branName;
							if (targetBranFormat != this.selectedBran) {
								this.selectedBran = targetBran.branCode + '_' + targetBran.branName;
								const userMenuRes = await this.$bi.getUserMenu({
									branCode: this.customCondition.branCode
								});
								this.userMenu = userMenuRes.data;
							}
							if (this.customCondition.userCode) {
								const targetUser = this.userMenu.find(item => item.userCode == this.customCondition.userCode);
								if (targetUser) {
									this.selectedUser = targetUser.userCode + '_' + targetUser.userName;
								}
							}
						}
					}
				}
			}
			else {
				return;
			}
			this.$nextTick(() => {
				this.$emit('custom-loaded');
				this.watchEnabled = true;
			});
		}
	}
};
</script>

<style lang="css" scoped>
.user-condition-field {
	display: grid;
	width: 100%;
	grid-template-rows: max-content 1fr;
}

.user-condition-field:has(> div[role='bran']) {
	grid-template-columns: repeat(2, 1fr) max-content;
}

.user-condition-field:has(> div[role='user']) {
	grid-template-columns: repeat(3, 1fr) max-content;
}

.user-condition-field > div:nth-last-child(2) {
	margin-right: 2px;
}

.user-condition-field > div:nth-last-child(n+3) {
	margin-right: .25rem;
}

.user-condition-field > div:not(:last-child){
	display: grid;
    grid-template-rows: subgrid;
	grid-row: span 2;
	position: relative;
}

.user-condition-field > div:not(:last-child) > span:last-child {
	position: absolute;
	top: calc(100% + 2px);
}

.user-condition-field > div:last-child {
	grid-row: 2;
	grid-column: -1;
}

</style>
