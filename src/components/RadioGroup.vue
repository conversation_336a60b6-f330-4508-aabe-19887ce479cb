<script setup>
import { computed } from 'vue';

const model = defineModel({ type: [String, Number, Boolean] });

const { options, optionLabel, optionValue, inline, invalid } = defineProps({
	options: {
		type: Array,
		required: true
	},
	optionValue: {
		type: [Function, String],
		default: 'value'
	},
	optionLabel: {
		type: [Function, String],
		default: 'label'
	},
	inline: Boolean,
	invalid: Boolean
});

const resolvedOptions = computed(() => options.map(option => ({
	...option,
	value: typeof optionValue === 'function' ? optionValue(option) : option[optionValue],
	label: typeof optionLabel === 'function' ? optionLabel(option) : option[optionLabel]
})));

function onLabelClick(value) {
	model.value = value;
}

</script>

<template>
	<div>
		<div v-for="option in resolvedOptions" class="form-check" :class="{'form-check-inline': inline}">
			<input
				v-model="model"
				type="radio"
				:value="option.value"
				class="form-check-input"
				:label="option.label"
				:class="{ 'is-invalid': invalid }"
			>
			<label class="form-check-label cursor-pointer" @click="onLabelClick(option.value)">{{ option.label }}</label>
		</div>
	</div>
</template>
