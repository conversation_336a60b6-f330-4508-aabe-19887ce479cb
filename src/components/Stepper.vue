<script setup lang="js">
const model = defineModel({ type: [Number, null] });
const emits = defineEmits(['click']);
const { items } = defineProps({
	items: {
		type: Array,
		required: true
	}
});

function onClick(index) {
	emits('click', index);
}
</script>

<template>
	<div class="tab-step">
		<ul class="nav steps steps-tab justify-content-center mb-3" role="tablist">
			<li v-for="(item, index) in items" class="step-item">
				<a
					class="step-link"
					:class="{ active: model === index }"
					style="cursor: pointer;"
					@click="onClick(index)"
				>
					<slot name="item" :item :index>
						<span class="step-number">{{ index + 1 }}</span> <span class="step-title">{{ item.title }}</span>
					</slot>
				</a>
			</li>
		</ul>
		<div class="tab-content mt-2">
			<div
				v-for="(item, index) in items"
				v-show="model === index"
				role="tabpanel"
				class="tab-pane fade"
				:class="{ 'show active': model === index }"
			>
				<slot :name="item.slot" :item>
					<slot name="content" :item>
						{{ item }}
					</slot>
				</slot>
			</div>
		</div>
	</div>
</template>
