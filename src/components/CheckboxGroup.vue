<script setup>
const model = defineModel({ type: Array });

const { options, optionLabel, optionValue, inline, invalid } = defineProps({
	options: {
		type: Array,
		required: true
	},
	optionValue: {
		type: [Function, String],
		default: 'value'
	},
	optionLabel: {
		type: [Function, String],
		default: 'label'
	},
	inline: Boolean,
	invalid: Boolean
});

const resolvedValueAndLabel = computed(() => options.map(option => ({
	value: resolveOptionValue(option),
	label: resolveOptionLabel(option)
})));

function resolveOptionValue(option) {
	return typeof optionValue === 'function' ? optionValue(option) : option[optionValue];
}

function resolveOptionLabel(option) {
	return typeof optionLabel === 'function' ? optionLabel(option) : option[optionLabel];
}

</script>

<template>
	<div>
		<Checkbox
			v-for="(option, index) in options"
			:key="resolvedValueAndLabel[index].value"
			v-model="model"
			:invalid
			:class="{'form-check-inline': inline}"
			:label="resolvedValueAndLabel[index].label"
			:value="resolvedValueAndLabel[index].value"
		>
			<template #content="{onSelect}">
				<slot name="option" :on-select :option>
					<span class="cursor-pointer" @click="onSelect">{{ resolvedValueAndLabel[index].label }}</span>
				</slot>
			</template>
		</Checkbox>
	</div>
</template>
