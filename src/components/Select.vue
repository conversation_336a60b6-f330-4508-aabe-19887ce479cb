<script setup>
const model = defineModel({	type: [String, Number, Array] });
const { options, optionValue, optionLabel, disabled, invalid } = defineProps({
	options: {
		type: [Array, undefined],
		required: true
	},
	optionValue: {
		type: [String, Function],
		default: 'value'
	},
	optionLabel: {
		type: [String, Function],
		default: 'label'
	},
	disabled: Boolean,
	invalid: Boolean
});

watch(() => options, (val) => {
	if (!val || !Array.isArray(val)) return;
	if (val.some(option => resolveOptionValue(option) === model.value)) return;
	model.value = resolveOptionValue(val[0]);
});

function resolveOptionValue(option) {
	return typeof optionValue === 'function'
		? optionValue(option)
		: typeof option === 'object'
			? option[optionValue]
			: option;
}

function resolveOptionLabel(option) {
	return typeof optionLabel === 'function'
		? optionLabel(option)
		: typeof option === 'object'
			? option[optionLabel]
			: option;
}

</script>

<template>
	<select
		v-model="model"
		class="form-select"
		:disabled
		:class="{ 'is-invalid': invalid }"
	>
		<option v-for="option in options" :key="resolveOptionValue(option)" :value="resolveOptionValue(option)">
			{{ resolveOptionLabel(option) }}
		</option>
	</select>
</template>
