<script setup>

/**
 * @typedef {object} TabItem
 * @property {string|number} code Tab 值
 * @property {string} name Tab 名稱
 */

const model = defineModel({
	type: [String, Number, null],
	required: true
});

const { tabs, tabLabel, tabValue, variant, fill } = defineProps({
	tabs: Array,
	tabValue: {
		type: [String, Function],
		default: 'code'
	},
	tabLabel: {
		type: [String, Function],
		default: 'name'
	},
	fill: Boolean,
	variant: String
});

const resolvedTabs = computed(() => tabs?.map((tab, index) => ({
	...tab,
	value: typeof tabValue === 'function' ? tabValue(tab, index) : tab[tabValue],
	label: typeof tabLabel === 'function' ? tabLabel(tab, index) : tab[tabLabel]
})));

watch(resolvedTabs, (val) => {
	if (!val || model.value != null) return;
	model.value = val[0]?.value;
});

</script>

<template>
	<ul
		class="nav"
		:class="{
			'nav-pills' : variant === 'pill',
			'nav-tabs': variant === 'tabs',
			'nav-line': variant === 'line',
			'nav-fill': fill
		}"
		role="tablist"
	>
		<li
			v-for="tab in resolvedTabs"
			class="nav-item"
			style="cursor: pointer;"
			@click="model = tab.value"
		>
			<a class="nav-link" :class="{ active: model === tab.value }">
				<slot name="content" :tab :active="model === tab.value">
					{{ tab.label }}
				</slot>
			</a>
		</li>
	</ul>
</template>
