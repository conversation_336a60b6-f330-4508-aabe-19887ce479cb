<script setup>
import { computed } from 'vue';

/**
 * @typedef {object} ButtonProps
 * @property {string} [label] - The text to display on the button.
 * @property {boolean} [disabled=false] - Whether the button is disabled.
 * @property {boolean} [outline=false] - Whether the button has an outline style.
 * @property {'primary'|'secondary'|'success'|'danger'|'warning'|'info'|'light'|'dark'} [color='primary'] - The color of the button.
 * @property {('sm' | 'lg') | undefined} [size] - The size of the button.
 * @property {boolean} [link=false] - Whether the button is styled as a link.
 * @property {boolean} [icon=false] - Whether the button is styled as an icon
 */

/**
 * @type {ButtonProps}
 */
const { color, outline, disabled, size, link, icon } = defineProps({
	label: {
		type: String
	},
	disabled: Boolean,
	outline: Boolean,
	link: Boolean,
	icon: Boolean,

	color: {
		type: String,
		default: 'primary'
	},
	size: {
		type: String,
		validator: value => ['sm', 'lg'].includes(value)
	}
});

const emits = defineEmits(['click']);

const cssClasses = computed(() => {
	const classes = [];
	if (!color.startsWith('#')) classes.push(outline ? `btn-outline-${color}` : `btn-${color}`);
	if (size) classes.push(`btn-${size}`);
	if (icon) classes.push('btn-icon');
	if (link) {
		classes.push('btn-link');
		if (disabled) classes.push('disabled');
	}
	return classes;
});

</script>

<template>
	<a
		v-if="link"
		href="#"
		role="button"
		class="btn"
		:class="cssClasses"
		tabindex="-1"
		@click.prevent="emits('click', $event)"
	>
		<slot>
			{{ label }}
		</slot>
	</a>
	<button
		v-else
		class="btn"
		:disabled="disabled"
		:aria-disabled="disabled"
		:class="cssClasses"
		@click.prevent="emits('click', $event)"
	>
		<slot>
			{{ label }}
		</slot>
	</button>
</template>
