<script setup>

defineProps({
	value: {
		type: Number
	},
	threshold: {
		type: Number,
		default: 0
	}
});

</script>

<template>
	<span
		:class="{
			'text-positive': value > threshold,
			'text-negative': value < threshold
		}"
	>
		<slot>
			{{ value }}
		</slot>
	</span>
</template>

<style lang="css" scoped>
/* 預設綠漲紅跌 */
.text-positive {
	color: var(--text-positive, #41b658);
}

.text-negative {
	color: var(--text-positive, red);
}

</style>
