<script setup>
import VuePagination from '@/views/components/pagination.vue';

const { columns, list, rows, title, pagination, bordered, hover } = defineProps({
	rows: {
		type: [Array, null],
		validate: value => value == null || Array.isArray(value),
		default: () => []
	},
	columns: {
		type: Array,
		required: true
	},
	title: {
		type: String
	},
	pagination: {
		type: Object
	},
	list: Boolean,
	hover: Boolean,
	bordered: Boolean
});

const emits = defineEmits(['page']);

const hasTableHeader = computed(() => columns.some(it => it.label));

function resolveCellValue(item, column) {
	return typeof column.field === 'function'
		? column.field(item)
		: item[column.field];
}

function onGoToPage(page) {
	emits('page', page);
}

</script>

<template>
	<div class="card" :class="list ? 'card-table-list' : 'card-table'">
		<div class="card-header">
			<h4>{{ title }}</h4>
			<vue-pagination v-if="pagination" :pageable="pagination" :goto-page="onGoToPage" />
		</div>
		<div class="table-responsive">
			<table
				class="table table-RWD text-center"
				:class="{
					'table-list': list ,
					'table-hover': hover,
					'table-bordered': bordered,
				}"
			>
				<thead v-if="hasTableHeader">
					<tr>
						<th v-for="column in columns" :class="column.headerClass">
							{{ column.label }}
						</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="(row, rowIndex) in rows">
						<td v-for="(column, columnIndex) in columns" :data-th="column.label" :class="column.bodyClass">
							<slot
								:name="`body-cell-${column.name}`"
								:item="row"
								:row-index
								:column-index
							>
								{{ resolveCellValue(row, column) }}
							</slot>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</template>
