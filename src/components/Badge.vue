<script setup>

/**
 * @typedef {object} BadgeProps
 * @property {string} [description] - The text to give context for aux reader
 * @property {'primary'|'secondary'|'success'|'danger'|'warning'|'info'|'light'|'dark'} [color='primary'] - The color of the badge.
 * @property {boolean} [pill=false] - Whether the badge is pill style
 */

/**
 * @type {BadgeProps}
 */
const { color, pill, description } = defineProps({
	pill: Boolean,
	color: {
		type: String,
		default: 'primary'
	},
	description: String
});

const cssClasses = computed(() => {
	const classes = [`bg-${color}`];
	if (color === 'warning' || color === 'info' || color === 'light') classes.push('text-dark');
	if (pill) classes.push('rounded-pill');
	return classes;
});

</script>

<template>
	<span v-bind="$attrs" class="badge" :class="cssClasses">
		<slot />
	</span>
	<span class="visually-hidden">{{ description }}</span>
</template>
