<script setup>
import { computed } from 'vue';

/**
 * Bootstrap-styled link component.
 * 已 click.prevent 阻止連結預設的導向行為
 */

/**
 * @typedef {object} LinkProps
 * @property {'primary'|'secondary'|'success'|'danger'|'warning'|'info'|'light'|'dark'} [color='primary'] - The color of the button.
 */

/**
 * @type {LinkProps}
 */
const { color } = defineProps({
	href: {
		type: String,
		default: '#'
	},
	color: {
		type: String,
		default: 'primary'
	}
});

const emits = defineEmits(['click']);

const cssClasses = computed(() => `link-${color}`);

</script>

<template>
	<a :href :class="cssClasses" @click.prevent="emits('click', $event)">
		<slot />
	</a>
</template>
