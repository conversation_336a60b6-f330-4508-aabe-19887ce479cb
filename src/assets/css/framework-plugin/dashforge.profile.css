/*
 * Dashforge Profile Page
 *
 * This style is use in profile page, connections, groups, events and timeline.
 *
 */
.page-profile {
  color: #1b2e4b; }

@media (min-width: 992px) {
  .content-profile {
    padding: 40px 0; } }

@media (min-width: 992px) {
  .profile-sidebar {
    width: 260px; } }

.profile-sidebar .media-list .media + .media {
  margin-top: 15px; }

@media (min-width: 992px) {
  .profile-sidebar-two {
    width: 230px; } }

.profile-skillset h4 {
  font-size: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Inter UI", Roboto, sans-serif;
  letter-spacing: -1px;
  margin-bottom: 3px; }

.profile-skillset label {
  display: block;
  margin-bottom: 0;
  text-transform: uppercase;
  color: #8392a5;
  font-size: 10px;
  font-weight: 500;
  font-family: -apple-system, BlinkMacSystemFont, "Inter UI", Roboto, sans-serif;
  letter-spacing: .5px; }

  .profile-info-list { margin-bottom: 0;}
.profile-info-list li {
  display: flex;
  align-items: center;
line-height: 1.1; 
 color: #8392a5;}
  .profile-info-list li + li {
    margin-top: 5px; }

.profile-info-list a {
  color: #1b2e4b;
  transition: all 0.2s ease-in-out; }
  @media (prefers-reduced-motion: reduce) {
    .profile-info-list a {
      transition: none; } }
  .profile-info-list a:hover, .profile-info-list a:focus {
    color: #0168fa; }

.profile-info-list i {
  font-size: 16px; 
  margin-right: 10px; }

.profile-update-option i {
  font-size: 18px;  }

.card-profile-interest .media + .media {
  margin-top: 25px; }

.img-group-mutual .img {
  width: 40px;
  height: 40px; }
  .img-group-mutual .img + .img {
    margin-left: -16px; }

.list-inline-skills {
  font-size: 12px;
  display: flex;
  flex-wrap: wrap; }
  .list-inline-skills .list-inline-item {
    margin-right: 5px;
    margin-bottom: 5px; }
    .list-inline-skills .list-inline-item a {
      display: block;
      background-color: #fff;
      border: 1px solid #c0ccda;
      padding-top: 3px;
      padding-bottom: 1px;
      padding-left: 8px;
      padding-right: 8px;
      color: #1b2e4b;
      border-radius: 0.25rem; }

.nav-line-profile .nav-link {
  display: flex;
  align-items: center; }
  .nav-line-profile .nav-link.active {
    font-weight: 500; }
    .nav-line-profile .nav-link.active .badge {
      background-color: #00cccc;
      color: #fff; }

.nav-line-profile .badge {
  margin-left: 10px;
  background-color: #e5e9f2;
  color: #8392a5; }

.timeline-label {
  margin-left: 10px;
  padding-left: 25px;
  padding-top: 25px;
  padding-bottom: 25px;
  border-left: 1px solid rgba(72, 94, 144, 0.16);
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: .5px;
  position: relative; }
  @media (min-width: 576px) {
    .timeline-label {
      margin-left: 120px; } }
  @media (min-width: 768px) {
    .timeline-label {
      margin-left: 150px; } }
  .timeline-label::before {
    content: '';
    position: absolute;
    top: 29px;
    left: -6px;
    background-color: #3b4863;
    width: 11px;
    height: 11px;
    border-radius: 100%; }
  .timeline-label:first-child {
    padding-top: 0; }
    .timeline-label:first-child::before {
      top: 4px; }

@media (min-width: 576px) {
  .timeline-item {
    display: flex;
    align-items: flex-start; } }

.timeline-item + .timeline-item {
  margin-top: 20px;
  position: relative; }
  .timeline-item + .timeline-item::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 10px;
    width: 1px;
    height: 20px;
    background-color: rgba(72, 94, 144, 0.16); }
    @media (min-width: 576px) {
      .timeline-item + .timeline-item::before {
        left: 120px; } }
    @media (min-width: 768px) {
      .timeline-item + .timeline-item::before {
        left: 150px; } }

.timeline-time {
  flex-shrink: 0;
  padding-left: 25px;
  margin-left: 10px;
  border-left: 1px solid rgba(72, 94, 144, 0.16); }
  @media (min-width: 576px) {
    .timeline-time {
      width: 120px;
      text-align: right;
      padding-right: 25px;
      padding-left: 0;
      margin-left: 0;
      border-left: 0; } }
  @media (min-width: 768px) {
    .timeline-time {
      width: 150px; } }

.timeline-body {
  flex: 1;
  padding-left: 25px;
  border-left: 1px solid rgba(72, 94, 144, 0.16);
  position: relative;
  margin-left: 10px; }
  @media (min-width: 576px) {
    .timeline-body {
      margin-left: 0; } }
  .timeline-body::before {
    content: '';
    position: absolute;
    top: 4px;
    left: -6px;
    background-color: #fff;
    width: 11px;
    height: 11px;
    border: 2px solid #0168fa;
    border-radius: 100%; }
  .timeline-body h6 {
    line-height: 1.5; }
