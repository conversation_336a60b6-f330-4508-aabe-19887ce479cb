 
 /*marquee*/
.simple-marquee-container *{
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	-o-box-sizing:border-box;
	box-sizing:border-box; 
}
.simple-marquee-container { 
  width: 60%;  margin-left: 1%;
	display: inline-block;
	overflow: hidden;
	box-sizing: border-box;
	height: 25px;
  position: relative;  
	cursor: pointer;
} 
 
.simple-marquee-container .marquee, .simple-marquee-container *[class^="marquee"] {
	display: inline-block;
	white-space: nowrap;
	position:absolute;
} 
.simple-marquee-container .marquee{
  margin-left: 25%;
} 
.simple-marquee-container .marquee-content-items{
  display: inline-block;
  padding: 0px;
  margin: 0;
  height:25px;
  position: relative;
} 
.simple-marquee-container .marquee-content-items li{
  display: inline-block;
  line-height: 25px;
  color: #333333;
  font-size: 13px;
} 
.simple-marquee-container .marquee-content-items li:after{
	content: "|";
	margin: 0 1em;
}
 