<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 207.37 45"><defs><style>.cls-1{fill:#fff;}.cls-2{fill:none;}</style></defs><g id="圖層_2" data-name="圖層 2"><g id="圖層_1-2" data-name="圖層 1"><path class="cls-1" d="M33.11,39V12.32H43V39Z"/><polygon class="cls-2" points="9.86 10.67 9.86 16.02 17.71 16.02 17.71 10.64 10.03 10.64 10.03 10.67 9.86 10.67"/><path class="cls-1" d="M20.16,1.68H10v9h7.68v5.62H9.86v8.35h10.3c4.37,0,6.35-1.68,6.35-6.82V8.5C26.51,3.32,24.53,1.68,20.16,1.68Z"/><rect class="cls-1" x="9.86" y="10.64" width="0.17" height="0.03"/><rect class="cls-2" x="10.03" y="24.66" width="8.96" height="5.38"/><path class="cls-1" d="M21.78,16.07H10V10.66H0V30H10V24.66h9V30H10v9H21.78c5,0,7.24-1.64,7.24-6.82V22.89C29,17.75,26.75,16.07,21.78,16.07Z"/><path class="cls-1" d="M33.11,10.67V1.77H43v8.9Z"/><path class="cls-1" d="M62.75,15.31l-.26.11-.73-1.31V24.7H58.87V7.44c0-.51.25-.77.7-.77H64c-.5-1.17-.75-1.79-.75-1.84h-5.1V1.77H67.8V.7h2.89V1.77H80.3V4.83H75c-.31,1-.49,1.63-.54,1.84H78.8a.68.68,0,0,1,.73.77V22.86a2.6,2.6,0,0,1-1.36,2.31,7.06,7.06,0,0,1-4.4.41l-.22-2.88h-10c-.51,0-.76-.31-.76-.86V15.31Zm1.85-4.53a3,3,0,0,0,.63-1H62c-.15,0-.22.08-.22.24v2.4A8,8,0,0,0,64.6,10.78Zm11,4.67v6.39a.93.93,0,0,1-.27.7,1.66,1.66,0,0,0,.6-.14,1.14,1.14,0,0,0,.76-1v-7H71.47c-.89,0-1.34-.52-1.34-1.5V9.74H68.24c-.1.39-.2.77-.29,1.14a9.22,9.22,0,0,1-3.64,3.74H74.77C75.29,14.62,75.55,14.89,75.55,15.45Zm-3,2.13H65.76c-.09,0-.14.06-.12.13v1.87c0,.08,0,.14.12.16h6.75c.1,0,.15-.09.15-.16V17.71C72.66,17.62,72.61,17.58,72.51,17.58ZM67.05,6.67h4.37c.19-.59.39-1.2.61-1.84H66.35A14,14,0,0,1,67.05,6.67Zm6.67,4.75h2.92V10c0-.16-.08-.24-.25-.24H73v.88C73,11.15,73.27,11.42,73.72,11.42Z"/><path class="cls-1" d="M102,24.05c0,.57-.27.85-.77.85H85.48c-.5,0-.76-.3-.76-.85V14.19c-.48.24-1,.48-1.55.71l-1.53-2.82a9,9,0,0,0,5-3.28H82.73V6.05h.59A6.48,6.48,0,0,0,82,4.56,12.15,12.15,0,0,0,84.77,0L87.4,1.39a.57.57,0,0,0-.13.24h6.36V4.27H90.14V6.05h4V8.8h-3c1.1.8,2.18,1.65,3.25,2.56L93.17,13.2h8c.51,0,.77.27.77.82Zm-14.54-18V4.27h-1.6a18.17,18.17,0,0,1-1.33,1.78ZM86.35,13.2H92c-.92-.89-2-1.82-3.08-2.77A8.89,8.89,0,0,1,86.59,13,1.55,1.55,0,0,0,86.35,13.2Zm1.26,4.26H99.1V16.29c0-.09,0-.13-.15-.13H87.73c-.09,0-.13.06-.12.13Zm11.49,3H87.61v1.35a.13.13,0,0,0,.12.16H99c.1,0,.15-.09.15-.16ZM104,12.21c0,.28-.13.43-.38.43H94.85c-.25,0-.37-.15-.37-.43V1.87c0-.27.12-.4.37-.4h8.74c.25,0,.38.13.38.4Zm-2.91-8H97.37c-.08,0-.11.06-.1.13V9.73a.15.15,0,0,0,.1.16h3.69a.18.18,0,0,0,.12-.16V4.34A.13.13,0,0,0,101.06,4.21Z"/><path class="cls-1" d="M114.75,23.7h-3.5l.17.82a17,17,0,0,1-4,.88l-.23-1.58,1.3-.18a.43.43,0,0,1,0-.17V17l-1.15.53L106.76,16a15.68,15.68,0,0,0,3.14-1.73l.76,1.39a19.48,19.48,0,0,1-1.91,1.18h2.43l-.52-1.4.66-.12a3.49,3.49,0,0,0,2.11-1.39v0h-.9a4.7,4.7,0,0,1-.9.79l-1.06-1.19a5.69,5.69,0,0,0,1.71-1.87l1.17.81h4.74l.78.85a24.64,24.64,0,0,1-1.57,2.18,7.63,7.63,0,0,0,1.57.34l-.53,1.61-.93-.21v6.26a.2.2,0,0,1,0,.14c.45.12.88.23,1.27.36l-.42,1.49a16.54,16.54,0,0,0-3.76-.91Zm-7.47-11.38h2.83V13.9h-2.83ZM110,18.71h6v-.4s0-.07-.06-.07H110a.06.06,0,0,0,0,.07Zm0,1.76h6V20h-6Zm6,1.3h-6v.44a.08.08,0,0,0,0,.08h5.94a.09.09,0,0,0,.06-.08Zm-4.1-4.93h4.68a7.17,7.17,0,0,1-2.14-1.73A4.91,4.91,0,0,1,111.9,16.84Zm4.79-1.69a.53.53,0,0,0,.15.08l-.6-.49c.28-.33.51-.6.69-.84h-1.59A3.66,3.66,0,0,0,116.69,15.15Z"/><path class="cls-1" d="M119.75,14h4.46v1.58h-4.46Zm4.48,10.62c0,.3-.13.44-.39.44h-3.66c-.25,0-.38-.14-.38-.44v-4.4c0-.29.13-.43.38-.43h3.66c.26,0,.39.14.39.43v3.66a9.66,9.66,0,0,0,1.44-4l-.92.22-.5-1.61,1.51-.39V13.79h-1v-1.7h4.88q.5-.06.51.36a75.47,75.47,0,0,0,.13,10c0,.08,0,.13.07.13s0-.06.06-.15c0-.37,0-.94,0-1.69l1.49.46c-.22,2.13-.54,3.38-.95,3.76a.88.88,0,0,1-.63.27.86.86,0,0,1-.66-.31c-.71-.86-1.07-2.89-1.1-6.08-.36.15-.82.32-1.38.5q-.09.82-.24,1.8a9,9,0,0,1-1.52,3.79l-1.21-.44Zm-4-12.5h3.59v1.58h-3.59Zm0,3.81h3.59v1.58h-3.59Zm0,1.93h3.59v1.58h-3.59Zm2.48,3.51h-1.36s-.07,0-.06.07v2a.07.07,0,0,0,.06.09h1.36s.08-.05.08-.09v-2C122.77,21.36,122.74,21.34,122.69,21.34Zm5.62-4.22.27.83c0-1.05,0-2.34,0-3.85a.23.23,0,0,0-.2-.31h-1.14v3.75C127.7,17.37,128.05,17.24,128.31,17.12Z"/><path class="cls-1" d="M140.47,23.58a8.78,8.78,0,0,1-3.16,1.83l-.65-1a1.13,1.13,0,0,1-.3.67.94.94,0,0,1-.53.32c-.18,0-.67,0-1.46,0L134,23.58a3.26,3.26,0,0,0,1,.16.21.21,0,0,0,.12-.2v-2.7h-.87a21.9,21.9,0,0,1-.67,4.09L132,24.48a18.28,18.28,0,0,0,.82-5.63V12.52c0-.28.13-.42.37-.42h3.12a.38.38,0,0,1,.26.12.51.51,0,0,1,.12.3V16a2.07,2.07,0,0,0,.91-1.82V12.45a.37.37,0,0,1,.09-.27.36.36,0,0,1,.24-.1h4a.3.3,0,0,1,.24.1.52.52,0,0,1,.1.27v2.72a.47.47,0,0,0,0,.23.33.33,0,0,0,.23.09h.19a.83.83,0,0,0,.33-.12c.08-.06.11-.23.11-.52s0-.67,0-1.07l1.42.14c0,1.15-.08,1.86-.12,2.15a1,1,0,0,1-.32.66,1.25,1.25,0,0,1-.84.39h-1.29a1.07,1.07,0,0,1-.83-.34,1.37,1.37,0,0,1-.35-.92V14c0-.17-.09-.26-.24-.26h-1.25c-.14,0-.21.08-.21.23v.79a3.27,3.27,0,0,1-1.21,2.55h4.93l.93.88A15.83,15.83,0,0,1,142.64,21a10.12,10.12,0,0,1-1,1.4,13.46,13.46,0,0,0,2.87,1.41l-1,1.55A15.6,15.6,0,0,1,140.47,23.58Zm-6.14-7.9h.83V14a.3.3,0,0,0-.06-.14.16.16,0,0,0-.1,0h-.5c-.11,0-.17.06-.17.18Zm0,3.47h.83V17.37h-.83Zm2.78.67,1-.85h-.76V17.59h0l-.64-.89v7a13.26,13.26,0,0,0,2.46-1.29A19.71,19.71,0,0,1,137.11,19.82Zm1.38-.85a16.48,16.48,0,0,0,1.85,2.37A7.39,7.39,0,0,0,141.78,19Z"/><path class="cls-1" d="M147.85,24.16v.65h-1.52v-6.2l-.17.23L144.72,18a15.36,15.36,0,0,0,2.67-6.14l1.52.41a23,23,0,0,1-1.06,3.47V17a10.87,10.87,0,0,0,1.83-4l1.46.47a17.26,17.26,0,0,1-1.4,3.51h5.19a10.92,10.92,0,0,1-1-3.06c0-.13-.07-.2-.14-.2h-2.16V12.1h3.09c.23,0,.36.14.39.4a11.1,11.1,0,0,0,.71,2.78,8,8,0,0,0,1.48,2.26l-1.44.84h0c0,1.25,0,2.37,0,3.38a6.35,6.35,0,0,1-.43,2.37,1.88,1.88,0,0,1-.9,1,4.64,4.64,0,0,1-3.32-.19l0-2.05c1.16.54,2,.71,2.36.51a1.06,1.06,0,0,0,.49-.51,3.22,3.22,0,0,0,.18-.78,14.41,14.41,0,0,0,0-3.18.22.22,0,0,0-.2-.14h-1.87c-.2,3-1.28,5.18-3.26,6.5Zm2.57-4.22a11.08,11.08,0,0,0,.14-1.2h-1.18V17.63c-.18.25-.35.49-.51.69l-1-.63v6.16A6.19,6.19,0,0,0,150.42,19.94Z"/><path class="cls-1" d="M161.59,22.52v2.34H160v-7a19.42,19.42,0,0,1-1.6,1.14l-1-1.52.91-.55a16.65,16.65,0,0,0,2.77-2.48H158V12.79h4.3c.22-.38.45-.82.71-1.3l1.39.91a2.4,2.4,0,0,0-.2.39h5.11v1.68h-6.12l-.81,1.1h6.35c.27,0,.4.14.4.43v7.63a1.45,1.45,0,0,1-.74,1.28,3,3,0,0,1-1.28.43,8.61,8.61,0,0,1-2.19-.16l-.1-1.94a3.87,3.87,0,0,0,2.33.15.66.66,0,0,0,.41-.58v-.29Zm0-4.25h5.95V17.4a.14.14,0,0,0-.14-.14h-5.68a.12.12,0,0,0-.13.14Zm0,2.61h5.95v-1h-5.95Z"/><path class="cls-1" d="M180.2,21.87a7.77,7.77,0,0,0,2.49,2.06l-1.2,1.31a15,15,0,0,1-1.38-1.13,9.92,9.92,0,0,1-2.85-5.32h-.54v4.28c.63-.17,1.19-.33,1.67-.5l.35,1.66a19.9,19.9,0,0,1-4.59,1l-.27-1.69,1.32-.19V20.85A2.07,2.07,0,0,1,174,22.11a3.45,3.45,0,0,1-1.69,0v2.73H170.8V12.43a.31.31,0,0,1,0-.19.22.22,0,0,1,.17-.06h3.19l1,.76v-.29c0-.33.15-.49.44-.49h5.63a.43.43,0,0,1,.46.49v5.6c0,.36-.17.54-.49.54H178.8c0,.18.23.72.56,1.63a16.94,16.94,0,0,0,1.9-1.59l.93,1.35A15.47,15.47,0,0,1,180.2,21.87Zm-7.58-5.39.84-2.66h-1a.11.11,0,0,0-.09,0,.13.13,0,0,0,0,.1v6.46a1.51,1.51,0,0,0,1,.17.65.65,0,0,0,.37-.22c.25-.53,0-1.49-.92-2.86A1.07,1.07,0,0,1,172.62,16.48Zm2.58,2.71V13.26l-.9,2.83c-.18.61-.16,1,0,1.25A5.53,5.53,0,0,1,175.2,19.19Zm1.52-4.59h3.49v-.36q0-.39-.36-.39H177.1c-.26,0-.39.14-.38.42Zm3.49,1.69h-3.49v.81h3.08c.27,0,.41-.16.41-.46Z"/><path class="cls-1" d="M185.5,13.7l1.51.65a13.16,13.16,0,0,1-2.77,4.83l-1.41-1.09A11.49,11.49,0,0,0,185.5,13.7Zm7.61,10.07-9.58.88c-.11-.54-.25-1.13-.43-1.76l1.65-.13a53.76,53.76,0,0,0,2.93-6.43l1.45.81a43.28,43.28,0,0,1-2.39,5.45l5.49-.44c-.71-1.22-1.29-2.19-1.75-2.9l1.38-.92a55.49,55.49,0,0,1,3.32,5.94l-1.43.81C193.55,24.64,193.34,24.2,193.11,23.77Zm-6-11.68h4.78c.2,0,.33.05.41.29a10.49,10.49,0,0,0,1,3.13,9.33,9.33,0,0,0,2.11,2.54l-1.49,1.07a10.1,10.1,0,0,1-1.75-2.27,16.68,16.68,0,0,1-1-2.71c0-.24-.08-.35-.17-.35H187.1Z"/><path class="cls-1" d="M196.59,12.05h10.28c.33,0,.5.2.5.6v11a1.37,1.37,0,0,1-.74,1.13c-.91.58-2.33.61-4.27.08l0-1.9c1.53.54,2.51.71,2.95.5a.73.73,0,0,0,.46-.7V14c0-.19-.09-.29-.26-.29h-8.89Zm.28,2.39h7.84v1.69h-7.84Zm7.28,8c0,.17-.08.26-.23.26h-6.65c-.15,0-.22-.09-.22-.26V17.06c0-.16.08-.24.22-.24h6.65a.22.22,0,0,1,.23.24Zm-1.6-3.89h-3.91a.08.08,0,0,0-.07.07v2.3c0,.06,0,.09.07.09h3.91s.08,0,.08-.09v-2.3A.07.07,0,0,0,202.55,18.51Z"/><path class="cls-1" d="M58.83,38.5,61,38.29a2.6,2.6,0,0,0,.78,1.57,2.34,2.34,0,0,0,1.59.51,2.43,2.43,0,0,0,1.59-.45,1.35,1.35,0,0,0,.53-1.05,1,1,0,0,0-.22-.65,1.91,1.91,0,0,0-.79-.47c-.26-.09-.84-.24-1.75-.47a5.94,5.94,0,0,1-2.46-1.07,2.83,2.83,0,0,1-.53-3.72A2.91,2.91,0,0,1,61,31.39,5.44,5.44,0,0,1,63.17,31a4.56,4.56,0,0,1,3.07.9,3.18,3.18,0,0,1,1.08,2.38l-2.19.1a1.81,1.81,0,0,0-.6-1.2,2.16,2.16,0,0,0-1.38-.37,2.47,2.47,0,0,0-1.49.39.77.77,0,0,0-.35.67.84.84,0,0,0,.32.66,6,6,0,0,0,2,.73A10.61,10.61,0,0,1,66,36a3.11,3.11,0,0,1,1.2,1.1,3.31,3.31,0,0,1,.43,1.73,3.22,3.22,0,0,1-.52,1.74,3.12,3.12,0,0,1-1.46,1.21,6.23,6.23,0,0,1-2.36.39,4.65,4.65,0,0,1-3.15-1A4.11,4.11,0,0,1,58.83,38.5Z"/><path class="cls-1" d="M69,38a4.22,4.22,0,0,1,.51-2,3.49,3.49,0,0,1,1.44-1.48A4.31,4.31,0,0,1,73,34a3.9,3.9,0,0,1,2.91,1.16,4,4,0,0,1,1.14,2.92,4,4,0,0,1-1.15,3A3.87,3.87,0,0,1,73,42.2a4.58,4.58,0,0,1-2-.49,3.33,3.33,0,0,1-1.49-1.43A4.75,4.75,0,0,1,69,38Zm2.13.11a2.61,2.61,0,0,0,.55,1.79,1.81,1.81,0,0,0,2.73,0,2.7,2.7,0,0,0,.55-1.81,2.64,2.64,0,0,0-.55-1.77,1.81,1.81,0,0,0-2.73,0A2.61,2.61,0,0,0,71.1,38.1Z"/><path class="cls-1" d="M77.78,34.18h1.15v-.59a4,4,0,0,1,.21-1.48,1.68,1.68,0,0,1,.77-.79A2.94,2.94,0,0,1,81.34,31a5.75,5.75,0,0,1,1.74.27l-.28,1.45a4.07,4.07,0,0,0-1-.12.84.84,0,0,0-.64.21,1.22,1.22,0,0,0-.2.81v.55h1.55v1.63H81V42H78.93V35.81H77.78Z"/><path class="cls-1" d="M87.31,34.18v1.65H85.9V39a7.41,7.41,0,0,0,0,1.12.43.43,0,0,0,.18.26.58.58,0,0,0,.35.11,2.86,2.86,0,0,0,.84-.2l.17,1.61a4.24,4.24,0,0,1-1.64.31,2.71,2.71,0,0,1-1-.19,1.51,1.51,0,0,1-.66-.49,2,2,0,0,1-.29-.81,10.54,10.54,0,0,1-.07-1.46V35.83h-1V34.18h1V32.62l2.09-1.21v2.77Z"/><path class="cls-1" d="M88.78,31.2H93.1a12.91,12.91,0,0,1,1.92.1,3,3,0,0,1,1.13.45,3,3,0,0,1,.83.9,2.48,2.48,0,0,1,.33,1.27,2.57,2.57,0,0,1-.41,1.4,2.51,2.51,0,0,1-1.11,1,2.87,2.87,0,0,1,1.52,1,2.63,2.63,0,0,1,.53,1.63,3.24,3.24,0,0,1-.34,1.44,2.91,2.91,0,0,1-.94,1.11,3.07,3.07,0,0,1-1.47.51c-.36,0-1.24.07-2.63.08H88.78ZM91,33v2.5h1.43c.85,0,1.38,0,1.59,0a1.43,1.43,0,0,0,.88-.38,1.14,1.14,0,0,0,.32-.85,1.18,1.18,0,0,0-.27-.81,1.25,1.25,0,0,0-.83-.38c-.21,0-.84,0-1.86,0Zm0,4.3v2.9h2a10.3,10.3,0,0,0,1.5-.07,1.28,1.28,0,0,0,1.1-1.35,1.47,1.47,0,0,0-.23-.83,1.37,1.37,0,0,0-.69-.49,7.06,7.06,0,0,0-1.94-.16Z"/><path class="cls-1" d="M99.62,42V31.2h2.18V42Z"/><path class="cls-1" d="M115,38l2.12.67a4.8,4.8,0,0,1-1.62,2.63,4.61,4.61,0,0,1-2.88.86,4.65,4.65,0,0,1-3.54-1.47,5.61,5.61,0,0,1-1.39-4,5.89,5.89,0,0,1,1.4-4.19,4.77,4.77,0,0,1,3.67-1.5A4.47,4.47,0,0,1,116,32.19a4.28,4.28,0,0,1,1.11,2l-2.16.52a2.33,2.33,0,0,0-.8-1.33,2.28,2.28,0,0,0-1.48-.49,2.48,2.48,0,0,0-2,.86,4.24,4.24,0,0,0-.75,2.8,4.59,4.59,0,0,0,.74,2.93,2.39,2.39,0,0,0,1.92.87,2.2,2.2,0,0,0,1.5-.56A3.2,3.2,0,0,0,115,38Z"/><path class="cls-1" d="M118.51,38a4.22,4.22,0,0,1,.51-2,3.49,3.49,0,0,1,1.44-1.48,4.28,4.28,0,0,1,2.09-.51,3.92,3.92,0,0,1,2.91,1.16,4,4,0,0,1,1.14,2.92,4,4,0,0,1-1.15,3,3.87,3.87,0,0,1-2.89,1.17,4.58,4.58,0,0,1-2-.49A3.33,3.33,0,0,1,119,40.28,4.75,4.75,0,0,1,118.51,38Zm2.12.11a2.61,2.61,0,0,0,.56,1.79,1.81,1.81,0,0,0,2.73,0,2.7,2.7,0,0,0,.55-1.81,2.64,2.64,0,0,0-.55-1.77,1.81,1.81,0,0,0-2.73,0A2.61,2.61,0,0,0,120.63,38.1Z"/><path class="cls-1" d="M130.21,42h-2.08V34.18h1.93v1.11a3.51,3.51,0,0,1,.89-1,1.69,1.69,0,0,1,.9-.25,2.63,2.63,0,0,1,1.36.39l-.64,1.81a1.87,1.87,0,0,0-1-.34,1.15,1.15,0,0,0-.74.24,1.69,1.69,0,0,0-.48.87,12.81,12.81,0,0,0-.17,2.63Z"/><path class="cls-1" d="M134.05,34.18H136v1.15a2.94,2.94,0,0,1,1-1,2.86,2.86,0,0,1,1.42-.37,3,3,0,0,1,2.32,1.07,4.33,4.33,0,0,1,.95,3,4.48,4.48,0,0,1-1,3,3,3,0,0,1-2.32,1.09,2.69,2.69,0,0,1-1.18-.26,4,4,0,0,1-1.11-.89v4h-2.07Zm2,3.79a3,3,0,0,0,.52,2,1.61,1.61,0,0,0,1.28.63,1.49,1.49,0,0,0,1.2-.58,3,3,0,0,0,.48-1.9,2.84,2.84,0,0,0-.49-1.83,1.53,1.53,0,0,0-1.23-.6,1.61,1.61,0,0,0-1.26.59A2.64,2.64,0,0,0,136.1,38Z"/><path class="cls-1" d="M143.34,42V39.94h2.07V42Z"/><path class="cls-1" d="M151.81,42V31.29H154V40.2h5.43V42Z"/><path class="cls-1" d="M164.57,34.18v1.65h-1.42V39a9.29,9.29,0,0,0,0,1.12.45.45,0,0,0,.19.26.56.56,0,0,0,.35.11,2.74,2.74,0,0,0,.83-.2l.18,1.61a4.24,4.24,0,0,1-1.64.31,2.71,2.71,0,0,1-1-.19,1.51,1.51,0,0,1-.66-.49,1.88,1.88,0,0,1-.29-.81,9.11,9.11,0,0,1-.07-1.46V35.83h-.95V34.18h.95V32.62l2.08-1.21v2.77Z"/><path class="cls-1" d="M173.2,42h-1.93V40.87a3.06,3.06,0,0,1-1.13,1,2.93,2.93,0,0,1-1.32.33,3,3,0,0,1-2.31-1.09,4.45,4.45,0,0,1-1-3,4.4,4.4,0,0,1,.94-3,3.08,3.08,0,0,1,2.37-1,2.91,2.91,0,0,1,2.27,1.1V31.2h2.08Zm-5.54-4.09a3.62,3.62,0,0,0,.35,1.82,1.56,1.56,0,0,0,1.4.81,1.54,1.54,0,0,0,1.22-.61,2.84,2.84,0,0,0,.5-1.82,3.07,3.07,0,0,0-.49-2,1.6,1.6,0,0,0-2.48,0A2.65,2.65,0,0,0,167.66,37.93Z"/><path class="cls-1" d="M175.24,42V39.94h2.08V42Z"/></g></g><!-- Code injected by live-server -->
<script>
	// <![CDATA[  <-- For SVG support
	if ('WebSocket' in window) {
		(function () {
			function refreshCSS() {
				var sheets = [].slice.call(document.getElementsByTagName("link"));
				var head = document.getElementsByTagName("head")[0];
				for (var i = 0; i < sheets.length; ++i) {
					var elem = sheets[i];
					var parent = elem.parentElement || head;
					parent.removeChild(elem);
					var rel = elem.rel;
					if (elem.href && typeof rel != "string" || rel.length == 0 || rel.toLowerCase() == "stylesheet") {
						var url = elem.href.replace(/(&|\?)_cacheOverride=\d+/, '');
						elem.href = url + (url.indexOf('?') >= 0 ? '&' : '?') + '_cacheOverride=' + (new Date().valueOf());
					}
					parent.appendChild(elem);
				}
			}
			var protocol = window.location.protocol === 'http:' ? 'ws://' : 'wss://';
			var address = protocol + window.location.host + window.location.pathname + '/ws';
			var socket = new WebSocket(address);
			socket.onmessage = function (msg) {
				if (msg.data == 'reload') window.location.reload();
				else if (msg.data == 'refreshcss') refreshCSS();
			};
			if (sessionStorage && !sessionStorage.getItem('IsThisFirstTime_Log_From_LiveServer')) {
				console.log('Live reload enabled.');
				sessionStorage.setItem('IsThisFirstTime_Log_From_LiveServer', true);
			}
		})();
	}
	else {
		console.error('Upgrade your browser. This Browser is NOT supported WebSocket for Live-Reloading.');
	}
	// ]]>
</script>
</svg>