import Swal from 'sweetalert2';
import { useI18n } from 'vue-i18n';

export function useConfirm() {
	const { t } = useI18n();
	function alert({ text }) {
		Swal.fire({
			icon: 'error',
			text,
			showCloseButton: true,
			confirmButtonText: t('core.confirm'),
			customClass: {
				confirmButton: 'btn btn-danger'
			}
		});
	}

	function success({ text }) {
		Swal.fire({
			icon: 'success',
			text,
			showCloseButton: true,
			confirmButtonText: t('core.confirm'),
			customClass: {
				confirmButton: 'btn btn-success'
			}
		});
	}

	async function confirm({ text, onOk, onCancel }) {
		const result = await Swal.fire({
			icon: 'warning',
			text,
			showCloseButton: true,
			showCancelButton: true,
			confirmButtonText: t('core.confirm'),
			cancelButtonText: t('core.cancel'),
			reverseButtons: true,
			buttonsStyling: false,
			customClass: {
				confirmButton: 'btn btn-primary',
				cancelButton: 'btn btn-white'
			}
		});

		if (result.isConfirmed) onOk?.();
		else onCancel?.();
	}

	return { alert, confirm, success };
}
