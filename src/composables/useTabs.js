import api from '@/api/apiService';
import { computed, onMounted, ref } from 'vue';

// 抓取 API 的 tabs 資訊
// 這個 composable 函數會在組件中使用，提供 tabs 的 資訊和當前選中的 tab
// 以及 tab code 與 id 的雙向映射表
export function useTabs(menuCode) {
	const tabs = ref([]);
	const tab = ref(null);

	const codeIdMap	= computed(() => Object.fromEntries(tabs.value.map(tab => [tab.code, tab.id])));
	const idCodeMap	= computed(() => Object.fromEntries(tabs.value.map(tab => [tab.id, tab.code])));
	const componentId = computed(() => codeIdMap.value[tab.value]);

	async function fetchTabs() {
		const ret = await api.getMenuTabApi(menuCode);
		tabs.value = ret.data.map(x => ({ id: x.url, code: x.code, ref: x.code, name: x.name })) || [];
		tab.value = tabs.value[0]?.code ?? tab.value;
	}

	onMounted(fetchTabs);

	return { codeIdMap, idCodeMap, componentId, tab, tabs, refetch: fetchTabs };
}
