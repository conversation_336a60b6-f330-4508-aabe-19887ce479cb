<template>
	<!-- Modal 1-->
	<vue-modal :is-open="isOpenModal" @close="closeModal">
		<template #content="props">
			<div class="modal-dialog modal-dialog-centered modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<h4 id="myModalLabel" class="modal-title">
							活動報名{{ $t('mkt.view') }}
						</h4>
						<button type="button" class="btn-expand">
							<i class="bi bi-arrows-fullscreen" />
						</button>
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click.prevent="props.close()"
						/>
					</div>
					<div class="modal-body">
						<div class="card card-table mb-3">
							<div class="card-header">
								<h4>活動專案</h4>
							</div>
							<div class="table-responsive">
								<table class="table table-bordered table-RWD table-hover">
									<tbody>
										<tr>
											<th class="wd-15p">
												主辦單位：
											</th>
											<td class="wd-35p">
												{{ activityItem.branCode }}：{{ activityItem.branName }}
											</td>
											<th class="wd-15p">
												活動{{ $t('mkt.name') }}：
											</th>
											<td class="wd-35p">
												{{ activityItem.activityName }}
											</td>
										</tr>
										<tr>
											<th>活動地點：</th>
											<td>{{ activityItem.activityLocation }}</td>
											<th>合作對象：</th>
											<td>{{ activityItem.cooperationName }}</td>
										</tr>
										<tr>
											<th>預估費用：</th>
											<td>{{ activityItem.expectedFee }}</td>
											<th>活動形式：</th>
											<td>{{ activityItem.activityTypeName }}</td>
										</tr>
										<tr>
											<th>預期成效：</th>
											<td>
												<span v-if="fundIncYn == 'Y'">基金銷售增額:{{ activityItem.fundIncAmt }}</span>
												<span v-if="newCustomerYn == 'Y'">新開發客戶數:{{ activityItem.newCustomerCnt }}</span>
												<span v-if="fundIncYn == 'Y'">其他，請說明{{ activityItem.othersMemo }}</span>
											</td>
											<th>邀請函：</th>
											<td>
												<span v-if="activityItem.intitationYn == 'N'">無</span>
												<span v-if="activityItem.intitationYn == 'Y'">無</span>
												<span v-for="file in activityItem.invitationFile">
													<a class="tx-link" @click="fileDownloadHandler(file.fileId)">{{ file.showName }}</a>
												</span>
											</td>
										</tr>
										<tr>
											<th>說　　明</th>
											<td colspan="3">
												{{ activityItem.activityMemo }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="card card-table">
							<div class="card-header">
								<h4>活動場次資訊</h4>
							</div>
							<div class="table-responsive">
								<table class="table table-bordered table-RWD table-hover">
									<thead>
										<tr>
											<th width="15%">
												活動編號
											</th>
											<th width="15%">
												場次{{ $t('mkt.name') }}
											</th>
											<th width="15%">
												舉辦{{ $t('mkt.date') }}
											</th>
											<th width="15%">
												場次負責人
											</th>
											<th width="15%">
												預估人數
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="session in activityItem.sessionList">
											<td class="text-alignRight">
												{{ activityItem.activityCode }}-{{ session.sessionCode }}
											</td>
											<td class="text-alignRight">
												{{ activityItem.sessionName }}
											</td>
											<td class="text-alignRight">
												{{ $filters.formatDate(session.sessionDate) }}
											</td>
											<td class="text-alignRight">
												{{ session.sessionUserCode }} {{ session.sessionUserName }}
											</td>
											<td class="text-alignRight">
												{{ session.totalCnt }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="modalFooterId" class="modal-footer">
							<input
								id="over1"
								class="btn btn-white"
								type="button"
								value="關閉"
								@click.prevent="props.close()"
							>
						</div>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
</template>
<script>
import vueModal from '@/views/components/model.vue';
import _ from 'lodash';
export default {
	components: {
		vueModal
	},
	props: {},
	data: function () {
		return {
			fundIncYn: null,
			newCustomerYn: null,
			activityItem: {},
			isOpenModal: false
		};
	},
	methods: {
		getDetail: async function (eventId) {
			const self = this;
			if (_.isBlank(eventId)) {
				return;
			}
			const ret = await self.$api.getActivityEventDetail({
				eventId: eventId
			});
			self.activityItem = ret.data;
			self.isOpenModal = true;
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		fileDownloadHandler: function (fileId) {
			const self = this;
			self.$api.downloadFileApi({
				fileId: fileId,
				fileType: 'MktFiles'
			});
		}
	}
};
</script>
