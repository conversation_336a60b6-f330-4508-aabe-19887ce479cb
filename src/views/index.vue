<template>
	<div class="content ht-100v pd-0">
		<div v-if="!isShowSummary">
			<!-- <div> -->
			<div class="row g-3 main-index">
				<component
					:is="component.componentId"
					v-for="(component, index) in componentList"
					:key="component.componentId"
					@cus-code="doViewSummary($event)"
				/>
				<div id="myModalxl" class="modal fade">
					<div class="modal-dialog modal-dialog-centered modal-xl">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">
									{{ $t('gen.personalizeHomepageSettings') }}
								</h4>
								<button type="button" class="btn-expand">
									<i class="bi bi-arrows-fullscreen" />
								</button>
								<button
									type="button"
									class="btn-close"
									data-bs-dismiss="modal"
									aria-label="Close"
								/>
							</div>
							<div class="modal-body">
								<div class="card card-form card-collapse">
									<div class="card-header">
										<h4>{{ $t('gen.stepOneSelectFunctions') }}</h4>
										<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroupStep1" />
									</div>
									<div id="collapseListGroupStep1" class="collapse show">
										<div class="card-body">
											<table class="table table-RWD">
												<thead>
													<tr>
														<th width="5%">
															<input
																id="selectAll"
																class="form-check-input"
																type="checkbox"
																value="all"
															>
														</th>
														<th width="6%">
															<label for="selectAll">{{ $t('gen.item') }}</label>
														</th>
														<th width="14%">
															<label for="selectAll">{{ $t('gen.functionName') }}</label>
														</th>
														<th width="75%">
															<label for="select1">{{ $t('gen.functionDescription') }}</label>
														</th>
													</tr>
												</thead>
												<tbody>
													<tr>
														<td>
															<input
																id="select1"
																class="form-check-input"
																type="checkbox"
																name="user_active"
																value="s1"
															>
														</td>
														<td :data-th="$t('gen.item')">
															<label for="select1">1</label>
														</td>
														<td :data-th="$t('gen.functionName')">
															<label for="select1">{{ $t('gen.quickSearch') }}</label>
														</td>
														<td :data-th="$t('gen.functionDescription')">
															<label for="select1">{{ $t('gen.quickSearchDesc') }}</label>
														</td>
													</tr>
													<tr>
														<td>
															<input
																id="select2"
																class="form-check-input"
																type="checkbox"
																name="user_active"
																value="s2"
															>
														</td>
														<td :data-th="$t('gen.item')">
															<label for="select2">2</label>
														</td>
														<td :data-th="$t('gen.functionName')">
															<label for="select2">{{ $t('gen.todayMemos') }}</label>
														</td>
														<td :data-th="$t('gen.functionDescription')">
															<label for="select2">{{ $t('gen.todayMemosDesc') }}</label>
														</td>
													</tr>
													<tr>
														<td>
															<input
																id="select3"
																class="form-check-input"
																type="checkbox"
																name="user_active"
																value="s3"
															>
														</td>
														<td :data-th="$t('gen.item')">
															<label for="select3">3</label>
														</td>
														<td :data-th="$t('gen.functionName')">
															<label for="select3">{{ $t('gen.todaySchedule') }}</label>
														</td>
														<td :data-th="$t('gen.functionDescription')">
															<label for="select3">{{ $t('gen.todayScheduleDesc') }}</label>
														</td>
													</tr>
													<tr>
														<td>
															<input
																id="select4"
																class="form-check-input"
																type="checkbox"
																name="user_active"
																value="s4"
															>
														</td>
														<td :data-th="$t('gen.item')">
															<label for="select4">4</label>
														</td>
														<td :data-th="$t('gen.functionName')">
															<label for="select4">{{ $t('gen.customerImportantDates') }}</label>
														</td>
														<td :data-th="$t('gen.functionDescription')">
															<label for="select4">{{ $t('gen.customerImportantDatesDesc') }}</label>
														</td>
													</tr>
													<tr>
														<td>
															<input
																id="select5"
																class="form-check-input"
																type="checkbox"
																name="user_active"
																value="s5"
															>
														</td>
														<td :data-th="$t('gen.item')">
															<label for="select5">5</label>
														</td>
														<td :data-th="$t('gen.functionName')">
															<label for="select5">{{ $t('gen.eventNotificationBoard') }}</label>
														</td>
														<td :data-th="$t('gen.functionDescription')">
															<label for="select5">{{ $t('gen.eventNotificationBoardDesc') }}</label>
														</td>
													</tr>
													<tr>
														<td>
															<input
																id="select6"
																class="form-check-input"
																type="checkbox"
																name="user_active"
																value="s6"
															>
														</td>
														<td :data-th="$t('gen.item')">
															<label for="select6">6</label>
														</td>
														<td :data-th="$t('gen.functionName')">
															<label for="select6">{{ $t('gen.generalAnnouncements') }}</label>
														</td>
														<td :data-th="$t('gen.functionDescription')">
															<label
																for="select6"
															>{{ $t('gen.generalAnnouncementsDesc') }}</label>
														</td>
													</tr>
													<tr>
														<td>
															<input
																id="select7"
																class="form-check-input"
																type="checkbox"
																name="user_active"
																value="s7"
															>
														</td>
														<td :data-th="$t('gen.item')">
															<label for="select7">7</label>
														</td>
														<td :data-th="$t('gen.functionName')">
															<label for="select7">{{ $t('gen.researchInvestment') }}</label>
														</td>
														<td :data-th="$t('gen.functionDescription')">
															<label
																for="select7"
															>{{ $t('gen.researchInvestmentDesc') }}</label>
														</td>
													</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>

								<div class="card card-form">
									<div class="card-header">
										<h4>{{ $t('gen.stepTwoDragBlocks') }}</h4>
									</div>
									<div id="sortable" class="row g-3">
										<div id="block1" class="col-lg-4 sort-pic">
											<img :src="getImgURL('', 'section-1.png')" :alt="$t('gen.quickSearchBlockThumbnail')">
											<i id="b1Delete" class="bi bi-x-circle-fill" />
										</div>

										<div id="block2" class="col-lg-4 sort-pic">
											<img :src="getImgURL('', 'section-2.png')" :alt="$t('gen.todayMemosBlockThumbnail')">
											<i id="b2Delete" class="bi bi-x-circle-fill" />
										</div>

										<div id="block3" class="col-lg-4 sort-pic">
											<img :src="getImgURL('', 'section-3.png')" :alt="$t('gen.todayScheduleBlockThumbnail')">
											<i id="b3Delete" class="bi bi-x-circle-fill" />
										</div>

										<div id="block4" class="col-lg-4 sort-pic">
											<img :src="getImgURL('', 'section-4.png')" :alt="$t('gen.customerImportantDatesBlockThumbnail')">
											<i id="b4Delete" class="bi bi-x-circle-fill" />
										</div>

										<div id="block5" class="col-lg-8 sort-pic">
											<img :src="getImgURL('', 'section-5.png')" :alt="$t('gen.customerImportantDatesBlockThumbnail')">
											<i id="b5Delete" class="bi bi-x-circle-fill" />
										</div>

										<div id="block6" class="col-lg-6 sort-pic">
											<img :src="getImgURL('', 'section-6.png')" :alt="$t('gen.generalAnnouncementsBlockThumbnail')">
											<i id="b6Delete" class="bi bi-x-circle-fill" />
										</div>

										<div id="block7" class="col-lg-6 sort-pic">
											<img :src="getImgURL('', 'section-7.png')" :alt="$t('gen.researchInvestmentBlockThumbnail')">
											<i id="b7Delete" class="bi bi-x-circle-fill" />
										</div>
									</div>
								</div>
							</div>
							<div class="modal-footer">
								<button class="btn btn-white" data-bs-dismiss="modal">
									{{ $t('gen.closeWindow') }}
								</button>
								<button class="btn btn-primary" data-bs-dismiss="modal">
									{{ $t('gen.save') }}
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<vue-cus-summary
			v-show="isShowSummary"
			ref="cusSummary"
			:set-is-show-summary="setIsShowSummary"
			:set-show-search-result="setIsShowSearchResult"
		/>
		<!--頁面內容 end-->
	</div>
	<!-- <vue-modal :is-open="isOpenShutDownModal" @close="closeModal('shutDown')">
    <template v-slot:content="props">
      <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h4 class="modal-title">{{ $t('gen.shutdownAnnouncement') }}</h4>
          </div>
          <div class="modal-body">
            <div class="card">
              <div class="card-body">
                <table class="table table-bordered table-RWD table-horizontal-RWD" v-for="item in admShutDownInfo">
                  <tr>
                    <th class="w20">日期</th>
                    <td class="w80">{{ $filters.formatDate(item.startDt) }}</td>
                  </tr>
                  <tr>
                    <th>主題</th>
                    <td>{{ $filters.formatDateTime(item.startDt) }} ~
                      {{ $filters.formatDateTime(item.endDt) }}<br />
                      {{ item.shutdownDesc }}
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
          <div class="modal-footer d-flex justify-content-end" id="appointmentFooter">
            <input name="btnClose" class="btn btn-white" id="appointmentCloseButton" type="button" value="登出"
              @click="logout">
          </div>
        </div>
      </div>
    </template>
</vue-modal>

<vue-modal :is-open="isOpenBeforeShutDownModal" @close="closeModal('beforeShutDown')">
  <template v-slot:content="props">
      <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h4 class="modal-title">{{ $t('gen.shutdownAnnouncement') }}</h4>
            <button type="button" class="btn-close" aria-label="Close" @click.prevent="props.close()"></button>
          </div>
          <div class="modal-body">
            <div class="card">
              <div class="card-body">
                <table class="table table-bordered table-RWD table-horizontal-RWD" v-for="item in admShutDownInfo">
                  <tr>
                    <th class="w20">日期</th>
                    <td class="w80">{{ $filters.formatDate(item.startDt) }}</td>
                  </tr>
                  <tr>
                    <th>主題</th>
                    <td>{{ $filters.formatDateTime(item.startDt) }} ~
                      {{ $filters.formatDateTime(item.endDt) }}<br />
                      {{ item.shutdownDesc }}
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
          <div class="modal-footer d-flex justify-content-end">
            <input name="btnClose" class="btn btn-white" type="button" @click.prevent="props.close()" value="關閉">
          </div>
        </div>
      </div>
    </template>
</vue-modal>
 -->
	<vue-modal :is-open="isOpenForceReadModal" @close="closeModal('forceRead')">
		<template #content="props">
			<div class="modal-dialog modal-dialog-centered modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							強制閱讀
						</h4>
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click.prevent="closeForceRead(); props.close()"
						/>
					</div>
					<div class="modal-body">
						<div class="card">
							<div class="card-body">
								<form abineguid="349C13DE6B814CA4847A995042C1CE36">
									<table class="table table-bordered table-RWD table-horizontal-RWD">
										<tr>
											<th class="w20">
												日期
											</th>
											<th class="w70">
												主題
											</th>
											<th class="w10">
												檢視
											</th>
										</tr>
										<tr v-for="item in genForceReadMessage">
											<td>{{ $filters.formatDate(item.validBgnDt) }}</td>
											<td>{{ item.msgTitle }}</td>
											<td>
												<div>
													<button type="button" class="btn btn-dark  btn-icon" @click="getView(item.msgId)">
														<i
															class="bi bi-search"
														/>
													</button>
												</div>
											</td>
										</tr>
									</table>
								</form>
							</div>
						</div>
					</div>
					<div class="modal-footer d-flex justify-content-end">
						<input
							name="btnClose"
							class="btn btn-white"
							type="button"
							value="關閉"
							@click.prevent="closeForceRead(); props.close()"
						>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<vue-index-msg ref="internalMsg" />
	<vue-modal :is-open="isOpenMessageModal" @close="closeModal('message')">
		<template #content="props">
			<vue-index-message ref="message" />
		</template>
	</vue-modal>
</template>
<script>
import vueModal from '@/views/components/model.vue';
import _ from 'lodash';
import moment from 'moment';
import vueIndexEvent from './index/event.vue';
import vueIndexMsg from './indexMsg.vue';
import vueIndexMessage from './indexMessage.vue';
import vueCusSummary from './cus/include/cusSummary.vue';
import vueIndexBulletin from './index/bulletin.vue';
import vueIndexFinanceMsg from './index/financeMsg.vue';
import vueIndexCalendar from './index/calendar.vue';
import { getImgURL } from '@/utils/imgURL.js';
// 審核作業看板
import vueIndexPendingList from './index/audit.vue';
export default {
	components: {
		vueModal,
		vueIndexEvent,
		vueIndexMsg,
		vueIndexMessage,
		vueCusSummary,
		vueIndexBulletin,
		vueIndexFinanceMsg,
		vueIndexCalendar,
		vueIndexPendingList
	},
	data: function () {
		return {
			homeType: null,
			roleType: null,
			admShutDownInfo: [],
			shutDown: null,
			beforeShutDown: null,
			genForceReadMessage: [],
			forceRead: null,
			internalMsg: null,
			message: null,
			// 事件通知
			tdCat1Name: null,
			// 行事曆
			calendarTasks: [],
			// 指定名單
			myOpportunities: [],
			showOpprPage: false,
			// 待審核清單
			startDate: '2017-01-01',
			endDate: moment().add(1, 'days').format('YYYY-MM-DD'),
			memberApplyStartDate: moment().subtract(2, 'months').format('YYYY-MM-DD'),
			memberApplyEndDate: moment().format('YYYY-MM-DD'),
			wkfItems: [],
			// 商品文件
			proSpFiles: [],
			// 主頁顯示的元件
			componentList: [],

			contentUrl: null,

			isOpenShutDownModal: false,
			isOpenBeforeShutDownModal: false,
			isOpenForceReadModal: false,
			isOpenMessageModal: false,

			isShowSummary: false
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo'])
	},
	watch: {
		userInfo: function (newVal) {
			const self = this;
			if (newVal) {
				self.userCode = newVal.userCode;
				self.homeType = newVal.homeType;
				self.roleType = newVal.roleType;
				self.initPageDatas();
			}
		},
		cookieCurrency: function () {
			localStorage.setItem(this.cookieCurrencyKey, this.cookieCurrency);
		}
	},
	mounted: async function () {
		const self = this;

		await self.getHomeComponent();

		// await self.getAdmShutDownInfo();

		// if (self.admShutDownInfo.some((info) => _.isEqual(info.noticeFlag, 'DURING'))) {
		//   self.openModal('shutDown');
		// } else {
		//   if (self.admShutDownInfo.some((info) => _.isEqual(info.noticeFlag, 'BEFORE')) && !JSON.parse(sessionStorage.getItem('beforeShutDown'))) {
		//     self.openModal('beforeShutDown');
		//     sessionStorage.setItem('beforeShutDown', 'true');
		//   }

		await self.getGenForceReadMessage();
		if (!_.isEmpty(self.genForceReadMessage)) {
			self.openModal('forceRead');
		}
		else {
			self.closeModal('forceRead');
		}
		// }
	},
	methods: {
		getImgURL,
		openModal: function (modalName) {
			const capitalizedModalName = modalName.charAt(0).toUpperCase() + modalName.slice(1);
			this[`isOpen${capitalizedModalName}Modal`] = true;
		},
		closeModal: function (modalName) {
			const capitalizedModalName = modalName.charAt(0).toUpperCase() + modalName.slice(1);
			this[`isOpen${capitalizedModalName}Modal`] = false;
		},
		getAdmShutDownInfo: async function () {
			const self = this;
			const resp = await self.$api.getAdmShutDownInfoApi();
			self.admShutDownInfo = resp.data;
		},
		logout: function () {
			const self = this;
			self.$router.push('/logout');
		},
		getGenForceReadMessage: async function () {
			const self = this;
			const resp = await self.$api.getGenNewsMessageApi({ msgType: 'C' });
			self.genForceReadMessage = resp.data;
		},
		closeForceRead: async function () {
			const self = this;
			await self.$refs.internalMsg.getGenInternalMsg('Y');
			this.closeModal('forceRead');
			if (!self.$refs.internalMsg.isEmpty()) {
				self.$refs.internalMsg.show();
			}
		},
		closeBeforeShutDown: async function () {
			this.closeModal('beforeShutDown');
		},
		getView: function (msgId) {
			const self = this;
			self.openModal('message');
			self.$refs.message.getView(msgId);
		},
		initPageDatas: function () {
			const self = this;
			const homeType = self.homeType;

			// if (homeType == 'TYPE2' || homeType == 'TYPE3' || homeType == 'TYPE4') {
			// 	self.getWkfItems();
			// }
			//
			// if (homeType == 'TYPE1' || homeType == 'TYPE2' || homeType == 'TYPE3') {
			// 	self.getCalendarTasks();
			// }
			//
			// if (homeType == 'TYPE1') {
			// 	self.getMyOpportunities();
			// }
		},
		getWkfItems: async function () {
			const self = this;
			const resp = await self.$api.getMenuVerifyRecsApi({
				startDate: _.formatDate(self.startDate),
				endDate: _.formatDate(self.endDate)
			});
			self.wkfItems = ret.data;
		},
		getProFiles: async function () {
			const self = this;
			const ret = await self.$api.getProSpFilesApi();
			self.proSpFiles = ret.data;
		},
		downloadFile: async function (proSpFileId, fileName) {
			const self = this;
			const data = await self.$api.downloadProSpFilesApi({
				proSpFileId: proSpFileId
			});
			const link = document.createElement('a');
			const url = URL.createObjectURL(data);
			link.download = fileName;
			link.href = url;
			document.body.appendChild(link);
			link.click();
			link.remove();
			setTimeout(() => URL.revokeObjectURL(url), 1000);
		},
		getMyOpportunities: async function () {
			const self = this;
			const ret = await self.$api.getMktMyOpportunitiesApi();
			self.myOpportunities = ret.data;
		},
		doViewOppr: function (oppr) {
			const self = this;
			self.showOpprPage = true;
			this.$refs.myOpprPage.getOpprDetail(oppr);
		},
		showSearchList: function () {
			const self = this;
			self.showOpprPage = false;
		},
		setIsShowSummary(val) {
			this.isShowSummary = val;
		},
		setIsShowSearchResult(val) {
			this.isShowSummary = !val;
		},
		doViewSummary(cusCode) {
			this.isShowSummary = true;
			this.$refs.cusSummary.setCusCode(cusCode);
		},
		getHomeComponent: async function () {
			const self = this;
			const ret = await self.$api.getHomeComponentApi();
			self.componentList = ret.data;
			this.$forceUpdate();
		}
	}
};
</script>
