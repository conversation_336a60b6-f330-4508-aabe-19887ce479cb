<template>
	<div class="table-scroll table-5row belowtab">
		<vue-loading color="#3459e6" :active="Boolean(isLoading)" :is-full-page="false" />
		<table class="table table-RWD text-center mb-0 table-todo table-striped table-hover ">
			<thead>
				<tr>
					<th rowspan="2" data-group="group1" class="text-start">
						{{ $t('core.eventCategory') }}
					</th>
					<th data-group="group3" colspan="3">
						{{ $t('core.numberOfEventsPending') }}
					</th>
				</tr>
				<tr>
					<th data-group="group3" width="17%">
						{{ $t('core.newGenerated') }}
					</th>
					<th data-group="group3" width="17%">
						{{ $t('core.notExpired') }}
					</th>
					<th data-group="group3" width="17%">
						{{ $t('core.expired') }}
					</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="tdItem in tdItemStats">
					<td class="text-start" :data-th="$t('core.eventCategory')">
						{{ tdItem.itemName }}
					</td>
					<td :data-th="$t('core.newGenerated')">
						<template v-if="tdItem.todayTdCount > 0">
							<RouterLink class="tx-link" v-bind="goTaskMgn(tdItem, 'TO_DAY_TD')">
								{{ tdItem.todayTdCount }}
							</RouterLink>
						</template>
						<template v-else>
							0
						</template>
					</td>
					<td :data-th="$t('core.notExpired')">
						<template v-if="tdItem.notTodayTdCount > 0">
							<RouterLink class="tx-link" v-bind="goTaskMgn(tdItem, 'NOT_TO_DAY_TD')">
								{{ tdItem.todayTdCount }}
							</RouterLink>
						</template>
						<template v-else>
							0
						</template>
					</td>
					<td :data-th="$t('core.expired')">
						<template v-if="tdItem.expireTdCount > 0">
							<RouterLink class="tx-link" v-bind="goTaskMgn(tdItem, 'EXPIRE_TD')">
								{{ tdItem.todayTdCount }}
							</RouterLink>
						</template>
						<template v-else>
							0
						</template>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>
<script>
import _ from 'lodash';
import VueLoading from 'vue-loading-overlay';
export default {
	components: {
		'vue-loading': VueLoading
	},
	props: {
		tdCatCode: String
	},
	data: function () {
		return {
			tdItemStats: {},
			isLoading: 0
		};
	},
	computed: {
		itemTypeMap() {
			return new Map([
				['TO_DAY_TD', this.$t('core.newGenerated')],
				['NOT_TO_DAY_TD', this.$t('core.notExpired')],
				['EXPIRE_TD', this.$t('core.expired')]
			]);
		}
	},
	watch: {},
	mounted: function () {
		const self = this;
		self.getTdItemStats();
	},
	methods: {
		// Get statistics for each event: new generated, not expired, expired
		getTdItemStats: async function () {
			const self = this;
			self.isLoading++;
			const resp = await self.$api.getTdItmeStatApi({
				tdCat1Code: self.tdCatCode
			});
			const data = _.isArray(resp.data) ? resp.data : [];
			self.tdItemStats = _.orderBy(
				data,
				[
					function (item) {
						return item.toptenNum != null ? item.toptenNum : Infinity;
					},
					'showOrder'
				],
				['asc', 'asc']
			);
			self.isLoading--;
		},
		goTaskMgn: function ({ tdCat1Code, itemCode }, toDoItemType) {
			return { to: {
				name: 'taskMgn',
				query: {
					tdCat1Code,
					itemCode,
					toDoItemTypeName: this.itemTypeMap.get(toDoItemType)
				}
			} };
		}
	}
};
</script>
