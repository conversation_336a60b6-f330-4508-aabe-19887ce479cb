<template>
	<div class="table-scroll table-5row">
		<vue-loading color="#3459e6" :active="Boolean(isLoading)" :is-full-page="false" />
		<table class="table table-RWD table-striped table-hover">
			<thead>
				<tr>
					<th width="30%">
						{{ $t('core.announcementDate') }}
					</th>
					<th>{{ $t('core.announcementTitle') }}</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="list in messageList">
					<td :data-th="$t('core.announcementDate')">
						{{ $filters.formatDate(list.validBgnDt) }}~{{ $filters.formatDate(list.validEndDt) }}
					</td>
					<td :data-th="$t('gen.announcementTitle')">
						<ColoredLink @click="showMessage(list.msgId)">
							{{ list.msgTitle }}
						</ColoredLink>
						<img v-if="showNew(list.validBgnDt)" class="ml-1" src="@/assets/images/icon/icon_new.gif">
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="text-end">
		<RouterLink :to="{name: 'bbsHead', query: {msgCode}}" replace>
			{{ $t('core.more') }}
		</RouterLink>
	</div>

	<vue-modal :is-open="isOpenModal" @close="closeModal()">
		<template #content>
			<vue-index-message ref="message" />
		</template>
	</vue-modal>
</template>
<script>
import vueModal from '@/views/components/model.vue';
import VueLoading from 'vue-loading-overlay';
import moment from 'moment';
import { getImgURL } from '@/utils/imgURL';
import vueIndexMessage from '../../indexMessage.vue';
export default {
	components: {
		vueModal,
		vueIndexMessage,
		'vue-loading': VueLoading
	},
	props: {
		msgCode: String
	},
	data: function () {
		return {
			messageList: [],
			pageable: {
				page: 0,
				size: 10,
				sort: 'VALID_BGN_DT',
				direction: 'DESC'
			},
			isOpenModal: false,
			isLoading: 0
		};
	},
	watch: {},
	mounted: async function () {
		const self = this;
		self.getMessageList();
	},
	methods: {
		getImgURL,
		getMessageList: async function () {
			const self = this;
			self.isLoading++;
			const resp = await self.$api.getBbsMgtPageData(
				self.pageable,
				{
					msgCode: self.msgCode,
					expiredYn: 'N',
					showYn: 'Y'
				}
			);

			self.messageList = resp.data.content;
			self.isLoading--;
		},
		showNew: function (validBgnDt) {
			return moment().format('yyyy/MM/DD') === validBgnDt;
		},
		showMessage: function (msgId) {
			this.openModal();
			this.$refs.message.getView(msgId);
		},
		openModal() {
			this.isOpenModal = true;
		},
		closeModal() {
			this.isOpenModal = false;
		}
	}
};
</script>
