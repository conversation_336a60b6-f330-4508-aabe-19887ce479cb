<template>
	<div class="table-scroll table-5row">
		<table class="table table-RWD table-striped table-hover">
			<thead>
				<tr>
					<th width="30%">
						{{ $t('core.announcementDate') }}
					</th>
					<th>{{ $t('core.announcementTitle') }} </th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="(list) in msgList">
					<td :data-th="$t('core.announcementDate')" class="text-center">
						{{ $filters.formatDate(list.validDt) }}
					</td>
					<td :data-th="$t('gen.announcementTitle')">
						<a href="#" class="tx-link" @click.prevent="viewSelDoc(list.docId)">{{ list.docName }}</a>
						<img v-if="showNew(list.validDt)" :src="getImgURL('icon', 'icon_new.gif')">
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="text-end">
		<RouterLink v-bind="moreLinkProps" replace>
			{{ $t('core.more') }}
		</RouterLink>
	</div>

	<vue-modal v-if="isOpenModal" :is-open="isOpenModal" @close="closeModal">
		<template #content="props">
			<div class="modal-dialog modal-lg modal-dialog-centered">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('core.documentQuery') }}
						</h4>
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click.prevent="props.close()"
						/>
					</div>
					<div class="modal-body">
						<div class="card card-form mb-3">
							<div class="card-header">
								<h4>{{ $t('core.documentCategory') }}</h4>
							</div>
							<table class="table table-RWD table-horizontal-RWD table-bordered">
								<tbody>
									<tr>
										<th class="wd-15p">
											{{ $t('core.documentType') }}
										</th>
										<td class="wd-85p">
											{{ selDoc?.docCatName }}
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div class="card card-form mb-3">
							<div class="card-header">
								<h4>{{ $t('core.documentContent') }}</h4>
							</div>
							<table class="table table-RWD table-horizontal-RWD table-bordered">
								<tbody>
									<tr>
										<th class="wd-15p tx-require">
											{{ $t('core.documentTitle') }}
										</th>
										<td class="wd-85p">
											{{ selDoc?.docName }}
										</td>
									</tr>
									<tr>
										<th class="tx-require">
											{{ $t('core.effectiveDate') }}
										</th>
										<td>{{ $filters.formatDate(selDoc?.validDt) }}</td>
									</tr>
									<tr>
										<th class="tx-require">
											{{ $t('core.expiryDate') }}
										</th>
										<td>{{ $filters.formatDate(selDoc?.expireDt) }}</td>
									</tr>
									<tr>
										<th class="tx-require">
											{{ $t('core.urgencyLevel') }}
										</th>
										<td>{{ selDoc?.priorityName }}</td>
									</tr>
									<tr>
										<th class="tx-require">
											{{ $t('core.availableToCustomers') }}
										</th>
										<td>{{ selDoc?.showCusName }}</td>
									</tr>
									<tr>
										<th>{{ $t('core.summary') }}</th>
										<td>{{ selDoc?.docDesc }}</td>
									</tr>
									<tr>
										<th>{{ $t('core.attachedFiles') }}</th>
										<td>
											<template v-for="file in selDoc?.fileInfo">
												<a href="#" @click="viewFile(file.docFileId)">{{ file.showName }}</a>
											</template>
										</td>
									</tr>
									<tr>
										<th>{{ $t('core.issuingInstitution') }}</th>
										<td>
											{{ issuers }}
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div class="card card-form mb-3">
							<div class="card-header">
								<h4>{{ $t('core.maintenanceInfo') }}</h4>
							</div>
							<table class="table table-RWD table-horizontal-RWD table-bordered">
								<tbody>
									<tr>
										<th class="wd-15p">
											{{ $t('core.creator') }}
										</th>
										<td class="wd-85p">
											{{ selDoc?.createBy }}
										</td>
									</tr>
									<tr>
										<th>{{ $t('core.createDate') }}</th>
										<td>{{ $filters.formatDate(selDoc?.createDt) }}</td>
									</tr>
									<tr>
										<th>{{ $t('core.lastMaintainer') }}</th>
										<td>{{ selDoc?.modifyBy }}</td>
									</tr>
									<tr>
										<th>{{ $t('core.lastMaintenanceDate') }}</th>
										<td>{{ $filters.formatDate(selDoc?.modifyDt) }}</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
					<div class="modal-footer d-flex justify-content-end">
						<button
							id="modalCloseButton"
							type="button"
							class="btn btn-white"
							@click.prevent="props.close()"
						>
							{{ $t('core.closeWindow') }}
						</button>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
</template>
<script>
import vueModal from '@/views/components/model.vue';
import { getImgURL } from '@/utils/imgURL';
import moment from 'moment';

export default {
	components: {
		vueModal
	},
	props: {
		catCode: String
	},
	data: function () {
		return {
			selDoc: null,
			isOpenModal: false,
			isLoading: 0,
			cache: new Map()
		};
	},
	computed: {
		issuers: function () {
			return this.selDoc?.issuerInfo?.map(item => item.issuerName).join(',');
		},
		msgList() {
			return this.cache.get(this.catCode) ?? [];
		},
		moreLinkProps: function () {
			const destination = {
				ISSUER: 'docIssuers',
				MKTEVENT: 'docSales',
				MKTOUTLOOK: 'docResearch',
				NEWS: 'docNews',
				PRODUCT: 'docProDCD'
			}[this.catCode.toUpperCase()];
			return destination
				? { to: { name: destination } }
				: { to: '#' };
		}
	},
	watch: {
		catCode: {
			handler(val) {
				if (!val) return;
				this.getMsgList();
			},
			immediate: true
		}
	},
	unmounted() {
		this.cache.clear();
	},
	methods: {
		getImgURL,
		getMsgList: async function () {
			const self = this;
			const catCode = self.catCode.toUpperCase();
			if (this.cache.has(catCode)) return;
			const resp = await self.$api.getDocHomeListApi({
				docCat: catCode
			});
			this.cache.set(catCode, resp.data);
		},
		showNew: function (validDt) {
			return moment().format('yyyy/MM/DD') === validDt;
		},
		goFinanceInfo: function () {},
		viewSelDoc: async function (docId) {
			const self = this;
			const ret = await self.$api.getViewSelDoc({
				docCat: self.catCode,
				docId: docId
			});

			console.log('viewSelDoc: ', docId, ret.data);
			if (!ret.data?.length > 0) return;
			self.selDoc = ret.data[0];
			self.isOpenModal = true;
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		viewFile: async function (fileId) {
			const self = this;
			await self.$api.downloadFileApi({ fileType: 'GenDocFiles', fileId });
		}
	}
};
</script>
