<template>
	<div class="col-lg-6 mb-3">
		<div class="card card-table card-collapse">
			<div class="card-header">
				<h4><i class="fa-solid fa-blue fas fa-calendar" />{{ $t('core.calendar') }}</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#block05" />
			</div>
			<div id="block05" class="collapse show">
				<div class="tab-nav-line">
					<ul class="nav nav-line px-3">
						<li class="nav-item">
							<a
								href="#"
								class="nav-link"
								:class="{ active: tab === 1 }"
								@click.prevent="tab = 1"
							>{{ $t('core.todayItinerary') }}</a>
						</li>
						<li class="nav-item">
							<a
								href="#"
								class="nav-link"
								:class="{ active: tab === 2 }"
								@click.prevent="tab = 2"
							>{{ $t('core.todayCustomers') }}</a>
						</li>
					</ul>
					<div class="tab-content" style="position:relative">
						<vue-loading color="#3459e6" :active="Boolean(isLoading)" :is-full-page="false" />
						<div v-if="tab === 1" class="tab-pane" :class="{ active: tab === 1 }">
							<div class="table-scroll table-5row belowtab">
								<table class="bih-table table table-RWD">
									<thead>
										<tr>
											<th>{{ $t('core.date') }}</th>
											<th>{{ $t('core.time') }}</th>
											<th>{{ $t('core.itinerary') }}</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="calendar in calendarList">
											<td>{{ $filters.formatDate(calendar.nextRemindDt) }}</td>
											<td>{{ calendar.nextRemindTime }}</td>
											<td class="text">
												{{ calendar.title }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div v-if="tab === 2" class="tab-pane" :class="{ active: tab === 2 }">
							<div class="table-scroll table-5row belowtab">
								<table class="bih-table table table-RWD">
									<thead>
										<tr>
											<th>{{ $t('core.customerName') }}</th>
											<th>{{ $t('core.description') }}</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="cusMemory in cusMemoryTasks">
											<td>{{ cusMemory.cusName }}</td>
											<td>{{ cusMemory.note }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import VueLoading from 'vue-loading-overlay';
import moment from 'moment';
export default {
	components: {
		'vue-loading': VueLoading
	},
	props: {},
	data: function () {
		return {
			tab: 1,
			sysDt: moment().format('YYYY/MM/DD'),
			calendarList: [],
			cusMemoryTasks: [],
			isLoading: 0
		};
	},
	watch: {},
	mounted: async function () {
		const self = this;
		self.getCalendarTasks();
	},
	methods: {
		getCalendarTasks: async function () {
			const self = this;

			self.isLoading++;

			const ret = await self.$api.getCalendarTasksApi({
				startDate: self.sysDt,
				endDate: self.sysDt
			});

			if (ret.data) {
				const appointmentTasks = ret.data.appointmentTasks || [];
				const personalTasks = ret.data.personalTasks || [];
				const contactTasks = ret.data.contactTasks || [];
				const cusMemoryTasks = ret.data.cusMemoryTasks || [];

				const calendarList = [].concat(appointmentTasks, personalTasks, contactTasks);

				self.calendarList = calendarList;
				self.cusMemoryTasks = cusMemoryTasks;
			}
			self.isLoading--;
		}
	}
};
</script>
