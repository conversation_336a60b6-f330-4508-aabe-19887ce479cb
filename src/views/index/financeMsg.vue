<template>
	<div class="col-lg-6">
		<div class="card card-table card-collapse">
			<div class="card-header">
				<h4><img :src="getImgURL('icon', 'icon-note.svg')" alt="note icon">{{ $t('core.financialInformation') }}</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#block07" />
			</div>
			<div id="block07" class="collapse show">
				<div class="tab-nav-line">
					<Tabs
						v-model="tab"
						:tabs="docHome"
						tab-label="catName"
						tab-value="catCode"
						class="px-3"
						variant="line"
					>
						<template #content="{tab : currentTab, active}">
							<span>{{ currentTab.label }}</span>
							<Badge class="ml-1" :color="active ? 'primary' : 'dark'">
								{{ currentTab.cnt ?? 0 }}
							</Badge>
						</template>
					</Tabs>
				</div>
				<vue-index-finance-msg-detail v-if="tab" :cat-code="tab" />
			</div>
		</div>
	</div>
</template>
<script>
import { getImgURL } from '@/utils/imgURL';
import vueIndexFinanceMsgDetail from './include/financeMsgDetail.vue';
export default {
	components: {
		vueIndexFinanceMsgDetail
	},
	data: function () {
		return {
			tab: null,
			docHome: []
		};
	},
	mounted: async function () {
		this.getDocHome();
	},
	methods: {
		getImgURL,
		getDocHome: async function () {
			const self = this;
			const resp = await self.$api.getDocHomeApi();
			self.docHome = resp.data;
		},
		active: function (i) {
			return this.tab == i;
		}
	}
};
</script>
