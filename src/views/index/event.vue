<template>
	<div class="col-lg-6 mb-3">
		<div class="card card-table card-collapse">
			<div class="card-header">
				<h4><img :src="getImgURL('icon', 'icon-note.svg')" alt="note icon">{{ $t('core.eventProcessing') }}</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#block02" />
			</div>
			<div id="block02" class="collapse show">
				<div class="tab-nav-line">
					<ul class="nav nav-line px-3">
						<li v-for="(item, i) in tdItemCatToDoMenu" class="nav-item">
							<a class="nav-link" :class="{ active: active(i) }" @click="tab = i">{{ item.tdCat1Name }}<span
								class="badge"
							>{{ item.cnt ? item.cnt : 0 }}</span></a>
						</li>
					</ul>
					<template v-for="(item, index) in tdItemCatToDoMenu">
						<div class="tab-content">
							<div role="tabpanel" class="tab-pane" :class="{ active: index == tab }">
								<vue-index-event-detail :td-cat-code="item.tdCat1Code" />
							</div>
						</div>
					</template>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { getImgURL } from '@/utils/imgURL';
import vueIndexEventDetail from './include/eventDetail.vue';
export default {
	components: {
		vueIndexEventDetail
	},
	data: function () {
		return {
			tdItemCatToDoMenu: [
				{
					tdCat1Code: 'U',
					tdCat1Name: this.$t('core.custom')
				}
			],
			tdItemCounts: [],
			tab: 0
		};
	},
	watch: {},
	mounted: function () {
		const self = this;
		self.getTdItemCatToDoMenu();
	},
	methods: {
		getImgURL,
		getTdItemCatToDoMenu: async function () {
			const self = this;
			const resp = await self.$api.getTdItemCat1ToDoMenuApi();
			if (resp.data && resp.data.length > 0) {
				self.tdItemCatToDoMenu.push(...resp.data);
			}
			self.getTdItemCounts();
			self.getTdItemCustomizeCount();
		},
		// Get total count for each event
		getTdItemCounts: async function () {
			const self = this;
			const ret = await self.$api.getTdItmeCountApi({ groupCode: null });
			self.tdItemCounts = ret.data;
			self.tdItemCatToDoMenu.forEach(function (menu) {
				menu.cnt = 0;
				self.tdItemCounts.forEach(function (item) {
					if (menu.tdCat1Code == item.tdCat1Code) {
						menu.cnt = item.count;
					}
				});
			});
		},
		// Get custom event total count
		getTdItemCustomizeCount: async function () {
			const self = this;
			const resp = await self.$api.getTdItemCustomizeCountApi();
			const customizeItem = self.tdItemCatToDoMenu.find(function (item) {
				return item.tdCat1Code === 'U';
			});

			if (customizeItem) {
				customizeItem.cnt = resp.data.count;
			}
		},
		active: function (i) {
			return this.tab == i;
		}
	}
};
</script>
