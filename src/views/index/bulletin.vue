<template>
	<div class="col-lg-6">
		<div class="card card-table card-collapse">
			<div class="card-header">
				<h4><img :src="getImgURL('icon', 'icon-note.svg')" alt="note icon">{{ $t('core.generalAnnouncements') }}</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#block06" />
			</div>
			<div id="block06" class="collapse show">
				<div class="tab-nav-line">
					<ul class="nav nav-line px-3">
						<li v-for="(item, i) in messageMap" class="nav-item" @click="tab = i">
							<a
								href="#"
								class="nav-link"
								:class="{ active: active(i) }"
								data-bs-toggle="tab"
							>
								{{ item.msgName }}
								<span class="badge">{{ item.cnt }}</span>
							</a>
						</li>
					</ul>
					<template v-for="(item, index) in messageMap">
						<div class="tab-content">
							<div role="tabpanel" class="tab-pane fade show" :class="{ active: index == tab }">
								<vue-index-bulletin-detail :msg-code="item.msgCode" />
							</div>
						</div>
					</template>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { getImgURL } from '@/utils/imgURL';
import vueIndexBulletinDetail from './include/bulletinDetail.vue';

export default {
	components: {
		vueIndexBulletinDetail
	},
	props: {},
	data: function () {
		return {
			messageMap: [],
			tab: 0
		};
	},
	watch: {},
	mounted: function () {
		const self = this;
		self.getMessageMap();
	},
	methods: {
		getImgURL,
		getMessageMap: async function () {
			const self = this;
			const resp = await self.$api.getMessageMapHomeApi({
				msgType: ['B']
			});
			self.messageMap = resp.data;
		},
		active: function (i) {
			return this.tab == i;
		}
	}
};
</script>
