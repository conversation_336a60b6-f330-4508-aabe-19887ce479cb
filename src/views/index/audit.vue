<template>
	<div class="col-lg-6 mb-3">
		<div class="card card-table card-collapse ">
			<div class="card-header">
				<h4><img :src="getImgURL('icon', 'icon-note.svg')" alt="note icon">{{ $t('core.auditWorkBoard') }}</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#block08" />
			</div>
			<div id="block08" class="collapse show">
				<div class="tab-nav-line">
					<ul class="nav nav-line px-3">
						<li class="nav-item">
							<a href="#tabGeneral" data-bs-toggle="tab" class="nav-link active">{{ $t('core.generalWorkAudit') }}<span
								class="badge"
							>{{ genCnt }}</span></a>
						</li>
						<li class="nav-item">
							<a href="#tabClient" data-bs-toggle="tab" class="nav-link">{{ $t('core.customerWorkAudit') }}<span class="badge">{{

								cusCnt }}</span></a>
						</li>
						<li class="nav-item">
							<a href="#tabProduct" data-bs-toggle="tab" class="nav-link">{{ $t('core.productWorkAudit') }}<span
								class="badge"
							>{{ proCnt }}</span></a>
						</li>
					</ul>
					<div class="tab-content">
						<div id="tabGeneral" class="tab-pane fade active show">
							<div class="table-scroll table-5row belowtab">
								<table class="table table-RWD text-center mb-0 table-todo table-striped table-hover ">
									<thead>
										<tr>
											<th rowspan="2" data-group="group1" class="text-start">
												{{ $t('core.pendingAuditItems') }}
											</th>
											<th data-group="group3" colspan="2">
												{{ $t('core.pendingAuditCount') }}
											</th>
										</tr>
										<tr>
											<th data-group="group3" width="23%">
												{{ $t('core.submittedForAudit') }}
											</th>
											<th data-group="group3" width="22%">
												{{ $t('core.pendingAudit') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<template v-if="genReview.some(obj => obj.reviewYn === 'Y')">
											<tr v-for="item in genReview">
												<td class="text-start" :data-th="$t('core.pendingAuditItems')">
													{{ item.menuName }}
												</td>
												<td :data-th="$t('core.submittedForAudit')">
													<template v-if="item.submitCnt > 0">
														<router-link class="tx-link" :to="item.progClassname + '?submitReview=true'">
															{{
																item.submitCnt }}
														</router-link>
													</template>
													<template v-else>
														0
													</template>
												</td>
												<td :data-th="$t('core.pendingAudit')">
													<template v-if="item.verifyCnt > 0">
														<router-link class="tx-link" :to="item.progClassname + '?pendingReview=true'">
															{{
																item.verifyCnt
															}}
														</router-link>
													</template>
													<template v-else>
														0
													</template>
												</td>
											</tr>
										</template>
									</tbody>
								</table>
							</div>
						</div>

						<div id="tabClient" class="tab-pane fade">
							<div class="table-scroll table-5row belowtab">
								<table class="table table-RWD text-center mb-0 table-todo table-striped table-hover ">
									<thead>
										<tr>
											<th rowspan="2" data-group="group1" class="text-start">
												{{ $t('core.pendingAuditItems') }}
											</th>
											<th data-group="group3" colspan="2">
												{{ $t('core.pendingAuditCount') }}
											</th>
										</tr>
										<tr>
											<th data-group="group3" width="23%">
												{{ $t('core.underReview') }}
											</th>
											<th data-group="group3" width="22%">
												{{ $t('core.pending') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<template v-if="cusReview.some(obj => obj.reviewYn === 'Y')">
											<tr v-for="item in cusReview">
												<td class="text-start" :data-th="$t('core.pendingItems')">
													{{ item.menuName }}
												</td>
												<td :data-th="$t('core.underReview')">
													<template v-if="item.submitCnt > 0">
														<router-link
															v-if="(userInfo.roleCode === 'AO01' || userInfo.roleCode === 'AO02') && item.menuName === $t('core.highAssetQualificationApplication')"
															class="tx-link"
															to="/wob/pbsOpenReview"
														>
															{{ item.submitCnt }}
														</router-link>
														<router-link
															v-else-if="(userInfo.roleCode === 'AO01' || userInfo.roleCode === 'AO02') && item.menuName === $t('core.serviceChangeApplication')"
															class="tx-link"
															to="/wob/cusApplyReview"
														>
															{{ item.submitCnt }}
														</router-link>
														<router-link v-else class="tx-link" :to="item.progClassname">
															{{ item.submitCnt
															}}
														</router-link>
													</template>
													<template v-else>
														0
													</template>
												</td>
												<td :data-th="$t('gen.pending')">
													<template v-if="item.verifyCnt > 0">
														<router-link class="tx-link" :to="item.progClassname">
															{{ item.verifyCnt }}
														</router-link>
													</template>
													<template v-else>
														0
													</template>
												</td>
											</tr>
										</template>
									</tbody>
								</table>
							</div>
						</div>
						<div id="tabProduct" class="tab-pane fade">
							<div class="table-scroll table-5row belowtab">
								<table class="table table-RWD text-center mb-0 table-todo table-striped table-hover ">
									<thead>
										<tr>
											<th rowspan="2" data-group="group1" class="text-start">
												{{ $t('core.pendingAuditItems') }}
											</th>
											<th data-group="group3" colspan="2">
												{{ $t('core.pendingAuditCount') }}
											</th>
										</tr>
										<tr>
											<th data-group="group3" width="23%">
												{{ $t('core.submittedForAudit') }}
											</th>
											<th data-group="group3" width="22%">
												{{ $t('core.pendingAudit') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<template v-if="proReview.some(obj => obj.reviewYn === 'Y')">
											<tr v-for="item in proReview">
												<td class="text-start" :data-th="$t('core.pendingAuditItems')">
													{{ item.menuName }}
												</td>
												<td :data-th="$t('core.totalCases')">
													<template v-if="item.submitCnt > 0">
														<router-link class="tx-link" :to="item.progClassname">
															{{ item.submitCnt }}
														</router-link>
													</template>
													<template v-else>
														0
													</template>
												</td>
												<td :data-th="$t('core.newGenerated')">
													<template v-if="item.verifyCnt > 0">
														<router-link class="tx-link" :to="item.progClassname">
															{{ item.verifyCnt }}
														</router-link>
													</template>
													<template v-else>
														0
													</template>
												</td>
											</tr>
										</template>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { getImgURL } from '@/utils/imgURL';
import _ from 'lodash';

export default {
	data: function () {
		return {
			genReview: [],
			cusReview: [],
			proReview: []
		};
	},
	computed: {
		genCnt: function () {
			return _.sumBy(this.genReview, 'submitCnt') + _.sumBy(this.genReview, 'verifyCnt');
		},
		cusCnt: function () {
			return _.sumBy(this.cusReview, 'submitCnt') + _.sumBy(this.cusReview, 'verifyCnt');
		},
		proCnt: function () {
			return _.sumBy(this.proReview, 'submitCnt') + _.sumBy(this.proReview, 'verifyCnt');
		},
		...mapState(useUserInfoStore, ['userInfo'])
	},
	mounted: function () {
		const self = this;
		self.getHomepageGenReviewCnt();
		self.getHomepageCusReviewCnt();
		self.getHomepageProReviewCnt();
	},
	methods: {
		getImgURL,
		getHomepageGenReviewCnt: async function () {
			const self = this;
			const resp = await self.$api.getHomepageGenReviewCntApi();
			self.genReview = resp.data;
		},
		getHomepageCusReviewCnt: async function () {
			const self = this;
			const resp = await self.$api.getHomepageCusReviewCntApi();
			self.cusReview = resp.data;
		},
		getHomepageProReviewCnt: async function () {
			const self = this;
			const resp = await self.$api.getHomepageProReviewCntApi();
			self.proReview = resp.data;
		}
	}
};
</script>
