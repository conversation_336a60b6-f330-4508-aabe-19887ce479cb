<template>
	<div>
		<ul class="bi-tree-root">
			<tree-node
				v-for="node in cfg.data"
				:key="node.code"
				ref="treeNode"
				:data="node"
				:expand-on-load="cfg.expandOnLoad"
				:on-expand="cfg.onExpand"
				:on-collapse="cfg.onCollapse"
				:generate-checkbox="cfg.generateCheckbox"
				:checkbox-behavior="cfg.checkboxBehavior"
				:checkbox-name="cfg.checkboxName"
				:add-remove-y-n="cfg.addRemoveYN"
				:on-label-hover-over="cfg.onLabelHoverOver"
				:on-label-hover-out="cfg.onLabelHoverOut"
				:on-click="cfg.onClick"
				:node-suffix="cfg.nodeSuffix"
				:url="cfg.url"
				:extra-param="cfg.extraParam"
				:tail-links="cfg.tailLinks"
				:on-label-click="cfg.onLabelClick"
				:label-action="cfg.labelAction"
				:on-check="cfg.onCheck"
				:on-un-check="cfg.onUnCheck"
				:action="action"
			/>
		</ul>
	</div>
</template>

<script>
import TreeNode from './biTreeNode.vue';

export default {
	name: 'BiTree',
	components: {
		TreeNode
	},
	props: ['treeCfg', 'treeData', 'expandOnLoad', 'action'],
	data() {
		return {
			cfg: {
				/* Callbacks
                The callbacks should be functions that take one argument. The checkbox tree
                will return the jQuery wrapped LI element of the item that was checked/expanded.
                */
				onExpand: function () {},
				onCollapse: function () {},
				onCheck: function () {},
				onUnCheck: function () {},
				onLabelHoverOver: function () {},
				onLabelHoverOut: function () {},
				/* Valid choices: 'expand', 'check' */
				labelAction: 'expand',
				/**
				 * function(id){} a function to call when onClick, textNode is the scope
				 */
				onLabelClick: null,
				// currently only support json, may add html format if needed later
				type: 'json',
				/**
				 * if true, will expand the nodes with checked checkbox
				 * if no generateCheckbox = false, will expand all
				 */
				expandOnLoad: this.expandOnLoad || false,
				// name for the checkbox default is "id"
				checkboxName: 'id',
				// whether to generate checkbox
				generateCheckbox: false,
				/**
				 * behavior of how checking one check box will affect(check/half-check/uncheck) parent and children
				 * 'half-check-nocheck' <-- when check one will half-check/check parent, but checkbox status remain uncheck until all children are checked
				 * 'half-check-check' default <-- when check one will half-check/check parent, but checkbox status will be checked as long as one child is checked
				 * 'none' <-- checking one does not affect others
				 */
				checkboxBehavior: 'half-check-check',
				/**
				 * links to be added at the end of the text of each node
				 * link param
				 * {
				 *     type: 'button':'anchor', //default is 'anchor'
				 *     url:'',
				 *     imageUrl:'', //if provided, the imageUrl will be used to create a img tag and text will be used as alt. this works for both  "type: 'button':'anchor'"
				 *     cssCls:'',//css class
				 *     text: '',
				 *     onClick : function(id, node{li}){} ,
				 *     toApply : function(data) {}// if returned false then the link will not apply to the specific node
				 *                                // if not given then this link will be applied to all nodes
				 * }
				 */
				tailLinks: [],
				/**
				 * append suffix to node
				 * string or function
				 *
				 */
				nodeSuffix: null,
				/**
				 * if "root" is given and "data" is not an array,
				 * will find the real tree data in "data" with attribute equals to root
				 */
				root: null,
				/**
				 * data must be in the form of an array, if not a "root" must be provided for the attribut name of data array
				 * date or url either one must be given if type is 'json'
				 * data format for one tree, if more than one tree, then give data as array or multiple objects
				 * {
				 *     text : '', the label of the node
				 *     id : '', the id of the node will also be the value of the checkbox if a value is given for 'checked'
				 *     checked : if generateCheckbox then whether the checkbox is checked or not
				 *     leaf : false, if leaf is false and children is empty and a url is given, then will attempt to load leaf children once with url and id asynchronously when it is clicked the first time
				 *     children : [], the children configurations
				 *     iconCls : '' cls to apply to the node for an icon in the front
				 *     tailLinks : []//will overide the global links
				 * }
				 */
				data: null,
				// url to load the data from server
				url: null,
				// extra global parameters to pass back to the server
				extraParam: {},
				// whether to clear content in the DIV before insert the tree
				clear: true,
				addRemoveYN: false
			}
		};
	},
	created() {
		const cfg = this.cfg;
		this.cfg = { ...cfg, ...this.treeCfg, data: this.treeData };
		// if is json and no data provided, will load data asynchronously
		if (this.cfg.type === 'json' && !this.cfg.data) {
			this.cfg.data = this.loadData();
		}
		else if (this.cfg.root && !Array.isArray(this.cfg.data)) {
			this.cfg.data = this.cfg.data[this.cfg.root];
		}

		if (!this.cfg.data) {
			throw {
				name: 'biTreeError',
				message: 'Data is empty.'
			};
		}
	},
	methods: {
		async loadData() {
			const response = await fetch(this.cfg.data.url, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(this.cfg.extraParam) // 附加參數
			});
			const data = await response.json();
			return data;
		},
		// 展開/收合所有節點的方法
		expandAllNodes(isExpand) {
			this.$refs.treeNode.forEach((node) => {
				node.expandAllNodes(isExpand);
			});
		}
	}
};
</script>

<style scoped>
@import '@/assets/css/bi/bi.tree.css';
</style>
