<template>
	<div style="display: flex; align-items: center; gap: .5rem;">
		<label style="color: currentColor; flex-shrink: 0;">{{ $t('core.language') }}:</label>
		<select v-model="selected">
			<option v-for="lang in languages" :key="lang.value" :value="lang.value">
				{{ lang.label }}
			</option>
		</select>
	</div>
</template>
<script setup>
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';

const i18nStore = useI18nStore();
const route = useRoute();
const { t } = useI18n();

const languages = computed(() =>	i18nStore.availableLocales.map(locale => ({ value: locale, label: t(`core.locales.${locale}`) })));

const selected = computed({
	get: () => i18nStore.locale,
	set: value => i18nStore.setLocale(value)
});

watch(() => i18nStore.locale, (newVal) => {
	selected.value = newVal;
});

watch(() => route.path, () => {
	i18nStore.setLocale(selected.value);
});
</script>
