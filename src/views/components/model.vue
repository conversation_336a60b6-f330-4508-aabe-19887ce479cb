<template>
	<teleport to="body">
		<div :id="modalId" ref="modal" class="modal fade">
			<slot name="content" :open="open" :close="close" />
		</div>
	</teleport>
</template>

<script>
import { Modal } from 'bootstrap';
export default {
	props: {
		isOpen: {
			type: Boolean,
			default: false
		},
		id: String
	},
	emits: ['open', 'close'],
	data: function () {
		return {
			modal: null,
			defaultId: 'modal' + this.$_.now() + this.$_.random(0, 99)
		};
	},
	computed: {
		modalId: function () {
			return this.id ? this.id : this.defaultId;
		}
	},
	watch: {
		isOpen: {
			handler: function (newVal) {
				if (newVal) {
					this.open();
				}
				else {
					this.close();
				}
				/**
				 * TODO: 待決議
				 * * 自製 modal 或整合使用 bootstrap modal，並處理 beforeOpen 跟 beforeClose callback
				 * * 或改使用更懶人的 UI Lib (大工程!!!), e.g. Nuxt UI, PrimeVue...
				 */
				// if (newVal == true) {
				// 	this.$nextTick(function () {
				// 		this.open();
				// 	});
				// } else if (newVal == false) {
				// 	this.$nextTick(function () {
				// 		this.close();
				// 	});
				// }
			},
			immediate: true
		}
	},
	mounted: function () {
		this.$nextTick(() => {
			this.initModal();
		});
	},
	beforeUnmount: function () {
		if (this.modal) {
			this.modal.dispose();
		}
	},
	methods: {
		initModal() {
			if (!this.$refs.modal) return;
			if (this.modal) {
				this.modal.dispose(); // 清理現有實例
			}
			this.modal = new Modal(this.$refs.modal);

			// 添加事件監聽
			this.$refs.modal.addEventListener('hidden.bs.modal', () => {
				// Modal 完全隱藏後的回調
				this.$emit('close');
			});

			this.$refs.modal.addEventListener('shown.bs.modal', () => {
				// Modal 完全顯示後的回調
				this.$emit('open');
			});
		},
		open() {
			if (!this.modal) {
				this.initModal();
			}

			try {
				this.modal.show();
			}
			catch {
				this.initModal();
				this.modal?.show();
			}
		},
		close() {
			if (!this.modal) return;
			this.modal.hide();
		}
	}
};
</script>
