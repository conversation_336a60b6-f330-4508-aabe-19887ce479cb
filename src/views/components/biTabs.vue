<template>
	<div class="tab-nav-main">
		<ul class="nav nav-pills" role="tablist">
			<li v-for="t in showTabs" class="nav-item cursor-pointer" @click="selectedCode = t.code">
				<a class="nav-link" :class="{ active: selectedCode === t.code }">{{ tabNameDecorator(t) }}</a>
			</li>
		</ul>
		<div class="tab-content">
			<slot
				:id="templateProps?.id"
				:ref="templateProps?.ref"
				:code="templateProps?.code"
				:tab-name="templateProps?.name"
			/>
		</div>
	</div>
</template>

<script>
export default {
	components: {},
	props: {
		menuCode: {
			type: String,
			required: true
		},
		defaultTabCode: {
			type: String,
			required: false
		},
		tabNameDecorator: {
			type: Function,
			default: tab => tab.name
		}
	},
	data: function () {
		return {
			showTabs: [],
			selectedCode: ''
		};
	},
	computed: {
		templateProps: function () {
			return this.showTabs.find(x => x.code === this.selectedCode);
		}
	},
	watch: {
		selectedCode: function (newVal) {
			this.logging();
			this.$emit('change-tab', newVal);
		}
	},
	mounted: function () {
		this.getMenus();
	},
	methods: {
		getMenus: async function () {
			const ret = await this.$api.getMenuTabApi(this.menuCode);
			this.showTabs = ret.data.map(x => ({ id: x.url, code: x.code, ref: x.code, name: x.name })) || [];
			this.selectedCode = this.showTabs.find(x => x.code === this.defaultTabCode)?.code || this.showTabs[0]?.code || '';
		},
		logging: async function () {
			const menuCode = this.showTabs.find(tabs => tabs.code === this.selectedCode)?.code;
			this.$api.postLoggingApi({
				menuCode: menuCode
			});
		}
	}
};
</script>
