<template>
	<nav v-if="pageable !== null && pageable.totalPages > 0" aria-label="Page navigation" class="bi-pages">
		<span v-if="pageable.totalPages" class="page-current">共{{ pageable.totalPages }}頁</span>
		<ul class="pagination pagination-sm">
			<li class="page-item First-left-etc" @click.prevent="clickPage(first)">
				<a class="page-link" href="#" />
			</li>
			<li class="page-item left-etc" @click.prevent="clickPage(prev)">
				<a class="page-link" href="#" />
			</li>
			<li class="page-item">
				<select
					id="sel1"
					v-model="queryPage"
					class="form-select"
					@change="clickPage(queryPage - 1)"
				>
					<option v-for="pageNum in pageable.totalPages" :value="pageNum" :label="pageNum">
						{{ pageNum }}
					</option>
				</select>
			</li>
			<li class="page-item right-etc" @click.prevent="clickPage(next)">
				<a class="page-link" href="#" />
			</li>
			<li class="page-item Last-right-etc" @click.prevent="clickPage(last)">
				<a class="page-link" href="#" />
			</li>
		</ul>
	</nav>
</template>

<script>
export default {
	props: {
		gotoPage: Function,
		pageable: {
			default: null
		}, // 分頁資料
		pageSize: {
			default: 5
		}, // 最多顯示幾個分頁
		showInfo: {
			default: true
		} // 是否顯示分頁資訊
	},
	data: function () {
		return {
			first: 0,
			last: 0,
			prev: 0,
			next: 0,
			pages: [],
			nowPage: 0,
			queryPage: 1,
			totalElements: 0,
			size: 0,
			numberOfElements: 0
		};
	},
	computed: {
		showPrev: function () {
			return this.prev >= 0 && this.nowPage !== this.first;
		},
		showNext: function () {
			return this.next >= 0 && this.nowPage !== this.last;
		}
	},
	watch: {
		pageable: {
			handler: function (newVal, oldVal) {
				this.$nextTick(function () {
					this.changePageDebounce();
				});
				if (newVal.pageable) {
					this.queryPage = newVal.pageable.pageNumber + 1;
				}
			},
			immediate: true
		}
	},
	created: function () {},
	mounted: function () {
		this.changePageDebounce = this.$_.debounce(function () {
			this.changePage();
		}, 100);
	},
	methods: {
		clickPage: function (page) {
			if (page < 0) {
				return;
			}
			if (page < this.first || page > this.last) {
				return;
			}
			if (page === this.nowPage) {
				return;
			}
			this.queryPage = page + 1;
			this.gotoPage(page);
		},
		isNowPage: function (page) {
			return this.nowPage === page;
		},
		changePage: function () {
			if (this.$_.isEmpty(this.pageable) || this.pageable.totalPages <= 0) {
				this.first = -1;
				this.last = -1;
				this.prev = -1;
				this.next = -1;
				this.pages = [];
				this.nowPage = -1;
				this.totalElements = -1;
				this.size = -1;
				this.numberOfElements = -1;
				return;
			}

			const halfSize = Math.floor(this.pageSize / 2);
			let start = this.pageable.number - halfSize;
			let rightAdd = 0;
			if (start < 0) {
				rightAdd = 0 - start;
				start = 0;
			}

			let end = this.pageable.number + halfSize + rightAdd;
			const endPage = this.pageable.totalPages - 1;
			let leftAdd = 0;
			if (end > endPage) {
				leftAdd = end - endPage;
				end = endPage;
			}

			start = start - leftAdd;
			if (start < 0) {
				start = 0;
			}

			const pages = [];
			for (let i = start; i <= end; i++) {
				pages.push(i);
			}

			let prevPage = this.pageable.number - 1;
			if (prevPage < 0) {
				prevPage = 0;
			}
			let nextPage = this.pageable.number + 1;
			if (nextPage > endPage) {
				nextPage = endPage;
			}

			this.pages = pages;
			this.first = 0;
			this.last = endPage;
			this.prev = prevPage;
			this.next = nextPage;
			this.nowPage = this.pageable.number;
			this.totalElements = this.pageable.totalElements;
			this.size = this.pageable.size;
			this.numberOfElements = this.pageable.numberOfElements;
		}
	}
};
</script>
