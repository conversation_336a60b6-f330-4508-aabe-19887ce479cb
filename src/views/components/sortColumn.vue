<template>
	<slot
		:is-sorting="isSorting"
		:on-sort="onSort"
		:direction="direction"
		:sort="sort"
		:icon-classes="iconClasses"
	/>
</template>

<script>
export default {
	name: 'SortColumn',
	inject: ['getSortColumns'],
	props: {
		colname: {
			type: String,
			required: true
		}
	},
	computed: {
		sort() {
			return this.getSortColumns().sort;
		},
		direction() {
			return this.getSortColumns().direction;
		},
		isSorting() {
			return this.colname === this.sort;
		},
		iconClasses() {
			if (!this.isSorting) return ['bi', 'bi-chevron-expand'];
			if (this.direction === 'ASC') return ['bi', 'bi-caret-up-fill'];
			if (this.direction === 'DESC') return ['bi', 'bi-caret-down-fill'];
			return [];
		}
	},
	methods: {
		async onSort() {
			const context = this.getSortColumns();
			if (this.isSorting) {
				context.$emit('update:direction', this.direction === 'ASC' ? 'DESC' : 'ASC');
			}
			else {
				context.$emit('update:sort', this.colname);
			}
			await this.$nextTick();
			context.$emit('sortChange');
		}
	}
};
</script>
