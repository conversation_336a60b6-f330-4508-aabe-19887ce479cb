<template>
	<div class="row">
		<div class="col-12">
			<DataTable
				:rows="wkfEvents"
				:columns="columns"
				list
				:title="$t('wkf.pendingReviewList')"
			>
				<template #body-cell-varItem="{item, columnIndex}">
					<span v-html="item.itemDatas[columnIndex-2]?.varItemValue" />
				</template>
				<template #body-cell-detail="{item}">
					<button type="button" class="btn btn-dark btn-icon" @click="getDetail(item.eventId)">
						<i class="bi bi-search" />
					</button>
				</template>
				<template #body-cell-status="{item}">
					<ul v-for="(chekcItem, index) in item.wkfEngineFlows" class="list-unstyled mb-0">
						<li v-if="item.buttonYn == 'Y'">
							<div class="form-check">
								<input
									:id="item.eventId + index"
									v-model="item.status"
									class="form-check-input"
									type="radio"
									:name="item.eventId"
									:value="chekcItem.actionStatus"
								>
								<label class="form-check-label" :for="item.eventId + index">{{ chekcItem.actionName }}</label>
							</div>
						</li>
					</ul>
				</template>
				<template #body-cell-note="{item}">
					<textarea
						v-model="item.desc"
						class="form-control"
						rows="3"
						:disabled="item.status != 'R'"
					/>
				</template>
			</DataTable>
		</div>
		<div class="col-12 mt-3 text-end">
			<input
				class="btn btn-primary btn-lg"
				type="button"
				:value="$t('wkf.reviewComplete')"
				@click="audit()"
			>
		</div>
	</div>
	<adm-role-review-detail ref="admRoleReviewDetail" />
	<vue-mkt-camp-review-detail ref="mktCampReviewDetail" />
	<user-account-detail ref="userAccountDetail" />

	<vue-pro-mgt-review-detail v-if="wfgId === 'WFG20241203001'" ref="proMgtReviewDetail" />
	<vue-pro-new-review-detail v-if="wfgId === 'WFG20241203002'" ref="proNewReviewDetail" />
	<vue-pro-pfcats-review-detail v-if="wfgId === 'WFG20241203003'" ref="proPfcatsReviewDetail" />
</template>

<script>
import userAccountDetail from './include/userAccountDetail.vue';
import admRoleReviewDetail from './include/admRoleReviewDetail.vue';
import vueMktCampReviewDetail from '@/views/mkt/MKT106/include/mktCampReviewDetail.vue';
import vueProMgtReviewDetail from '@/views/pro/PrdReview/proMgtReviewDetail.vue';
import vueProNewReviewDetail from '@/views/pro/PrdAdd/proNewReviewDetail.vue';
import vueProPfcatsReviewDetail from '@/views/pro/PrdVerify/proPfcatsReviewDetail.vue';
import { convertWkfResponse } from '@/utils/convertWkfResponse';
import { biModule } from '@/utils/bi/module';
import DataTable from '@/components/DataTable.vue';
export default {
	components: {
		userAccountDetail,
		admRoleReviewDetail,
		vueMktCampReviewDetail,
		vueProMgtReviewDetail,
		vueProNewReviewDetail,
		vueProPfcatsReviewDetail,
		DataTable
	},
	data: function () {
		return {
			// 主要顯示資料
			wkfEvents: [],
			varItems: [],
			wfgId: null
		};
	},
	computed: {
		columns() {
			return [
				{ label: this.$t('wkf.submitDate'), field: 'createDt' },
				{ label: this.$t('wkf.submitter'), field(item) { return `${item.createBy} ${item.userName}`; } },
				...this.varItems.map(varItem => ({ name: 'varItem', label: varItem.varItemName })),
				{ name: 'detail', label: this.$t('wkf.applicationDetail'), bodyClass: 'text-center' },
				{ name: 'status', label: this.$t('wkf.reviewStatus'), headerClass: 'wd-100', bodyClass: 'text-start' },
				{ name: 'note', label: this.$t('wkf.otherNotes'), headerClass: 'wd-10p' }
			];
		}
	},
	watch: {
		'$route.params.wfgId': function () {
			this.wfgId = this.$route.params.wfgId;
			this.getVarItems();
			this.getWkfEvents();
		}
	},
	created() {
		this.wfgId = this.$route.params.wfgId;
	},
	mounted: function () {
		this.getVarItems();
		this.getWkfEvents();
	},
	methods: {
		getVarItems: async function () {
			const ret = await this.$api.getVarItemsApi(this.wfgId);
			this.varItems = ret.data;
		},
		getWkfEvents: async function () {
			const ret = await this.$api.getWkfEventsApi(this.wfgId);
			this.wkfEvents = ret.data;
		},
		getDetail: function (eventId) {
			if (this.$_.isEqual(this.wfgId, 'WFG20121005008')) {
				// System Role Maintenance
				this.$refs.admRoleReviewDetail.getDetail(eventId);
			}
			if (this.$_.isEqual(this.wfgId, 'WFG20241118001')) {
				// Activity Creation Review
				this.$refs.mktCampReviewDetail.getDetail(eventId);
			}
			if (this.$_.isEqual(this.wfgId, 'WFG20141112001')) {
				// System User Management Review
				this.$refs.userAccountDetail.getDetail(eventId);
			}
			if (this.$_.isEqual(this.wfgId, 'WFG20241203001')) {
				// Product Data Review
				this.$refs.proMgtReviewDetail.getDetail(eventId);
			}
			if (this.$_.isEqual(this.wfgId, 'WFG20241203002')) {
				// New Product Temporary Listing Review
				this.$refs.proNewReviewDetail.getDetail(eventId);
			}
			if (this.$_.isEqual(this.wfgId, 'WFG20241203003')) {
				// Featured Recommended Product Review
				this.$refs.proPfcatsReviewDetail.getDetail(eventId);
			}
		},
		audit: async function () {
			const wkfEvents = this.wkfEvents;
			const updateRoleMenuLogs = [];
			const self = this;
			for (let i = 0; i < wkfEvents.length; i++) {
				const wkfEvent = wkfEvents[i];
				if (wkfEvent.status == 'A' || wkfEvent.status == 'R') {
					if (wkfEvent.status == 'R' && this.$_.isBlank(wkfEvent.desc)) {
						this.$bi.alert(this.$t('wkf.pleaseEnterRejectReason'));
						return;
					}
					updateRoleMenuLogs.push(wkfEvent);
				}
			}

			if (this.$_.isEmpty(updateRoleMenuLogs)) {
				this.$bi.alert(this.$t('wkf.noDataToReview'));
				return;
			}

			let finishCnt = 0;
			for (let i = 0; i < updateRoleMenuLogs.length; i++) {
				const roleMenuLog = updateRoleMenuLogs[i];

				const ret = await this.$api.patchAuditApi({
					eventId: roleMenuLog.eventId,
					actionCode: this.$_.filter(roleMenuLog.wkfEngineFlows, ['actionStatus', roleMenuLog.status])[0].actionCode,
					desc: roleMenuLog.desc
				});

				const processResult = convertWkfResponse(ret.data, true);
				if (processResult.success && processResult.value) biModule.alert(processResult.value);
				else if (!processResult.success && processResult.error) biModule.alert(processResult.error);
				finishCnt++;

				if (finishCnt == updateRoleMenuLogs.length) {
					this.$swal
						.fire({
							icon: 'success',
							text: this.$t('wkf.reviewComplete') + '.',
							showCloseButton: true,
							confirmButtonText: this.$t('wkf.confirm'),
							buttonsStyling: false,
							customClass: {
								confirmButton: 'btn btn-success'
							}
						})
						.then(function () {
							self.getWkfEvents();
						});
				}
			}
		}
	}
};
</script>
