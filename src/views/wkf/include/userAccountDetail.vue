<template>
	<modal ref="modal" @close="closeModal">
		<template #content="props">
			<div class="modal-dialog modal-dialog-centered modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<h4 id="detailModal" class="modal-title">
							{{ $t('wkf.applicationDetail') }}
						</h4>
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click="props.close()"
						/>
					</div>
					<div class="modal-body">
						<table class="table table-bordered">
							<caption>
								{{ $t('wkf.roleSettingPreview') }}
							</caption>
							<tbody>
								<tr>
									<th>{{ $t('wkf.role') }}</th>
									<th>{{ $t('wkf.accountAvailability') }}</th>
								</tr>
								<tr v-for="item in userPosEventItem">
									<td :data-th="$t('wkf.role')">
										<span>{{ item.branName }} > {{ item.roleName }}</span>
									</td>
									<td :data-th="$t('wkf.accountAvailability')">
										<span>{{ $t('wkf.status') }}：{{ item.startFlag == 'Y' ? $t('wkf.enabled') : $t('wkf.closed') }}</span>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</template>
	</modal>
</template>

<script>
import modal from '@/views/components/model.vue';
export default {
	components: {
		modal
	},
	data: function () {
		return {
			userPosEventItem: []
		};
	},
	methods: {
		getDetail: async function (eventId) {
			const ret = await this.$api.getUserAccountDetailApi(eventId);
			if (!ret.data) return;
			this.userPosEventItem = ret.data;
			this.$refs.modal.open();
		},
		closeModal: function () {
			this.$refs.modal.close();
		}
	}
};
</script>
