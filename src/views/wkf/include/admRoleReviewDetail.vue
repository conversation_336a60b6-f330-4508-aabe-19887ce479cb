<template>
	<modal ref="modal" @close="closeModal">
		<template #content="props">
			<div class="modal-dialog modal-dialog-centered modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<h4 id="detailModal" class="modal-title">
							{{ $t('wkf.applicationDetail') }}
						</h4>
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click="props.close()"
						/>
					</div>
					<div class="modal-body" style="overflow: scroll">
						<table class="table table-bordered">
							<caption>
								{{ $t('wkf.functionalMenu') }}
							</caption>
							<tbody>
								<tr>
									<th colspan="8">
										{{ $t('wkf.systemRole') }}：{{ roleName }}
									</th>
								</tr>
								<tr>
									<th>{{ $t('wkf.firstLevelMenu') }}</th>
									<th>{{ $t('wkf.secondLevelMenu') }}</th>
									<th>{{ $t('wkf.thirdLevelMenu') }}</th>
									<th>{{ $t('wkf.fourthLevelMenu') }}</th>
									<th>{{ $t('wkf.fifthLevelMenu') }}</th>
									<th>{{ $t('wkf.query') }}</th>
									<th>{{ $t('wkf.edit') }}</th>
									<th>{{ $t('wkf.verify') }}</th>
									<th>{{ $t('wkf.export') }}</th>
								</tr>
								<tr v-for="item in roleMenuLogs">
									<td :data-th="$t('wkf.firstLevelMenu')">
										{{ item.firMenuName }}
									</td>
									<td :data-th="$t('wkf.secondLevelMenu')" class="text-start">
										{{ item.secMenuName }}
									</td>
									<td :data-th="$t('wkf.thirdLevelMenu')">
										{{ item.thiMenuName }}
									</td>
									<td :data-th="$t('wkf.fourthLevelMenu')">
										{{ item.fouMenuName }}
									</td>
									<td :data-th="$t('wkf.fifthLevelMenu')">
										{{ item.fifMenuName }}
									</td>
									<td :data-th="$t('wkf.query')">
										<div class="form-check form-check-inline">
											<input
												class="form-check-input"
												type="checkbox"
												disabled
												:checked="item.view"
											>
										</div>
									</td>
									<td :data-th="$t('wkf.edit')">
										<div class="form-check form-check-inline">
											<input
												class="form-check-input"
												type="checkbox"
												disabled
												:checked="item.edit"
											>
										</div>
									</td>
									<td :data-th="$t('wkf.verify')">
										<div class="form-check form-check-inline">
											<input
												class="form-check-input"
												type="checkbox"
												disabled
												:checked="item.verify"
											>
										</div>
									</td>
									<td :data-th="$t('wkf.export')">
										<div class="form-check form-check-inline bi-tree-removeY">
											<input
												class="form-check-input"
												type="checkbox"
												disabled
												:checked="item.export"
											>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-white" @click.prevent="props.close()">
							{{ $t('wkf.closeWindow') }}
						</button>
					</div>
				</div>
			</div>
		</template>
	</modal>
</template>

<script>
import modal from '@/views/components/model.vue';
export default {
	components: {
		modal
	},
	data: function () {
		return {
			roleName: String,
			roleMenuLogs: []
		};
	},
	methods: {
		getDetail: async function (eventId, roleName) {
			if (this.$_.isBlank(eventId)) {
				return;
			}
			this.roleName = roleName;

			const ret = await this.$api.getDetailApi(eventId);
			this.roleMenuLogs = ret.data;
			this.$refs.modal.open();
		},
		closeModal: function () {
			this.$refs.modal.close();
		}
	}
};
</script>
