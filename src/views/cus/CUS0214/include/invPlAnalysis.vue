<template>
	<div class="tab-content">
		<div class="tab-pane active show">
			<div class="card card-table ">
				<div class="card-header">
					<h4>損益分析</h4>
					<div class="tx-13">
						(新臺幣:元)
					</div>
				</div>
				<div class="table-responsive">
					<table class="table table-bordered table-RWD">
						<thead>
							<tr style="text-align: center;">
								<th rowspan="3">
									產品種類
								</th>
								<th rowspan="2" colspan="3">
									投資餘額(投資本金含手續費)
								</th>
								<th colspan="12">
									實現損益(報酬率)
								</th>
							</tr>
							<tr style="text-align: center;">
								<th colspan="4">
									本季
								</th>
								<th colspan="4">
									年初至今
								</th>
								<th colspan="4">
									去年
								</th>
							</tr>
							<tr style="text-align: center;">
								<th class="text-end">
									投資本金(含手續費)
								</th>
								<th class="text-end">
									投資現值
								</th>
								<th class="text-end">
									報酬率
								</th>
								<th class="text-end">
									已實現損益
								</th>
								<th class="text-end">
									投資本金(含手續費)
								</th>
								<th class="text-end">
									報酬率
								</th>
								<th class="text-end">
									區間報酬率
								</th>

								<th class="text-end">
									已實現損益
								</th>
								<th class="text-end">
									投資本金(含手續費)
								</th>
								<th class="text-end">
									報酬率
								</th>
								<th class="text-end">
									區間報酬率
								</th>

								<th class="text-end">
									已實現損益
								</th>
								<th class="text-end">
									投資本金(含手續費)
								</th>
								<th class="text-end">
									報酬率
								</th>
								<th class="text-end">
									區間報酬率
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="plAnalysis in invPlAnalDetails">
								<td>{{ plAnalysis.pfcatName }}</td>
								<td class="text-end" data-th="投資餘額-投資成本">
									{{ $filters.formatNumberZero(plAnalysis.invAmtLtd) }}
								</td>
								<td class="text-end" data-th="投資餘額-投資現值">
									{{ $filters.formatNumberZero(plAnalysis.mktAmtLtd) }}
								</td>
								<td class="text-end" data-th="投資餘額-報酬率">
									<HighLightDeltaText :value="plAnalysis.returnLtd">
										{{ $filters.formatAmt(plAnalysis.returnLtd) }}%
									</HighLightDeltaText>
								</td>
								<td class="text-end" data-th="實現損益-本季-已實現損益">
									<HighLightDeltaText :value="plAnalysis.totRplQtd">
										{{ $filters.formatNumberZero(plAnalysis.totRplQtd) }}
									</HighLightDeltaText>
								</td>
								<td class="text-end" data-th="實現損益-本季-投資本金">
									{{ $filters.formatNumberZero(plAnalysis.totSellInvamtQtd) }}
								</td>
								<td class="text-end" data-th="實現損益-本季-報酬率">
									<HighLightDeltaText :value="plAnalysis.returnQtd">
										{{ $filters.formatAmt(plAnalysis.returnQtd) }}%
									</HighLightDeltaText>
								</td>
								<th class="text-end" data-th="實現損益-本季-區間報酬率">
									<HighLightDeltaText :value="plAnalysis.mwrQtd">
										{{ $filters.formatAmt(plAnalysis.mwrQtd) }}%
									</HighLightDeltaText>
								</th>
								<td class="text-end" data-th="實現損益-年初至今-已實現損益">
									<HighLightDeltaText :value="plAnalysis.totRplYtd">
										{{ $filters.formatNumberZero(plAnalysis.totRplYtd) }}
									</HighLightDeltaText>
								</td>
								<td class="text-end" data-th="實現損益-年初至今-投資本金">
									{{ $filters.formatNumberZero(plAnalysis.totSellInvamtYtd) }}
								</td>
								<td class="text-end" data-th="實現損益-年初至今-報酬率">
									<HighLightDeltaText :value="plAnalysis.returnYtd">
										{{ $filters.formatAmt(plAnalysis.returnYtd) }}%
									</HighLightDeltaText>
								</td>
								<th class="text-end" data-th="實現損益-年初至今-區間報酬率">
									<HighLightDeltaText :value="plAnalysis.mwrYtd">
										{{ $filters.formatAmt(plAnalysis.mwrYtd) }}%
									</HighLightDeltaText>
								</th>
								<td class="text-end" data-th="實現損益-去年-已實現損益">
									<HighLightDeltaText :value="plAnalysis.totRplL1y">
										{{ $filters.formatNumberZero(plAnalysis.totRplL1y) }}
									</HighLightDeltaText>
								</td>
								<td class="text-end" data-th="實現損益-去年-投資本金">
									{{ $filters.formatNumberZero(plAnalysis.totSellInvamtL1y) }}
								</td>
								<td class="text-end" data-th="實現損益-去年-報酬率">
									<HighLightDeltaText :value="plAnalysis.returnL1y">
										{{ $filters.formatAmt(plAnalysis.returnL1y) }}%
									</HighLightDeltaText>
								</td>
								<th class="text-end" data-th="實現損益-去年-區間報酬率">
									<HighLightDeltaText :value="plAnalysis.mwrL1y">
										{{ $filters.formatAmt(plAnalysis.mwrL1y) }}%
									</HighLightDeltaText>
								</th>
							</tr>
							<tr class="tx-sum bg-total text-end">
								<td data-th="產品種類">
									總計
								</td>
								<td class="text-end" data-th="未實現損益-投資本金(含手續費)">
									{{ invPlAnalSummary.invAmtLtd === 0 ? '0' : $filters.formatNumberZero(invPlAnalSummary.invAmtLtd) }}
								</td>
								<td class="text-end" data-th="未實現損益-投資現值(含息)">
									{{ invPlAnalSummary.mktAmtLtd === 0 ? '0' : $filters.formatNumberZero(invPlAnalSummary.mktAmtLtd) }}
								</td>
								<td class="text-end" data-th="未實現損益-含息報酬率">
									<HighLightDeltaText :value="invPlAnalSummary.returnLtd">
										{{ invPlAnalSummary.returnLtd === 0 ? '0' : `${$filters.formatAmt(invPlAnalSummary.returnLtd)}%` }}
									</HighLightDeltaText>
								</td>
								<td class="text-end" data-th="已實現損益-本季-已實現損益">
									<HighLightDeltaText :value="invPlAnalSummary.totRplQtd">
										{{ invPlAnalSummary.totRplQtd === 0 ? '0' : $filters.formatNumberZero(invPlAnalSummary.totRplQtd) }}
									</HighLightDeltaText>
								</td>
								<td class="text-end" data-th="已實現損益-本季-投資本金">
									{{ invPlAnalSummary.totSellInvamtQtd === 0 ? '0' : $filters.formatNumberZero(invPlAnalSummary.totSellInvamtQtd) }}
								</td>
								<td class="text-end" data-th="已實現損益-本季-含息報酬率">
									<HighLightDeltaText :value="invPlAnalSummary.returnQtd">
										{{ invPlAnalSummary.returnQtd === 0 ? '0' : `${$filters.formatAmt(invPlAnalSummary.returnQtd)}%` }}
									</HighLightDeltaText>
								</td>
								<td class="text-end" data-th="已實現損益-本季-區間報酬率">
									<HighLightDeltaText :value="invPlAnalSummary.mwrQtd">
										{{ invPlAnalSummary.mwrQtd === 0 ? '0' : `${$filters.formatAmt(invPlAnalSummary.mwrQtd)}%` }}
									</HighLightDeltaText>
								</td>
								<td class="text-end" data-th="已實現損益-年初至今-已實現損益(含息)">
									<HighLightDeltaText :value="invPlAnalSummary.totRplYtd">
										{{ invPlAnalSummary.totRplYtd === 0 ? '0' : $filters.formatNumberZero(invPlAnalSummary.totRplYtd) }}
									</HighLightDeltaText>
								</td>
								<td class="text-end" data-th="已實現損益-年初至今-投資本金(含手續費)">
									{{ invPlAnalSummary.totSellInvamtYtd === 0 ? '0' : $filters.formatNumberZero(invPlAnalSummary.totSellInvamtYtd) }}
								</td>
								<td class="text-end" data-th="已實現損益-年初至今-含息報酬率">
									<HighLightDeltaText :value="invPlAnalSummary.returnYtd">
										{{ invPlAnalSummary.returnYtd === 0 ? '0' : `${$filters.formatAmt(invPlAnalSummary.returnYtd)}%` }}
									</HighLightDeltaText>
								</td>
								<td class="text-end" data-th="已實現損益-年初至今-區間報酬率">
									<HighLightDeltaText :value="invPlAnalSummary.mwrYtd">
										{{ invPlAnalSummary.mwrYtd === 0 ? '0' : `${$filters.formatAmt(invPlAnalSummary.mwrYtd)}%` }}
									</HighLightDeltaText>
								</td>
								<td class="text-end" data-th="已實現損益-去年-已實現損益(含息)">
									<HighLightDeltaText :value="invPlAnalSummary.totRplL1y">
										{{ invPlAnalSummary.totRplL1y === 0 ? '0' : $filters.formatNumberZero(invPlAnalSummary.totRplL1y) }}
									</HighLightDeltaText>
								</td>
								<td class="text-end" data-th="已實現損益-去年-投資本金(含手續費)">
									{{ invPlAnalSummary.totSellInvamtL1y === 0 ? '0' : $filters.formatNumberZero(invPlAnalSummary.totSellInvamtL1y) }}
								</td>
								<td class="text-end" data-th="已實現損益-去年-含息報酬率">
									<HighLightDeltaText :value="invPlAnalSummary.returnL1y">
										{{ invPlAnalSummary.returnL1y === 0 ? '0' : `${$filters.formatAmt(invPlAnalSummary.returnL1y)}%` }}
									</HighLightDeltaText>
								</td>
								<td class="text-end" data-th="已實現損益-去年-區間報酬率">
									<HighLightDeltaText :value="invPlAnalSummary.mwrL1y">
										{{ invPlAnalSummary.mwrL1y === 0 ? '0' : `${$filters.formatAmt(invPlAnalSummary.mwrL1y)}%` }}
									</HighLightDeltaText>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div class="tx-note">
				此金額不包含，存款、信託-保險、黃金存摺
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		cusCode: null,
		hasAuth: Boolean,
		customer: Object,
		userName: String,
		userRoleName: String,
		queryDt: null,
		dataDt: null
	},
	data: function () {
		return {
			invPlAnalDetails: [], // 明細資料
			invPlAnalSummary: {} // 總計
		};
	},
	mounted: function () {
		const self = this;
		self.getInvsetPlAnalysis();
	},
	methods: {
		getInvsetPlAnalysis: function () {
			const self = this;
			self.$api.getInvsetPlAnalysisApi({
				cusCode: self.cusCode
			}).then(function (ret) {
				self.invPlAnalDetails = ret.data.details;
				self.invPlAnalSummary = ret.data.summary;
			});
		}
	}

};
</script>
