<template>
	<div class="card card-table mb-3">
		<div class="card-header">
			<h4>財富增長分析</h4>
			<div class="tx-13">
				(新臺幣:元)
			</div>
		</div>
		<div class="row px-4 mb-3">
			<div id="wealthAnalysisChartContainer" class="col-lg-6" />
			<div id="wealthAnalysisDailyChartContainer" class="col-lg-6" />
		</div>
		<div class="table-responsive">
			<table class="table  table-RWD">
				<thead>
					<tr>
						<th colspan="5">
							最近12月往來投資變化狀況
						</th>
						<th colspan="3">
							{{ formatYearMonthOnly(firstAnalysis.dataDt) }} 帳戶市值：{{ formatCurAmt(firstAnalysis.mktAmtLc, 0) }}
						</th>
					</tr>
					<tr>
						<th>日期</th>
						<th class="text-end">
							投資本金(含手續費)
						</th>
						<th class="text-end">
							已實現損益
						</th>
						<th class="text-end">
							未實現損益
						</th>
						<th class="text-end">
							累計配息
						</th>
						<th class="text-end">
							投資本金(含手續費)增減
						</th>
						<th class="text-end">
							投資現值
						</th>
						<th class="text-end">
							期間報酬率
						</th>
					</tr>
				</thead>
				<tbody>
					<template v-if="wealthAnalysisReversed.length">
						<tr v-for="(wealthData) in wealthAnalysisReversed">
							<td data-th="日期">
								{{ wealthData.dataYm || '--' }}
							</td>
							<td class="text-end" data-th="投資本金">
								{{ formatCurAmt(wealthData.invAmtLc, 0) }}
							</td>
							<td class="text-end" data-th="已實現損益">
								<HighLightDeltaText :value="wealthData.totRplLc">
									{{ formatCurAmt(wealthData.totRplLc, 0) }}
								</HighLightDeltaText>
							</td>
							<td class="text-end" data-th="未實現損益">
								<HighLightDeltaText :value="wealthData.totUplLc">
									{{ formatCurAmt(wealthData.totUplLc, 0) }}
								</HighLightDeltaText>
							</td>
							<td class="text-end" data-th="累計配息">
								{{ formatCurAmt(wealthData.totDividendLc, 0) }}
							</td>
							<td class="text-end" data-th="投資本金(含手續費)增減">
								{{ formatCurAmt(wealthData.totDiffAmtLc, 0) }}
							</td>
							<td class="text-end" data-th="投資現值">
								{{ formatCurAmt(wealthData.mktAmtLc, 0) }}
							</td>
							<td class="text-end" data-th="含息期間報酬率(%)">
								<HighLightDeltaText :value="wealthData.twrLc">
									{{ wealthData.twrLc.toFixed(2) }}%
								</HighLightDeltaText>
							</td>
						</tr>
					</template>
				</tbody>
			</table>
		</div>
	</div>
	<div class="tx-note">
		註:此金額不包含，存款、信託-保險、黃金存摺；財金-附條件交易
	</div>
</template>

<script>
import _ from 'lodash';
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
// 导入过滤器函数
import {
	formatCurAmt,
	formatPct,
	formatYearMonthOnly
} from '@/utils/filter';

export default {
	props: {
		cusCode: null,
		hasAuth: Boolean,
		customer: Object,
		userName: String,
		userRoleName: String,
		queryDt: null,
		dataDt: null
	},
	data: function () {
		return {
			wealthChartRoot: null,
			chartRoot: null,
			// 主要顯示資料
			wealthAnalysis: [],
			wealthAnalysisMonthly: [],
			// 線圖資料
			wealthChartXAxis: [],
			wealthChartData: [],
			wealthDailyChartXAxis: [],
			wealthChartDailyData: []
		};
	},
	computed: {
		wealthAnalysisReversed() {
			return this.wealthAnalysis.toSorted((a, b) => -a.dataYm.localeCompare(b.dataYm));
		},
		firstAnalysis: function () {
			if (this.wealthAnalysis.length > 0) {
				return this.wealthAnalysis[0];
			}
			else {
				return {};
			}
		}
	},
	mounted: function () {
		const self = this;
		self.getWealthAnalysis();
	},
	methods: {
		formatCurAmt(value, precision, customFormat) {
			return formatCurAmt(value, precision, customFormat);
		},
		formatPct(value) {
			return formatPct(value);
		},
		formatYearMonthOnly(value) {
			return formatYearMonthOnly(value);
		},

		getWealthAnalysis: function () {
			const self = this;
			self.$api
				.getWealthAnalysisEachMonthApi({ cusCodes: self.cusCode })
				.then(function (ret) {
					if (!ret.data) return;
					self.wealthAnalysis = ret.data.wealthAnalysis;
					self.wealthAnalysisMonthly = ret.data.wealthAnalysisMonthly;

					// 日期排序
					self.wealthAnalysis.sort((a, b) => {
						return new Date(a.dataYm) - new Date(b.dataYm);
					});

					self.wealthAnalysisMonthly.sort((a, b) => {
						return new Date(a.dataYm) - new Date(b.dataYm);
					});

					const invAmtLc = {
						type: 'column',
						name: '投資本金(含手續費)',
						data: []
					};
					const mktAmtLc = {
						type: 'column',
						name: '投資現值',
						data: []
					};
					const twrLc = {
						type: 'column',
						name: '期間報酬率',
						data: []
					};
					const daliyReturn = {
						type: 'column',
						name: '每月期間連續報酬率',
						data: []
					};
					self.wealthChartXAxis = [];
					self.wealthDailyChartXAxis = [];

					// 處理圖表數據
					self.wealthAnalysis.forEach(function (wealthData) {
						self.wealthChartXAxis.push(wealthData.dataYm);
						invAmtLc.data.push(wealthData.invAmtLc);
						mktAmtLc.data.push(wealthData.mktAmtLc);
					});

					self.wealthAnalysisMonthly.forEach(function (dailyData) {
						// X軸 (月份)
						self.wealthDailyChartXAxis.push(dailyData.dataYm);
						// 每月期間連續報酬率
						daliyReturn.data.push(dailyData.dailyReturn);
						// 期間報酬率
						const wealthData = _.find(self.wealthAnalysis, {
							dataYm: dailyData.dataYm
						});
						twrLc.data.push(wealthData ? wealthData.twrLc : null);
					});

					self.wealthChartData = [invAmtLc, mktAmtLc];
					self.wealthChartDailyData = [twrLc, daliyReturn];
					self.renderWealthChart();
					self.renderWealthDailyChart();
				});
		},
		renderWealthChart: function () {
			const self = this;
			// 如果已存在圖表根元素，銷毀它
			if (this.wealthChartRoot) {
				this.wealthChartRoot.dispose();
				this.wealthChartRoot = null;
			}
			// 防禦性檢查
			if (
				!self.wealthChartXAxis
				|| !Array.isArray(self.wealthChartXAxis)
				|| !self.wealthChartData
				|| !Array.isArray(self.wealthChartData)
			) {
				console.error(
					'wealthChartXAxis or wealthChartData is not initialized or not an array'
				);
				return;
			}

			am5.ready(() => {
				// 動態設定容器高度
				const container = document.getElementById('wealthAnalysisChartContainer');
				if (container) {
					container.innerHTML = ''; // 清空容器內容
				}
				container.style.height = window.innerHeight * 0.5 + 'px'; // 視窗高度的 50%

				// 創建根元素
				const root = am5.Root.new('wealthAnalysisChartContainer');
				root._logo.dispose();

				// 保存圖表根元素到實例變數，供後續銷毀
				self.wealthChartRoot = root;

				// 設置主題
				root.setThemes([am5themes_Animated.new(root)]);

				// 創建 XY 圖表
				const chart = root.container.children.push(
					am5xy.XYChart.new(root, {
						panX: false,
						panY: false,
						wheelX: 'none',
						wheelY: 'none',
						layout: root.verticalLayout
					})
				);

				// X軸（分類軸）
				const xAxis = chart.xAxes.push(
					am5xy.CategoryAxis.new(root, {
						categoryField: 'category',
						renderer: am5xy.AxisRendererX.new(root, {
							minGridDistance: 30
						})
					})
				);

				// X軸數據
				xAxis.data.setAll(self.wealthChartXAxis.map(label => ({ category: label })));

				// Y軸
				const yAxis = chart.yAxes.push(
					am5xy.ValueAxis.new(root, {
						renderer: am5xy.AxisRendererY.new(root, {})
					})
				);

				// 創建柱狀圖系列
				self.wealthChartData.forEach((seriesData) => {
					const series = chart.series.push(
						am5xy.ColumnSeries.new(root, {
							name: seriesData.name,
							xAxis: xAxis,
							yAxis: yAxis,
							valueYField: 'value',
							categoryXField: 'category',
							tooltip: am5.Tooltip.new(root, {
								labelText: '{name}: {valueY.formatNumber("#,###")}'
							})
						})
					);

					// 設置數據
					series.data.setAll(
						seriesData.data.map((value, index) => ({
							category: self.wealthChartXAxis[index],
							value: Number(value) || 0
						}))
					);

					// 設置柱子樣式
					series.columns.template.setAll({
						tooltipText: '{categoryX}: [bold]{valueY.formatNumber("#,###")}[/]',
						cornerRadiusTL: 3,
						cornerRadiusTR: 3
					});
				});

				// 創建圖例
				const legend = chart.children.push(
					am5.Legend.new(root, {
						centerX: am5.p50,
						x: am5.p50
					})
				);

				legend.data.setAll(chart.series.values);

				// 啟動初始動畫
				chart.appear(1000, 100);
			});
		},
		renderWealthDailyChart: function () {
			const self = this;
			// 如果已存在圖表根元素，銷毀它
			if (self.chartRoot) {
				self.chartRoot.dispose();
				self.chartRoot = null;
			}

			// 防禦性檢查
			if (
				!self.wealthDailyChartXAxis
				|| !Array.isArray(self.wealthDailyChartXAxis)
				|| !self.wealthChartDailyData
				|| !Array.isArray(self.wealthChartDailyData)
			) {
				console.error(
					'wealthDailyChartXAxis or wealthChartDailyData is not initialized or not an array'
				);
				return;
			}

			am5.ready(() => {
				const container = document.getElementById('wealthAnalysisDailyChartContainer');
				if (container) {
					container.innerHTML = '';
				}
				container.style.height = window.innerHeight * 0.5 + 'px';

				// 創建根元素
				const root = am5.Root.new('wealthAnalysisDailyChartContainer');
				root._logo.dispose();

				// 保存圖表根元素到實例變數，供後續銷毀
				self.chartRoot = root;

				// 設置主題
				root.setThemes([am5themes_Animated.new(root)]);

				const chart = root.container.children.push(
					am5xy.XYChart.new(root, {
						panX: false,
						panY: false,
						wheelX: 'none',
						wheelY: 'none',
						layout: root.verticalLayout
					})
				);

				// 創建 X 軸 (類別軸)
				const xAxis = chart.xAxes.push(
					am5xy.CategoryAxis.new(root, {
						categoryField: 'category',
						renderer: am5xy.AxisRendererX.new(root, {
							minGridDistance: 30
						})
					})
				);

				// X軸數據
				xAxis.data.setAll(self.wealthDailyChartXAxis.map(label => ({ category: label })));

				// Y軸
				const yAxis = chart.yAxes.push(
					am5xy.ValueAxis.new(root, {
						renderer: am5xy.AxisRendererY.new(root, {})
					})
				);

				// 創建柱狀圖
				self.wealthChartDailyData.forEach((seriesData) => {
					const series = chart.series.push(
						am5xy.ColumnSeries.new(root, {
							name: seriesData.name,
							xAxis: xAxis,
							yAxis: yAxis,
							valueYField: 'value',
							categoryXField: 'category',
							tooltip: am5.Tooltip.new(root, {
								labelText: '{name}: {valueY}%'
							})
						})
					);

					// 設置數據
					series.data.setAll(
						seriesData.data.map((value, index) => ({
							category: self.wealthDailyChartXAxis[index],
							value: Number(value) || 0
						}))
					);

					// 設置柱子樣式
					series.columns.template.setAll({
						tooltipText: '{categoryX}: [bold]{valueY}%[/]',
						cornerRadiusTL: 3,
						cornerRadiusTR: 3
					});
				});

				// 添加圖例
				const legend = chart.children.push(
					am5.Legend.new(root, {
						centerX: am5.p50,
						x: am5.p50
					})
				);
				legend.data.setAll(chart.series.values);

				// 啟動初始動畫
				chart.appear(1000, 100);
			});
		}
	}
};
</script>
