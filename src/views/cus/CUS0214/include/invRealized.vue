<template>
	<div>
		<div class="card card-table card-collapse mb-3">
			<div class="card-header">
				<h4>查詢條件</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup4" />
			</div>
			<div id="collapseListGroup4" class="collapse show">
				<div class="card-body">
					<form>
						<div class="row g-3 align-items-end">
							<div class="col-md-6 col-lg-6">
								<label class="form-label tx-require">查詢商品</label><br>
								<div v-for="(item, i) in pfcatList" class="form-check form-check-inline">
									<input
										:id="'proCatList_' + item.pfcatCode"
										v-model="req.pfcatCodeList"
										class="form-check-input"
										type="checkbox"
										:value="item.pfcatCode"
									>
									<label class="form-check-label" :for="'proCatList_' + item.pfcatCode">{{ item.pfcatName }}</label>
								</div>
							</div>
							<div class="col-md-6 col-lg-6">
								<label class="form-label">商品代號</label>
								<div class="input-group">
									<input
										id="proCodeId"
										v-model="req.proCode"
										name="proCodee"
										class="form-control"
										type="text"
									>
									<span><a href="#" class="tc-blue margin-left10" @click.prevent="openProModal"><u>查詢代碼</u></a></span>
								</div>
							</div>

							<div class="col-md-6 col-lg-6">
								<label class="form-label">憑證編號</label>
								<input
									id="proRefNo"
									v-model="req.refNo"
									name="proRefNo"
									class="form-control"
									type="text"
									size="20"
								>
							</div>

							<div class=" col-md-6 col-lg-6">
								<label class="form-label">交易日期區間</label>
								<div class="input-group">
									<input
										id="startDate"
										v-model="req.stdDt"
										name="startDate"
										class="form-control"
										type="date"
										size="13"
										maxlength="10"
										placeholder=""
									>
									<span class="input-group-text">~</span>
									<input
										id="endDate"
										v-model="req.endDt"
										name="endDate"
										class="form-control"
										type="date"
										size="13"
										maxlength="10"
									>
								</div>
							</div>
						</div>
						<div class=" text-end col-md-12 col-lg-12 mt-3">
							<button type="button" class="btn btn-primary " @click.prevent="query">
								查詢
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<div class="tx-note mb-3">
			證券台股僅顯示現股資料。
		</div>
		<div v-if="plList.length > 0" id="result">
			<!--總損益-->
			<div class="card card-table card-collapse mb-3 ">
				<div class="card-header">
					<h4>總損益</h4>
					<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup4" />
				</div>
				<div id="collapseListGroup4" class="collapse show">
					<div class="table-responsive">
						<table class="table bih-table table-RWD">
							<thead>
								<tr>
									<th>損益期間</th>
									<th>總損益(折台)</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>{{ req.stdDt }}～{{ req.endDt }}</td>
									<td>{{ $filters.formatAmt($filters.sumTotal(plList, 'realPl')) }}</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<div class="tx-note">
				贖回再申購於備註欄填上轉至/轉自+憑證編號
			</div>

			<!--信託-基金-->
			<div v-if="fundPlList.length > 0" class="card card-table card-collapse mb-3">
				<div class="card-header">
					<h4>信託-基金</h4>
					<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1" />
				</div>
				<div id="collapseListGroup1" class="collapse show">
					<table class="table table-RWD bih-table">
						<thead>
							<tr>
								<th>憑證編號</th>
								<th rowspan="2">
									交易日
								</th>
								<th rowspan="2">
									投資金額
								</th>
								<th rowspan="2">
									投資損益
								</th>
								<th rowspan="2">
									累計配息
								</th>
								<th rowspan="2">
									交易費用
								</th>
								<th rowspan="2">
									實質投資損益
								</th>
							</tr>
							<tr>
								<th>商品代號/投資標的</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="p in fundPlList">
								<td class="text-start">
									{{ p.refNo }}<br>
									<a href="">{{ p.proName }}({{ p.branProCode }})</a>
								</td>
								<td class="text-start">
									{{ $filters.formatDate(p.tranDt) }}
								</td>
								<td class="num">
									{{ $filters.formatAmt(p.invAmtFc) }}
								</td>
								<td class="num">
									{{ $filters.formatAmt(p.plFc) }} {{ p.tranCurCode }}<br>{{
										$filters.formatPct(p.returnFc) }}%
								</td>
								<td class="num">
									{{ $filters.formatAmt(p.totDividendFc) }}
									{{ p.tranCurCode }}<br>{{ $filters.formatPct(p.intRate) }}%
								</td>
								<td class="num">
									{{ $filters.formatAmt(p.tranFeeFc) }}<br>{{ $filters.formatPct(p.tranFeeRate) }}%
								</td>
								<td class="num">
									{{ $filters.formatAmt(p.realPl) }}
									{{ p.tranCurCode }}<br>{{ $filters.formatPct(p.realPlRate) }}%
								</td>
							</tr>
							<tr class="tr-sum">
								<td colspan="3" class="num">
									期間報酬合計
								</td>
								<td class="num">
									{{ $filters.formatAmt($filters.sumTotal(fundPlList, 'plFc')) }} TWD
								</td>
								<td class="num">
									{{ $filters.formatAmt($filters.sumTotal(fundPlList, 'totDividendFc')) }} TWD
								</td>
								<td class="num">
									{{ $filters.formatAmt($filters.sumTotal(fundPlList, 'tranFeeFc')) }}
								</td>
								<td class="num">
									{{ $filters.formatAmt($filters.sumTotal(fundPlList, 'realPl')) }}TWD
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>

			<!--信託-海外債-->
			<div v-if="fbPlList.length > 0" class="card card-table card-collapse mb-3">
				<div class="card-header">
					<h4>信託-海外債</h4>
					<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2" />
				</div>
				<div id="collapseListGroup2" class="collapse show">
					<table class="table bih-table table-RWD">
						<thead>
							<tr>
								<th>憑證編號</th>
								<th rowspan="2">
									交易日
								</th>
								<th rowspan="2">
									投資金額
								</th>
								<th rowspan="2">
									投資損益
								</th>
								<th rowspan="2">
									累計配息
								</th>
								<th rowspan="2">
									交易費用
								</th>
								<th rowspan="2">
									實質投資損益
								</th>
							</tr>
							<tr>
								<th>商品代號/投資標的</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="p in fbPlList">
								<td class="text-start">
									{{ p.refNo }}<br>
									<a href="">{{ p.proName }}({{ p.branProCode }})</a>
								</td>
								<td class="text-start">
									{{ $filters.formatDate(p.tranDt) }}
								</td>
								<td class="num">
									{{ $filters.formatAmt(p.invAmtFc) }}
								</td>
								<td class="num">
									{{ $filters.formatAmt(p.plFc) }} {{ p.tranCurCode }}<br>{{
										$filters.formatPct(p.returnFc) }}%
								</td>
								<td class="num">
									{{ $filters.formatAmt(p.totDividendFc) }}
									{{ p.tranCurCode }}<br>{{ $filters.formatPct(p.intRate) }}%
								</td>
								<td class="num">
									{{ $filters.formatAmt(p.tranFeeFc) }}<br>{{ $filters.formatPct(p.tranFeeRate) }}%
								</td>
								<td class="num">
									{{ $filters.formatAmt(p.realPl) }}
									{{ p.tranCurCode }}<br>{{ $filters.formatPct(p.realPlRate) }}%
								</td>
							</tr>
							<tr class="tr-sum">
								<td colspan="3" class="num">
									期間報酬合計
								</td>
								<td class="num">
									{{ $filters.formatAmt($filters.sumTotal(fbPlList, 'plFc')) }} TWD
								</td>
								<td class="num">
									{{ $filters.formatAmt($filters.sumTotal(fbPlList, 'totDividendFc')) }} TWD
								</td>
								<td class="num">
									{{ $filters.formatAmt($filters.sumTotal(fbPlList, 'tranFeeFc')) }}
								</td>
								<td class="num">
									{{ $filters.formatAmt($filters.sumTotal(fbPlList, 'realPl')) }}TWD
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>

			<!--信託-ETF-->
			<div v-if="etfPlList.length > 0" class="card card-table card-collapse mb-3">
				<div class="card-header">
					<h4>信託-ETF</h4>
					<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup3" />
				</div>
				<div id="collapseListGroup3" class="collapse show">
					<div class="table-responsive">
						<table class="table bih-table table-RWD">
							<thead>
								<tr>
									<th>成交序號</th>
									<th rowspan="2">
										交易日
									</th>
									<th rowspan="2">
										投資金額
									</th>
									<th rowspan="2">
										投資損益
									</th>
									<th rowspan="2">
										累計配息
									</th>
									<th rowspan="2">
										交易費用
									</th>
									<th rowspan="2">
										實質投資損益
									</th>
								</tr>
								<tr>
									<th>商品代號/投資標的</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="p in etfPlList">
									<td class="text-start">
										{{ p.refNo }}<br>
										<a href="">{{ p.proName }}({{ p.branProCode }})</a>
									</td>
									<td class="text-start">
										{{ $filters.formatDate(p.tranDt) }}
									</td>
									<td class="num">
										{{ $filters.formatAmt(p.invAmtFc) }}
									</td>
									<td class="num">
										{{ $filters.formatAmt(p.plFc) }}
										{{ p.tranCurCode }}<br>{{ $filters.formatPct(p.returnFc) }}%
									</td>
									<td class="num">
										{{ $filters.formatAmt(p.totDividendFc) }}
										{{ p.tranCurCode }}<br>{{ $filters.formatPct(p.intRate) }}%
									</td>
									<td class="num">
										{{ $filters.formatAmt(p.tranFeeFc) }}<br>{{ $filters.formatPct(p.tranFeeRate) }}%
									</td>
									<td class="num">
										{{ $filters.formatAmt(p.realPl) }}
										{{ p.tranCurCode }}<br>{{ $filters.formatPct(p.realPlRate) }}%
									</td>
								</tr>
								<tr class="tr-sum">
									<td colspan="3" class="num">
										期間報酬合計
									</td>
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(etfPlList, 'plFc')) }} TWD
									</td>
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(etfPlList, 'totDividendFc')) }} TWD
									</td>
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(etfPlList, 'tranFeeFc')) }}
									</td>
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(etfPlList, 'realPl')) }}TWD
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>

			<!--信託-海外股票-->
			<div v-if="pfdPlList.length > 0" class="card card-table card-collapse mb-3">
				<div class="card-header">
					<h4>信託-海外股票</h4>
					<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup4" />
				</div>
				<div id="collapseListGroup4" class="collapse show">
					<div class="table-responsive">
						<table class="table bih-table table-RWD">
							<thead>
								<tr>
									<th>成交序號</th>
									<th rowspan="2">
										交易日
									</th>
									<th rowspan="2">
										投資金額
									</th>
									<th rowspan="2">
										投資損益
									</th>
									<th rowspan="2">
										累計配息
									</th>
									<th rowspan="2">
										交易費用
									</th>
									<th rowspan="2">
										實質投資損益
									</th>
								</tr>
								<tr>
									<th>商品代號/投資標的</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="p in pfdPlList">
									<td class="text-start">
										{{ p.refNo }}<br>
										<a href="">{{ p.proName }}({{ p.branProCode }})</a>
									</td>
									<td class="text-start">
										{{ $filters.formatDate(p.tranDt) }}
									</td>
									<td class="num">
										{{ $filters.formatAmt(p.invAmtFc) }}
									</td>
									<td class="num">
										{{ $filters.formatAmt(p.plFc) }}
										{{ p.tranCurCode }}<br>{{ $filters.formatPct(p.returnFc) }}%
									</td>
									<td class="num">
										{{ $filters.formatAmt(p.totDividendFc) }}
										{{ p.tranCurCode }}<br>{{ $filters.formatPct(p.intRate) }}%
									</td>
									<td class="num">
										{{ $filters.formatAmt(p.tranFeeFc) }}<br>{{ $filters.formatPct(p.tranFeeRate) }}%
									</td>
									<td class="num">
										{{ $filters.formatAmt(p.realPl) }}
										{{ p.tranCurCode }}<br>{{ $filters.formatPct(p.realPlRate) }}%
									</td>
								</tr>
								<tr class="tr-sum">
									<td colspan="3" class="num">
										期間報酬合計
									</td>
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(pfdPlList, 'plFc')) }} TWD
									</td>
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(pfdPlList, 'totDividendFc')) }} TWD
									</td>
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(pfdPlList, 'tranFeeFc')) }}
									</td>
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(pfdPlList, 'realPl')) }}TWD
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
		<div class="alert alert-block alert-danger fade in margin-top10">
			本功能畫面提供的有關資訊是截止統計日期，由系統根據客戶歷史投資資料自動生成的，僅供參考。
		</div>
	</div>
	<vue-pro-quick-search ref="proModal" :set-pro-code="setProCode" />
</template>
<script>
import { Field, Form } from 'vee-validate';
import moment from 'moment';
import vueProQuickSearch from '../../CUS0202/include/proQuickSearch.vue';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueProQuickSearch
	},
	props: {
		cusCode: null
	},
	data: function () {
		return {
			req: {
				pfcatCodeList: []
			},
			pfcatList: [],
			plList: [],
			fundPlList: [],
			fbPlList: [],
			etfPlList: [],
			pfdPlList: []
		};
	},
	watch: {},
	mounted: function () {
		const self = this;
		self.req.cusCode = self.cusCode;
		self.req.stdDt = moment().add(-31, 'days').format('YYYY-MM-DD'); // 交易日期起 (預設一個月前)
		self.req.endDt = moment().format('YYYY-MM-DD'); // 交易日期迄
		self.getPfcatList();
	},
	methods: {
		getPfcatList: async function () {
			const self = this;
			const resp = await self.$api.getProCatApi({
				tranYn: 'Y'
			});
			self.pfcatList = resp.data;
		},
		openProModal() {
			this.$refs.proModal.show();
		},
		setProCode(proCode) {
			this.req.proCode = proCode;
		},
		async query() {
			const self = this;
			const resp = await self.$api.getCusRealizePlListApi(self.req);
			self.plList = resp.data;
			self.fundPlList = self.plList.filter((tran) => {
				return tran.pfcatCode === 'FUND';
			});
			self.fbPlList = self.plList.filter((tran) => {
				return tran.pfcatCode === 'FB';
			});
			self.etfPlList = self.plList.filter((tran) => {
				return tran.pfcatCode === 'ETF';
			});
			self.pfdPlList = self.plList.filter((tran) => {
				return tran.pfcatCode === 'PFD';
			});
		}
	}
};
</script>
