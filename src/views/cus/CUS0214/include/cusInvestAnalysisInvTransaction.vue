<template>
	<div class="card card-form-collapse">
		<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
			<h4>{{ $t('cus.searchConditions') }}</h4>
		</div>
		<div id="collapseListGroup1" class="collapse show">
			<div class="card-body">
				<vue-form v-slot="{ errors, validate, handleReset }" ref="invTranForm">
					<div class="form-row">
						<div class="form-group col-12 col-lg-6">
							<label class="form-label tx-require">{{ $t('cus.searchProduct') }}</label>
							<div class="form-check-group">
								<div v-for="(item, i) in pfcatList" class="form-check form-check-inline">
									<vue-field
										:id="'pfcatCodes-' + i"
										v-model="form.pfcatCode"
										class="form-check-input"
										name="pfcat_code"
										type="checkbox"
										:value="item.pfcatCode"
									/>
									<label class="form-check-label" :for="'pfcatCodes-' + i">{{ item.pfcatName }}</label>
								</div>
								<span v-show="errors.pfcat_code" class="text-danger">{{ errors.pfcat_code }}</span>
							</div>
						</div>
						<div class="form-group col-12 col-lg-6">
							<label class="form-label tx-require">{{ $t('cus.transactionType') }}</label>
							<div class="form-check-group">
								<div v-for="(item, i) in trantypeList" class="form-check form-check-inline">
									<vue-field
										:id="'tranTypeCode-' + i"
										v-model="form.tranTypeCode"
										class="form-check-input"
										name="tranTypeCode"
										type="checkbox"
										:value="item.trantypeCode"
									/>
									<label class="form-check-label" :for="'tranTypeCode-' + i">{{ item.trantypeName }}</label>
								</div>
								<span v-show="errors.tranTypeCode" class="text-danger">{{ errors.tranTypeCode }}</span>
							</div>
						</div>
						<div class="form-group col-12 col-lg-3">
							<label class="form-label">{{ $t('cus.productCode') }}</label>
							<input
								id="bankProCode"
								v-model="form.bankProCode"
								name="bankProCode"
								class="form-control"
								type="text"
							>
						</div>
						<div class="form-group col-12 col-lg-3">
							<label class="form-label">{{ $t('cus.certificateNumber') }}</label>
							<input
								id="refNo"
								v-model="form.refNo"
								name="refNo"
								class="form-control"
								type="text"
							>
						</div>
						<div class="form-group col-md-10 col-lg-6">
							<label class="form-label">{{ $t('cus.transactionDateRange') }}</label>
							<div class="input-group">
								<input
									id="startDate"
									v-model="form.tranDtB"
									name="startDate"
									class="form-control"
									type="date"
									size="13"
									maxlength="10"
								>
								<span class="input-group-text">~</span>
								<input
									id="endDate"
									v-model="form.tranDtE"
									name="endDate"
									class="form-control"
									type="date"
									size="13"
									maxlength="10"
								>
							</div>
						</div>
					</div>
					<div class="form-footer">
						<button type="button" class="btn btn-primary btn-search" @click="search">
							{{ $t('cus.search') }}
						</button>
					</div>
				</vue-form>
			</div>
		</div>
	</div>
	<div class="tx-note mb-3">
		{{ $t('cus.securitiesStockDataNote') }}
	</div>
	<div class="searchResult">
		<div v-if="fundTranList.length > 0" class="card card-table card-collapse">
			<div class="card-header">
				<h4>{{ $t('cus.trustFund') }}</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup4" />
			</div>
			<div id="collapseListGroup4" class="collapse show">
				<div class="table-responsive">
					<table class="table table-RWD table-bordered">
						<thead>
							<tr>
								<th rowspan="2">
									{{ $t('cus.transactionDate') }}
								</th>
								<th>{{ $t('cus.accountNumber') }}</th>
								<th rowspan="2">
									{{ $t('cus.transactionType') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.transactionCurrency') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.exchangeRate') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.transactionAmount') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.netValue') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.transactionUnits') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.handlingFee') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.otherFees') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.remarks') }}
								</th>
							</tr>
							<tr>
								<th>{{ $t('cus.productCodeInvestmentTarget') }}</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="tran in fundTranList">
								<td :data-th="$t('cus.transactionDate')">
									{{ $filters.formatDate(tran.tranDt) }}
								</td>
								<td :data-th="$t('cus.accountNumber') + ' ' + $t('cus.productCodeInvestmentTarget')">
									{{ tran.refNo }}<br><a class="tx-link" href="#">{{ tran.proName }}({{ tran.bankProCode }})</a>
								</td>
								<td :data-th="$t('cus.transactionType')">
									{{ tran.trantypeName }}
								</td>
								<td class="" :data-th="$t('cus.transactionCurrency')">
									<label class="num">{{ tran.tranCurCode }}</label>
								</td>
								<td class="text-end" :data-th="$t('cus.exchangeRate')">
									<label class="num">{{ $filters.formatAmt(tran.fxRate) }}</label>
								</td>
								<td class="text-end" :data-th="$t('cus.transactionAmount')">
									<label class="num">{{ $filters.formatAmt(tran.invAmtFc) }}</label>&nbsp;{{ tran.tranCurCode }}
								</td>
								<td class="text-end" :data-th="$t('cus.netValue')">
									<label class="num">{{ $filters.formatAmt(tran.price) }}</label>
								</td>
								<td class="text-end" :data-th="$t('cus.transactionUnits')">
									<label class="num">{{ $filters.formatAmt(tran.unit) }}</label>
								</td>
								<td class="text-end" :data-th="$t('cus.handlingFee')">
									<label class="num">{{ $filters.formatAmt(tran.feeFc) }}</label>&nbsp;{{ tran.fCurCode }}
								</td>
								<td class="text-end" :data-th="$t('cus.otherFees')">
									<label class="num">{{ $filters.formatAmt(tran.oFeeFc) }}</label>&nbsp;{{ tran.oCurCode }}
								</td>
								<td :data-th="$t('cus.remarks')">
									{{ tran.memo }}
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div v-if="fbTranList.length > 0" class="card card-table card-collapse mb-3">
			<div class="card-header">
				<h4>{{ $t('cus.trustForeignBond') }}</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup6" />
			</div>
			<div id="collapseListGroup6" class="collapse show">
				<div class="table-responsive">
					<table class="table table-RWD table-bordered">
						<thead>
							<tr>
								<th rowspan="2">
									{{ $t('cus.transactionDate') }}
								</th>
								<th>{{ $t('cus.accountNumber') }}</th>
								<th rowspan="2">
									{{ $t('cus.transactionType') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.transactionAmount') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.transactionFaceValue') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.exchangeRate') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.pricePercentage') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.frontEndInterest') }}
								</th>
								<th>{{ $t('cus.channelServiceFeeActualRate') }}</th>
							</tr>
							<tr>
								<th>{{ $t('cus.productCodeInvestmentTarget') }}</th>
								<th>{{ $t('cus.channelServiceFeeAnnualRate') }}</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="tran in fbTranList">
								<td :data-th="$t('cus.transactionDate')">
									{{ $filters.formatDate(tran.tranDt) }}
								</td>
								<td :data-th="$t('cus.accountNumber') + ' ' + $t('cus.productCodeInvestmentTarget')">
									{{ tran.refNo }}<br><a class="tx-link" href="#">{{ tran.proName }}({{ tran.bankProCode }})</a>
								</td>
								<td :data-th="$t('cus.transactionType')">
									{{ tran.trantypeName }}
								</td>
								<td class="text-end" :data-th="$t('cus.transactionAmount')">
									<label class="num">{{ $filters.formatAmt(tran.invAmtFc) }}</label>&nbsp;{{ tran.tranCurCode }}
								</td>
								<td class="text-end" :data-th="$t('cus.transactionFaceValue')" />
								<td class="text-end" :data-th="$t('cus.exchangeRate')">
									<label class="num">{{ $filters.formatAmt(tran.fxRate) }}</label>
								</td>
								<td class="text-end" :data-th="$t('cus.pricePercentage')">
									<label class="num">{{ $filters.formatAmt(tran.price) }}</label>
								</td>
								<td class="text-end" :data-th="$t('cus.frontEndInterest')">
									<label class="num">{{ $filters.formatAmt(tran.uFeeFc) }}</label>&nbsp;{{ tran.tranCurCode }}
								</td>
								<td class="text-end" :data-th="$t('cus.channelServiceFeeActualRate') + ' ' + $t('cus.channelServiceFeeAnnualRate')">
									<label class="num">{{ $filters.formatPct(tran.channelServiceRate) }}%</label><br>
									<label class="num">{{ $filters.formatPct(tran.channelServiceRateYear) }}%</label>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div v-if="etfTranList.length > 0" class="card card-table card-collapse mb-3">
			<div class="card-header">
				<h4>{{ $t('cus.trustETF') }}</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup5" />
			</div>
			<div id="collapseListGroup5" class="collapse show">
				<div class="table-responsive">
					<table class="table table-RWD table-bordered">
						<thead>
							<tr>
								<th rowspan="2">
									{{ $t('cus.transactionDate') }}
								</th>
								<th>{{ $t('cus.dealSequenceNumber') }}</th>
								<th rowspan="2">
									{{ $t('cus.transactionType') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.transactionAmount') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.exchangeRate') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.dealPrice') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.shares') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.handlingFee') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.taxFee') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.trustManagementFee') }}
								</th>
							</tr>
							<tr>
								<th>{{ $t('cus.productCodeInvestmentTarget') }}</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="tran in etfTranList">
								<td :data-th="$t('cus.transactionDate')">
									{{ $filters.formatDate(tran.tranDt) }}
								</td>
								<td :data-th="$t('cus.dealSequenceNumber') + ' ' + $t('cus.productCodeInvestmentTarget')">
									{{ tran.refNo }}<br><a class="tx-link" href="#">{{ tran.proName }}({{ tran.bankProCode }})</a>
								</td>
								<td :data-th="$t('cus.transactionType')">
									{{ tran.trantypeName }}
								</td>
								<td class="text-end" :data-th="$t('cus.transactionAmount')">
									<label class="num">{{ $filters.formatAmt(tran.invAmtFc) }}</label>&nbsp;{{ tran.tranCurCode }}
								</td>
								<td class="text-end" :data-th="$t('cus.exchangeRate')">
									<label class="num">{{ $filters.formatAmt(tran.fxRate) }}</label>
								</td>
								<td class="text-end" :data-th="$t('cus.dealPrice')">
									<label class="num">{{ $filters.formatAmt(tran.price) }}</label>
								</td>
								<td class="text-end" :data-th="$t('cus.shares')">
									<label class="num">{{ $filters.formatAmt(tran.unit) }}</label>
								</td>
								<td class="text-end" :data-th="$t('cus.handlingFee')">
									<label class="num">{{ $filters.formatAmt(tran.fFeeFc) }}</label>&nbsp;{{ tran.fCurCode }}
								</td>
								<td class="text-end" :data-th="$t('cus.taxFee')">
									<label class="num">{{ $filters.formatAmt(tran.sFeeFc) }}</label>&nbsp;{{ tran.tranCurCode }}
								</td>
								<td class="text-end" :data-th="$t('cus.trustManagementFee')">
									<label class="num">{{ $filters.formatAmt(tran.mFeeFc) }}</label>&nbsp;{{ tran.tranCurCode }}
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div v-if="pfdTranList.length > 0" class="card card-table card-collapse mb-3">
			<div class="card-header">
				<h4>{{ $t('cus.trustForeignStock') }}</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup5" />
			</div>
			<div id="collapseListGroup5" class="collapse show">
				<div class="table-responsive">
					<table class="table table-RWD table-bordered">
						<thead>
							<tr>
								<th rowspan="2">
									{{ $t('cus.transactionDate') }}
								</th>
								<th>{{ $t('cus.delegationNumber') }}</th>
								<th rowspan="2">
									{{ $t('cus.transactionType') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.investmentPrincipal') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.referenceExchangeRate') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.dealPrice') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.dealShares') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.estimatedHandlingFee') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.taxFee') }}
								</th>
								<th rowspan="2">
									{{ $t('cus.estimatedManagementFee') }}
								</th>
							</tr>
							<tr>
								<th>{{ $t('cus.productCodeInvestmentTarget') }}</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="tran in pfdTranList">
								<td :data-th="$t('cus.transactionDate')">
									{{ $filters.formatDate(tran.tranDt) }}
								</td>
								<td :data-th="$t('cus.delegationNumber') + ' ' + $t('cus.productCodeInvestmentTarget')">
									{{ tran.refNo }}<br><a class="tx-link" href="#">{{ tran.proName }}({{ tran.bankProCode }})</a>
								</td>
								<td :data-th="$t('cus.transactionType')">
									{{ tran.trantypeName }}
								</td>
								<td class="text-end" :data-th="$t('cus.investmentPrincipal')">
									<label class="num">{{ $filters.formatAmt(tran.invAmtFc) }}</label>&nbsp;{{ tran.tranCurCode }}
								</td>
								<td class="text-end" :data-th="$t('cus.referenceExchangeRate')">
									<label class="num">{{ $filters.formatAmt(tran.fxRate) }}</label>
								</td>
								<td class="text-end" :data-th="$t('cus.dealPrice')">
									<label class="num">{{ $filters.formatAmt(tran.price) }}</label>
								</td>
								<td class="text-end" :data-th="$t('cus.dealShares')">
									<label class="num">{{ $filters.formatAmt(tran.unit) }}</label>
								</td>
								<td class="text-end" :data-th="$t('cus.estimatedHandlingFee')">
									<label class="num">{{ $filters.formatAmt(tran.fFeeFc) }}</label>&nbsp;{{ tran.fCurCode }}
								</td>
								<td class="text-end" :data-th="$t('cus.taxFee')">
									<label class="num">{{ $filters.formatAmt(tran.sFeeFc) }}</label>&nbsp;{{ tran.tranCurCode }}
								</td>
								<td class="text-end" :data-th="$t('cus.estimatedManagementFee')">
									<label class="num">{{ $filters.formatAmt(tran.mFeeFc) }}</label>&nbsp;{{ tran.tranCurCode }}
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div class="alert alert-warning mt-3" role="alert">
			<span class="ico-alert" />
			{{ $t('cus.functionalityDisclaimer') }}
		</div>
	</div>
</template>
<script>
import { Field, Form } from 'vee-validate';
import moment from 'moment';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	props: {
		cusCode: null
	},
	data: function () {
		return {
			form: {
				cusCode: null,
				pfcatCode: [],
				tranTypeCode: [],
				bankProCode: null,
				refNo: null,
				tranDtB: null,
				tranDtE: null
			},
			pfcatList: [],
			trantypeList: [],
			pageData: [],
			fundTranList: [],
			fbTranList: [],
			etfTranList: [],
			pfdTranList: []
		};
	},
	watch: {},
	mounted: function () {
		const self = this;
		self.form.cusCode = self.cusCode;
		self.form.tranDtB = moment().add(-31, 'days').format('YYYY-MM-DD'); // 交易日期起 (預設一個月前)
		self.form.tranDtE = moment().format('YYYY-MM-DD'); // 交易日期迄
		self.getPfcatList();
		self.getTrantypeList();
	},
	methods: {
		getPfcatList: async function () {
			const self = this;
			const resp = await self.$api.getProCatApi({
				tranYn: 'Y'
			});
			self.pfcatList = resp.data;
		},
		getTrantypeList: async function () {
			const self = this;
			const resp = await self.$api.getTranTypeListApi();
			self.trantypeList = resp.data;
		},
		search: function () {
			const self = this;
			self.$refs.invTranForm.validate().then(function (pass) {
				if (pass.valid) {
					self.getPageData();
				}
			});
		},
		gotoPage: function (page) {
			this.getPageData(page);
		},
		// 呼叫後端 API 取得「客戶交易明細」
		getPageData: async function () {
			const self = this;
			const resp = await self.$api.getTransactionLogApi(self.form);
			self.pageData = resp.data;
			self.fundTranList = self.pageData.filter((tran) => {
				return tran.pfcatCode === 'FUND';
			});
			self.fbTranList = self.pageData.filter((tran) => {
				return tran.pfcatCode === 'FB';
			});
			self.etfTranList = self.pageData.filter((tran) => {
				return tran.pfcatCode === 'ETF';
			});
			self.pfdTranList = self.pageData.filter((tran) => {
				return tran.pfcatCode === 'PFD';
			});
		},
		tranName: function (tranYn) {
			switch (tranYn) {
				case 'Y':
					return this.$t('cus.yes');
				case 'N':
					return this.$t('cus.no');
				default:
					return '';
			}
		},
		productModal: function (item) {
			this.$refs.productModal.open(item.proCode, item.pfcatCode);
		}
	}
};
</script>
