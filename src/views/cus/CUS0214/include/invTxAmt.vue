<template>
	<div class="tab-content">
		<div class="card card-table card-collapse">
			<div class="card-header">
				<h4>交易金額分析</h4>
				<div class="d-flex align-items-center">
					<div class="tx-13 me-2">
						(新臺幣:元)
					</div>
					<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListbyProduct" />
				</div>
			</div>
			<div id="collapseListbyProduct" class="collapse show">
				<div class="table-responsive">
					<table class="table  table-RWD table-bordered text-end">
						<thead>
							<tr>
								<th rowspan="2" class="text-start">
									商品種類
								</th>
								<th colspan="3" class="text-center">
									總買進
								</th>
								<th colspan="3" class="text-center">
									總入帳金額
								</th>
								<th colspan="3" class="text-center">
									總配息
								</th>
								<th colspan="3" class="text-center">
									總損益
								</th>
							</tr>
							<tr>
								<th>MTD</th>
								<th>QTD</th>
								<th>YTD</th>
								<th>MTD</th>
								<th>QTD</th>
								<th>YTD</th>
								<th>MTD</th>
								<th>QTD</th>
								<th>YTD</th>
								<th>MTD</th>
								<th>QTD</th>
								<th>YTD</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="txAmtData in txAmtDatas">
								<td data-th="產品種類">
									{{ txAmtData.pfcatName || '--' }}
								</td>
								<td class="text-end" data-th="總買進-MTD">
									{{ $filters.formatAmt(txAmtData.totBuyAmtMtd, '0,0') }}
								</td>
								<td class="text-end" data-th="總買進-QTD">
									{{ $filters.formatAmt(txAmtData.totBuyAmtQtd, '0,0') }}
								</td>
								<td class="text-end" data-th="總買進-YTD">
									{{ $filters.formatAmt(txAmtData.totBuyAmtYtd, '0,0') }}
								</td>
								<td class="text-end" data-th="總入帳金額-MTD">
									{{ $filters.formatAmt(txAmtData.totSellAmtMtd, '0,0') }}
								</td>
								<td class="text-end" data-th="總入帳金額-QTD">
									{{ $filters.formatAmt(txAmtData.totSellAmtQtd, '0,0') }}
								</td>
								<td class="text-end" data-th="總入帳金額-YTD">
									{{ $filters.formatAmt(txAmtData.totSellAmtYtd, '0,0') }}
								</td>
								<td class="text-end" data-th="總配息-MTD">
									{{ $filters.formatAmt(txAmtData.totDividendMtd, '0,0') }}
								</td>
								<td class="text-end" data-th="總配息-QTD">
									{{ $filters.formatAmt(txAmtData.totDividendQtd, '0,0') }}
								</td>
								<td class="text-end" data-th="總配息-YTD">
									{{ $filters.formatAmt(txAmtData.totDividendYtd, '0,0') }}
								</td>
								<td class="text-end" data-th="總損益-MTD">
									{{ $filters.formatAmt(txAmtData.totRplLcMtd, '0,0') }}
								</td>
								<td class="text-end" data-th="總損益-QTD">
									{{ $filters.formatAmt(txAmtData.totRplLcQtd, '0,0') }}
								</td>
								<td class="text-end" data-th="總損益-YTD">
									{{ $filters.formatAmt(txAmtData.totRplLcYtd, '0,0') }}
								</td>
							</tr>
						</tbody>
						<tfoot>
							<tr class="tx-sum bg-total text-end">
								<td>總計</td>
								<td class="text-end" data-th="總計-總買進-MTD">
									{{ $filters.formatAmt(txAmtDatasSum.totBuyAmtMtdSum, '0,0') }}
								</td>
								<td class="text-end" data-th="總計-總買進-QTD">
									{{ $filters.formatAmt(txAmtDatasSum.totBuyAmtQtdSum, '0,0') }}
								</td>
								<td class="text-end" data-th="總計-總買進-YTD">
									{{ $filters.formatAmt(txAmtDatasSum.totBuyAmtYtdSum, '0,0') }}
								</td>
								<td class="text-end" data-th="總計-總入帳金額-MTD">
									{{ $filters.formatAmt(txAmtDatasSum.totSellAmtMtdSum, '0,0') }}
								</td>
								<td class="text-end" data-th="總計-總入帳金額-QTD">
									{{ $filters.formatAmt(txAmtDatasSum.totSellAmtQtdSum, '0,0') }}
								</td>
								<td class="text-end" data-th="總計-總入帳金額-YTD">
									{{ $filters.formatAmt(txAmtDatasSum.totSellAmtYtdSum, '0,0') }}
								</td>
								<td class="text-end" data-th="總計-總配息-MTD">
									{{ $filters.formatAmt(txAmtDatasSum.totDividendMtdSum, '0,0') }}
								</td>
								<td class="text-end" data-th="總計-總配息-QTD">
									{{ $filters.formatAmt(txAmtDatasSum.totDividendQtdSum, '0,0') }}
								</td>
								<td class="text-end" data-th="總計-總配息-YTD">
									{{ $filters.formatAmt(txAmtDatasSum.totDividendYtdSum, '0,0') }}
								</td>
								<td class="text-end" data-th="總計-總損益-MTD">
									{{ $filters.formatAmt(txAmtDatasSum.totRplLcMtdSum, '0,0') }}
								</td>
								<td class="text-end" data-th="總計-總損益-QTD">
									{{ $filters.formatAmt(txAmtDatasSum.totRplLcQtdSum, '0,0') }}
								</td>
								<td class="text-end" data-th="總計-總損益-YTD">
									{{ $filters.formatAmt(txAmtDatasSum.totRplLcYtdSum, '0,0') }}
								</td>
							</tr>
						</tfoot>
					</table>
					<div class="tx-note">
						<ol>
							<li>MTD:月初至今；QTD:季初至今；YTD:年初至今</li>
							<li>此金額不包含，存款、信託-保險、黃金存摺；財金-附條件交易</li>
						</ol>
					</div>
				</div>
			</div>
		</div>
		<div class="row g-2">
			<div class="col-lg-6">
				<div class="card card-table card-collapse">
					<div class="card-header">
						<h4>單月交易金額</h4>
						<div class="d-flex align-items-center">
							<div class="tx-13 me-2">
								(新臺幣:元)
							</div>
							<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseA" />
						</div>
					</div>
					<div id="collapseA" class="card-body pt-0 collapse show">
						<div class="table-scroll" style="height: 350px;">
							<table class="table  table-RWD text-end">
								<thead>
									<tr>
										<th>
											<span class="TC">日期</span>
										</th>
										<th><span class="TC">買進</span></th>
										<th><span class="TC">入帳金額</span></th>
										<th>配息</th>
										<th><span class="TC">已實現損益</span></th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="txAmtMonthData in txAmtMonthDatas">
										<td class="text-center" data-th="日期">
											{{ txAmtMonthData.dataYm || '--' }}
										</td>
										<td data-th="買進" class="text-end">
											{{ $filters.formatAmt(txAmtMonthData.totBuyAmtLc, '0,0') }}
										</td>
										<td data-th="入帳金額" class="text-end">
											{{ $filters.formatAmt(txAmtMonthData.totSellAmtLc, '0,0') }}
										</td>
										<td class="text-end" data-th="配息">
											{{ $filters.formatAmt(txAmtMonthData.totDividendLc, '0,0') }}
										</td>
										<td data-th="已實現損益" class="text-end">
											<HighLightDeltaText :value="txAmtMonthData.totRplLc">
												{{ $filters.formatAmt(txAmtMonthData.totRplLc, '0,0') }}
											</HighLightDeltaText>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div class="tx-note">
							此金額不包含，存款、信託-保險、黃金存摺；財金-附條件交易
						</div>
						<div class="text-center mt-2">
							<div id="txAmtOneMonthCharContainer" />
						</div>
					</div>
				</div>
			</div>
			<div class="col-lg-6">
				<div class="card card-table card-collapse">
					<div class="card-header">
						<h4> 各月交易金額累計</h4>
						<div class="d-flex align-items-center">
							<div class="tx-13 me-2">
								(新臺幣:元)
							</div>
							<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseB" />
						</div>
					</div>
					<div id="collapseB" class="card-body pt-0 collapse show">
						<div class="table-scroll" style="height: 350px;">
							<table class="table  table-RWD text-end">
								<thead>
									<tr>
										<th>
											<span class="TC">日期</span>
										</th>
										<th><span class="TC">買進</span></th>
										<th><span class="TC">入帳金額</span></th>
										<th>配息</th>
										<th><span class="TC">已實現損益</span></th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="txAmtMonthData in txAmtMonthDatas">
										<td class="text-center" data-th="日期">
											{{ txAmtMonthData.dataYm || '--' }}
										</td>
										<td data-th="買進" class="text-end">
											{{ $filters.formatAmt(txAmtMonthData.grandTotBuyAmtLc, '0,0') }}
										</td>
										<td data-th="入帳金額" class="text-end">
											{{ $filters.formatAmt(txAmtMonthData.grandTotSellAmtLc, '0,0') }}
										</td>
										<td data-th="配息" class="text-end">
											{{ $filters.formatAmt(txAmtMonthData.grandTotDividendLc, '0,0') }}
										</td>
										<td data-th="已實現損益" class="text-end">
											<HighLightDeltaText :value="txAmtMonthData.grandTotRplLc">
												{{ $filters.formatAmt(txAmtMonthData.grandTotRplLc, '0,0') }}
											</HighLightDeltaText>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div class="tx-note">
							此金額不包含，存款、信託-保險、黃金存摺；財金-附條件交易
						</div>
						<div class="text-center mt-2">
							<div id="txAmtMonthlyCharContainer" />
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import _ from 'lodash';
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
export default {
	props: {
		cusCode: null,
		hasAuth: Boolean,
		customer: Object,
		userName: String,
		userRoleName: String,
		queryDt: null,
		dataDt: null
	},
	data: function () {
		return {
			// 主要顯示資料
			txAmtDatas: [],
			txAmtDatasSum: {
				totBuyAmtMtdSum: 0,
				totBuyAmtQtdSum: 0,
				totBuyAmtYtdSum: 0,
				totSellAmtMtdSum: 0,
				totSellAmtQtdSum: 0,
				totSellAmtYtdSum: 0,
				totDividendMtdSum: 0,
				totDividendQtdSum: 0,
				totDividendYtdSum: 0,
				totRplLcMtdSum: 0,
				totRplLcQtdSum: 0,
				totRplLcYtdSum: 0
			},
			txAmtMonthDatas: [],

			// 線圖顯示資料
			txAmtMonthCharLabels: [],
			txAmtOneMonthCharDatas: [],
			txAmtMonthlyCharDatas: []
		};
	},
	mounted: function () {
		const self = this;
		self.getTxAmt();
		self.getTxAmtMonth();
	},
	methods: {
		clean: function () {
			const self = this;
			self.txAmtDatasSum.totBuyAmtMtdSum = 0;
			self.txAmtDatasSum.totBuyAmtQtdSum = 0;
			self.txAmtDatasSum.totBuyAmtYtdSum = 0;
			self.txAmtDatasSum.totSellAmtMtdSum = 0;
			self.txAmtDatasSum.totSellAmtQtdSum = 0;
			self.txAmtDatasSum.totSellAmtYtdSum = 0;
			self.txAmtDatasSum.totDividendMtdSum = 0;
			self.txAmtDatasSum.totDividendQtdSum = 0;
			self.txAmtDatasSum.totDividendYtdSum = 0;
			self.txAmtDatasSum.totRplLcMtdSum = 0;
			self.txAmtDatasSum.totRplLcQtdSum = 0;
			self.txAmtDatasSum.totRplLcYtdSum = 0;
		},
		getTxAmt: function () {
			const self = this;
			self.$api.getInvAnalysisTxAmtApi({ cusCodes: [self.cusCode] }).then(function (ret) {
				self.txAmtDatas = ret.data;
				self.txAmtDatas.forEach(function (item) {
					self.txAmtDatasSum.totBuyAmtMtdSum += !_.isNil(item.totBuyAmtMtd) ? item.totBuyAmtMtd : 0;
					self.txAmtDatasSum.totBuyAmtQtdSum += !_.isNil(item.totBuyAmtQtd) ? item.totBuyAmtQtd : 0;
					self.txAmtDatasSum.totBuyAmtYtdSum += !_.isNil(item.totBuyAmtYtd) ? item.totBuyAmtYtd : 0;
					self.txAmtDatasSum.totSellAmtMtdSum += !_.isNil(item.totSellAmtMtd) ? item.totSellAmtMtd : 0;
					self.txAmtDatasSum.totSellAmtQtdSum += !_.isNil(item.totSellAmtQtd) ? item.totSellAmtQtd : 0;
					self.txAmtDatasSum.totSellAmtYtdSum += !_.isNil(item.totSellAmtYtd) ? item.totSellAmtYtd : 0;
					self.txAmtDatasSum.totDividendMtdSum += !_.isNil(item.totDividendMtd) ? item.totDividendMtd : 0;
					self.txAmtDatasSum.totDividendQtdSum += !_.isNil(item.totDividendQtd) ? item.totDividendQtd : 0;
					self.txAmtDatasSum.totDividendYtdSum += !_.isNil(item.totDividendYtd) ? item.totDividendYtd : 0;
					self.txAmtDatasSum.totRplLcMtdSum += !_.isNil(item.totRplLcMtd) ? item.totRplLcMtd : 0;
					self.txAmtDatasSum.totRplLcQtdSum += !_.isNil(item.totRplLcQtd) ? item.totRplLcQtd : 0;
					self.txAmtDatasSum.totRplLcYtdSum += !_.isNil(item.totRplLcYtd) ? item.totRplLcYtd : 0;
				});
			});
		},
		getTxAmtMonth: function () {
			const self = this;
			self.$api.getInvAnalysisEachMonthTxAmtApi({ cusCodes: [self.cusCode] }).then(function (ret) {
				self.txAmtMonthDatas = ret.data;
				self.txAmtMonthCharLabels = [];
				self.txAmtOneMonthCharDatas = [];
				self.txAmtMonthlyCharDatas = [];

				const oneMonthBuyAmts = {
					type: 'column',
					name: '總買進金額(含手續費)',
					data: []
				};

				const oneMonthSellAmts = {
					type: 'column',
					name: '入帳金額',
					data: []
				};

				const oneMonthDividends = {
					type: 'spline',
					name: '配息',
					data: []
				};

				const oneMonthRpls = {
					type: 'spline',
					name: '已實現損益',
					data: []
				};

				const monthlyBuyAmts = {
					type: 'column',
					name: '總買進金額(含手續費)',
					data: []
				};

				const monthlySellAmts = {
					type: 'column',
					name: '入帳金額',
					data: []
				};

				const monthlyDividends = {
					type: 'spline',
					name: '配息',
					data: []
				};

				const monthlyRpls = {
					type: 'spline',
					name: '已實現損益',
					data: []
				};
				// 因後端 SQL 以日期做倒序排列, 但圖表資料要求呈正序排列(由舊至新), 故先將其順序反轉, 產生完圖表資料後再反轉回來
				_.reverse(self.txAmtMonthDatas);

				self.txAmtMonthDatas.forEach(function (item) {
					self.txAmtMonthCharLabels.push(item.dataYm);
					oneMonthBuyAmts.data.push(item.totBuyAmtLc);
					oneMonthSellAmts.data.push(item.totSellAmtLc);
					oneMonthDividends.data.push(item.totDividendLc);
					oneMonthRpls.data.push(item.totRplLc);

					monthlyBuyAmts.data.push(item.grandTotBuyAmtLc);
					monthlySellAmts.data.push(item.grandTotSellAmtLc);
					monthlyDividends.data.push(item.grandTotDividendLc);
					monthlyRpls.data.push(item.grandTotRplLc);
				});

				_.reverse(self.txAmtMonthDatas); // 將資料反轉回來

				self.txAmtOneMonthCharDatas.push(oneMonthBuyAmts);
				self.txAmtOneMonthCharDatas.push(oneMonthSellAmts);
				self.txAmtOneMonthCharDatas.push(oneMonthDividends);
				self.txAmtOneMonthCharDatas.push(oneMonthRpls);

				self.txAmtMonthlyCharDatas.push(monthlyBuyAmts);
				self.txAmtMonthlyCharDatas.push(monthlySellAmts);
				self.txAmtMonthlyCharDatas.push(monthlyDividends);
				self.txAmtMonthlyCharDatas.push(monthlyRpls);
				// 渲染各月交易圖表
				self.renderChart({
					elementId: 'txAmtOneMonthCharContainer',
					dataset: self.txAmtOneMonthCharDatas,
					labels: self.txAmtMonthCharLabels
				});
				// 渲染各月交易累計圖表
				self.renderChart({
					elementId: 'txAmtMonthlyCharContainer',
					dataset: self.txAmtMonthlyCharDatas,
					labels: self.txAmtMonthCharLabels
				});
			});
		},
		renderChart({ elementId, dataset, labels }) {
			const container = document.getElementById(elementId);
			if (container) container.innerHTML = ''; // 清空容器內容
			container.style.height = window.innerHeight * 0.6 + 'px'; // 視窗高度的 50%
			// container.style.height = "500px";

			// 創建根元素
			const root = am5.Root.new(elementId);
			root._logo.dispose();

			root.setThemes([am5themes_Animated.new(root)]);

			// 創建圖表
			const chart = root.container.children.push(
				am5xy.XYChart.new(root, {
					panX: false,
					panY: false,
					wheelX: 'none',
					wheelY: 'none',
					paddingBottom: 30 // 確保底部有足夠空間
				})
			);

			// X軸（分類軸）
			const xAxis = chart.xAxes.push(
				am5xy.CategoryAxis.new(root, {
					categoryField: 'category',
					renderer: am5xy.AxisRendererX.new(root, {
						minGridDistance: 30
					})
				})
			);

			// X軸數據
			xAxis.data.setAll(labels.map(label => ({ category: label })));
			// Y軸1（主要軸）
			const yAxis1 = chart.yAxes.push(
				am5xy.ValueAxis.new(root, {
					renderer: am5xy.AxisRendererY.new(root, {})
				})
			);

			// Y軸2（對應軸，顯示在右側）
			const yAxis2 = chart.yAxes.push(
				am5xy.ValueAxis.new(root, {
					renderer: am5xy.AxisRendererY.new(root, {
						opposite: true
					})
				})
			);

			// 創建數據系列
			dataset.forEach((seriesData) => {
				if (seriesData.type === 'column') {
					const series = chart.series.push(
						am5xy.ColumnSeries.new(root, {
							name: seriesData.name,
							xAxis: xAxis,
							yAxis: yAxis1,
							valueYField: 'value',
							categoryXField: 'category',
							tooltip: am5.Tooltip.new(root, {
								labelText: '{name}: {valueY}'
							})
						})
					);

					series.data.setAll(
						seriesData.data.map((value, index) => ({
							category: labels[index],
							value: value
						}))
					);

					series.columns.template.setAll({
						tooltipText: '{categoryX}: [bold]{valueY}[/]',
						cornerRadiusTL: 5,
						cornerRadiusTR: 5
					});
				}
				else {
					const series = chart.series.push(
						am5xy.LineSeries.new(root, {
							name: seriesData.name,
							xAxis: xAxis,
							yAxis: yAxis2,
							valueYField: 'value',
							categoryXField: 'category',
							tooltip: am5.Tooltip.new(root, {
								labelText: '{name}: {valueY}'
							})
						})
					);

					series.strokes.template.setAll({
						strokeWidth: 2
					});
					series.data.setAll(
						seriesData.data.map((value, index) => ({
							category: labels[index],
							value: value
						}))
					);

					const cursor = chart.set('cursor', am5xy.XYCursor.new(root, {
						xAxis: xAxis
					}));
					cursor.lineY.set('visible', false);
				}
			});

			// 創建圖例
			const legend = am5.Legend.new(root, {
				centerX: am5.percent(50),
				x: am5.percent(50),
				layout: root.horizontalLayout, // 水平排列
				paddingTop: 10 // 與圖表的距離
			});

			// 將圖例移動到圖表底部
			chart.bottomAxesContainer.children.push(legend);
			// chart.children.moveValue(legend, chart.bottomAxesContainer);

			legend.setAll({
				layout: root.gridLayout, // 改為網格佈局
				maxColumns: 3 // 每行顯示最多 3 個項目
			});
			legend.itemContainers.template.setAll({
				paddingLeft: 5, // 方塊與文字之間的左間距
				paddingRight: 5, // 右間距
				paddingTop: 5,
				paddingBottom: 5,
				minWidth: 80, // 設置每個圖例項目的最小寬度
				minHeight: 20 // 設置每個圖例項目的最小高度
			});

			legend.markers.template.setAll({
				width: 10, // 寬度
				height: 10, // 高度
				shape: am5.Circle.new(root, {}) // 改為圓形（默認為方形）
			});

			legend.labels.template.setAll({
				fontSize: '12px', // 調整字體大小
				maxWidth: 70, // 限制圖例項目文字寬度
				wrap: true, // 啟用文字換行
				textAlign: 'left' // 文字居中
			});
			// 綁定圖例數據
			legend.data.setAll(chart.series.values);

			chart.appear(1000, 100);
		}
	}
};
</script>
