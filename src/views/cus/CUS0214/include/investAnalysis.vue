<template>
	<div>
		<vue-bi-tabs :menu-code="'M20-058'">
			<template #default="{ id }">
				<div>
					<component :is="id" :cus-code="cusCode" :customer="customer" />
				</div>
			</template>
		</vue-bi-tabs>
	</div>
</template>
<script>
import moment from 'moment';
import vueBiTabs from '@/views/components/biTabs.vue';
import vuePagination from '@/views/components/pagination.vue';
import vueCusInvestAnalysisInvTransaction from './cusInvestAnalysisInvTransaction.vue';
import vueCusInvestAnalysisInvTarget from './invTarget.vue';
import vueCusInvestAnalysisInvRealized from './invRealized.vue';
import vueCusInvestAnalysisInvNetWorth from './invNetWorth.vue';
import vueCusInvestAnalysisInvPlAnalysis from './invPlAnalysis.vue';
import vueCusInvestAnalysisInvWealth from './invWealth.vue';
import vueCusInvestAnalysisInvTxAmt from './invTxAmt.vue';
export default {
	components: {
		vueBiTabs,
		vuePagination,
		vueCusInvestAnalysisInvTransaction,
		vueCusInvestAnalysisInvTarget,
		vueCusInvestAnalysisInvRealized,
		vueCusInvestAnalysisInvNetWorth,
		vueCusInvestAnalysisInvPlAnalysis,
		vueCusInvestAnalysisInvWealth,
		vueCusInvestAnalysisInvTxAmt
	},
	props: {
		cusCode: null,
		hasAuth: Boolean,
		customer: Object
	},
	data: function () {
		return {
			// 畫面邏輯判斷用參數
			customTitle: null,
			tabCode: 0,

			userName: '', // 操作者姓名
			userRoleName: '', // 操作者角色名稱
			queryDt: null, // 查詢時間
			dataDt: null // 資料時間
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo'])
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				const self = this;
				if (newVal) {
					if (newVal.deputyUserName) {
						self.userName = newVal.deputyUserName;
					}
					else {
						self.userName = newVal.userName;
					}
					self.userRoleName = newVal.roleName;
				}
			}
		}
	},
	mounted: function () {
		const self = this;
		if (self.hasAuth) {
			self.getQueryDt();
			self.tabCode = 1;
		}
		else {
			self.$bi.alert(self.$t('cus.customerNotUnderYourCenter'));
		}
	},
	methods: {
		changeTab: function (tabCode) {
			const self = this;
			self.tabCode = tabCode;
			self.getQueryDt();
		},
		// 取得查詢時間
		getQueryDt: async function () {
			const self = this;
			self.queryDt = moment().format('YYYY/MM/DD HH:mm');

			const ret = await self.$api.getLastAssetAmount({
				cusCode: self.cusCode
			});
			if (ret.data) {
				self.dataDt = ret.data.dataDt;
			}
		}
	}
};
</script>
