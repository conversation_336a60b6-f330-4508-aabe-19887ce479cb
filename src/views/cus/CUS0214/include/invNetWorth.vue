<template>
	<div class="inv-net-worth-container">
		<div class="tab-content">
			<div class="d-flex justify-content-end">
				<div class="tx-note mt-0">
					金額無特別注記幣別者，皆以新台幣計。
				</div>
			</div>
			<div class="row g-2">
				<div class="col-lg-6">
					<div class="card card-table">
						<div class="card-header">
							<h4>各月月底餘額 - 未實現損益</h4>
							<div class="tx-13">
								(新臺幣:元)
							</div>
						</div>
						<div class="card-body pt-0">
							<div class="table-scroll" style="height: 350px;">
								<table class="table table-RWD table-bordered text-end">
									<thead>
										<tr>
											<th>
												日期
											</th>
											<th>投資本金<br>(含手續費)</th>
											<th>投資現值</th>
											<th>未實現損益</th>
											<th>報酬率</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="monthAum in invAnalysisEachMonthAum" :key="monthAum.dataYm">
											<td data-th="日期" class="text-center">
												{{ monthAum.dataYm || '--' }}
											</td>
											<td data-th="投資成本" class="text-end">
												{{ $filters.formatDividend(monthAum.invAmtLc, '0,0') }}
											</td>
											<td data-th="投資現值" class="text-end">
												{{ $filters.formatDividend(monthAum.mktAmtLc, '0,0') }}
											</td>
											<td class="text-end" data-th="未實現損益">
												<HighLightDeltaText :value="monthAum.uplLc">
													{{ $filters.formatDividend(monthAum.uplLc, '0,0') }}
												</HighLightDeltaText>
											</td>
											<td class="text-end" data-th="報酬率">
												<HighLightDeltaText :value="monthAum.ureturnLc">
													{{ $filters.formatDividend(monthAum.ureturnLc, '0,0.00') }}%
												</HighLightDeltaText>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="text-center mt-2">
								<div id="monthAumChartContainer" />
							</div>
						</div>
					</div>
					<div class="tx-note">
						<ol>
							<li>本表數據，為各月月底各投資產品的投資餘額，其中不包含活存、定存等被歸類為現金的產品</li>
							<li>投資成本，為已經考慮費用影響，並以投資時點匯率換算的本國幣別投資成本</li>
							<li>市值：投資產品以各月月底結算報價 × 持倉單位數</li>
							<li>未實現損益：市值 – 投資成本</li>
						</ol>
					</div>
				</div>
				<div class="col-lg-6">
					<div class="card card-table">
						<div class="card-header">
							<h4>各月月底實現損益金額</h4>
							<div class="tx-13 text-end">
								(新臺幣:元)
							</div>
						</div>
						<div class="card-body pt-0">
							<div class="table-scroll" style="height: 350px;">
								<table class="table table-RWD">
									<thead>
										<tr>
											<th>
												日期
											</th>
											<th>總賣出金額</th>
											<th>投資本金<br>(含手續費)</th>
											<th>已實現損益</th>
											<th>累計配息</th>
											<th>報酬率</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="monthRpl in invAnalysisEachMonthRpl" :key="monthRpl.dataYm">
											<td data-th="日期" class="text-center">
												{{ monthRpl.dataYm || '--' }}
											</td>
											<td data-th="投資成本" class="text-end">
												{{ $filters.formatDividend(monthRpl.totSellAmtLc, '0,0') }}
											</td>
											<td data-th="投資本金(含手續費)" class="text-end">
												{{ $filters.formatDividend(monthRpl.totSellInvamtLc, '0,0') }}
											</td>
											<td data-th="已實現損益" class="text-end">
												<HighLightDeltaText :value="monthRpl.totRplLc">
													{{ $filters.formatDividend(monthRpl.totRplLc, '0,0') }}
												</HighLightDeltaText>
											</td>
											<td data-th="累計配息" class="text-end">
												{{ $filters.formatDividend(monthRpl.totDividendLc, '0,0') }}
											</td>
											<td data-th="含息報酬率(%)" class="text-end">
												<HighLightDeltaText :value="monthRpl.returnLc">
													{{ $filters.formatDividend(monthRpl.returnLc, '0,0.00') }}%
												</HighLightDeltaText>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="text-center mt-2">
								<div id="monthRplChartContainer" />
							</div>
						</div>
					</div>
					<div class="tx-note">
						<ol>
							<li>本表數據，為各月月底累計當月發生的賣出交易所產生的實現損益金額。<br>統計範圍僅限各投資產品的投資餘額，其中不包含活存、定存等被歸類為現金的產品</li>
							<li>賣出金額 ＝（賣出價格×當日匯率 ＋ 費用影響調整 ＋ 匯率影響調整 ＋ 利息影響調整）× 賣出單位數</li>
							<li>成本 ＝為已經考慮費用影響，並以投資時點匯率換算的本國幣別投資成本</li>
							<li>累計配息：單位數累計的平均配息金額 × 賣出單位數</li>
							<li>報酬率 ＝ （實現損益＋累計配息） / 成本</li>
						</ol>
					</div>
				</div>
			</div>
		</div>

		<!-- Modal 各月月底餘額 - 未實現損益 -->
		<vue-modal :is-open="modalStates.modal1" @close="() => closeModal('modal1')">
			<template #content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								各月月底餘額 - 未實現損益
							</h4>
							<button type="button" class="btn-expand">
								<i class="bi bi-arrows-fullscreen" />
							</button>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="props.close()"
							/>
						</div>
						<div class="modal-body">
							<div class="table-responsive mb-3">
								<table class="table table-RWD table-horizontal-RWD table-bordered">
									<thead>
										<tr>
											<th width="20%">
												資產類別
											</th>
											<th width="13%">
												USD
											</th>
											<th width="10%">
												EUR
											</th>
											<th width="10%">
												JPY
											</th>
											<th width="10%">
												NTD
											</th>
											<th width="18%">
												總投資金額(NTD)
											</th>
											<th width="10%">
												市值(NTD)
											</th>
											<th width="11%">
												報酬率
											</th>
										</tr>
									</thead>
									<tbody>
										<tr class="tx-sum">
											<th data-th="資產類別" class="btn-minus">
												<button
													class="btn btn-icon"
													type="button"
													data-bs-toggle="collapse"
													data-bs-target="#collapse4"
													aria-expanded="false"
													aria-controls="collapse4"
												>
													<strong><i
														class="bi bi-plus"
													/></strong>
												</button>
												其他市場
											</th>
											<td data-th="USD">
												31,772.00
											</td>
											<td data-th="EUR">
												8,864.00
											</td>
											<td data-th="JPY">
												991.00
											</td>
											<td data-th="CNY">
												876,463.00
											</td>
											<td data-th="總投資金額(CNY)">
												2,269,278.25
											</td>
											<td data-th="市值(CNY)">
												2,269,278.25
											</td>
											<td data-th="報酬率">
												100%
											</td>
										</tr>
										<tr id="collapse4" class="collapse" aria-expanded="true">
											<td><span class="mg-xl-s-28-f">衍生及固定期間結構商品</span></td>
											<td data-th="USD">
												772.00
											</td>
											<td data-th="EUR">
												864.00
											</td>
											<td data-th="JPY">
												891.00
											</td>
											<td data-th="CNY">
												463.00
											</td>
											<td data-th="總投資金額(CNY)">
												0.00
											</td>
											<td data-th="市值(CNY)">
												0.00
											</td>
											<td data-th="報酬率">
												100%
											</td>
										</tr>
										<tr class="tx-sum bg-subtotal">
											<td data-th="資產類別">
												<span class="mg-xl-s-28-f">小計(NTD)</span>
											</td>
											<td>3,533,583.00</td>
											<td>715,427.00</td>
											<td>568,765.00</td>
											<td>1,221,739.00</td>
											<td>145,308,590.45</td>
											<td>145,308,590.45</td>
											<td>&nbsp;</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="row">
								<div class="col-lg-6">
									<div id="chartdivAstPro" class="dbchart-container" />
								</div>
								<div class="col-lg-6">
									<div id="chartdivAstCoin" class="dbchart-container" />
								</div>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-white" @click.prevent="props.close()">
								關閉
							</button>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>

		<!-- Modal 各月月底實現損益金額 -->
		<vue-modal :is-open="modalStates.modal2" @close="() => closeModal('modal2')">
			<template #content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								各月月底實現損益金額
							</h4>
							<button type="button" class="btn-expand">
								<i class="bi bi-arrows-fullscreen" />
							</button>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="props.close()"
							/>
						</div>
						<div class="modal-body">
							<div class="table-responsive">
								<table class="table table-RWD table-horizontal-RWD table-bordered text-end">
									<thead>
										<tr>
											<th class="text-start">
												賬戶編號<br>產品名稱
											</th>
											<th>交易日</th>
											<th>幣種<br>交易類型</th>
											<th>交易金額<br>(原幣)</th>
											<th>原始投資金額<br>(原幣)</th>
											<th>原始投資金額<br>(本國幣)</th>
											<th>損益<br>(原幣)</th>
											<th>未調整損益<br>(本國幣)</th>
											<th>匯率效果<br>(本國幣)</th>
											<th>利息效果<br>(本國幣)</th>
											<th>費用效果<br>(本國幣)</th>
											<th>實質損益<br>(本國幣)</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td class="text-start">
												07493000406<br>保德信全球醫療基金(5306)
											</td>
											<td>2022-01-13</td>
											<td>CNY<br>賣單</td>
											<td>110,000.00</td>
											<td>100,000.00</td>
											<td>100,000.00</td>
											<td>10,000.00<br>10.00%</td>
											<td>10,000.00<br>10.00%</td>
											<td>0.00<br>0.00%</td>
											<td>100.00<br>0.10%</td>
											<td>-3,000.00<br>-3.00%</td>
											<td>7,100.00<br>7.10%</td>
										</tr>
										<tr class="tx-sum bg-total">
											<td colspan="5">
												期間報酬合計
											</td>
											<td>9,700,000.00</td>
											<td>--</td>
											<td>1,610,000.00<br>16.60%</td>
											<td> 15,000.00<br> 0.15%</td>
											<td> 5,600.00<br> 0.06%</td>
											<td> -13,000.00<br>-0.13%</td>
											<td>1,617,600.00<br>16.69%</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="tx-note">
								<ol>
									<li>未調整本國幣損益 = 原始投資匯率 * 原幣損益</li>
									<li>原始投資金額均已含原始投資所發生的費用</li>
									<li>實質損益 = 未調整損益 + 匯率效果 + 利息效果 + 費用效果</li>
								</ol>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-white" @click.prevent="props.close()">
								關閉
							</button>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
	</div>
</template>

<script>
import _, { get } from 'lodash';
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import vueModal from '@/views/components/model.vue';
import {
	formatDividend
} from '@/utils/filter';

export default {
	components: {
		vueModal
	},
	props: {
		cusCode: null,
		hasAuth: Boolean,
		customer: Object,
		userName: String,
		userRoleName: String,
		queryDt: null,
		dataDt: null
	},
	data: function () {
		return {
			modalStates: {
				modal1: false,
				modal2: false
			},
			// 主要顯示資料
			invAnalysisEachMonthAum: [],
			invAnalysisEachMonthRpl: [],
			monthAumChartDateYm: [],
			monthRplChartDateYm: [],
			monthAumChartData: [],
			monthRplChartData: [],

			baseCurCode: 'TWD',
			baseCurName: '新台幣',
			curCode: null, // 選定計算幣別
			curName: null, // 選定計算幣別名稱
			curScale: null // 選定計算幣別小數位數
		};
	},
	mounted: function () {
		const self = this;
		self.getInvAnalysisEachMonthAum();
		self.getInvAnalysisEachMonthRpl();

		// if (self.cusCode) {
		// 	self.getBaseCurrency();
		// }
	},
	methods: {
		formatDividend(value) {
			return formatDividend(value);
		},
		closeModal: function (modalName) {
			this.modalStates[modalName] = false;
		},
		openModal: function (modalName) {
			this.modalStates[modalName] = true;
		},
		// 取得會員基準幣
		// getBaseCurrency: function () {
		// 	var self = this;
		// 	var url = self.config.apiPath + '/cus/baseCurCode';
		//
		// 	$.bi
		// 		.ajax({
		// 			url: url,
		// 			method: 'GET',
		// 			data: {
		// 				cusCode: self.cusCode
		// 			}
		// 		})
		// 		.then(function (ret) {
		// 			self.baseCurCode = ret.data.curCode;
		// 			self.baseCurName = ret.data.curName;
		// 			self.curCode = ret.data.curCode;
		// 			self.curName = ret.data.curName;
		// 			self.curScale = ret.data.scale;
		// 			// 設定 curCode 之後, 執行頁面載入
		// 			self.getInvAnalysisEachMonthAum();
		// 			self.getInvAnalysisEachMonthRpl();
		// 		});
		// },
		getInvAnalysisEachMonthAum: function () {
			const self = this;
			self.$api.getCusInvAnalysisEachMonthAumApi({ cusCodes: self.cusCode }).then(function (ret) {
				self.invAnalysisEachMonthAum = ret.data.map((item) => {
					const { dataYm, ...rest } = item;
					return {
						...rest,
						dataYm: /^\d{6}$/.test(dataYm)
							? `${dataYm.substring(0, 4)}-${dataYm.substring(4)}`
							: dataYm };
				});
				const invAmtLc = {
					type: 'column',
					name: '投資成本(含手續費)',
					data: []
				};
				const mktAmtLc = {
					type: 'column',
					name: '投資現值',
					data: []
				};
				const ureturnLc = {
					type: 'spline',
					name: '報酬率',
					yAxis: 1,
					data: [],
					tooltip: {
						// 報酬率圖表曲線顯示百分比
						pointFormatter() {
							return `${this.series.name}: <b>${this.y}%</b>`;
						}
					}
				};

				// 因後端 SQL 以日期做倒序排列, 但圖表資料要求呈正序排列(由舊至新), 故先將其順序反轉, 產生完圖表資料後再反轉回來
				_.reverse(self.invAnalysisEachMonthAum);

				self.monthAumChartDateYm = [];
				self.invAnalysisEachMonthAum.forEach(function (item) {
					invAmtLc.data.push(item.invAmtLc);
					mktAmtLc.data.push(item.mktAmtLc);
					ureturnLc.data.push(item.ureturnLc);
					self.monthAumChartDateYm.push(item.dataYm);
				});

				_.reverse(self.invAnalysisEachMonthAum); // 將資料反轉回來

				self.monthAumChartData = [];
				self.monthAumChartData.push(invAmtLc);
				self.monthAumChartData.push(mktAmtLc);
				self.monthAumChartData.push(ureturnLc);
				self.renderMonthAumChart();
			});
		},
		getInvAnalysisEachMonthRpl: function () {
			const self = this;
			self.$api.getCusInvAnalysisEachMonthRplApi({ cusCodes: self.cusCode }).then(function (ret) {
				self.invAnalysisEachMonthRpl = ret.data;
				const totSellAmtLc = {
					type: 'column',
					name: '總入帳金額',
					data: []
				};
				const totSellInvamtLc = {
					type: 'column',
					name: '投資本金(含手續費)',
					data: []
				};
				const totRplLc = {
					type: 'column',
					name: '已實現損益',
					data: []
				};
				const returnLc = {
					type: 'spline',
					name: '報酬率',
					yAxis: 1,
					data: [],
					tooltip: {
						// 報酬率圖表曲線顯示百分比
						pointFormatter() {
							return `${this.series.name}: <b>${this.y}%</b>`;
						}
					}
				};
				// 因後端 SQL 以日期做倒序排列, 但圖表資料要求呈正序排列(由舊至新), 故先將其順序反轉, 產生完圖表資料後再反轉回來
				_.reverse(self.invAnalysisEachMonthRpl);

				self.monthRplChartDateYm = [];
				self.invAnalysisEachMonthRpl.forEach(function (item) {
					totSellAmtLc.data.push(item.totSellAmtLc);
					totSellInvamtLc.data.push(item.totSellInvamtLc);
					returnLc.data.push(item.returnLc);
					self.monthRplChartDateYm.push(item.dataYm);
				});

				_.reverse(self.invAnalysisEachMonthRpl); // 將資料反轉回來

				self.monthRplChartData = [];
				self.monthRplChartData.push(totSellAmtLc);
				self.monthRplChartData.push(totSellInvamtLc);
				self.monthRplChartData.push(returnLc);
				self.renderMonthRplChart();
			});
		},
		renderMonthAumChart: function () {
			const self = this;

			// Create root element
			const root = am5.Root.new('monthAumChartContainer');
			root._logo.dispose();

			// Set themes
			root.setThemes([am5themes_Animated.new(root)]);

			// Create chart
			const chart = root.container.children.push(
				am5xy.XYChart.new(root, {
					layout: root.verticalLayout
				})
			);

			// Create X-axis (categories)
			const xAxis = chart.xAxes.push(
				am5xy.CategoryAxis.new(root, {
					categoryField: 'category',
					renderer: am5xy.AxisRendererX.new(root, {
						minGridDistance: 20
					})
				})
			);

			// Set X-axis data
			const xAxisData = self.monthAumChartDateYm.map(date => ({ category: date }));
			xAxis.data.setAll(xAxisData);

			// Create Y-axis for values
			const yAxisLeft = chart.yAxes.push(
				am5xy.ValueAxis.new(root, {
					renderer: am5xy.AxisRendererY.new(root, {}),
					tooltip: am5.Tooltip.new(root, {}),
					numberFormat: '#a' // No decimals for the left Y-axis
				})
			);

			// Create secondary Y-axis for percentages
			const yAxisRight = chart.yAxes.push(
				am5xy.ValueAxis.new(root, {
					renderer: am5xy.AxisRendererY.new(root, {
						opposite: true
					}),
					tooltip: am5.Tooltip.new(root, {}),
					numberFormat: '#.#\'%\''
				})
			);

			// Add series for the left Y-axis
			const seriesLeft = chart.series.push(
				am5xy.ColumnSeries.new(root, {
					name: 'Column Data',
					xAxis: xAxis,
					yAxis: yAxisLeft,
					valueYField: 'value',
					categoryXField: 'category',
					tooltip: am5.Tooltip.new(root, {
						labelText: '{name}: {valueY}'
					})
				})
			);

			// Add series for the right Y-axis
			const seriesRight = chart.series.push(
				am5xy.LineSeries.new(root, {
					name: 'Percentage Data',
					xAxis: xAxis,
					yAxis: yAxisRight,
					valueYField: 'percentage',
					categoryXField: 'category',
					tooltip: am5.Tooltip.new(root, {
						labelText: '{name}: {valueY}%'
					})
				})
			);

			// Set data for both series
			const chartData = self.monthAumChartData.map((item, index) => ({
				category: self.monthAumChartDateYm[index],
				value: item.value,
				percentage: item.percentage
			}));
			seriesLeft.data.setAll(chartData);
			seriesRight.data.setAll(chartData);

			// Add legend
			const legend = chart.children.push(
				am5.Legend.new(root, {
					centerX: am5.p50,
					x: am5.p50,
					layout: root.horizontalLayout
				})
			);
			legend.data.setAll(chart.series.values);

			// Add responsive rules
			root.responsive.enabled = true;
			root.responsive.rules.push({
				relevant: am5Responsive.defaultRules.small,
				apply: function () {
					chart.set('layout', root.verticalLayout);
					legend.set('layout', root.verticalLayout);
				}
			});

			// Cleanup on destroy
			return function cleanup() {
				root.dispose();
			};
		},
		renderMonthRplChart: function () {
			const self = this;

			// Create root element
			const root = am5.Root.new('monthRplChartContainer');
			root._logo.dispose();

			// Set themes
			root.setThemes([am5themes_Animated.new(root)]);

			// Create chart
			const chart = root.container.children.push(
				am5xy.XYChart.new(root, {
					layout: root.verticalLayout
				})
			);

			// Create X-axis (categories)
			const xAxis = chart.xAxes.push(
				am5xy.CategoryAxis.new(root, {
					categoryField: 'category',
					renderer: am5xy.AxisRendererX.new(root, {
						minGridDistance: 20
					})
				})
			);

			// Map self.monthRplChartDateYm to xAxis data
			const xAxisData = self.monthRplChartDateYm.map(date => ({ category: date }));
			xAxis.data.setAll(xAxisData);

			// Create Y-axis for values
			const yAxisLeft = chart.yAxes.push(
				am5xy.ValueAxis.new(root, {
					renderer: am5xy.AxisRendererY.new(root, {}),
					tooltip: am5.Tooltip.new(root, {}),
					numberFormat: '#a' // Disable decimals for the left Y-axis
				})
			);

			// Create secondary Y-axis for percentages
			const yAxisRight = chart.yAxes.push(
				am5xy.ValueAxis.new(root, {
					renderer: am5xy.AxisRendererY.new(root, {
						opposite: true
					}),
					tooltip: am5.Tooltip.new(root, {}),
					numberFormat: '#.#\'%\''
				})
			);

			// Add series for the left Y-axis
			const seriesLeft = chart.series.push(
				am5xy.ColumnSeries.new(root, {
					name: 'Column Data',
					xAxis: xAxis,
					yAxis: yAxisLeft,
					valueYField: 'value',
					categoryXField: 'category',
					tooltip: am5.Tooltip.new(root, {
						labelText: '{name}: {valueY}'
					})
				})
			);

			// Add series for the right Y-axis
			const seriesRight = chart.series.push(
				am5xy.LineSeries.new(root, {
					name: 'Percentage Data',
					xAxis: xAxis,
					yAxis: yAxisRight,
					valueYField: 'percentage',
					categoryXField: 'category',
					tooltip: am5.Tooltip.new(root, {
						labelText: '{name}: {valueY}%'
					})
				})
			);

			// Map self.monthRplChartData to series data
			const chartData = self.monthRplChartData.map((data, index) => ({
				category: self.monthRplChartDateYm[index],
				value: data.value,
				percentage: data.percentage
			}));
			seriesLeft.data.setAll(chartData);
			seriesRight.data.setAll(chartData);

			// Add legend
			const legend = chart.children.push(
				am5.Legend.new(root, {
					centerX: am5.p50,
					x: am5.p50,
					layout: root.horizontalLayout
				})
			);
			legend.data.setAll(chart.series.values);

			// Add responsive rules
			root.responsive.enabled = true;
			root.responsive.rules.push({
				relevant: am5Responsive.defaultRules.small,
				apply: function () {
					chart.set('layout', root.verticalLayout);
					legend.set('layout', root.verticalLayout);
				}
			});

			// Cleanup on destroy
			return function cleanup() {
				root.dispose();
			};
		}
	}

};
</script>
