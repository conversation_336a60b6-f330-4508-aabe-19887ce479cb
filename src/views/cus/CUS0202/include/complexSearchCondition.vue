<template>
	<ul id="job" class="list-group list-inline-tags mb-3">
		<li class="label">
			{{ $t('cus.searchConditions') }}：
		</li>
		<li v-show="cond.graName != null" class="list-group-item">
			<ColoredLink href="#">
				<span><strong>{{ $t('cus.customerLevel') }} : </strong> {{ cond.graName }}</span>
				<span
					class="img-delete JQ-delet"
					title=""
					:data-bs-original-title="$t('cus.delete')"
					@click.prevent="deleteGra"
				/>
			</ColoredLink>
		</li>
		<li v-show="cond.area != null || cond.ca != null || cond.branCode != null" class="list-group-item">
			<ColoredLink href="#">
				<span>
					<strong>{{ $t('cus.organization') }} : </strong>
					<span v-if="cond.area != null"><strong>{{ $t('cus.region') }}</strong> : {{ cond.area }} </span>
					<span v-if="cond.bran != null"><strong> {{ $t('cus.branch') }}</strong> : {{ cond.bran }} </span>
					<span v-if="cond.user != null"><strong> {{ $t('cus.advisor') }}</strong> : {{ cond.user }} </span>
				</span>
				<span
					class="img-delete JQ-delet"
					title=""
					:data-bs-original-title="$t('cus.delete')"
					@click.prevent="deleteBran"
				/>
			</ColoredLink>
		</li>
		<li v-show="cond.birth != null" class="list-group-item">
			<ColoredLink href="#">
				<span><strong>{{ $t('cus.customerBirthday') }} : </strong> {{ cond.birth }}</span>
				<span
					class="img-delete JQ-delet"
					title=""
					:data-bs-original-title="$t('cus.delete')"
					@click.prevent="deleteBirth"
				/>
			</ColoredLink>
		</li>
		<li v-show="cond.rank != null" class="list-group-item">
			<ColoredLink href="#">
				<span><strong>{{ $t('cus.investmentProfile') }} : </strong> {{ cond.rank }}</span>
				<span
					class="img-delete JQ-delet"
					title=""
					:data-bs-original-title="$t('cus.delete')"
					@click.prevent="deleteRank"
				/>
			</ColoredLink>
		</li>
		<li v-show="req.aumAmountS != null" class="list-group-item">
			<ColoredLink href="#">
				<span><strong>{{ $t('cus.bankTotalAssets') }} : {{ $t('cus.amountBetween') }}</strong>: {{ $filters.formatNumber(req.aumAmountS) }}~{{
					$filters.formatNumber(req.aumAmountE)
				}}
					{{ $t('cus.yuan') }}</span>
				<span
					class="img-delete JQ-delet"
					title=""
					:data-bs-original-title="$t('cus.delete')"
					@click.prevent="deleteAum"
				/>
			</ColoredLink>
		</li>
		<li v-show="req.contbAmountS != null" class="list-group-item">
			<ColoredLink href="#">
				<span>
					<strong>{{ $t('cus.bankContribution') }} : </strong>
					<strong> {{ $t('cus.contributionBetween') }}</strong>: {{ $filters.formatNumber(req.contbAmountS) }}~{{ $filters.formatNumber(req.contbAmountE) }}{{ $t('cus.yuan') }}
					<span v-if="req.contbTime === 'DEF'"><strong> {{ $t('cus.timeInterval') }}</strong>: {{ cond.contbTimeS }}~{{ cond.contbTimeE }}</span>
					<span v-else><strong> {{ $t('cus.timeInterval') }}</strong> : {{ cond.contbTime }}</span>
				</span>
				<span
					class="img-delete JQ-delet"
					title=""
					:data-bs-original-title="$t('cus.delete')"
					@click.prevent="deleteContb"
				/>
			</ColoredLink>
		</li>
		<li v-show="cond.savingType != null || cond.savingC != null || req.savingAmountS != null" class="list-group-item">
			<ColoredLink href="#">
				<span>
					<strong>{{ $t('cus.savingsProducts') }} : </strong>
					<span v-if="cond.savingType != null"><strong>{{ $t('cus.savingsType') }}</strong> : {{ cond.savingType }} </span>
					<span v-if="cond.savingC != null"><strong> {{ $t('cus.currency') }}</strong> : {{ cond.savingC }} </span>
					<span v-if="req.savingAmountS != null"><strong> {{ $t('cus.accountBalance') }}</strong> : {{ $filters.formatNumber(req.savingAmountS) }}~{{
						$filters.formatNumber(req.savingAmountE)
					}}
					</span>
				</span>
				<span
					class="img-delete JQ-delet"
					title=""
					:data-bs-original-title="$t('cus.delete')"
					@click.prevent="deleteSaving"
				/>
			</ColoredLink>
		</li>
		<li
			v-show="
				req.proCatList.length > 0 ||
					req.proC != null ||
					req.intType != null ||
					req.proCode != null ||
					req.mktAmountS != null ||
					req.invAmountS != null ||
					req.returnAmountS != null
			"
			class="list-group-item"
		>
			<ColoredLink href="#">
				<span>
					<strong>{{ $t('cus.investmentProducts') }} : </strong>
					<span v-if="req.proCatList.length > 0"><strong>{{ $t('cus.productMainCategory') }}</strong> : {{ cond.proCatList }} </span>
					<span v-if="req.proCode != null"><strong> {{ $t('cus.byProductCode') }}</strong> : {{ req.proCode }} </span>
					<span v-if="req.proC != null"><strong> {{ $t('cus.currency') }}</strong> : {{ cond.proC }} </span>
					<span v-if="req.intType != null"><strong> {{ $t('cus.dividendStatus') }}</strong> : {{ cond.intType }} </span>
					<span v-if="req.mktAmountS != null"><strong> {{ $t('cus.marketValue') }}</strong> : {{ $filters.formatNumber(req.mktAmountS) }}~{{ $filters.formatNumber(req.mktAmountE) }}
					</span>
					<span v-if="req.invAmountS != null"><strong> {{ $t('cus.investmentPrincipal') }}</strong> : {{ $filters.formatNumber(req.invAmountS) }}~{{ $filters.formatNumber(req.invAmountE) }}
					</span>
					<span v-if="req.returnAmountS != null"><strong> {{ $t('cus.returnRate') }}</strong> : {{ $filters.formatPct(req.returnAmountS) }}%~{{ $filters.formatPct(req.returnAmountE) }}%
					</span>
				</span>
				<span
					class="img-delete JQ-delet"
					title=""
					:data-bs-original-title="$t('cus.delete')"
					@click.prevent="deletePro"
				/>
			</ColoredLink>
		</li>

		<li
			v-show="req.invType != null || req.debitType != null || req.fundC != null || req.efficiencyInv != null || req.fundType != null"
			class="list-group-item"
		>
			<ColoredLink href="#">
				<span>
					<strong>{{ $t('cus.fundProducts') }} : </strong>
					<span v-if="req.invType != null"><strong> {{ $t('cus.subscriptionType') }}</strong> : {{ cond.invType }} </span>
					<span v-if="req.debitType != null"><strong> {{ $t('cus.debitType') }}</strong> : {{ cond.debitType }} </span>
					<span v-if="req.fundC != null"><strong> {{ $t('cus.currency') }}</strong> : {{ cond.fundC }} </span>
					<span v-if="req.efficiencyInv != null"><strong> {{ $t('cus.efficiencyInvestment') }}</strong> : {{ cond.efficiencyInv }} </span>
					<span v-if="req.fundType != null"><strong> {{ $t('cus.fundType') }}</strong> : {{ cond.fundType }} </span>
				</span>
				<span
					class="img-delete JQ-delet"
					title=""
					:data-bs-original-title="$t('cus.delete')"
					@click.prevent="deleteFundPro"
				/>
			</ColoredLink>
		</li>
		<li v-show="req.insType != null || req.insC != null || req.insAAmountS != null" class="list-group-item">
			<ColoredLink href="#">
				<span>
					<strong>{{ $t('cus.lifeInsuranceProducts') }} : </strong>
					<span v-if="req.insType != null"><strong>{{ $t('cus.insuranceType') }}</strong> : {{ cond.insType }} </span>
					<span v-if="req.insC != null"><strong> {{ $t('cus.currency') }}</strong> : {{ cond.insC }} </span>
					<span v-if="req.insAAmountS != null"><strong> {{ $t('cus.accumulatedPremiumPaid') }}</strong> : {{ $filters.formatNumber(req.insAAmountS) }}~{{ $filters.formatNumber(req.insAAmountE) }}
					</span>
				</span>
				<span
					class="img-delete JQ-delet"
					title=""
					:data-bs-original-title="$t('cus.delete')"
					@click.prevent="deleteIns"
				/>
			</ColoredLink>
		</li>
		<li
			v-show="
				cond.loanProCode != null ||
					cond.loanRemark != null ||
					req.loanStdDateS != null ||
					req.loanStdDateE != null ||
					req.loanPeriodS != null ||
					req.loanPeriodE != null ||
					req.appropriationS != null ||
					req.appropriationE != null ||
					req.loanOverAmountS != null ||
					req.loanOverAmountE != null ||
					req.conAppropriationS != null ||
					req.conAppropriationE != null ||
					req.conRateS != null ||
					req.conRateE != null ||
					cond.conReturnType != null ||
					req.conOverAmountS != null ||
					req.conOverAmountE != null ||
					req.conRepaymentRateS != null ||
					req.conRepaymentRateE != null
			"
			class="list-group-item"
		>
			<ColoredLink href="#">
				<span>
					<strong>{{ $t('cus.personalLoanDisbursement') }} : </strong>
					<span v-if="cond.loanProCode != null"><strong>{{ $t('cus.loanProduct') }}</strong> : {{ cond.loanProCode }} </span>
					<span v-if="cond.loanRemark != null"><strong> {{ $t('cus.loanRemark') }}</strong> : {{ cond.loanRemark }} </span>
					<span v-if="req.loanStdDateS != null"><strong> {{ $t('cus.initialLoanDate') }}</strong> : {{ req.loanStdDateS }}~{{ req.loanStdDateE }}</span>
					<span v-if="req.loanPeriodS != null"><strong> {{ $t('cus.loanPeriod') }}</strong> : {{ $filters.formatNumber(req.loanPeriodS) }}~{{ $filters.formatNumber(req.loanPeriodE) }}
					</span>
					<span v-if="req.appropriationS != null"><strong> {{ $t('cus.disbursementAmount') }}</strong> : {{ $filters.formatNumber(req.appropriationS) }}~{{
						$filters.formatNumber(req.appropriationE)
					}}
					</span>
					<span v-if="req.loanOverAmountS != null"><strong> {{ $t('cus.loanBalance') }}</strong> : {{ $filters.formatNumber(req.loanOverAmountS) }}~{{
						$filters.formatNumber(req.loanOverAmountE)
					}}
					</span>
					<span v-if="req.conAppropriationS != null"><strong> {{ $t('cus.disbursementAmountShort') }}</strong> : {{ $filters.formatNumber(req.conAppropriationS) }}~{{
						$filters.formatNumber(req.conAppropriationE)
					}}
					</span>
					<span v-if="req.conRateS != null"><strong> {{ $t('cus.contractRate') }}</strong> : {{ $filters.formatPct(req.conRateS) }}%~{{ $filters.formatPct(req.conRateE) }}%
					</span>
					<span v-if="cond.conReturnType != null"><strong> {{ $t('cus.repaymentMethod') }}</strong> : {{ cond.conReturnType }} </span>
					<span v-if="req.conOverAmountS != null"><strong> {{ $t('cus.loanBalanceShort') }}</strong> : {{ $filters.formatNumber(req.conOverAmountS) }}~{{ $filters.formatNumber(req.conOverAmountE) }}
					</span>
					<span v-if="req.conRepaymentRateS != null"><strong> {{ $t('cus.repaymentRate') }}</strong> : {{ $filters.formatPct(req.conRepaymentRateS) }}%~{{ $filters.formatPct(req.conRepaymentRateE) }}%
					</span>
				</span>
				<span
					class="img-delete JQ-delet"
					title=""
					:data-bs-original-title="$t('cus.delete')"
					@click.prevent="deleteLoanDisbursement"
				/>
			</ColoredLink>
		</li>
		<li
			v-show="
				req.approveAmountS != null ||
					req.approveAmountE != null ||
					req.interestRateS != null ||
					req.interestRateE != null ||
					cond.status != null ||
					req.userAmountS != null ||
					req.userAmountE != null ||
					req.availableAmountS != null ||
					req.availableAmountE != null
			"
			class="list-group-item"
		>
			<ColoredLink href="#">
				<span>
					<strong>{{ $t('cus.personalLoanLimit') }} : </strong>
					<span v-if="req.approveAmountS != null"><strong>{{ $t('cus.approvedLimit') }}</strong> : {{ $filters.formatNumber(req.approveAmountS) }}~{{ $filters.formatNumber(req.approveAmountE) }}
					</span>
					<span v-if="req.interestRateS != null"><strong> {{ $t('cus.interestRate') }}</strong> : {{ $filters.formatPct(req.interestRateS) }}%~{{ $filters.formatPct(req.interestRateE) }}%
					</span>
					<span v-if="cond.status != null"><strong> {{ $t('cus.limitStatus') }}</strong> : {{ cond.status }} </span>
					<span v-if="req.userAmountS != null"><strong> {{ $t('cus.usedLimit') }}</strong> : {{ $filters.formatNumber(req.userAmountS) }}~{{ $filters.formatNumber(req.userAmountE) }}
					</span>
					<span v-if="req.availableAmountS != null"><strong> {{ $t('cus.availableLimit') }}</strong> : {{ $filters.formatNumber(req.availableAmountS) }}~{{
						$filters.formatNumber(req.availableAmountE)
					}}
					</span>
				</span>
				<span
					class="img-delete JQ-delet"
					title=""
					:data-bs-original-title="$t('cus.delete')"
					@click.prevent="deleteLoanLimit"
				/>
			</ColoredLink>
		</li>
		<template v-for="selEx in selExDataItem">
			<li v-if="form.queList[selEx.quesectionId] != null && form.queList[selEx.quesectionId].length > 0" class="list-group-item">
				<ColoredLink href="#">
					<span>
						<strong>{{ selEx.quesectionName }} : </strong>
						<template v-for="que in selEx.queitem">
							<span v-if="que.answer != null"><strong>{{ ' ' + que.queitemName }}</strong> : {{ que.answer.queitemselName }}
							</span>
						</template>
					</span>
					<span
						class="img-delete JQ-delet"
						title=""
						:data-bs-original-title="$t('cus.delete')"
						@click.prevent="deleteSelEx(selEx)"
					/>
				</ColoredLink>
			</li>
		</template>
	</ul>
	<div class="col-lg-8">
		<div class="card card-form card-collapse mb-3">
			<div class="card-header">
				<h4>{{ $t('cus.comprehensiveSearch') }}</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1" />
			</div>
			<div id="collapseListGroup1" class="collapse show">
				<div class="card-body">
					<v-form ref="form">
						<div class="row g-3 align-items-end">
							<div class="col-md-4">
								<label class="form-label">{{ $t('cus.customerAssetLevel') }}</label>
								<div class="input-group">
									<select v-model="form.gra" class="form-select">
										<option :value="null">
											{{ $t('cus.pleaseSelect') }}
										</option>
										<option v-for="item in selGrades" :value="item">
											{{ item.graName }}
										</option>
									</select>
									<ColoredLink data-bs-toggle="tooltip" href="#" class="table-icon">
										<Button
											type="button"
											class="btn btn-info btn-icon JQ-singleSearch"
											:title="$t('cus.add')"
											@click="addGra"
										>
											<i class="fa-solid fa-plus" />
										</Button>
									</ColoredLink>
								</div>
							</div>
							<div class="col-md-8">
								<UserCondition
									:area-label="$t('cus.organization')"
									:bran-label="''"
									:user-label="''"
									:is-required="'area'"
									@change-area="(val) => (form.area = val)"
									@change-bran="(val) => (form.bran = val)"
									@change-user="(val) => (form.user = val)"
									@change-area-name="(val) => (form.areaName = val)"
									@change-bran-name="(val) => (form.branName = val || null)"
									@change-user-name="(val) => (form.userName = val || null)"
									@loaded="onUserConditionLoaded"
								>
									<template #append>
										<Button
											icon
											color="info"
											:title="$t('cus.add')"
											@click="addBran"
										>
											<i class="fa-solid fa-plus" />
										</Button>
									</template>
								</UserCondition>
							</div>
							<div class="col-md-8">
								<label class="form-label"> {{ $t('cus.customerBirthday') }}</label>
								<div class="input-group mb-2">
									<select v-model="form.birthMS" class="form-select">
										<option :value="null" size="15">
											{{ $t('cus.pleaseSelect') }}
										</option>
										<option v-for="item in selMonth" :value="item.codeValue">
											{{ item.codeName }}
										</option>
									</select>
									<select v-model="form.birthDS" name="cusSearchBirth.beginDd" class="form-select">
										<option :value="null" size="15">
											{{ $t('cus.pleaseSelect') }}
										</option>
										<option v-for="item in selDay" :value="item.codeValue">
											{{ item.codeName }}
										</option>
									</select>
									<span class="input-group-text">~</span>
									<select v-model="form.birthME" class="form-select">
										<option :value="null" size="15">
											{{ $t('cus.pleaseSelect') }}
										</option>
										<option v-for="item in selMonth" :value="item.codeValue">
											{{ item.codeName }}
										</option>
									</select>
									<select v-model="form.birthDE" class="form-select">
										<option :value="null" size="15">
											{{ $t('cus.pleaseSelect') }}
										</option>
										<option v-for="item in selDay" :value="item.codeValue">
											{{ item.codeName }}
										</option>
									</select>
									<ColoredLink data-bs-toggle="tooltip" href="#" class="table-icon">
										<Button
											type="button"
											class="btn btn-info btn-icon JQ-singleSearch"
											:title="$t('cus.add')"
											@click="addBirth"
										>
											<i class="fa-solid fa-plus" />
										</Button>
									</ColoredLink>
								</div>
							</div>
							<div class="col-md-12">
								<div class="tab-nav-tabs">
									<ul class="nav nav-tabs nav-justified" role="tablist">
										<li class="nav-item">
											<ColoredLink data-bs-toggle="tab" href="#tab1" class="nav-link active">
												{{ $t('cus.assetStatus') }}
											</ColoredLink>
										</li>
										<li class="nav-item">
											<ColoredLink data-bs-toggle="tab" href="#tab2" class="nav-link">
												{{ $t('cus.personalLoan') }}
											</ColoredLink>
										</li>
										<li class="nav-item">
											<ColoredLink data-bs-toggle="tab" href="#tab3" class="nav-link">
												{{ $t('cus.otherData') }}
											</ColoredLink>
										</li>
									</ul>
									<div class="tab-content">
										<div id="tab1" class="tab-pane fade show active">
											<div class="row g-2">
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>{{ $t('cus.investmentProfile') }}:</b></label><br>
													<div class="row justify-content-between align-items-end">
														<div class="col-10">
															<div v-for="(item, i) in selRank" class="form-check form-check-inline">
																<input
																	:id="'rankCode' + i"
																	v-model="form.rank"
																	class="form-check-input"
																	type="radio"
																	:value="item"
																>
																<label class="form-check-label" :for="'rankCode' + i">{{ item.codeName }}</label>
															</div>
														</div>
														<div class="col-2 text-end">
															<ColoredLink data-bs-toggle="tooltip" href="#" class="table-icon">
																<Button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	:title="$t('cus.add')"
																	@click="addRank"
																>
																	<i class="fa-solid fa-plus" />
																</Button>
															</ColoredLink>
														</div>
													</div>
												</div>
												<div class="divider" />
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>{{ $t('cus.bankTotalAssets') }}:</b></label><br>
													<div class="row justify-content-between">
														<div class="col-10">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.between') }}</span>
																<input
																	v-model="form.aumAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('aumAmountS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.aumAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('aumAmountE', $event)"
																>
																<span class="input-group-text">{{ $t('cus.yuan') }}</span>
															</div>
														</div>
														<div class="col-2 text-end">
															<ColoredLink data-bs-toggle="tooltip" href="#" class="table-icon">
																<Button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	:title="$t('cus.add')"
																	@click="addAum"
																>
																	<i class="fa-solid fa-plus" />
																</Button>
															</ColoredLink>
														</div>
													</div>
												</div>
												<div class="divider" />
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>{{ $t('cus.bankContribution') }}:</b></label><br>
													<div class="row">
														<div class="col-md-10">
															<div class="input-group mb-2">
																<span class="input-group-text">{{ $t('cus.contributionBetween') }}</span>
																<input
																	v-model="form.contbAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('contbAmountS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.contbAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('contbAmountE', $event)"
																>
																<span class="input-group-text">{{ $t('cus.yuan') }}</span>
															</div>
														</div>
														<div class="col-10">
															<label class="form-label"> {{ $t('cus.timeInterval') }} </label>
															<div v-for="(item, i) in selContbTime" class="form-check form-check-inline">
																<input
																	:id="'contbTime_' + i"
																	v-model="form.contbTime"
																	class="form-check-input"
																	type="radio"
																	:value="item"
																>
																<label class="form-check-label" :for="'contbTime_' + i">{{ item.codeName }}</label>
															</div>
															<div v-show="form.contbTime?.codeValue === 'DEF'" class="input-group">
																<input v-model="form.contbTimeS" class="JQ-datepicker form-control" type="date">
																<span class="input-group-text">~</span>
																<input v-model="form.contbTimeE" class="JQ-datepicker form-control" type="date">
															</div>
														</div>
														<div class="col-2 text-end">
															<ColoredLink data-bs-toggle="tooltip" href="#" class="table-icon">
																<Button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	:title="$t('cus.add')"
																	@click="addContb"
																>
																	<i class="fa-solid fa-plus" />
																</Button>
															</ColoredLink>
														</div>
													</div>
												</div>
												<div class="divider" />
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>{{ $t('cus.savingsProducts') }}:</b></label><br>
													<div class="row align-items-end">
														<div class="col-md-10">
															<div class="input-group">
																<div class="input-group-text">
																	{{ $t('cus.savingsType') }}
																</div>
																<select v-model="form.savingType" class="form-select">
																	<option :value="null">
																		{{ $t('cus.pleaseSelect') }}
																	</option>
																	<option v-for="item in selSavingProtype" :value="item">
																		{{ item.codeName }}
																	</option>
																</select>
																<div class="input-group-text">
																	{{ $t('cus.currency') }}
																</div>
																<select v-model="form.savingC" class="form-select">
																	<option :value="null">
																		{{ $t('cus.pleaseSelect') }}
																	</option>
																	<option v-for="item in selCurencies" :value="item">
																		{{ item.curName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-10">
															<div class="input-group mt-2">
																<span class="input-group-text">{{ $t('cus.accountBalance') }} {{ $t('cus.between') }}</span>
																<input
																	v-model="form.savingAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('savingAmountS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.savingAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('savingAmountE', $event)"
																>
																<span class="input-group-text">{{ $t('cus.yuan') }}</span>
															</div>
														</div>
														<div class="col-2 text-end">
															<ColoredLink data-bs-toggle="tooltip" href="#" class="table-icon">
																<Button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	:title="$t('cus.add')"
																	@click="addSaving"
																>
																	<i class="fa-solid fa-plus" />
																</Button>
															</ColoredLink>
														</div>
													</div>
												</div>
												<div class="divider" />
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>{{ $t('cus.investmentProducts') }}:</b></label><br>
													<div v-for="(item, i) in selProCat" class="form-check form-check-inline">
														<input
															:id="'proCatList' + i"
															v-model="form.proCatList"
															class="form-check-input"
															type="checkbox"
															:value="item"
														>
														<label class="form-check-label" :for="'proCatList' + i">{{ item.pfcatName }}</label>
													</div>
													<div class="d-inline-flex" style="word-break: keep-all; align-items: center">
														<div class="input-group ms-1 wd-300">
															<span class="input-group-text">{{ $t('cus.productCode') }}</span>
															<input
																v-model="form.proCode"
																class="form-control"
																type="text"
																value=""
															>
															<ColoredLink class="btn btn-info" href="#" @click.prevent="openProModal">
																{{ $t('cus.queryCode') }}
															</ColoredLink>
														</div>
													</div>
													<div class="row align-items-center">
														<div class="col-auto">
															<div class="input-group">
																<div class="input-group-text">
																	{{ $t('cus.currency') }}
																</div>
																<select v-model="form.proC" class="form-select">
																	<option :value="null">
																		{{ $t('cus.pleaseSelect') }}
																	</option>
																	<option v-for="item in selCurencies" :value="item">
																		{{ item.curName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-auto">
															{{ $t('cus.dividendStatus') }}
															<div v-for="(item, i) in selInvproInttype" class="form-check form-check-inline">
																<input
																	:id="'intType' + i"
																	v-model="form.intType"
																	class="form-check-input"
																	type="radio"
																	:value="item"
																>
																<label class="form-check-label" :for="'intType' + i">{{ item.codeName }}</label>
															</div>
														</div>
													</div>
													<div class="row align-items-end">
														<div class="col-md-10">
															<div class="input-group mt-2">
																<span class="input-group-text">{{ $t('cus.marketValue') }}</span>
																<input
																	v-model="form.mktAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('mktAmountS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.mktAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('mktAmountE', $event)"
																>
																<span class="input-group-text">{{ $t('cus.yuan') }}</span>
															</div>
															<div class="input-group mt-2">
																<span class="input-group-text">{{ $t('cus.investmentPrincipal') }}</span>
																<input
																	v-model="form.invAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('invAmountS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.invAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('invAmountE', $event)"
																>
																<span class="input-group-text">{{ $t('cus.yuan') }}</span>
															</div>
														</div>
														<div class="col-10">
															<div class="input-group mt-2">
																<span class="input-group-text">{{ $t('cus.returnRate') }}</span>
																<input
																	v-model="form.returnAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.returnAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																>
																<span class="input-group-text">% ({{ $t('cus.unrealizedPLWithInterest') }})</span>
															</div>
														</div>
														<div class="col-2 text-end">
															<ColoredLink data-bs-toggle="tooltip" href="#" class="table-icon">
																<Button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	:title="$t('cus.add')"
																	@click="addPro"
																>
																	<i class="fa-solid fa-plus" />
																</Button>
															</ColoredLink>
														</div>
													</div>
												</div>
												<div class="divider" />
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>{{ $t('cus.fundProducts') }}:</b></label><br>
													<div class="row g-2">
														<div class="col-md-4">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.subscriptionType') }}</span>
																<select v-model="form.invType" class="form-select">
																	<option :value="null">
																		--
																	</option>
																	<option v-for="item in selFundInvtype" :value="item">
																		{{ item.codeName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-md-4">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.debitType') }}</span>
																<select v-model="form.debitType" class="form-select">
																	<option :value="null">
																		--
																	</option>
																	<option v-for="item in selDeductCode" :value="item">
																		{{ item.codeName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-md-4">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.currency') }}</span>
																<select v-model="form.fundC" class="form-select">
																	<option :value="null">
																		--
																	</option>
																	<option v-for="item in selCurencies" :value="item">
																		{{ item.curName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-md-4">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.efficiencyInvestment') }}</span>
																<select v-model="form.efficiencyInv" class="form-select">
																	<option :value="null">
																		--
																	</option>
																	<option v-for="item in selSmartStock" :value="item">
																		{{ item.codeName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-10 col-md-4">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.fundType') }}</span>
																<select v-model="form.fundType" class="form-select">
																	<option :value="null">
																		--
																	</option>
																	<option v-for="item in selFundProType" :value="item">
																		{{ item.proTypeName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-2 col-md-4 text-end">
															<ColoredLink data-bs-toggle="tooltip" href="#" class="table-icon">
																<Button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	:title="$t('cus.add')"
																	@click="addFundPro"
																>
																	<i class="fa-solid fa-plus" />
																</Button>
															</ColoredLink>
														</div>
													</div>
												</div>
												<div class="divider" />
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>{{ $t('cus.lifeInsuranceProducts') }}:</b></label>
													<div class="row g-2">
														<div class="col-md-8">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.insuranceType') }}</span>
																<select v-model="form.insType" class="form-select">
																	<option :value="null">
																		{{ $t('cus.pleaseSelect') }}
																	</option>
																	<option v-for="item in selInsProType" :value="item">
																		{{ item.proTypeName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-md-4">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.currency') }}</span>
																<select v-model="form.insC" class="form-select">
																	<option :value="null">
																		{{ $t('cus.pleaseSelect') }}
																	</option>
																	<option v-for="item in selCurencies" :value="item">
																		{{ item.curName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-10">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.accumulatedPremiumPaid') }}</span>
																<input
																	v-model="form.insAAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('insAAmountS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.insAAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	value=""
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('insAAmountE', $event)"
																>
																<span class="input-group-text">{{ $t('cus.yuan') }}</span>
															</div>
														</div>
														<div class="col-2 text-end">
															<ColoredLink data-bs-toggle="tooltip" href="#" class="table-icon">
																<Button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	:title="$t('cus.add')"
																	@click="addIns"
																>
																	<i class="fa-solid fa-plus" />
																</Button>
															</ColoredLink>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div id="tab2" class="tab-pane fade">
											<div class="row g-2">
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>{{ $t('cus.personalLoanDisbursement') }}:</b></label><br>
													<div class="row g-2">
														<div class="col-md-5">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.loanProduct') }}</span>
																<select v-model="form.loanProCode" class="form-select">
																	<option :value="null">
																		{{ $t('cus.pleaseSelect') }}
																	</option>
																	<option v-for="item in selLoanProType" :value="item">
																		{{ item.proTypeName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-md-5">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.loanRemark') }}</span>
																<select v-model="form.loanRemark" class="form-select">
																	<option :value="null">
																		--
																	</option>
																	<option v-for="item in selLoanStatus" :value="item">
																		{{ item.codeName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.initialLoanDate') }}</span>
																<input
																	v-model="form.loanStdDateS"
																	class="JQ-datepicker form-control"
																	type="date"
																	size="13"
																	maxlength="10"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.loanStdDateE"
																	class="JQ-datepicker form-control"
																	type="date"
																	size="13"
																	maxlength="10"
																>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.loanPeriod') }}</span>
																<input
																	v-model="form.loanPeriodS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="5"
																	maxlength="3"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('loanPeriodS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.loanPeriodE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="5"
																	maxlength="3"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('loanPeriodE', $event)"
																>
																<span class="input-group-text">{{ $t('cus.periods') }}</span>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.disbursementAmount') }}</span>
																<input
																	v-model="form.appropriationS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('appropriationS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.appropriationE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('appropriationE', $event)"
																>
																<span class="input-group-text">{{ $t('cus.yuan') }}</span>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text"> {{ $t('cus.loanBalance') }}</span>
																<input
																	v-model="form.loanOverAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('loanOverAmountS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.loanOverAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('loanOverAmountE', $event)"
																>
																<span class="input-group-text">{{ $t('cus.yuan') }}</span>
															</div>
														</div>
														<div class="divider" />
														<label class="form-label tx-primary">{{ $t('cus.loanConditionQuery') }}:</label>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.disbursementAmountShort') }}</span>
																<input
																	v-model="form.conAppropriationS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conAppropriationS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.conAppropriationE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conAppropriationE', $event)"
																>
																<span class="input-group-text">{{ $t('cus.yuan') }}</span>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text"> {{ $t('cus.contractRate') }}</span>
																<input
																	v-model="form.conRateS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conRateS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.conRateE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conRateE', $event)"
																>
																<span class="input-group-text">%</span>
															</div>
														</div>
														<div class="col-md-auto">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.repaymentMethod') }}</span>
																<select v-model="form.conReturnType" class="form-select">
																	<option :value="null">
																		--
																	</option>
																	<option v-for="item in selRepayCode" :value="item">
																		{{ item.codeName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.loanBalanceShort') }}</span>
																<input
																	v-model="form.conOverAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conOverAmountS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.conOverAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conOverAmountE', $event)"
																>
																<span class="input-group-text">{{ $t('cus.yuan') }}</span>
															</div>
														</div>
														<div class="col-10">
															<div class="input-group">
																<span class="input-group-text"> {{ $t('cus.repaymentRate') }}</span>
																<input
																	v-model="form.conRepaymentRateS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conRepaymentRateS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.conRepaymentRateE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('conRepaymentRateE', $event)"
																>
																<span class="input-group-text">%</span>
															</div>
														</div>
														<div class="col-2 text-end">
															<ColoredLink data-bs-toggle="tooltip" href="#" class="table-icon">
																<Button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	:title="$t('cus.add')"
																	@click="addLoanDisbursement"
																>
																	<i class="fa-solid fa-plus" />
																</Button>
															</ColoredLink>
														</div>
													</div>
												</div>
												<div class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>{{ $t('cus.personalLoanLimit') }}:</b></label><br>
													<div class="row g-2">
														<label class="form-label tx-primary">{{ $t('cus.limitConditionQuery') }}:</label>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.approvedLimit') }}</span>
																<input
																	v-model="form.approveAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('approveAmountS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.approveAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('approveAmountE', $event)"
																>
																<span class="input-group-text">{{ $t('cus.yuan') }}</span>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text"> {{ $t('cus.interestRate') }}</span>
																<input
																	v-model="form.interestRateS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('interestRateS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.interestRateE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('interestRateE', $event)"
																>
																<span class="input-group-text">%</span>
															</div>
														</div>
														<div class="col-md-auto">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.limitStatus') }}</span>
																<select v-model="form.status" class="form-select">
																	<option :value="null">
																		--
																	</option>
																	<option v-for="item in selQuotaStatus" :value="item">
																		{{ item.codeName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-md-10">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.usedLimit') }}</span>
																<input
																	v-model="form.userAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('userAmountS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.userAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('userAmountE', $event)"
																>
																<span class="input-group-text">{{ $t('cus.yuan') }}</span>
															</div>
														</div>
														<div class="col-10">
															<div class="input-group">
																<span class="input-group-text"> {{ $t('cus.availableLimit') }}</span>
																<input
																	v-model="form.availableAmountS"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('availableAmountS', $event)"
																>
																<span class="input-group-text">~</span>
																<input
																	v-model="form.availableAmountE"
																	class="form-control text-end JQ-float"
																	type="number"
																	size="20"
																	@keydown="onNumberKeydown"
																	@input="onNumberInput('availableAmountE', $event)"
																>
																<span class="input-group-text">{{ $t('cus.yuan') }}</span>
															</div>
														</div>
														<div class="col-2 text-end">
															<ColoredLink data-bs-toggle="tooltip" href="#" class="table-icon">
																<Button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	:title="$t('cus.add')"
																	@click="addLoanLimit"
																>
																	<i class="fa-solid fa-plus" />
																</Button>
															</ColoredLink>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div id="tab3" class="tab-pane fade">
											<div class="row g-2">
												<div v-for="selEx in selExDataItem" class="col-md-12 bg-ligntblue p-3">
													<label class="form-label"><b>{{ selEx.quesectionName }}：</b></label><br>
													<div class="row g-2">
														<div v-for="que in selEx.queitem" class="col-md-10">
															<div class="input-group">
																<span class="input-group-text">{{ que.queitemName }}</span>
																<select v-model="que.answer" class="form-select">
																	<option :value="undefined">
																		{{ $t('cus.pleaseSelect') }}
																	</option>
																	<option v-for="sel in que.itemsel" :value="sel">
																		{{ sel.queitemselName }}
																	</option>
																</select>
															</div>
														</div>
														<div class="col-2 text-end">
															<ColoredLink data-bs-toggle="tooltip" href="#" class="table-icon">
																<Button
																	type="button"
																	class="btn btn-info btn-icon JQ-singleSearch"
																	:title="$t('cus.add')"
																	@click="addSelEx(selEx)"
																>
																	<i class="fa-solid fa-plus" />
																</Button>
															</ColoredLink>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="text-end mt-3">
									<input
										name="Submit1"
										type="button"
										class="btn btn-primary btn-lg JQ-singleSearch"
										:value="$t('cus.customerSearch')"
										@click="search"
									>
								</div>
							</div>
						</div>
					</v-form>
				</div>
			</div>
		</div>
	</div>
	<vue-pro-quick-search ref="proModal" :set-pro-code="setProCode" />
	<vue-pro-quick-search ref="fundModal" :set-pro-code="setFundProCode" />
</template>
<script>
import vueProQuickSearch from './proQuickSearch.vue';
import { Form } from 'vee-validate';
import _ from 'lodash';
export default {
	components: {
		vueProQuickSearch,
		'v-form': Form
	},
	props: {
		parentSearch: Function
	},
	data() {
		return {
			req: {
				graCode: null,
				area: null,
				branCode: null,
				userCode: null,
				birthStart: null,
				birthEnd: null,
				rankCode: null,
				aumAmountS: null,
				aumAmountE: null,
				contbAmountS: null,
				contbAmountE: null,
				contbTime: null,
				contbTimeS: null,
				contbTimeE: null,
				savingType: [],
				savingC: null,
				savingAmountS: null,
				savingAmountE: null,
				proCatList: [],
				proCode: null,
				proC: null,
				intType: null,
				mktAmountS: null,
				mktAmountE: null,
				invAmountS: null,
				invAmountE: null,
				returnAmountS: null,
				returnAmountE: null,
				invType: null,
				debitType: null,
				fundC: null,
				efficiencyInv: null,
				fundType: null,
				insType: null,
				insC: null,
				insAAmountS: null,
				insAAmountE: null,

				loanProCode: null,
				loanRemark: null,
				loanStdDateS: null,
				loanStdDateE: null,
				loanPeriodS: null,
				loanPeriodE: null,
				appropriationS: null,
				appropriationE: null,
				loanOverAmountS: null,
				loanOverAmountE: null,
				conAppropriationS: null,
				conAppropriationE: null,
				conRateS: null,
				conRateE: null,
				conReturnType: null,
				conOverAmountS: null,
				conOverAmountE: null,
				conRepaymentRateS: null,
				conRepaymentRateE: null,

				approveAmountS: null,
				approveAmountE: null,
				interestRateS: null,
				interestRateE: null,
				status: null,
				userAmountS: null,
				userAmountE: null,
				availableAmountS: null,
				availableAmountE: null,

				queList: []
			},
			form: {
				gra: null,
				area: null,
				bran: null,
				user: null,
				areaName: null,
				branName: null,
				userName: null,
				birthMS: null,
				birthDS: null,
				birthME: null,
				birthDE: null,
				rank: null,
				aumAmountS: null,
				aumAmountE: null,
				contbAmountS: null,
				contbAmountE: null,
				contbTime: null,
				contbTimeS: null,
				contbTimeE: null,
				savingType: null,
				savingC: null,
				savingAmountS: null,
				savingAmountE: null,
				proCatList: [],
				proC: null,
				intType: null,
				proCode: null,
				mktAmountS: null,
				mktAmountE: null,
				invAmountS: null,
				invAmountE: null,
				returnAmountS: null,
				returnAmountE: null,
				invType: null,
				debitType: null,
				fundC: null,
				efficiencyInv: null,
				fundType: null,
				insType: null,
				insC: null,
				insAAmountS: null,
				insAAmountE: null,
				loanProCode: null,
				loanRemark: null,
				loanStdDateS: null,
				loanStdDateE: null,
				loanPeriodS: null,
				loanPeriodE: null,
				appropriationS: null,
				appropriationE: null,
				loanOverAmountS: null,
				loanOverAmountE: null,
				conAppropriationS: null,
				conAppropriationE: null,
				conRateS: null,
				conRateE: null,
				conReturnType: null,
				conOverAmountS: null,
				conOverAmountE: null,
				conRepaymentRateS: null,
				conRepaymentRateE: null,
				approveAmountS: null,
				approveAmountE: null,
				interestRateS: null,
				interestRateE: null,
				status: null,
				userAmountS: null,
				userAmountE: null,
				availableAmountS: null,
				availableAmountE: null,
				queList: {}
			},
			cond: {
				graName: null,
				area: null,
				bran: null,
				user: null,
				birth: null,
				rank: null,
				contbTime: null,
				contbTimeS: null,
				contbTimeE: null,
				savingType: null,
				savingC: null,
				proCatList: null,
				proC: null,
				intType: null,
				loanProCode: null,
				loanRemark: null,
				conReturnType: null,
				status: null
			},
			selGrades: [],
			selAreaList: [],
			selBranList: [],
			selUserList: [],
			selMonth: [],
			selDay: [],
			selRank: [],
			selContbTime: [],
			selCurencies: [],
			selSavingProtype: [],
			selProCat: [],
			selFundInvtype: [],
			selDeductCode: [],
			selSmartStock: [],
			selFundProType: [],
			selInsProType: [],
			selLoanProType: [],
			selLoanStatus: [],
			selRepayCode: [],
			selInvproInttype: [],
			selQuotaStatus: [],

			selExDataItem: [],

			selMultiCusList: []
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo'])
	},
	watch: {
		'form.area'(newVal) {
			this.form.ca = null;

			if (newVal == null) {
				this.selBranList = [];
			}
			else {
				this.getAdmBranListByParentBranCode(newVal.branCode);
			}
		},
		'form.bran'(newVal) {
			this.form.branCode = null;

			if (newVal == null) {
				this.selUserList = [];
			}
			else {
				this.getAdmUserListByBranCode(newVal.branCode);
			}
		}
	},
	mounted: async function () {
		this.getGrades();
		this.getAdmBranchesByRole();
		this.getProCurrenciesList();
		this.getProCat();
		this.getExtDataItem();

		const result = await Promise.all([
			this.getAdmCodeDetail('CUS_BIRTH_MONTH'),
			this.getAdmCodeDetail('CUS_BIRTH_DAY'),
			this.getAdmCodeDetail('CUS_RANK_ITEM'),
			this.getAdmCodeDetail('CUS_CONTB_TIME'),
			this.getAdmCodeDetail('CUS_SAVING_PROTYPE'),
			this.getAdmCodeDetail('CUS_INVPRO_INTTYPE'),
			this.getAdmCodeDetail('CUS_FUND_INVTYPE'),
			this.getProTypes('FUND'),
			this.getProTypes('INS'),

			this.getProTypes('LOAN'),
			this.getAdmCodeDetail('DEDUCT_CODE'),
			this.getAdmCodeDetail('CUS_SMART_STOCK'),
			this.getAdmCodeDetail('LOAN_STATUS'),
			this.getAdmCodeDetail('QUOTA_STATUS'),
			this.getAdmCodeDetail('REPAY_CODE')
		]);
		[
			this.selMonth,
			this.selDay,
			this.selRank,
			this.selContbTime,
			this.selSavingProtype,
			this.selInvproInttype,
			this.selFundInvtype,
			this.selFundProType,
			this.selInsProType,
			this.selLoanProType,
			this.selDeductCode,
			this.selSmartStock,
			this.selLoanStatus,
			this.selQuotaStatus,
			this.selRepayCode
		] = result;
	},
	methods: {
		search() {
			const queList = [];
			_.forEach(this.form.queList, function (answer) {
				_.forEach(answer, function (e) {
					queList.push(e.queitemselId);
				});
			});
			this.req.queList = queList;
			this.parentSearch(this.req);
		},
		async getAdmBranchesByRole() {
			const resp = await this.$api.getAdmBranchesApi({
				branLvlCode: '30',
				removeYn: 'N'
			});
			this.selAreaList = resp.data;
		},
		async getAdmBranListByParentBranCode(parentBranCode) {
			const resp = await this.$api.getAdmBranchesApi({
				parentBranCode: parentBranCode
			});
			this.selBranList = resp.data;
		},
		async getAdmUserListByBranCode(branCode) {
			const resp = await this.$api.getAdmUsersListApi({
				branCode: branCode,
				roleCode: '00'
			});
			this.selUserList = resp.data;
		},
		addGra() {
			if (_.isEmpty(this.form.gra)) {
				this.$bi.alert(this.$t('cus.pleaseSelectCustomerAssetLevel'));
				return;
			}
			this.req.graCode = this.form.gra?.graCode;
			this.cond.graName = this.form.gra?.graName;
		},
		deleteGra() {
			this.req.graCode = null;
			this.cond.graName = null;
		},
		addBran() {
			if (_.isEmpty(this.form.area)) {
				this.$bi.alert(this.$t('cus.pleaseSelectRegion'));
				return;
			}
			this.req.area = this.form.area;
			this.req.branCode = this.form.bran;
			this.req.userCode = this.form.user;
			this.cond.area = this.form.areaName;
			this.cond.bran = this.form.branName;
			this.cond.user = this.form.userName;
		},
		deleteBran() {
			this.req.area = null;
			this.req.branCode = null;
			this.req.userCode = null;
			this.cond.area = null;
			this.cond.bran = null;
			this.cond.user = null;
		},
		addBirth() {
			if (this.form.birthMS == null || this.form.birthDS == null || this.form.birthME == null || this.form.birthDE == null) {
				this.$bi.alert(this.$t('cus.rangeCannotBeEmpty'));
			}
			else if (!this.checkDay(this.form.birthMS, this.form.birthDS)) {
				this.$bi.alert(this.$t('cus.startDateInvalid'));
			}
			else if (!this.checkDay(this.form.birthME, this.form.birthDE)) {
				this.$bi.alert(this.$t('cus.endDateInvalid'));
			}
			else {
				const birthStart = this.form.birthMS.padStart(2, '0') + this.form.birthDS.padStart(2, '0');
				const birthEnd = this.form.birthME.padStart(2, '0') + this.form.birthDE.padStart(2, '0');

				if (birthStart > birthEnd) {
					this.$bi.alert(this.$t('cus.startDateCannotBeLessThanEndDate'));
				}
				else {
					this.req.birthStart = birthStart;
					this.req.birthEnd = birthEnd;
					this.cond.birth = this.form.birthMS + this.$t('cus.month') + this.form.birthDS + this.$t('cus.day') + '~' + this.form.birthME + this.$t('cus.month') + this.form.birthDE + this.$t('cus.day');
				}
			}
		},
		deleteBirth() {
			this.req.birthStart = null;
			this.req.birthEnd = null;
			this.cond.birth = null;
		},
		addRank() {
			if (_.isEmpty(this.form.rank)) {
				this.$bi.alert(this.$t('cus.pleaseSelectInvestmentProfile'));
				return;
			}
			this.req.rankCode = this.form.rank?.codeValue;
			this.cond.rank = this.form.rank?.codeName;
		},
		deleteRank() {
			this.req.rankCode = null;
			this.cond.rank = null;
		},
		addAum: async function () {
			await this.checkRangeRequire(this.form.aumAmountS, this.form.aumAmountE, 'AUM');

			this.req.aumAmountS = this.form.aumAmountS;
			this.req.aumAmountE = this.form.aumAmountE;
		},
		deleteAum() {
			this.req.aumAmountS = null;
			this.req.aumAmountE = null;
		},
		addContb: async function () {
			await this.checkRangeRequire(this.form.contbAmountS, this.form.contbAmountE, this.$t('cus.contribution'));

			if (_.isEmpty(this.form.contbTime)) {
				this.$bi.alert(this.$t('cus.pleaseSelectTimeInterval'));
				return;
			}
			if (this.form.contbTime?.codeValue === 'DEF') {
				await this.checkDateRangeRequire(this.form.contbTimeS, this.form.contbTimeE, this.$t('cus.time'));
				this.req.contbTimeS = this.form.contbTimeS;
				this.req.contbTimeE = this.form.contbTimeE;
				this.cond.contbTimeS = this.form.contbTimeS;
				this.cond.contbTimeE = this.form.contbTimeE;
			}
			else {
				this.req.contbTimeS = null;
				this.req.contbTimeE = null;
				this.cond.contbTimeS = null;
				this.cond.contbTimeE = null;
			}
			this.req.contbAmountS = this.form.contbAmountS;
			this.req.contbAmountE = this.form.contbAmountE;
			this.req.contbTime = this.form.contbTime?.codeValue;
			this.cond.contbTime = this.form.contbTime?.codeName;
		},
		deleteContb() {
			this.req.contbTime = null;
			this.req.contbAmountS = null;
			this.req.contbAmountE = null;
			this.req.contbTimeS = null;
			this.req.contbTimeE = null;
			this.cond.contbTime = null;
		},
		addSaving: async function () {
			await this.checkRangeRequire(this.form.savingAmountS, this.form.savingAmountE, this.$t('cus.accountBalance'));

			if (this.form.savingType != null) {
				this.req.savingType = this.form.savingType?.codeValue.split(',');
				this.cond.savingType = this.form.savingType?.codeName;
			}
			else {
				this.req.savingType = [];
				this.cond.savingType = null;
			}
			this.req.savingC = this.form.savingC?.curCode ?? null;
			this.req.savingAmountS = this.form.savingAmountS;
			this.req.savingAmountE = this.form.savingAmountE;
			this.cond.savingC = this.form.savingC?.curName ?? null;
		},
		deleteSaving() {
			this.req.savingType = [];
			this.req.savingC = null;
			this.req.savingAmountS = null;
			this.req.savingAmountE = null;
			this.cond.savingType = null;
			this.cond.savingC = null;
		},
		addPro: async function () {
			if (_.isEmpty(this.form.proCatList) && _.isBlank(this.form.proCode)) {
				this.$bi.alert(this.$t('cus.pleaseSelectInvestmentProductOrEnterProductCode'));
				return;
			}
			await this.checkRangeNoRequire(this.form.mktAmountS, this.form.mktAmountE, this.$t('cus.marketValue'));
			await this.checkRangeNoRequire(this.form.invAmountS, this.form.invAmountE, this.$t('cus.investmentPrincipal'));
			await this.checkRangeNoRequire(this.form.returnAmountS, this.form.returnAmountE, this.$t('cus.returnRate'));

			const proCatList = [];
			const proCatName = [];
			_.forEach(this.form.proCatList, function (e) {
				proCatList.push(e.pfcatCode);
				proCatName.push(e.pfcatName);
			});
			this.req.proCatList = proCatList;
			this.req.proCode = this.form.proCode;
			this.req.proC = this.form.proC?.curCode ?? null;
			this.req.intType = this.form.intType?.codeValue;
			this.req.mktAmountS = this.form.mktAmountS;
			this.req.mktAmountE = this.form.mktAmountE;
			this.req.invAmountS = this.form.invAmountS;
			this.req.invAmountE = this.form.invAmountE;
			this.req.returnAmountS = this.form.returnAmountS != null ? this.form.returnAmountS / 100 : null;
			this.req.returnAmountE = this.form.returnAmountE != null ? this.form.returnAmountE / 100 : null;

			this.cond.proCatList = proCatName.join(', ') || null;
			this.cond.proC = this.form.proC?.curName ?? null;
			this.cond.intType = this.form.intType?.codeName ?? null;
		},
		deletePro() {
			this.req.proCatList = [];
			this.req.proCode = null;
			this.req.proC = null;
			this.req.intType = null;
			this.req.mktAmountS = null;
			this.req.mktAmountE = null;
			this.req.invAmountS = null;
			this.req.invAmountE = null;
			this.req.returnAmountS = null;
			this.req.returnAmountE = null;
			this.cond.proCatList = null;
			this.cond.proC = null;
			this.cond.intType = null;
		},
		addFundPro() {
			if (
				_.isEmpty(this.form.invType)
				&& _.isEmpty(this.form.debitType)
				&& _.isEmpty(this.form.fundC)
				&& _.isEmpty(this.form.efficiencyInv)
				&& _.isEmpty(this.form.fundType)
			) {
				this.$bi.alert(this.$t('cus.pleaseSelectAtLeastOneCondition'));
				return;
			}
			this.req.invType = this.form.invType?.codeValue ?? null;
			this.req.debitType = this.form.debitType?.codeValue ?? null;
			this.req.fundC = this.form.fundC?.curCode ?? null;
			this.req.efficiencyInv = this.form.efficiencyInv?.codeValue ?? null;
			this.req.fundType = this.form.fundType?.proTypeCode ?? null;

			this.cond.invType = this.form.invType?.codeName ?? null;
			this.cond.debitType = this.form.debitType?.codeName ?? null;
			this.cond.fundC = this.form.fundC?.curName ?? null;
			this.cond.efficiencyInv = this.form.efficiencyInv?.codeName ?? null;
			this.cond.fundType = this.form.fundType?.proTypeName ?? null;
		},
		deleteFundPro() {
			this.req.invType = null;
			this.req.debitType = null;
			this.req.fundC = null;
			this.req.efficiencyInv = null;
			this.req.fundType = null;
			this.cond.invType = null;
			this.cond.debitType = null;
			this.cond.fundC = null;
			this.cond.efficiencyInv = null;
			this.cond.fundType = null;
		},
		addIns: async function () {
			await this.checkRangeRequire(this.form.insAAmountS, this.form.insAAmountE, this.$t('cus.accumulatedPremiumPaid'));

			this.req.insAAmountS = this.form.insAAmountS;
			this.req.insAAmountE = this.form.insAAmountE;
			this.req.insType = this.form.insType?.proTypeCode ?? null;
			this.req.insC = this.form.insC?.curCode ?? null;
			this.cond.insType = this.form.insType?.proTypeName ?? null;
			this.cond.insC = this.form.insC?.curName ?? null;
		},
		deleteIns() {
			this.req.insAAmountS = null;
			this.req.insAAmountE = null;
			this.req.insType = null;
			this.req.insC = null;
			this.cond.insType = null;
			this.cond.insC = null;
		},
		addLoanDisbursement: async function () {
			if (_.isEmpty(this.form.loanProCode) && _.isEmpty(this.form.loanRemark)) {
				this.$bi.alert(this.$t('cus.pleaseSelectLoanProductOrLoanRemark'));
				return;
			}
			await this.checkDateRangeNoRequire(this.form.loanStdDateS, this.form.loanStdDateE, this.$t('cus.initialLoanDate'));
			await this.checkRangeNoRequire(this.form.loanPeriodS, this.form.loanPeriodE, this.$t('cus.loanPeriod'));
			await this.checkRangeNoRequire(this.form.appropriationS, this.form.appropriationE, this.$t('cus.disbursementAmount'));
			await this.checkRangeNoRequire(this.form.loanOverAmountS, this.form.loanOverAmountE, this.$t('cus.loanBalance'));
			await this.checkRangeNoRequire(this.form.conAppropriationS, this.form.conAppropriationE, this.$t('cus.disbursementAmount'));
			await this.checkRangeNoRequire(this.form.conRateS, this.form.conRateE, this.$t('cus.contractRate'));
			await this.checkRangeNoRequire(this.form.conOverAmountS, this.form.conOverAmountE, this.$t('cus.loanBalance'));
			await this.checkRangeNoRequire(this.form.conRepaymentRateS, this.form.conRepaymentRateE, this.$t('cus.repaymentRate'));

			this.req.loanProCode = this.form.loanProCode?.proTypeCode ?? null;
			this.req.loanRemark = this.form.loanRemark?.codeValue ?? null;
			this.req.loanStdDateS = this.form.loanStdDateS;
			this.req.loanStdDateE = this.form.loanStdDateE;
			this.req.loanPeriodS = this.form.loanPeriodS;
			this.req.loanPeriodE = this.form.loanPeriodE;
			this.req.appropriationS = this.form.appropriationS;
			this.req.appropriationE = this.form.appropriationE;
			this.req.loanOverAmountS = this.form.loanOverAmountS;
			this.req.loanOverAmountE = this.form.loanOverAmountE;
			this.req.conAppropriationS = this.form.conAppropriationS;
			this.req.conAppropriationE = this.form.conAppropriationE;
			this.req.conRateS = this.form.conRateS != null ? this.form.conRateS / 100 : null;
			this.req.conRateE = this.form.conRateE != null ? this.form.conRateE / 100 : null;
			this.req.conReturnType = this.form.conReturnType?.codeValue ?? null;
			this.req.conOverAmountS = this.form.conOverAmountS;
			this.req.conOverAmountE = this.form.conOverAmountE;
			this.req.conRepaymentRateS = this.form.conRepaymentRateS != null ? this.form.conRepaymentRateS / 100 : null;
			this.req.conRepaymentRateE = this.form.conRepaymentRateE != null ? this.form.conRepaymentRateE / 100 : null;
			this.cond.loanProCode = this.form.loanProCode?.proTypeName ?? null;
			this.cond.loanRemark = this.form.loanRemark?.codeName ?? null;
			this.cond.conReturnType = this.form.conReturnType?.codeName ?? null;
		},
		deleteLoanDisbursement() {
			this.req.loanProCode = null;
			this.req.loanRemark = null;
			this.req.loanStdDateS = null;
			this.req.loanStdDateE = null;
			this.req.loanPeriodS = null;
			this.req.loanPeriodE = null;
			this.req.appropriationS = null;
			this.req.appropriationE = null;
			this.req.loanOverAmountS = null;
			this.req.loanOverAmountE = null;
			this.req.conAppropriationS = null;
			this.req.conAppropriationE = null;
			this.req.conRateS = null;
			this.req.conRateE = null;
			this.req.conReturnType = null;
			this.req.conOverAmountS = null;
			this.req.conOverAmountE = null;
			this.req.conRepaymentRateS = null;
			this.req.conRepaymentRateE = null;
			this.cond.loanProCode = null;
			this.cond.loanRemark = null;
			this.cond.conReturnType = null;
		},

		addLoanLimit: async function () {
			if (_.isEmpty(this.form.status)) {
				this.$bi.alert(this.$t('cus.pleaseSelectLimitStatus'));
				return;
			}
			await this.checkRangeNoRequire(this.form.approveAmountS, this.form.approveAmountE, this.$t('cus.approvedLimit'));
			await this.checkRangeNoRequire(this.form.interestRateS, this.form.interestRateE, this.$t('cus.interestRate'));
			await this.checkRangeNoRequire(this.form.userAmountS, this.form.userAmountE, this.$t('cus.usedLimit'));
			await this.checkRangeNoRequire(this.form.availableAmountS, this.form.availableAmountE, this.$t('cus.availableBalance'));

			this.req.approveAmountS = this.form.approveAmountS;
			this.req.approveAmountE = this.form.approveAmountE;
			this.req.interestRateS = this.form.interestRateS != null ? this.form.interestRateS / 100 : null;
			this.req.interestRateE = this.form.interestRateE != null ? this.form.interestRateE / 100 : null;
			this.req.status = this.form.status?.codeValue;
			this.req.userAmountS = this.form.userAmountS;
			this.req.userAmountE = this.form.userAmountE;
			this.req.availableAmountS = this.form.availableAmountS;
			this.req.availableAmountE = this.form.availableAmountE;
			this.cond.status = this.form.status?.codeName;
		},
		deleteLoanLimit() {
			this.req.approveAmountS = null;
			this.req.approveAmountE = null;
			this.req.interestRateS = null;
			this.req.interestRateE = null;
			this.req.status = null;
			this.req.userAmountS = null;
			this.req.userAmountE = null;
			this.req.availableAmountS = null;
			this.req.availableAmountE = null;
			this.cond.status = null;
		},
		addSelEx(selEx) {
			const answer = [];
			_.forEach(selEx.queitem, function (e) {
				if (e.answer != null) {
					answer.push(e.answer);
				}
			});

			this.form.queList[selEx.quesectionId] = answer;
		},
		deleteSelEx(selEx) {
			this.form.queList[selEx.quesectionId] = [];
		},
		checkDateRangeRequire(start, end, columnName) {
			return new Promise(function (resolve, reject) {
				if (_.isBlank(start) || _.isBlank(end)) {
					this.$bi.alert(this.$t('cus.pleaseEnter') + columnName + this.$t('cus.range'));
				}
				else if (!_.isBlank(start) && !_.isBlank(end) && start > end) {
					this.$bi.alert(columnName + this.$t('cus.startDateCannotBeGreaterThanEndDate'));
				}
				else {
					resolve();
				}
			});
		},
		checkDateRangeNoRequire(start, end, columnName) {
			return new Promise((resolve, reject) => {
				if (_.isBlank(start) && !_.isBlank(end)) {
					this.$bi.alert(this.$t('cus.pleaseEnter') + columnName + this.$t('cus.startDate'));
				}
				else if (!_.isBlank(start) && _.isBlank(end)) {
					this.$bi.alert(this.$t('cus.pleaseEnter') + columnName + this.$t('cus.endDate'));
				}
				else if (!_.isBlank(start) && !_.isBlank(end) && start > end) {
					this.$bi.alert(columnName + this.$t('cus.startDateCannotBeGreaterThanEndDate'));
				}
				else {
					resolve();
				}
			});
		},
		checkRangeRequire(start, end, name) {
			return new Promise((resolve, reject) => {
				if (_.isBlank(start) || _.isBlank(end)) {
					this.$bi.alert(this.$t('cus.pleaseEnter') + name + this.$t('cus.range'));
				}
				else if (_.toNumber(start) > _.toNumber(end)) {
					this.$bi.alert(name + this.$t('cus.minValueCannotBeGreaterThanMaxValue'));
				}
				else {
					resolve();
				}
			});
		},
		checkRangeNoRequire(start, end, columnName) {
			return new Promise((resolve, reject) => {
				if (_.isBlank(start) && !_.isBlank(end)) {
					this.$bi.alert(this.$t('cus.pleaseEnter') + columnName + this.$t('cus.minValue'));
				}
				else if (!_.isBlank(start) && _.isBlank(end)) {
					this.$bi.alert(this.$t('cus.pleaseEnter') + columnName + this.$t('cus.maxValue'));
				}
				else if (!_.isBlank(start) && !_.isBlank(end) && _.toNumber(start) > _.toNumber(end)) {
					this.$bi.alert(columnName + this.$t('cus.minValueCannotBeGreaterThanMaxValue'));
				}
				else {
					resolve();
				}
			});
		},
		openFundModal() {
			this.$refs.fundModal.show();
		},
		setFundProCode(proCode) {
			this.form.fundProCode = proCode;
		},
		openProModal() {
			this.$refs.proModal.show();
		},
		setProCode(proCode) {
			this.form.proCode = proCode;
		},
		async getAdmCodeDetail(codeType) {
			const resp = await this.$api.getAdmCodeDetail({
				codeType: codeType
			});
			return resp.data;
		},
		async getProCurrenciesList() {
			const resp = await this.$api.getProCurrenciesListApi();
			this.selCurencies = resp.data;
		},
		async getProCat() {
			const resp = await this.$api.getCusSaveResultCountApi({
				paramType: 'CUS',
				paramCode: 'INV_PFCAT_CODE'
			});
			const resp2 = await this.$api.getProCatApi({
				pfcatCodes: resp.data[0].paramValue
			});
			this.selProCat = resp2.data;
		},
		async getProTypes(pfcatCode) {
			const resp = await this.$api.getProTypesApi({
				pfcatCode: pfcatCode
			});
			return resp.data;
		},
		async getExtDataItem() {
			const resp = await this.$api.getExtDataItemApi();
			this.selExDataItem = resp.data;
			const queList = {};
			_.forEach(resp.data, function (e) {
				queList[e.quesectionId] = [];
			});
			this.form.queList = queList;
		},
		checkDay(month, day) {
			let result = true;

			switch (month) {
				case '4':
				case '6':
				case '9':
				case '11':
					result = day !== '31';
					break;
				case '2':
					var number = _.toNumber(day);
					result = _.toNumber(day) < 30;
					break;
			}

			return result;
		},
		async getGrades() {
			const resp = await this.$api.getCusGradesApi();
			this.selGrades = resp.data;
		},
		onUserConditionLoaded() {
			if (_.isEqual(this.userInfo.roleType, 'BM') || _.isEqual(this.userInfo.roleType, 'RM') || _.isEqual(this.userInfo.roleType, 'FC')) {
				this.addBran();
			}
		},
		onNumberKeydown(e) {
			if (e.key === '-') {
				e.preventDefault();
			}
		},
		onNumberInput(fieldName, event) {
			const val = event.target.value;
			const num = Number(val);
			if (val.includes('-') || isNaN(num) || num < 0) {
				event.target.value = '';
				this.form[fieldName] = '';
			}
		}
	}
};
</script>
