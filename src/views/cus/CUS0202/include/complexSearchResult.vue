<template>
	<div>
		<div v-show="isShowResult">
			<div class="card card-table">
				<div class="card-header">
					<h4>{{ $t('cus.customerSearchResultList') }}</h4>
					<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
					<button
						type="button"
						class="btn btn-light"
						:title="$t('cus.changeFields')"
						@click="showCustomized()"
					>
						{{ $t('cus.changeFields') }}
					</button>
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-hover text-center">
						<thead>
							<tr v-for="(item, index) in pageData.content.slice(0, 1)" :key="index">
								<th>
									<div class="form-check">
										<input
											id="selectAll"
											v-model="allSelected"
											class="form-check-input"
											type="checkbox"
											@click="selectAll"
										>
										<label class="form-check-label" for="selectAll">{{ $t('cus.selectAll') }}</label>
									</div>
								</th>
								<th>{{ $t('cus.customerIdTaxId') }} <a class="icon-sort" @click="sort('cusCode')" /></th>
								<th>{{ $t('cus.customerName') }}<a class="icon-sort" @click="sort('cusName')" /></th>
								<th>{{ $t('cus.branch') }}<a class="icon-sort" @click="sort('branName')" /></th>
								<th>{{ $t('cus.aoCode') }}<a class="icon-sort" @click="sort('aoCode')" /></th>
								<th>{{ $t('cus.accountManager') }}<a class="icon-sort" @click="sort('userName')" /></th>
								<th v-for="(field, index) in fieldList" :key="index">
									{{ field.fieldShowName }}
									<a class="icon-sort" @click="sort('COL' + (index + 1))" />
								</th>
								<th width="10%" class="text-start">
									{{ $t('cus.execute') }}
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="(item, index) in pageData.content" :key="index">
								<td>
									<input
										id="cusSelect"
										v-model="selectedCusCodes"
										class="form-check-input"
										type="checkbox"
										:value="item.cusCode"
									>
								</td>
								<td :data-th="$t('cus.customerIdTaxId')">
									{{ item.cusCode }}
								</td>
								<td :data-th="$t('cus.customerName')">
									<a class="tx-link" @click="doViewSummary(item.cusCode)">{{ item.cusName }}</a>
								</td>
								<td :data-th="$t('cus.branch')">
									{{ item.branName }}
								</td>
								<td :data-th="$t('cus.aoCode')">
									{{ item.aoCode }}
								</td>
								<td :data-th="$t('cus.accountManager')">
									{{ item.userName }}
								</td>
								<td v-if="fieldList[0]?.fieldShowName" :data-th="fieldList[0].fieldShowName">
									{{ formatValue(item.col1, fieldList[0]?.fieldType) }}
								</td>
								<td v-if="fieldList[1]?.fieldShowName" :data-th="fieldList[1].fieldShowName">
									{{ formatValue(item.col2, fieldList[1]?.fieldType) }}
								</td>
								<td v-if="fieldList[2]?.fieldShowName" :data-th="fieldList[2].fieldShowName">
									{{ formatValue(item.col3, fieldList[2]?.fieldType) }}
								</td>
								<td v-if="fieldList[3]?.fieldShowName" :data-th="fieldList[3].fieldShowName">
									{{ formatValue(item.col4, fieldList[3]?.fieldType) }}
								</td>
								<td v-if="fieldList[4]?.fieldShowName" :data-th="fieldList[4].fieldShowName">
									{{ formatValue(item.col5, fieldList[4]?.fieldType) }}
								</td>
								<td v-if="fieldList[5]?.fieldShowName" :data-th="fieldList[5].fieldShowName">
									{{ formatValue(item.col6, fieldList[5]?.fieldType) }}
								</td>
								<td :data-th="$t('cus.execute')" class="text-start">
									<a class="btn btn-action btn-icon" @click="createNewConnect(item.cusCode)">
										<i class="bi bi-chat-dots-fill" data-bs-toggle="tooltip" :title="$t('cus.createContactRecord')" />
									</a>
									<a class="btn btn-action btn-icon" @click="createNewAppointment(item.cusCode)">
										<i class="bi bi-calendar2-plus-fill" data-bs-toggle="tooltip" :title="$t('cus.createAppointment')" />
									</a>
									<!-- <a href="#date" class="table-icon">
									<button type="button" class="btn btn-action btn-icon" data-bs-toggle="tooltip" title="執行投資屬性問卷"><i class="fas fa-clipboard"></i></button>
								</a>
								<a href="../../CIF/TrxPlan/cusTradePlan.htm" class="table-icon">
									<button type="button" class="btn btn-action btn-icon" data-bs-toggle="tooltip" title="投資規劃"><i class="fas fa-dollar-sign"></i></button>
								</a> -->
								</td>
							</tr>
						</tbody>
					</table>
				</div>
				<div class="card-footer">
					<div class="card-footer">
						<div class="row align-items-end pb-3">
							<div class="col-8 col-md-4">
								<label class="form-label d-flex justify-content-between"> {{ $t('cus.addToKeyCustomerGroup') }}</label>
								<div class="input-group">
									<select v-model="groupCode" class="form-select">
										<option :value="''">
											{{ $t('cus.pleaseSelect') }}
										</option>
										<option v-for="cusGroup in cusGroupList" :value="cusGroup.groupCode">
											{{ cusGroup.groupName }}
										</option>
									</select>
									<button class="btn btn-info btn-glow" type="button" @click="saveCusGroup()">
										{{ $t('cus.save') }}
									</button>
								</div>
							</div>
							<div class="col-4 col-md-2 text-md-start text-end">
								<input
									id="comeBack"
									name="Submit1"
									type="button"
									class="btn btn-info btn-glow"
									:value="$t('cus.summaryCalculation')"
									@click="showDetail()"
								>
							</div>
							<div class="col-md-6 mt-sm-3">
								<label class="form-label"> {{ $t('cus.saveSearchResults') }}</label>
								<div class="input-group">
									<span class="input-group-text" style="padding-left: 15px">{{ $t('cus.searchResultName') }}</span>
									<input
										v-model="resultName"
										class="form-control"
										type="text"
										rules="required"
										:label="$t('cus.searchResultName')"
									>
									<button class="btn btn-info btn-glow" type="button" @click="chkSelectSaveCnt()">
										{{ $t('cus.save') }}
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div v-show="isShowDetail" class="card mt-2">
				<div class="card-body">
					<h4 class="caption px-0">
						{{ $t('cus.summaryCalculationResults') }}
					</h4>
					<table class="table table-bordered table-hover">
						<thead>
							<tr>
								<th>{{ $t('cus.summaryItems') }}</th>
								<th>{{ $t('cus.summaryAmount') }}</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<th class="wd-65p">
									AUM
								</th>
								<td class="wd-35p">
									<div>{{ summaryTable.sumAum != null && summaryTable.sumAum !== '' ? formatValue(summaryTable.sumAum, 'F') : 0 }}</div>
								</td>
							</tr>
							<tr>
								<th class="wd-65p">
									{{ $t('cus.specificMoneyTrustAUM') }}
								</th>
								<td class="wd-35p">
									<div>
										{{
											summaryTable.sumTrustAum != null && summaryTable.sumTrustAum !== ''
												? formatValue(summaryTable.sumTrustAum, 'F')
												: 0
										}}
									</div>
								</td>
							</tr>
							<tr>
								<th class="wd-65p">
									{{ $t('cus.twdDepositBalance') }}
								</th>
								<td class="wd-35p">
									<div>
										{{ summaryTable.sumSbal != null && summaryTable.sumSbal !== '' ? formatValue(summaryTable.sumSbal, 'F') : 0 }}
									</div>
								</td>
							</tr>
							<tr>
								<th class="wd-65p">
									{{ $t('cus.foreignCurrencyDepositBalance') }}
								</th>
								<td class="wd-35p">
									<div>
										{{
											summaryTable.sumSbalFc != null && summaryTable.sumSbalFc !== '' ? formatValue(summaryTable.sumSbalFc, 'F') : 0
										}}
									</div>
								</td>
							</tr>
							<tr>
								<th class="wd-65p">
									{{ $t('cus.currentMonthContribution') }}
								</th>
								<td class="wd-35p">
									<div>
										{{
											summaryTable.sumFmonth != null && summaryTable.sumFmonth !== '' ? formatValue(summaryTable.sumFmonth, 'F') : 0
										}}
									</div>
								</td>
							</tr>
							<tr>
								<th class="wd-65p">
									{{ $t('cus.currentYearAccumulatedContribution') }}
								</th>
								<td class="wd-35p">
									<div>
										{{ summaryTable.sumFytd != null && summaryTable.sumFytd !== '' ? formatValue(summaryTable.sumFytd, 'F') : 0 }}
									</div>
								</td>
							</tr>
							<tr>
								<th class="wd-65p">
									{{ $t('cus.previousYearAccumulatedContribution') }}
								</th>
								<td class="wd-35p">
									<div>{{ summaryTable.sumFly != null && summaryTable.sumFly !== '' ? formatValue(summaryTable.sumFly, 'F') : 0 }}</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>

			<div class="btn-end">
				<input
					id="comeBack"
					name="Submit1"
					type="button"
					class="btn btn-primary btn-glow btn-lg"
					:value="$t('cus.goBack')"
					@click="comeBack()"
				>
			</div>
		</div>

		<div v-show="isShowCustomized">
			<div class="card card-form">
				<div class="card-header">
					<h4>{{ $t('cus.customFieldListForSearchResults') }}</h4>
					<span class="tx-square-bracket">{{ $t('cus.pleaseSelectMaxSixItems') }}</span>
				</div>

				<div class="table-responsive">
					<table class="table">
						<tbody>
							<template v-for="(item, index) in customizedMenus" :key="index">
								<tr v-if="index % 2 === 0">
									<td width="40%">
										<input
											:id="'chk' + index"
											v-model="selectedFieldNames"
											class="form-check-input"
											type="checkbox"
											:value="item.fieldName"
										>
										<label :for="'chk' + index" class="form-check-label"> {{ item.fieldShowName }}</label>
									</td>
									<td v-if="customizedMenus[index + 1]" width="60%">
										<input
											:id="'chk' + (index + 1)"
											v-model="selectedFieldNames"
											class="form-check-input"
											type="checkbox"
											:value="customizedMenus[index + 1].fieldName"
										>
										<label :for="'chk' + (index + 1)" class="form-check-label">{{ customizedMenus[index + 1].fieldShowName }}</label>
									</td>
								</tr>
							</template>
						</tbody>
					</table>
				</div>
			</div>
			<div class="mt-3 text-end">
				<input
					type="button"
					class="btn btn-lg btn-glow btn-primary"
					:value="$t('cus.save')"
					@click="saveCustomized()"
				>
				<input
					type="button"
					class="btn btn-lg btn-glow btn-primary"
					:value="$t('cus.goBack')"
					@click="hideCustomized()"
				>
			</div>
		</div>
		<vue-cus-summary
			v-show="isShowSummary"
			ref="cusSummary"
			:set-is-show-summary="setIsShowSummary"
			:set-show-search-result="setIsShowSearchResult"
		/>
		<vue-wob-new-task-modal ref="newTaskModal" :prop-cus-code="connectCusCode" :show-section="newTaskSection" />
	</div>
</template>
<script>
import vueCusSummary from '../../include/cusSummary.vue';
import vuePagination from '@/views/components/pagination.vue';
import _ from 'lodash';
import vueWobNewTaskModal from '@/views/wob/WOB0400/include/newTaskModal.vue';
export default {
	components: {
		vueCusSummary,
		vuePagination,
		vueWobNewTaskModal
	},
	props: {
		setIsShowSearchBar: Function,
		queryReq: Object,
		parentSearchYn: Boolean,
		parentSearch: Function,
		getSearchHistory: Function,
		setCusCode: Function,
		setIsShowTitle: Function
	},
	data() {
		return {
			isShowSummary: false,
			// pop視窗控制參數
			connectCusCode: null,
			newTaskSection: null,

			maxSelection: 6,
			summaryTable: {
				sumAum: 0,
				sumTrustAum: 0,
				sumSbal: 0,
				sumSbalFc: 0,
				sumFmonth: 0,
				sumFytd: 0,
				sumFly: 0
			},

			// API 用參數
			resultName: '',
			selectedCusCodes: [],
			groupCode: '',
			selectedFieldNames: [],

			// 畫面邏輯判斷用參數
			isShowResult: true,
			allSelected: false,
			isShowDetail: false,
			isShowCustomized: false,
			selectedMenuItems: [],
			cusSaveResultCount: 0,
			roleType: null,

			// 下拉選單
			cusGroupList: [],
			customizedMenus: [],

			// 畫面顯示用參數
			columnDef: {
				cusCode: { sortRef: 'CUS_CODE' },
				cusName: { sortRef: 'CUS_NAME' },
				branName: { sortRef: 'BRAN_NAME' },
				aoCode: { sortRef: 'AO_CODE' },
				userName: { sortRef: 'USER_NAME' }
			},
			// 主要顯示資料
			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'CUS_CODE',
				direction: 'ASC'
			},
			fieldList: [],
			searchHistory: []

		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo']),
		isShowPageTitle() {
			return !this.isShowSummary;
		}
	},
	watch: {
		selAreaList(newValue) {
			// 當 selAreaList 只有一筆資料時，自動設置 rmBranCode 的預設值
			if (newValue.length === 1) {
				this.rmBranCode = newValue[0].branCode;
			}
		},
		selectedCusCodes: {
			handler: function () {
				this.summaryTable = {
					sumAum: 0,
					sumTrustAum: 0,
					sumSbal: 0,
					sumSbalFc: 0,
					sumFmonth: 0,
					sumFytd: 0,
					sumFly: 0
				};
				const selectedCusCodes = this.selectedCusCodes;

				this.pageData.content.forEach((item) => {
					if (selectedCusCodes.includes(item.cusCode)) {
						this.summaryTable.sumAum += isNaN(parseFloat(item.aum)) ? 0 : parseFloat(item.aum);
						this.summaryTable.sumTrustAum += isNaN(parseFloat(item.trustAum)) ? 0 : parseFloat(item.trustAum);
						this.summaryTable.sumSbal += isNaN(parseFloat(item.sbal)) ? 0 : parseFloat(item.sbal);
						this.summaryTable.sumSbalFc += isNaN(parseFloat(item.sbalFc)) ? 0 : parseFloat(item.sbalFc);
						this.summaryTable.sumFmonth += isNaN(parseFloat(item.fmonth)) ? 0 : parseFloat(item.fmonth);
						this.summaryTable.sumFytd += isNaN(parseFloat(item.fytd)) ? 0 : parseFloat(item.fytd);
						this.summaryTable.sumFly += isNaN(parseFloat(item.fly)) ? 0 : parseFloat(item.fly);
					}
				});
			},
			deep: true, // 深層監聽
			immediate: true // 如果需要立即觸發一次
		},
		selectedFieldNames(newVal) {
			if (newVal.length > this.maxSelection) {
				this.$bi.alert(this.$t('cus.maxSixItemsCanBeSelected'));
				// 移除最後一個添加的項目
				this.selectedFieldNames.pop();
			}
			if (newVal.length === 0) {
				this.$bi.alert(this.$t('cus.atLeastOneItemMustBeSelected'));
				// 保持至少一個被選中的項目
				this.selectedFieldNames.push(this.lastSelectedField || newVal[0]);
			}
			else {
				// 記錄最新的選擇，以便在取消所有選擇時回滾
				this.lastSelectedField = newVal[newVal.length - 1];
			}
		},
		userInfo(newVal) {
			const self = this;
			if (newVal) {
				self.roleType = newVal.roleType;
				if (self.roleType != 'RM') {
					// self.sortMenu.push({ name: '久未往來註記', sortRef: 'USER_CODE_YN' });
				}
			}
		}
	},
	mounted() {
		this.getCusGroupList();
		this.getSelectedMenuItems();
		this.getAllSearchFields();
		this.getCusSaveResultCount();
	},
	methods: {
		gotoPage(page) {
			this.pageable.page = page;
			this.getPageData();
		},
		getPageData() {
			if (this.queryReq == null) {
				return;
			}
			if (this.parentSearchYn) {
				this.parentSearch(this.pageable).then((resp) => {
					if (resp.data.cusList.totalElements == 0) {
						this.$bi.alert(this.$t('cus.noCustomersFound'));
						this.comeBack();
					}
					else {
						this.pageData = resp.data.cusList;
						this.fieldList = resp.data?.fieldList || [];
						this.setIsShowSearchBar(true);
					}
				});
			}
			else {
				this.singleQuery();
			}
		},
		async singleQuery() {
			const url = _.toPageUrl('', this.pageable.page, this.pageable);
			const resp = await this.$api.cusSingleQuery(this.queryReq, url);
			if (resp.data.cusList.totalElements == 0) {
				this.$bi.alert(this.$t('cus.noCustomersFound'));
				this.comeBack();
			}
			else {
				this.pageData = resp.data.cusList;
				this.fieldList = resp.data?.fieldList || [];
				this.setIsShowSearchBar(true);
			}
		},
		sort(columnName) {
			if (this.pageable.sort !== (this.columnDef[columnName]?.sortRef || columnName)) {
				this.pageable.sort = this.columnDef[columnName]?.sortRef || columnName;
				this.pageable.direction = 'DESC';
			}
			else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}
			this.gotoPage(0);
		},
		selectAll() {
			if (!this.allSelected) {
				// 使用 Vue.set 或者重新賦值的方式來更新 selectedCusCodes
				const newSelected = [...this.selectedCusCodes]; // 先複製一份陣列
				this.pageData.content.forEach((item) => {
					if (!newSelected.includes(item.cusCode)) {
						newSelected.push(item.cusCode); // 更新新陣列
					}
				});
				this.selectedCusCodes = newSelected; // 重新賦值
			}
			else {
				const newSelected = [...this.selectedCusCodes]; // 先複製一份陣列
				this.pageData.content.forEach((target) => {
					newSelected.forEach((item, index) => {
						if (item == target.cusCode) {
							newSelected.splice(index, 1); // 更新新陣列
						}
					});
				});
				this.selectedCusCodes = newSelected; // 重新賦值
			}
		},
		async getCusGroupList() {
			const ret = await this.$api.getCusGroupListApi();
			this.cusGroupList = ret.data;
		},
		async getSelectedMenuItems() {
			const ret = await this.$api.getSelectedMenuItemsApi();
			this.selectedMenuItems = ret.data.map(item => item.fieldName);
		},
		async getAllSearchFields() {
			const ret = await this.$api.getAllSearchFieldsApi();
			this.customizedMenus = ret.data;
		},
		async getCusSaveResultCount() {
			const ret = await this.$api.getCusSaveResultCountApi({
				paramType: 'CUS_SAVE_RESULT_COUNT'
			});
			this.cusSaveResultCount = parseInt(ret.data[0].paramValue, 10);
		},
		showCustomized() {
			this.selectedFieldNames = [...this.selectedMenuItems];
			this.isShowResult = false;
			this.isShowCustomized = true;
		},
		hideCustomized() {
			this.isShowResult = true;
			this.isShowCustomized = false;
		},
		async saveCustomized() {
			await this.$api.postCusSelfsetFields({
				fieldNameList: this.selectedFieldNames
			});
			this.$bi.alert(this.$t('cus.addSuccessful'));
			this.gotoPage(this.pageable.page);
			this.getSelectedMenuItems();
			this.hideCustomized();
		},
		async chkSelectSaveCnt() {
			if (_.isBlank(this.resultName)) {
				this.$swal.fire({
					icon: 'error',
					text: this.$t('cus.pleaseFillInSearchResultName'),
					showCloseButton: true,
					confirmButtonText: this.$t('cus.confirm'),
					buttonsStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}
			else if (this.resultName.length > 60) {
				this.$swal.fire({
					icon: 'error',
					text: this.$t('cus.searchResultNameCannotExceed60Characters'),
					showCloseButton: true,
					confirmButtonText: this.$t('cus.confirm'),
					buttonsStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			if (_.isEmpty(this.selectedCusCodes)) {
				this.$swal.fire({
					icon: 'error',
					text: this.$t('cus.pleaseSelectCustomer'),
					showCloseButton: true,
					confirmButtonText: this.$t('cus.confirm'),
					buttonsStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			const ret = await this.$api.getCountSearchResultApi();
			this.insertQueryResult(ret.data.cnt);
		},
		async insertQueryResult(cusSaveResultCount) {
			if (cusSaveResultCount >= this.cusSaveResultCount) {
				this.$swal.fire({
					icon: 'error',
					text: this.$t('cus.searchResultsAlreadyHaveRecords') + this.cusSaveResultCount + this.$t('cus.recordsCannotAddMore'),
					showCloseButton: true,
					confirmButtonText: this.$t('cus.confirm'),
					buttonsStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			const ret = await this.$api.postCusSearchList({
				resultName: this.resultName,
				cusCodeList: this.selectedCusCodes
			});
			this.insertCusSearchLog(ret.data);
		},
		async insertCusSearchLog(resultCode) {
			const ret = await this.$api.insertCusSearchLogApi({
				resultCode: resultCode,
				resultName: this.resultName,
				logType: 'A'
			});
			if (ret.status === 200) {
				this.$swal.fire({
					icon: 'success',
					text: this.$t('cus.saveSuccessful'),
					showCloseButton: true,
					confirmButtonText: this.$t('cus.confirm'),
					buttonsStyling: false,
					customClass: {
						confirmButton: 'btn btn-success'
					}
				});
			}
			else {
				this.$swal.fire({
					icon: 'error',
					text: this.$t('cus.saveFailed'),
					showCloseButton: true,
					confirmButtonText: this.$t('cus.confirm'),
					buttonsStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
			}
		},
		showDetail() {
			this.isShowDetail = !this.isShowDetail;
		},
		async saveCusGroup() {
			if (_.isEmpty(this.selectedCusCodes)) {
				this.$swal.fire({
					icon: 'error',
					text: this.$t('cus.pleaseSelectCustomer'),
					showCloseButton: true,
					confirmButtonText: this.$t('cus.confirm'),
					buttonsStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			if (this.groupCode === '') {
				this.$swal.fire({
					icon: 'error',
					text: this.$t('cus.pleaseSelectCustomerGroup'),
					showCloseButton: true,
					confirmButtonText: this.$t('cus.confirm'),
					buttonsStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}
			const ret = await this.$api.postCusGroupDetail({
				groupCode: this.groupCode,
				cusCodeList: this.selectedCusCodes
			});
			if (ret.data) {
				this.$swal.fire({
					icon: 'success',
					text: this.$t('cus.saveSuccessful'),
					showCloseButton: true,
					confirmButtonText: this.$t('cus.confirm'),
					buttonsStyling: false,
					customClass: {
						confirmButton: 'btn btn-success'
					}
				});
			}
			else {
				this.$swal.fire({
					icon: 'error',
					text: this.$t('cus.customerAlreadyExistsInGroup'),
					showCloseButton: true,
					confirmButtonText: this.$t('cus.confirm'),
					buttonsStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
			}
		},
		doViewSummary(cusCode) {
			this.$refs.cusSummary.setCusCode(cusCode);
			this.setIsShowSummary(true);
		},
		setIsShowSummary(val) {
			this.setIsShowTitle(false);
			this.isShowSummary = val;
			this.isShowResult = !val;
		},
		setIsShowSearchResult(val) {
			this.setIsShowTitle(true);
			this.isShowSummary = !val;
			this.isShowResult = val;
		},
		comeBack() {
			this.pageData.content = [];
			this.pageable = {
				page: 0,
				size: 20,
				sort: 'CUS_CODE',
				direction: 'ASC'
			};
			this.fieldList = [];
			this.resultName = '';
			this.groupCode = '';
			this.selectedCusCodes = [];
			this.isShowDetail = false;
			this.allSelected = false;
			this.queryReq.userCodeYn = null;
			this.setIsShowSearchBar(false);
			this.getSearchHistory();
		},
		formatValue(value, type) {
			switch (type) {
				case 'I': // 整數格式
					return this.formatInteger(value);
				case 'D': // 日期格式
					return this.formatDate(value);
				case 'F': // 浮點數轉為千分位整數格式
					return this.formatFloatAsInteger(value);
				case 'S': // 字串格式
					return value;
				// 其他類型的格式化處理可以在這裡繼續擴展
				default:
					return value; // 預設返回原始值
			}
		},
		formatInteger(value) {
			const numericValue = Number(value);
			return isNaN(numericValue) ? 0 : Math.floor(numericValue);
		},
		formatDate(value) {
			const date = new Date(value);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`; // 手動格式化為 'YYYY-MM-DD'
		},
		formatFloatAsInteger(value) {
			const numericValue = Number(value);
			return isNaN(numericValue) ? 0 : Math.round(numericValue).toLocaleString(); // 或者其他浮點數處理方式
		},
		createNewAppointment(cusCode) {
			this.connectCusCode = cusCode;
			this.newTaskSection = 2;
			this.$refs.newTaskModal.show();
		},
		createNewConnect(cusCode) {
			this.connectCusCode = cusCode;
			this.newTaskSection = 3;
			this.$refs.newTaskModal.show();
		},
		next() {
			self.$router.push('/cus/favCusSetup');
		}
	}
};
</script>
