<template>
	<vue-modal :is-open="isOpenModal" @close="closeModal">
		<template #content="props">
			<div class="modal-dialog modal-xl modal-dialog-centered">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('cus.productQuickSearch') }}
						</h4>
						<button type="button" class="btn-expand">
							<i class="bi bi-arrows-fullscreen" />
						</button>
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click.prevent="props.close()"
						/>
					</div>
					<div class="modal-body">
						<div class="card card-form shadow-none">
							<div class="card-body">
								<form>
									<div class="row g-3 align-items-end">
										<div class="col-lg-4 col-xl-4">
											<label class="form-label">{{ $t('cus.productName') }}</label>
											<div class="input-group">
												<input
													id="proName"
													v-model="proSearch.proName"
													name="proName"
													class="form-control"
													type="text"
													size="15"
												>
												<button class="btn btn-primary JQ-query" type="button" @click.prevent="proSearchByName">
													<i class="bi bi-search" />
												</button>
											</div>
										</div>
										<div class="col-lg-4 col-xl-4">
											<label class="form-label">{{ $t('cus.productCode') }}</label>
											<div class="input-group">
												<input
													id="bankProCode"
													v-model="proSearch.proCode"
													name="bankProCode"
													class="form-control"
													type="text"
													size="15"
												>
												<button class="btn btn-primary JQ-query" type="button" @click.prevent="proSearchByCode">
													<i class="bi bi-search" />
												</button>
											</div>
										</div>
										<div class="col-lg-4 col-xl-4">
											<label class="form-label">{{ $t('cus.productMainCategory') }}</label>
											<div class="input-group">
												<select v-model="proSearch.proType" name="pfcatCode" class="form-select">
													<option :value="null">
														{{ $t('cus.all') }}
													</option>
													<option v-for="item in selProCat" :value="item.pfcatCode">
														{{ item.pfcatName }}
													</option>
												</select>
												<button class="btn btn-primary JQ-query" type="button" @click.prevent="proSearchByType">
													<i class="bi bi-search" />
												</button>
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
						<div id="ProResult">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('cus.searchResult') }}</h4>
									<vue-pagination :pageable="proSearch.pageData" :goto-page="getProQuickSearch" />
								</div>
								<div class="table-responsive">
									<table id="tbl" class="table table-RWD table-bordered text-center">
										<thead>
											<tr>
												<th width="5%">
													{{ $t('cus.select') }}
												</th>
												<th width="10%">
													{{ $t('cus.productCode') }}
												</th>
												<th width="14%">
													{{ $t('cus.chineseName') }}
												</th>
												Ï		<th width="14%">
													{{ $t('cus.englishName') }}
												</th>
												<th width="12%">
													{{ $t('cus.productSubcategory') }}
												</th>
												<th width="10%">
													{{ $t('cus.riskProfile') }}
												</th>
												<th width="10%">
													{{ $t('cus.productCurrency') }}
												</th>
												<th width="12%">
													{{ $t('cus.availableForSubscription') }}
												</th>
												<th width="13%">
													{{ $t('cus.productExpiryDate') }}
												</th>
											</tr>
										</thead>
										<tbody id="wrapperList">
											<tr v-for="(item, index) in proSearch.pageData.content" :key="index">
												<td data-th="">
													<input
														v-model="proSearch.radioProCode"
														name="proSearch.radioProCode"
														type="radio"
														class="form-check-input"
														:value="item.bankProCode"
													>
												</td>
												<td :data-th="$t('cus.productCode')">
													{{ item.bankProCode }}
												</td>
												<td :data-th="$t('cus.chineseName')">
													{{ item.proName }}
												</td>
												<td :data-th="$t('cus.englishName')">
													{{ item.engProName }}
												</td>
												<td :data-th="$t('cus.productSubcategory')">
													{{ item.proSubType }}
												</td>
												<td :data-th="$t('cus.riskProfile')">
													{{ item.riskName }}
												</td>
												<td :data-th="$t('cus.productCurrency')">
													{{ item.curName }}
												</td>
												<td :data-th="$t('cus.availableForSubscription')">
													<img v-if="item.buyYn === 'Y'" :src="getImgURL('icon', 'ico-yes.png')">
													<img v-else :src="getImgURL('icon', 'ico-no.png')">
												</td>
												<td :data-th="$t('cus.productExpiryDate')">
													{{ $filters.formatDate(item.expireDt) }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-white" @click="closeModal">
							{{ $t('cus.close') }}
						</button>
						<button
							type="button"
							class="btn btn-primary"
							@click="
								setProCode(proSearch.radioProCode);
								closeModal();
							"
						>
							{{ $t('cus.confirm') }}
						</button>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
</template>
<script>
import vuePagination from '@/views/components/pagination.vue';
import vueModal from '@/views/components/model.vue';
import { getImgURL } from '@/utils/imgURL.js';
import _ from 'lodash';
export default {
	components: {
		vueModal,
		vuePagination
	},
	props: {
		setProCode: Function
	},
	data: function () {
		return {
			proSearch: {
				proName: null,
				proCode: null,
				proType: null,
				req: {
					proName: null,
					proCode: null,
					proType: null
				},
				// 分頁元件
				pageable: {
					page: 0,
					size: 10,
					sort: 'PRO_CODE',
					direction: 'ASC'
				},
				// 查詢結果
				pageData: {
					content: {}
				},
				radioProCode: null
			},
			isOpenModal: false
		};
	},
	watch: {},
	beforeMount: function () { },
	created: function () { },
	mounted: function () { },
	methods: {
		getImgURL,
		show: function () {
			this.isOpenModal = true;
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		proSearchByName: function () {
			if (_.isBlank(this.proSearch.proName)) {
				this.$bi.alert(this.$t('cus.pleaseEnterProductName'));
			}
			else {
				this.proSearch.req.proName = this.proSearch.proName;
				this.proSearch.req.proCode = null;
				this.proSearch.req.proType = null;
				this.getProQuickSearch(0);
			}
		},
		proSearchByCode: function () {
			if (_.isBlank(this.proSearch.proCode)) {
				this.$bi.alert(this.$t('cus.pleaseEnterProductCode'));
			}
			else {
				this.proSearch.req.proName = null;
				this.proSearch.req.proCode = this.proSearch.proCode;
				this.proSearch.req.proType = null;
				this.getProQuickSearch(0);
			}
		},
		proSearchByType: function () {
			this.proSearch.req.proName = null;
			this.proSearch.req.proCode = null;
			this.proSearch.req.proType = this.proSearch.proType;
			this.getProQuickSearch(0);
		},
		getProQuickSearch: async function (page) {
			const self = this;
			self.proSearch.pageable.page = page;
			self.proSearch.radioProCode = null;
			const url = _.toPageUrl('', self.proSearch.pageable.page, self.proSearch.pageable);
			const resp = await self.$api.getDocProPageData(self.proSearch.req, url);
			resp.data.totalPages = 10;
		}
	}
};
</script>
