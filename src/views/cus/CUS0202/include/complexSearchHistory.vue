<template>
	<div class="col-lg-4 ps-lg-3">
		<div class="d-none d-lg-block">
			<div class="d-grid gap-2 mb-3">
				<router-link class="btn btn-lg btn-dark tx-16 tx-bold d-flex justify-content-between" :to="'/cus/cusSearch'">
					<span><img :src="getImgURL('icon', 'ico-single.png')"> {{ $t('cus.singleConditionCustomerSearch') }}</span> <i
						class="bi bi-shuffle"
					/>
				</router-link>
			</div>
		</div>
		<div class="card card-table">
			<div class="card-header">
				<h4>{{ $t('cus.searchHistoryList') }}</h4>
				<span class="tx-square-bracket">{{ $t('cus.maxFiveSearchResults') }}</span>
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-hover text-center">
					<thead>
						<tr>
							<th class="text-start">
								{{ $t('cus.searchResult') }}
							</th>
							<th>{{ $t('cus.createDate') }}</th>
							<th>{{ $t('cus.execute') }}</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="item in searchHistory">
							<td class="text-start" :data-th="$t('cus.searchResult')">
								<a href="#" class="tx-link" @click="getSearchHistoryByResultCode(item.resultCode)">{{ item.resultName
								}}</a>
							</td>
							<td :data-th="$t('cus.createDate')">
								{{ item.createDt }}
							</td>
							<td :data-th="$t('cus.execute')">
								<button
									id="btnQuery"
									type="button"
									class="btn tx-danger btn-icon-only JQ-logDelete"
									data-bs-toggle="tooltip"
									:data-bs-original-title="$t('cus.delete')"
									@click="deleteLog(item)"
								>
									<i class="bi bi-trash" />
								</button>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>
<script>
import { getImgURL } from '@/utils/imgURL.js';

export default {
	props: {
		search: Function
	},
	data() {
		return {
			// 主要顯示資料
			searchHistory: []
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo'])
	},
	watch: {
		userInfo(newVal) {
			if (!newVal) return;
			this.getSearchHistory();
		}
	},
	mounted() {
		this.getSearchHistory();
	},
	methods: {
		getImgURL,
		async getSearchHistory() {
			const ret = await this.$api.getSearchHistoryApi({});
			this.searchHistory = ret.data;
		},
		async deleteLog(item) {
			this.$bi.confirm(this.$t('cus.confirmDeleteWithName') + item.resultName, {
				event: {
					confirmOk: async () => {
						await this.$api.postCusSearchLog({
							resultCode: item.resultCode,
							resultName: item.resultName,
							logType: 'D'
						});
						await this.$api.deleteSearchResult({
							resultCode: item.resultCode
						});
						this.$bi.alert(this.$t('cus.deleteSuccess'));
						this.getSearchHistory();
					}
				}
			});
		},
		getSearchHistoryByResultCode: function (resultCode) {
			this.search({
				queryType: 'RESULT_CODE',
				resultCode: resultCode
			});
		}
	}
};
</script>
