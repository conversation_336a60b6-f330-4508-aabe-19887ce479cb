<template>
	<!--filemgr-sidebar start-->
	<div class="filemgr-wrapper filemgr-wrapper-two" :class="{ hidemenu: isHideMenu }">
		<span id="filemgrMenuclose" @click="hideMenu()" />
		<vue-cus-info-sidebar
			:cus-code="cusCode"
			:page-code="pageCode"
			:set-auth="setAuth"
			:set-customer="setCustomer"
		/>
		<div class="filemgr-content with-sectionnav">
			<vue-cus-info-page
				v-if="hasAuth == true"
				:cus-code="cusCode"
				:role-code="roleCode"
				:user-code="userCode"
				:has-auth="hasAuth"
				:customer="customer"
			/>
		</div>
	</div>
	<!-- filemgr-content -->
</template>
<script>
import vueCusInfoSidebar from '@/views/cus/include/cusInfoSidebar.vue';
import vueCusInfoPage from './include/cusInfo.vue';
export default {
	components: {
		vueCusInfoSidebar,
		vueCusInfoPage
	},
	data: function () {
		return {
			pageCode: 1,
			cusCode: null,
			roleCode: null,
			userCode: null,
			// 畫面判斷用邏輯
			customer: {
				cusName: null,
				idnEntityType: null
			},
			hasAuth: false,
			isHideMenu: false
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo'])
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				const self = this;
				if (newVal) {
					self.roleCode = newVal.roleCode;
					self.userCode = newVal.userCode;
				}
			}
		}
	},
	mounted: function () {
		const self = this;
		self.cusCode = self.prop.cusCode;
	},
	methods: {
		setAuth: function (val) {
			const self = this;
			self.hasAuth = val;
		},
		setCustomer: function (val) {
			const self = this;
			self.customer = val;
		},
		hideMenu: function () {
			const self = this;
			self.isHideMenu = !self.isHideMenu;
		}
	}
};
</script>
