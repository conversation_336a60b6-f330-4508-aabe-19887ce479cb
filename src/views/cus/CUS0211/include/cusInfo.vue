<template>
	<div>
		<div class="container-fluid">
			<vue-bi-tabs :menu-code="'M20-052'">
				<template #default="{ id }">
					<div>
						<component :is="id" :cus-code="cusCode" :set-cus-code="setCusCode" />
					</div>
				</template>
			</vue-bi-tabs>
		</div>
		<!-- Modal start -->
		<vue-modal :is-open="isOpenModal" @close="closeModal">
			<template #content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 id="myModal1" class="modal-title">
								{{ $t('cus.disabilityCategoryDimensionExplanation') }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="props.close()"
							/>
						</div>
						<div class="modal-body">
							<div class="card card-table mb-3">
								<div class="card-header">
									<h4>{{ $t('cus.disabilityCategoryDimensionExplanation') }}</h4>
								</div>
								<table class="table table-RWD table-bordered">
									<thead>
										<tr>
											<th>{{ $t('cus.category') }}</th>
											<th>{{ $t('cus.assessmentDimension') }}</th>
											<th>{{ $t('cus.correspondingChinese') }}</th>
										</tr>
									</thead>
									<tbody>
										<template v-for="group in disabledDimensionasGroupedData" :key="group.codeValue">
											<tr v-for="(item, index) in group.data" :key="index">
												<td v-if="index == 0" :data-th="$t('cus.category')" :rowspan="group.data.length">
													{{ item.codeName }}
												</td>
												<td :data-th="$t('cus.assessmentDimension')">
													{{ item.disabledCode }}
												</td>
												<td :data-th="$t('cus.correspondingChinese')">
													{{ item.disabledName }}
												</td>
											</tr>
										</template>
									</tbody>
								</table>
							</div>
						</div>
						<div id="disabledDimensionnalClaModalFooter" class="modal-footer">
							<button type="button" class="btn btn-white" @click.prevent="props.close()">
								{{ $t('cus.closeWindow') }}
							</button>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
	</div>
	<!-- Modal  End -->
</template>
<script>
import vueBiTabs from '@/views/components/biTabs.vue';
import vueCusBasicInfo from './cusBasicInfo.vue';
import vueCusCompany from './cusCompany.vue';
import vueCusHomeFriend from './cusHomeFriend.vue';
import vueCusImportantHoliday from '../../CUS0217/include/importantHoliday.vue';
import vueCusOtherInfo from './cusOtherInfo.vue';
import vueModal from '@/views/components/model.vue';
export default {
	components: {
		vueBiTabs,
		vueCusBasicInfo,
		vueCusCompany,
		vueCusHomeFriend,
		vueCusImportantHoliday,
		vueCusOtherInfo,
		vueModal
	},
	props: {
		cusCode: String,
		hasAuth: Boolean,
		customer: Object,
		setCusCode: Function
	},
	data: function () {
		return {
			isOpenModal: null,
			tabCode: 1,
			tabs: [],
			disabledDimensionas: []
		};
	},
	computed: {
		disabledDimensionasGroupedData: function () {
			const dataList = this.disabledDimensionas;
			const groupedData = dataList.reduce(function (acc, curr) {
				const codeValue = curr.codeValue;
				const data = Object.assign({}, curr);
				delete data.cusCode;
				if (!acc[codeValue]) {
					acc[codeValue] = { cusCode: codeValue, data: [] };
				}
				acc[codeValue].data.push(data);
				return acc;
			}, {});
			return groupedData;
		}
	},
	mounted: function () {
		const self = this;
		// Initialize tabs with i18n
		self.tabs = [
			{ tabCode: 1, label: self.$t('cus.bankCustomerData') },
			{ tabCode: 2, label: self.$t('cus.companyBasicData') },
			{ tabCode: 3, label: self.$t('cus.familyAndFriendsData') },
			{ tabCode: 4, label: self.$t('cus.importantHolidaySettings') }
		];
		// self.getDisabledDimensiona();
	},
	methods: {
		changeTab: function (tabCode) {
			const self = this;
			if (self.hasAuth) {
				if (self.customer.secretYn == 'Y') {
					self.$bi.alert(self.$t('cus.customerIsConfidential'));
				}
				else if (self.customer.employeesYn == 'Y') {
					self.$bi.alert(self.$t('cus.customerIsEmployee'));
				}
				else {
					self.tabCode = tabCode;
					for (let i = 0; i < self.tabs.length; i++) {
						const tab = self.tabs[i];
						if (tab.tabCode == tabCode) {
							self.title = tab.label;
						}
					}
				}
			}
			else {
				self.$bi.alert(self.$t('cus.customerNotUnderYourPrivateBankingCenter').replace('{0}', self.customer.cusName));
			}
		},
		initPageDatas: function (cusCode) {
			const self = this;
			if (this.$refs.basicInfo) {
				this.$refs.basicInfo.initPageDatas(cusCode);
			}
		},
		getDisabledDimensiona: async function () {
			const self = this;
			const ret = await self.$api.getDisabledDimensionsApi({
				cusCode: self.cusCode
			});
			self.disabledDimensionas = ret.data;
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		openModal: function () {
			this.isOpenModal = true;
		}
	}
};
</script>
