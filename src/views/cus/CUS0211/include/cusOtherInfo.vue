<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<DataTable
				v-for="que in quesection"
				:key="que.quesectionId"
				:columns
				:title="que.quesectionName"
				:rows="que.queitem"
				bordered
				class="mb-2"
			>
				<template #body-cell-options="{item}">
					<CheckboxGroup
						v-if="item.queitemType == 'CHECK'"
						v-model="item.checkedItemSel"
						inline
						:options="item.itemsel"
						option-value="queitemselId"
						option-label="queitemselName"
						class="d-flex gap-2 flex-wrap"
					>
						<template #option="{onSelect, option}">
							<span class="cursor-pointer" @click="onSelect">
								{{ option.queitemselName }}
							</span>
							<template v-if="option.inputYn === 'Y'">
								<Textarea
									v-if="option.inputType === 'NULL'"
									v-model="option.ansText"
									class="d-inline ms-1"
									:maxlength="option.inputCols"
									:disabled="initTextAble(item, option.queitemselId)"
								/>
								<Input
									v-else
									v-model="option.ansText"
									type="text"
									class="d-inline ms-1"
									style="width: 100px"
									:maxlength="option.inputCols"
									size="40"
									:disabled="initTextAble(item, option.queitemselId)"
								/>
							</template>
						</template>
					</CheckboxGroup>
					<RadioGroup
						v-else-if="item.queitemType == 'RADIO'"
						v-model="item.checkedRadioItem"
						inline
						:options="item.itemsel"
						option-value="queitemselId"
						option-label="queitemselName"
						class="d-flex gap-2 flex-wrap"
					/>
					<Textarea
						v-else-if="item.queitemType == 'TEXT'"
						v-model="item.ansText"
					/>
					<span v-else>ERROR: {{ item.queitemType }}</span>
				</template>
			</DataTable>
			<div id="extDataLogForm">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('cus.changeRecord') }}</h4>
					</div>
					<div class="table-responsive">
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th colspan="2" class="wd-15p">
										{{ $t('cus.lastUpdateDate') }}
									</th>
									<td>
										<span v-if="extDataLog">{{ $filters.formatDate(extDataLog.createDt) }}</span>
									</td>
								</tr>
								<tr>
									<th colspan="2" class="wd-15p">
										{{ $t('cus.updatePersonnel') }}
									</th>
									<td>
										<span v-if="extDataLog">{{ extDataLog.userCode }} {{ extDataLog.userName }}</span>
									</td>
								</tr>
								<tr>
									<th colspan="2" class="wd-15p">
										{{ $t('cus.caCode') }}
									</th>
									<td>
										<span v-if="extDataLog">{{ extDataLog.aoCode }} {{ extDataLog.aoName }}</span>
									</td>
								</tr>
								<tr>
									<th colspan="2" class="wd-15p">
										{{ $t('cus.affiliatedUnit') }}
									</th>
									<td>
										<span v-if="extDataLog">{{ extDataLog.branCode }} {{ extDataLog.branName }}</span>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<div v-if="auth" class="text-end mt-3">
				<Button :label="$t('cus.save')" size="lg" @click="updateExtDataAns()" />
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
export default {
	props: {
		cusCode: null
	},
	data: function () {
		return {
			quesection: [],
			extDataAnswers: [],
			extDataLog: null,
			authYn: null,
			columns: [
				{ field: 'queitemName', bodyClass: 'text-wrap w-[15%] max-w-[15%] font-bold' },
				{ name: 'options' }
			]
		};
	},
	computed: {
		auth: function () {
			return this.authYn === 'Y';
		}
	},
	mounted: function () {
		const self = this;
		self.getQuesectionMenu();
		self.getExtDataLog();
		self.chkCustomerAuth();
	},
	methods: {
		getQuesectionMenu: async function () {
			const self = this;
			const ret = await self.$api.getExtDataItemApi();
			self.quesection = ret.data;
			self.getExtDataAnswers();
		},
		getExtDataAnswers: async function () {
			const self = this;
			const ret = await self.$api.getExtDataAnswersApi({
				cusCode: self.cusCode
			});
			self.extDataAnswers = ret.data;
			self.setExtDataAns();
		},
		getExtDataLog: async function () {
			const self = this;
			const ret = await self.$api.getExtDataLogApi({ cusCode: self.cusCode });
			self.extDataLog = ret.data.length > 0 ? ret.data[0] : {};
		},
		setExtDataAns: function () {
			const self = this;
			const ansMap = _.groupBy(self.extDataAnswers, 'queitemId');
			self.quesection.forEach((quesection) => {
				quesection.queitem.forEach((queItem) => {
					if (!queItem.checkedItemSel) queItem.checkedItemSel = [];

					const ans = ansMap[queItem.queitemId];
					if (ans == null) return;

					if (queItem.queitemType === 'CHECK') {
						ans.forEach((ansItem) => {
							queItem.checkedItemSel.push(ansItem.queitemselId);
						});
					}
					else if (queItem.queitemType === 'RADIO') {
						queItem.checkedRadioItem = ans[0].queitemselId;
					}
					else if (queItem.queitemType === 'TEXT') {
						queItem.ansText = ans[0].ansText;
					}

					if (queItem.queitemType === 'CHECK' || queItem.queitemType === 'RADIO') {
						const selMap = _.groupBy(ans, 'queitemselId');

						queItem.itemsel.forEach((itemsel) => {
							if (itemsel.inputYn !== 'Y' || selMap[itemsel.queitemselId] == null) return;
							itemsel.ansText = selMap[itemsel.queitemselId][0].ansText;
						});
					}
				});
			});
		},
		updateExtDataAns: async function () {
			const self = this;
			const extDataAns = [];

			self.quesection.forEach(async function (quesection) {
				quesection.queitem.forEach(function (queItem) {
					const reqQueitem = {
						queitemId: queItem.queitemId,
						queitemsel: []
					};
					const anaTextObjMap = {};
					queItem.itemsel.forEach(function (queItemSel) {
						if (queItemSel.inputYn === 'Y') {
							const anaTextObj = {};
							anaTextObj.queItemSelId = queItemSel.queitemselId;
							anaTextObj.ansText = queItemSel.ansText;
							anaTextObjMap[queItemSel.queitemselId] = anaTextObj;
						}
					});

					if (queItem.queitemType === 'CHECK') {
						queItem.checkedItemSel.forEach(function (ans) {
							const reqQueitemsel = {};
							reqQueitemsel.queitemselId = ans;

							if (anaTextObjMap[ans] != null) {
								reqQueitemsel.ansText = anaTextObjMap[ans].ansText;
							}

							reqQueitem.queitemsel.push(reqQueitemsel);
						});
					}

					if (queItem.queitemType === 'RADIO' && !_.isBlank(queItem.checkedRadioItem)) {
						const reqQueitemsel = {};
						reqQueitemsel.queitemselId = queItem.checkedRadioItem;

						if (anaTextObjMap[reqQueitemsel.queitemselId] != null) {
							reqQueitemsel.ansText = anaTextObjMap[reqQueitemsel.queitemselId].ansText;
						}

						reqQueitem.queitemsel.push(reqQueitemsel);
					}

					if (queItem.queitemType === 'TEXT') {
						reqQueitem.queitemsel.push({
							ansText: queItem.ansText,
							queitemselId: queItem.itemsel[0].queitemselId
						});
					}

					extDataAns.push(reqQueitem);
				});
			});
			await self.$api.updateExtDataAnsApi({
				cusCode: self.cusCode,
				queitem: extDataAns
			});
			self.getExtDataLog();
			self.$bi.alert(self.$t('cus.updateSuccessful'));
		},
		initTextAble: function (queItem, queItemSelId) {
			const self = this;
			// 判斷是否為經管PBC
			if (!self.authYn) {
				// 不是直接退出
				return true;
			}
			if (queItem.queitemType == 'CHECK' && queItem.checkedItemSel?.includes(queItemSelId)) {
				return false;
			}
			else if (queItem.queitemType == 'RADIO' && queItem.checkedRadioItem == queItemSelId) {
				return false;
			}
			else if (queItem.queitemType == 'TEXT') {
				return false;
			}
			else {
				return true;
			}
		},
		chkCustomerAuth: async function () {
			const self = this;

			const resp = await self.$api.chkCustomerAuthApi({
				cusCode: self.cusCode,
				progCode: 'ACUS_003'
			});
			self.authYn = resp.data.authYn;
		}
	}
};
</script>
