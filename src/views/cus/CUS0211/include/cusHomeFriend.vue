<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<div v-if="auth" class="card card-form">
				<div class="card-header">
					<h4>{{ $t('cus.familyFriendsDataSetup') }}</h4>
					<span class="tx-square-bracket">{{ $t('cus.requiredField') }}</span>
				</div>
				<div class="card-body">
					<vue-form v-slot="{ errors }" ref="homeFriend">
						<div class="row g-3 align-items-end">
							<div class="col-md-3">
								<label class="form-label tx-require">{{ $t('cus.relationship') }}</label>
								<vue-field
									id="select1"
									v-model="reltypeCode"
									name="reltypeCode"
									class="form-select JQdata-hide"
									:class="{ 'is-invalid': errors.reltypeCode }"
									rules="required"
									:label="$t('cus.relationship')"
									as="select"
								>
									<option value="">
										{{ $t('cus.pleaseSelect') }}
									</option>
									<option v-for="relativeType in relativeTypeMenu" :value="relativeType.codeValue">
										{{ relativeType.codeName }}
									</option>
								</vue-field>
								<div v-show="errors.reltypeCode" class="text-danger" style="height: 3px">
									{{ errors.reltypeCode }}
								</div>
							</div>
							<div class="col-md-3">
								<label class="form-label">{{ $t('cus.customerIdNumber') }}</label>
								<input
									v-model="idn"
									name="text"
									type="text"
									class="form-control JQdata-hide"
								>
								<div class="text-danger" style="height: 3px" />
							</div>
							<div class="col-md-3">
								<label class="form-label tx-require">{{ $t('cus.nameRequired') }}</label>
								<vue-field
									v-model="relName"
									name="relName"
									type="text"
									class="form-control"
									maxlength="60"
									:label="$t('cus.nameRequired')"
									rules="required"
								/>
								<div v-show="errors.relName" class="text-danger" style="height: 3px">
									{{ errors.relName }}
								</div>
							</div>
							<div class="col-md-3">
								<label class="form-label tx-require">{{ $t('cus.genderRequired') }}</label>
								<div class="JQdata-hide">
									<div v-for="(item, i) in genderType" class="form-check form-check-inline">
										<vue-field
											:id="'genderType-' + i"
											v-model="gender"
											type="radio"
											name="gender"
											class="form-check-input"
											:value="item.codeValue"
											rules="required"
											:label="$t('cus.genderRequired')"
										/>
										<label :for="'genderType-' + i" class="form-label">{{ item.codeName }}</label>
									</div>
									<div v-show="errors.gender" class="text-danger" style="height: 3px">
										{{ errors.gender }}
									</div>
								</div>
							</div>
							<div class="col-md-3">
								<label class="form-label">{{ $t('cus.birthdayDate') }}</label>
								<DateInput v-model="birthday" />
							</div>
							<div class="col-md-3">
								<label class="form-label">{{ $t('cus.contactPhone') }}</label>
								<vue-field
									v-model="cphone"
									name="cphone"
									type="text"
									class="form-control JQdata-hide"
									:label="$t('cus.contactPhone')"
									maxlength="20"
								/>
								<div v-show="errors.cphone" class="text-danger" style="height: 3px">
									{{ errors.cphone }}
								</div>
							</div>
							<div class="col-md-3">
								<label class="form-label">{{ $t('cus.educationLevel') }}</label>
								<vue-field
									v-model="education"
									name="education"
									type="text"
									class="form-control JQdata-hide"
									:label="$t('cus.educationLevel')"
									maxlength="20"
								/>
								<div v-show="errors.education" class="text-danger" style="height: 3px">
									{{ errors.education }}
								</div>
							</div>
							<div class="col-md-3">
								<label class="form-label">{{ $t('cus.workUnit') }}</label>
								<vue-field
									v-model="organization"
									name="organization"
									type="text"
									class="form-control JQdata-hide"
									:label="$t('cus.workUnit')"
									maxlength="50"
								/>
								<div v-show="errors.organization" class="text-danger" style="height: 3px">
									{{ errors.organization }}
								</div>
							</div>
							<div class="col-md-3">
								<label class="form-label">{{ $t('cus.position') }}</label>
								<vue-field
									v-model="post"
									name="post"
									type="text"
									class="form-control JQdata-hide"
									:label="$t('cus.position')"
									maxlength="20"
								/>
								<div v-show="errors.post" class="text-danger" style="height: 3px">
									{{ errors.post }}
								</div>
							</div>
							<div class="col-md-6">
								<label class="form-label">{{ $t('cus.remarks') }}</label>
								<vue-field
									v-model="note"
									name="note"
									type="text"
									class="form-control JQdata-hide"
									:label="$t('cus.remarks')"
									maxlength="100"
								/>
								<div v-show="errors.note" class="text-danger" style="height: 3px">
									{{ errors.note }}
								</div>
							</div>
						</div>
						<div class="form-footer">
							<div v-if="id == null" id="button1">
								<button class="btn btn-primary btn-glow" type="button" @click="insertRelativeFriends()">
									{{ $t('cus.save') }}
								</button>
							</div>
							<div v-if="id" id="button2">
								<input
									class="btn btn-primary"
									type="button"
									:value="$t('cus.modify')"
									name="cancel"
									@click="updateRelativeFriends()"
								>
								<input
									class="btn btn-primary"
									type="button"
									:value="$t('cus.cancelModification')"
									name="cancel"
									@click="cancel()"
								>
							</div>
						</div>
					</vue-form>
				</div>
			</div>
		</div>

		<div class="card card-table">
			<div class="card-header">
				<h4>{{ $t('cus.familyFriendsDataList') }}</h4>
			</div>
			<div class="table-responsive">
				<table class="table table-RWD text-center">
					<thead>
						<tr>
							<th>{{ $t('cus.relationship') }}</th>
							<th>{{ $t('cus.customerIdNumber') }}</th>
							<th>{{ $t('cus.name') }}</th>
							<th>{{ $t('cus.gender') }}</th>
							<th>{{ $t('cus.birthday') }}</th>
							<th>{{ $t('cus.contactPhone') }}</th>
							<th>{{ $t('cus.education') }}</th>
							<th>{{ $t('cus.workUnit') }}</th>
							<th>{{ $t('cus.position') }}</th>
							<th class="text-start">
								{{ $t('cus.remarks') }}
							</th>
							<th v-if="auth" class="text-end">
								{{ $t('cus.execute') }}
							</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="relativeFriend in relativeFriends">
							<td>{{ relativeFriend.reltypeName }}</td>
							<td>{{ relativeFriend.idn }}</td>
							<td>
								<a
									v-if="relativeFriend.relCusCode"
									href="#"
									class="JQ-cusDetail tx-link"
									@click.prevent="setCusCode(relativeFriend.relCusCode)"
								>{{ relativeFriend.relName }}</a>
								<div v-else>
									{{ relativeFriend.relName }}
								</div>
							</td>
							<td>{{ relativeFriend.genderName }}</td>
							<td>{{ $filters.formatDate(relativeFriend.birthday) }}</td>
							<td>{{ relativeFriend.cphone }}</td>
							<td>{{ relativeFriend.education }}</td>
							<td>{{ relativeFriend.organization }}</td>
							<td>{{ relativeFriend.post }}</td>
							<td class="text-start">
								{{ relativeFriend.note }}
							</td>
							<td v-if="auth" class="text-end">
								<Button
									color="info"
									icon
									:title="$t('cus.edit')"
									@click="doUpdate(relativeFriend)"
								>
									<i class="bi bi-pen" />
								</Button>
								<Button
									color="danger"
									icon
									:title="$t('cus.delete')"
									@click="deleteRelativeFriends(relativeFriend.id)"
								>
									<i class="bi bi-trash" />
								</Button>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>
<script>
import { Field, Form } from 'vee-validate';
import { formatDate } from '@/utils/filter';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	props: {
		cusCode: null,
		setCusCode: Function
	},
	data: function () {
		return {
			relativeFriends: [],
			authYn: null,
			id: null,
			reltypeCode: '',
			idn: null,
			relName: null,
			gender: null,
			birthday: null,
			cphone: null,
			education: null,
			organization: null,
			post: null,
			note: null,
			relativeTypeMenu: [],
			genderType: []
		};
	},
	computed: {
		auth: function () {
			return this.authYn === 'Y';
		}
	},
	mounted: function () {
		const self = this;
		self.getRelativeFriends();
		self.getRelativeType();
		self.getGenderType();
		self.chkCustomerAuth();
	},
	methods: {
		getRelativeFriends: async function () {
			const self = this;
			const ret = await self.$api.getRelativeFriendsApi({
				cusCode: self.cusCode
			});
			self.relativeFriends = ret.data;
		},
		doUpdate: function (relativeFriend) {
			const self = this;
			self.id = relativeFriend.id;
			self.reltypeCode = relativeFriend.reltypeCode;
			self.idn = relativeFriend.idn;
			self.relName = relativeFriend.relName;
			self.gender = relativeFriend.gender;
			self.birthday = formatDate(relativeFriend.birthday);
			self.cphone = relativeFriend.cphone;
			self.education = relativeFriend.education;
			self.organization = relativeFriend.organization;
			self.post = relativeFriend.post;
			self.note = relativeFriend.note;
		},
		insertRelativeFriends: async function () {
			const self = this;

			self.$refs.homeFriend.validate().then(async function (pass) {
				if (pass.valid) {
					const ret = await self.$api.insertRelativeFriendsApi({
						cusCode: self.cusCode,
						idn: self.idn,
						relName: self.relName,
						reltypeCode: self.reltypeCode,
						gender: self.gender,
						birthday: formatDate(self.birthday),
						cphone: self.cphone,
						education: self.education,
						organization: self.organization,
						post: self.post,
						note: self.note
					});
					if (ret.data > 0) {
						self.$bi.alert(self.$t('cus.addSuccessful'));
						self.getRelativeFriends();
					}
					else {
						self.$bi.alert(self.$t('cus.systemNoCustomerCannotSave'));
					}
				}
			});
		},
		updateRelativeFriends: async function () {
			const self = this;
			const pass = await self.$refs.homeFriend.validate();
			if (!pass.valid) return;
			await self.$api.updateRelativeFriendsApi({
				id: self.id,
				cusCode: self.cusCode,
				idn: self.idn,
				relName: self.relName,
				reltypeCode: self.reltypeCode,
				gender: self.gender,
				birthday: formatDate(self.birthday),
				cphone: self.cphone,
				education: self.education,
				organization: self.organization,
				post: self.post,
				note: self.note
			});
			self.$bi.alert(self.$t('cus.updateSuccessful'));
			self.getRelativeFriends();
		},
		deleteRelativeFriends: async function (id) {
			const self = this;
			const ret = await self.$api.deleteRelativeFriendsApi({
				id: id,
				cusCode: self.cusCode
			});

			self.$bi.alert(self.$t('cus.deleteSuccessful'));
			self.getRelativeFriends();
			if ((self.id = id)) {
				self.id = null;
				self.cancel();
			}
		},
		getRelativeType: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'RELTYPE_CODE'
			});
			self.relativeTypeMenu = ret.data;
		},
		getGenderType: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'GENDER'
			});
			self.genderType = ret.data;
		},
		cancel: function () {
			const self = this;
			self.id = null;
			self.reltypeCode = null;
			self.idn = null;
			self.relName = null;
			self.gender = null;
			self.birthday = null;
			self.cphone = null;
			self.education = null;
			self.organization = null;
			self.post = null;
			self.note = null;
		},
		chkCustomerAuth: async function () {
			const self = this;
			const resp = await self.$api.chkCustomerAuthApi({
				cusCode: self.cusCode,
				progCode: 'ACUS_002'
			});
			self.authYn = resp.data.authYn;
		}
	}
};
</script>
