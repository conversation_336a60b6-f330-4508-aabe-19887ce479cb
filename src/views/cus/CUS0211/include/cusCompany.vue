<template>
	<div>
		<div class="tab-content">
			<div class="tab-pane fade show active">
				<div class="card card-table card-collapse">
					<div class="card-header">
						<h4>{{ $t('cus.companyData') }}</h4>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD">
							<thead>
								<tr>
									<th>{{ $t('cus.companyList') }}</th>
									<th class="text-end">
										{{ $t('cus.execute') }}
									</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="company in companies">
									<td>
										<ColoredLink v-if="company.url" :href="company.url" @click="openCompanyWebsite(company.url)">
											{{ company.comName }}
										</ColoredLink>
										<span v-else>
											{{ company.comName }}
										</span>
									</td>
									<td class="text-end">
										<button
											v-if="auth"
											type="button"
											class="btn btn-info btn-glow btn-icon"
											data-bs-toggle="tooltip"
											:title="$t('cus.edit')"
											@click="doUpdateCompany(company, 'UPDATE')"
										>
											<i class="bi bi-pen" />
										</button>
										<button
											v-if="auth"
											type="button"
											class="btn btn-danger btn-glow btn-icon"
											data-bs-toggle="tooltip"
											:title="$t('cus.delete')"
											@click="deleteCompany(company)"
										>
											<i class="bi bi-trash" />
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="text-end my-3">
					<Button
						v-if="auth"
						size="lg"
						:label="$t('cus.addCompanyData')"
						@click="doInsertCompany()"
					/>
				</div>

				<!-- 新增公司資料 -->
				<div v-if="reqType" id="companyInfo">
					<Stepper v-model="step" :items="steps" @click="gotoStep($event)">
						<template #basic>
							<div class="card card-form-collapse">
								<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
									<h4>{{ $t('cus.companyBasicData') }}</h4>
								</div>
								<div id="collapseListGroup1" class="collapse show">
									<div class="card-body">
										<vue-form v-slot="{ errors }" ref="companyBasicInfo">
											<div class="form-row">
												<div class="form-group col-lg-4">
													<label class="form-label tx-require"> {{ $t('cus.companyCategory') }}</label><br>
													<div v-for="(item, i) in compOwnType" class="form-check form-check-inline">
														<vue-field
															:id="'own_' + i"
															v-model="ownType"
															type="radio"
															class="form-check-input"
															name="own_type"
															:value="item.codeValue"
															rules="required"
															:label="$t('cus.companyCategory')"
															:class="{ 'is-invalid': errors.own_type }"
														/>
														<label :for="'own_' + i" class="form-check-label">{{ item.codeName }}</label>
													</div>
													<div style="height: 25px">
														<span v-show="errors.own_type" class="text-danger">{{ errors.own_type }}</span>
													</div>
												</div>
												<div class="form-group col-lg-4">
													<label class="form-label tx-require"> {{ $t('cus.companyNameRequired') }}</label><br>
													<vue-field
														id="comName"
														v-model="comName"
														name="comName"
														type="text"
														class="form-control"
														size="40"
														maxlength="60"
														:class="{ 'is-invalid': errors.comName }"
														rules="required"
														:label="$t('cus.companyNameRequired')"
													/>
													<span v-show="errors.comName" class="text-danger">{{ errors.comName }}</span>
												</div>
												<div class="form-group col-lg-4">
													<label class="form-label"> {{ $t('cus.companyEnglishName') }}:</label><br>
													<input
														v-model="comEname"
														name="com_ename"
														type="text"
														class="form-control JQ-keyup"
														size="40"
														maxlength="60"
													>
												</div>
												<div class="form-group col-lg-4">
													<label class="form-label"> {{ $t('cus.companyUnifiedNumber') }}</label><br>
													<vue-field
														id="vatNum"
														v-model="vatNum"
														name="vatNum"
														type="text"
														class="form-control JQ-intOnly"
														maxlength="8"
														:class="{ 'is-invalid': errors.vatNum }"
														:label="$t('cus.companyUnifiedNumber')"
														data-vv-scope="companyBasicInfo"
													/>
													<span v-show="errors.vatNum" class="text-danger">{{ errors.vatNum }}</span>
												</div>
												<div class="form-group col-lg-4">
													<label class="form-label"> {{ $t('cus.companyRepresentative') }}</label><br>
													<input
														v-model="owner"
														name="owner"
														type="text"
														class="form-control"
														size="40"
														maxlength="60"
													>
												</div>
												<div class="form-group col-lg-4">
													<label class="form-label"> {{ $t('cus.companyEstablishmentDate') }}</label><br>
													<input
														v-if="reqType == 'READONLY'"
														v-model="establishDt"
														type="input"
														class="form-control"
														size="10"
														maxlength="10"
														readonly="true"
													>
													<input
														v-else
														v-model="establishDt"
														name="establish_dt"
														type="date"
														class="form-control"
														size="10"
														maxlength="10"
													>
												</div>
												<div class="form-group col-lg-4">
													<label class="form-label">{{ $t('cus.companyWebsite') }}</label><br>
													<input
														v-model="url"
														name="url"
														type="text"
														class="form-control JQ-keyup"
														size="40"
														maxlength="60"
													>
												</div>
												<div class="form-group col-lg-4">
													<label class="form-label"> {{ $t('cus.companyPhone') }}</label><br>
													<input
														v-model="phone"
														name="phone"
														type="text"
														class="form-control JQ-intOnly"
														size="40"
														maxlength="20"
													>
												</div>
												<div class="form-group col-lg-4">
													<label class="form-label">{{ $t('cus.contactPerson') }}</label><br>
													<input
														v-model="contactPerson"
														name="contact_person"
														type="text"
														class="form-control"
														size="40"
														maxlength="60"
													>
												</div>
												<div class="form-group col-lg-4">
													<label class="form-label">{{ $t('cus.contactPhone') }}</label><br>
													<input
														v-model="contactPhone"
														name="contact_phone"
														type="text"
														class="form-control JQ-intOnly"
														size="40"
														maxlength="20"
													>
												</div>
												<div class="form-group col-lg-4">
													<label class="form-label">{{ $t('cus.industryType') }}</label><br>
													<select
														id="indus_code"
														v-model="indusCode"
														name="indus_code"
														class="form-select"
													>
														<option value="" selected="selected">
															--
														</option>
														<option v-for="cusIndustry in cusIndustriesMenu" :value="cusIndustry.codeValue">
															{{ cusIndustry.codeName }}
														</option>
													</select>
												</div>
												<div class="form-group col-lg-4">
													<label class="form-label">{{ $t('cus.capitalAmount') }}</label><br>
													<label	v-if="reqType == 'READONLY'" class="form-control">
														{{ $filters.formatNum(capital, '0,0') }}
													</label>
													<InputNumber
														v-else
														v-model="capital"
														name="capital"
														maxlength="25"
													/>
												</div>
												<div class="form-group col-lg-12">
													<label class="form-label tx-require">{{ $t('cus.companyRegistrationCountry') }}</label><br>
													<vue-field
														id="reg_cun_code"
														v-model="regCunCode"
														name="reg_cun_code"
														class="form-select"
														rules="required"
														:label="$t('cus.companyRegistrationCountry')"
														as="select"
														:class="{ 'is-invalid': errors.reg_cun_code }"
														data-vv-scope="companyBasicInfo"
													>
														<option v-for="regCountry in countriesMenu" :value="regCountry.cunCode">
															{{ regCountry.cunName }}
														</option>
													</vue-field>
													<span v-show="errors.reg_cun_code" class="text-danger">{{ errors.reg_cun_code }}</span>
												</div>
												<div class="form-group col-lg-12">
													<label class="form-label">{{ $t('cus.companyAddress') }}</label><br>
													<div class="row g-1">
														<div class="col-md-7">
															<div class="input-group">
																<span class="input-group-text">{{ $t('cus.country') }}</span>
																<select
																	id="cun_code"
																	v-model="cunCode"
																	name="cun_code"
																	class="form-select"
																>
																	<option v-for="country in countriesMenu" :value="country.cunCode">
																		{{ country.cunName }}
																	</option>
																</select>
																<span class="input-group-text"> {{ $t('cus.postalCode') }}</span>
																<input
																	v-model="zip"
																	name="zip"
																	type="text"
																	class="form-control JQ-intOnly"
																	size="10"
																	maxlength="10"
																>
															</div>
														</div>
														<div class="col-md-5">
															<input
																v-model="caddress"
																name="caddress"
																type="text"
																class="form-control"
																size="100"
																maxlength="66"
															>
														</div>
													</div>
												</div>
												<div class="form-group col-lg-12">
													<label class="form-label">{{ $t('cus.note') }}</label><br>
													<vue-field
														v-model="note"
														as="textarea"
														class="form-control"
														name="note"
														rows="2"
														cols="100"
														maxlength="200"
														:rules="{ max: 200 }"
													/>
													<div class="tx-note">
														200{{ $t('cus.charactersAvailable') }}
													</div>
													<div style="height: 25px">
														<span v-show="errors.note" class="text-danger">{{ errors.note }}</span>
													</div>
												</div>
											</div>
										</vue-form>
									</div>
								</div>
							</div>
							<div class="text-end mt-3">
								<input
									v-if="reqType != 'READONLY'"
									id="next-business"
									type="button"
									class="btn btn-lg btn-glow btn-primary"
									:value="$t('cus.saveAndNext')"
									@click="saveCompany()"
								>
							</div>
						</template>
						<template #business>
							<div class="card card-form mb-3">
								<div class="card-header">
									<h4>{{ $t('cus.companyOperationalData') }}</h4>
								</div>
								<table class="table table-RWD table-horizontal-RWD table-bordered">
									<tbody>
										<tr>
											<td>
												<vue-form ref="ownCompaniesWatch">
													<div class="form-row mb-2">
														<FormField
															v-slot="{field, invalid}"
															class="flex-fill"
															name="annual"
															:label="$t('cus.year')"
															required
															:rules="{ required: true, min: 4, min_value: 1900, numeric: true }"
														>
															<InputNumber maxlength="4" v-bind="field" :invalid />
														</FormField>
													</div>
													<div class="form-row mb-2 gap-2">
														<FormField
															v-slot="{field, invalid}"
															name="sales"
															class="flex-fill"
															:label="$t('cus.revenue')"
															required
															:rules="{ required: true, numeric: true, max: 8, min: 0 }"
														>
															<div class="input-group gap-1">
																<InputNumber maxlength="15" v-bind="field" :invalid />
																<div class="input-group-text">
																	{{ $t('cus.tenThousandYuan') }}
																</div>
															</div>
														</FormField>
														<FormField
															v-slot="{field, invalid}"
															class="flex-fill"
															name="gainLoss"
															:label="$t('cus.profitLoss')"
															required
															:rules="{ required: true, numeric: true, max: 8, min: 0 }"
														>
															<div class="input-group">
																<InputNumber maxlength="15" v-bind="field" :invalid />
																<div class="input-group-text">
																	{{ $t('cus.tenThousandYuan') }}
																</div>
															</div>
														</FormField>
														<FormField
															v-slot="{field, invalid}"
															class="flex-fill"
															name="netValue"
															:label="$t('cus.netValue')"
															required
															:rules="{ required: true, numeric: true, max: 8, min: 0 }"
														>
															<div class="input-group">
																<InputNumber maxlength="15" v-bind="field" :invalid />
																<div class="input-group-text">
																	{{ $t('cus.tenThousandYuan') }}
																</div>
															</div>
														</FormField>
														<Button
															class="ms-4 align-self-start"
															:disabled="reqType == 'READONLY'"
															color="info"
															icon
															title="新增"
															data-bs-original-title="新增"
															aria-label="新增"
															@click="insertOwnCompanie()"
														>
															<i class="fa-solid fa-plus" />
														</Button>
													</div>
												</vue-form>
												<table class="table table-RWD table-horizontal-RWD table-bordered">
													<thead>
														<tr>
															<th class="wd-20p">
																{{ $t('cus.year') }}
															</th>
															<th>{{ $t('cus.revenue') }}</th>
															<th>{{ $t('cus.profitLoss') }}</th>
															<th>{{ $t('cus.netValue') }}</th>
															<th>{{ $t('cus.execute') }}</th>
														</tr>
													</thead>
													<tbody>
														<tr v-for="(ownCompanie, i) in ownCompanies">
															<td>{{ ownCompanie.annual }}</td>
															<td>{{ ownCompanie.sales }} {{ $t('cus.tenThousandYuan') }}</td>
															<td>{{ ownCompanie.gainLoss }} {{ $t('cus.tenThousandYuan') }}</td>
															<td>{{ ownCompanie.netValue }} {{ $t('cus.tenThousandYuan') }}</td>
															<td class="text-end">
																<Button
																	color="danger"
																	icon
																	:title="$t('cus.delete')"
																	:disabled="reqType == 'READONLY'"
																	@click="deleteOwnCompanie(i)"
																>
																	<i class="bi bi-trash" />
																</Button>
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div v-if="reqType !== 'READONLY'" class="text-end">
								<input
									id="back-basic"
									type="button"
									class="btn btn-lg btn-info"
									value="上一步"
									@click="gotoStep(0)"
								>
								<input
									id="next-other"
									type="button"
									class="btn btn-lg btn-primary"
									value="儲存並下一步"
									@click="saveOwnCompanies()"
								>
							</div>
						</template>
						<template #other>
							<div class="card card-form mb-3">
								<div class="card-header">
									<h4>公司其他資料</h4>
								</div>
								<table class="table table-RWD table-horizontal-RWD table-bordered">
									<tbody>
										<tr>
											<th>{{ $t('cus.employeeCount') }}</th>
											<td>
												<InputNumber v-model="employeeNum" maxlength="10" />
											</td>
										</tr>
										<tr>
											<th>
												<span class="tx-require">{{ $t('cus.isListed') }}</span>
											</th>
											<td>
												<RadioGroup
													v-model="listedYn"
													inline
													:options="optionYn"
													option-label="codeName"
													option-value="codeValue"
												/>
											</td>
										</tr>
										<tr>
											<th class="wd-15p">
												海外分公司
											</th>
											<td>
												<vue-form v-slot="{ errors }" ref="compOverseas">
													<div class="form-check-group">
														<div class="form-group">
															地點:
															<vue-field
																id="place"
																v-model="place"
																name="place"
																type="text"
																class="form-control w-25 mx-1"
																size="20"
																maxlength="13"
																rules="required"
																label="地點"
																:class="{ 'is-invalid': errors.text }"
																data-vv-scope="compOverseas"
															/>
															<span v-show="errors.place" class="text-danger">{{ errors.place }}</span>
															海外分公司:
															<vue-field
																id="overseaName"
																v-model="overseaName"
																name="overseaName"
																type="text"
																class="form-control"
																size="20"
																maxlength="20"
																rules="required"
																:class="{ 'is-invalid': errors.text }"
																label="海外分公司"
																data-vv-scope="compOverseas"
															/>
															<span v-show="errors.overseaName" class="text-danger">{{ errors.overseaName }}</span>
															<Button
																color="info"
																icon
																:disabled="reqType == 'READONLY'"
																data-bs-toggle="tooltip"
																title="新增"
																aria-label="新增"
																@click="addCompOverseas()"
															>
																<i class="fa-solid fa-plus" />
															</Button>
														</div>
													</div>
												</vue-form>
												<table class="table table-RWD table-horizontal-RWD table-bordered">
													<thead>
														<tr>
															<th>地點</th>
															<th>分公司名稱</th>
															<th>執行</th>
														</tr>
													</thead>
													<tbody>
														<tr v-for="(compOversea, i) in compOverseas">
															<td>{{ compOversea.place }}</td>
															<td>{{ compOversea.overseaName }}</td>
															<td class="text-end">
																<button
																	type="button"
																	class="btn btn-danger btn-glow btn-icon"
																	data-bs-toggle="tooltip"
																	:title="$t('cus.delete')"
																	:disabled="reqType == 'READONLY'"
																	@click="deleteCompOversea(i)"
																>
																	<i class="bi bi-trash" />
																</button>
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="text-end">
								<input
									v-if="reqType != 'READONLY'"
									type="button"
									class="btn btn-info btn-lg"
									value="上一步"
									data-bs-toggle="tab"
									@click="gotoStep(1)"
								>
								<input
									v-if="reqType != 'READONLY'"
									type="button"
									class="btn btn-primary btn-lg"
									value="儲存"
									@click="updateCompOtherDatas()"
								>
							</div>
						</template>
					</Stepper>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import _ from 'lodash';
import { Field, Form } from 'vee-validate';
import { nextTick } from 'vue';
export default {
	components: {
		vueField: Field,
		vueForm: Form
	},
	props: {
		cusCode: null
	},
	data: function () {
		return {
			companies: [],
			authYn: null,
			compOwnType: [],
			comId: null,
			// Step 1:公司基本資料
			ownType: null,
			comName: null,
			comEname: null,
			vatNum: null,
			owner: null,
			establishDt: null,
			url: null,
			phone: null,
			contactPerson: null,
			contactPhone: null,
			indusCode: null,
			cunCode: 'TW',
			regCunCode: 'TW',
			zip: null,
			caddress: null,
			note: null,
			capital: null,
			createBy: null,
			// Step 2:公司營運資料
			ownCompanies: [],
			annual: null,
			sales: null,
			gainLoss: null,
			netValue: null,
			// Step 3:公司其他資料
			place: null,
			overseaName: null,
			employeeNum: null,
			listedYn: 'N',
			listedPlace: null,
			compOverseas: [],
			// 下拉選單
			cusIndustriesMenu: [],
			countriesMenu: [],
			// 畫面邏輯用
			reqType: null,
			step: 0,
			optionYn: []
		};
	},
	computed: {
		auth: function () {
			return this.authYn === 'Y';
		},
		steps() {
			return [
				{ slot: 'basic', title: this.$t('cus.basicData') },
				{ slot: 'business', title: this.$t('cus.operationalData'), disabled: this.comId === null },
				{ slot: 'other', title: this.$t('cus.otherData'), disabled: this.comId === null }
			];
		}
	},
	mounted: async function () {
		const self = this;
		self.getCompanies();
		self.compOwnType = await self.getAdmCodeDetail('COMP_OWN_TYPE');
		self.getIndustriesMenu();
		self.getCountriesMenu();
		self.optionYn = await self.getAdmCodeDetail('OPTION_YN');
		self.chkCustomerAuth();
	},
	methods: {
		getCompanies: async function () {
			const self = this;
			const ret = await self.$api.getCompaniesApi({
				cusCode: self.cusCode
			});
			self.companies = ret.data;
		},
		getIndustriesMenu: async function () {
			const self = this;
			self.cusIndustriesMenu = await self.getAdmCodeDetail('CUS_INDUSTRIES');
		},
		getCountriesMenu: async function () {
			const self = this;
			const ret = await self.$api.getCountriesMenuApi();
			self.countriesMenu = ret.data;
		},
		gotoStep: function (step) {
			const self = this;
			if (step !== 0 && self.comId == null) {
				self.$bi.alert(self.$t('cus.pleaseCreateOrSelectCompany'));
				return;
			}

			if (step == 1) {
				self.getOperationDatas();
			}
			else if (step == 2) {
				self.getCompOverseas();
			}
			self.step = step;
		},
		doInsertCompany: function () {
			const self = this;
			self.comId = null;
			self.clearData();
			self.reqType = 'INSERT';
			self.gotoStep(0);
			self.maskInputComponents(); // 遮罩輸入類型元件

			if (self.$refs.companyBasicInfo != null) {
				setTimeout(function () {
					self.$refs.companyBasicInfo.resetForm();
				}, 50);
			}
		},
		doUpdateCompany: function (company, reqType) {
			const self = this;
			self.clearData();
			self.reqType = reqType; // READONLY: 唯獨模式, UPDATE: 編輯模式
			self.comId = company.comId;
			self.ownType = company.ownType;
			self.comName = company.comName;
			self.comEname = company.comEname;
			self.vatNum = company.vatNum;
			self.owner = company.owner;
			self.establishDt = company.establishDt;
			self.url = company.url;
			self.phone = company.phone;
			self.contactPerson = company.contactPerson;
			self.contactPhone = company.contactPhone;
			self.indusCode = company.indusCode;
			self.cunCode = company.cunCode;
			self.regCunCode = company.regCunCode;
			self.zip = company.zip;
			self.caddress = company.caddress;
			self.note = company.note;
			self.capital = company.capital;
			self.createBy = company.createBy;

			self.employeeNum = company.employeeNum;
			self.listedYn = company.listedYn ?? 'N';
			self.listedPlace = company.listedPlace;

			self.annual = null;
			self.sales = null;
			self.gainLoss = null;
			self.netValue = null;
			self.place = null;
			self.overseaName = null;

			self.gotoStep(0);
			self.maskInputComponents(); // 遮罩輸入類型元件
		},
		saveCompany: function () {
			const self = this;

			if (self.reqType == 'INSERT') {
				self.insertCompany();
			}
			else if (self.reqType == 'UPDATE') {
				self.updateCompany();
			}
		},
		insertCompany: async function () {
			const self = this;
			const companyBasicInfo = self.$refs.companyBasicInfo;
			companyBasicInfo.validate().then(async function (pass) {
				if (Number(self.employeeNum) && !(self.employeeNum % 1 === 0 && self.employeeNum > 0)) {
					companyBasicInfo.setFieldError('employeeNum', self.$t('cus.employeeCountMustBePositiveInteger'));
					return;
				}
				if (pass.valid) {
					const ret = await self.$api.postCompanyApi({
						cusCode: self.cusCode,
						ownType: self.ownType,
						comName: self.comName,
						comEname: self.comEname,
						vatNum: self.vatNum,
						owner: self.owner,
						establishDt: _.formatDate(self.establishDt),
						url: self.url,
						phone: self.phone,
						contactPerson: self.contactPerson,
						contactPhone: self.contactPhone,
						indusCode: self.indusCode,
						cunCode: self.cunCode,
						regCunCode: self.regCunCode,
						zip: self.zip,
						caddress: self.caddress,
						employeeNum: self.employeeNum,
						listedYn: self.listedYn,
						note: self.note,
						capital: self.capital
					});
					self.comId = ret.data;
					self.$bi.alert(self.$t('cus.addSuccessful'));
					self.reqType = 'UPDATE';
					self.getCompanies();
					self.gotoStep(1);
				}
			});
		},
		updateCompany: async function () {
			const self = this;
			const pass = await self.$refs.companyBasicInfo.validate();
			if (Number(self.employeeNum) && !(self.employeeNum % 1 === 0 && self.employeeNum > 0)) {
				self.errors.add({
					field: 'companyBasicInfo.employeeNum',
					msg: '員工人數須為正整數。'
				});
				return;
			}

			if (!pass.valid) return;
			await self.$api.patchCompanyApi({
				comId: self.comId,
				cusCode: self.cusCode,
				ownType: self.ownType,
				comName: self.comName,
				comEname: self.comEname,
				vatNum: self.vatNum,
				owner: self.owner,
				establishDt: _.formatDate(self.establishDt),
				url: self.url,
				phone: self.phone,
				contactPerson: self.contactPerson,
				contactPhone: self.contactPhone,
				indusCode: self.indusCode,
				cunCode: self.cunCode,
				regCunCode: self.regCunCode,
				zip: self.zip,
				caddress: self.caddress,
				employeeNum: self.employeeNum,
				listedYn: self.listedYn,
				note: self.note,
				capital: self.capital
			});
			self.$bi.alert(self.$t('cus.updateSuccessful'));
			self.getCompanies();
			self.gotoStep(1);
		},
		deleteCompany: async function (company) {
			const self = this;
			self.$bi.confirm(self.$t('cus.confirmDeleteAllCompanyData'), {
				event: {
					confirmOk: async function () {
						const ret = await self.$api.deleteCompanyApi({
							comId: company.comId,
							cusCode: self.cusCode
						});
						self.$bi.alert(self.$t('cus.deleteSuccessful'));
						self.getCompanies();
						self.clearData();
						self.reqType = null;
					}
				}
			});
		},
		clearData: function () {
			const self = this;
			self.comId = null;
			self.ownType = null;
			self.comName = null;
			self.comEname = null;
			self.vatNum = null;
			self.owner = null;
			self.establishDt = null;
			self.url = null;
			self.phone = null;
			self.contactPerson = null;
			self.contactPhone = null;
			self.indusCode = null;
			self.cunCode = null;
			self.regCunCode = null;
			self.zip = null;
			self.caddress = null;
			self.employeeNum = null;
			self.listedYn = null;
			self.note = null;
			self.capital = null;
			self.createBy = null;
			self.annual = null;
			self.sales = null;
			self.gainLoss = null;
			self.netValue = null;
			self.place = null;
			self.overseaName = null;
		},
		getOperationDatas: async function () {
			const self = this;
			const ret = await self.$api.getOwnCompanies({
				comId: self.comId
			});
			self.ownCompanies = ret.data;
		},
		deleteOwnCompanie: function (i) {
			const self = this;
			self.ownCompanies.splice(i, 1);
		},
		insertOwnCompanie: function () {
			const self = this;
			self.$refs.ownCompaniesWatch.validate().then(function (pass) {
				if (pass.valid) {
					self.ownCompanies.push({
						annual: self.annual,
						sales: self.sales,
						gainLoss: self.gainLoss,
						netValue: self.netValue
					});

					self.annual = null;
					self.sales = null;
					self.gainLoss = null;
					self.netValue = null;

					setTimeout(function () {
						self.$refs.ownCompaniesWatch.resetForm();
					}, 50);
				}
			});
		},
		saveOwnCompanies: async function () {
			const self = this;
			await self.$api.saveOwnCompaniesApi({
				comId: self.comId,
				ownComList: self.ownCompanies
			});
			self.$bi.alert(self.$t('cus.saveSuccessful'));
			self.gotoStep(2);
		},
		getCompOverseas: async function () {
			const self = this;
			const resp = await self.$api.getCompOverseasApi({
				comId: self.comId
			});
			self.compOverseas = resp.data;
		},
		addCompOverseas: async function () {
			const self = this;
			const pass = await self.$refs.compOverseas.validate();
			if (!pass.valid) return;
			self.compOverseas.push({
				place: self.place,
				overseaName: self.overseaName
			});

			self.place = null;
			self.overseaName = null;
			await nextTick();
			self.$refs.compOverseas.resetForm();
		},
		deleteCompOversea: function (i) {
			const self = this;
			self.compOverseas.splice(i, 1);
		},
		updateCompOtherDatas: async function () {
			const self = this;
			await Promise.all([
				self.$api.patchCompanyApi({
					comId: self.comId,
					cusCode: self.cusCode,
					ownType: self.ownType,
					comName: self.comName,
					comEname: self.comEname,
					vatNum: self.vatNum,
					owner: self.owner,
					establishDt: _.formatDate(self.establishDt),
					url: self.url,
					phone: self.phone,
					contactPerson: self.contactPerson,
					contactPhone: self.contactPhone,
					indusCode: self.indusCode,
					cunCode: self.cunCode,
					regCunCode: self.regCunCode,
					zip: self.zip,
					caddress: self.caddress,
					employeeNum: self.employeeNum,
					listedYn: self.listedYn,
					note: self.note,
					capital: self.capital
				}),
				self.$api.updateCompOtherDatasApi({
					comId: self.comId,
					overseaList: self.compOverseas
				})
			]);
			self.$bi.alert(self.$t('cus.updateSuccessful'));
			self.getCompanies();
			self.getCompOverseas();
			self.clearData();
			self.reqType = null;
		},
		// 全面遮罩 UI 輸入類型元件
		maskInputComponents: function () {
			const self = this;
			// Code that will run only after the entire view has been re-rendered
			self.$nextTick(function () {
				if (self.reqType == 'READONLY') {
					// 唯讀模式
					$('input, textarea').prop('readonly', true);
					$('input').prop('disabled', true);
					$('select').prop('disabled', true);
				}
				else {
					$('input, textarea').prop('readonly', false);
					$('input').prop('disabled', false);
					$('select').prop('disabled', false);
				}
			});
		},
		chkCustomerAuth: async function () {
			const self = this;
			const resp = await self.$api.chkCustomerAuthApi({
				cusCode: self.cusCode,
				progCode: 'ACUS_001'
			});
			self.authYn = resp.data.authYn;
		},
		getAdmCodeDetail: async function (codeType) {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: codeType
			});
			return ret.data;
		},
		openCompanyWebsite(url) {
			if (!url) return;
			window.open(url, '_blank', 'noopener noreferrer');
		}
	}
};
</script>
