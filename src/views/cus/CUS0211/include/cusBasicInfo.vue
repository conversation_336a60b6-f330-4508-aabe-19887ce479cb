<template>
	<div>
		<div class="tab-content">
			<div v-if="isNatural" class="tab-pane fade show active">
				<!-- 基本資料 -->
				<div id="section1" class="card card-table card-collapse mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.customerBasicInfo') }}</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup" />
					</div>
					<div id="collapseListGroup" class="collapse show">
						<table class="biv-table table table-RWD table-horizontal-RWD">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('cus.namePassportEnglishName') }}
									</th>
									<td>{{ customerDetail.cusName || '--' }}</td>
									<th class="wd-15p">
										{{ $t('cus.idNumber') }}
									</th>
									<td>{{ customerDetail.idn || '--' }}</td>
									<th class="wd-15p">
										{{ $t('cus.gender') }}
									</th>
									<td>{{ customerDetail.genderName || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.birthday') }}</th>
									<td>{{ customerDetail.birth || '--' }}</td>
									<th>{{ $t('cus.age') }}</th>
									<td>{{ customerDetail.age || '--' }}</td>
									<th>{{ $t('cus.education') }}</th>
									<td>{{ customerDetail.eduName || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.occupation') }}</th>
									<td>{{ customerDetail.ocuName || '--' }}</td>
									<th>{{ $t('cus.jobTitle') }}</th>
									<td>{{ customerDetail.titleName || '--' }}</td>
									<th>{{ $t('cus.serviceOrganization') }}</th>
									<td>{{ customerDetail.employer || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.personalAnnualIncome') }}</th>
									<td>{{ customerDetail.income || '--' }}</td>
									<th>{{ $t('cus.maritalStatus') }}</th>
									<td>{{ customerDetail.maritalName || '--' }}</td>
									<th>{{ $t('cus.personalDataProtectionAct') }}</th>
									<td>{{ customerDetail.perSecuLawSignYn || '--' }}({{ customerDetail.perSecuLawSignDt || '--' }})</td>
								</tr>
								<tr>
									<th>{{ $t('cus.obuDbuCustomer') }}</th>
									<td>{{ customerDetail.buName || '--' }}</td>
									<th>{{ $t('cus.isEmployee') }}</th>
									<td>{{ customerDetail.employeesYn || '--' }}</td>
									<th>{{ $t('cus.earliestAccountOpeningDate') }}</th>
									<td>{{ customerDetail.iniOpenDt || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.anyComplaints') }}</th>
									<td>{{ customerDetail.compYn || '--' }}</td>
									<th>{{ $t('cus.agreeToTelemarketing') }}</th>
									<td>{{ customerDetail.mktPhoneYn || '--' }}</td>
									<th>{{ $t('cus.priorityNumberMarking') }}</th>
									<td>{{ customerDetail.priorityGetnumYn || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.customerVipLevel') }}</th>
									<td>{{ customerDetail.graName || '--' }}</td>
									<th>{{ $t('cus.moneyLaunderingRiskLevel') }}</th>
									<td>{{ customerDetail.kycRiskYn || '--' }}</td>
									<th>{{ $t('cus.warningAccountMarking') }}</th>
									<td>{{ customerDetail.warnYnwarnedHouseholdsYn || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.legalRepresentative') }}</th>
									<td>{{ customerDetail.legalRpstName || '--' }}({{ customerDetail.legalRpstPhone || '--' }})</td>
									<th>{{ $t('cus.legalRepresentative2') }}</th>
									<td>{{ customerDetail.legalRpstName2 || '--' }}({{ customerDetail.legalRpstPhone2 || '--' }})</td>
									<th>{{ $t('cus.alertAccountMarking') }}</th>
									<td>{{ customerDetail.watchListedAccountYn || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.wealthManagementBranch') }}</th>
									<td>{{ customerDetail.branName || '--' }}</td>
									<th>{{ $t('cus.financialAdvisor') }}</th>
									<td>{{ customerDetail.userName || '--' }}</td>
									<th>{{ $t('cus.onlineBankingOpened') }}</th>
									<td>{{ customerDetail.openOnlineYn || '--' }}</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!-- 聯絡資料 -->
				<div id="section2" class="card card-table card-collapse mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.contactInfo') }}</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2" />
					</div>
					<div id="collapseListGroup2" class="collapse show">
						<table class="biv-table table table-RWD table-horizontal-RWD">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('cus.phone1') }}
									</th>
									<td>{{ customerDetail.phoneH || '--' }}</td>
									<th class="wd-15p">
										{{ $t('cus.phone2') }}
									</th>
									<td>{{ customerDetail.phoneC || '--' }}</td>
									<th class="wd-15p">
										{{ $t('cus.mobilePhone') }}
									</th>
									<td>{{ customerDetail.cellphoneM || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.email') }}</th>
									<td>{{ customerDetail.email || '--' }}</td>
									<th>{{ $t('cus.preferredContactMethod') }}</th>
									<td colspan="4">
										{{ customerDetail.contactText || '--' }}
									</td>
								</tr>
								<tr>
									<th>{{ $t('cus.registeredAddress') }}</th>
									<td colspan="5">
										{{ customerDetail.addrFullH || '--' }}
									</td>
								</tr>
								<tr>
									<th>{{ $t('cus.contactAddress') }}</th>
									<td colspan="5">
										{{ customerDetail.addrFullC || '--' }}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!-- 重要聯絡註記 -->
				<div id="section3" class="card card-table card-collapse mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.importantContactNotes') }}</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup3" />
					</div>
					<div id="collapseListGroup3" class="collapse show">
						<table class="biv-table table table-RWD table-horizontal-RWD">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('cus.bankPhoneSalesMarking') }}
									</th>
									<td>{{ customerDetail.bkPhoneNote || '--' }}</td>
									<th class="wd-15p">
										{{ $t('cus.bankSmsSalesMarking') }}
									</th>
									<td>{{ customerDetail.bkEmailNote || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.bankEmailSalesMarking') }}</th>
									<td>{{ customerDetail.bkSmsNote || '--' }}</td>
									<th>{{ $t('cus.bankDmMailingMarking') }}</th>
									<td>{{ customerDetail.bkDmNote || '--' }}</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!-- 經管AO資訊 -->
				<div id="section4" class="card card-table card-collapse mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.accountOfficerInfo') }}</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup4" />
					</div>
					<div id="collapseListGroup4" class="collapse show">
						<table class="bih-table table table-RWD">
							<thead>
								<tr>
									<th>{{ $t('cus.managementDate') }}</th>
									<th>{{ $t('cus.managementBranch') }}</th>
									<th>{{ $t('cus.accountManager') }}</th>
									<th>{{ $t('cus.aoCode') }}</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td />
									<td />
									<td />
									<td />
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!-- AOCODE最近一次異動記錄 -->
				<div id="section5" class="card card-table card-collapse mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.recentAoCodeChangeRecord') }}</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup5" />
					</div>
					<div id="collapseListGroup5" class="collapse show">
						<table class="bih-table table table-RWD">
							<thead>
								<tr>
									<th>{{ $t('cus.previousManagementBranch') }}</th>
									<th>{{ $t('cus.previousManagementPersonnel') }}</th>
									<th>{{ $t('cus.previousAoCode') }}</th>
									<th>{{ $t('cus.changeDate') }}</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>{{ cusAoHistory.outBranCode || '--' }} {{ cusAoHistory.outBranName || '--' }}</td>
									<td>{{ cusAoHistory.outUserName || '--' }}</td>
									<td>{{ cusAoHistory.outUserCode || '--' }}</td>
									<td>{{ cusAoHistory.stdDt || '--' }}</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!-- 行銷專案 -->
				<div id="section6" class="card card-table card-collapse mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.marketingProjects') }}</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup6" />
					</div>
					<div id="collapseListGroup6" class="collapse show">
						<table class="bih-table table table-RWD">
							<thead>
								<tr>
									<th>{{ $t('cus.projectName') }}</th>
									<th>{{ $t('cus.assignedPersonnel') }}</th>
									<th>{{ $t('cus.contactResult') }}</th>
									<th>{{ $t('cus.contactTime') }}</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td />
									<td />
									<td />
									<td />
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!-- 理財屬性資料 -->
				<div id="section7" class="card card-table card-collapse mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.financialAttributes') }}</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup7" />
					</div>
					<div id="collapseListGroup7" class="collapse show">
						<table class="biv-table table table-RWD table-horizontal-RWD">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('cus.aum') }}
									</th>
									<td>{{ customerDetail.aua1M || '--' }}</td>
									<th class="wd-15p">
										{{ $t('cus.professionalInvestor') }}
									</th>
									<td>{{ customerDetail.piLegalType || '--' }}({{ customerDetail.piSignDt || '--' }})</td>
								</tr>
								<tr>
									<th>{{ $t('cus.customerInvestmentProfile') }}</th>
									<td>{{ customerDetail.rankName || '--' }}</td>
									<th>{{ $t('cus.questionnaireEffectiveDate') }}</th>
									<td>{{ customerDetail.rankQueDt || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.customerRiskExpiryDate') }}</th>
									<td>{{ customerDetail.nextRankQueDt || '--' }}</td>
									<th>{{ $t('cus.minorWealthManagementConsent') }}</th>
									<td>{{ customerDetail.childInvYn || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.specificTrustInvestmentConsent') }}</th>
									<td>{{ customerDetail.recommStatus || '--' }}</td>
									<th>{{ $t('cus.highYieldBondRiskNotice') }}</th>
									<td>{{ customerDetail.invBondNotiYn || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.holdsCreditCard') }}</th>
									<td>{{ customerDetail.cardYn || '--' }}</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<div v-else class="tab-pane fade show active">
				<!-- 基本資料 -->
				<div id="section1" class="card card-table card-collapse mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.customerBasicInfo') }}</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup" />
					</div>
					<div id="collapseListGroup" class="collapse show">
						<table class="biv-table table table-RWD table-horizontal-RWD">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('cus.companyName') }}
									</th>
									<td>{{ customerDetail.cusName || '--' }}</td>
									<th class="wd-15p">
										{{ $t('cus.companyEnglishName') }}
									</th>
									<td>{{ customerDetail.cusEname || '--' }}</td>
									<th class="wd-15p">
										{{ $t('cus.unifiedBusinessNumber') }}
									</th>
									<td>{{ customerDetail.idn || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.companyEstablishmentDate') }}</th>
									<td>{{ customerDetail.birth || '--' }}</td>
									<th>{{ $t('cus.businessLocation') }}</th>
									<td>{{ customerDetail.businessLocation || '--' }}</td>
									<th>{{ $t('cus.earliestAccountOpeningDate') }}</th>
									<td>{{ customerDetail.iniOpenDt || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.industryClassificationNumber') }}</th>
									<td>{{ customerDetail.ocuName || '--' }}</td>
									<th>{{ $t('cus.businessType') }}</th>
									<td>{{ customerDetail.orgtypeName || '--' }}</td>
									<th>{{ $t('cus.smeMarking') }}</th>
									<td>{{ customerDetail.smeYn || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.companyPhonePhone1') }}</th>
									<td>{{ customerDetail.phoneC || '--' }}</td>
									<th>{{ $t('cus.homePhonePhone2') }}</th>
									<td>{{ customerDetail.phoneH || '--' }}</td>
									<th>{{ $t('cus.mobilePhone') }}</th>
									<td>{{ customerDetail.cellphoneM || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.representativeId') }}</th>
									<td>{{ customerDetail.phoneC || '--' }}</td>
									<th>{{ $t('cus.representativeName') }}</th>
									<td>{{ customerDetail.phoneH || '--' }}</td>
									<th>{{ $t('cus.gender') }}</th>
									<td>{{ customerDetail.genderName || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.obuDbuCustomer') }}</th>
									<td>{{ customerDetail.buName || '--' }}</td>
									<th>{{ $t('cus.wealthManagementBranch') }}</th>
									<td>{{ customerDetail.branName || '--' }}</td>
									<th>{{ $t('cus.financialAdvisor') }}</th>
									<td>{{ customerDetail.userName || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.customerVipLevel') }}</th>
									<td>{{ customerDetail.graName || '--' }}</td>
									<th>{{ $t('cus.email') }}</th>
									<td>{{ customerDetail.email || '--' }}</td>
									<th />
									<td />
								</tr>
								<tr>
									<th>{{ $t('cus.groupAffiliation') }}</th>
									<td />
									<th>{{ $t('cus.authorizedPersonId') }}</th>
									<td />
									<th>{{ $t('cus.authorizedPersonName') }}</th>
									<td />
								</tr>
								<tr>
									<th>{{ $t('cus.registeredAddressCompany') }}</th>
									<td>{{ customerDetail.addrFullH || '--' }}</td>
									<th />
									<td />
									<th />
									<td />
								</tr>
								<tr>
									<th>{{ $t('cus.contactAddress') }}</th>
									<td>{{ customerDetail.addrFullC || '--' }}</td>
									<th />
									<td />
									<th />
									<td />
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!-- 重要聯絡註記 -->
				<div id="section3" class="card card-table card-collapse mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.importantContactNotes') }}</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup3" />
					</div>
					<div id="collapseListGroup3" class="collapse show">
						<table class="biv-table table table-RWD table-horizontal-RWD">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('cus.bankPhoneSalesMarking') }}
									</th>
									<td>{{ customerDetail.bkPhoneNote || '--' }}</td>
									<th class="wd-15p">
										{{ $t('cus.bankSmsSalesMarking') }}
									</th>
									<td>{{ customerDetail.bkEmailNote || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.bankEmailSalesMarking') }}</th>
									<td>{{ customerDetail.bkSmsNote || '--' }}</td>
									<th>{{ $t('cus.bankDmMailingMarking') }}</th>
									<td>{{ customerDetail.bkDmNote || '--' }}</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!-- 理財屬性資料 -->
				<div id="section7" class="card card-table card-collapse mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.financialAttributes') }}</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup7" />
					</div>
					<div id="collapseListGroup7" class="collapse show">
						<table class="biv-table table table-RWD table-horizontal-RWD">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('cus.aum') }}
									</th>
									<td>{{ customerDetail.aua1M || '--' }}</td>
									<th class="wd-15p">
										{{ $t('cus.professionalInvestor') }}
									</th>
									<td>{{ customerDetail.piLegalType || '--' }}({{ customerDetail.piSignDt || '--' }})</td>
								</tr>
								<tr>
									<th>{{ $t('cus.customerInvestmentProfile') }}</th>
									<td>{{ customerDetail.rankName || '--' }}</td>
									<th>{{ $t('cus.questionnaireEffectiveDate') }}</th>
									<td>{{ customerDetail.rankQueDt || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.customerRiskExpiryDate') }}</th>
									<td>{{ customerDetail.nextRankQueDt || '--' }}</td>
									<th>{{ $t('cus.minorWealthManagementConsent') }}</th>
									<td>{{ customerDetail.childInvYn || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.specificTrustInvestmentConsent') }}</th>
									<td>{{ customerDetail.recommStatus || '--' }}</td>
									<th>{{ $t('cus.highYieldBondRiskNotice') }}</th>
									<td>{{ customerDetail.invBondNotiYn || '--' }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.holdsCreditCard') }}</th>
									<td>{{ customerDetail.cardYn || '--' }}</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!-- 經管AO資訊 -->
				<div id="section4" class="card card-table card-collapse mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.accountOfficerInfo') }}</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup4" />
					</div>
					<div id="collapseListGroup4" class="collapse show">
						<table class="bih-table table table-RWD">
							<thead>
								<tr>
									<th>{{ $t('cus.managementDate') }}</th>
									<th>{{ $t('cus.managementBranch') }}</th>
									<th>{{ $t('cus.managementPersonnel') }}</th>
									<th>{{ $t('cus.aoCode') }}</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td />
									<td />
									<td />
									<td />
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!-- AOCODE最近一次異動記錄 -->
				<div id="section5" class="card card-table card-collapse mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.recentAoCodeChangeRecord') }}</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup5" />
					</div>
					<div id="collapseListGroup5" class="collapse show">
						<table class="bih-table table table-RWD">
							<thead>
								<tr>
									<th>{{ $t('cus.previousManagementBranch') }}</th>
									<th>{{ $t('cus.previousManagementPersonnel') }}</th>
									<th>{{ $t('cus.previousAoCode') }}</th>
									<th>{{ $t('cus.changeDate') }}</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>{{ cusAoHistory.outBranCode || '--' }} {{ cusAoHistory.outBranName || '--' }}</td>
									<td>{{ cusAoHistory.outUserName || '--' }}</td>
									<td>{{ cusAoHistory.outUserCode || '--' }}</td>
									<td>{{ cusAoHistory.stdDt || '--' }}</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!-- 行銷專案 -->
				<div id="section6" class="card card-table card-collapse mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.marketingProjects') }}</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup6" />
					</div>
					<div id="collapseListGroup6" class="collapse show">
						<table class="bih-table table table-RWD">
							<thead>
								<tr>
									<th>{{ $t('cus.projectName') }}</th>
									<th>{{ $t('cus.assignedPersonnel') }}</th>
									<th>{{ $t('cus.contactResult') }}</th>
									<th>{{ $t('cus.contactTime') }}</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td />
									<td />
									<td />
									<td />
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
export default {
	filters: {},
	props: {
		cusCode: null,
		roleCode: null,
		userCode: null,
		hasAuth: Boolean
	},
	data: function () {
		return {
			customerDetail: {},
			cusAoHistory: {}
		};
	},
	computed: {
		isNatural: function () {
			return this.customerDetail.idnEntityType === 'N';
		}
	},
	beforeMount: function () { },
	created: function () { },
	mounted: function () {
		const self = this;
		self.getCustomerInfo();
		self.getCusAoHistory();
	},
	methods: {
		getCustomerInfo: async function () {
			const self = this;
			const resp = await self.$api.getCustomerInfoApi({
				cusCode: self.cusCode
			});
			if (resp.data == null) {
				self.$bi.alert(self.$t('cus.noCustomerFound'));
			}
			else {
				self.customerDetail = resp.data;
			}
		},
		getCusAoHistory: async function () {
			const self = this;
			const url = _.toPageUrl('', 0, {
				page: 0,
				size: 1,
				sort: 'STD_DT',
				direction: 'DESC'
			});
			const resp = await self.$api.getCusAoHistoryApi(
				{
					cusCode: self.cusCode
				},
				url
			);
			self.cusAoHistory = resp.data.content[0] || {};
		},
		iconClass: function (yn) {
			return yn === 'Y' ? 'icon-yes' : 'icon-no';
		}
	}
};
</script>
