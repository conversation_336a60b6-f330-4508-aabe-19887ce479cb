<template>
	<vue-cus-bank-statement v-if="tabCode == 1" ref="bankStatement" :cus-code="cusCode" />
</template>
<script>
import vueCusBankStatement from './bankStatementReport.vue';
export default {
	components: {
		vueCusBankStatement
	},
	props: {
		cusCode: null,
		hasAuth: <PERSON><PERSON><PERSON>,
		customer: Object
	},
	data: function () {
		return {
			// 畫面邏輯判斷用參數
			customTitle: null,
			tabCode: 0,

			userName: '', // 操作者姓名
			userRoleName: '', // 操作者角色名稱
			queryDt: null, // 查詢時間
			dataDt: null // 資料時間
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo'])
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				const self = this;
				if (newVal) {
					if (newVal.deputyUserName) {
						self.userName = newVal.deputyUserName;
					}
					else {
						self.userName = newVal.userName;
					}
					self.userRoleName = newVal.roleName;
				}
			}
		}
	},
	mounted: function () {
		const self = this;
		if (self.hasAuth) {
			self.tabCode = 1;
		}
		else {
			self.$bi.alert(self.$t('cus.customerNotUnderYourCenter'));
		}
	},
	methods: {
		changeTab: function (tabCode) {
			const self = this;
			self.tabCode = tabCode;
		}
	}
};
</script>
