<template>
	<div>
		<div>
			<div class="row">
				<div class="col-12">
					<div class="text-end">
						<button
							name="Submit1"
							type="button"
							class="btn btn-primary"
							@click="generateCuaAssetReport()"
						>
							{{ $t('cus.generateIntegratedAssetReport') }}
						</button>
					</div>
					<div class="card card-table mt-3 table-responsive">
						<div class="card-header">
							<h4>{{ $t('cus.integratedAssetReport') }}</h4>
						</div>
						<table class="table">
							<thead>
								<tr>
									<th>{{ $t('cus.generatedDate') }}</th>
									<th class="wd-100 text-center">
										{{ $t('cus.view') }}
									</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in cusFiles">
									<td>{{ item.createDt }}</td>
									<td class="text-center">
										<button
											type="button"
											class="btn btn-dark btn-icon"
											:title="$t('cus.view')"
											data-bs-toggle="tooltip"
											@click="downloadFile(item)"
										>
											<i class="bi bi-search" />
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import moment from 'moment';

export default {
	props: {
		cusCode: String
	},
	data: function () {
		return {
			cusFiles: []
		};
	},
	mounted: function () {
		if (this.cusCode) {
			this.getFiles();
		}
	},
	methods: {
		getFiles: async function () {
			const ret = await this.$api.getCusFilesList({
				cusCode: this.cusCode,
				fileType: 'AST'
			});
			this.cusFiles = ret.data;
		},
		downloadFile: async function (file) {
			await this.$api.previewFileApi({ fileType: 'CusFiles', fileId: file.fileId });
		},
		generateCuaAssetReport: async function () {
			if (this.cusFiles.some(cf => moment().isSame(cf.createDt, 'day'))) {
				this.$bi.alert(this.$t('cus.todayReportAlreadyGenerated'));
			}
			else {
				await this.$api.generateCuaAssetReportApi({	cusCode: this.cusCode });
				this.getFiles();
			}
		}
	}
};
</script>
