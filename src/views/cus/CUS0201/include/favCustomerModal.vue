<template>
	<vue-modal :is-open="isOpenModal" @close="closeModal">
		<template #content="props">
			<div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('cus.keyCustomerSetup') }}
						</h4>
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click="props.close()"
						/>
					</div>
					<div class="modal-body">
						<div class="card card-form">
							<vue-form v-slot="{ errors, validate }" ref="favCustomerModal">
								<div class="card-header">
									<h4>{{ $t('cus.pleaseEnterFollowingData') }}</h4>
									<span class="tx-square-bracket">{{ $t('cus.requiredField') }}</span>
								</div>
								<div class="card-body">
									<div class="row g-3 align-items-end">
										<div class="col-lg-6">
											<label class="form-label tx-require">{{ $t('cus.customGroupName') }}</label>
											<vue-field
												id="txtGroupName"
												v-model="groupName"
												name="groupName"
												class="form-control"
												type="text"
												size="30"
												rules="required"
												:class="{ 'is-invalid': errors.groupName }"
												:label="$t('cus.groupName')"
											/>
										</div>
										<div class="col-lg-3">
											<button id="btnInsGroupName" class="btn btn-primary btn-glow btn-save" @click="insertGroup()">
												{{ $t('cus.save') }}
											</button>
										</div>
										<div class="col-10" style="height: 3px">
											<span v-show="errors.groupName" class="text-danger">{{ errors.groupName }}</span>
										</div>
									</div>
								</div>
							</vue-form>
						</div>

						<div v-if="doUpdateCode == null" id="searchRusult">
							<div class="card card-table">
								<div class="card-header">
									<h4>{{ $t('cus.keyCustomerGroup') }}</h4>
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-hover">
										<thead>
											<tr>
												<th width="50%">
													{{ $t('cus.customGroupName') }}
												</th>
												<th width="50%" class="text-end">
													{{ $t('cus.execute') }}
												</th>
											</tr>
										</thead>
										<tbody id="wrapperList">
											<tr v-for="cusGroupData in cusGroups">
												<td id="grpName1" :data-th="$t('cus.customGroupName')">
													{{ cusGroupData.groupName }}
												</td>
												<td :data-th="$t('cus.execute')" class="text-end">
													<button
														type="button"
														class="btn btn-info btn-glow btn-icon"
														data-bs-toggle="tooltip"
														:data-bs-original-title="$t('cus.edit')"
														@click="doUpdateGroup(cusGroupData)"
													>
														<i class="bi bi-pen" />
													</button>
													<button
														type="button"
														class="btn btn-danger btn-glow btn-icon"
														data-bs-toggle="tooltip"
														:data-bs-original-title="$t('cus.delete')"
														@click="deleteGroup(cusGroupData.groupCode)"
													>
														<i class="bi bi-trash" />
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
							<div class="tx-note">
								{{ $t('cus.keyCustomerSetupNote') }}
							</div>
							<div class="modal-footer">
								<button
									type="button"
									class="btn-close"
									aria-label="Close"
									@click="props.close()"
								/>
							</div>
						</div>

						<vue-fav-cus-setup
							:title="$t('cus.editKeyCustomer')"
							:group-name="groupName"
							:do-update-code="doUpdateCode"
							:refresh-update-code="refreshUpdateCode"
						/>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
</template>
<script>
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import vueFavCusSetup from './favCusSetup.vue';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		vueFavCusSetup
	},
	props: {
		refreshFun: Function // 外部程式刷新功能
	},
	data: function () {
		return {
			isOpenModal: true,
			// API 用參數
			groupName: null,

			graCode: null,
			idn: null,
			selectCusCodes: [],

			// 畫面邏輯用參數
			doUpdateCode: null,
			queryCusCodes: [],
			// 下拉選單
			aumMenu: [],
			// 畫面顯示用參數
			cusGroups: [],

			// 主要顯示資料
			queryCus: [],
			selectCus: [],
			pageable: {
				page: 0,
				size: 5000,
				sort: 'CUS_CODE',
				direction: 'ASC'
			}
		};
	},
	mounted: function () {
		const self = this;
		self.getCusGroup();
	},
	methods: {
		getCusGroup: async function () {
			const self = this;
			const ret = await self.$api.getCusGroupMenuApi();
			self.cusGroups = ret.data;
		},
		insertGroup: async function () {
			const self = this;
			self.$refs.favCustomerModal.validate().then(async function (pass) {
				if (pass.valid) {
					const ret = await self.$api.postGroupApi({ groupName: self.groupName });
					self.$bi.alert(self.$t('cus.addSuccess'));
					self.getCusGroup();
				}
			});
		},
		doUpdateGroup: function (cusGroupData) {
			const self = this;
			self.doUpdateCode = cusGroupData.groupCode;
			self.groupName = cusGroupData.groupName;
		},
		deleteGroup: function (groupCode) {
			const self = this;
			self.$bi.confirm(self.$t('cus.confirmDeleteRecord'), {
				event: {
					confirmOk: async function () {
						const ret = await self.$api.deleteGroupApi({
							groupCode: groupCode
						});
						self.$bi.alert(self.$t('cus.deleteSuccess'));
						self.getCusGroup();
					}
				}
			});
		},
		refreshUpdateCode: function (doUpdateCode) {
			const self = this;
			self.doUpdateCode = doUpdateCode;
		},
		refreshOutside: function () {
			const self = this;
			if (self.refreshFun) {
				self.refreshFun();
			}
		},
		closeModal: function () {
			this.isOpenModal = false;
		}
	}
};
</script>
