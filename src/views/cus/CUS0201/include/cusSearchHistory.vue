<template>
	<div class="col-lg-4 ps-lg-3">
		<div class="d-none d-lg-block">
			<div class="d-grid gap-2 mb-3">
				<button
					class="btn btn-lg btn-dark btn-glow tx-16 tx-bold d-flex justify-content-between"
					type="button"
					@click="redirect()"
				>
					<span><img :src="getImgURL('icon', 'ico-multi.png')">{{ $t('cus.comprehensiveSearch') }}</span> <i class="bi bi-shuffle" />
				</button>
			</div>
		</div>
		<div class="card card-table">
			<div class="card-header">
				<h4>{{ $t('cus.searchHistoryList') }}</h4>
				<span class="tx-square-bracket">{{ $t('cus.maxFiveSearchResults') }}</span>
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-hover text-center">
					<thead>
						<tr>
							<th class="text-start">
								{{ $t('cus.searchResult') }}
							</th>
							<th>{{ $t('cus.createDate') }}</th>
							<th>{{ $t('cus.execute') }}</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="item in searchHistory">
							<td class="text-start" :data-th="$t('cus.searchResult')">
								<a class="tx-link JQ-logView" @click="getSearchHistoryByResultCode(item.resultCode)">{{ item.resultName
								}}</a>
							</td>
							<td :data-th="$t('cus.createDate')">
								{{ item.createDt }}
							</td>
							<td :data-th="$t('cus.execute')">
								<button
									type="button"
									class="btn tx-danger btn-icon-only JQ-logDelete"
									data-bs-toggle="tooltip"
									:data-bs-original-title="$t('cus.delete')"
									@click="deleteLog(item)"
								>
									<i class="bi bi-trash" />
								</button>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>
<script>
import { getImgURL } from '@/utils/imgURL.js';

export default {
	props: {
		queryReq: Object,
		gotoPage: Function
	},
	data: function () {
		return {
			// API 用參數
			userCode: null,

			// 主要顯示資料
			searchHistory: []
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo'])
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				const self = this;
				if (newVal) {
					self.userCode = newVal.userCode;
					self.getSearchHistory();
				}
			}
		}
	},
	methods: {
		getImgURL,
		getSearchHistory: async function () {
			const self = this;
			const ret = await self.$api.getSearchHistoryApi({
				userCode: self.userCode
			});
			self.searchHistory = ret.data;
		},
		deleteLog: async function (item) {
			const self = this;
			self.$bi.confirm(self.$t('cus.confirmDelete') + item.resultName, {
				event: {
					confirmOk: async function () {
						await self.$api.postCusSearchLog({
							userCode: self.userInfo.userCode,
							resultCode: item.resultCode,
							logType: 'D',
							deputyUserCode: self.userInfo.principalUserCode
						});
						await self.$api.deleteSearchResult({
							resultCode: item.resultCode
						});
						self.$bi.alert(self.$t('cus.deleteSuccess'));
						self.getSearchHistory();
					}
				}
			});
		},
		getSearchHistoryByResultCode: function (resultCode) {
			const self = this;
			Object.keys(self.queryReq).forEach(key => delete self.queryReq[key]);
			self.queryReq.queryType = 'RESULT_CODE';
			self.queryReq.resultCode = resultCode;
			self.gotoPage(0);
		},
		redirect: function () {
			const self = this;
			self.$router.push('/cus/complexSearch');
		}
	}
};
</script>
