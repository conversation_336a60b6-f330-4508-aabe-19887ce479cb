<template>
	<div>
		<div class="card card-form">
			<div class="card-header">
				<h4>{{ $t('cus.specialSearch') }}</h4>
			</div>
			<div class="card-body">
				<div class="row g-2 align-items-end">
					<div class="col-md-12 bg-ligntblue p-3">
						<label class="form-label d-flex justify-content-between">{{ $t('cus.keyCustomerGroupSearch') }}<a id="cusGroupLink" class="tx-link" @click="next()">{{ $t('cus.editKeyCustomer') }}</a></label>
						<vue-form v-slot="{ errors }" ref="groupCode">
							<div class="input-group">
								<vue-field
									id="groupCode"
									v-model="groupCode"
									as="select"
									name="groupCode"
									class="form-select"
									:class="{ 'is-invalid': errors.groupCode }"
									rules="required"
									:label="$t('cus.keyCustomer')"
								>
									<option value="" disabled>
										{{ $t('cus.pleaseSelect') }}
									</option>
									<option value="ALL">
										{{ $t('cus.allGroupCustomers') }}
									</option>
									<option value="NONE">
										{{ $t('cus.ungroupedCustomers') }}
									</option>
									<option v-for="cusGroup in cusGroupMenu" :value="cusGroup.groupCode">
										{{ cusGroup.groupName }}
									</option>
								</vue-field>
								<button
									class="btn btn-primary btn-glow JQ-singleSearch"
									type="button"
									data-rel="byGroupCode"
									@click.prevent="queryByGroup()"
								>
									<i class="bi bi-search" />
								</button>
							</div>
							<div style="height: 25px">
								<span v-show="errors.groupCode" class="text-danger">{{ errors.groupCode }}</span>
							</div>
						</vue-form>
					</div>
					<div class="divider" />
					<div class="col-md-12 bg-ligntblue p-3">
						<vue-form v-slot="{ errors }" ref="balanceWatch">
							<label class="form-label d-flex justify-content-between">{{ $t('cus.productMarketValueSearch') }}</label>
							<label class="form-label">{{ $t('cus.productBalanceSearch') }}</label><br>
							<div v-for="productBalance in productBalanceMenu" class="form-check form-check-inline">
								<vue-field
									id="balanceItems"
									v-model="balanceItems"
									class="form-check-input"
									name="balanceItems"
									type="checkbox"
									:value="productBalance.codeValue"
									rules="required"
									:label="$t('cus.product')"
									:class="{ 'is-invalid': errors.balanceItems }"
									data-vv-scope="balanceWatch"
								/>
								<label class="form-check-label">{{ productBalance.codeName }}</label>
							</div>
							<div class="row justify-content-between">
								<div class="col-10">
									<div class="input-group">
										<vue-field
											id="balanceBegin"
											v-model="balanceBegin"
											name="balanceBegin"
											class="form-control text-end"
											type="number"
											size="20"
											:rules="balanceBeginRules"
											:label="$t('cus.balanceStart')"
											data-vv-scope="balanceWatch"
											:class="{ 'is-invalid': errors.balanceBegin }"
										/>
										<span class="input-group-text">~</span>
										<vue-field
											id="balanceEnd"
											v-model="balanceEnd"
											name="balanceEnd"
											class="form-control text-end"
											:class="{ 'is-invalid': errors.balanceEnd }"
											type="number"
											size="20"
											:rules="balanceEndRules"
											:label="$t('cus.balanceEnd')"
											data-vv-scope="balanceWatch"
										/>
										<span class="input-group-text">{{ $t('cus.yuan') }}</span>
									</div>
								</div>
								<div class="col-2 text-end">
									<Button type="button" @click="queryByBalance()">
										<i class="bi bi-search" />
									</Button>
								</div>
								<div class="col-10" style="height: 3px">
									<span v-show="errors.balanceItems" class="text-danger">{{ errors.balanceItems }}</span>
									<span v-show="errors.balanceBegin" class="text-danger">{{ errors.balanceBegin }}</span>
									<span v-show="errors.balanceEnd" class="text-danger">{{ errors.balanceEnd }}</span>
								</div>
							</div>
						</vue-form>
					</div>

					<div class="divider" />
					<div class="col-md-12 bg-ligntblue p-3">
						<vue-form ref="expireItemsForm" class="d-flex flex-column gap-2">
							<label class="form-label">{{ $t('cus.productExpirySearch') }}</label>
							<FormField
								v-slot="{invalid, field}"
								v-model="expireItems"
								vertical
								name="expireItems"
								rules="required"
								:label="$t('cus.productMainCategory')"
							>
								<CheckboxGroup
									v-bind="field"
									:invalid
									inline
									:options="productExpireMenu"
									option-label="codeName"
									option-value="codeValue"
								/>
							</FormField>
							<FormField
								v-slot="{field, invalid}"
								v-model="expireDay"
								vertical
								name="expireDays"
								:label="$t('cus.daysToExpiry')"
								rules="required"
							>
								<div class="d-flex">
									<RadioGroup
										v-bind="field"
										:invalid
										class="d-flex align-items-center gap-1 flex-fill"
										:options="expireDayList"
										option-label="codeName"
										option-value="codeValue"
										inline
									/>
									<Button @click="queryByProductExpire">
										<i class="bi bi-search" />
									</Button>
								</div>
							</FormField>
						</vue-form>
					</div>
					<div class="divider" />
					<div class="col-md-12 bg-ligntblue p-3">
						<vue-form v-slot="{ errors }" ref="trustCertificate">
							<label class="form-label">{{ $t('cus.trustCertificatePLSearch') }}</label><br>
							<div class="row align-items-end justify-content-between">
								<div class="col-10">
									<span class="input-group-text">{{ $t('cus.plRange') }}</span>
									<div v-for="certificate in trustCertificateMenu" class="form-check form-check-inline">
										<vue-field
											v-model="trustCertificate"
											class="form-check-input"
											:class="{ 'is-invalid': errors.trustCertificate }"
											name="trustCertificate"
											type="radio"
											:value="certificate.codeValue"
											rules="required"
											:label="$t('cus.trustCertificatePL')"
										/>
										<label class="form-check-label">{{ certificate.codeName }}</label>
									</div>
								</div>
								<div class="col-2 text-end">
									<button class="btn btn-primary btn-glow" type="button" @click.prevent="queryByTrustCertificate()">
										<i class="bi bi-search" />
									</button>
								</div>
							</div>
							<div style="height: 25px">
								<span v-show="errors.trustCertificate" class="text-danger">{{ errors.trustCertificate }}</span>
							</div>
						</vue-form>
					</div>
				</div>
			</div>
		</div>

		<vue-fav-customer-model :refresh-fun="getCusGroup" />
	</div>
</template>
<script>
import { Field, Form } from 'vee-validate';
import vueFavCustomerModel from './favCustomerModal.vue';
import _ from 'lodash';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueFavCustomerModel
	},
	props: {
		queryReq: Object,
		gotoPage: Function
	},
	data: function () {
		return {
			groupCode: '',
			balanceItems: [],
			balanceBegin: '',
			balanceBeginRules: {
				required: true,
				min_value: 0
			},
			balanceEnd: '',
			curCode: 'ALL', // 查詢幣別代碼
			trustCertificate: null,

			expireItems: [],
			expireDay: 0,
			expireDays: [],
			// 畫面顯示用邏輯參數
			isGroupNotNull: false,
			// 下拉選單
			cusGroupMenu: [],
			productBalanceMenu: [],
			productExpireMenu: [],
			trustCertificateMenu: [],
			expireDayList: [],
			currenciesMenu: [] // 查詢幣別
		};
	},
	computed: {
		balanceEndRules() {
			return {
				required: true,
				min_value: this.balanceBegin || 0
			};
		}
	},
	mounted: function () {
		const self = this;
		self.getCusGroup();
		self.getProductBalance();
		self.getProductExpire();
		self.getTrustCertificate();
		// self.getCurrMenu();
		self.getExpireDayList();
	},
	methods: {
		next: function () {
			const self = this;
			self.$router.push('/cus/favCusSetup');
		},
		getCusGroup: async function () {
			const self = this;
			const ret = await self.$api.getCusGroupMenuApi();
			if (!_.isEmpty(ret.data)) {
				self.cusGroupMenu = ret.data;
				self.isGroupNotNull = true;
			}
		},
		getProductBalance: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'MKT_AMT_FIELD'
			});
			self.productBalanceMenu = ret.data;
		},
		getProductExpire: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'PRO_EXPIRE_SRH'
			});
			self.productExpireMenu = ret.data;
		},
		getTrustCertificate: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'PL_RATE_RANGE'
			});
			self.trustCertificateMenu = ret.data;
		},
		getExpireDayList: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'PRO_EXPIRE_TIME'
			});
			self.expireDayList = ret.data;
		},
		async queryByGroup() {
			const self = this;
			const pass = await self.$refs.groupCode.validate();
			if (!pass.valid) return;

			self.clearQueryReq();
			self.queryReq.queryType = 'GROUP_CODE';
			self.queryReq.groupCode = self.groupCode;
			self.gotoPage(0);
			// self.groupCode = null;
		},
		clearQueryReq: function () {
			const self = this;

			for (const key in self.queryReq) {
				delete self.queryReq[key];
			}
		},
		async queryByBalance() {
			const self = this;
			const pass = await self.$refs.balanceWatch.validate();
			if (!pass.valid) return;

			self.clearQueryReq();
			self.queryReq.queryType = 'BALANCE';
			self.queryReq.mktProList = self.balanceItems;
			self.queryReq.mktValueStart = self.balanceBegin;
			self.queryReq.mktValueEnd = self.balanceEnd;
			self.gotoPage(0);
			// self.balanceItems = [];
			// self.balanceEnd = null;
			// self.balanceBegin = null;
		},
		async queryByProductExpire() {
			const self = this;
			const pass = await self.$refs.expireItemsForm.validate();
			if (!pass.valid) return;
			self.clearQueryReq();
			self.queryReq.queryType = 'EXPIRE_ITEMS';
			self.queryReq.expProList = self.expireItems.join(',');
			self.queryReq.expireDay = self.expireDay;
			self.gotoPage(0);
			// self.expireItems = [];
			// self.expireDay = 0;
		},
		async queryByTrustCertificate() {
			const self = this;
			const pass = await self.$refs.trustCertificate.validate();
			if (!pass.valid) return;
			self.clearQueryReq();
			self.queryReq.queryType = 'TRUST_CERTIFICATE';
			self.queryReq.plRange = self.trustCertificate;
			self.gotoPage(0);
			// self.trustCertificate = null;
		}
	}
};
</script>
