<template>
	<div>
		<div class="card card-form card-collapse mb-0">
			<div class="card-header">
				<h4>{{ $t('cus.basicSearch') }}<span class="tx-square-bracket">{{ $t('cus.requiredField') }}</span></h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1" />
			</div>
			<div class="card-body">
				<form>
					<div class="row g-3 align-items-end">
						<div class="col-md-6">
							<vue-form v-slot="{ errors }" ref="cusName">
								<label class="form-label">{{ $t('cus.customerName') }}</label>
								<div class="input-group">
									<vue-field
										id="cusName"
										v-model="cusName"
										name="cusName"
										:class="{ 'is-invalid': errors.cusName }"
										class="form-control"
										type="text"
										rules="required"
										:label="$t('cus.customerName')"
									/>
									<button class="btn btn-primary btn-glow" type="button" @click.prevent="queryByCusName()">
										<i class="bi bi-search" />
									</button>
								</div>
								<div style="height: 25px">
									<span v-show="errors.cusName" class="text-danger">{{ errors.cusName }}</span>
								</div>
							</vue-form>
						</div>
						<div class="col-md-6">
							<vue-form v-slot="{ errors }" ref="cusCode">
								<label class="form-label">{{ $t('cus.customerIdTaxId') }}</label>
								<div class="input-group">
									<vue-field
										id="cusCode"
										v-model="cusCode"
										name="cusCode"
										class="form-control JQ-uCase"
										:class="{ 'is-invalid': errors.cusCode }"
										type="text"
										rules="required"
										:label="$t('cus.customerIdTaxId')"
									/>
									<button
										class="btn btn-primary btn-glow"
										type="button"
										data-rel="byIdn"
										@click.prevent="queryByIdn()"
									>
										<i class="bi bi-search" />
									</button>
								</div>
								<div style="height: 25px">
									<span v-show="errors.cusCode" class="text-danger">{{ errors.cusCode }}</span>
								</div>
							</vue-form>
						</div>
						<div class="col-md-6">
							<vue-form v-slot="{ errors }" ref="graCode">
								<label class="form-label">{{ $t('cus.customerAssetLevel') }}</label>
								<div class="input-group">
									<vue-field
										id="graCode"
										v-model="graCode"
										as="select"
										name="graCode"
										class="form-select JQ-uCase"
										:class="{ 'is-invalid': errors.graCode }"
										rules="required"
										:label="$t('cus.customerAssetLevel')"
									>
										<option :value="''">
											{{ $t('cus.pleaseSelect') }}
										</option>
										<option v-for="(item, index) in cusGrades" :key="index" :value="item.graCode">
											{{ item.graName }}
										</option>
									</vue-field>
									<button
										class="btn btn-primary btn-glow"
										type="button"
										data-rel="byGraCode"
										@click.prevent="queryByGraCode()"
									>
										<i class="bi bi-search" />
									</button>
								</div>
								<div style="height: 25px">
									<span v-show="errors.graCode" class="text-danger">{{ errors.graCode }}</span>
								</div>
							</vue-form>
						</div>
						<div class="col-md-6">
							<UserCondition
								@change-area="areaCode = $event"
								@change-bran="branCode = $event"
								@change-user="userCode = $event"
							>
								<template #append>
									<Button @click.prevent="queryByBranCode()">
										<i class="bi bi-search" />
									</Button>
								</template>
							</UserCondition>
							<div style="height: 25px" />
						</div>
						<div class="col-md-6">
							<vue-form v-slot="{ errors }" ref="cellPhone">
								<label class="form-label">{{ $t('cus.cellPhone') }}</label>
								<div class="input-group">
									<vue-field
										id="cellPhone"
										v-model="cellPhone"
										name="cellPhone"
										class="form-control"
										type="text"
										:class="{ 'is-invalid': errors.cellPhone }"
										size="30"
										rules="required"
										:label="$t('cus.cellPhone')"
									/>
									<button class="btn btn-primary btn-glow" type="button" @click.prevent="queryByMobile()">
										<i class="bi bi-search" />
									</button>
								</div>
								<div style="height: 25px">
									<span v-show="errors.cellPhone" class="text-danger">{{ errors.cellPhone }}</span>
								</div>
							</vue-form>
						</div>
						<div class="col-md-6">
							<vue-form v-slot="{ errors }" ref="contactPhone">
								<label class="form-label">{{ $t('cus.homePhone') }}</label>
								<div class="input-group">
									<vue-field
										id="homePhone"
										v-model="contactPhone"
										name="contactPhone"
										class="form-control"
										:class="{ 'is-invalid': errors.contactPhone }"
										type="text"
										size="30"
										rules="required"
										:label="$t('cus.homePhone')"
									/>
									<button
										class="btn btn-primary btn-glow"
										type="button"
										data-rel="byHomePhone"
										@click.prevent="queryByPhone()"
									>
										<i class="bi bi-search" />
									</button>
								</div>
								<div style="height: 25px">
									<span v-show="errors.contactPhone" class="text-danger">{{ errors.contactPhone }}</span>
								</div>
							</vue-form>
						</div>
						<div class="col-md-6">
							<vue-form v-slot="{ errors }" ref="email">
								<label class="form-label">{{ $t('cus.email') }}</label>
								<div class="input-group">
									<vue-field
										id="email"
										v-model="email"
										name="email"
										class="form-control"
										:class="{ 'is-invalid': errors.email }"
										type="text"
										size="50"
										rules="required"
										:label="$t('cus.email')"
									/>
									<button
										class="btn btn-primary btn-glow"
										type="button"
										data-rel="byEmail"
										@click.prevent="queryByEmail()"
									>
										<i class="bi bi-search" />
									</button>
								</div>
								<div style="height: 25px">
									<span v-show="errors.email" class="text-danger">{{ errors.email }}</span>
								</div>
							</vue-form>
						</div>
						<div class="col-md-6">
							<vue-form v-slot="{ errors }" ref="contactAddress">
								<label class="form-label">{{ $t('cus.contactAddress') }}</label>
								<div class="input-group">
									<vue-field
										id="address"
										v-model="contactAddress"
										name="contactAddress"
										class="form-control"
										type="text"
										:class="{ 'is-invalid': errors.contactAddress }"
										size="30"
										rules="required"
										:label="$t('cus.contactAddress')"
									/>
									<button
										class="btn btn-primary btn-glow"
										type="button"
										data-rel="byAddress"
										@click.prevent="queryByAddr()"
									>
										<i class="bi bi-search" />
									</button>
								</div>
								<div style="height: 25px">
									<span v-show="errors.contactAddress" class="text-danger">{{ errors.contactAddress }}</span>
								</div>
							</vue-form>
						</div>
					</div>
				</form>
			</div>
		</div>
		<div class="tx-note mb-4">
			<ol>
				<li>{{ $t('cus.customerNameFuzzySearch') }}</li>
				<li>{{ $t('cus.customerDataT1Note') }}</li>
			</ol>
		</div>
	</div>
</template>
<script>
import { Field, Form } from 'vee-validate';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	props: {
		queryReq: Object,
		gotoPage: Function
	},
	data: function () {
		return {
			// 查詢資料
			cusName: null,
			cusCode: null,
			graCode: '',
			smeYn: '',
			contactPhone: null,
			cellPhone: null,
			email: null,
			contactAddress: null,
			areaCode: '',
			userCode: '',
			branCode: '',
			account: null,

			// 下拉選單
			selAreaList: [],
			selBranList: [],
			selUserList: [],
			cusGrades: []
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo'])
	},
	mounted: function () {
		const self = this;
		self.getCusGrades();
	},
	methods: {
		getCusGrades: async function () {
			const self = this;
			const ret = await self.$api.getCusGradesApi();
			self.cusGrades = ret.data;
		},
		clearQueryReq: function () {
			const self = this;

			for (const key in self.queryReq) {
				delete self.queryReq[key];
			}
		},
		queryByCusName: function () {
			const self = this;
			self.$refs.cusName.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'CUS_NAME';
					self.queryReq.cusName = self.cusName;
					self.gotoPage(0);
				}
			});
		},
		queryByIdn: function () {
			const self = this;
			self.$refs.cusCode.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'IDN';
					self.queryReq.cusCode = self.cusCode;
					self.gotoPage(0);
				}
			});
		},
		queryByEmail: function () {
			const self = this;
			self.$refs.email.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'EMAIL';
					self.queryReq.email = self.email;
					self.gotoPage(0);
				}
			});
		},
		queryByPhone: function () {
			const self = this;
			self.$refs.contactPhone.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'CONTACT_PHONE';
					self.queryReq.contactPhone = self.contactPhone;
					self.gotoPage(0);
				}
			});
		},
		queryByMobile: function () {
			const self = this;
			self.$refs.cellPhone.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'CELL_PHONE';
					self.queryReq.cellPhone = self.cellPhone;
					self.gotoPage(0);
				}
			});
		},
		queryByAddr: function () {
			const self = this;
			self.$refs.contactAddress.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'CONTACT_ADDRESS';
					self.queryReq.contactAddress = self.contactAddress;
					self.gotoPage(0);
				}
			});
		},
		queryByBranCode: function () {
			this.clearQueryReq();
			this.queryReq.queryType = 'BRAN_CODE';
			this.queryReq.areaCode = this.areaCode;
			this.queryReq.userCode = this.userCode;
			this.queryReq.branCode = this.branCode;
			this.gotoPage(0);
		},
		queryByAccount: function () {
			const self = this;
			self.$refs.account.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'ACCOUNT';
					self.queryReq.account = self.account;
					self.gotoPage(0);
				}
			});
		},
		queryByGraCode: function () {
			const self = this;
			self.$refs.graCode.validate().then(function (pass) {
				if (pass.valid) {
					self.clearQueryReq();
					self.queryReq.queryType = 'GRA_CODE';
					self.queryReq.graCode = self.graCode;
					self.gotoPage(0);
				}
			});
		}
	}
};
</script>
