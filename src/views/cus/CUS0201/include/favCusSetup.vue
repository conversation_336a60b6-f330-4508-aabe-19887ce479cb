<template>
	<!--頁面內容 start-->
	<div v-if="doUpdateCode != null">
		<div class="card-header">
			<h4>{{ $t('cus.pleaseEnterFollowingData') }}</h4>
		</div>
		<div class="card-body">
			<div class="row g-3">
				<div class="col-md-4">
					<label class="form-label">{{ $t('cus.byCustomerGrade') }}</label>
					<div class="input-group">
						<select id="selectCusGrade" v-model="graCode" class="form-select">
							<option value="">
								{{ $t('cus.all') }}
							</option>
							<option v-for="aumData in aumMenu" :value="aumData.graCode">
								{{ aumData.graName }}
							</option>
						</select>
						<button
							id="searchByGraBtn"
							class="btn btn-primary btn-glow"
							type="button"
							@click="queryByGraCode()"
						>
							<i class="bi bi-search" />
						</button>
					</div>
				</div>
				<div class="col-md-4">
					<label class="form-label">{{ $t('cus.byCustomerIdTaxId') }}</label>
					<div class="input-group">
						<input
							id="inputCusCode"
							v-model="idn"
							name="inputCusCode"
							class="form-control"
							type="text"
							size="30"
							maxlength="10"
						>
						<button
							id="searchByIdnBtn"
							class="btn btn-primary btn-glow"
							type="button"
							@click="queryByIdn()"
						>
							<i class="bi bi-search" />
						</button>
					</div>
				</div>
				<div class="col-md-4">
					<label class="form-label">{{ $t('cus.byCustomerName') }}</label>
					<div class="input-group">
						<input
							id="inputCusName"
							v-model="cusName"
							name="inputCusName"
							class="form-control"
							type="text"
							size="30"
							maxlength="10"
						>
						<button
							id="searchByNameBtn"
							class="btn btn-primary btn-glow"
							type="button"
							@click="queryByCusName()"
						>
							<i class="bi bi-search" />
						</button>
					</div>
				</div>
			</div>

			<div class="divider" />
			<div class="row g-0 text-center align-items-center justify-content-center">
				<div id="ShowList" class="col-5">
					<div class="tx-title">
						{{ $t('cus.availableCustomers') }}
					</div>
					<select
						id="availableCusPool"
						v-model="queryCusCodes"
						name="availableCusPool"
						class="form-select"
						size="13"
						multiple
					>
						<option v-for="cusItem in queryCus" :value="cusItem.cusCode">
							{{ cusItem.cusName }}({{ cusItem.idn }}){{ cusItem.graName }}
						</option>
					</select>
				</div>
				<div class="col-2">
					<button
						id="addCus"
						type="button"
						class="btn btn-info mb-2"
						@click="addCus()"
					>
						{{ $t('cus.join') }} <i class="bi bi-arrow-right-circle" />
					</button>
					<br>
					<button
						id="removeCus"
						type="button"
						class="btn btn-info"
						@click="removeCus()"
					>
						{{ $t('cus.remove') }} <i class="bi bi-arrow-left-circle" />
					</button>
				</div>
				<div class="col-5">
					<div class="tx-title">
						{{ $t('cus.addedGroupCustomers') }}
					</div>
					<select
						id="selectedCusPool"
						v-model="selectCusCodes"
						name="selectedCusPool"
						class="form-select"
						size="13"
						multiple
					>
						<option v-for="selectedCusItem in selectCus" :value="selectedCusItem.cusCode">
							{{ selectedCusItem.cusName }}({{ selectedCusItem.idn }}){{ selectedCusItem.graName }}
						</option>
					</select>
				</div>
			</div>
		</div>
		<div class="text-end mt-3">
			<button type="button" class="btn btn-lg btn-glow btn-secondary" @click="clearEditForm()">
				{{ $t('cus.close') }}
			</button>
			<button type="button" class="btn btn-lg btn-glow btn-primary" @click="updateGroupCus()">
				{{ $t('cus.save') }}
			</button>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
export default {
	props: {
		groupName: String,
		doUpdateCode: Number,
		gotoPage: Function,
		refreshUpdateCode: Function
	},
	data: function () {
		return {
			// API 用參數
			graCode: null,
			idn: null,
			cusName: null,
			selectCusCodes: [],

			// 畫面邏輯用參數
			// doUpdateCode: null,
			queryCusCodes: [],
			// 下拉選單
			aumMenu: [],
			// 畫面顯示用參數
			cusGroups: [],

			// 主要顯示資料
			queryCus: [],
			selectCus: [],
			pageable: {
				page: 0,
				size: 500,
				sort: 'CUS_CODE',
				direction: 'ASC'
			}
		};
	},
	watch: {
		doUpdateCode: function () {
			const self = this;
			if (!_.isBlank(self.doUpdateCode)) {
				self.getGroupCus();
			}
		}
	},
	mounted: function () {
		const self = this;
		self.getAumMenu();
	},
	methods: {
		getAumMenu: async function () {
			const self = this;
			const ret = await self.$api.getCusGradesApi();
			self.aumMenu = ret.data;
		},
		getCusGroup: async function () {
			const self = this;
			const ret = await self.$api.getCusGroupMenuApi();
			self.cusGroups = ret.data;
		},
		queryByGraCode: function () {
			const self = this;
			const queryReq = { graCode: self.graCode };
			self.singleQuery(queryReq);
		},
		queryByIdn: function () {
			const self = this;
			const queryReq = { idn: self.idn };
			self.singleQuery(queryReq);
		},
		queryByCusName: function () {
			const self = this;
			const queryReq = { cusName: self.cusName };
			self.singleQuery(queryReq);
		},
		singleQuery: async function (queryReq) {
			const self = this;
			const url = _.toPageUrl('', page, self.pageable);
			const ret = await self.$api.getCusSummariesApi(
				{
					idn: queryReq.idn,
					graCode: queryReq.graCode,
					cusName: queryReq.cusName
				},
				url
			);

			if (ret.data.content.length == 0) {
				self.queryCus = [];
				self.$bi.alert(self.$t('cus.noCustomerFound'));
			}
			else {
				self.queryCus = ret.data.content;

				self.selectCus.forEach(function (selectCus) {
					self.queryCus.forEach(function (item, index, arr) {
						if (selectCus.cusCode == item.cusCode) {
							arr.splice(index, 1);
						}
					});
				});
			}
		},
		getGroupCus: async function () {
			const self = this;
			const ret = await self.$api.getGroupCustomers({
				groupCode: self.doUpdateCode
			});
			self.selectCus = ret.data;
		},
		clearEditForm: function () {
			const self = this;
			self.groupName = '';
			self.queryCus = [];
			self.selectCus = [];
			self.doUpdateCode = null;
			self.refreshUpdateCode(self.doUpdateCode);
		},
		addCus: function () {
			const self = this;

			self.queryCusCodes.forEach(function (queryCusCode) {
				self.queryCus.forEach(function (item, index, arr) {
					if (queryCusCode == item.cusCode) {
						self.selectCus.push(item);
						arr.splice(index, 1);
					}
				});
			});
		},
		removeCus: function () {
			const self = this;

			self.selectCusCodes.forEach(function (selectCusCode) {
				self.selectCus.forEach(function (item, index, arr) {
					if (selectCusCode == item.cusCode) {
						self.queryCus.push(item);
						arr.splice(index, 1);
					}
				});
			});
		},
		updateGroupCus: async function () {
			const self = this;

			const selectCusCodes = [];
			self.selectCus.forEach(function (item) {
				selectCusCodes.push(item.cusCode);
			});

			const ret = await self.$api.postGroupCustomers({
				groupName: self.groupName,
				groupCode: self.doUpdateCode,
				cusCodes: selectCusCodes
			});
			self.$bi.alert(self.$t('cus.updateSuccess'));
			self.getCusGroup();
		}
	}
};
</script>
