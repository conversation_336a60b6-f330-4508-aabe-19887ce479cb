<template>
	<div class="row gx-2">
		<div v-show="!showSearchResult" class="d-block d-lg-none mb-3 d-flex justify-content-end">
			<a
				class="btn btn-lg btn-dark tx-16 tx-bold wd-200 d-flex justify-content-between"
				type="button"
				@click="redirect()"
			><span><img :src="getImgURL('icon', 'ico-multi.png')">{{ $t('cus.comprehensiveCustomerSearch') }}</span> <i
				class="bi bi-shuffle"
			/></a>
		</div>

		<div v-show="!showSearchResult" class="col-lg-8">
			<vue-cus-single-search
				ref="cusSingleSearch"
				:title="$t('cus.basicSearch')"
				:goto-page="gotoPage"
				:query-req="queryReq"
			/>
			<vue-cus-spec-search
				ref="cusSpecSearch"
				:title="$t('cus.specialSearch')"
				:goto-page="gotoPage"
				:query-req="queryReq"
			/>
		</div>
		<!-- 歷史查詢結果名單 -->
		<vue-cus-search-history
			v-show="!showSearchResult"
			ref="searchHistoryPage"
			:title="$t('cus.searchHistoryResultList')"
			:goto-page="gotoPage"
			:query-req="queryReq"
		/>

		<!-- 查詢結果 -->
		<div v-show="showSearchResult">
			<vue-complex-search-result
				ref="searchPage"
				:query-req="queryReq"
				:set-is-show-search-bar="setShowSearchResult"
				:get-search-history="reCusSearchHistory"
				:set-is-show-title="setIsShowTitle"
			/>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
import vueCusSingleSearch from './include/cusSingleSearch.vue';
import vueComplexSearchResult from '../CUS0202/include/complexSearchResult.vue';
import vueCusSpecSearch from './include/cusSpecSearch.vue';
import vueCusSearchHistory from './include/cusSearchHistory.vue';
import { getImgURL } from '@/utils/imgURL';
export default {
	components: {
		vueCusSingleSearch,
		vueComplexSearchResult,
		vueCusSpecSearch,
		vueCusSearchHistory
	},
	filters: {},
	data: function () {
		return {
			// API 用參數
			isShowSummary: false,
			resultName: null,
			selectedCusCodes: [],
			groupCode: null,

			// 子組件用條件物件
			queryReq: { queryType: null },

			// 畫面邏輯判斷用參數
			isShowTitle: true,
			showSearchResult: false,
			showCalResult: false,
			allSelected: false,
			showCusSingleSearchPage: false,
			customFields: [],
			hasCustomFields: false,
			customFieldValues: [],

			// 下拉選單
			groupCodeMenu: [],

			// 畫面顯示用參數
			calResult: {
				aum: 0,
				trustAum: 0,
				sBal: 0,
				sBalFc: 0,
				fMonth: 0,
				fYtd: 0,
				fLy: 0
			}
		};
	},
	computed: {
		isShowPageTitle: function () {
			return this.isShowTitle;
		},
		// 首頁查詢條件
		cusName: function () {
			return this.$route.params?.cusName || null;
		},
		cusCode: function () {
			return this.$route.params?.cusCode || null;
		},
		isHomePageQuery: function () {
			return this.$route.params?.isHomePageQuery || null;
		}
	},
	beforeMount: function () { },
	created: function () { },
	mounted: function () {
		const self = this;
		self.checkHomePageQuery();
	},
	methods: {
		getImgURL,
		receiveMessage(val) {
			this.isShowSummary = val;
		},
		checkHomePageQuery: function () {
			const self = this;
			if (self.isHomePageQuery == 'Y') {
				self.queryReq.cusName = self.cusName;
				self.queryReq.cusCode = self.cusCode;
				self.queryReq.queryType = 'SINGLE';
				// 清除URL參數
				history.replaceState({}, document.title, window.location.pathname);
				self.gotoPage(0);
			}
		},
		gotoPage: function (page) {
			this.$refs.searchPage.gotoPage(page);
		},
		reCusSearchHistory: function () {
			this.$refs.searchHistoryPage.getSearchHistory();
		},
		setShowSearchResult: function (val) {
			const self = this;
			self.showSearchResult = val;
			window.scrollTo(0, 0);
		},
		redirect: function () {
			const self = this;
			location.href = '/cus/cusSearch1';
		},
		setIsShowTitle(val) {
			this.isShowTitle = val;
		}
	}
};
</script>
