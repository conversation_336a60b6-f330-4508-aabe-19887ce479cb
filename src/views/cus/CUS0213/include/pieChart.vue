<template>
	<div :id="chartId" style="height: 300px" />
</template>
<script>
import * as am5 from '@amcharts/amcharts5';
import * as am5percent from '@amcharts/amcharts5/percent';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
export default {
	props: {
		chartId: String,
		chartData: Object
	},
	data() {
		return {
			root: null
		};
	},
	watch: {},
	updated() {
		if (this.root) {
			am5.array.each(am5.registry.rootElements, (root) => {
				if (root.dom.id === this.chartId) {
					root.dispose();
					this.root = null;
					this.renderChart();
				}
			});
		}
		else {
			this.renderChart();
		}
	},
	beforeUnmount() {
		if (this.root) {
			am5.array.each(am5.registry.rootElements, (root) => {
				const domId = root?.dom?.id;
				if (domId === this.chartId) {
					root.dispose();
					this.root = null;
				}
			});
		}
	},
	methods: {
		renderChart() {
			if (!this.chartData || this.chartData.length === 0) {
				return;
			}
			am5.ready(() => {
				// Create root element
				// https://www.amcharts.com/docs/v5/getting-started/#Root_element
				const root = am5.Root.new(this.chartId);
				root._logo.dispose();

				// Set themes
				// https://www.amcharts.com/docs/v5/concepts/themes/
				root.setThemes([am5themes_Animated.new(root)]);

				// Create chart
				// https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/
				const chart = root.container.children.push(
					am5percent.PieChart.new(root, {
						layout: root.verticalLayout,
						innerRadius: am5.percent(50)
					})
				);

				// Create series
				// https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/#Series
				const series = chart.series.push(
					am5percent.PieSeries.new(root, {
						valueField: 'value',
						categoryField: 'category',
						alignLabels: false
					})
				);

				// series.labels.template.setAll({
				//    textType: "circular",
				//    centerX: 0,
				//    centerY: 0
				// });

				// Disabling labels and ticks
				series.labels.template.set('visible', false);
				series.ticks.template.set('visible', false);

				// Set data
				// https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/#Setting_data
				series.data.setAll(this.chartData);

				// Create legend
				// https://www.amcharts.com/docs/v5/charts/percent-charts/legend-percent-series/
				const legend = chart.children.push(
					am5.Legend.new(root, {
						centerX: am5.percent(50),
						x: am5.percent(50),
						marginTop: 15,
						marginBottom: 15
					})
				);

				legend.data.setAll(series.dataItems);

				// Play initial series animation
				// https://www.amcharts.com/docs/v5/concepts/animations/#Animation_of_series
				series.appear(1000, 100);

				this.root = root;
			}); // end am5.ready()
		}
	}
};
</script>
