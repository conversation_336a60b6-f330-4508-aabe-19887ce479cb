<template>
	<div>
		<div class="row">
			<div class="col-12">
				<div class="tab-nav-main tab-nav-mainAsset">
					<ul class="nav nav-pills">
						<li v-for="tab in tabs" class="nav-item">
							<a
								:class="{ active: tabCode == tab.code }"
								class="nav-link"
								href="#"
								@click.prevent="() => { tabCode = tab.code }"
							>
								{{ tab.label }}
							</a>
						</li>
					</ul>
				</div>
			</div>
			<div class="col-12">
				<div class="tab-content">
					<vue-cus-asset-detail v-if="tabCode === 'ALL'" :cus-code="cusCode" />
					<vue-cus-saving v-if="tabCode === 'SAVING'" :cus-code="cusCode" />
					<vue-cus-fund v-if="tabCode === 'FUND'" :cus-code="cusCode" />
					<vue-cus-bond v-if="tabCode === 'BOND'" :cus-code="cusCode" />
					<vue-cus-etf v-if="tabCode === 'ETF'" :cus-code="cusCode" />
					<vue-cus-pfd v-if="tabCode === 'PFD'" :cus-code="cusCode" />
					<vue-cus-dcd
						v-if="tabCode === 'DCI'"
						:cus-code="cusCode"
						:fx-rate="fxRate"
						:base-currency-name="baseCurrencyName"
					/>
					<vue-cus-sp
						v-if="tabCode === 'SP'"
						:cus-code="cusCode"
						:fx-rate="fxRate"
						:base-currency-name="calculateCurrency.curName"
					/>
					<vue-cus-ins v-if="tabCode === 'INS'" :cus-code="cusCode" />
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import moment from 'moment';
import _ from 'lodash';

import vueCusAssetDetail from './cusAccountOverView/assetDetail.vue';
import vueCusSaving from './cusAccountOverView/saving.vue';
import vueCusFund from './cusAccountOverView/fund.vue';
import vueCusBond from './cusAccountOverView/bond.vue';
import vueCusEtf from './cusAccountOverView/etf.vue';
import vueCusPfd from './cusAccountOverView/pfd.vue';
import vueCusSp from './cusAccountOverView/sp.vue';
import vueCusIns from './cusAccountOverView/ins.vue';
import vueCusDcd from './cusAccountOverView/dcd.vue';

export default {
	components: {
		vueCusAssetDetail,
		vueCusSaving,
		vueCusFund,
		vueCusBond,
		vueCusEtf,
		vueCusPfd,
		vueCusIns,
		vueCusSp,
		vueCusDcd
	},
	props: {
		cusCode: [String, Number],
		hasAuth: Boolean,
		pfcatCode: String
	},
	data: function () {
		return {
			queryCusCodes: [],
			relCustomerMenu: [],
			relCustomerList: [],
			tabCode: 'SAVING',
			baseCurrency: {}, // 客戶個人基準幣

			calculateCurCode: 'NTD', // 下拉選單查詢幣別代碼
			calculateCurrency: {}, // 下拉選單查詢幣別
			calculateCurCodes: [], // 計算幣別下拉選單

			userName: '', // 操作者姓名
			userRoleName: '', // 操作者角色名稱

			queryDt: null // 查詢時間
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo']),
		tabs() {
			return [
				{ code: 'ALL', label: this.$t('cus.assets.ALL') },
				{ code: 'SAVING', label: this.$t('cus.assets.SAVING') },
				{ code: 'FUND', label: this.$t('cus.assets.FUND') },
				{ code: 'BOND', label: this.$t('cus.assets.BOND') },
				{ code: 'ETF', label: this.$t('cus.assets.ETF') },
				{ code: 'PFD', label: this.$t('cus.assets.PFD') },
				{ code: 'DCI', label: this.$t('cus.assets.DCI') },
				{ code: 'SP', label: this.$t('cus.assets.SP') },
				{ code: 'INS', label: this.$t('cus.assets.INS') }
			];
		}
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal) {
				const self = this;
				if (newVal) {
					if (newVal.deputyUserName) {
						self.userName = newVal.deputyUserName;
					}
					else {
						self.userName = newVal.userName;
					}
					self.userRoleName = newVal.roleName;
				}
			}
		}
	},
	mounted: function () {
		const self = this;
		if (self.hasAuth) {
			self.getQueryDt();
			self.queryCusCodes.push(self.cusCode);
			self.getRelCustomerMenu();
			// self.getBaseCurrency();
			self.getCalculateCurCodes();
			self.tabCode = self.pfcatCode || self.tabCode;
		}
		else {
			self.$bi.alert('該顧客非屬您歸屬私銀中心/組轄下顧客。');
		}
	},
	methods: {
		getRelCustomerMenu: async function () {
			const self = this;
			const ret = await self.$api.getRelCustomerMenuApi({
				cusCode: self.cusCode
			});
			self.relCustomerMenu = ret.data;
			self.buildRelCustomerList();
		},
		buildRelCustomerList: function () {
			const self = this;
			_.forEach(self.relCustomerMenu, function (data) {
				if (data.cusCodes.length == 1 && data.typeName != '歸戶總資產') {
					const relCustomer = { cusCode: null, typeName: null };
					relCustomer.cusCode = data.cusCodes[0];
					relCustomer.typeName = data.typeName;
					self.relCustomerList.push(relCustomer);
				}
			});
		},
		// 取得會員基準幣
		getBaseCurrency: async function () {
			const self = this;
			const ret = await self.$api.getBaseCurCodeApi({
				cusCode: self.cusCode
			});
			self.baseCurrency = ret.data;
			self.calculateCurCode = self.baseCurrency.curCode;
		},
		changeTab: function (tabCode) {
			const self = this;
			if (self.hasAuth) {
				self.tabCode = tabCode;
				self.getQueryDt();
			}
			else {
				self.$bi.alert('顧客' + self.customer.cusName + '非屬您歸屬私銀中心/組轄下顧客。');
			}
		},
		// 更換計算幣別
		changeCalculateCurCode: function () {
			const self = this;
			self.calculateCurrency = _.find(self.calculateCurCodes, { curCode: self.calculateCurCode }) ?? self.calculateCurrency;
		},
		// 取得計算幣別下拉選單
		getCalculateCurCodes: async function () {
			const self = this;
			const ret = await self.$api.getCalculateCurCodesApi();
			self.calculateCurCodes = ret.data;
			self.changeCalculateCurCode();
		},
		// 取得查詢時間
		getQueryDt: function () {
			const self = this;
			self.queryDt = moment().format('YYYY/MM/DD HH:mm');
		}
	}
};
</script>
