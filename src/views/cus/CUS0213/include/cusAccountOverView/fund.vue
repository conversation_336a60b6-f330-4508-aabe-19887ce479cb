<template>
	<div class="tab-nav-main">
		<ul class="nav nav-pills">
			<li class="nav-item">
				<a class="nav-link active" href="#SectionA" data-bs-toggle="tab">本行部位餘額（前一日）</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#SectionB" data-bs-toggle="tab">同業部位餘額</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#SectionC" data-bs-toggle="tab">即時未實現投資餘額</a>
			</li>
		</ul>
		<div class="tab-content">
			<div id="SectionA" class="tab-pane fade active show">
				<!--基金分析-->
				<div class="card card-table card-tabs">
					<div class="card-header">
						<h4>基金分析(不含在途) </h4>
						<ul class="nav nav-pills  card-header-pills">
							<li>
								<a href="#tab-mkt" class="nav-link active" data-bs-toggle="pill">依投資市場</a>
							</li>
							<li>
								<a href="#tab-coin" class="nav-link" data-bs-toggle="pill">依信託幣別</a>
							</li>
							<li>
								<a href="#tab-type" class="nav-link" data-bs-toggle="pill">依基金類型</a>
							</li>
						</ul>
					</div>
					<div class="tab-content">
						<!-- 依投資市場 -->
						<div id="tab-mkt" class="tab-pane fade show active">
							<div class="row g-3 align-items-center mb-3">
								<div class="col-lg-5">
									<vue-cus-assets-pie-chart chart-id="fundSecChart" :chart-data="fundSecChartData" />
								</div>
								<div class="col-lg-7">
									<div class="tx-13 text-end">
										(新臺幣:元)
									</div>
									<table class="table table-striped table-RWD table-bordered">
										<thead>
											<tr>
												<th><span>投資市場</span></th>
												<th class="text-end">
													<span>投資現值</span>
												</th>
												<th class="text-end">
													<span>累計配息</span>
												</th>
												<th class="text-end">
													<span>投資現值(含息)</span>
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="fund in fundSecChartData">
												<td data-th="信託類別">
													{{ fund.sectorName }}
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt(fund.mktAmtLc) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt(fund.totDividendLc) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt(fund.mktAmtLc +
														fund.totDividendLc) }}
												</td>
											</tr>
											<tr class="tx-sum bg-subtotal">
												<td data-th="信託類別">
													小計
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(fundSecChartData,
														'mktAmtLc')) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(fundSecChartData,
														'totDividendLc')) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(fundSecChartData, 'mktAmtLc') +
														$filters.sumTotal(fundSecChartData, 'totDividendLc')) }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
						<!-- 依信託幣別 -->
						<div id="tab-coin" class="tab-pane fade">
							<div class="row g-3 align-items-center mb-3">
								<div class="col-lg-5">
									<vue-cus-assets-pie-chart chart-id="fundCurChart" :chart-data="fundCurChartData" />
								</div>
								<div class="col-lg-7">
									<table class="table table-striped table-RWD table-bordered">
										<thead>
											<tr>
												<th><span>信託幣別</span></th>
												<th class="text-end">
													<span>投資現值</span>
												</th>
												<th class="text-end">
													<span>累計配息</span>
												</th>
												<th class="text-end">
													<span>投資現值(含息)</span>
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="fund in fundCurChartData">
												<td data-th="信託幣別">
													{{ fund.proCurName }}
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt(fund.mktAmtLc) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt(fund.totDividendLc) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt(fund.mktAmtLc +
														fund.totDividendLc) }}
												</td>
											</tr>
											<tr class="tx-sum bg-subtotal">
												<td data-th="信託幣別">
													小計
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(fundCurChartData,
														'mktAmtLc')) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(fundCurChartData,
														'totDividendLc')) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(fundCurChartData, 'mktAmtLc') +
														$filters.sumTotal(fundCurChartData, 'totDividendLc')) }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
						<!-- 依基金類型 -->
						<div id="tab-type" class="tab-pane fade ">
							<div class="row g-3 align-items-center mb-3">
								<div class="col-lg-5">
									<vue-cus-assets-pie-chart chart-id="fundTypeChart" :chart-data="fundTypeChartData" />
								</div>
								<div class="col-lg-7">
									<div class="tx-13 text-end">
										(新臺幣:元)
									</div>
									<table class="table table-striped table-RWD table-bordered">
										<thead>
											<tr>
												<th><span>基金類型</span></th>
												<th class="text-end">
													<span>投資現值</span>
												</th>
												<th class="text-end">
													<span>累計配息</span>
												</th>
												<th class="text-end">
													<span>投資現值(含息)</span>
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="fund in fundTypeChartData">
												<td data-th="基金類型">
													{{ fund.protypeName }}
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt(fund.mktAmtLc) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt(fund.totDividendLc) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt(fund.mktAmtLc +
														fund.totDividendLc) }}
												</td>
											</tr>
											<tr class="tx-sum bg-subtotal">
												<td data-th="基金類型">
													小計
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(fundTypeChartData,
														'mktAmtLc')) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(fundTypeChartData,
														'totDividendLc')) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(fundTypeChartData, 'mktAmtLc') +
														$filters.sumTotal(fundTypeChartData, 'totDividendLc')) }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!--在途明細-->
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>在途明細</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsedetail" />
					</div>
					<div id="collapsedetail" class="collapse show">
						<table class="table bih-table table-RWD">
							<thead>
								<tr>
									<th>
										<p><span class="TC">投資標的</span></p>
									</th>
									<th><span class="TC">交易型態</span></th>
									<th><span class="TC">交易金額</span>/單位數</th>
									<th><span class="TC">憑證編號</span></th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="onway in onwayFundList">
									<td>
										<ColoredLink>
											{{ onway.proName }}
										</ColoredLink>
									</td>
									<td>{{ onway.trantypeName }}</td>
									<td class="num">
										{{ onway.onwayUnit }}
									</td>
									<td class="num">
										{{ onway.refNo }}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

				<!--庫存明細-->
				<h4 class="tx-title">
					庫存明細
				</h4>
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>依商品名稱加總明細</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsedetailtadd" />
					</div>
					<div id="collapsedetailadd" class="collapse show">
						<table class="table bih-table table-RWD">
							<thead>
								<tr>
									<th class="text-start">
										投資標的<br>
										商品代碼<br>
										<br>
										<br>
									</th>
									<th class="num">
										信託起日<br>
										參考淨值日<br>
										參考淨值(B)<br>
									</th>
									<th class="num">
										持有單位數(A)<br>
										參考匯率(C)<br>
										匯率基準日<br>
										<br>
									</th>
									<th class="num">
										投資金額(D)<br>
										投資現值(E=A*B*C)<br>
										<br>
										<br>
									</th>
									<th class="num">
										投資損益(F=E-D)<br>
										報酬率(H=F/D)<br>
										累計配息(G)<br>
										含息報酬率(I=(F+G)/D)
									</th>
								</tr>
							</thead>
							<tbody>
								<!--                                <tr>-->
								<!--                                    <td><a href="#" data-toggle="modal" data-target="#myModal">JF東協基金</a><br>-->
								<!--                                        161<br><br><br></td>-->
								<!--                                    <td class="num">2002/6/11<br>-->
								<!--                                        2016/11/17<br>-->
								<!--                                        88 TWD-->
								<!--                                    </td>-->
								<!--                                    <td class="num">1327.30<br>-->
								<!--                                        1<br>-->
								<!--                                        2016/11/17<br><br></td>-->
								<!--                                    <td class="num">50,000 TWD<br>-->
								<!--                                        117,028 TWD<br><br><br></td>-->
								<!--                                    <td class="num">67,028 TWD<br>-->
								<!--                                        134.06%<br>-->
								<!--                                        0 TWD<br>134.06%-->
								<!--                                    </td>-->
								<!--                                </tr>-->
								<!--                                <tr>-->
								<!--                                    <td><a href="#" data-toggle="modal" data-target="#myModal">富蘭克林坦伯頓成長基金</a><br>-->
								<!--                                        224<br>-->
								<!--                                        <br>-->
								<!--                                        <br>-->
								<!--                                    </td>-->
								<!--                                    <td class="num">2004/4/7<br>-->
								<!--                                        2016/11/17<br>-->
								<!--                                        9.99 USD-->
								<!--                                    </td>-->
								<!--                                    <td class="num">496.18<br>-->
								<!--                                        32,945<br>-->
								<!--                                        2016/11/17<br>-->
								<!--                                        <br>-->
								<!--                                    </td>-->
								<!--                                    <td class="num">200,000 TWD<br>-->
								<!--                                        163.303 TWD<br>-->
								<!--                                        <br>-->
								<!--                                        <br>-->
								<!--                                    </td>-->
								<!--                                    <td class="num">-36,697 TWD<br>-->
								<!--                                        -13.35%<br>-->
								<!--                                        0 TWD<br>-->
								<!--                                        -13.35%-->
								<!--                                    </td>-->
								<!--                                </tr>-->
								<!--                                <tr class="tr-sum">-->
								<!--                                    <td class="num">新臺幣 小計</td>-->
								<!--                                    <td class="num">&nbsp;</td>-->
								<!--                                    <td class="num">&nbsp;</td>-->
								<!--                                    <td class="num">-->
								<!--                                        <p>250,000 </p>-->
								<!--                                        <p>280,031 <br>-->
								<!--                                        </p>-->
								<!--                                    </td>-->
								<!--                                    <td class="num">30,931 <br>-->
								<!--                                        120.56%<br>-->
								<!--                                        0 <br>-->
								<!--                                        120.56%-->
								<!--                                    </td>-->
								<!--                                </tr>-->
							</tbody>
						</table>
					</div>
				</div>
				<!--基金單筆持有明細-->
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>基金單筆持有明細</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsedetailtmono" />
					</div>
					<div id="collapsedetailtmono" class="collapse show">
						<table class="table bih-table table-RWD">
							<thead>
								<tr>
									<th width="17%" class="text-start">
										投資標的<br>
										商品代碼<br>
										憑證編號<br>
										<br>
									</th>
									<th width="10%" class="num">
										信託起日<br>
										參考淨值日<br>
										參考淨值(B)<br>
									</th>
									<th width="11%" class="num">
										持有單位數<span class="text-start">(A)</span><br>
										參考匯率(C)<br>
										匯率基準日<br>
										<br>
									</th>
									<th width="14%" class="num">
										投資金額(D)<br>
										投資現值(E=A*B*C)<br>
										<br>
										<br>
									</th>
									<th width="17%" class="num">
										投資損益(F=E-D)<br>
										報酬率(H=F/D)<br>
										累計配息(G)<br>
										含息報酬率(I=(F+G)/D)
									</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="fund in fundList_S">
									<td>
										<ColoredLink @click.prevent="$refs.productModal?.open(fund.proCode, 'FUND')">
											{{ fund.proName }}
										</ColoredLink>
										<br>
										{{ fund.bankProCode }}<br>
										{{ fund.refNo }}<br><br>
									</td>
									<td class="num">
										{{ $filters.formatDate(fund.firstBuyDt) }}<br>
										{{ $filters.formatDate(fund.priceDt) }}<br>
										{{ $filters.formatAmt(fund.price) }} {{ fund.tranCurCode }}<br>
									</td>
									<td class="num">
										{{ $filters.formatAmt(fund.unit) }}<br>
										{{ $filters.formatAmt(fund.fxRate) }}<br>
										{{ $filters.formatDate(fund.fxRateDt) }}<br><br>
									</td>
									<td class="num">
										{{ $filters.formatAmt(fund.invAmtFc) }} {{ fund.tranCurCode }}<br>
										{{ $filters.formatAmt(fund.mktAmtFc) }} {{ fund.tranCurCode }}<br><br><br>
									</td>
									<td class="num">
										{{ $filters.formatAmt(fund.uplFc) }} {{ fund.tranCurCode }}<br>
										{{ $filters.formatPct(fund.ureturnFc) }}%<br>
										{{ $filters.formatAmt(fund.totDividendFc) }} {{ fund.tranCurCode }}<br>
										{{ $filters.formatPct(fund.ureturnFcDividend) }}%
									</td>
								</tr>
								<tr class="tr-sum">
									<td class="num">
										新臺幣 小計
									</td>
									<td class="num">
&nbsp;
									</td>
									<td class="num">
&nbsp;
									</td>
									<td class="num">
										<p>{{ $filters.formatAmt($filters.sumTotal(fundList_S, 'invAmtLc')) }}</p>
										<p>
											{{ $filters.formatAmt($filters.sumTotal(fundList_S, 'mktAmtLc')) }}<br>
										</p>
									</td>
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(fundList_S, 'mktAmtLc') -
											$filters.sumTotal(fundList_S, 'invAmtLc')) }}<br>
										{{ $filters.sumTotal(fundList_S, 'invAmtLc') > 0 ? $filters.formatAmt(($filters.sumTotal(fundList_S,
											'mktAmtLc') - $filters.sumTotal(fundList_S, 'invAmtLc')) * 100 / $filters.sumTotal(fundList_S,
												'invAmtLc')) : 0 }}%<br>
										{{ $filters.formatAmt($filters.sumTotal(fundList_S, 'totDividendLc')) }}<br>
										{{ $filters.sumTotal(fundList_S, 'invAmtLc') > 0 ? $filters.formatAmt(($filters.sumTotal(fundList_S,
											'mktAmtLc') - $filters.sumTotal(fundList_S, 'invAmtLc') + $filters.sumTotal(fundList_S,
												'totDividendLc')) * 100 / $filters.sumTotal(fundList_S, 'invAmtLc')) : 0 }}%
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!--基金定時定額持有明細-->
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>基金定時定額持有明細</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsedetailtime" />
					</div>
					<div id="collapsedetailtime" class="collapse show">
						<table class="table bih-table table-RWD">
							<thead>
								<tr>
									<th width="19%" class="text-start">
										投資標的<br>
										商品代碼<br>
										憑證編號<br>
										<br>
									</th>
									<th width="11%" class="num">
										信託起日 <br>
										參考淨值日<br>
										參考淨值(B)<br>
									</th>
									<th width="11%" class="num">
										持有單位數(A)<br>
										參考匯率(C)<br>
										匯率基準日<br>
										<br>
									</th>
									<th width="12%" class="num">
										投資金額(D)<br>
										投資現值(E=A*B*C)<br>
										<br>
										<br>
									</th>
									<th width="13%" class="num">
										投資損益(F=E-D)<br>
										報酬率(H=F/D)<br>
										累計配息(G)<br>
										含息報酬率(I=(F+G)/D)
									</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="fund in fundList_L">
									<td>
										<a href="#" @click.prevent="$refs.productModal.open(fund.proCode, 'FUND')">
											{{ fund.proName }}
										</a>
										<br>
										{{ fund.bankProCode }}
										<br>
										{{ fund.refNo }}
										<br><br>
									</td>
									<td class="num">
										{{ $filters.formatDate(fund.firstBuyDt) }}<br>
										{{ $filters.formatDate(fund.priceDt) }}<br>
										{{ $filters.formatAmt(fund.price) }} {{ fund.tranCurCode }}<br>
									</td>
									<td class="num">
										{{ $filters.formatAmt(fund.unit) }}<br>
										{{ $filters.formatAmt(fund.fxRate) }}<br>
										{{ $filters.formatDate(fund.fxRateDt) }}<br><br>
									</td>
									<td class="num">
										{{ $filters.formatAmt(fund.invAmtFc) }} {{ fund.tranCurCode }}<br>
										{{ $filters.formatAmt(fund.mktAmtFc) }} {{ fund.tranCurCode }}<br><br><br>
									</td>
									<td class="num">
										{{ $filters.formatAmt(fund.uplFc) }} {{ fund.tranCurCode }}<br>
										{{ $filters.formatPct(fund.ureturnFc) }}%<br>
										{{ $filters.formatAmt(fund.totDividendFc) }} {{ fund.tranCurCode }}<br>
										{{ $filters.formatPct(fund.ureturnFcDividend) }}%
									</td>
								</tr>
								<tr class="tr-sum">
									<td class="num">
										新臺幣 小計
									</td>
									<td class="num">
&nbsp;
									</td>
									<td class="num">
&nbsp;
									</td>
									<td class="num">
										<p>{{ $filters.formatAmt($filters.sumTotal(fundList_L, 'invAmtLc')) }}</p>
										<p>
											{{ $filters.formatAmt($filters.sumTotal(fundList_L, 'mktAmtLc')) }}<br>
										</p>
									</td>
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(fundList_L, 'mktAmtLc') -
											$filters.sumTotal(fundList_L, 'invAmtLc')) }}<br>
										{{ $filters.sumTotal(fundList_L, 'invAmtLc') > 0 ? $filters.formatAmt(($filters.sumTotal(fundList_L,
											'mktAmtLc') - $filters.sumTotal(fundList_L, 'invAmtLc')) * 100 / $filters.sumTotal(fundList_L,
												'invAmtLc')) : 0 }}%<br>
										{{ $filters.formatAmt($filters.sumTotal(fundList_L, 'totDividendLc')) }}<br>
										{{ $filters.sumTotal(fundList_L, 'invAmtLc') > 0 ? $filters.formatAmt(($filters.sumTotal(fundList_L,
											'mktAmtLc') - $filters.sumTotal(fundList_L, 'invAmtLc') + $filters.sumTotal(fundList_L,
												'totDividendLc')) * 100 / $filters.sumTotal(fundList_L, 'invAmtLc')) : 0 }}%
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!--FUND心配持有明細-->
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>FUND心配持有明細</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsedetailfund" />
					</div>
					<div id="collapsedetailfund" class="collapse show">
						<table class="table bih-table table-RWD">
							<thead>
								<tr>
									<th width="10%" class="text-start">
										投資標的<br>商品代碼<br>
										憑證編號
									</th>
									<th width="8%" class="num">
										持有單位數(A)<br>
										母子基金別
									</th>
									<th width="8%" class="num">
										信託起日<br> <br>
									</th>
									<th width="9%" class="num">
										參考淨值(B)<br>
										參考淨值日<br>
									</th>
									<th width="7%" class="num">
										參考匯率(C)<br>
										匯率基準日
									</th>
									<th width="9%" class="num">
										投資金額(D)<br>
										投資現值<br>
										(E=A*B*C)
									</th>
									<th width="9%" class="num">
										投資損益<br>
										(F=E-D)<br>
										報酬率<br>
										(H=F/D)
									</th>
									<th width="6%" class="num">
										獲利點<br>
										停損點(負)
									</th>
								</tr>
							</thead>
							<tbody>
								<template v-for="curCode in lodash_keys_fundList_MS_group">
									<tr v-for="fund in fundList_MS_group[curCode]">
										<td>
											<ColoredLink @click="$refs.productModal.open(fund.proCode, 'FUND')">
												{{ fund.proName }}
											</ColoredLink><br>
											{{ fund.bankProCode }}<br>
											{{ fund.refNo }}<br>
										</td>
										<td class="num">
											{{ $filters.formatAmt(fund.unit) }}<br>
											{{ fund.msFunds }}<br>
										</td>
										<td class="num">
											{{ $filters.formatDate(fund.firstBuyDt) }}<br>
										</td>
										<td class="num">
											{{ $filters.formatAmt(fund.price) }} {{ fund.tranCurCode }}<br>
											{{ $filters.formatDate(fund.priceDt) }}<br>
										</td>
										<td class="num">
											{{ $filters.formatAmt(fund.fxRate) }}<br>
											{{ $filters.formatDate(fund.fxRateDt) }}<br>
										</td>
										<td class="num">
											{{ $filters.formatAmt(fund.invAmtFc) }} {{ fund.tranCurCode }}<br>
											{{ $filters.formatAmt(fund.mktAmtFc) }} {{ fund.tranCurCode }}<br>
										</td>
										<td class="num">
											{{ $filters.formatAmt(fund.uplFc) }} {{ fund.tranCurCode }}<br>
											{{ $filters.formatAmt(fund.ureturnFc) }}%<br>
										</td>
										<td class="num">
											{{ $filters.formatAmt(fund.takeProfit) }}<br>
											{{ $filters.formatAmt(fund.stopLoss) }}<br>
										</td>
									</tr>
									<tr class="tr-sum">
										<td class="num">
											{{ curCode }} 小計
										</td>
										<td class="num">
&nbsp;
										</td>
										<td class="num">
&nbsp;
										</td>
										<td class="num">
&nbsp;
										</td>
										<td class="num">
&nbsp;
										</td>
										<td class="num">
											<p>{{ $filters.formatAmt($filters.sumTotal(fundList_MS_group[curCode], 'invAmtFc')) }}</p>
											<p>
												{{ $filters.formatAmt($filters.sumTotal(fundList_MS_group[curCode], 'mktAmtFc')) }}<br>
											</p>
										</td>
										<td class="num">
											{{ $filters.formatAmt($filters.sumTotal(fundList_MS_group[curCode], 'mktAmtFc') - $filters.sumTotal(fundList_MS_group[curCode], 'invAmtFc')) }}<br>
											{{ $filters.sumTotal(fundList_MS_group[curCode], 'invAmtFc') > 0 ? $filters.formatAmt(($filters.sumTotal(fundList_MS_group[curCode], 'mktAmtFc') - $filters.sumTotal(fundList_MS_group[curCode], 'invAmtFc')) * 100 / $filters.sumTotal(fundList_MS_group[curCode], 'invAmtFc')) : 0 }}%<br>
										</td>
										<td class="num">
&nbsp;
										</td>
									</tr>
								</template>
								<tr class="tr-sum">
									<td class="num">
										新臺幣 合計
									</td>
									<td class="num">
&nbsp;
									</td>
									<td class="num">
&nbsp;
									</td>
									<td class="num">
&nbsp;
									</td>
									<td class="num">
&nbsp;
									</td>
									<td class="num">
										<p>{{ $filters.formatAmt($filters.sumTotal(fundList_MS, 'invAmtLc')) }}</p>
										<p>
											{{ $filters.formatAmt($filters.sumTotal(fundList_MS, 'mktAmtLc')) }}<br>
										</p>
									</td>
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(fundList_MS, 'mktAmtLc') - $filters.sumTotal(fundList_MS, 'invAmtLc')) }}<br>
										{{ $filters.sumTotal(fundList_MS, 'invAmtLc') > 0 ? $filters.formatAmt(($filters.sumTotal(fundList_MS, 'mktAmtLc') - $filters.sumTotal(fundList_MS, 'invAmtLc')) * 100 / $filters.sumTotal(fundList_MS, 'invAmtLc')) : 0 }}%<br>
									</td>
									<td class="num">
&nbsp;
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="tx-note">
					<ul>
						<li>截至資料日期之投資組合明細(信託資金投資標的參考淨值為本行可取得之最新報價，僅供參考。)</li>
						<li>投資金額、投資損益及報酬率計算均未含相關申購費用；投資損益採計方式為依憑證編號分別計算之 。</li>
						<li>『累計配息』依憑證編號累計，憑證編號經更新後包括但不限於轉換，累計配息將重新起算。</li>
						<li>效率投資法之『信託起日』係指委託人向本行提出效率投資法投資之交易申請日。</li>
					</ul>
				</div>

				<div class="alert alert-block alert-danger fade in margin-top10">
					本功能畫面提供的有關資訊是截止統計日期，由系統根據客戶歷史投資資料自動生成的，僅供參考。
				</div>
			</div>
			<div id="SectionB" class="tab-pane fade">
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>加入同業投資部位餘額</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseadd" />
					</div>
					<div id="collapseadd" class="collapse show">
						<div class="table-responsive">
							<table class="bih-table table table-RWD">
								<thead>
									<tr>
										<th> 銀行名稱</th>
										<th> 商品名稱</th>
										<th> 幣別</th>
										<th> 金額</th>
										<th> 起始日</th>
										<th>到期日</th>
										<th>是否警示</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td data-th="銀行名稱">
											<input
												id=""
												type="text"
												name=""
												size="8"
												maxlength="11"
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="商品名稱">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="幣別">
											<select name="select" class="form-select">
												<option>--</option>
												<option>台幣</option>
												<option>美元</option>
												<option>歐元</option>
												<option>澳幣</option>
												<option>新加坡幣</option>
												<option>日圓</option>
											</select>
										</td>
										<td data-th="金額">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="起始日">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="到期日">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="是否警示">
											<input
												class="form-check-input"
												name="cpasummary.active_yn"
												type="checkbox"
												value="Y"
											>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>

				<div id="button1" class="num">
					<input
						class="btn btn-primary JQdata-hide"
						type="button"
						value="儲存"
						name="bsave"
					>
				</div>

				<div id="button2" class="num" style="display:none">
					<input
						class="btn btn-primary JQdata-show"
						type="button"
						value="修改"
						name="cancel"
						style="display:none"
						onclick="window.location='cpsaving_other_bank.htm'"
					>
					<input
						class="btn btn-primary JQdata-show"
						type="button"
						value="取消修改"
						name="cancel"
						style="display:none"
						onclick="window.location='cpsaving_other_bank.htm'"
					>
				</div>

				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>同業投資部位餘額</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapserest" />
					</div>
					<div id="collapserest" class="collapse show">
						<div class="table-responsive">
							<table class="bih-table table table-RWD">
								<thead>
									<tr>
										<th><span>銀行名稱</span></th>
										<th><span>商品名稱</span></th>
										<th><span>幣別</span></th>
										<th><span>金額</span></th>
										<th><span>起始日</span></th>
										<th><span>到期日</span></th>
										<th><span>是否警示</span></th>
										<th>執行</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td>HSBC</td>
										<td>HSBC Direct TD</td>
										<td>台幣</td>
										<td class="num">
											200,000
										</td>
										<td>2008/02/10</td>
										<td>2009/02/10</td>
										<td class="tx-center">
											<span class="TC">是</span>
										</td>
										<td class="tx-center">
											<button
												id="edit"
												type="button"
												class="btn btn-info btn-icon"
												data-bs-toggle="tooltip"
												title=""
												data-bs-original-title="編輯"
												aria-label="編輯"
											>
												<i
													class="fa-solid fa-pen"
												/>
											</button>
											<button
												type="button"
												class="btn btn-danger btn-icon"
												data-bs-toggle="tooltip"
												data-bs-original-title="刪除"
											>
												<i class="fa-solid fa-trash" />
											</button>
										</td>
									</tr>
									<tr>
										<td><span class="TC">中國信託</span></td>
										<td><span class="TC">外幣優惠存款</span></td>
										<td><span class="TC">美元</span></td>
										<td class="num">
											400,000
										</td>
										<td>2008/05/22</td>
										<td>2008/08/22</td>
										<td class="tx-center">
											<span class="TC">是</span>
										</td>
										<td class="tx-center">
											<button
												type="button"
												class="btn btn-info btn-icon"
												data-bs-toggle="tooltip"
												title=""
												data-bs-original-title="編輯"
												aria-label="編輯"
											>
												<i
													class="fa-solid fa-pen"
												/>
											</button>
											<button
												type="button"
												class="btn btn-danger btn-icon"
												data-bs-toggle="tooltip"
												data-bs-original-title="刪除"
											>
												<i class="fa-solid fa-trash" />
											</button>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>

			<div id="SectionC" class="tab-pane fade">
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>持有明細</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsehold" />
					</div>
					<div id="collapsehold" class="collapse show">
						<div class="table-responsive">
							<table class="table bih-table table-RWD">
								<thead>
									<tr>
										<th rowspan="2">
											<input id="c1-1" name="checkbox" type="checkbox">全選
										</th>
										<th rowspan="2">
											<span class="TC">基金代碼</span>
										</th>
										<th rowspan="2">
											<span class="TC">基金名稱</span>
										</th>
										<th rowspan="2">
											計價幣別
										</th>
										<th colspan="6">
											報酬率(%)
										</th>
									</tr>
									<tr>
										<th>1M</th>
										<th>3M</th>
										<th>6M</th>
										<th>1Y</th>
										<th>3Y</th>
										<th>5Y</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td>
											<div align="center">
												<input id="c1-1" name="checkbox" type="checkbox">
											</div>
										</td>
										<td>5307</td>
										<td><a href="#" data-toggle="modal" data-target="#myModal"><span class="TC">富蘭克林坦伯頓成長基金</span></a></td>
										<td>美金</td>
										<td class="num">
											5.01
										</td>
										<td class="num">
											7.12
										</td>
										<td class="num">
											8.12
										</td>
										<td class="num">
											9.58
										</td>
										<td class="num">
											10.32
										</td>
										<td class="num">
											12.21
										</td>
									</tr>
									<tr>
										<td>
											<div align="center">
												<input id="c1-2" name="c1-" type="checkbox">
											</div>
										</td>
										<td>5353</td>
										<td><a href="#"><span class="TC">JF東協基金</span></a></td>
										<td>美金</td>
										<td class="num">
											3.05
										</td>
										<td class="num">
											2.09
										</td>
										<td class="num">
											8.54
										</td>
										<td class="num">
											6.38
										</td>
										<td class="num">
											19.22
										</td>
										<td class="num">
											9.22
										</td>
									</tr>
									<tr>
										<td>
											<div align="center">
												<input id="c1-3" name="c1-2" type="checkbox">
											</div>
										</td>
										<td>3524</td>
										<td><a href="#"><span class="TC"> 高盛印度基金</span></a></td>
										<td>美金</td>
										<td class="num">
											3.98
										</td>
										<td class="num">
											2.04
										</td>
										<td class="num">
											5.43
										</td>
										<td class="num">
											12.44
										</td>
										<td class="num">
											10.43
										</td>
										<td class="num">
											2.39
										</td>
									</tr>
									<tr>
										<td>
											<div align="center">
												<input id="c1-4" name="c1-3" type="checkbox">
											</div>
										</td>
										<td>7331</td>
										<td><a href="#"><span class="TC">富蘭克林高科技基金A</span></a></td>
										<td>台幣</td>
										<td class="num">
											1.34
										</td>
										<td class="num">
											2.95
										</td>
										<td class="num">
											5.28
										</td>
										<td class="num">
											2.95
										</td>
										<td class="num">
											4.29
										</td>
										<td class="num">
											14.22
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>

				<div class="d-flex justify-content-between">
					<div class="tx-note">
						<ol>
							<span class="TC">
								<li>點選『基金績效分析』可展現各基金與比較基準的績效比較線圖。</li>
								<li>點選基金名稱，可檢視該檔基金的商品細部資料。</li>
							</span>
						</ol>
					</div>
					<div>
						<input
							id="btn"
							name="Submit1"
							type="button"
							class="btn btn-primary pull-right "
							value="基金績效分析"
							onclick="fundCompare.style.display='',pDetail.style.display='none', fundRank.style.display='none'"
						>
					</div>
				</div>

				<div id="fundCompare" class="col-sm-12 collapse-div" style="">
					<div class="card card-form card-collapse my-3">
						<div class="card-header">
							<h4>基金績效分析</h4>
							<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1" />
						</div>
						<div id="collapseListGroup1" class="collapse show">
							<div class="table-responsive">
								<table class="bih-table table table-RWD">
									<thead>
										<tr>
											<th colspan="3">
												近5年月報酬率走勢圖
											</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>選擇基金(同區域)：</td>
											<td>
												<select id="fundLocalSec" class="form-control">
													<option value="68215532">
														?豐中國A股匯聚證券投資信託基金
													</option>
													<option value="68027895">
														GAM Star 中華股票基金 美元 A 累積
													</option>
													<option value="65086493">
														GAM Star 中華股票基金 美元 累積
													</option>
												</select>
											</td>
											<td>
												<input
													id=""
													name="Submit1"
													type="button"
													class="btn btn-primary"
													value="加入"
												>
											</td>
										</tr>
										<tr>
											<td>選擇對應指數：</td>
											<td>
												<select id="benchmark" class="form-control">
													<option value="11030020">
														Citi Government/Mortgage TR
													</option>
													<option value="11000790">
														Citi Japan WGBI TR
													</option>
													<option value="11021543">
														Citi NZD 3 Months Eurodeposit
													</option>
													<option value="11000769">
														Citi United Kingdom WGBI TR
													</option>
												</select>
											</td>
											<td>
												<input
													id=""
													name="Submit1"
													type="button"
													class="btn btn-primary"
													value="加入"
												>
											</td>
										</tr>

										<tr>
											<td>選擇區域：</td>
											<td>
												<select id="sector" class="form-control">
													<option value="19041012">
														大中華股票
													</option>
													<option value="19041023">
														小型公司股票
													</option>
													<option value="19041014">
														日本股票
													</option>
													<option value="19041015">
														北美股票
													</option>
												</select>
											</td>
											<td>
												<input
													id=""
													name="Submit1"
													type="button"
													class="btn btn-primary"
													value="加入"
												>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<table id="tbl" class="table bih-table table-RWD">
								<thead>
									<tr>
										<th>基金代碼</th>
										<th>基金名稱</th>
										<th>計價幣別</th>
										<th>淨值</th>
										<th>一年報酬率</th>
										<th>今年以來累積報酬率</th>
										<th>一年標準差</th>
										<th>動作</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td>014587</td>
										<td>摩根東協基金</td>
										<td>台幣</td>
										<td>67.80</td>
										<td>3.57%</td>
										<td>72.05%</td>
										<td class="tx-center">
											0.32
										</td>
										<td class="tx-center">
											<input class="btn btn-default shiny" type="button" value="刪除">
										</td>
									</tr>
									<tr>
										<td>584752</td>
										<td>摩根中國基金</td>
										<td>台幣</td>
										<td>32.60</td>
										<td>9.03%</td>
										<td>1.54%</td>
										<td class="tx-center">
											0.48
										</td>
										<td class="tx-center">
											<input class="btn btn-default shiny" type="button" value="刪除">
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>

					<div class="tab-nav-main tab-nav-mainAsset">
						<Tabs
							variant="pill"
							:model-value="'1y'"
							:tabs="[
								{code: '1m', name: '1個月'},
								{code: '3m', name: '3個月'},
								{code: '6m', name: '6個月'},
								{code: '1y', name: '1年'},
								{code: '2y', name: '2年'},
								{code: '3y', name: '3年'},
								{code: '5y', name: '5年'},
							]"
						/>

						<img src="@/assets/images/chart/cpFundAnl.gif" class=" w-100">
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- <vue-product-modal ref="productModal"></vue-product-modal> -->
</template>
<script>
import { getImgURL } from '@/utils/imgURL';
import vueCusAssetsPieChart from '../pieChart.vue';
import _ from 'lodash';
import vueProductModal from '@/views/pro/PRO0101/include/productModal.vue';

export default {
	components: {
		vueCusAssetsPieChart,
		vueProductModal
	},
	props: {
		cusCode: String
	},
	data: function () {
		return {
			fundSecChartData: [],
			fundCurChartData: [],
			fundTypeChartData: [],
			fundList: [],
			fundList_S: [],
			fundList_L: [],
			fundList_MS: [],
			fundList_MS_group: [],
			onwayFundList: [],
			checkAll: false,
			isShowPerformances: false,
			fundCode: '',
			benchmark: '',
			selectedProCodes: [],
			observedFundsList: [],
			dsfFundMenu: [],
			dsfBenchmarkMenu: [],
			performancesId: 'fundPerformancesChart',
			chartsData: [], // 績效表現圖表資料
			proPriceRangeMenu: []
		};
	},
	computed: {
		lodash_keys_fundList_MS_group: function () {
			return _.keys(this.fundList_MS_group);
		}
	},
	watch: {
		cusCode: function () {
			this.initData();
		}
	},
	mounted: function () {
		this.initData();
	},
	methods: {
		getImgURL,
		initData() {
			this.getFundSecChartData();
			this.getFundCurChartData();
			this.getFundTypeChartData();
			this.getFundList();
			this.getOnwayFundList();
			this.getDsfFundMenu();
			this.getDsfBenchmarkMenu();
			this.getProPriceRangeMenu();
		},
		async getFundSecChartData() {
			const result = await this.$api.getCusFundGroupBySectorApi({
				cusCode: this.cusCode
			});
			this.fundSecChartData = result.data;
			this.fundSecChartData.forEach((fund) => {
				fund.category = fund.sectorName;
				fund.value = fund.mktAmtLc + fund.totDividendLc;
			});
		},
		async getFundCurChartData() {
			const result = await this.$api.getCusFundGroupByCurApi({
				cusCode: this.cusCode
			});
			this.fundCurChartData = result.data;
			this.fundCurChartData.forEach((fund) => {
				fund.category = fund.proCurName;
				fund.value = fund.mktAmtLc + fund.totDividendLc;
			});
		},
		async getFundTypeChartData() {
			const result = await this.$api.getCusFundGroupByProtypeApi({
				cusCode: this.cusCode
			});
			this.fundTypeChartData = result.data;
			this.fundTypeChartData.forEach((fund) => {
				fund.category = fund.protypeName;
				fund.value = fund.mktAmtLc + fund.totDividendLc;
			});
		},
		async getFundList() {
			const result = await this.$api.getCusFundApi({
				cusCode: this.cusCode
			});
			this.fundList = result.data;
			this.fundList_S = this.fundList.filter((fund) => {
				return fund.invType === 'S';
			});
			this.fundList_L = this.fundList.filter((fund) => {
				return fund.invType === 'L';
			});
			this.fundList_MS = this.fundList.filter((fund) => {
				return fund.invType === 'L' && (fund.msFunds === 'M' || fund.msFunds === 'S');
			});
			this.fundList_MS_group = _.groupBy(this.fundList_MS, 'tranCurCode');
		},
		async getOnwayFundList() {
			const result = await this.$api.getCusOnwayApi({
				cusCode: this.cusCode,
				pfcatCode: 'FUND'
			});
			this.onwayFundList = result.data;
		},
		async getDsfFundMenu() {
			const self = this;
			const ret = await self.$api.getDsfFundMenuApi();
			if (!_.isNil(ret.data)) {
				self.dsfFundMenu = ret.data;
			}
		},
		async getDsfBenchmarkMenu() {
			const self = this;
			const ret = await self.$api.getDsfBenchmarkMenuApi();
			if (!_.isNil(ret.data)) {
				self.dsfBenchmarkMenu = ret.data;
			}
		},
		async getProPriceRangeMenu() {
			const ret = await this.$api.getPriceTimeRangeMenuApi();
			this.proPriceRangeMenu = ret.data;
		},
		toggleCheckAll() {
			this.selectedProCodes = [];
			if (this.checkAll) {
				this.selectedProCodes = this.fundList.reduce(function (proCodes, fund) {
					if (fund.lipperId && !proCodes.includes(fund.lipperId)) {
						proCodes.push(fund.lipperId);
					}
					return proCodes;
				}, []);
			}
		},
		async calPerformances() {
			if (this.selectedProCodes.length === 0) {
				this.$bi.message('至少勾選一筆商品');
				return;
			}
			const ret = await self.$api.getObservedFundsApi({
				lipperIds: this.selectedProCodes.join()
			});
			if (!_.isNil(ret.data)) {
				this.observedFundsList = ret.data;
				this.getFundPerformances(
					ret.data.map(v => v.proCode),
					'Y',
					-1.0
				);
			}
			this.isShowPerformances = true;
		},
		// 績效表現-加入ETF(ETF/美股)
		addFund() {
			const self = this;
			let pk = null;
			pk = _.find(self.selectedProCodes, function (item) {
				return item == self.fundCode;
			});
			if (self.fundCode != null && pk == null) {
				self.selectedProCodes.push(self.fundCode); // 加入選擇商品
				self.calPerformances();
			}
			else if (self.fundCode != null && pk != null) {
				self.$bi.message('此商品已加入');
			}
			else {
				self.$bi.message('請選擇商品');
			}
		},
		// 績效表現-加入ETF(對應指數)
		addBenchmark() {
			const self = this;
			let pk = null;
			pk = _.find(self.selectedProCodes, function (item) {
				return item == self.benchmark;
			});
			if (self.benchmark != null && pk == null) {
				self.selectedProCodes.push(self.benchmark); // 加入選擇商品
				self.calPerformances();
			}
			else if (self.benchmark != null && pk != null) {
				self.$bi.message('此商品已加入');
			}
			else {
				self.$bi.message('請選擇商品');
			}
		},
		// 績效表現 已加入商品 刪除按鈕
		deleteObservedFunds(proCode) {
			const self = this;
			if (self.selectedProCodes.length > 1) {
				const index = self.selectedProCodes.indexOf(proCode); // 找出要移除的index
				self.selectedProCodes.splice(index, 1); // 移除加入的商品(要插入或刪除的索引位置, 要刪除的元素數量)
				self.calPerformances();
			}
			else {
				self.$bi.message('至少要有一項商品');
			}
		},
		getFundPerformances: async function (proCodes, rangeType, rangeFixed) {
			const self = this;

			const ret = await self.$api.postFundPrformanceHisApi({
				proCodes: proCodes,
				freqType: rangeType,
				freqFixed: rangeFixed
			});
			if (!_.isNil(ret.data)) {
				for (let i = 0; i < ret.data.length; i++) {
					ret.data[i].datas.forEach((e) => {
						e.value = e.returnFc;
						e.date = new Date(e.returnDt).getTime();
					});
				}
				self.chartsData = ret.data;
				self.$refs.fundPerformancesChartRef.initChart(self.chartsData);
			}
		}
	}
};
</script>
