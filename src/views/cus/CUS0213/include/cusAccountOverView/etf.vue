<template>
	<div class="tab-nav-main">
		<ul class="nav nav-pills">
			<li class="nav-item">
				<a class="nav-link active" href="#SectionA" data-bs-toggle="tab">本行部位餘額（前一日）</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#SectionB" data-bs-toggle="tab">同業部位餘額</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#SectionC" data-bs-toggle="tab">即時未實現投資餘額</a>
			</li>
		</ul>
		<div class="tab-content">
			<div id="SectionA" class="tab-pane fade active show">
				<div class="card card-table card-tabs">
					<div class="card-header">
						<h4>ETF分析(不含在途)</h4>
						<ul class="nav nav-pills  card-header-pills">
							<li>
								<a href="#tab-mkt" class="nav-link active" data-bs-toggle="pill">依投資市場</a>
							</li>
							<li>
								<a href="#tab-coin" class="nav-link" data-bs-toggle="pill">依信託幣別</a>
							</li>
							<li>
								<a href="#tab-type" class="nav-link" data-bs-toggle="pill">依ETF類型</a>
							</li>
						</ul>
					</div>
					<div class="tab-content">
						<!-- 依投資市場 -->
						<div id="tab-mkt" class="tab-pane fade show active">
							<div class="row g-3 align-items-center mb-3">
								<div class="col-lg-5">
									<vue-cus-assets-pie-chart chart-id="etfSecChart" :chart-data="etfSecChartData" />
								</div>
								<div class="col-lg-7">
									<div class="tx-13 num">
										(新臺幣:元)
									</div>
									<table id="selAST" class="table bih-table table-RWD">
										<thead>
											<tr>
												<th><span>投資市場</span></th>
												<th class="num">
													<span>投資現值</span>
												</th>
												<th class="num">
													<span>累計配息</span>
												</th>
												<th class="num">
													<span>投資現值(含息)</span>
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="etf in etfSecChartData">
												<td data-th="投資市場">
													{{ etf.sectorName }}
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt(etf.mktAmtLc) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt(etf.totDividendLc) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt(etf.mktAmtLc + etf.totDividendLc)
													}}
												</td>
											</tr>
											<tr class="tx-sum bg-subtotal">
												<td data-th="投資市場">
													小計
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(etfSecChartData,
														'mktAmtLc')) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(etfSecChartData,
														'totDividendLc')) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(etfSecChartData, 'mktAmtLc') +
														$filters.sumTotal(etfSecChartData, 'totDividendLc')) }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>

						<!-- 依信託幣別 -->
						<div id="tab-coin" class="tab-pane fade">
							<div class="row g-3 align-items-center mb-3">
								<div class="col-lg-5">
									<vue-cus-assets-pie-chart chart-id="etfCurChart" :chart-data="etfCurChartData" />
								</div>
								<div class="col-lg-7">
									<table id="selCUR" class="table bih-table table-RWD">
										<thead>
											<tr>
												<th>
													<span>信託幣別</span><!--span class="EN">Currency</span-->
												</th>
												<th class="num">
													<span>投資現值</span><!--span class="EN">Total cost</span-->
												</th>
												<th class="num">
													<span>累計配息</span><!--span class="EN">Market value</span-->
												</th>
												<th class="num">
													<span>投資現值(含息)</span><!--span class="EN">Gain/Loss</span-->
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="etf in etfCurChartData">
												<td data-th="信託幣別">
													{{ etf.proCurName }}
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt(etf.mktAmtLc) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt(etf.totDividendLc) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt(etf.mktAmtLc + etf.totDividendLc)
													}}
												</td>
											</tr>
											<tr class="tx-sum bg-subtotal">
												<td data-th="信託幣別">
													小計
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(etfCurChartData,
														'mktAmtLc')) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(etfCurChartData,
														'totDividendLc')) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(etfCurChartData, 'mktAmtLc') +
														$filters.sumTotal(etfCurChartData, 'totDividendLc')) }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>

						<!-- 依基金類型 -->
						<div id="tab-type" class="tab-pane fade ">
							<div class="row g-3 align-items-center mb-3">
								<div class="col-lg-5">
									<vue-cus-assets-pie-chart chart-id="etfTypeChart" :chart-data="etfTypeChartData" />
								</div>
								<div class="col-lg-7">
									<div class="tx-13 num">
										(新臺幣:元)
									</div>
									<table id="selAST" class="table bih-table table-RWD">
										<thead>
											<tr>
												<th><span>基金類型</span></th>
												<th class="num">
													<span>投資現值</span>
												</th>
												<th class="num">
													<span>累計配息</span>
												</th>
												<th class="num">
													<span>投資現值(含息)</span>
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="etf in etfTypeChartData">
												<td data-th="ETF類型">
													{{ etf.protypeName }}
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt(etf.mktAmtLc) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt(etf.totDividendLc) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt(etf.mktAmtLc + etf.totDividendLc)
													}}
												</td>
											</tr>
											<tr class="tx-sum bg-subtotal">
												<td data-th="ETF類型">
													小計
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(etfTypeChartData,
														'mktAmtLc')) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(etfTypeChartData,
														'totDividendLc')) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(etfTypeChartData, 'mktAmtLc') +
														$filters.sumTotal(etfTypeChartData, 'totDividendLc')) }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>在途明細</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsedetail" />
					</div>
					<div id="collapsedetail" class="collapse show">
						<table class="table bih-table table-RWD">
							<thead>
								<tr>
									<th>交易日</th>
									<th><span class="TC">交易類別</span></th>
									<th>
										<p><span class="TC">投資標的</span></p>
									</th>
									<th>股數</th>
									<th>成交價格</th>
									<th><span class="TC">成交序號</span></th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="onway in onwayEtfList">
									<td>{{ $filters.formatDate(onway.orderDt) }}</td>
									<td>{{ onway.trantypeName }}</td>
									<td>
										<a href="#"><span class="TC">{{ onway.proName }}
										</span></a>
									</td>
									<td class="num">
										{{ $filters.formatAmt(onway.onwayUnit) }}
									</td>
									<td class="num">
										{{ $filters.formatAmt(onway.onwayAmtFc) }}
									</td>
									<td class="num">
										{{ onway.refNo }}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

				<h4 class="tx-title">
					庫存明細
				</h4>
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>依商品名稱加總明細</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsedetailtadd" />
					</div>
					<div id="collapsedetailtadd" class="collapse show">
						<table class="table bih-table table-RWD">
							<thead>
								<tr>
									<th class="text-start" colspan="8">
										商品名稱
									</th>
								</tr>
								<tr>
									<th width="10%" class="num">
										商品代號<br>交易市場
									</th>
									<th width="9%" class="num">
										幣別<br>計價幣別
									</th>
									<th width="8%" class="num">
										可交易股數含配股(A)<br>信託金額
									</th>
									<th width="13%" class="num">
										參考價格(B)<br>參考價格日
									</th>
									<th width="9%" class="num">
										參考現值(A)*(B)<br>參考信託管理費
									</th>
									<th width="9%" class="num">
										<br>累計配息
									</th>
									<th width="6%" class="num">
										投資損益<br>含息損益
									</th>
									<th width="9%" class="num">
										報酬率(%)<br>含息報酬率
									</th>
								</tr>
							</thead>
							<tbody>
								<template v-for="etf in etfList">
									<tr>
										<td class="text-start" colspan="8">
											<a
												href="#"
												@click.prevent="$refs.productModal.open(etf.proCode, 'ETF')"
											>{{ etf.proName }}</a>
										</td>
									</tr>
									<tr>
										<td width="10%" class="num">
											{{ etf.bankProCode }}<br>{{ etf.exCode }}
										</td>
										<td width="9%" class="num">
											{{ etf.tranCurCode }}<br>{{ etf.curCode }}
										</td>
										<td width="8%" class="num">
											{{ $filters.formatAmt(etf.unit) }}<br>{{ $filters.formatAmt(etf.invAmtFc)
											}}
										</td>
										<td width="13%" class="num">
											{{ $filters.formatAmt(etf.price) }}<br>{{ $filters.formatDate(etf.priceDt) }}
										</td>
										<td width="9%" class="num">
											{{ $filters.formatAmt(etf.mktAmtFc) }}<br>{{ $filters.formatAmt(etf.mFee)
											}}
										</td>
										<td width="9%" class="num">
											<br>{{ $filters.formatAmt(etf.totDividendFc) }}
										</td>
										<td width="6%" class="num">
											{{ $filters.formatAmt(etf.uplFc) }}<br>{{ $filters.formatAmt(etf.uplFcDividend) }}
										</td>
										<td width="9%" class="num">
											{{ $filters.formatPct(etf.ureturnFc) }}%<br>{{ $filters.formatPct(etf.ureturnFcDividend) }}%
										</td>
									</tr>
								</template>
								<tr class="tr-sum">
									<td class="num" colspan="2">
										新台幣 小計
									</td>
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(etfList, 'invAmtFc')) }}
									</td>
									<td class="num" />
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(etfList, 'mktAmtFc')) }}
									</td>
									<td />
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(etfList,
											'uplFc')) }}<br>{{ $filters.formatAmt($filters.sumTotal(etfList, 'uplFcDividend')) }}
									</td>
									<td class="num">
										{{ $filters.formatAmt(($filters.sumTotal(etfList, 'mktAmtFc') - $filters.sumTotal(etfList,
											'invAmtFc')) * 100 /
											$filters.sumTotal(etfList, 'invAmtFc')) }}%<br>
										{{ $filters.formatAmt(($filters.sumTotal(etfList, 'mktAmtFc') - $filters.sumTotal(etfList,
											'invAmtFc') +
											$filters.sumTotal(etfList, 'totDividendFc')) * 100 / $filters.sumTotal(etfList, 'invAmtFc')) }}%
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

				<div class="tx-note">
					<ol>
						<li>截至資料日期之投資組合明細(信託資金投資標的參考淨值為本行可取得之最新報價，僅供參考。)</li>
						<li>投資金額、投資損益及報酬率計算均未含相關申購費用；庫存投資金額係採先進先出法依投資標的分別計算之 。</li>
					</ol>
				</div>

				<div class="alert alert-block alert-danger fade in margin-top10">
					本功能畫面提供的有關資訊是截止統計日期，由系統根據客戶歷史投資資料自動生成的，僅供參考。
				</div>
			</div>

			<div id="SectionB" class="tab-pane fade">
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>加入同業投資部位餘額</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseadd" />
					</div>
					<div id="collapseadd" class="collapse show">
						<div class="table-responsive">
							<table class="bih-table table table-RWD">
								<thead>
									<tr>
										<th> 銀行名稱</th>
										<th> 商品名稱</th>
										<th> 幣別</th>
										<th> 金額</th>
										<th> 起始日</th>
										<th>到期日</th>
										<th>是否警示</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td data-th="銀行名稱">
											<input
												id=""
												type="text"
												name=""
												size="8"
												maxlength="11"
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="商品名稱">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="幣別">
											<select name="select" class="form-select">
												<option>--</option>
												<option>台幣</option>
												<option>美元</option>
												<option>歐元</option>
												<option>澳幣</option>
												<option>新加坡幣</option>
												<option>日圓</option>
											</select>
										</td>
										<td data-th="金額">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="起始日">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="到期日">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="是否警示">
											<input
												class="form-check-input"
												name="cpatr-summary.active_yn"
												type="checkbox"
												value="Y"
											>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>

				<div id="button1" class="num">
					<input
						class="btn btn-primary JQdata-hide"
						type="button"
						value="儲存"
						name="bsave"
					>
				</div>

				<div id="button2" class="num" style="display:none">
					<input
						class="btn btn-primary JQdata-show"
						type="button"
						value="修改"
						name="cancel"
						style="display:none"
						onclick="window.location='cpsaving_other_bank.htm'"
					>
					<input
						class="btn btn-primary JQdata-show"
						type="button"
						value="取消修改"
						name="cancel"
						style="display:none"
						onclick="window.location='cpsaving_other_bank.htm'"
					>
				</div>

				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>同業投資部位餘額</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapserest" />
					</div>
					<div id="collapserest" class="collapse show">
						<div class="table-responsive">
							<table class="bih-table table table-RWD">
								<thead>
									<tr>
										<th><span class="TC">銀行名稱</span></th>
										<th><span class="TC">商品名稱</span></th>
										<th><span class="TC">幣別</span></th>
										<th><span class="TC">金額</span></th>
										<th><span class="TC">起始日</span></th>
										<th><span class="TC">到期日</span></th>
										<th><span class="TC">是否警示</span></th>
										<th>執行</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td>HSBC</td>
										<td>HSBC Direct TD</td>
										<td>台幣</td>
										<td class="num">
											200,000
										</td>
										<td>2008/02/10</td>
										<td>2009/02/10</td>
										<td class="text-alignCenter">
											<span class="TC">是</span>
										</td>
										<td class="text-alignCenter">
											<button
												id="edit"
												type="button"
												class="btn btn-info btn-icon"
												data-bs-toggle="tooltip"
												title=""
												data-bs-original-title="編輯"
												aria-label="編輯"
											>
												<i class="fa-solid fa-pen" />
											</button>
											<button
												type="button"
												class="btn btn-danger btn-icon"
												data-bs-toggle="tooltip"
												data-bs-original-title="刪除"
											>
												<i class="fa-solid fa-trash" />
											</button>
										</td>
									</tr>
									<tr>
										<td><span class="TC">中國信託</span></td>
										<td><span class="TC">外幣優惠存款</span></td>
										<td><span class="TC">美元</span></td>
										<td class="num">
											400,000
										</td>
										<td>2008/05/22</td>
										<td>2008/08/22</td>
										<td class="text-alignCenter">
											<span class="TC">是</span>
										</td>
										<td class="text-alignCenter">
											<button
												type="button"
												class="btn btn-info btn-icon"
												data-bs-toggle="tooltip"
												title=""
												data-bs-original-title="編輯"
												aria-label="編輯"
											>
												<i class="fa-solid fa-pen" />
											</button>
											<button
												type="button"
												class="btn btn-danger btn-icon"
												data-bs-toggle="tooltip"
												data-bs-original-title="刪除"
											>
												<i class="fa-solid fa-trash" />
											</button>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>

			<div id="SectionC" class="tab-pane fade" />
		</div>
	</div>
	<!-- <vue-product-modal ref="productModal"></vue-product-modal> -->
</template>
<script>
import _ from 'lodash';
import vueCusAssetsPieChart from '../pieChart.vue';
import vueProductModal from '../../../../pro/PRO0101/include/productModal.vue';

export default {
	components: {
		vueCusAssetsPieChart,
		vueProductModal
	},
	props: {
		cusCode: String
	},
	data: function () {
		return {
			etfSecChartData: [],
			etfCurChartData: [],
			etfTypeChartData: [],
			etfList: [],
			onwayEtfList: [],
			checkAll: false,
			isShowPerformances: false,
			profileName: '',
			benchmark: '',
			classMajor: '',
			selectedProCodes: [],
			observedEtfsList: [], // 績效表現-已加入ETF表資料
			etfProfileNameMenu: [], // 績效表現-ETF/美股下拉
			etfProfileBenchmarksMenu: [], // 績效表現-對應指數下拉
			etfClassMajorMenu: [],
			performancesId: 'etfPerformancesChart',
			chartsData: [], // 績效表現圖表資料
			proPriceRangeMenu: []
		};
	},
	watch: {
		cusCode: function () {
			this.initData();
		}
	},
	mounted: function () {
		this.initData();
	},
	methods: {
		initData() {
			this.getEtfSecChartData();
			this.getEtfCurChartData();
			this.getEtfTypeChartData();
			this.getEtfList();
			this.getOnwayEtfList();
			this.getEtfProfileNameMenu();
			this.getEtfProfileBenchmarksMenu();
			this.getEtfClassMajorMenu();
			this.getProPriceRangeMenu();
		},
		async getEtfSecChartData() {
			const result = await this.$api.getCusEtfGroupBySectorApi({
				cusCode: this.cusCode
			});
			this.etfSecChartData = result.data;
			this.etfSecChartData.forEach((etf) => {
				etf.category = etf.sectorName;
				etf.value = etf.mktAmtLc + etf.totDividendLc;
			});
		},
		async getEtfCurChartData() {
			const result = await this.$api.getCusEtfGroupByCurApi({
				cusCode: this.cusCode
			});
			this.etfCurChartData = result.data;
			this.etfCurChartData.forEach((etf) => {
				etf.category = etf.proCurName;
				etf.value = etf.mktAmtLc + etf.totDividendLc;
			});
		},
		async getEtfTypeChartData() {
			const result = await this.$api.getCusEtfGroupByProtypeApi({
				cusCode: this.cusCode
			});
			this.etfTypeChartData = result.data;
			this.etfTypeChartData.forEach((etf) => {
				etf.category = etf.protypeName;
				etf.value = etf.mktAmtLc + etf.totDividendLc;
			});
		},
		async getEtfList() {
			const result = await this.$api.getCusEtfApi({
				cusCode: this.cusCode
			});
			this.etfList = result.data;
		},
		async getOnwayEtfList() {
			const result = await this.$api.getCusOnwayApi({
				cusCode: this.cusCode,
				pfcatCode: 'ETF'
			});
			this.onwayEtfList = result.data;
		},
		// 績效分析-ETF/美股下拉
		async getEtfProfileNameMenu() {
			const self = this;
			const ret = await self.$api.getEtfProfileNameMenuApi();
			if (!_.isNil(ret.data)) {
				self.etfProfileNameMenu = ret.data;
			}
		},
		// 績效分析-對應指數下拉
		async getEtfProfileBenchmarksMenu() {
			const self = this;
			const ret = await self.$api.getEtfProfileBenchmarksMenuApi();
			if (!_.isNil(ret.data)) {
				self.etfProfileBenchmarksMenu = ret.data;
			}
		},
		async getEtfClassMajorMenu() {
			const ret = await this.$api.getEtfPerformanceClassMajorMenuApi();
			if (!_.isNil(ret.data)) {
				this.etfClassMajorMenu = ret.data;
			}
		},
		async getProPriceRangeMenu() {
			const ret = await this.$api.getPriceTimeRangeMenuApi();
			this.proPriceRangeMenu = ret.data;
		},
		toggleCheckAll() {
			this.selectedProCodes = [];
			if (this.checkAll) {
				this.selectedProCodes = this.etfList.reduce(function (proCodes, etf) {
					if (etf.proCode && !proCodes.includes(etf.proCode)) {
						proCodes.push(etf.proCode);
					}
					return proCodes;
				}, []);
			}
		},
		async calPerformances() {
			if (this.selectedProCodes.length === 0) {
				this.$bi.message('至少勾選一筆商品');
				return;
			}
			const ret = await this.$api.getObservedEtfsApi({
				proCodes: this.selectedProCodes.join()
			});
			if (!_.isNil(ret.data)) {
				this.observedEtfsList = ret.data;
			}
			this.getEtfPerformances('Y', -1.0);
			this.isShowPerformances = true;
		},
		// 績效表現-加入ETF(ETF/美股)
		addEtf() {
			const self = this;
			let pk = null;
			pk = _.find(self.selectedProCodes, function (item) {
				return item == self.profileName;
			});
			if (self.profileName != null && pk == null) {
				self.selectedProCodes.push(self.profileName); // 加入選擇商品
				self.calPerformances();
			}
			else if (self.profileName != null && pk != null) {
				self.$bi.message('此商品已加入');
			}
			else {
				self.$bi.message('請選擇商品');
			}
		},
		// 績效表現-加入ETF(對應指數)
		addBenchmark() {
			const self = this;
			let pk = null;
			pk = _.find(self.selectedProCodes, function (item) {
				return item == self.benchmark;
			});
			if (self.benchmark != null && pk == null) {
				self.selectedProCodes.push(self.benchmark); // 加入選擇商品
				self.calPerformances();
			}
			else if (self.benchmark != null && pk != null) {
				self.$bi.message('此商品已加入');
			}
			else {
				self.$bi.message('請選擇商品');
			}
		},
		addClassMajor() {
			const self = this;
			let pk = null;
			pk = _.find(self.selectedProCodes, function (item) {
				return item == self.classMajor;
			});
			if (self.classMajor != null && pk == null) {
				self.selectedProCodes.push(self.classMajor); // 加入選擇商品
				self.calPerformances();
			}
			else if (self.classMajor != null && pk != null) {
				self.$bi.message('此商品已加入');
			}
			else {
				self.$bi.message('請選擇商品');
			}
		},
		// 績效表現 已加入商品 刪除按鈕
		deleteObservedEtfs(proCode) {
			const self = this;
			if (self.selectedProCodes.length > 1) {
				const index = self.selectedProCodes.indexOf(proCode); // 找出要移除的index
				self.selectedProCodes.splice(index, 1); // 移除加入的商品(要插入或刪除的索引位置, 要刪除的元素數量)
				self.calPerformances();
			}
			else {
				self.$bi.message('至少要有一項商品');
			}
		},
		getEtfPerformances: async function (rangeType, rangeFixed) {
			const self = this;
			const ret = await self.$api.postFundPrformanceHisApi({
				proCodes: self.selectedProCodes,
				freqType: rangeType,
				freqFixed: rangeFixed
			});
			if (!_.isNil(ret.data)) {
				for (let i = 0; i < ret.data.length; i++) {
					ret.data[i].datas.forEach((e) => {
						e.value = e.returnFc;
						e.date = new Date(e.returnDt).getTime();
					});
				}
				self.chartsData = ret.data;
				self.$refs.etfPerformancesChartRef.initChart(self.chartsData);
			}
		}
	}
};
</script>
