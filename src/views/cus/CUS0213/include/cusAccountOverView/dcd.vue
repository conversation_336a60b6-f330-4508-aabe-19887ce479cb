<template>
	<div class="card card-table card-collapse mb-0">
		<div class="card-header">
			<h4>庫存明細</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup12" />
		</div>
		<div id="collapseListGroup12" class="collapse show">
			<div class="table-responsive">
				<table class="table table-striped table-RWD table-bordered">
					<thead>
						<tr>
							<th>商品代號</th>
							<th>商品名稱</th>
							<th>
								起息日<br>
								到期日
							</th>
							<th>連結標的</th>
							<th>幣別</th>
							<th>存單字號</th>
							<th>投資金額(原幣)</th>
							<th>評估損益(原幣)</th>
						</tr>
					</thead>
					<tbody>
						<template v-for="dcd in dcdList">
							<tr v-if="dcd && (dcd.aciCode || '') !== '13'">
								<td data-th="商品代號">
									{{ dcd.bankProCode }}
								</td>
								<td data-th="商品名稱">
									{{ dcd.proName }}
								</td>
								<td data-th="起息日">
									{{ $filters.formatDate(dcd.proStartDt) }}<br>
									{{ $filters.formatDate(dcd.proEndDt) }}
								</td>
								<td data-th="連結標的">
									{{ dcd.snProTypeName }}
								</td>
								<td data-th="幣別">
									{{ dcd.tranCurCode }}
								</td>
								<td data-th="存單字號">
									<a href="#" @click="getDcdDivList(dcd)">{{ dcd.refNo }}</a>
								</td>
								<td data-th="投資金額(原幣)">
									{{ $filters.formatAmt(dcd.invAmtFc) }}
								</td>
								<td data-th="評估損益(原幣)">
									{{ makeUplFc(dcd) }}
								</td>
							</tr>
						</template>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<div class="tx-note">
		<ul>
			<li>截至資料日期之投資組合明細(信託資金投資標的參考價格為本行可取得之最新報價，僅供參考。)</li>
			<li>『累計配息』依憑證編號累計，憑證編號經更新後包括但不限於轉換，累計配息將重新起算。</li>
			<li>投資金額、投資損益及報酬率計算均未含相關申購費用；投資損益採計方式為依憑證編號分別計算之。</li>
			<li>到期保本率係為原幣信託且未扣除信託管理費。</li>
		</ul>
	</div>
	<!-- modal  -->
	<vue-modal :is-open="isOpenModal" @close="() => isOpenModal = false">
		<template #content="props">
			<div class="modal-dialog modal-xl modal-dialog-centered">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							投資組合配息資料
						</h4>
						<button type="button" class="btn-expand">
							<i class="bi bi-arrows-fullscreen" />
						</button>
						<button
							type="button"
							class="btn-close"
							data-bs-dismiss="modal"
							aria-label="Close"
						/>
					</div>
					<div class="modal-body">
						<div class="card card-table card-collapse mb-0">
							<div class="card-header">
								<h4>存單字號</h4>
								<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup13" />
							</div>
							<div id="collapseListGroup13" class="collapse show">
								<div class="table-responsive">
									<table class="table table-striped table-RWD table-bordered">
										<thead>
											<tr>
												<th>商品代號</th>
												<th>商品名稱</th>
												<th>投資幣別</th>
												<th>投資金額</th>
												<th>起息日</th>
												<th>到期日</th>
												<th>定存帳號</th>
												<th>存單字號</th>
												<th>決價方式</th>
												<th>提前到期日</th>
											</tr>
										</thead>
										<tbody>
											<tr v-if="selectedDcd != null">
												<td data-th="商品代號">
													{{ selectedDcd.bankProCode }}
												</td>
												<td data-th="商品名稱">
													{{ selectedDcd.proName }}
												</td>
												<td data-th="投資幣別">
													{{ selectedDcd.tranCurCode }}
												</td>
												<td data-th="投資金額">
													{{ $filters.formatAmt(selectedDcd.invAmtFc) }}
												</td>
												<td data-th="起息日">
													{{ $filters.formatDate(selectedDcd.intStartDt) }}
												</td>
												<td data-th="到期日">
													{{ $filters.formatDate(selectedDcd.intEndDt) }}
												</td>
												<td data-th="定存帳號">
													{{ selectedDcd.accNo }}
												</td>
												<td data-th="存單字號">
													{{ selectedDcd.refNo }}
												</td>
												<td data-th="決價方式">
													{{ makeKindLabel }}
												</td>
												<!--                                                <td data-th="決價方式">{{selectedDcd.makeKind}}</td>-->
												<td data-th="提前到期日">
													{{ $filters.formatDate(selectedDcd.preEndDt) }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
						<div class="card card-table card-collapse mb-0">
							<div class="card-header">
								<h4>配息資料</h4>
								<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup14" />
							</div>
							<div id="collapseListGroup14" class="collapse show">
								<div class="table-responsive">
									<table class="table table-striped table-RWD table-bordered">
										<thead>
											<tr>
												<th>決價</th>
												<th>年化收益率</th>
												<th>計息起日</th>
												<th>計息迄日</th>
												<th>配息金額(未扣稅)</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="(dcdDiv, i) in dcdDivList">
												<td data-th="決價">
													第{{ i + 1 }}次
												</td>
												<td data-th="年化收益率">
													{{ $filters.formatAmt(dcdDiv.proBenefitRate) }}%
												</td>
												<td data-th="計息起日">
													{{ $filters.formatDate(dcdDiv.proBeginDt) }}
												</td>
												<td data-th="計息迄日">
													{{ $filters.formatDate(dcdDiv.proEndDt) }}
												</td>
												<td data-th="配息金額(未扣稅)">
													{{ $filters.formatAmt(dcdDiv.benefitAmt) }}
												</td>
											</tr>
											<tr v-if="dcdDivList?.length > 0">
												<td data-th="決價">
													小計
												</td>
												<td data-th="年化收益率" />
												<td data-th="計息起日" />
												<td data-th="計息迄日" />
												<td data-th="配息金額(未扣稅)">
													{{ $filters.formatAmt($filters.sumTotal(dcdDivList, 'benefitAmt')) }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<input
							class="btn btn-white"
							type="button"
							value="關閉"
							data-bs-dismiss="modal"
						>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- modal end -->
</template>
<script>
import vueModal from '@/views/components/model.vue';
import _ from 'lodash';
export default {
	components: {
		vueModal
	},
	props: {
		cusCode: String,
		fxRate: {
			type: Number,
			default: 1
		},
		baseCurrencyName: {
			type: String,
			default: '新台幣'
		}
	},
	data: function () {
		return {
			dcdList: [],
			selectedDcd: {},
			dcdDivList: [],
			isOpenModal: null
		};
	},
	computed: {
		makeKindLabel() {
			if (this.selectedDcd.makeKind === '0') {
				return '單次決價';
			}
			else if (this.selectedDcd.makeKind === '1') {
				return '多次決價';
			}
			return '';
		}
	},
	watch: {
		cusCode: function () {
			this.initData();
		}
	},
	mounted: function () {
		this.initData();
	},
	methods: {
		initData() {
			this.getDcdList();
		},
		async getDcdList() {
			const result = await this.$api.getCusDcdApi({
				cusCode: this.cusCode
			});
			this.dcdList = result.data;
		},
		async getDcdDivList(dcd) {
			this.selectedDcd = dcd;
			const result = await this.$api.getCusDcdDividendApi({
				cusCode: this.cusCode,
				refNo: dcd.refNo
			});
			this.dcdDivList = result.data;
			this.isOpenModal = true;
		},
		makeUplFc(dcd) {
			if (_.startsWith(dcd.bankProCode, 'V0')) {
				return this.$filters.formatAmt(dcd.uplFc);
			}
			if (_.isNil(dcd.aprice) || _.isNil(dcd.invAmtFc)) {
				return this.$filters.formatAmt(0);
			}
			return this.$filters.formatAmt((dcd.aprice / 100.0) * dcd.invAmtFc - dcd.invAmtFc);
		}
	}
};
</script>
