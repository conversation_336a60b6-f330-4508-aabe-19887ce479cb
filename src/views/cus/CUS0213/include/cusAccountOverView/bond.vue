<template>
	<div class="tab-nav-main">
		<ul class="nav nav-pills">
			<li class="nav-item">
				<a class="nav-link active" href="#SectionA" data-bs-toggle="tab">本行部位餘額（前一日）</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#SectionB" data-bs-toggle="tab">同業部位餘額</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#SectionC" data-bs-toggle="tab">即時未實現投資餘額</a>
			</li>
		</ul>
		<div class="tab-content">
			<div id="SectionA" class="tab-pane fade active show">
				<!--債券分析(不含在途)-->
				<div class="card card-table card-tabs">
					<div class="card-header">
						<h4>債券分析(不含在途)</h4>
						<ul class="nav nav-pills  card-header-pills">
							<li>
								<a href="#tab-mkt" class="nav-link active" data-bs-toggle="pill">依債券類型</a>
							</li>
							<li>
								<a href="#tab-coin" class="nav-link" data-bs-toggle="pill">依信託幣別</a>
							</li>
						</ul>
					</div>
					<div class="tab-content">
						<!-- 依投資市場 -->
						<div id="tab-mkt" class="tab-pane fade show active">
							<div class="row g-3 align-items-center mb-3">
								<div class="col-lg-5">
									<vue-cus-assets-pie-chart chart-id="bondTypeChart" :chart-data="bondTypeChartData" />
								</div>
								<div class="col-lg-7">
									<div class="tx-13 text-end">
										(新臺幣:元)
									</div>
									<table id="selPrdType" width="100%" class="table bih-table table-RWD">
										<thead>
											<tr>
												<th>債券類型<!--span class="EN">Currency</span--></th>
												<th><span class="TC">投資現值</span><!--span class="EN">Total cost</span--></th>
												<th>累計配息</th>
												<th><span class="TC">投資現值(含息)</span><!--span class="EN">Market value</span--></th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="bond in bondTypeChartData">
												<td>{{ bond.protypeName }}</td>
												<td class="num">
													{{ $filters.formatAmt(bond.mktAmtLc) }}
												</td>
												<td class="num">
													{{ $filters.formatAmt(bond.totDividendLc) }}
												</td>
												<td class="num">
													{{ $filters.formatAmt(bond.mktAmtLc + bond.totDividendLc) }}
												</td>
											</tr>
											<tr class="tr-sum">
												<td class="num">
													<span class="TC">小計</span><!--span class="EN">Sub total</span-->
												</td>
												<td class="num">
													{{ $filters.formatAmt($filters.sumTotal(bondTypeChartData, 'mktAmtLc')) }}
												</td>
												<td class="num">
													{{ $filters.formatAmt($filters.sumTotal(bondTypeChartData, 'totDividendLc')) }}
												</td>
												<td class="num">
													{{ $filters.formatAmt($filters.sumTotal(bondTypeChartData, 'mktAmtLc') +
														$filters.sumTotal(bondTypeChartData, 'totDividendLc')) }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>

						<!-- 依信託幣別 -->
						<div id="tab-coin" class="tab-pane fade">
							<div class="row g-3 align-items-center mb-3">
								<div class="col-lg-5">
									<vue-cus-assets-pie-chart chart-id="bondCurChart" :chart-data="bondCurChartData" />
								</div>
								<div class="col-lg-7">
									<!-- <div class="tx-13 text-end">(新臺幣:元)</div> -->
									<table id="selCUR" class="table bih-table table-RWD">
										<thead>
											<tr>
												<th>
													<span>信託幣別</span><!--span class="EN">Currency</span-->
												</th>
												<th class="text-end">
													<span>投資現值</span><!--span class="EN">Total cost</span-->
												</th>
												<th class="text-end">
													<span>累計配息</span><!--span class="EN">Market value</span-->
												</th>
												<th class="text-end">
													<span>投資現值(含息)</span><!--span class="EN">Gain/Loss</span-->
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="bond in bondCurChartData">
												<td data-th="信託幣別">
													<a class="JQ-curLink tx-link" href="#">{{ bond.tranCurName }}</a>
												</td>
												<td class="text-end" data-th="text.table.mktAmtFc">
													<label class="num">{{ $filters.formatAmt(bond.mktAmtLc) }}</label>
												</td>
												<td class="text-end" data-th="累計配息">
													<label class="num">{{ $filters.formatAmt(bond.totDividendLc)
													}}</label>
												</td>
												<td class="text-end" data-th="投資現值(含息)">
													<label class="num">{{ $filters.formatAmt(bond.mktAmtLc +
														bond.totDividendLc) }}</label>
												</td>
											</tr>
											<tr class="tr-sum tr-subtotal">
												<td class="text-end">
													小計
												</td>
												<td class="text-end">
													<label class="num">{{
														$filters.formatAmt($filters.sumTotal(bondCurChartData,
															'mktAmtLc')) }}</label>
												</td>
												<td class="text-end">
													<label class="num">{{
														$filters.formatAmt($filters.sumTotal(bondCurChartData,
															'totDividendLc')) }}</label>
												</td>
												<td class="text-end">
													<label class="num">{{
														$filters.formatAmt($filters.sumTotal(bondCurChartData,
															'mktAmtLc') + $filters.sumTotal(bondCurChartData, 'totDividendLc')) }}</label>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!--在途明細-->
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>在途明細</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsedetail" />
					</div>
					<div id="collapsedetail" class="collapse show">
						<table class="table bih-table table-RWD">
							<thead>
								<tr>
									<th>
										<p><span class="TC">投資標的</span></p>
									</th>
									<th>交易型態</th>
									<th>交易面額</th>
									<th><span class="TC">憑證編號</span></th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="onway in onwayBondList">
									<td><a href="#">{{ onway.proName }}</a></td>
									<td>{{ onway.trantypeName }}</td>
									<td class="num">
										{{ onway.onwayUnit }})
									</td>
									<td class="num">
										{{ onway.refNo }}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!--庫存明細-->
				<h4 class="tx-title">
					庫存明細
				</h4>
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>依商品名稱加總明細</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsedetailtadd2" />
					</div>
					<div id="collapsedetailtadd2" class="collapse show">
						<table class="table bih-table table-RWD">
							<thead>
								<tr>
									<th width="11%" class="text-end">
										投資標的<br>商品代碼
									</th>
									<th width="11%" class="text-end">
										信託起日<br>憑證編號
									</th>
									<th width="9%" class="text-end">
										參考價格(B)<br>
										參考價格基準日
									</th>
									<th width="8%" class="text-end">
										參考匯率(C)<br>匯率基準日
									</th>
									<th width="11%" class="text-end">
										庫存面額<span class="text-start">(A)</span><br>
										投資金額(D)
									</th>
									<th width="8%" class="text-start">
										<span class="text-end">投資現值<br>(E=A*B*C)</span>
									</th>
									<th width="11%" class="text-start">
										投資損益<span class="text-end">(F=E-D)</span><br>報酬率<span
											class="text-end"
										>(H=F/D)</span>
									</th>
									<th width="8%" class="text-end">
										累計配息(G)<br>
										含息報酬率<br>(I=(F+G)/D)
									</th>
									<th width="7%" class="text-start">
										到期日<br>
										前手息
									</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="bond in bondList">
									<td>
										<span class="text-start"><a
												href="#"
												@click.prevent="$refs.productModal.open(bond.proCode, 'BOND')"
											>{{ bond.proName }}</a><br>
											{{ bond.bankProCode }} </span>
									</td>
									<td class="text-end">
										{{ $filters.formatDate(bond.firstBuyDt) }}<br>
										{{ bond.refNo }}
									</td>
									<td class="text-end">
										{{ $filters.formatAmt(bond.price) }} {{ bond.tranCurCode }}<br>
										{{ $filters.formatDate(bond.priceDt) }}
									</td>
									<td class="text-end">
										{{ $filters.formatAmt(bond.fxRate) }}<br>
										{{ $filters.formatDate(bond.fxRateDt) }}
									</td>
									<td class="text-end">
										<span class="text-start">{{ $filters.formatAmt(bond.unit) }}
											{{ bond.tranCurCode }}</span><br>
										{{ $filters.formatAmt(bond.invAmtFc) }} {{ bond.tranCurCode }}
									</td>
									<td class="text-end">
										{{ $filters.formatAmt(bond.mktAmtFc) }} {{ bond.tranCurCode }}
									</td>
									<td class="text-end">
										{{ $filters.formatAmt(bond.uplFc) }} {{ bond.tranCurCode }}<br>
										{{ $filters.formatPct(bond.ureturnFc) }}%
									</td>
									<td class="text-end">
										{{ $filters.formatAmt(bond.totDividendFc) }} {{ bond.tranCurCode }}<br>
										{{ $filters.formatPct(bond.ureturnFcDividend) }}%
									</td>
									<td>
										<span class="text-end">{{ $filters.formatDate(bond.expireDt) }}<br>
											{{ $filters.formatAmt(bond.orderLInt) }}</span>
									</td>
								</tr>
								<tr class="tr-sum">
									<td class="text-end">
										新臺幣 小計
									</td>
									<td class="text-end">
&nbsp;
									</td>
									<td class="text-end">
&nbsp;
									</td>
									<td class="text-end">
&nbsp;
									</td>
									<td class="text-end">
										{{ $filters.formatAmt($filters.sumTotal(bondList, 'invAmtLc')) }}
									</td>
									<td class="text-end">
										{{ $filters.formatAmt($filters.sumTotal(bondList, 'mktAmtLc')) }}
									</td>
									<td class="text-end">
										{{ $filters.formatAmt(($filters.sumTotal(bondList, 'mktAmtLc') -
											$filters.sumTotal(bondList, 'invAmtLc')) * 100 / $filters.sumTotal(bondList, 'invAmtLc')) }}%
									</td>
									<td class="text-end">
										{{ $filters.formatAmt(($filters.sumTotal(bondList, 'mktAmtLc') -
											$filters.sumTotal(bondList, 'invAmtLc') + $filters.sumTotal(bondList, 'totDividendLc')) * 100 /
											$filters.sumTotal(bondList, 'invAmtLc')) }}%
									</td>
									<td>&nbsp;</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

				<div class="tx-note">
					<ol>
						<li>截至資料日期之投資組合明細(信託資金投資標的參考價格為本行可取得之最新報價，僅供參考。)</li>
						<li>投資金額、投資損益及報酬率計算均未含相關申購費用；投資損益採計方式為依憑證編號分別計算之。</li>
					</ol>
				</div>
			</div>

			<div id="SectionB" class="tab-pane fade">
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>加入同業投資部位餘額</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseadd" />
					</div>
					<div id="collapseadd" class="collapse show">
						<div class="table-responsive">
							<table class="bih-table table table-RWD">
								<thead>
									<tr>
										<th> 銀行名稱</th>
										<th> 商品名稱</th>
										<th> 幣別</th>
										<th> 金額</th>
										<th> 起始日</th>
										<th>到期日</th>
										<th>是否警示</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td data-th="銀行名稱">
											<input
												id=""
												type="text"
												name=""
												size="8"
												maxlength="11"
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="商品名稱">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="幣別">
											<select name="select" class="form-select">
												<option>--</option>
												<option>台幣</option>
												<option>美元</option>
												<option>歐元</option>
												<option>澳幣</option>
												<option>新加坡幣</option>
												<option>日圓</option>
											</select>
										</td>
										<td data-th="金額">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="起始日">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="到期日">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="是否警示">
											<input
												class="form-check-input"
												name="cpasummary.active_yn"
												type="checkbox"
												value="Y"
											>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>

				<div id="button1" class="text-end">
					<input
						class="btn btn-primary JQdata-hide"
						type="button"
						value="儲存"
						name="bsave"
					>
				</div>

				<div id="button2" class="text-end" style="display:none">
					<input
						class="btn btn-primary JQdata-show"
						type="button"
						value="修改"
						name="cancel"
						style="display:none"
						onclick="window.location='cpsaving_other_bank.htm'"
					>
					<input
						class="btn btn-primary JQdata-show"
						type="button"
						value="取消修改"
						name="cancel"
						style="display:none"
						onclick="window.location='cpsaving_other_bank.htm'"
					>
				</div>

				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>同業投資部位餘額</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapserest" />
					</div>
					<div id="collapserest" class="collapse show">
						<div class="table-responsive">
							<table class="bih-table table table-RWD">
								<thead>
									<tr>
										<th><span class="TC">銀行名稱</span></th>
										<th><span class="TC">商品名稱</span></th>
										<th><span class="TC">幣別</span></th>
										<th><span class="TC">金額</span></th>
										<th><span class="TC">起始日</span></th>
										<th><span class="TC">到期日</span></th>
										<th><span class="TC">是否警示</span></th>
										<th>執行</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td>HSBC</td>
										<td>HSBC Direct TD</td>
										<td>台幣</td>
										<td class="text-end">
											200,000
										</td>
										<td>2008/02/10</td>
										<td>2009/02/10</td>
										<td class="text-Center">
											<span class="TC">是</span>
										</td>
										<td class="text-center">
											<button
												id="edit"
												type="button"
												class="btn btn-info btn-icon"
												data-bs-toggle="tooltip"
												title=""
												data-bs-original-title="編輯"
												aria-label="編輯"
											>
												<i class="fa-solid fa-pen" />
											</button>
											<button
												type="button"
												class="btn btn-danger btn-icon"
												data-bs-toggle="tooltip"
												data-bs-original-title="刪除"
											>
												<i class="fa-solid fa-trash" />
											</button>
										</td>
									</tr>
									<tr>
										<td><span class="TC">中國信託</span></td>
										<td><span class="TC">外幣優惠存款</span></td>
										<td><span class="TC">美元</span></td>
										<td class="text-end">
											400,000
										</td>
										<td>2008/05/22</td>
										<td>2008/08/22</td>
										<td class="text-center">
											<span class="TC">是</span>
										</td>
										<td class="text-center">
											<button
												type="button"
												class="btn btn-info btn-icon"
												data-bs-toggle="tooltip"
												title=""
												data-bs-original-title="編輯"
												aria-label="編輯"
											>
												<i class="fa-solid fa-pen" />
											</button>
											<button
												type="button"
												class="btn btn-danger btn-icon"
												data-bs-toggle="tooltip"
												data-bs-original-title="刪除"
											>
												<i class="fa-solid fa-trash" />
											</button>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			<div id="SectionC" class="tab-pane fade" />
		</div>
	</div>
	<!-- <vue-product-modal ref="productModal"></vue-product-modal> -->
</template>
<script>
import vueCusAssetsPieChart from '../pieChart.vue';
import _ from 'lodash';
import vueProductModal from '../../../../pro/PRO0101/include/productModal.vue';

export default {
	components: {
		vueCusAssetsPieChart,
		vueProductModal
	},
	props: {
		cusCode: String
	},
	data: function () {
		return {
			bondCurChartData: [],
			bondTypeChartData: [],
			bondList: [],
			onwayBondList: [],
			checkAll: false,
			isShowPerformances: false,
			selectedProCodes: [],
			proPriceRangeMenu: [],
			observedProList: [],
			chartsData: [],
			issuersMenu: [],
			issuerCode: null,
			proCode: null
		};
	},
	watch: {
		cusCode: function () {
			this.initData();
		}
	},
	mounted: function () {
		this.initData();
	},
	methods: {
		initData() {
			this.getBondCurChartData();
			this.getBondTypeChartData();
			this.getBondList();
			this.getOnwayBondList();
			this.getProPriceRangeMenu();
			this.getIssuersMenu();
		},
		async getBondCurChartData() {
			const result = await this.$api.getCusBondGroupByCurApi({
				cusCode: this.cusCode
			});
			this.bondCurChartData = result.data;
			this.bondCurChartData.forEach((bond) => {
				bond.category = bond.tranCurName;
				bond.value = bond.mktAmtLc + bond.totDividendLc;
			});
		},
		async getBondTypeChartData() {
			const result = await this.$api.getCusBondGroupByProtypeApi({
				cusCode: this.cusCode
			});
			this.bondTypeChartData = result.data;
			this.bondTypeChartData.forEach((bond) => {
				bond.category = bond.protypeName;
				bond.value = bond.mktAmtLc + bond.totDividendLc;
			});
		},
		async getBondList() {
			const result = await this.$api.getCusBondApi({
				cusCode: this.cusCode
			});
			this.bondList = result.data;
		},
		async getOnwayBondList() {
			const result = await this.$api.getCusOnwayApi({
				cusCode: this.cusCode,
				pfcatCode: 'BOND'
			});
			this.onwayBondList = result.data;
		},
		async getIssuersMenu() {
			const ret = await this.$api.getIssuersMenuApi({
				pfcatCode: 'BOND'
			});
			this.issuersMenu = ret.data;
		},
		addPro() {
			if (!this.selectedProCodes.includes(this.proCode)) {
				this.selectedProCodes.push(this.proCode);
				this.calPerformances();
			}
		},
		deletePro(proCode) {
			this.selectedProCodes = this.selectedProCodes.filter(selectedProCode => selectedProCode !== proCode);
			this.calPerformances();
		},
		async getProPriceRangeMenu() {
			const ret = await this.$api.getPriceTimeRangeMenuApi();
			this.proPriceRangeMenu = ret.data;
		},
		toggleCheckAll() {
			this.selectedProCodes = [];
			if (this.checkAll) {
				this.selectedProCodes = this.bondList.reduce(function (proCodes, bond) {
					if (bond.proCode && !proCodes.includes(bond.proCode)) {
						proCodes.push(bond.proCode);
					}
					return proCodes;
				}, []);
			}
		},
		async calPerformances() {
			if (this.selectedProCodes.length === 0) {
				this.$bi.message('至少勾選一筆商品');
				return;
			}
			const ret = await this.$api.getObservedBondsApi({
				proCodes: this.selectedProCodes.join()
			});
			this.observedProList = ret.data;
			this.getPerformances(this.selectedProCodes.join(), 'Y', -1.0);
			this.isShowPerformances = true;
		},
		getPerformances: async function (proCodes, rangeType, rangeFixed) {
			const ret = await this.$api.getPerformanceRunChartApi({
				proCodes: proCodes,
				freqType: rangeType, // 示區間類型
				freqFixed: rangeFixed // "顯示區間數值
			});
			if (!_.isNil(ret.data)) {
				for (let i = 0; i < ret.data.length; i++) {
					ret.data[i].datas.forEach((e) => {
						e.value = e.returnFc;
						e.date = new Date(e.returnDt).getTime();
					});
				}
				this.chartsData = ret.data;
				this.$refs.performancesChartRef.initChart(this.chartsData);
			}
		}
	}
};
</script>
