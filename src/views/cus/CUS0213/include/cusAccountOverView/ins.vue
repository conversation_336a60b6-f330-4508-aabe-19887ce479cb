<template>
	<div class="tx-note">
		金額無特別註記幣別者，皆以新台幣計。
	</div>
	<div class="card card-form card-collapse mb-3">
		<table class="biv-table table table-horizontal-RWD">
			<tbody>
				<tr>
					<th class="wd-10p">
						<span>客戶姓名：</span>
					</th>
					<td />
					<th class="wd-10p">
						<span>合計實繳保費(台幣)： </span>
					</th>
					<td>{{ $filters.formatAmt($filters.sumTotal(insList, 'accPremiumLc')) }}</td>
					<th class="wd-10p">
						合計提領/給付金(台幣)：
					</th>
					<td>{{ $filters.formatAmt($filters.sumTotal(insList, 'totRecvValueLc')) }}</td>
					<th class="wd-10p">
						合計下期給付金(台幣)：
					</th>
					<td>{{ $filters.formatAmt($filters.sumTotal(insList, 'nextRecvValueLc')) }}</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="tx-note">
		上述計算基準以該客戶為要保人的有效保單
	</div>
	<div class="tab-nav-main">
		<ul class="nav nav-pills">
			<li class="nav-item">
				<a
					class="nav-link active"
					href="#"
					data-bs-toggle="tab"
					@click="insList = insListY"
				>要保人有效保單({{ insListY.length }})</a>
			</li>
			<li class="nav-item">
				<a
					class="nav-link"
					href="#"
					data-bs-toggle="tab"
					@click="insList = insListN"
				>要保人非有效保單({{
					insListN.length }})</a>
			</li>
			<li class="nav-item">
				<a
					class="nav-link"
					href="#"
					data-bs-toggle="tab"
					@click="insList = insListI"
				>客戶為關係人之其他保單({{
					insListI.length }})</a>
			</li>
		</ul>
		<div class="tab-content">
			<div class="tab-pane fade active show">
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>持有保單</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsedetail" />
					</div>
					<div id="collapsedetail" class="collapse show">
						<table class="table bih-table table-RWD">
							<thead>
								<tr>
									<th>保險公司<br>保單號碼</th>
									<th>商品類型<br>主約商品名稱</th>
									<th>要保人<br>被保險人</th>
									<th>保單生效日</th>
									<th>保單到期日</th>
									<th>累積實繳保費<br>(原幣)</th>
									<th>保單幣別</th>
									<th>保額<br>(原幣)</th>
									<th>保單狀態</th>
									<th>明細</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="ins in insList">
									<td>{{ ins.inscmpName }}<br>{{ ins.policyNo }}</td>
									<td>
										{{ ins.protypeName }}<br>
										<a href="#" data-bs-toggle="modal" data-bs-target="#myModal">{{ ins.proName }}</a>
									</td>
									<td>
										{{ ins.ownerName }}<br>
										{{ ins.insuredName }}
									</td>
									<td>{{ $filters.formatDate(ins.insValidDate) }}</td>
									<td>{{ $filters.formatDate(ins.insEndDate) }}</td>
									<td class="num">
										{{ $filters.formatAmt(ins.accPremiumFc) }}
									</td>
									<td>{{ ins.curCode }}</td>
									<td class="num">
										{{ $filters.formatAmt(ins.coverageFc) }}
									</td>
									<td>{{ ins.policyStatus }}</td>
									<td>
										<a href="#" @click="viewInsDetail(ins)">保單明細</a> <br>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- modal  -->
	<vue-modal :is-open="isOpenModal" @close="() => isOpenModal = false">
		<template #content="props">
			<div class="modal-dialog modal-lg" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							保單明細
						</h4>
						<button type="button" class="btn-expand">
							<i class="bi bi-arrows-fullscreen" />
						</button>
						<button
							type="button"
							class="btn-close"
							data-bs-dismiss="modal"
							aria-label="Close"
						/>
					</div>
					<div class="modal-body">
						<div class="card card-form">
							<div class="card-header">
								<h4>保單資訊及主約保障內容</h4>
							</div>
							<table class="biv-table table table-RWD table-horizontal-RWD">
								<tbody>
									<tr>
										<th class="">
											保險公司
										</th>
										<td class="">
											{{ insDetail.inscmpName }}
										</td>
										<th class="">
											要保人
										</th>
										<td class="">
											{{ insDetail.ownerName }} {{ insDetail.ownerIdn }}
										</td>
										<th class="">
											主約被保險人
										</th>
										<td class="">
											{{ insDetail.insuredName }} {{ insDetail.insuredIdn }}
										</td>
									</tr>
									<tr>
										<th class="">
											保單狀態
										</th>
										<td class="">
											{{ insDetail.policyStatus }}
										</td>
										<th class="">
											保單生效日
										</th>
										<td class="">
											{{ $filters.formatDate(insDetail.insValidDate) }}
										</td>
										<th class="">
											解約/契徹/失效日
										</th>
										<td class="">
											{{ insDetail.insFailDate }}
										</td>
									</tr>
									<tr>
										<th class="">
											停效日期
										</th>
										<td class="">
											{{ $filters.formatDate(insDetail.stopStartDate) }}
										</td>
										<th class="">
											停效迄日
										</th>
										<td class="">
											{{ $filters.formatDate(insDetail.stopEndDate) }}
										</td>
										<th class="">
											主約保障終止日
										</th>
										<td class="">
											{{ $filters.formatDate(insDetail.mstExpireDate) }}
										</td>
									</tr>
									<tr>
										<th class="">
											主約商品類型
										</th>
										<td class="">
											{{ insDetail.protypeName }}
										</td>
										<th class="">
											主約商品名稱
										</th>
										<td class="">
											{{ insDetail.proName }}
										</td>
										<th class="" />
										<td class="" />
									</tr>
									<tr>
										<th class="">
											主約保額
										</th>
										<td class="">
											{{ $filters.formatAmt(insDetail.coverageFc) }}
										</td>
										<th class="">
											主約計量單位
										</th>
										<td class="" />
										<th class="">
											主約幣別
										</th>
										<td class="">
											{{ insDetail.curCode }}
										</td>
									</tr>
									<tr>
										<th class="">
											主約年化應繳保費
										</th>
										<td class="" />
										<th class="">
											主約繳費年期
										</th>
										<td class="">
											{{ insDetail.payTerm }}
										</td>
										<th class="">
											繳別
										</th>
										<td class="">
											{{ insDetail.payType }}
										</td>
									</tr>
									<tr>
										<th class="">
											下期應繳日
										</th>
										<td class="">
											{{ $filters.formatDate(insDetail.nextPayDate) }}
										</td>
										<th class="">
											繳費方式
										</th>
										<td class="" />
										<th class="">
											扣款銀行帳號/信用卡號
										</th>
										<td class="">
											{{ insDetail.debitAccno }}
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
					<div class="modal-footer">
						<div class="num">
							<input
								class="btn btn-white"
								type="button"
								value="關閉"
								data-bs-dismiss="modal"
							>
						</div>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- modal end -->
</template>
<script>
import vueModal from '@/views/components/model.vue';

export default {
	components: {
		vueModal
	},
	props: {
		cusCode: String
	},
	data: function () {
		return {
			insList: [],
			insListY: [],
			insListN: [],
			insListI: [],
			insDetail: {},
			isOpenModal: null
		};
	},
	watch: {
		cusCode: function () {
			this.initData();
		}
	},
	mounted: function () {
		this.initData();
	},
	methods: {
		async initData() {
			this.insListY = await this.getInsList('Y');
			this.insListN = await this.getInsList('N');
			this.insListI = await this.getInsList('I');
		},
		async getInsList(queryType) {
			const result = await this.$api.getCusInsApi({
				cusCode: this.cusCode,
				queryType: queryType
			});
			return result.data;
		},
		viewInsDetail(ins) {
			this.insDetail = ins;
			this.isOpenModal = true;
		}
	}
};
</script>
