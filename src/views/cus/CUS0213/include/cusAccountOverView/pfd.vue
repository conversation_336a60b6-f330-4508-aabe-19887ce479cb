<template>
	<div class="tab-nav-main">
		<ul class="nav nav-pills">
			<li class="nav-item">
				<a class="nav-link active" href="#SectionA" data-bs-toggle="tab">本行部位餘額（前一日）</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#SectionB" data-bs-toggle="tab">同業部位餘額</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#SectionC" data-bs-toggle="tab">即時未實現投資餘額</a>
			</li>
		</ul>
		<div class="tab-content">
			<div id="SectionA" class="tab-pane fade active show">
				<div class="card card-table card-tabs">
					<div class="card-header">
						<h4>海外股票分析(不含在途)</h4>
						<ul class="nav nav-pills  card-header-pills">
							<li>
								<a href="#tab-mkt" class="nav-link active" data-bs-toggle="pill">依投資市場</a>
							</li>
							<li>
								<a href="#tab-coin" class="nav-link" data-bs-toggle="pill">依信託幣別</a>
							</li>
							<li>
								<a href="#tab-type" class="nav-link" data-bs-toggle="pill">依海外股票類型</a>
							</li>
						</ul>
					</div>
					<div class="tab-content">
						<!-- 依投資市場 -->
						<div id="tab-mkt" class="tab-pane fade show active">
							<div class="row g-3 align-items-center mb-3">
								<div class="col-lg-5">
									<vue-cus-assets-pie-chart chart-id="pfdSecChart" :chart-data="pfdSecChartData" />
								</div>
								<div class="col-lg-7">
									<div class="tx-13 num">
										(新臺幣:元)
									</div>
									<table id="selAST" class="table bih-table table-RWD">
										<thead>
											<tr>
												<th><span>投資市場</span></th>
												<th class="num">
													<span>投資現值</span>
												</th>
												<th class="num">
													<span>累計配息</span>
												</th>
												<th class="num">
													<span>投資現值(含息)</span>
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="pfd in pfdSecChartData">
												<td data-th="投資市場">
													{{ pfd.sectorName }}
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt(pfd.mktAmtLc) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt(pfd.totDividendLc) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt(pfd.mktAmtLc + pfd.totDividendLc)
													}}
												</td>
											</tr>
											<tr class="tx-sum bg-subtotal">
												<td data-th="投資市場">
													小計
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(pfdSecChartData,
														'mktAmtLc')) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(pfdSecChartData,
														'totDividendLc')) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(pfdSecChartData, 'mktAmtLc') +
														$filters.sumTotal(pfdSecChartData, 'totDividendLc')) }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>

						<!-- 依信託幣別 -->
						<div id="tab-coin" class="tab-pane fade">
							<div class="row g-3 align-items-center mb-3">
								<div class="col-lg-5">
									<vue-cus-assets-pie-chart chart-id="pfdCurChart" :chart-data="pfdCurChartData" />
								</div>
								<div class="col-lg-7">
									<table id="selCUR" class="table bih-table table-RWD">
										<thead>
											<tr>
												<th>
													<span>信託幣別</span><!--span class="EN">Currency</span-->
												</th>
												<th class="num">
													<span>投資現值</span><!--span class="EN">Total cost</span-->
												</th>
												<th class="num">
													<span>累計配息</span><!--span class="EN">Market value</span-->
												</th>
												<th class="num">
													<span>投資現值(含息)</span><!--span class="EN">Gain/Loss</span-->
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="pfd in pfdCurChartData">
												<td data-th="信託幣別">
													{{ pfd.proCurName }}
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt(pfd.mktAmtLc) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt(pfd.totDividendLc) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt(pfd.mktAmtLc + pfd.totDividendLc)
													}}
												</td>
											</tr>
											<tr class="tx-sum bg-subtotal">
												<td data-th="信託幣別">
													小計
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(pfdCurChartData,
														'mktAmtLc')) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(pfdCurChartData,
														'totDividendLc')) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(pfdCurChartData, 'mktAmtLc') +
														$filters.sumTotal(pfdCurChartData, 'totDividendLc')) }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>

						<!-- 依基金類型 -->
						<div id="tab-type" class="tab-pane fade ">
							<div class="row g-3 align-items-center mb-3">
								<div class="col-lg-5">
									<vue-cus-assets-pie-chart chart-id="pfdTypeChart" :chart-data="pfdTypeChartData" />
								</div>
								<div class="col-lg-7">
									<div class="tx-13 num">
										(新臺幣:元)
									</div>
									<table id="selAST" class="table bih-table table-RWD">
										<thead>
											<tr>
												<th><span>基金類型</span></th>
												<th class="num">
													<span>投資現值</span>
												</th>
												<th class="num">
													<span>累計配息</span>
												</th>
												<th class="num">
													<span>投資現值(含息)</span>
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="pfd in pfdTypeChartData">
												<td data-th="海外股票類型">
													{{ pfd.protypeName }}
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt(pfd.mktAmtLc) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt(pfd.totDividendLc) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt(pfd.mktAmtLc + pfd.totDividendLc)
													}}
												</td>
											</tr>
											<tr class="tx-sum bg-subtotal">
												<td data-th="海外股票類型">
													小計
												</td>
												<td data-th="投資現值" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(pfdTypeChartData,
														'mktAmtLc')) }}
												</td>
												<td data-th="累計配息" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(pfdTypeChartData,
														'totDividendLc')) }}
												</td>
												<td data-th="投資現值(含息)" class="text-end">
													{{ $filters.formatAmt($filters.sumTotal(pfdTypeChartData, 'mktAmtLc') +
														$filters.sumTotal(pfdTypeChartData, 'totDividendLc')) }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>在途明細</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsedetail" />
					</div>
					<div id="collapsedetail" class="collapse show">
						<table class="table bih-table table-RWD">
							<thead>
								<tr>
									<th>交易日</th>
									<th><span class="TC">交易類別</span></th>
									<th>
										<p><span class="TC">投資標的</span></p>
									</th>
									<th>股數</th>
									<th>成交價格</th>
									<th><span class="TC">成交序號</span></th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="onway in onwayPfdList">
									<td>{{ $filters.formatDate(onway.orderDt) }}</td>
									<td>{{ onway.trantypeName }}</td>
									<td>
										<a href="#"><span class="TC">{{ onway.proName }}
										</span></a>
									</td>
									<td class="num">
										{{ $filters.formatAmt(onway.onwayUnit) }}
									</td>
									<td class="num">
										{{ $filters.formatAmt(onway.onwayAmtFc) }}
									</td>
									<td class="num">
										{{ onway.refNo }}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

				<h4 class="tx-title">
					庫存明細
				</h4>
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>依商品名稱加總明細</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsedetailtadd" />
					</div>
					<div id="collapsedetailtadd" class="collapse show">
						<table class="table bih-table table-RWD">
							<thead>
								<tr>
									<th class="text-start" colspan="8">
										商品名稱
									</th>
								</tr>
								<tr>
									<th width="10%" class="num">
										商品代號<br>交易市場
									</th>
									<th width="9%" class="num">
										幣別<br>計價幣別
									</th>
									<th width="8%" class="num">
										可交易股數含配股(A)<br>信託金額
									</th>
									<th width="13%" class="num">
										參考價格(B)<br>參考價格日
									</th>
									<th width="9%" class="num">
										參考現值(A)*(B)<br>參考信託管理費
									</th>
									<th width="9%" class="num">
										<br>累計配息
									</th>
									<th width="6%" class="num">
										投資損益<br>含息損益
									</th>
									<th width="9%" class="num">
										報酬率(%)<br>含息報酬率
									</th>
								</tr>
							</thead>
							<tbody>
								<template v-for="pfd in pfdList">
									<tr>
										<td class="text-start" colspan="8">
											<a
												href="#"
												@click.prevent="$refs.productModal.open(pfd.proCode, 'PFD')"
											>{{ pfd.proName }}</a>
										</td>
									</tr>
									<tr>
										<td width="10%" class="num">
											{{ pfd.bankProCode }}<br>{{ pfd.exCode }}
										</td>
										<td width="9%" class="num">
											{{ pfd.tranCurCode }}<br>{{ pfd.curCode }}
										</td>
										<td width="8%" class="num">
											{{ $filters.formatAmt(pfd.unit) }}<br>{{ $filters.formatAmt(pfd.invAmtFc)
											}}
										</td>
										<td width="13%" class="num">
											{{ $filters.formatAmt(pfd.price) }}<br>{{ $filters.formatDate(pfd.priceDt) }}
										</td>
										<td width="9%" class="num">
											{{ $filters.formatAmt(pfd.mktAmtFc) }}<br>{{ $filters.formatAmt(pfd.mFee)
											}}
										</td>
										<td width="9%" class="num">
											<br>{{ $filters.formatAmt(pfd.totDividendFc) }}
										</td>
										<td width="6%" class="num">
											{{ $filters.formatAmt(pfd.uplFc) }}<br>{{ $filters.formatAmt(pfd.uplFcDividend) }}
										</td>
										<td width="9%" class="num">
											{{ $filters.formatPct(pfd.ureturnFc) }}%<br>{{ $filters.formatPct(pfd.ureturnFcDividend) }}%
										</td>
									</tr>
								</template>
								<tr class="tr-sum">
									<td class="num" colspan="2">
										新台幣 小計
									</td>
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(pfdList, 'invAmtFc')) }}
									</td>
									<td class="num" />
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(pfdList, 'mktAmtFc')) }}
									</td>
									<td />
									<td class="num">
										{{ $filters.formatAmt($filters.sumTotal(pfdList,
											'uplFc')) }}<br>{{ $filters.formatAmt($filters.sumTotal(pfdList, 'uplFcDividend')) }}
									</td>
									<td class="num">
										{{ $filters.formatAmt(($filters.sumTotal(pfdList, 'mktAmtFc') - $filters.sumTotal(pfdList,
											'invAmtFc')) * 100 /
											$filters.sumTotal(pfdList, 'invAmtFc')) }}%<br>
										{{ $filters.formatAmt(($filters.sumTotal(pfdList, 'mktAmtFc') - $filters.sumTotal(pfdList,
											'invAmtFc') +
											$filters.sumTotal(pfdList, 'totDividendFc')) * 100 / $filters.sumTotal(pfdList, 'invAmtFc')) }}%
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

				<div class="tx-note">
					<ol>
						<li>截至資料日期之投資組合明細(信託資金投資標的參考淨值為本行可取得之最新報價，僅供參考。)</li>
						<li>投資金額、投資損益及報酬率計算均未含相關申購費用；庫存投資金額係採先進先出法依投資標的分別計算之 。</li>
					</ol>
				</div>

				<div class="alert alert-block alert-danger fade in margin-top10">
					本功能畫面提供的有關資訊是截止統計日期，由系統根據客戶歷史投資資料自動生成的，僅供參考。
				</div>
			</div>

			<div id="SectionB" class="tab-pane fade">
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>加入同業投資部位餘額</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseadd" />
					</div>
					<div id="collapseadd" class="collapse show">
						<div class="table-responsive">
							<table class="bih-table table table-RWD">
								<thead>
									<tr>
										<th> 銀行名稱</th>
										<th> 商品名稱</th>
										<th> 幣別</th>
										<th> 金額</th>
										<th> 起始日</th>
										<th>到期日</th>
										<th>是否警示</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td data-th="銀行名稱">
											<input
												id=""
												type="text"
												name=""
												size="8"
												maxlength="11"
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="商品名稱">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="幣別">
											<select name="select" class="form-select">
												<option>--</option>
												<option>台幣</option>
												<option>美元</option>
												<option>歐元</option>
												<option>澳幣</option>
												<option>新加坡幣</option>
												<option>日圓</option>
											</select>
										</td>
										<td data-th="金額">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="起始日">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="到期日">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="是否警示">
											<input
												class="form-check-input"
												name="cpatr-summary.active_yn"
												type="checkbox"
												value="Y"
											>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>

				<div id="button1" class="num">
					<input
						class="btn btn-primary JQdata-hide"
						type="button"
						value="儲存"
						name="bsave"
					>
				</div>

				<div id="button2" class="num" style="display:none">
					<input
						class="btn btn-primary JQdata-show"
						type="button"
						value="修改"
						name="cancel"
						style="display:none"
						onclick="window.location='cpsaving_other_bank.htm'"
					>
					<input
						class="btn btn-primary JQdata-show"
						type="button"
						value="取消修改"
						name="cancel"
						style="display:none"
						onclick="window.location='cpsaving_other_bank.htm'"
					>
				</div>

				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>同業投資部位餘額</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapserest" />
					</div>
					<div id="collapserest" class="collapse show">
						<div class="table-responsive">
							<table class="bih-table table table-RWD">
								<thead>
									<tr>
										<th><span class="TC">銀行名稱</span></th>
										<th><span class="TC">商品名稱</span></th>
										<th><span class="TC">幣別</span></th>
										<th><span class="TC">金額</span></th>
										<th><span class="TC">起始日</span></th>
										<th><span class="TC">到期日</span></th>
										<th><span class="TC">是否警示</span></th>
										<th>執行</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td>HSBC</td>
										<td>HSBC Direct TD</td>
										<td>台幣</td>
										<td class="num">
											200,000
										</td>
										<td>2008/02/10</td>
										<td>2009/02/10</td>
										<td class="text-alignCenter">
											<span class="TC">是</span>
										</td>
										<td class="text-alignCenter">
											<button
												id="edit"
												type="button"
												class="btn btn-info btn-icon"
												data-bs-toggle="tooltip"
												title=""
												data-bs-original-title="編輯"
												aria-label="編輯"
											>
												<i class="fa-solid fa-pen" />
											</button>
											<button
												type="button"
												class="btn btn-danger btn-icon"
												data-bs-toggle="tooltip"
												data-bs-original-title="刪除"
											>
												<i class="fa-solid fa-trash" />
											</button>
										</td>
									</tr>
									<tr>
										<td><span class="TC">中國信託</span></td>
										<td><span class="TC">外幣優惠存款</span></td>
										<td><span class="TC">美元</span></td>
										<td class="num">
											400,000
										</td>
										<td>2008/05/22</td>
										<td>2008/08/22</td>
										<td class="text-alignCenter">
											<span class="TC">是</span>
										</td>
										<td class="text-alignCenter">
											<button
												type="button"
												class="btn btn-info btn-icon"
												data-bs-toggle="tooltip"
												title=""
												data-bs-original-title="編輯"
												aria-label="編輯"
											>
												<i class="fa-solid fa-pen" />
											</button>
											<button
												type="button"
												class="btn btn-danger btn-icon"
												data-bs-toggle="tooltip"
												data-bs-original-title="刪除"
											>
												<i class="fa-solid fa-trash" />
											</button>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>

			<div id="SectionC" class="tab-pane fade" />
		</div>
	</div>
	<!-- <vue-product-modal ref="productModal"></vue-product-modal> -->
</template>
<script>
import vueCusAssetsPieChart from '../pieChart.vue';
import vueProductModal from '../../../../pro/PRO0101/include/productModal.vue';

export default {
	components: {
		vueCusAssetsPieChart,
		vueProductModal
	},
	props: {
		cusCode: String
	},
	data: function () {
		return {
			pfdSecChartData: [],
			pfdCurChartData: [],
			pfdTypeChartData: [],
			pfdList: [],
			onwayPfdList: []
		};
	},
	watch: {
		cusCode: function () {
			this.initData();
		}
	},
	mounted: function () {
		this.initData();
	},
	methods: {
		initData() {
			this.getPfdSecChartData();
			this.getPfdCurChartData();
			this.getPfdTypeChartData();
			this.getPfdList();
			this.getOnwayPfdList();
		},
		async getPfdSecChartData() {
			const result = await this.$api.getCusPstockGroupBySectorApi({
				cusCode: this.cusCode
			});
			this.pfdSecChartData = result.data;
			this.pfdSecChartData.forEach((pfd) => {
				pfd.category = pfd.sectorName;
				pfd.value = pfd.mktAmtLc + pfd.totDividendLc;
			});
		},
		async getPfdCurChartData() {
			const result = await this.$api.getCusPstockGroupByCurApi({
				cusCode: this.cusCode
			});
			this.pfdCurChartData = result.data;
			this.pfdCurChartData.forEach((pfd) => {
				pfd.category = pfd.proCurName;
				pfd.value = pfd.mktAmtLc + pfd.totDividendLc;
			});
		},
		async getPfdTypeChartData() {
			const result = await this.$api.getCusPstockGroupByProtypeApi({
				cusCode: this.cusCode
			});
			this.pfdTypeChartData = result.data;
			this.pfdTypeChartData.forEach((pfd) => {
				pfd.category = pfd.protypeName;
				pfd.value = pfd.mktAmtLc + pfd.totDividendLc;
			});
		},
		async getPfdList() {
			const result = await this.$api.getCusPstockApi({
				cusCode: this.cusCode
			});
			this.pfdList = result.data;
		},
		async getOnwayPfdList() {
			const result = await this.$api.getCusOnwayApi({
				cusCode: this.cusCode,
				pfcatCode: 'PFD'
			});
			this.onwayPfdList = result.data;
		}
	}
};
</script>
