<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<div class="tab-nav-tabs">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a class="nav-link active" data-bs-toggle="pill" href="#tab-asset">本行部位餘額</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" data-bs-toggle="pill" href="#tab-perf">持有部位分析</a>
					</li>
				</ul>
				<div class="tab-content">
					<div id="tab-asset" role="tabpanel" class="tab-pane fade show active">
						<div class="card card-table card-tabs mb-3">
							<div class="card-header">
								<h4>結構型商品分析(不含在途) </h4>
								<ul class="nav nav-pills  card-header-pills">
									<li>
										<a
											href="#tab-save"
											class="nav-link active"
											:class="{ active: activeTab === '#tab-save' }"
											data-bs-toggle="pill"
										>依商品細類</a>
									</li>
									<li>
										<a
											href="#tab-coin"
											class="nav-link"
											:class="{ active: activeTab === '#tab-coin' }"
											data-bs-toggle="pill"
										>依計價幣別</a>
									</li>
								</ul>
							</div>
							<div class="tab-content">
								<div id="tab-save" class="tab-pane fade show active">
									<div class="row g-3 align-items-center">
										<div class="col-lg-5">
											<vue-cus-assets-pie-chart chart-id="spTypeChart" :chart-data="spTypeChartData" />
										</div>
										<div class="col-lg-7">
											<div class="tx-13 text-end">
												(新臺幣:元)
											</div>
											<table id="selPrdType" class="table table-bordered">
												<thead>
													<tr>
														<th><span>商品細類</span></th>
														<th class="text-end">
															<span>投資現值</span>
														</th>
														<th class="text-end">
															累計配息
														</th>
														<th class="text-end">
															<span>投資現值(含息)</span>
														</th>
													</tr>
												</thead>
												<tbody>
													<tr v-for="sp in spTypeChartData">
														<td data-th="商品細類">
															{{ sp.protypeName }}
														</td>
														<td data-th="投資現值" class="text-end">
															{{ $filters.formatAmt(sp.mktAmtLc) }}
														</td>
														<td data-th="累計配息" class="text-end">
															{{ $filters.formatAmt(sp.totDividendLc) }}
														</td>
														<td data-th="投資現值(含息)" class="text-end">
															{{ $filters.formatAmt(sp.mktAmtLc +
																sp.totDividendLc) }}
														</td>
													</tr>
													<tr class="tx-sum bg-subtotal">
														<td data-th="商品細類">
															小計
														</td>
														<td data-th="投資現值" class="text-end">
															{{ $filters.formatAmt($filters.sumTotal(spTypeChartData,
																'mktAmtLc')) }}
														</td>
														<td data-th="累計配息" class="text-end">
															{{ $filters.formatAmt($filters.sumTotal(spTypeChartData,
																'totDividendLc')) }}
														</td>
														<td data-th="投資現值(含息)" class="text-end">
															{{ $filters.formatAmt($filters.sumTotal(spTypeChartData, 'mktAmtLc') +
																$filters.sumTotal(spTypeChartData, 'totDividendLc')) }}
														</td>
													</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>
								<div id="tab-coin" class="tab-pane fade">
									<div class="row g-3 align-items-center">
										<div class="col-lg-5">
											<vue-cus-assets-pie-chart chart-id="spCurChart" :chart-data="spCurChartData" />
										</div>
										<div class="col-lg-7">
											<div class="tx-13 text-end">
												(新臺幣:元)
											</div>
											<table id="selCUR" class="table table-bordered">
												<thead>
													<tr>
														<th><span>計價幣別</span></th>
														<th><span>投資現值</span></th>
														<th><span>累計配息</span></th>
														<th><span>投資現值(含息)</span></th>
													</tr>
												</thead>
												<tbody>
													<tr v-for="sp in spCurChartData">
														<td data-th="計價幣別">
															{{ sp.proCurName }}
														</td>
														<td data-th="投資現值" class="text-end">
															{{ $filters.formatAmt(sp.mktAmtLc) }}
														</td>
														<td data-th="累計配息" class="text-end">
															{{ $filters.formatAmt(sp.totDividendLc) }}
														</td>
														<td data-th="投資現值(含息)" class="text-end">
															{{ $filters.formatAmt(sp.mktAmtLc +
																sp.totDividendLc) }}
														</td>
													</tr>
													<tr class="tx-sum bg-subtotal">
														<td data-th="計價幣別">
															小計
														</td>
														<td data-th="投資現值" class="text-end">
															{{ $filters.formatAmt($filters.sumTotal(spCurChartData,
																'mktAmtLc')) }}
														</td>
														<td data-th="累計配息" class="text-end">
															{{ $filters.formatAmt($filters.sumTotal(spCurChartData,
																'totDividendLc')) }}
														</td>
														<td data-th="投資現值(含息)" class="text-end">
															{{ $filters.formatAmt($filters.sumTotal(spCurChartData, 'mktAmtLc') +
																$filters.sumTotal(spCurChartData, 'totDividendLc')) }}
														</td>
													</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="card card-table card-collapse mb-3">
							<div class="card-header">
								<h4>在途明細</h4>
								<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup11" />
							</div>
							<div id="collapseListGroup11" class="collapse show">
								<div class="table-responsive">
									<table class="table table-striped table-RWD table-bordered">
										<thead>
											<tr>
												<th>帳號</th>
												<th>商品代碼 投資標的</th>
												<th>交易類型</th>
												<th>交易金額</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="onway in onwaySpList">
												<td>{{ onway.accNo }}</td>
												<td>{{ onway.bankProCode }} {{ onway.proName }}</td>
												<td>{{ onway.trantypeName }}</td>
												<td class="text-end">
													{{ onway.tranCurCode }} {{ $filters.formatAmt(onway.onwayAmtFc) }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
						<div class="card card-table card-collapse mb-0">
							<div class="card-header">
								<h4>庫存明細</h4>
								<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup12" />
							</div>
							<div id="collapseListGroup12" class="collapse show">
								<div class="table-responsive">
									<table class="table table-striped table-RWD table-bordered">
										<thead>
											<tr>
												<th>
													帳號<br>
													商品代碼 風險等級<br>
													投資標的<br>
													計價幣別 投資幣別
												</th>
												<th class="text-end">
													首次申請日<br>
													淨值基準日/參考報價日<br>
													平均單價/單位淨值
												</th>
												<th class="text-end">
													單位數/股數(A)<br>
													參考匯率(B)<br>
													參考淨值/收盤價(C)
												</th>
												<th class="text-end">
													原始投資金額(D)<br>
													基準日市值(E=A*B*C)<br>
													參考損益(F=E-D)
												</th>
												<th class="text-end">
													參考報酬率(G=F/D)<br>
													累計配息(I)<br>
													參考含息報酬率(H=(F+I)/D
												</th>
												<th class="text-end">
													到期日<br>
													配息頻率
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="sp in spList" :class="{ 'tx-sum': !sp.proCode, 'bg-subtotal': !sp.proCode }">
												<td v-if="sp.proCode">
													{{ sp.accNo }}<br>
													{{ sp.refNo }} {{ sp.riskName }}<br>
													{{ sp.proName }}<br>
													{{ sp.curCode }} {{ sp.tranCurCode }}
												</td>
												<td v-if="sp.proCode" class="text-end">
													{{ $filters.formatDate(sp.firstBuyDt) }}<br>
													{{ $filters.formatDate(sp.priceDt) }}<br>
													{{ sp.codeValue ? '-' : $filters.formatAmt(sp.avgCostFc, '0,0.0000') }}
												</td>
												<td v-if="sp.proCode" class="text-end">
													{{ $filters.formatAmt(sp.unit) }}<br>
													<span v-if="sp.curCode === sp.tranCurCode">{{ $filters.formatAmt(sp.fxRate,
														'0,0.[00]') }}</span>
													<span v-else-if="sp.curCode === 'JPY' || sp.curCode === 'THB'">{{
														$filters.formatAmt(sp.fxRate,
															'0,0.0000') }}</span>
													<span v-else>{{ $filters.formatAmt(sp.fxRate, '0,0.000') }}</span><br>
													{{ sp.codeValue ? '-' : $filters.formatAmt(sp.price, '0,0.0000') }}
												</td>
												<td v-if="sp.proCode" class="text-end">
													{{ $filters.formatAmt(sp.invAmtFc) }}<br>
													{{ sp.codeValue ? '-' : $filters.formatAmt(sp.mktAmtFc) }}<br>
													{{ sp.codeValue ? '-' : $filters.formatAmt(sp.uplFc) }}
												</td>
												<td v-if="sp.proCode" class="text-end">
													{{ sp.codeValue ? '-' : $filters.formatPct(sp.ureturnFc) }}%<br>
													{{ sp.codeValue ? '-' : $filters.formatAmt(sp.totDividendFc) }}<br>
													{{ sp.codeValue ? '-' : $filters.formatPct(sp.ureturnFcDividend) }}%
												</td>
												<td v-if="sp.proCode" class="text-end">
													{{ $filters.formatDate(sp.expireDt) }}<br>
													{{ sp.intFreqUnittypeName }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
						<div class="tx-note">
							<ul>
								<li>截至資料日期之投資組合明細(信託資金投資標的參考價格為本行可取得之最新報價，僅供參考。)</li>
								<li>『累計配息』依憑證編號累計，憑證編號經更新後包括但不限於轉換，累計配息將重新起算。</li>
								<li>投資金額、投資損益及報酬率計算均未含相關申購費用；投資損益採計方式為依憑證編號分別計算之。</li>
								<li>到期保本率係為原幣信託且未扣除信託管理費。</li>
							</ul>
						</div>
					</div>
					<div id="tab-perf" role="tabpanel" class="tab-pane fade">
						<div class="card card-table">
							<div class="card-header">
								<h4>持有明細</h4>
							</div>
							<table class="table table-striped table-RWD table-bordered">
								<thead>
									<tr>
										<th class="text-center">
											<input
												id="checkAll"
												v-model="checkAll"
												type="checkbox"
												class="form-check-input"
												@change="toggleCheckAll"
											>
										</th>
										<th><span>商品代碼</span></th>
										<th><span>商品名稱</span></th>
										<th>計價幣別</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="sp in _.uniqBy(spList, 'proCode')">
										<td class="text-center">
											<input
												v-model="selectedProCodes"
												type="checkbox"
												name="selectedProCodes"
												class="form-check-input"
												:value="sp.proCode"
											>
										</td>
										<td>{{ sp.bankProCode }}</td>
										<td>{{ sp.proName }}</td>
										<td>{{ sp.tranCurName }}</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div class="tx-note">
							<ul>
								<li>點選『績效分析』可展現各商品與比較基準的績效比較線圖。</li>
								<li>點選基金名稱，可檢視該檔基金的產品細部資料。</li>
							</ul>
						</div>
						<div class="text-end">
							<input
								type="button"
								class="btn btn-primary btn-lg"
								value="績效分析"
								@click="calPerformances"
							>
						</div>
						<div v-if="isShowPerformances" id="fundCompare" class="card card-table my-4">
							<div class="card-header">
								<h4>境內外結構型商品績效分析</h4>
							</div>
							<div class="form">
								<div class="row g-3">
									<div class="col-lg-6">
										<label class="form-label">選擇商品</label>
										<div class="input-group">
											<select v-model="proCode" class="form-select">
												<option v-for="item in productList" :value="item.proCode">
													{{ item.proName }} {{ item.bankProCode }}
												</option>
											</select>
											<button class="btn btn-primary" @click="addPro()">
												加入
											</button>
										</div>
									</div>
								</div>
							</div>
							<div class="caption">
								已加入商品
							</div>
							<div class="table-responsive mb-3">
								<table class="table table-striped table-bordered table-blue">
									<thead>
										<tr>
											<th>商品名稱</th>
											<th class="text-end">
												一年報酬率
											</th>
											<th class="text-center">
												動作
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in observedProList">
											<td>{{ item.proName }}{{ item.bankProCode }}</td>
											<td class="text-end">
												{{ $filters.formatPct(item.fcTdReturn) }}%
											</td>
											<td class="text-center">
												<button
													type="button"
													class="btn btn-danger btn-icon"
													data-bs-toggle="tooltip"
													title="刪除"
													@click="deletePro(item.proCode)"
												>
													<i class="fa-solid fa-trash" />
												</button>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="text-center">
								<vue-performances-chart
									ref="spPerformancesChartRef"
									:chart-id="performancesId"
								/>
								<div class="btn-group btn-group-sm mb-4" role="group">
									<template v-for="item in proPriceRangeMenu">
										<input
											:id="'performancesPeriod' + item.termValue"
											type="radio"
											class="btn-check"
											name="time"
											:checked="item.termValue == '4' ? true : false"
											@click="getSpPerformances(item.rangeType, item.rangeFixed)"
										>
										<label class="btn btn-outline-secondary" :for="'performancesPeriod' + item.termValue">{{
											item.termName }}</label>
									</template>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import vuePerformancesChart from '@/views/pro/PRO0101/include/performancesChart.vue';
import _ from 'lodash';
import vueCusAssetsPieChart from '@/views/cus/CUS0213/include/pieChart.vue';
export default {
	components: {
		vuePerformancesChart,
		vueCusAssetsPieChart
	},
	props: {
		cusCode: String,
		fxRate: {
			type: Number,
			default: 1
		},
		baseCurrencyName: {
			type: String,
			default: '新台幣'
		}
	},
	data: function () {
		return {
			_: _,
			activeTab: '#tab-save',
			spCurChartData: [],
			spTypeChartData: [],
			spList: [],
			onwaySpList: [],
			checkAll: false,
			isShowPerformances: false,
			chartsData: [], // 績效分析  績效所得圖表資料
			selectedProCodes: [], // 績效分析 已加入商品
			productList: [], // 績效分析 加入商品清單
			observedProList: [], // 績效分析 已加入商品顯示報酬率清單
			proCode: null, // 績效分析 選擇商品
			proPriceRangeMenu: [],
			performancesId: 'performancesChartId'
		};
	},
	watch: {
		cusCode: function () {
			this.initData();
		}
	},
	mounted: function () {
		this.activeTab = '#tab-save';
		this.initData();
	},
	methods: {
		initData() {
			this.getSpCurChartData();
			this.getSpTypeChartData();
			this.getSpList();
			this.getOnwaySpList();
		},
		async getSpCurChartData() {
			const result = await this.$api.getCusSpGroupByCurApi({
				cusCode: this.cusCode
			});
			this.spCurChartData = result.data;
			this.spCurChartData.forEach((sp) => {
				sp.category = sp.proCurName;
				sp.value = sp.mktAmtLc + sp.totDividendLc;
			});
		},
		async getSpTypeChartData() {
			const result = await this.$api.getCusSpGroupByProtypeApi({
				cusCode: this.cusCode
			});
			this.spTypeChartData = result.data;
			this.spTypeChartData.forEach((sp) => {
				sp.category = sp.protypeName;
				sp.value = sp.mktAmtLc + sp.totDividendLc;
			});
		},
		async getSpList() {
			const result = await this.$api.getCusSpApi({
				cusCode: this.cusCode
			});
			this.spList = result.data;
		},
		async getOnwaySpList() {
			const result = await this.$api.getCusOnwayApi({
				cusCode: this.cusCode,
				pfcatCode: 'SP'
			});
			this.onwaySpList = result.data;
		},
		async getProPriceRangeMenu() {
			const ret = await this.$api.getProPriceRangeMenuApi();
			this.proPriceRangeMenu = ret.data;
		},
		async getProductList() {
			const self = this;
			const ret = await self.$api.getProductByPfcatCodeApi({
				pfcatCode: 'SP'
			});
			self.productList = ret.data;
		},
		toggleCheckAll() {
			this.selectedProCodes = [];
			if (this.checkAll) {
				this.selectedProCodes = this.spList.reduce(function (pcs, sp) {
					if (sp.proCode && !pcs.includes(sp.proCode)) {
						pcs.push(sp.proCode);
					}
					return pcs;
				}, []);
			}
		},
		async calPerformances() {
			if (this.selectedProCodes.length === 0) {
				$.bi.message('至少勾選一筆商品');
				return;
			}
			const ret = await this.$api.getObservedProductsApi({
				proCodes: this.selectedProCodes.join()
			});
			this.observedProList = ret.data;
			this.getSpPerformances('Y', -1.0);
			this.getProPriceRangeMenu();
			this.getProductList();
			this.isShowPerformances = true;
		},
		getSpPerformances: async function (rangeType, rangeFixed) {
			const self = this;
			const ret = await self.$api.etPerformanceRunChartApi({
				proCodes: self.selectedProCodes,
				freqType: rangeType, // 示區間類型
				freqFixed: rangeFixed // "顯示區間數值
			});
			if (!_.isNil(ret.data)) {
				for (let i = 0; i < ret.data.length; i++) {
					ret.data[i].datas.forEach((e) => {
						e.value = e.returnFc;
						e.date = new Date(e.returnDt).getTime();
					});
				}
				self.chartsData = ret.data;
				self.$refs.spPerformancesChartRef.initChart(self.chartsData);
			}
		},
		addPro() {
			const self = this;
			let pk = null;
			pk = _.find(self.selectedProCodes, function (item) {
				return item == self.proCode;
			});
			if (self.proCode != null && pk == null) {
				self.selectedProCodes.push(self.proCode); // 加入選擇商品
				self.calPerformances();
			}
			else if (self.proCode != null && pk != null) {
				$.bi.alert('此商品已加入');
			}
			else {
				$.bi.alert('請選擇商品');
			}
		},
		deletePro(proCode) {
			const self = this;
			if (self.selectedProCodes.length > 1) {
				const index = self.selectedProCodes.indexOf(proCode); // 找出要移除的index
				self.selectedProCodes.splice(index, 1); // 移除加入的商品(要插入或刪除的索引位置, 要刪除的元素數量)
				self.calPerformances();
			}
			else {
				$.bi.alert('至少要有一項商品');
			}
		}
	}
};
</script>
