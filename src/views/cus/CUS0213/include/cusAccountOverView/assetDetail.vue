<template>
	<h4 class="tx-title mt-3">
		存款
	</h4>
	<div class="card card-table card-collapse mb-3">
		<div class="card-header">
			<h4>活期性存款帳戶明細</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup" />
		</div>
		<div id="collapseListGroup" class="collapse show">
			<div class="table-responsive">
				<table class="table bih-table table-RWD">
					<thead>
						<tr>
							<th><span class="TC">存款種類</span></th>
							<th><span class="TC">帳號</span></th>
							<th><span class="TC">績效行</span></th>
							<th><span class="TC">幣別</span></th>
							<th><span class="TC">最近交易日</span></th>
							<th><span class="TC">餘額（原幣）</span></th>
							<th><span class="TC">餘額（折台幣）</span></th>
							<th>上月平均餘額（千元）(原幣)</th>
							<th><span class="TC">近一年平均餘額(原幣)</span></th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="sav in savingP50P54">
							<td>{{ sav.protypeName }}</td>
							<td>{{ sav.accNo }}</td>
							<td>{{ sav.tranBranName }}</td>
							<td>{{ sav.curCode }}</td>
							<td class="num">
								{{ $filters.formatDate(sav.lastTranDt) }}
							</td>
							<td class="num">
								{{ $filters.formatAmt(sav.balFc) }}
							</td>
							<td class="num">
								{{ $filters.formatAmt(sav.balLc) }}
							</td>
							<td class="num">
								{{ $filters.formatAmt(sav.avgBalMonth) }}
							</td>
							<td class="num">
								{{ $filters.formatAmt(sav.avgBalYear) }}
							</td>
						</tr>
						<tr class="tr-sum">
							<td colspan="6" class="num">
								新臺幣 總計
							</td>
							<td class="num">
								{{ $filters.formatAmt($filters.sumTotal(savingP50P54, 'balLc')) }}
							</td>
							<td class="num">
								{{ $filters.formatAmt($filters.sumTotal(savingP50P54, 'avgBalMonth')) }}
							</td>
							<td class="num">
								{{ $filters.formatAmt($filters.sumTotal(savingP50P54, 'avgBalYear')) }}
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<div class="card card-table card-collapse mb-3">
		<div class="card-header">
			<h4>定期性存款存單明細</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2" />
		</div>
		<div id="collapseListGroup2" class="collapse show">
			<div class="table-responsive">
				<table class="table bih-table table-RWD">
					<thead>
						<tr>
							<th><span class="TC">存單種類</span></th>
							<th><span class="TC">綜合存款記號</span></th>
							<th><span class="TC">帳號/存單帳號</span></th>
							<th><span class="TC">績效行</span></th>
							<th><span class="TC">幣別</span></th>
							<th>起息日</th>
							<th><span class="TC">到期日</span></th>
							<th>期別</th>
							<th><span class="TC">存單利率(%)</span></th>
							<th>自動展期</th>
							<th>展期次數</th>
							<th>存單金額(原幣)</th>
							<th><span class="TC">存單金額(折台幣)</span></th>
							<th>質借註記</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="sav in savingP51P53">
							<td>{{ sav.ynRecNum }}</td>
							<td />
							<td>{{ sav.accNo }}</td>
							<td>{{ sav.tranBranName }}</td>
							<td>{{ sav.curCode }}</td>
							<td>{{ $filters.formatDate(sav.valueDt) }}</td>
							<td>{{ $filters.formatDate(sav.expireDt) }}</td>
							<td class="num">
								{{ sav.tenor }}
							</td>
							<td class="num">
								{{ $filters.formatPct(sav.intRate) }}%
							</td>
							<td>{{ sav.autoRenewYn }}</td>
							<td class="num">
								{{ sav.preDefineTimes }}
							</td>
							<td class="num">
								{{ $filters.formatAmt(sav.balFc) }}
							</td>
							<td class="num">
								{{ $filters.formatAmt(sav.balLc) }}
							</td>
							<td>{{ sav.cdsBorrow }}</td>
						</tr>
						<tr class="tr-sum">
							<td colspan="11" class="text-right">
								新臺幣 總計
							</td>
							<td class="text-right">
&nbsp;
							</td>
							<td class="num">
								{{ $filters.formatAmt($filters.sumTotal(savingP51P53, 'balLc')) }}
							</td>
							<td>&nbsp;</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<div class="card p-3 bg-sum">
		<h5 class="tr-sum mb-0 sum">
			存款總餘額(折合台幣)： <b>{{ $filters.formatAmt($filters.sumTotal(savingP50P54, 'balLc') + $filters.sumTotal(savingP51P53,
				'balLc')) }}</b>
		</h5>
	</div>
	<h4 class="tx-title mt-3">
		信託-基金
	</h4>
	<div class="card card-table card-collapse mb-3">
		<div class="card-header">
			<h4>在途明細</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsedetail" />
		</div>
		<div id="collapsedetail" class="collapse show">
			<table class="bih-table table table-RWD">
				<thead>
					<tr>
						<th>
							<p><span class="TC">投資標的</span></p>
						</th>
						<th><span class="TC">交易型態</span></th>
						<th><span class="TC">交易金額</span>/單位數</th>
						<th><span class="TC">憑證編號</span></th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="onway in onwayFundList">
						<td><a href="#">{{ onway.proName }}</a></td>
						<td>{{ onway.trantypeName }}</td>
						<td class="num">
							{{ onway.onwayUnit }}
						</td>
						<td class="num">
							{{ onway.refNo }}
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<h4 class="tx-title mt-3">
		庫存明細
	</h4>
	<!--基金單筆持有明細-->
	<div class="card card-table card-collapse mb-3">
		<div class="card-header">
			<h4>基金單筆持有明細</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsedetail2" />
		</div>
		<div id="collapsedetail2" class="collapse show">
			<table class="bih-table table table-RWD">
				<thead>
					<tr>
						<th width="17%" class="text-start">
							投資標的<br>
							商品代碼<br>
							憑證編號<br>
							<br>
						</th>
						<th width="10%" class="num">
							信託起日<br>
							參考淨值日<br>
							參考淨值(B)<br>
						</th>
						<th width="11%" class="num">
							持有單位數<span class="text-start">(A)</span><br>
							參考匯率(C)<br>
							匯率基準日<br>
							<br>
						</th>
						<th width="14%" class="num">
							投資金額(D)<br>
							投資現值(E=A*B*C)<br>
							<br>
							<br>
						</th>
						<th width="17%" class="num">
							投資損益(F=E-D)<br>
							報酬率(H=F/D)<br>
							累計配息(G)<br>
							含息報酬率(I=(F+G)/D)
						</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="fund in fundList_S">
						<td>
							<a href="#" @click.prevent="$refs.productModal.open(fund.proCode, 'FUND')">{{ fund.proName }}</a><br>
							{{ fund.bankProCode }}<br>
							{{ fund.refNo }}<br><br>
						</td>
						<td class="num">
							{{ $filters.formatDate(fund.firstBuyDt) }}<br>
							{{ $filters.formatDate(fund.priceDt) }}<br>
							{{ $filters.formatAmt(fund.price) }} {{ fund.tranCurCode }}<br>
						</td>
						<td class="num">
							{{ $filters.formatAmt(fund.unit) }}<br>
							{{ $filters.formatAmt(fund.fxRate) }}<br>
							{{ $filters.formatDate(fund.fxRateDt) }}<br><br>
						</td>
						<td class="num">
							{{ $filters.formatAmt(fund.invAmtFc) }} {{ fund.tranCurCode }}<br>
							{{ $filters.formatAmt(fund.mktAmtFc) }} {{ fund.tranCurCode }}<br><br><br>
						</td>
						<td class="num">
							{{ $filters.formatAmt(fund.uplFc) }} {{ fund.tranCurCode }}<br>
							{{ $filters.formatPct(fund.ureturnFc) }}%<br>
							{{ $filters.formatAmt(fund.totDividendFc) }} {{ fund.tranCurCode }}<br>
							{{ $filters.formatPct(fund.ureturnFcDividend) }}%
						</td>
					</tr>
					<tr class="tr-sum">
						<td class="num">
							新臺幣 小計
						</td>
						<td class="num">
&nbsp;
						</td>
						<td class="num">
&nbsp;
						</td>
						<td class="num">
							<p>{{ $filters.formatAmt($filters.sumTotal(fundList_S, 'invAmtLc')) }}</p>
							<p>
								{{ $filters.formatAmt($filters.sumTotal(fundList_S, 'mktAmtLc')) }}<br>
							</p>
						</td>
						<td class="num">
							{{ $filters.formatAmt($filters.sumTotal(fundList_S, 'mktAmtLc') -
								$filters.sumTotal(fundList_S, 'invAmtLc')) }}<br>
							{{ $filters.sumTotal(fundList_S, 'invAmtLc') > 0 ? $filters.formatAmt(($filters.sumTotal(fundList_S,
								'mktAmtLc') - $filters.sumTotal(fundList_S, 'invAmtLc')) * 100 / $filters.sumTotal(fundList_S,
									'invAmtLc')) : 0 }}%<br>
							{{ $filters.formatAmt($filters.sumTotal(fundList_S, 'totDividendLc')) }}<br>
							{{ $filters.sumTotal(fundList_S, 'invAmtLc') > 0 ? $filters.formatAmt(($filters.sumTotal(fundList_S,
								'mktAmtLc') - $filters.sumTotal(fundList_S, 'invAmtLc') + $filters.sumTotal(fundList_S, 'totDividendLc'))
								* 100 / $filters.sumTotal(fundList_S, 'invAmtLc')) : 0 }}%
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>

	<!--基金定時定額持有明細-->
	<div class="card card-table card-collapse mb-3">
		<div class="card-header">
			<h4>基金定時定額持有明細</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapsedetail3" />
		</div>
		<div id="collapsedetail3" class="collapse show">
			<table class="bih-table table table-RWD">
				<thead>
					<tr>
						<th width="19%" class="text-start">
							投資標的<br>
							商品代碼<br>
							憑證編號<br>
							<br>
						</th>
						<th width="11%" class="num">
							信託起日 <br>
							參考淨值日<br>
							參考淨值(B)<br>
						</th>
						<th width="11%" class="num">
							持有單位數(A)<br>
							參考匯率(C)<br>
							匯率基準日<br>
							<br>
						</th>
						<th width="12%" class="num">
							投資金額(D)<br>
							投資現值(E=A*B*C)<br>
							<br>
							<br>
						</th>
						<th width="13%" class="num">
							投資損益(F=E-D)<br>
							報酬率(H=F/D)<br>
							累計配息(G)<br>
							含息報酬率(I=(F+G)/D)
						</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="fund in fundList_L">
						<td>
							<a href="#" @click.prevent="$refs.productModal.open(fund.proCode, 'FUND')">{{ fund.proName }}</a><br>
							{{ fund.bankProCode }}<br>
							{{ fund.refNo }}<br><br>
						</td>
						<td class="num">
							{{ $filters.formatDate(fund.firstBuyDt) }}<br>
							{{ $filters.formatDate(fund.priceDt) }}<br>
							{{ $filters.formatAmt(fund.price) }} {{ fund.tranCurCode }}<br>
						</td>
						<td class="num">
							{{ $filters.formatAmt(fund.unit) }}<br>
							{{ $filters.formatAmt(fund.fxRate) }}<br>
							{{ $filters.formatDate(fund.fxRateDt) }}<br><br>
						</td>
						<td class="num">
							{{ $filters.formatAmt(fund.invAmtFc) }} {{ fund.tranCurCode }}<br>
							{{ $filters.formatAmt(fund.mktAmtFc) }} {{ fund.tranCurCode }}<br><br><br>
						</td>
						<td class="num">
							{{ $filters.formatAmt(fund.uplFc) }} {{ fund.tranCurCode }}<br>
							{{ $filters.formatPct(fund.ureturnFc) }}%<br>
							{{ $filters.formatAmt(fund.totDividendFc) }} {{ fund.tranCurCode }}<br>
							{{ $filters.formatPct(fund.ureturnFcDividend) }}%
						</td>
					</tr>
					<tr class="tr-sum">
						<td class="num">
							新臺幣 小計
						</td>
						<td class="num">
&nbsp;
						</td>
						<td class="num">
&nbsp;
						</td>
						<td class="num">
							<p>{{ $filters.formatAmt($filters.sumTotal(fundList_L, 'invAmtLc')) }}</p>
							<p>
								{{ $filters.formatAmt($filters.sumTotal(fundList_L, 'mktAmtLc')) }}<br>
							</p>
						</td>
						<td class="num">
							{{ $filters.formatAmt($filters.sumTotal(fundList_L, 'mktAmtLc') -
								$filters.sumTotal(fundList_L, 'invAmtLc')) }}<br>
							{{ $filters.sumTotal(fundList_L, 'invAmtLc') > 0 ? $filters.formatAmt(($filters.sumTotal(fundList_L,
								'mktAmtLc') - $filters.sumTotal(fundList_L, 'invAmtLc')) * 100 / $filters.sumTotal(fundList_L,
									'invAmtLc')) : 0 }}%<br>
							{{ $filters.formatAmt($filters.sumTotal(fundList_L, 'totDividendLc')) }}<br>
							{{ $filters.sumTotal(fundList_L, 'invAmtLc') > 0 ? $filters.formatAmt(($filters.sumTotal(fundList_L,
								'mktAmtLc') - $filters.sumTotal(fundList_L, 'invAmtLc') + $filters.sumTotal(fundList_L, 'totDividendLc'))
								* 100 / $filters.sumTotal(fundList_L, 'invAmtLc')) : 0 }}%
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<!--FUND心配持有明細-->
	<div class="card card-table card-collapse mb-3">
		<div class="card-header">
			<h4>FUND心配持有明細</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseFund" />
		</div>
		<div id="collapseFund" class="collapse show">
			<table class="table bih-table table-RWD">
				<thead>
					<tr>
						<th width="20%" class="text-start">
							<p>
								投資標的<br>商品代碼<br>
								憑證編號
							</p>
						</th>
						<th width="7%">
							持有單位數(A)<br>母子基金別
						</th>
						<th width="7%">
							信託起日<br> <br>
						</th>
						<th width="9%">
							參考淨值(B)<br>
							參考淨值日<br>
						</th>
						<th width="7%">
							參考匯率(C)<br>匯率基準日
						</th>
						<th width="9%">
							投資金額(D)<br>
							投資現值<br>(E=A*B*C)
						</th>
						<th width="8%">
							投資損益<br>(F=E-D)<br>
							報酬率<br>(H=F/D)
						</th>
						<th width="6%">
							獲利點<br>
							停損點(負)
						</th>
					</tr>
				</thead>
				<tbody />
			</table>
		</div>
	</div>
	<div class="tx-note">
		<ol>
			<li>截至資料日期之投資組合明細(信託資金投資標的參考淨值為本行可取得之最新報價，僅供參考。)</li>
			<li>投資金額、投資損益及報酬率計算均未含相關申購費用；投資損益採計方式為依憑證編號分別計算之 。</li>
			<li>『累計配息』依憑證編號累計，憑證編號經更新後包括但不限於轉換，累計配息將重新起算。</li>
			<li>效率投資法之『信託起日』係指委託人向本行提出效率投資法投資之交易申請日。</li>
		</ol>
	</div>
	<!--信託-海外債-->
	<h4 class="tx-title mt-3">
		信託-海外債
	</h4>
	<!--在途明細-->
	<div class="card card-table card-collapse mb-3">
		<div class="card-header">
			<h4>在途明細</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseList3" />
		</div>
		<div id="collapseList3" class="collapse show">
			<div class="table-responsive">
				<table class="table bih-table table-RWD">
					<thead>
						<tr>
							<th>
								<p><span class="TC">投資標的</span></p>
							</th>
							<th>交易型態</th>
							<th>交易面額</th>
							<th><span class="TC">憑證編號</span></th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="onway in onwayBondList">
							<td><a href="#">{{ onway.proName }}</a></td>
							<td>{{ onway.trantypeName }}</td>
							<td class="num">
								{{ onway.onwayUnit }})
							</td>
							<td class="num">
								{{ onway.refNo }}
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>

	<!--庫存明細-->
	<div class="card card-table card-collapse mb-3">
		<div class="card-header">
			<h4>庫存明細</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseList4" />
		</div>
		<div id="collapseList4" class="collapse show">
			<div class="table-responsive">
				<table class="bih-table table table-RWD">
					<thead>
						<tr>
							<th width="11%" class="text-end">
								投資標的<br>商品代碼
							</th>
							<th width="11%" class="text-end">
								信託起日<br>憑證編號
							</th>
							<th width="9%" class="text-end">
								參考價格(B)<br>
								參考價格基準日
							</th>
							<th width="8%" class="text-end">
								參考匯率(C)<br>匯率基準日
							</th>
							<th width="11%" class="text-end">
								庫存面額<span class="text-start">(A)</span><br>
								投資金額(D)
							</th>
							<th width="8%" class="text-start">
								<span class="text-end">投資現值<br>(E=A*B*C)</span>
							</th>
							<th width="11%" class="text-start">
								投資損益<span class="text-end">(F=E-D)</span><br>報酬率<span
									class="text-end"
								>(H=F/D)</span>
							</th>
							<th width="8%" class="text-end">
								累計配息(G)<br>
								含息報酬率<br>(I=(F+G)/D)
							</th>
							<th width="7%" class="text-start">
								到期日<br>
								前手息
							</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="bond in bondList">
							<td>
								<span class="text-start"><a href="#" @click.prevent="$refs.productModal.open(bond.proCode, 'BOND')">{{
										bond.proName }}</a><br>
									{{ bond.bankProCode }} </span>
							</td>
							<td class="text-end">
								{{ $filters.formatDate(bond.firstBuyDt) }}<br>
								{{ bond.refNo }}
							</td>
							<td class="text-end">
								{{ $filters.formatAmt(bond.price) }} {{ bond.tranCurCode }}<br>
								{{ $filters.formatDate(bond.priceDt) }}
							</td>
							<td class="text-end">
								{{ $filters.formatAmt(bond.fxRate) }}<br>
								{{ $filters.formatDate(bond.fxRateDt) }}
							</td>
							<td class="text-end">
								<span class="text-start">{{ $filters.formatAmt(bond.unit) }}
									{{ bond.tranCurCode }}</span><br>
								{{ $filters.formatAmt(bond.invAmtFc) }} {{ bond.tranCurCode }}
							</td>
							<td class="text-end">
								{{ $filters.formatAmt(bond.mktAmtFc) }} {{ bond.tranCurCode }}
							</td>
							<td class="text-end">
								{{ $filters.formatAmt(bond.uplFc) }} {{ bond.tranCurCode }}<br>
								{{ $filters.formatPct(bond.ureturnFc) }}%
							</td>
							<td class="text-end">
								{{ $filters.formatAmt(bond.totDividendFc) }} {{ bond.tranCurCode }}<br>
								{{ $filters.formatPct(bond.ureturnFcDividend) }}%
							</td>
							<td>
								<span class="text-end">{{ $filters.formatDate(bond.expireDt) }}<br>
									{{ $filters.formatAmt(bond.orderLInt) }}</span>
							</td>
						</tr>
						<tr class="tr-sum">
							<td class="text-end">
								新臺幣 小計
							</td>
							<td class="text-end">
&nbsp;
							</td>
							<td class="text-end">
&nbsp;
							</td>
							<td class="text-end">
&nbsp;
							</td>
							<td class="text-end">
								{{ $filters.formatAmt($filters.sumTotal(bondList, 'invAmtLc')) }}
							</td>
							<td class="text-end">
								{{ $filters.formatAmt($filters.sumTotal(bondList, 'mktAmtLc')) }}
							</td>
							<td class="text-end">
								{{ $filters.formatAmt(($filters.sumTotal(bondList, 'mktAmtLc') -
									$filters.sumTotal(bondList, 'invAmtLc')) * 100 / $filters.sumTotal(bondList, 'invAmtLc')) }}%
							</td>
							<td class="text-end">
								{{ $filters.formatAmt(($filters.sumTotal(bondList, 'mktAmtLc') -
									$filters.sumTotal(bondList, 'invAmtLc') + $filters.sumTotal(bondList, 'totDividendLc')) * 100 /
									$filters.sumTotal(bondList, 'invAmtLc')) }}%
							</td>
							<td>&nbsp;</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<div class="tx-note">
		<ul>
			<li>截至資料日期之投資組合明細(信託資金投資標的參考價格為本行可取得之最新報價，僅供參考。)</li>
			<li>投資金額、投資損益及報酬率計算均未含相關申購費用；投資損益採計方式為依憑證編號分別計算之 。</li>
		</ul>
	</div>
	<!--信託-ETF-->
	<h4 class="tx-title mt-3">
		信託-ETF
	</h4>
	<div class="card card-table card-collapse mb-3">
		<div class="card-header">
			<h4>在途明細</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup9" />
		</div>
		<div id=" collapseListGroup9" class="collapse show">
			<table class="bih-table table table-RWD">
				<thead>
					<tr>
						<th>交易日</th>
						<th><span class="TC">交易類別</span></th>
						<th>
							<p><span class="TC">投資標的</span></p>
						</th>
						<th>股數</th>
						<th>成交價格</th>
						<th><span class="TC">成交序號</span></th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="onway in onwayEtfList">
						<td>{{ $filters.formatDate(onway.orderDt) }}</td>
						<td>{{ onway.trantypeName }}</td>
						<td>
							<a href="#"><span class="TC">{{ onway.proName }}
							</span></a>
						</td>
						<td class="num">
							{{ $filters.formatAmt(onway.onwayUnit) }}
						</td>
						<td class="num">
							{{ $filters.formatAmt(onway.onwayAmtFc) }}
						</td>
						<td class="num">
							{{ onway.refNo }}
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>

	<!--庫存明細-->
	<div class="card card-table card-collapse mb-0">
		<div class="card-header">
			<h4>庫存明細</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup10" />
		</div>
		<div id=" collapseListGroup10" class="collapse show">
			<div class="table-responsive">
				<table class="bih-table table table-RWD">
					<thead>
						<tr>
							<th class="text-start" colspan="8">
								商品名稱
							</th>
						</tr>
						<tr>
							<th width="10%" class="num">
								商品代號<br>交易市場
							</th>
							<th width="9%" class="num">
								幣別<br>計價幣別
							</th>
							<th width="8%" class="num">
								可交易股數含配股(A)<br>信託金額
							</th>
							<th width="13%" class="num">
								參考價格(B)<br>參考價格日
							</th>
							<th width="9%" class="num">
								參考現值(A)*(B)<br>參考信託管理費
							</th>
							<th width="9%" class="num">
								<br>累計配息
							</th>
							<th width="6%" class="num">
								投資損益<br>含息損益
							</th>
							<th width="9%" class="num">
								報酬率(%)<br>含息報酬率
							</th>
						</tr>
					</thead>
					<tbody>
						<template v-for="etf in etfList">
							<tr>
								<td class="text-start" colspan="8">
									<a
										href="#"
										@click.prevent="$refs.productModal.open(etf.proCode, 'ETF')"
									>{{ etf.proName }}</a>
								</td>
							</tr>
							<tr>
								<td width="10%" class="num">
									{{ etf.bankProCode }}<br>{{ etf.exCode }}
								</td>
								<td width="9%" class="num">
									{{ etf.tranCurCode }}<br>{{ etf.curCode }}
								</td>
								<td width="8%" class="num">
									{{ $filters.formatAmt(etf.unit) }}<br>{{ $filters.formatAmt(etf.invAmtFc) }}
								</td>
								<td width="13%" class="num">
									{{ $filters.formatAmt(etf.price) }}<br>{{ $filters.formatDate(etf.priceDt)
									}}
								</td>
								<td width="9%" class="num">
									{{ $filters.formatAmt(etf.mktAmtFc) }}<br>{{ $filters.formatAmt(etf.mFee) }}
								</td>
								<td width="9%" class="num">
									<br>{{ $filters.formatAmt(etf.totDividendFc) }}
								</td>
								<td width="6%" class="num">
									{{ $filters.formatAmt(etf.uplFc) }}<br>{{ $filters.formatAmt(etf.uplFcDividend) }}
								</td>
								<td width="9%" class="num">
									{{ $filters.formatPct(etf.ureturnFc) }}%<br>{{ $filters.formatPct(etf.ureturnFcDividend) }}%
								</td>
							</tr>
						</template>
						<tr class="tr-sum">
							<td class="num" colspan="2">
								新台幣 小計
							</td>
							<td class="num">
								{{ $filters.formatAmt($filters.sumTotal(etfList, 'invAmtFc')) }}
							</td>
							<td class="num" />
							<td class="num">
								{{ $filters.formatAmt($filters.sumTotal(etfList, 'mktAmtFc')) }}
							</td>
							<td />
							<td class="num">
								{{ $filters.formatAmt($filters.sumTotal(etfList,
									'uplFc')) }}<br>{{ $filters.formatAmt($filters.sumTotal(etfList, 'uplFcDividend')) }}
							</td>
							<td class="num">
								{{ $filters.formatAmt(($filters.sumTotal(etfList, 'mktAmtFc') - $filters.sumTotal(etfList, 'invAmtFc'))
									*
									100 /
									$filters.sumTotal(etfList, 'invAmtFc')) }}%<br>
								{{ $filters.formatAmt(($filters.sumTotal(etfList, 'mktAmtFc') - $filters.sumTotal(etfList, 'invAmtFc') +
									$filters.sumTotal(etfList, 'totDividendFc')) * 100 / $filters.sumTotal(etfList, 'invAmtFc')) }}%
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<div class="tx-note">
		<ol>
			<li>截至資料日期之投資組合明細(信託資金投資標的參考淨值為本行可取得之最新報價，僅供參考。)</li>
			<li>投資金額、投資損益及報酬率計算均未含相關申購費用；庫存投資金額係採先進先出法依投資標的分別計算之 。</li>
		</ol>
	</div>
	<h4 class="tx-title mt-3">
		信託-海外股票
	</h4>
	<div class="card card-table card-collapse mb-3">
		<div class="card-header">
			<h4>在途明細</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseoverseas" />
		</div>
		<div id="collapseoverseas" class="collapse show">
			<table class="bih-table table table-RWD">
				<thead>
					<tr>
						<th>交易日</th>
						<th><span class="TC">交易類別</span></th>
						<th>
							<p><span class="TC">投資標的</span></p>
						</th>
						<th>股數</th>
						<th>成交價格</th>
						<th><span class="TC">成交序號</span></th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="onway in onwayPfdList">
						<td>{{ $filters.formatDate(onway.orderDt) }}</td>
						<td>{{ onway.trantypeName }}</td>
						<td>
							<a href="#"><span class="TC">{{ onway.proName }}
							</span></a>
						</td>
						<td class="num">
							{{ $filters.formatAmt(onway.onwayUnit) }}
						</td>
						<td class="num">
							{{ $filters.formatAmt(onway.onwayAmtFc) }}
						</td>
						<td class="num">
							{{ onway.refNo }}
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<div class="card card-table card-collapse mb-0">
		<div class="card-header">
			<h4>庫存明細</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseoverseas1" />
		</div>
		<div id="collapseoverseas1" class="collapse show">
			<div class="table-responsive">
				<table class="bih-table table table-RWD">
					<thead>
						<tr>
							<th class="text-start" colspan="8">
								商品名稱
							</th>
						</tr>
						<tr>
							<th width="10%" class="num">
								商品代號<br>交易市場
							</th>
							<th width="9%" class="num">
								幣別<br>計價幣別
							</th>
							<th width="8%" class="num">
								可交易股數含配股(A)<br>信託金額
							</th>
							<th width="13%" class="num">
								參考價格(B)<br>參考價格日
							</th>
							<th width="9%" class="num">
								參考現值(A)*(B)<br>參考信託管理費
							</th>
							<th width="9%" class="num">
								<br>累計配息
							</th>
							<th width="6%" class="num">
								投資損益<br>含息損益
							</th>
							<th width="9%" class="num">
								報酬率(%)<br>含息報酬率
							</th>
						</tr>
					</thead>
					<tbody>
						<template v-for="pfd in pfdList">
							<tr>
								<td class="text-start" colspan="8">
									<a
										href="#"
										@click.prevent="$refs.productModal.open(pfd.proCode, 'PFD')"
									>{{ pfd.proName }}</a>
								</td>
							</tr>
							<tr>
								<td width="10%" class="num">
									{{ pfd.bankProCode }}<br>{{ pfd.exCode }}
								</td>
								<td width="9%" class="num">
									{{ pfd.tranCurCode }}<br>{{ pfd.curCode }}
								</td>
								<td width="8%" class="num">
									{{ $filters.formatAmt(pfd.unit) }}<br>{{ $filters.formatAmt(pfd.invAmtFc) }}
								</td>
								<td width="13%" class="num">
									{{ $filters.formatAmt(pfd.price) }}<br>{{ $filters.formatDate(pfd.priceDt)
									}}
								</td>
								<td width="9%" class="num">
									{{ $filters.formatAmt(pfd.mktAmtFc) }}<br>{{ $filters.formatAmt(pfd.mFee) }}
								</td>
								<td width="9%" class="num">
									<br>{{ $filters.formatAmt(pfd.totDividendFc) }}
								</td>
								<td width="6%" class="num">
									{{ $filters.formatAmt(pfd.uplFc) }}<br>{{ $filters.formatAmt(pfd.uplFcDividend) }}
								</td>
								<td width="9%" class="num">
									{{ $filters.formatPct(pfd.ureturnFc) }}%<br>{{ $filters.formatPct(pfd.ureturnFcDividend) }}%
								</td>
							</tr>
						</template>
						<tr class="tr-sum">
							<td class="num" colspan="2">
								新台幣 小計
							</td>
							<td class="num">
								{{ $filters.formatAmt($filters.sumTotal(pfdList, 'invAmtFc')) }}
							</td>
							<td class="num" />
							<td class="num">
								{{ $filters.formatAmt($filters.sumTotal(pfdList, 'mktAmtFc')) }}
							</td>
							<td />
							<td class="num">
								{{ $filters.formatAmt($filters.sumTotal(pfdList,
									'uplFc')) }}<br>{{ $filters.formatAmt($filters.sumTotal(pfdList, 'uplFcDividend')) }}
							</td>
							<td class="num">
								{{ $filters.formatAmt(($filters.sumTotal(pfdList, 'mktAmtFc') - $filters.sumTotal(pfdList, 'invAmtFc'))
									*
									100 / $filters.sumTotal(pfdList, 'invAmtFc')) }}%<br>
								{{ $filters.formatAmt(($filters.sumTotal(pfdList, 'mktAmtFc') - $filters.sumTotal(pfdList, 'invAmtFc') +
									$filters.sumTotal(pfdList, 'totDividendFc')) * 100 / $filters.sumTotal(pfdList, 'invAmtFc')) }}%
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<div class="tx-note">
		<ol>
			<li>截至資料日期之投資組合明細(信託資金投資標的參考淨值為本行可取得之最新報價，僅供參考。)</li>
			<li>投資金額、投資損益及報酬率計算均未含相關申購費用；庫存投資金額係採先進先出法依投資標的分別計算之 。</li>
		</ol>
	</div>
	<h4 class="tx-title mt-3">
		人身保險
	</h4>
	<!-- Nav tabs -->
	<div class="tab-nav-tabs">
		<!-- Nav tabs -->
		<ul class="nav nav-tabs nav-justified">
			<li class="nav-item">
				<a
					class="nav-link active"
					href="#"
					data-bs-toggle="tab"
					@click="insList = insListY"
				>要保人有效保單({{ insListY.length
				}})</a>
			</li>
			<li class="nav-item">
				<a
					class="nav-link"
					href="#"
					data-bs-toggle="tab"
					@click="insList = insListN"
				>要保人非有效保單({{ insListN.length
				}})</a>
			</li>
			<li class="nav-item">
				<a
					class="nav-link"
					href="#"
					data-bs-toggle="tab"
					@click="insList = insListI"
				>客戶為關係人之其他保單({{ insListI.length
				}})</a>
			</li>
		</ul>
		<div class="tab-content">
			<div id="tabAst" class="tab-pane fade active show">
				<div class="card card-table ">
					<div class="card-header">
						<h4>持有保單</h4>
					</div>
					<table class="table bih-table table-RWD">
						<thead>
							<tr>
								<th>保險公司<br>保單號碼</th>
								<th>商品類型<br>主約商品名稱</th>
								<th>要保人<br>被保險人</th>
								<th>保單生效日</th>
								<th>保單到期日</th>
								<th>累積實繳保費<br>(原幣)</th>
								<th>保單幣別</th>
								<th>保額<br>(原幣)</th>
								<th>保單狀態</th>
								<th>明細</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="ins in insList">
								<td>{{ ins.inscmpName }}<br>{{ ins.policyNo }}</td>
								<td>
									{{ ins.protypeName }}<br>
									<a href="#" data-bs-toggle="modal" data-bs-target="#myModal">{{ ins.proName }}</a>
								</td>
								<td>
									{{ ins.ownerName }}<br>
									{{ ins.insuredName }}
								</td>
								<td>{{ $filters.formatDate(ins.insValidDate) }}</td>
								<td>{{ $filters.formatDate(ins.insEndDate) }}</td>
								<td class="num">
									{{ $filters.formatAmt(ins.accPremiumFc) }}
								</td>
								<td>{{ ins.curCode }}</td>
								<td class="num">
									{{ $filters.formatAmt(ins.coverageFc) }}
								</td>
								<td>{{ ins.policyStatus }}</td>
								<td>
									<a href="#" @click="viewInsDetail(ins)">保單明細</a> <br>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
	<!-- modal  -->
	<vue-modal :is-open="isOpenInsModal" @close="() => isOpenInsModal = false">
		<template #content="props">
			<div class="modal-dialog modal-lg" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							保單明細
						</h4>
						<button type="button" class="btn-expand">
							<i class="bi bi-arrows-fullscreen" />
						</button>
						<button
							type="button"
							class="btn-close"
							data-bs-dismiss="modal"
							aria-label="Close"
						/>
					</div>
					<div class="modal-body">
						<div class="card card-form">
							<div class="card-header">
								<h4>保單資訊及主約保障內容</h4>
							</div>
							<table class="biv-table table table-RWD table-horizontal-RWD">
								<tbody>
									<tr>
										<th class="">
											保險公司
										</th>
										<td class="">
											{{ insDetail.inscmpName }}
										</td>
										<th class="">
											要保人
										</th>
										<td class="">
											{{ insDetail.ownerName }} {{ insDetail.ownerIdn }}
										</td>
										<th class="">
											主約被保險人
										</th>
										<td class="">
											{{ insDetail.insuredName }} {{ insDetail.insuredIdn }}
										</td>
									</tr>
									<tr>
										<th class="">
											保單狀態
										</th>
										<td class="">
											{{ insDetail.policyStatus }}
										</td>
										<th class="">
											保單生效日
										</th>
										<td class="">
											{{ $filters.formatDate(insDetail.insValidDate) }}
										</td>
										<th class="">
											解約/契徹/失效日
										</th>
										<td class="">
											{{ insDetail.insFailDate }}
										</td>
									</tr>
									<tr>
										<th class="">
											停效日期
										</th>
										<td class="">
											{{ $filters.formatDate(insDetail.stopStartDate) }}
										</td>
										<th class="">
											停效迄日
										</th>
										<td class="">
											{{ $filters.formatDate(insDetail.stopEndDate) }}
										</td>
										<th class="">
											主約保障終止日
										</th>
										<td class="">
											{{ $filters.formatDate(insDetail.mstExpireDate) }}
										</td>
									</tr>
									<tr>
										<th class="">
											主約商品類型
										</th>
										<td class="">
											{{ insDetail.protypeName }}
										</td>
										<th class="">
											主約商品名稱
										</th>
										<td class="">
											{{ insDetail.proName }}
										</td>
										<th class="" />
										<td class="" />
									</tr>
									<tr>
										<th class="">
											主約保額
										</th>
										<td class="">
											{{ $filters.formatAmt(insDetail.coverageFc) }}
										</td>
										<th class="">
											主約計量單位
										</th>
										<td class="" />
										<th class="">
											主約幣別
										</th>
										<td class="">
											{{ insDetail.curCode }}
										</td>
									</tr>
									<tr>
										<th class="">
											主約年化應繳保費
										</th>
										<td class="" />
										<th class="">
											主約繳費年期
										</th>
										<td class="">
											{{ insDetail.payTerm }}
										</td>
										<th class="">
											繳別
										</th>
										<td class="">
											{{ insDetail.payType }}
										</td>
									</tr>
									<tr>
										<th class="">
											下期應繳日
										</th>
										<td class="">
											{{ $filters.formatDate(insDetail.nextPayDate) }}
										</td>
										<th class="">
											繳費方式
										</th>
										<td class="" />
										<th class="">
											扣款銀行帳號/信用卡號
										</th>
										<td class="">
											{{ insDetail.debitAccno }}
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
					<div class="modal-footer">
						<div class="num">
							<input
								class="btn btn-white"
								type="button"
								value="關閉"
								data-bs-dismiss="modal"
							>
						</div>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- modal end -->
	<h4 class="tx-title mt-3">
		個人授信
	</h4>
	<div class="card card-table card-collapse mb-0">
		<div class="card-header">
			<h4>放款資料</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapspersonal" />
		</div>
		<div id="collapspersonal" class="collapse show">
			<table class="bih-table table table-RWD">
				<thead>
					<tr>
						<th>放款帳號</th>
						<th>招攬行</th>
						<th>初貸日</th>
						<th>到期日</th>
						<th>幣別</th>
						<th>核准金額</th>
						<th>本日餘額</th>
						<th>專案號</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="loan in loanList">
						<td>{{ loan.accNo }}</td>
						<td>
							{{ loan.branName }}<br>
						</td>
						<td>{{ $filters.formatDate(loan.firstLoanDate) }}</td>
						<td>{{ $filters.formatDate(loan.useEndDt) }}</td>
						<td>{{ loan.curCode }}</td>
						<td>{{ $filters.formatAmt(loan.useAmtFc) }}</td>
						<td>{{ $filters.formatAmt(loan.balFc) }}</td>
						<td>{{ loan.proName }}</td>
					</tr>
					<tr class="tr-sum">
						<td colspan="6" class="num">
							合計
						</td>
						<td>{{ $filters.formatAmt($filters.sumTotal(loanList, 'useAmtLc')) }}</td>
						<td>{{ $filters.formatAmt($filters.sumTotal(loanList, 'balLc')) }}</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<div class="tx-note">
		放款資訊僅顯示正常。
	</div>
	<div class="card card-table card-collapse mb-0">
		<div class="card-header">
			<h4>額度資料</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapspemoney" />
		</div>
		<div id="collapspemoney" class="collapse show">
			<table class="bih-table table table-RWD">
				<thead>
					<tr>
						<th>核准號碼</th>
						<th>幣別</th>
						<th>核准金額（仟）</th>
						<th>額度餘額（仟）</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="loan in loanQuotaList">
						<td>{{ loan.approveNo }}</td>
						<td>{{ loan.curCode }}</td>
						<td>{{ $filters.formatAmt(loan.approveAmtFc) }}</td>
						<td>{{ $filters.formatAmt(loan.uesdAmtFc) }}</td>
					</tr>
					<tr class="tr-sum">
						<td colspan="3" class="num">
							合計
						</td>
						<td>{{ $filters.formatAmt($filters.sumTotal(loanQuotaList, 'uesdAmtLc')) }}</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<div class="tx-note">
		查詢範圍限有效之額度。
	</div>
	<h4 class="tx-title mt-3">
		信用卡
	</h4>
	<div class="card card-table card-collapse mb-0">
		<div class="card-header">
			<h4>卡片資訊</h4>
			<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapspecard" />
		</div>
		<div id="collapspecard" class="collapse show">
			<table class="bih-table table table-RWD">
				<thead>
					<tr>
						<th>名稱</th>
						<th>卡種</th>
						<th>卡號</th>
						<th>正/附卡</th>
						<th>開卡狀態</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="card in cardList">
						<td>{{ card.cardName }}</td>
						<td>{{ card.cardTypeDesc }}</td>
						<td>{{ $filters.maskCusInfo(card.cardNo, 4, 8) }}</td>
						<td>
							<span v-if="card.primaryCode == 'Y'">正卡</span>
							<span v-if="card.primaryCode == 'N'">附卡</span>
						</td>
						<td>
							<span v-if="card.cardStatus == 'Y'">已開卡</span>
							<span v-if="card.cardStatus == 'N'">未開卡</span>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<div class="tx-note">
		「卡片資訊」為歸戶下正附卡資料，不含持有他人之附卡資料。
	</div>
	<div class="alert alert-block alert-danger fade in margin-top10">
		本功能畫面提供的有關資訊是截止統計日期，由系統根據客戶歷史投資資料自動生成的，僅供參考。
	</div>
</template>
<script>
import vueModal from '@/views/components/model.vue';
export default {
	components: {
		vueModal
	},
	props: {
		cusCode: String
	},
	data: function () {
		return {
			savingP50P54: [],
			savingP51P53: [],
			fundList: [],
			fundList_S: [],
			fundList_L: [],
			onwayFundList: [],
			bondList: [],
			onwayBondList: [],
			etfList: [],
			onwayEtfList: [],
			pfdList: [],
			onwayPfdList: [],
			insList: [],
			insListY: [],
			insListN: [],
			insListI: [],
			insDetail: {},
			isOpenInsModal: null,
			loanList: [],
			loanQuotaList: [],
			cardList: []
		};
	},
	watch: {
		cusCode: function () {
			this.initData();
		}
	},
	mounted: function () {
		this.initData();
	},
	methods: {
		async initData() {
			this.savingP50P54 = await this.getAssetData('/cus/cusSavingByProtypeCodes', { protypeCodes: ['P50', 'P54'].join() });
			this.savingP51P53 = await this.getAssetData('/cus/cusSavingByProtypeCodes', { protypeCodes: ['P51', 'P53'].join() });
			this.fundList = await this.getAssetData('/cus/cusFund');
			this.fundList_S = this.fundList.filter((fund) => {
				return fund.invType === 'S';
			});
			this.fundList_L = this.fundList.filter((fund) => {
				return fund.invType === 'L';
			});
			this.onwayFundList = await this.getAssetData('/cus/cusOnway', { pfcatCode: 'FUND' });
			this.bondList = await this.getAssetData('/cus/cusBond');
			this.onwayBondList = await this.getAssetData('/cus/cusOnway', { pfcatCode: 'BOND' });
			this.etfList = await this.getAssetData('/cus/cusEtf');
			this.onwayEtfList = await this.getAssetData('/cus/cusOnway', { pfcatCode: 'ETF' });
			this.pfdList = await this.getAssetData('/cus/cusPstock');
			this.onwayPfdList = await this.getAssetData('/cus/cusOnway', { pfcatCode: 'PFD' });
			this.insListY = await this.getAssetData('/cus/cusIns', { queryType: 'Y' });
			this.insList = this.insListY;
			this.insListN = await this.getAssetData('/cus/cusIns', { queryType: 'N' });
			this.insListI = await this.getAssetData('/cus/cusIns', { queryType: 'I' });
			this.loanList = await this.getAssetData('/cus/cusLoan');
			this.loanQuotaList = await this.getAssetData('/cus/cusLoanQuota');
			this.cardList = await this.getAssetData('/cus/cusCard');
		},
		async getAssetData(apiPath, param = null) {
			const res = await this.$api.getAssetDataApi({
				path: apiPath,
				cusCode: this.cusCode,
				params: param
			});
			return res.data;
		},
		viewInsDetail(ins) {
			this.insDetail = ins;
			this.isOpenInsModal = true;
		}
	}
};
</script>
