<template>
	<div class="tx-note">
		金額無特別註記幣別者，皆以新台幣計。
	</div>
	<div class="tab-nav-main">
		<ul class="nav nav-pills">
			<li class="nav-item">
				<a class="nav-link active" href="#SectionA" data-bs-toggle="tab">本行部位餘額（前一日）</a>
			</li>
			<!--                <li class="nav-item"><a class="nav-link" href="#SectionB" data-bs-toggle="tab">同業部位餘額</a></li>-->
			<!--                <li class="nav-item"><a class="nav-link" href="#SectionC" data-bs-toggle="tab">即時存款餘額</a></li>-->
		</ul>
		<div class="tab-content">
			<div id="SectionA" class="tab-pane fade active show">
				<div class="row">
					<div class="col-12">
						<div class="card card-table mb-3">
							<div class="card-body">
								<div class="row g-3 align-items-center">
									<div class="col-lg-5">
										<h4 class="tx-title">
											依類別
										</h4>
										<div id="chartdivFin" class="dbchart-container">
											<vue-cus-assets-pie-chart chart-id="savingTypeChart" :chart-data="savingTypeChartData" />
										</div>
										<h4 class="tx-title">
											依幣別
										</h4>
										<div id="chartdivFin2" class="dbchart-container">
											<vue-cus-assets-pie-chart chart-id="savingCurChart" :chart-data="savingCurChartData" />
										</div>
									</div>
									<div class="col-lg-7">
										<div class="tx-13 sum">
											(新臺幣:元)
										</div>
										<div class="card card-table mb-3">
											<div class="card-header">
												<h4>存款餘額: 依類別</h4>
											</div>
											<table class="table bih-table table-RWD">
												<thead>
													<tr>
														<th width="23%">
															<span class="TC">存款類別</span>
														</th>
														<th width="48%">
															<span class="TC">折台餘額</span>
														</th>
														<th width="29%">
															<span class="TC">所佔比例(台幣)</span>
														</th>
													</tr>
												</thead>
												<tbody>
													<tr v-for="sav in savingTypeChartData">
														<td>{{ sav.protypeName }}</td>
														<td class="num">
															{{ $filters.formatAmt(sav.balLc) }}
														</td>
														<td class="num">
															{{ $filters.formatAmt(sav.balLc * 100 /
																$filters.sumTotal(savingTypeChartData, 'balLc')) }}%
														</td>
													</tr>
													<tr class="tr-sum">
														<td class="num">
															小計
														</td>
														<td class="num">
															{{ $filters.formatAmt($filters.sumTotal(savingTypeChartData, 'balLc')) }}
														</td>
														<td class="num">
															100%
														</td>
													</tr>
												</tbody>
											</table>
										</div>
										<div class="card card-table mb-3">
											<div class="card-header">
												<h4>存款餘額: 月平均</h4>
											</div>
											<table class="table bih-table table-RWD">
												<thead>
													<tr>
														<th>
															<span class="TC">3個月平均</span>
														</th>
														<th>
															<span class="TC">6個月平均</span>
														</th>
														<th>
															<span class="TC">9個月平均</span>
														</th>
													</tr>
												</thead>
												<tbody>
													<tr>
														<td class="num">
															{{ $filters.formatAmt(savingAvg.avg3m) }}
														</td>
														<td class="num">
															{{ $filters.formatAmt(savingAvg.avg6m) }}
														</td>
														<td class="num">
															{{ $filters.formatAmt(savingAvg.avg9m) }}
														</td>
													</tr>
												</tbody>
											</table>
										</div>
										<div class="card card-table mb-3">
											<div class="card-header">
												<h4>存款餘額: 依幣別</h4>
											</div>
											<table class="table bih-table table-RWD">
												<thead>
													<tr>
														<th>幣別</th>
														<th>原幣餘額</th>
														<th>台幣餘額</th>
														<th>所佔比例(台幣)</th>
													</tr>
												</thead>
												<tbody>
													<tr v-for="sav in savingCurChartData">
														<td><a href="#">{{ sav.curName }}</a></td>
														<td class="num">
															{{ $filters.formatAmt(sav.balFc) }}
														</td>
														<td class="num">
															{{ $filters.formatAmt(sav.balLc) }}
														</td>
														<td class="num">
															{{ $filters.formatAmt(sav.balLc * 100 /
																$filters.sumTotal(savingTypeChartData, 'balLc')) }}%
														</td>
													</tr>
													<tr class="tr-sum">
														<td colspan="2" style="text-align:right">
															小計
														</td>
														<td class="num">
															{{ $filters.formatAmt($filters.sumTotal(savingTypeChartData, 'balLc')) }}
														</td>
														<td class="num">
															100%
														</td>
													</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="card card-table card-collapse mb-3">
							<div class="card-header">
								<h4>近三個月到期存單</h4>
								<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1" />
							</div>
							<div id="collapseListGroup1" class="collapse show">
								<table class="bih-table table table-RWD">
									<thead>
										<tr>
											<th width="10%">
												<span class="TC">幣別</span>
											</th>
											<th width="10%">
												本月(T)<br>
												到期金額(原幣)
											</th>
											<th>
												T+1月<br>
												到期金額(原幣)
											</th>
											<th width="12%">
												T+2月<br>
												到期金額(原幣)
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="sav in savingExpire">
											<td>{{ sav.curCode }}</td>
											<td class="num">
												{{ $filters.formatAmt(sav.expireBalFcT) }}
											</td>
											<td class="num">
												{{ $filters.formatAmt(sav.expireBalFcT1) }}
											</td>
											<td class="num">
												{{ $filters.formatAmt(sav.expireBalFcT2) }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="card card-table card-collapse mb-3">
							<div class="card-header">
								<h4>活期性存款帳戶明細</h4>
								<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2" />
							</div>
							<div id="collapseListGroup2" class="collapse show">
								<table class="table bih-table table-RWD">
									<thead>
										<tr>
											<th><span class="TC">存款種類</span></th>
											<th><span class="TC">帳號</span></th>
											<th><span class="TC">績效行</span></th>
											<th><span class="TC">幣別</span></th>
											<th><span class="TC">最近交易日</span></th>
											<th><span class="TC">餘額（原幣）</span></th>
											<th><span class="TC">餘額（折台幣）</span></th>
											<th>上月平均餘額（千元）(原幣)</th>
											<th><span class="TC">近一年平均餘額(原幣)</span></th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="sav in savingP50P54">
											<td>{{ sav.protypeName }}</td>
											<td>{{ sav.accNo }}</td>
											<td>{{ sav.tranBranName }}</td>
											<td>{{ sav.curCode }}</td>
											<td class="num">
												{{ $filters.formatDate(sav.lastTranDt) }}
											</td>
											<td class="num">
												{{ $filters.formatAmt(sav.balFc) }}
											</td>
											<td class="num">
												{{ $filters.formatAmt(sav.balLc) }}
											</td>
											<td class="num">
												{{ $filters.formatAmt(sav.avgBalMonth) }}
											</td>
											<td class="num">
												{{ $filters.formatAmt(sav.avgBalYear) }}
											</td>
										</tr>
										<tr class="tr-sum">
											<td colspan="6" class="num">
												新臺幣 總計
											</td>
											<td class="num">
												{{ $filters.formatAmt($filters.sumTotal(savingP50P54, 'balLc')) }}
											</td>
											<td class="num">
												{{ $filters.formatAmt($filters.sumTotal(savingP50P54, 'avgBalMonth')) }}
											</td>
											<td class="num">
												{{ $filters.formatAmt($filters.sumTotal(savingP50P54, 'avgBalYear')) }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="card card-table card-collapse mb-3">
							<div class="card-header">
								<h4>定期性存款存單明細</h4>
								<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup3" />
							</div>
							<div id="collapseListGroup3" class="collapse show">
								<table class="table bih-table table-RWD">
									<thead>
										<tr>
											<th><span class="TC">存單種類</span></th>
											<th><span class="TC">綜合存款記號</span></th>
											<th><span class="TC">帳號/存單帳號</span></th>
											<th><span class="TC">績效行</span></th>
											<th><span class="TC">幣別</span></th>
											<th>起息日</th>
											<th><span class="TC">到期日</span></th>
											<th>期別</th>
											<th><span class="TC">存單利率(%)</span></th>
											<th>自動展期</th>
											<th>展期次數</th>
											<th>存單金額(原幣)</th>
											<th><span class="TC">存單金額(折台幣)</span></th>
											<th>質借註記</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="sav in savingP51P53">
											<td>{{ sav.ynRecNum }}</td>
											<td />
											<td>{{ sav.accNo }}</td>
											<td>{{ sav.tranBranName }}</td>
											<td>{{ sav.curCode }}</td>
											<td>{{ $filters.formatDate(sav.valueDt) }}</td>
											<td>{{ $filters.formatDate(sav.expireDt) }}</td>
											<td class="num">
												{{ sav.tenor }}
											</td>
											<td class="num">
												{{ $filters.formatPct(sav.intRate) }}%
											</td>
											<td>{{ sav.autoRenewYn }}</td>
											<td class="num">
												{{ sav.preDefineTimes }}
											</td>
											<td class="num">
												{{ $filters.formatAmt(sav.balFc) }}
											</td>
											<td class="num">
												{{ $filters.formatAmt(sav.balLc) }}
											</td>
											<td>{{ sav.cdsBorrow }}</td>
										</tr>
										<tr class="tr-sum">
											<td colspan="11" class="text-right">
												新臺幣 總計
											</td>
											<td class="text-right">
&nbsp;
											</td>
											<td class="num">
												{{ $filters.formatAmt($filters.sumTotal(savingP51P53, 'balLc')) }}
											</td>
											<td>&nbsp;</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="tx-note">
							此功能畫面外幣資料:折台匯率依前一日結帳匯率換算
						</div>
					</div>
				</div>
			</div>
			<div id="SectionB" class="tab-pane fade">
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>加入同業投資部位餘額</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseadd" />
					</div>
					<div id="collapseadd" class="collapse show">
						<div class="table-responsive">
							<table class="bih-table table table-RWD">
								<thead>
									<tr>
										<th> 銀行名稱</th>
										<th> 商品名稱</th>
										<th> 幣別</th>
										<th> 金額</th>
										<th> 起始日</th>
										<th>到期日</th>
										<th>是否警示</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td data-th="銀行名稱">
											<input
												id=""
												type="text"
												name=""
												size="8"
												maxlength="11"
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="商品名稱">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="幣別">
											<select name="select" class="form-select">
												<option>--</option>
												<option>台幣</option>
												<option>美元</option>
												<option>歐元</option>
												<option>澳幣</option>
												<option>新加坡幣</option>
												<option>日圓</option>
											</select>
										</td>
										<td data-th="金額">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="起始日">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="到期日">
											<input
												id=""
												type="text"
												name=""
												size=""
												maxlength=""
												value=""
												class="form-control num"
											>
										</td>
										<td data-th="是否警示">
											<input
												class="form-check-input"
												name="cpasummary.active_yn"
												type="checkbox"
												value="Y"
											>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
				<div id="button1" class="num">
					<input
						class="btn btn-primary JQdata-hide"
						type="button"
						value="儲存"
						name="bsave"
					>
				</div>
				<div id="button2" class="num" style="display:none">
					<input
						class="btn btn-primary JQdata-show"
						type="button"
						value="修改"
						name="cancel"
						style="display:none"
						onclick="window.location='cpsaving_other_bank.htm'"
					>
					<input
						class="btn btn-primary JQdata-show"
						type="button"
						value="取消修改"
						name="cancel"
						style="display:none"
						onclick="window.location='cpsaving_other_bank.htm'"
					>
				</div>
				<div class="card card-form card-collapse mb-3">
					<div class="card-header">
						<h4>同業投資部位餘額</h4>
						<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapserest" />
					</div>
					<div id="collapserest" class="collapse show">
						<div class="table-responsive">
							<table class="bih-table table table-RWD">
								<thead>
									<tr>
										<th><span class="TC">銀行名稱</span></th>
										<th><span class="TC">商品名稱</span></th>
										<th><span class="TC">幣別</span></th>
										<th><span class="TC">金額</span></th>
										<th><span class="TC">起始日</span></th>
										<th><span class="TC">到期日</span></th>
										<th><span class="TC">是否警示</span></th>
										<th>執行</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td>HSBC</td>
										<td>HSBC Direct TD</td>
										<td>台幣</td>
										<td class="num">
											200,000
										</td>
										<td>2008/02/10</td>
										<td>2009/02/10</td>
										<td class="text-alignCenter">
											<span class="TC">是</span>
										</td>
										<td class="text-alignCenter">
											<button
												id="edit"
												type="button"
												class="btn btn-info btn-icon"
												data-bs-toggle="tooltip"
												title=""
												data-bs-original-title="編輯"
												aria-label="編輯"
											>
												<i class="fa-solid fa-pen" />
											</button>
											<button
												type="button"
												class="btn btn-danger btn-icon"
												data-bs-toggle="tooltip"
												data-bs-original-title="刪除"
											>
												<i class="fa-solid fa-trash" />
											</button>
										</td>
									</tr>
									<tr>
										<td><span class="TC">中國信託</span></td>
										<td><span class="TC">外幣優惠存款</span></td>
										<td><span class="TC">美元</span></td>
										<td class="num">
											400,000
										</td>
										<td>2008/05/22</td>
										<td>2008/08/22</td>
										<td class="text-alignCenter">
											<span class="TC">是</span>
										</td>
										<td class="text-alignCenter">
											<button
												type="button"
												class="btn btn-info btn-icon"
												data-bs-toggle="tooltip"
												title=""
												data-bs-original-title="編輯"
												aria-label="編輯"
											>
												<i class="fa-solid fa-pen" />
											</button>
											<button
												type="button"
												class="btn btn-danger btn-icon"
												data-bs-toggle="tooltip"
												data-bs-original-title="刪除"
											>
												<i class="fa-solid fa-trash" />
											</button>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			<div id="SectionC" class="tab-pane fade" />
		</div>
	</div>
</template>
<script>
import vueCusAssetsPieChart from '../pieChart.vue';
export default {
	components: {
		vueCusAssetsPieChart
	},
	props: {
		cusCode: String
	},
	data: function () {
		return {
			savingTypeChartData: [],
			savingCurChartData: [],
			savingAvg: {},
			savingExpire: [],
			savingP50P54: [],
			savingP51P53: [],
			savingP52: []
		};
	},
	watch: {
		cusCode: function () {
			this.initData();
		}
	},
	mounted: function () {
		this.initData();
	},
	methods: {
		async initData() {
			this.getSavingTypeChartData();
			this.getSavingCurChartData();
			const savingAvgRes = await this.$api.getCusSavingAvgApi({
				cusCode: this.cusCode
			});
			this.savingAvg = savingAvgRes.data || {};

			const savingExpRes = await this.$api.getCusSavingExpireApi({
				cusCode: this.cusCode
			});
			this.savingExpire = savingExpRes.data;
			this.savingP50P54 = await this.getSavingByProtype(['P50', 'P54']);
			this.savingP51P53 = await this.getSavingByProtype(['P51', 'P53']);
			this.savingP52 = await this.getSavingByProtype(['P52']);
		},
		async getSavingTypeChartData() {
			const result = await this.$api.getCusSavingGroupByProtypeApi({
				cusCode: this.cusCode
			});
			this.savingTypeChartData = result.data;
			this.savingTypeChartData.forEach((sav) => {
				sav.category = sav.protypeName;
				sav.value = sav.balLc;
			});
		},
		async getSavingCurChartData() {
			const result = await this.$api.getCusSavingGroupByCurApi({
				cusCode: this.cusCode
			});
			this.savingCurChartData = result.data;
			this.savingCurChartData.forEach((sav) => {
				sav.category = sav.curName;
				sav.value = sav.balLc;
			});
		},
		async getSavingByProtype(protypeCodes) {
			const ret = await this.$api.getCusSavingByProtypeCodes({
				cusCode: this.cusCode,
				protypeCodes: protypeCodes.join()
			});
			return ret.data;
		}
	}
};
</script>
