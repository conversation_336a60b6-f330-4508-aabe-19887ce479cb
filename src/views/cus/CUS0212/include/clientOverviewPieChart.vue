<template>
	<div :id="chartId" />
</template>
<script>
import * as am5 from '@amcharts/amcharts5';
import * as am5percent from '@amcharts/amcharts5/percent';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { toRaw } from 'vue';
export default {
	props: {
		chartId: String,
		propChartData: Array
	},
	data: function () {
		return {
			am5Obj: {}
		};
	},
	computed: {},
	watch: {},
	mounted: function () {
		this.$nextTick(function () {
			this.initChart();
		});
	},
	beforeUnmount: function () {
		this.destroyChart();
	},
	methods: {
		initChart: function () {
			const self = this;

			const { am5Obj } = self;
			let firstLoad = false;
			// 透過 toRaw 解決因為 vue data proxy 導致跟 amcharts 衝突
			let { root, chart, series } = toRaw(am5Obj);
			if (!root) {
				firstLoad = true;
				// Create root element
				// https://www.amcharts.com/docs/v5/getting-started/#Root_element
				root = am5.Root.new(self.chartId);
				root._logo.dispose();

				// Set themes
				// https://www.amcharts.com/docs/v5/concepts/themes/
				root.setThemes([am5themes_Animated.new(root)]);

				// Create chart
				// https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/
				chart = root.container.children.push(
					am5percent.PieChart.new(root, {
						endAngle: 270
					})
				);

				// Create series
				// https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/#Series
				series = chart.series.push(
					am5percent.PieSeries.new(root, {
						valueField: 'value',
						categoryField: 'category',
						endAngle: 270,
						alignLabels: false
					})
				);

				series.states.create('hidden', {
					endAngle: -90
				});
				series
					.get('colors')
					.set('colors', [am5.color('#68c89e'), am5.color('#3aa0cf'), am5.color('#ff8037'), am5.color('#edbb7f'), am5.color('#56b9af')]);
			}
			else {
				chart.series.clear();
			}

			if (firstLoad) {
				Object.assign(am5Obj, { root, chart, series });
			}

			// Set data
			// https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/#Setting_data
			series.data.setAll(self.propChartData);

			series.labels.template.setAll({
				fontSize: 14,
				text: '{category}',
				textType: 'circular',
				inside: true,
				radius: 10,
				fill: am5.color(0x000000)
			});

			if (firstLoad) {
				series.appear(1000, 100);
				root._logo.dispose();
			}
		},
		destroyChart: function () {
			const { am5Obj } = this;
			const { root } = toRaw(am5Obj);
			if (root) {
				root.dispose();
			}
		}
	}
};
</script>
