<template>
	<div class="clientDB" v-bind="$attrs">
		<div class="row g-3">
			<!-- 基本資料 -->
			<div class="col-lg-5 height-size">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('cus.basicInfo') }}</h4>
					</div>
					<div class="table-scroll ht-300">
						<table class="table">
							<thead class="thead-primary tx-spacing-2">
								<th colspan="2">
									{{ $t('cus.customerLevel') }}：{{ customer.idnEntityTypeName }}({{ customer.graName }}) / {{ $t('cus.recentContact') }}：{{
										$filters.formatDate(customer.contactDate)
									}}
								</th>
							</thead>
							<tbody>
								<tr>
									<th width="30%">
										{{ $t('cus.contactPhone') }}
									</th>
									<td>({{ $t('cus.company') }}) {{ customer.phoneO }} ({{ $t('cus.mobile') }}) {{ customer.phoneM }} ({{ $t('cus.home') }}) {{ customer.phoneH }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.kycRiskLevel') }}({{ $t('cus.effectiveDate') }})</th>
									<td>{{ customer.rankName }} ({{ $filters.formatDate(customer.nextRankQueDt) }})</td>
								</tr>
								<tr>
									<th>{{ $t('cus.professionalInvestor') }}({{ $t('cus.expiryDate') }})</th>
									<td>{{ customer.piYn }} ({{ $filters.formatDate(customer.piDueDt) }})</td>
								</tr>
								<tr>
									<th>{{ $t('cus.totalAccountAssets') }} ({{ $t('cus.aua') }})</th>
									<td>{{ $filters.formatAmt(customer.mktAmtAua) }} ({{ $t('cus.equivalentTwd') }})</td>
								</tr>
								<tr>
									<th>{{ $t('cus.investmentMarketValue') }}({{ $t('cus.aum') }})</th>
									<td>{{ $filters.formatAmt(customer.mktAmtAum) }} ({{ $t('cus.equivalentTwd') }})</td>
								</tr>
								<tr>
									<th>{{ $t('cus.contactAddress') }}</th>
									<td>{{ customer.addC }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.registeredAddress') }}</th>
									<td>{{ customer.addH }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.canMarketingContact') }}</th>
									<td>{{ customer.rejectPhoneYn }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.recentTransactionDate') }}</th>
									<td>{{ $filters.formatDate(customer.transDate) }}</td>
								</tr>
								<tr>
									<th>{{ $t('cus.managingAdvisorBranch') }}</th>
									<td>{{ customer.userName }}/{{ customer.branName }}</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>

			<!-- 工作事項 -->
			<div class="col-lg-7 height-size">
				<div class="card card-table card-tabs">
					<div class="card-header">
						<h4>{{ $t('cus.workItems') }}</h4>
						<ul v-if="tdListsData && tdListsData.length > 0" class="nav nav-pills card-header-pills">
							<li>
								<a href="#tab-w1" class="nav-link active" data-bs-toggle="pill">{{ $t('cus.recentTwoMonthEvents') }}({{ tdListsData[0].totalCount }})</a>
							</li>
						</ul>
					</div>
					<div class="tab-content p-0">
						<div id="tab-w1" class="tab-pane fade show active">
							<div class="table-scroll ht-300">
								<table class="table table-striped table-hover table-RWD">
									<thead>
										<tr>
											<th>{{ $t('cus.category') }}</th>
											<th>{{ $t('cus.serviceRecord') }}</th>
											<th>{{ $t('cus.event') }}</th>
											<th>{{ $t('cus.description') }}</th>
											<th class="text-center">
												{{ $t('cus.dueDate') }}
											</th>
											<th class="text-center">
												{{ $t('cus.status') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in tdListsData">
											<td :data-th="$t('cus.category')">
												{{ item.tdcat1Name }}
											</td>
											<td :data-th="$t('cus.serviceRecord')" class="text-center">
												<Button
													color="action"
													icon
													:title="$t('cus.serviceRecord')"
													@click="newTask(1, cusCode)"
												>
													<i class="far fa-comments" />
												</Button>
											</td>
											<td :data-th="$t('cus.event')">
												{{ item.itemName }}
											</td>
											<td :data-th="$t('cus.description')">
												{{ item.content }}
											</td>
											<td :data-th="$t('cus.dueDate')" class="text-center">
												{{ $filters.formatDate(item.expireDt) }}
											</td>
											<td :data-th="$t('cus.status')" class="text-center">
												<span class="badge badge-danger wd-60">{{ item.statusName }} </span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="tab-w2" class="tab-pane fade">
							<div class="table-scroll ht-300">
								<table class="table table-striped table-hover table-RWD">
									<thead>
										<tr>
											<th width="15%">
												{{ $t('cus.customer') }}
											</th>
											<th width="8%">
												{{ $t('cus.startDate') }}
											</th>
											<th width="8%">
												{{ $t('cus.endDate') }}
											</th>
											<th width="8%">
												{{ $t('cus.category') }}
											</th>
											<th width="10%">
												{{ $t('cus.description') }}
											</th>
											<th width="10%">
												{{ $t('cus.status') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td :data-th="$t('cus.customer')">
												<div class="input-group">
													<input
														name="cusIdentity"
														class="form-control"
														type="text"
														size="15"
														maxlength="20"
														value=""
													>
													<button type="button" class="btn btn-dark btn-icon">
														<i class="bi bi-search" />
													</button>
												</div>
											</td>
											<td :data-th="$t('cus.startDate')">
												<input
													id="beginDate"
													type="date"
													name="beginDate"
													value=""
													class="form-control"
												>
											</td>
											<td :data-th="$t('cus.endDate')">
												<input
													id="endDate"
													type="date"
													name="beginDate"
													value=""
													class="form-control"
												>
											</td>
											<td :data-th="$t('cus.category')">
												<select name="Genre" class="form-select">
													<option value="0">
														{{ $t('cus.phoneCall') }}
													</option>
													<option value="1">
														{{ $t('cus.faceToFace') }}
													</option>
													<option value="2">
														{{ $t('cus.eventCategory') }}
													</option>
													<option value="3">
														{{ $t('cus.product') }}
													</option>
													<option value="4">
														{{ $t('cus.companyCategory') }}
													</option>
													<option value="5">
														{{ $t('cus.accompanyVisit') }}
													</option>
												</select>
											</td>

											<td :data-th="$t('cus.description')">
												<input
													name="cusIdentity"
													class="form-control"
													type="text"
													size="15"
													maxlength="20"
													value=""
												>
											</td>
											<td :data-th="$t('cus.status')">
												<select name="Status" class="form-select">
													<option value="0">
														{{ $t('cus.unprocessed') }}
													</option>
													<option value="1">
														{{ $t('cus.cannotContact') }}
													</option>
													<option value="2">
														{{ $t('cus.completed') }}
													</option>
												</select>
											</td>
										</tr>
									</tbody>
								</table>
								<div class="btn-end">
									<button class="btn btn-primary btn-search">
										{{ $t('cus.confirm') }}
									</button>
								</div>
							</div>
						</div>

						<div id="tab-w3" class="tab-pane fade">
							<div class="table-scroll ht-300" />
						</div>
					</div>
				</div>
			</div>

			<!-- <div class="col-lg-4 height-size2">
					<div class="card card-table">
						<div class="card-header">
							<h4> 資產分布 </h4>
						</div>
						 <div class="tab-content">
							<div id="tab-Inv-1" class="tab-pane fade show active">
								<div class="row align-items-center">
									<div class="col-xl-12 text-center">
										<div id="All-container" class="chart-size"></div>
									</div>
								</div>
							</div>
							<div id="tab-Inv-2" class="tab-pane fade">
								<img src="../../../images/AllContPFReview.gif" class="chart-size" />
							</div>
						</div>
					</div>
				</div> -->

			<div class="col-lg-12">
				<!-- 資產現況 -->
				<div class="card card-table card-tabs">
					<div class="card-header">
						<h4>{{ $t('cus.assetStatus') }}</h4>
						<ul class="nav nav-pills card-header-pills">
							<li>
								<a href="#tab-Inv1" class="nav-link active" data-bs-toggle="pill">{{ $t('cus.investmentProductAllocation') }} </a>
							</li>
							<!--<li>
									<a href="#tab-Inv-2" class="nav-link" data-bs-toggle="pill">投資組合配置</a>
								</li>-->
							<li>
								<a href="#tab-Inv3" class="nav-link" data-bs-toggle="pill">{{ $t('cus.loanAllocation') }}</a>
							</li>
						</ul>
					</div>
					<div class="tab-content">
						<div id="tab-Inv1" class="tab-pane fade show active">
							<div class="row align-items-center">
								<div class="col-xl-4 text-center">
									<vue-client-overview-pie-chart
										v-if="showFinanceChart"
										class="dbchart-container"
										:chart-id="financeChartId"
										:prop-chart-data="financeChartData"
									/>
								</div>
								<div class="col-xl-8">
									<div class="table-responsive">
										<table class="table table-striped table-RWD table-hover">
											<thead>
												<tr>
													<th width="18%">
														<span>{{ $t('cus.productCategory') }}</span>
													</th>
													<th width="18%">
														<span>{{ $t('cus.allocationRatio') }}</span>
													</th>
													<th width="23%">
														<span>{{ $t('cus.originalInvestmentAmount') }}</span>
													</th>
													<th width="23%">
														<span>{{ $t('cus.marketValue') }}</span>
													</th>
													<th width="18%">
														<span>{{ $t('cus.returnRate') }}</span>
													</th>
												</tr>
											</thead>
											<tbody v-if="assetLoansData && assetLoansData.cusAssetAmountMergeList">
												<tr v-for="item in assetLoansData.cusAssetAmountMergeList">
													<td data-th="商品類別">
														<template v-if="!hasAuth">
															{{ item.pfcatName }}
														</template>
														<a v-if="hasAuth" href="#" @click.prevent="changeTab('M20-055', item.pfcatCode)">{{
															item.pfcatName
														}}</a>
													</td>
													<td :data-th="$t('cus.allocationRatio')" class="text-end">
														{{ $filters.formatPct(item.alcRate) }}%
													</td>
													<td :data-th="$t('cus.originalInvestmentAmount')" class="text-end">
														{{ formatValue(item.sumInvAmtLc, 'I') }}
													</td>
													<td :data-th="$t('cus.marketValue')" class="text-end">
														{{ formatValue(item.sumMktAmtLc, 'I') }}
													</td>
													<td :data-th="$t('cus.returnRate')" class="text-end">
														{{ $filters.formatPct(item.rtnRate) }}%
													</td>
												</tr>
											</tbody>
											<tfoot
												v-if="
													assetLoansData &&
														assetLoansData.cusAssetAmountMergeList &&
														assetLoansData.cusAssetAmountMergeList.length > 0
												"
											>
												<tr class="tx-sum bg-total">
													<td data-th="商品類別">
														{{ $t('cus.total') }}
													</td>
													<td :data-th="$t('cus.allocationRatio')" class="text-end">
														100.00%
													</td>
													<td :data-th="$t('cus.originalInvestmentAmount')" class="text-end">
														{{ formatValue(assetLoansData.sumInvAmtLc, 'I') }}
													</td>
													<td :data-th="$t('cus.marketValue')" class="text-end">
														{{ formatValue(assetLoansData.sumMktAmtLc, 'I') }}
													</td>
													<td :data-th="$t('cus.returnRate')" class="text-end">
														{{ $filters.formatPct(assetLoansData.rtnRate) }}%
													</td>
													<!--<td data-th="日變動" class="text-end">--</td>
														<td data-th="週變動" class="text-end">--</td>-->
												</tr>
											</tfoot>
										</table>
									</div>
								</div>
							</div>
						</div>
						<div id="tab-Inv3" class="tab-pane fade">
							<div class="row align-items-center">
								<div class="col-xl-4 text-center">
									<vue-client-overview-pie-chart
										v-if="showLoanChart"
										class="dbchart-container"
										:chart-id="loanChartId"
										:prop-chart-data="loanChartData"
									/>
								</div>
								<div class="col-xl-8">
									<div class="table-responsive">
										<table class="table table-striped table-RWD table-hover">
											<thead>
												<tr>
													<th width="10%">
														<span>{{ $t('cus.type') }}</span>
													</th>
													<th width="18%">
														<span>{{ $t('cus.ratio') }}</span>
													</th>
													<th width="18%">
														<span>{{ $t('cus.initialLoanAmountTwd') }}</span>
													</th>
													<th width="18%">
														<span>{{ $t('cus.initialLoanAmount') }}</span>
													</th>
													<th width="18%">
														<span>{{ $t('cus.currentLoanBalance') }}</span>
													</th>
													<th width="18%">
														<span>{{ $t('cus.repaidAmount') }}</span>
													</th>
												</tr>
											</thead>
											<tbody v-if="assetLoansData && assetLoansData.cusLoansMergeRespList">
												<tr v-for="item in assetLoansData.cusLoansMergeRespList">
													<td data-th="商品類別">
														<template v-if="!hasAuth">
															{{ item.curName }}
														</template>
														<a v-if="hasAuth" href="#" @click.prevent="changeTab('M20-055', 'ALL')">{{ item.curName }}</a>
													</td>
													<td :data-th="$t('cus.ratio')" class="text-end">
														{{ $filters.formatPct(item.alcRate) }}%
													</td>
													<td :data-th="$t('cus.initialLoanAmountTwd')" class="text-end">
														{{ formatValue(item.sumUmtLc, 'I') }}
													</td>
													<td :data-th="$t('cus.initialLoanAmount')" class="text-end">
														{{ formatValue(item.sumUmtFc, 'I') }}
													</td>
													<td :data-th="$t('cus.currentLoanBalance')" class="text-end">
														{{ formatValue(item.sumBalFc, 'I') }}
													</td>
													<td :data-th="$t('cus.repaidAmount')" class="text-end">
														{{ formatValue(item.sumUseBalFc, 'I') }}
													</td>
												</tr>
											</tbody>
											<tfoot
												v-if="
													assetLoansData &&
														assetLoansData.cusLoansMergeRespList &&
														assetLoansData.cusLoansMergeRespList.length > 0
												"
											>
												<tr class="tx-sum bg-total">
													<td data-th="商品類別">
														{{ $t('cus.total') }}
													</td>
													<td :data-th="$t('cus.allocationRatio')" class="text-end">
														100.00%
													</td>
													<td :data-th="$t('cus.initialLoanAmountTwd')" class="text-end">
														{{ formatValue(assetLoansData.sumUmtLc, 'I') }}
													</td>
													<td :data-th="$t('cus.initialLoanAmount')" class="text-end">
														{{ formatValue(assetLoansData.sumUmtFc, 'I') }}
													</td>
													<td :data-th="$t('cus.currentLoanBalance')" class="text-end">
														{{ formatValue(assetLoansData.sumBalFc, 'I') }}
													</td>
													<td :data-th="$t('cus.repaidAmount')" class="text-end">
														{{ formatValue(assetLoansData.sumUseBalFc, 'I') }}
													</td>
												</tr>
											</tfoot>
										</table>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 近12個月資產走勢 -->
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('cus.recent12MonthAssetTrend') }}</h4>
					</div>
					<h5 align="right">
						{{ $t('cus.assetBalanceNtdThousand') }}
					</h5>
					<div class="table-responsive">
						<!-- 近12個月資產走勢 圖表 -->
						<vue-asset-trend-chart ref="assetTrendChartRef" :chart-id="assetTrendId" />
					</div>
				</div>
			</div>
		</div>
	</div>
	<vue-wob-new-task-modal ref="newTaskModal" />
</template>
<script>
import vueAssetTrendChart from './assetTrendChart.vue';
import vueClientOverviewPieChart from './clientOverviewPieChart.vue';
import vueWobNewTaskModal from '@/views/wob/WOB0400/include/newTaskModalForHeader.vue';
import numeral from 'numeral';
export default {
	components: {
		vueAssetTrendChart,
		vueClientOverviewPieChart,
		vueWobNewTaskModal
	},
	props: {
		cusCode: String,
		hasAuth: Boolean,
		customer: Object,
		setMenuCode: Function
	},
	data: function () {
		return {
			assetTrendId: 'assetTrendId', // 近12個月資產走勢圖id
			assetTrendData: [],
			financeChartId: 'financeChartId',
			loanChartId: 'loanChartId',
			financeChartData: [],
			loanChartData: [],
			tdListsData: [], // 工作項目
			assetLoansData: null, // 資產現況和貸款配置
			showFinanceChart: false,
			showLoanChart: false
		};
	},
	watch: {
		cusCode: function () {
			this.near12MonthassetTrend();
			this.getTdListsData();
			this.getassetLoansData();
		}
	},
	mounted: function () {
		const self = this;
		self.near12MonthassetTrend(); // 近12個月資產走勢資料
		self.getTdListsData(); // 工作項目
		self.getassetLoansData(); //
	},
	methods: {
		async near12MonthassetTrend() {
			// 近12個月資產走勢資料
			const self = this;
			const ret = await self.$api.getAssetTrendApi({
				cusCode: self.cusCode
			});
			for (let i = 0; i < ret.data.length; i++) {
				ret.data[i].datas.forEach((e) => {
					const dataDtString = String(e.dataDt).padStart(6, '0');
					const year = parseInt(dataDtString.substring(0, 4), 10);
					const month = parseInt(dataDtString.substring(4, 6), 10) - 1; // 月份從0開始
					e.date = new Date(year, month, 1).getTime();
				});
			}
			self.assetTrendData = ret.data;
			self.$refs.assetTrendChartRef.initChart(ret.data);
		},
		async getTdListsData() {
			const self = this;
			const ret = await self.$api.getTdListsDataApi({
				cusCode: self.cusCode
			});
			self.tdListsData = ret.data;
		},
		async getassetLoansData() {
			const self = this;
			self.financeChartData = [];
			self.loanChartData = [];
			self.showFinanceChart = false;
			self.showLoanChart = false;
			const ret = await self.$api.getAssetLoansDataApi({
				cusCode: self.cusCode
			});
			self.assetLoansData = ret.data;
			if (self.assetLoansData && self.assetLoansData.cusAssetAmountMergeList && self.assetLoansData.cusAssetAmountMergeList.length > 0) {
				self.assetLoansData.cusAssetAmountMergeList.forEach((e) => {
					self.financeChartData.push({
						value: e.alcRate,
						category: e.pfcatName
					});
				});
				self.showFinanceChart = true;
			}

			if (self.assetLoansData && self.assetLoansData.cusLoansMergeRespList && self.assetLoansData.cusLoansMergeRespList.length > 0) {
				self.assetLoansData.cusLoansMergeRespList.forEach((e) => {
					self.loanChartData.push({
						value: e.alcRate,
						category: e.curName
					});
				});
				self.showLoanChart = true;
			}
		},
		changeTab(menuCode, code) {
			const self = this;
			self.setMenuCode(menuCode, code);
		},
		formatValue(value, type) {
			switch (type) {
				case 'I': // 千分位整數格式
					return this.formatInteger(value);
				case 'D': // 日期格式
					return this.formatDate(value);
				case 'F': // 浮點數轉為千分位整數格式
					return this.formatFloatAsInteger(value);
				case 'S': // 字串格式
					return value;
				// 其他類型的格式化處理可以在這裡繼續擴展
				default:
					return value; // 預設返回原始值
			}
		},
		formatInteger(value) {
			const numericValue = Number(value);
			return isNaN(numericValue) ? 0 : numeral(numericValue).format('0,0');
		},
		formatDate(value) {
			const date = new Date(value);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`; // 手動格式化為 'YYYY-MM-DD'
		},
		formatFloatAsInteger(value) {
			const numericValue = Number(value);
			return isNaN(numericValue) ? 0 : parseFloat(numericValue).toFixed(0); // 或者其他浮點數處理方式
		},
		newTask: function (sectionCode, cusCode) {
			this.$refs.newTaskModal.getSelectVisitPurpose();
			this.$refs.newTaskModal.getSelectIdentity();
			this.$refs.newTaskModal.getSelectVisitType();
			this.$refs.newTaskModal.getSelectPlaceType();
			this.$refs.newTaskModal.getSelectAttBranches();
			this.$refs.newTaskModal.getSelectContactPurpose();
			this.$refs.newTaskModal.getSelectContactType();
			this.$refs.newTaskModal.getSelectReuseWordSelf();
			this.$refs.newTaskModal.show(sectionCode);
			this.$refs.newTaskModal.setSelectCusCode(cusCode, cusCode.slice(0, -1));
			this.$refs.newTaskModal.getCusInfo();
		}
	}
};
</script>
