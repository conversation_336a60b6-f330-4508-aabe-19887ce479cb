<template>
	<!--filemgr-sidebar start-->
	<div class="filemgr-wrapper filemgr-wrapper-two">
		<span id="filemgrMenuclose"><i class="bi bi-arrow-left-square-fill" @click="hideMenu()" /></span>
		<vue-cus-info-sidebar
			v-if="customer"
			:cus-code="cusCode"
			:page-code="pageCode"
			:has-auth="hasAuth"
			:customer="customer"
		/>
		<div class="filemgr-content">
			<vue-cus-client-overview
				v-if="customer"
				:cus-code="cusCode"
				:has-auth="hasAuth"
				:customer="customer"
			/>
		</div>
	</div>
	<!-- filemgr-content -->
</template>
<script>
import vueCusClientOverview from './include/clientOverview.vue';
import vueCusInfoSidebar from '../include/cusInfoSidebar.vue';
import _ from 'lodash';
export default {
	components: {
		vueCusClientOverview,
		vueCusInfoSidebar
	},
	data: function () {
		return {
			pageCode: 2,
			cusCode: null,
			customer: null,
			hasAuth: false,
			pbStatusName: null
		};
	},
	mounted: function () {
		const self = this;
		if (self.$route.params.cusCode) {
			self.cusCode = self.$route.params.cusCode;
		}
		self.checkAuthAndGetCustomer();
	},
	methods: {
		hideMenu() {
			this.$router.back();
		},
		checkAuthAndGetCustomer: async function () {
			const self = this;
			const retAuth = await self.$api.checkCusAuthApi({
				cusCode: self.cusCode
			});
			if (!_.isNil(retAuth.data) && retAuth.data.authYn != 'N') {
				const ret = await self.$api.getCusInfoApi({
					cusCode: self.cusCode
				});
				if (!ret.data || ret.data.length == 0) {
					self.$bi.confirm(self.$t('cus.customerDoesNotExist'), {
						event: {
							confirmOk: function () {
								location.href = '/';
							},
							confirmCancel: function () {
								location.href = '/';
							}
						},
						button: {
							confirmOk: self.$t('cus.confirm')
						}
					});
				}
				else {
					if (retAuth.data.authYn == 'Y') {
						self.hasAuth = true;
					}
					else if (retAuth.data.authYn == 'E') {
						self.hasAuth = false;
					}
					self.customer = ret.data;
					self.customer.pbStatusName = retAuth.data.pbStatusName;
					self.customer.pbStatus = retAuth.data.authYn;
				}
			}
			else {
				self.hasAuth = false;
				self.$bi.confirm(self.$t('cus.customerNotAuthorizedForViewing'), {
					event: {
						confirmOk: function () {
							location.href = '/';
						},
						confirmCancel: function () {
							location.href = '/';
						}
					},
					button: {
						confirmOk: self.$t('cus.confirm')
					}
				});
			}
		}
	}
};
</script>
