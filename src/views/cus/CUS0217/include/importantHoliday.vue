<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<div class="card card-form">
				<div class="card-header">
					<h4>{{ $t('cus.importantHolidaySettings') }}</h4>
					<span class="tx-square-bracket">{{ $t('cus.requiredField') }}</span>
				</div>
				<div class="card-body">
					<vue-form ref="importantHoliday">
						<div class="form-row">
							<FormField
								v-slot="{field, meta}"
								v-model="memoryDate"
								name="memoryDate"
								rules="required"
								:label="$t('cus.date')"
								class="col-12 col-xxl-4"
								required
							>
								<DateInput v-bind="field" :invalid="!meta.valid && meta.validated" />
							</FormField>
							<FormField
								v-slot="{field, meta}"
								v-model="note"
								name="note"
								class="col-12 col-xxl-8"
								:label="$t('cus.description')"
							>
								<Input
									v-bind="field"
									maxlength="100"
									type="text"
									:invalid="!meta.valid && meta.validated"
								/>
							</FormField>
							<FormField
								v-slot="{field, meta}"
								v-model="remindYn"
								name="remindYn"
								:label="$t('cus.enableReminder')"
								rules="required"
								class="col-12 col-xl-4"
								required
							>
								<RadioGroup
									v-bind="field"
									:invalid="!meta.valid && meta.validated"
									inline
									:options="[
										{value: 'Y', label: $t('cus.yes')},
										{value: 'N', label: $t('cus.no')},
									]"
								/>
							</FormField>
							<FormField
								v-if="remindYn === 'Y'"
								v-slot="{field, meta}"
								v-model="remindDays"
								name="remindDays"
								:label="$t('cus.reminderDaysBeforeExpiry')"
								:rules="{ remindDays: true, integer: true, min_value: 0 }"
								class="col-12 col-xl-8"
								required
							>
								<InputNumber maxlength="4" v-bind="field" :invalid="!meta.valid && meta.validated" />
							</FormField>
						</div>
						<div class="form-footer">
							<Button v-if="id == null" :label="$t('cus.save')" @click="insertMemoryDates()" />
							<template v-else>
								<Button :label="$t('cus.modify')" @click="updateMemoryDates()" />
								<Button :label="$t('cus.cancelModification')" @click="cancel()" />
							</template>
						</div>
					</vue-form>
				</div>
			</div>
			<DataTable :columns :rows="memoryDates" :title="$t('cus.importantHolidayList')">
				<template #body-cell-remindYn="{item}">
					<span v-if="item.remindYn">
						{{ item.remindYn == 'Y' ? $t('cus.yes') : $t('cus.no') }}
					</span>
				</template>
				<template #body-cell-action="{item}">
					<template v-if="showButton" />
					<Button
						color="info"
						icon
						:title="$t('cus.edit')"
						@click="doUpdate(item)"
					>
						<i class="bi bi-pen" />
					</Button>
					<Button
						icon
						color="danger"
						:title="$t('cus.delete')"
						@click="deleteMemoryDate(item.id)"
					>
						<i class="bi bi-trash" />
					</Button>
				</template>
			</DataTable>
		</div>
	</div>
</template>
<script>
import { formatDate } from '@/utils/filter';
import { Form, defineRule } from 'vee-validate';

export default {
	components: {
		'vue-form': Form
	},
	props: {
		cusCode: null
	},
	data: function () {
		return {
			id: null,
			memoryDate: null,
			note: null,
			remindYn: null,
			remindDays: null,
			authYn: null,
			memoryDates: []
		};
	},
	computed: {
		showButton: function () {
			return this.authYn === 'Y';
		},
		columns() {
			return [
				{ label: this.$t('cus.date'), field: 'memoDate' },
				{ label: this.$t('cus.description'), headerClass: 'text-start', field: 'memoNote', bodyClass: 'text-start' },
				{ label: this.$t('cus.enableReminder'), name: 'remindYn' },
				{ label: this.$t('cus.reminderDaysBeforeCalendarExpiry'), field: 'remindDays' },
				{ label: this.$t('cus.execute'), headerClass: 'text-end', name: 'action', bodyClass: 'text-end' }
			];
		}

	},
	created: function () {
		const self = this;
		defineRule('remindDays', function () {
			if (self.remindYn === 'Y' && self.remindDays == null) {
				return self.$t('cus.needToFill');
			}
			else {
				return true;
			}
		});
	},
	mounted: function () {
		const self = this;
		self.chkCustomerAuth();
		self.getMemoryDates();
	},
	methods: {
		chkCustomerAuth: async function () {
			const self = this;
			const resp = await self.$api.chkCustomerAuthApi({
				cusCode: self.cusCode,
				progCode: 'ACUS_005'
			});
			self.authYn = resp.data.authYn;
		},
		getMemoryDates: async function () {
			const self = this;
			const ret = await self.$api.getMemoryDateApi({
				cusCode: self.cusCode
			});
			self.memoryDates = ret.data;
		},
		doUpdate: function (memoryDate) {
			const self = this;
			self.id = memoryDate.id;
			self.remindYn = memoryDate.remindYn;
			self.remindDays = memoryDate.remindDays;
			self.note = memoryDate.memoNote ?? null;
			self.memoryDate = formatDate(memoryDate.memoDate);
		},
		insertMemoryDates: async function () {
			const self = this;
			const importantHoliday = self.$refs.importantHoliday;

			const pass = await importantHoliday.validate();

			if (!pass.valid) return;
			await self.$api.postMemoryDateApi({
				cusCode: self.cusCode,
				dateDt: self.memoryDate,
				remindYn: self.remindYn,
				remindDays: self.remindYn === 'Y' ? self.remindDays : null,
				note: self.note
			});
			self.$bi.alert(self.$t('cus.addSuccess'));
			self.getMemoryDates();
		},
		updateMemoryDates: async function () {
			const self = this;
			const pass = await self.$refs.importantHoliday.validate();
			if (!pass.valid) return;
			await self.$api.updateMemoryDateApi({
				id: self.id,
				cusCode: self.cusCode,
				dateDt: self.memoryDate,
				remindYn: self.remindYn,
				remindDays: self.remindYn === 'Y' ? self.remindDays : null,
				note: self.note ?? null
			});
			self.$bi.alert(self.$t('cus.updateSuccess'));
			self.getMemoryDates();
		},
		deleteMemoryDate: async function (id) {
			const self = this;
			await self.$api.deleteMemoryDateApi({ id: id });
			self.$bi.alert(self.$t('cus.deleteSuccess'));
			self.getMemoryDates();
			if (self.id == id) {
				self.id = null;
				self.cancel();
			}
		},
		cancel: function () {
			const self = this;
			self.id = null;
			self.note = null;
			self.memoryDate = null;
			self.remindYn = null;
			self.remindDays = null;
			self.$refs.importantHoliday.resetForm();
		}
	}
};
</script>
