<template>
	<div class="card card-form-collapse">
		<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formSearch">
			<h4>查詢條件</h4>
			<span class="tx-square-bracket">為必填欄位</span>
		</div>
		<div class="card-body collapse show">
			<vue-form v-slot="{errors}" ref="queryForm">
				<div class="row g-3 align-items-end">
					<div class="col-md-4">
						<label class="form-label tx-require">查詢期間</label>
						<div class="input-group">
							<vue-field
								v-model="queryCriteria.startDt"
								type="date"
								label="查詢日期起"
								:max="queryCriteria.maxStartDt"
								rules="required"
								name="startDt"
								class="form-control"
							/>
							<span class="input-group-text">~</span>
							<vue-field
								v-model="queryCriteria.endDt"
								type="date"
								label="查詢日期迄"
								:min="queryCriteria.minEndDt"
								:max="queryCriteria.maxStartDt"
								rules="required"
								name="endDt"
								class="JQ-datepicker form-control"
							/>
						</div>
						<div style="height: 10px">
							<span v-show="errors.startDt" class="text-danger">{{ errors.startDt }}</span>
							<span v-show="errors.endDt" class="text-danger">{{ errors.endDt }}</span>
						</div>
					</div>

					<div class="col-md-4">
						<UserCondition
							ref="userCondition"
							:show="'bran'"
							:is-required="'bran'"
							@change-area="queryCriteria.minorCode = $event"
							@change-bran="queryCriteria.branCode = $event"
						/>
						<div style="height: 10px" />
					</div>

					<div class="col-md-4">
						<label class="form-label mb-0">移轉者</label>
						<div class="input-group">
							<template v-for="[codeValue, transfer] in queryCriteria.transferRadios" :key="codeValue">
								<div class="form-check form-check-inline">
									<input
										:id="`transfer-${codeValue}`"
										v-model="queryCriteria.transfer"
										type="radio"
										:value="codeValue"
										name="transfer"
										class="form-check-input"
									>
									<label :for="`transfer-${codeValue}`" class="form-check-label">{{ transfer.codeName
									}}</label>
								</div>
								<select
									v-if="transfer.select"
									v-model="transfer.select.value"
									:disabled="transfer.select.disabled"
									name="userCode"
									class="form-select"
								>
									<option value="">
										請選擇
									</option>
									<option
										v-for="[userCode, user] in transfer.select.options"
										:key="userCode"
										:value="userCode"
									>
										{{ user.userDisplay }}
									</option>
								</select>
							</template>
						</div>
						<div style="height: 10px">
							<span v-show="errors.transfer" class="text-danger">{{ errors.transfer }}</span>
							<span v-show="errors.userCode" class="text-danger">{{ errors.userCode }}</span>
						</div>
					</div>

					<div class="col-md-8">
						<label id="labcusName" class="form-label mb-0">異動類別</label><br>
						<div
							v-for="[codeValue, chgType] in queryCriteria.chgTypeCheckboxes"
							:key="codeValue"
							class="form-check form-check-inline"
						>
							<input
								:id="`queryChgType-${codeValue}`"
								v-model="queryCriteria.chgType"
								class="form-check-input"
								name="chgType"
								type="checkbox"
								:value="codeValue"
							>
							<label class="form-check-label" :for="`queryChgType-${codeValue}`">{{ chgType.codeName
							}}</label>
						</div>
						<a
							class="tx-link"
							href="#"
							data-bs-toggle="modal"
							data-bs-target="#ModalTransfer1"
						>
							<u>說明</u>
						</a>
						<div style="height: 10px">
							<span v-show="errors.chgType" class="text-danger">{{ errors.chgType }}</span>
						</div>
					</div>

					<div class="col-md-4">
						<label class="form-label tx-require mb-0">查詢類別</label>
						<div class="form-check-group">
							<div
								v-for="[codeValue, searchType] in queryCriteria.searchTypeRadios"
								class="form-check form-check-inline"
							>
								<input
									:id="`searchtype-${codeValue}`"
									v-model="queryCriteria.searchType"
									type="radio"
									:value="codeValue"
									name="searchtype"
									class="form-check-input"
								>
								<label class="form-check-label" :for="`searchtype-${codeValue}`">{{ searchType.codeName
								}}</label>
							</div>
						</div>
						<div style="height: 10px">
							<span v-show="errors.searchtype" class="text-danger">{{ errors.searchtype }}</span>
						</div>
					</div>

					<div class="col-md-12 text-end">
						<button
							type="button"
							class="btn btn-primary btn-search"
							@click="queryCriteria.onQuery()"
						>
							查詢
						</button>
					</div>
				</div>
			</vue-form>
		</div>
	</div>
	<div class="tx-note">
		<ol>
			<li>查詢期間每次可選範圍為兩年。</li>
			<li>只要原分行和新分行其中一個符合所選的查詢分行，則結果就會帶出來。</li>
		</ol>
	</div>
	<div v-if="isMainDisplayed" class="card card-table">
		<div class="card-header">
			<h4> 異動紀錄列表</h4>
			<vue-pagination
				:key="mainDisplay.queryCount"
				:pageable="mainDisplay.responseData"
				:goto-page="mainDisplay.gotoPage"
			/>
		</div>
		<div class="table-responsive">
			<table class="bih-table table table-RWD">
				<thead>
					<tr>
						<th width="9%">
							移轉日期
							<i
								class="bi"
								:class="mainDisplay.columns.STD_DT.iconClass"
								@click="mainDisplay.columns.STD_DT.onSort()"
							/>
						</th>
						<th width="10%">
							移轉前單位
							<i
								class="bi"
								:class="mainDisplay.columns.OUT_BRAN_CODE.iconClass"
								@click="mainDisplay.columns.OUT_BRAN_CODE.onSort()"
							/>
						</th>
						<th width="12%">
							移轉前業務人員
							<i
								class="bi"
								:class="mainDisplay.columns.OUT_USER_CODE.iconClass"
								@click="mainDisplay.columns.OUT_USER_CODE.onSort()"
							/>
						</th>
						<th width="10%">
							移轉後單位
							<i
								class="bi"
								:class="mainDisplay.columns.IN_BRAN_CODE.iconClass"
								@click="mainDisplay.columns.IN_BRAN_CODE.onSort()"
							/>
						</th>
						<th width="12%">
							移轉後業務人員
							<i
								class="bi"
								:class="mainDisplay.columns.IN_USER_CODE.iconClass"
								@click="mainDisplay.columns.IN_USER_CODE.onSort()"
							/>
						</th>
						<th width="9%">
							移轉者
							<i
								class="bi"
								:class="mainDisplay.columns.CREATE_BY.iconClass"
								@click="mainDisplay.columns.CREATE_BY.onSort()"
							/>
						</th>
						<th width="11%">
							異動類型
							<i
								class="bi"
								:class="mainDisplay.columns.CHG_TYPE_NAME.iconClass"
								@click="mainDisplay.columns.CHG_TYPE_NAME.onSort()"
							/>
						</th>
						<th width="7%">
							客戶數
							<i
								class="bi"
								:class="mainDisplay.columns.CUS_CHG_TOTAL.iconClass"
								@click="mainDisplay.columns.CUS_CHG_TOTAL.onSort()"
							/>
						</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="[chgId, cus] in mainDisplay.responseData.content" :key="chgId">
						<td data-th="移轉日期">
							{{ cus.stdDt }}
						</td>
						<td data-th="移轉前單位">
							{{ cus.outBranDisplay }}
						</td>
						<td data-th="移轉前業務人員">
							{{ cus.outUserDisplay }}
						</td>
						<td data-th="移轉後單位">
							{{ cus.inBranDisplay }}
						</td>
						<td data-th="移轉後業務人員">
							{{ cus.inUserDisplay }}
						</td>
						<td data-th="移轉者">
							{{ cus.createByDisplay }}
						</td>
						<td data-th="異動類型">
							{{ cus.chgTypeName }}
						</td>
						<td data-th="客戶數" class="num">
							<a href="#" @click.prevent="cus.onQuery()">{{ cus.cusChgTotal
							}}</a>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<div v-if="isDetailDisplayed" class="card card-table">
		<div class="card-header">
			<h4> 異動紀錄明細</h4>
			<vue-pagination
				:key="detailDisplay.queryCount"
				:pageable="detailDisplay.responseData"
				:goto-page="detailDisplay.gotoPage"
			/>
		</div>
		<div class="table-responsive">
			<table class="bih-table table table-RWD">
				<thead>
					<tr>
						<th width="9%">
							移轉日期
							<i
								class="bi"
								:class="detailDisplay.columns.STD_DT.iconClass"
								@click="detailDisplay.columns.STD_DT.onSort()"
							/>
						</th>
						<th width="10%">
							移轉前單位
							<i
								class="bi"
								:class="detailDisplay.columns.OUT_BRAN_CODE.iconClass"
								@click="detailDisplay.columns.OUT_BRAN_CODE.onSort()"
							/>
						</th>
						<th width="12%">
							移轉前業務人員
							<i
								class="bi"
								:class="detailDisplay.columns.OUT_USER_CODE.iconClass"
								@click="detailDisplay.columns.OUT_USER_CODE.onSort()"
							/>
						</th>
						<th width="10%">
							移轉後單位
							<i
								class="bi"
								:class="detailDisplay.columns.IN_BRAN_CODE.iconClass"
								@click="detailDisplay.columns.IN_BRAN_CODE.onSort()"
							/>
						</th>
						<th width="12%">
							移轉後業務人員
							<i
								class="bi"
								:class="detailDisplay.columns.IN_USER_CODE.iconClass"
								@click="detailDisplay.columns.IN_USER_CODE.onSort()"
							/>
						</th>
						<th width="7%">
							客戶ID/統編
							<i
								class="bi"
								:class="detailDisplay.columns.CUS_CODE.iconClass"
								@click="detailDisplay.columns.CUS_CODE.onSort()"
							/>
						</th>
						<th width="6%">
							客戶姓名
							<i
								class="bi"
								:class="detailDisplay.columns.CUS_NAME.iconClass"
								@click="detailDisplay.columns.CUS_NAME.onSort()"
							/>
						</th>
						<th width="9%">
							移轉者
							<i
								class="bi"
								:class="detailDisplay.columns.CREATE_BY.iconClass"
								@click="detailDisplay.columns.CREATE_BY.onSort()"
							/>
						</th>
						<th width="11%">
							異動類型
							<i
								class="bi"
								:class="detailDisplay.columns.CHG_TYPE_NAME.iconClass"
								@click="detailDisplay.columns.CHG_TYPE_NAME.onSort()"
							/>
						</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="[cusCode, cus] in detailDisplay.responseData.content" :key="cusCode">
						<td data-th="移轉日期">
							{{ cus.stdDt }}
						</td>
						<td data-th="移轉前單位">
							{{ cus.outBranDisplay }}
						</td>
						<td data-th="移轉前業務人員">
							{{ cus.outUserDisplay }}
						</td>
						<td data-th="移轉後單位">
							{{ cus.inBranDisplay }}
						</td>
						<td data-th="移轉後業務人員">
							{{ cus.inUserDisplay }}
						</td>
						<td data-th="客戶ID/統編">
							{{ cus.cusCode }}
						</td>
						<td data-th="客戶姓名">
							{{ cus.cusName }}
						</td>
						<td data-th="移轉者">
							{{ cus.createByDisplay }}
						</td>
						<td data-th="異動類型">
							{{ cus.chgTypeName }}
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<vue-modal :is-open="mainDisplay.detailModal.isOpen" @close="mainDisplay.detailModal.beforeClose()">
		<template #content="props">
			<div class="modal-dialog modal-lg modal-dialog-centered">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							客戶異動紀錄統計明細
						</h4>
						<button type="button" class="btn-expand">
							<i class="bi bi-arrows-fullscreen" />
						</button>
						<button
							type="button"
							class="btn-close"
							data-bs-dismiss="modal"
							aria-label="Close"
						/>
					</div>
					<div class="modal-body">
						<div class="card card-form">
							<div class="card-header">
								<h4>統計明細列表</h4>
							</div>
							<div class="table-responsive">
								<table class="table table-RWD table-horizontal-RWD table-bordered">
									<thead>
										<tr>
											<th width="7%">
												移轉日期
											</th>
											<th width="7%">
												移轉前單位
											</th>
											<th width="7%">
												移轉前業務人員
											</th>
											<th width="7%">
												移轉後單位
											</th>
											<th width="7%">
												移轉後業務人員
											</th>
											<th width="7%">
												現任業務人員
											</th>
											<th width="7%">
												客戶ID/統編
											</th>
											<th width="6%">
												客戶姓名
											</th>
											<th width="7%">
												移轉者
											</th>
											<th width="6%">
												異動類型
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="[cusCode, cus] in mainDisplay.detailModal.responseData.content" :key="cusCode">
											<td>{{ cus.stdDt }}</td>
											<td>{{ cus.outBranDisplay }}</td>
											<td>{{ cus.outUserDisplay }}</td>
											<td>{{ cus.inBranDisplay }}</td>
											<td>{{ cus.inUserDisplay }}</td>
											<td>{{ cus.aoUserDisplay }}</td>
											<td>{{ cus.cusCode }}</td>
											<td><a href="#">{{ cus.cusName }}</a></td>
											<td>{{ cus.createByDisplay }}</td>
											<td>{{ cus.chgTypeName }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
					<div id="appointmentFooter" class="modal-footer">
						<input
							id="appointmentCloseButton"
							name="btnClose"
							class="btn btn-white"
							type="button"
							value="關閉"
							data-bs-dismiss="modal"
						>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
</template>
<script>
import vueModal from '@/views/components/model.vue';
import moment from 'moment';
import { Field, Form } from 'vee-validate';
import vuePagination from '@/views/components/pagination.vue';

export default {
	components: {
		vueModal,
		'vue-form': Form,
		'vue-field': Field,
		vuePagination
	},
	data() {
		const vueObj = this;
		const today = moment().format('YYYY-MM-DD');
		const firstDayOfLastMonth = moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
		const createRadioTemplate = () => ({ codeName: '全部', codeValue: '' });
		const createSortStates = (columnNames = [''], options = {}) => {
			const directions = ['ASC', 'DESC'];
			let { sort = '', direction = 'ASC', callback = () => {} } = options;
			const defaultDirection = direction;
			const columns = columnNames.reduce((accmu, column) => {
				accmu[column] = {
					get checked() {
						return sort === column;
					},
					get isAsc() {
						return this.checked && direction === 'ASC';
					},
					get isDesc() {
						return this.checked && direction === 'DESC';
					},
					get iconClass() {
						const { checked, isAsc, isDesc } = this;
						return {
							'bi-caret-down-fill': isDesc,
							'bi-caret-up-fill': isAsc,
							'bi-chevron-expand': !checked
						};
					}
				};
				return accmu;
			}, {});
			Object.keys(columns).forEach((column) => {
				columns[column].onSort = function () {
					direction = column !== sort ? defaultDirection : directions[(directions.indexOf(direction) + 1) % 2];
					sort = column;
					callback();
				};
			});
			return {
				set sort(nval) {
					if (!columnNames.includes(nval)) return;
					sort = nval;
				},
				get sort() {
					return sort;
				},
				set direction(nval) {
					if (!directions.includes(nval)) return;
					direction = nval;
				},
				get direction() {
					return direction;
				},
				columns
			};
		};
		const extendBranOption = (source = {}) => {
			Object.defineProperties(source, {
				branDisplay: {
					get() {
						return `${this.branCode || ''} ${this.branName || ''}`.trim();
					},
					configurable: true,
					enumerable: true
				}
			});
			return source;
		};
		const extendUserOption = (source = {}) => {
			Object.defineProperties(source, {
				userDisplay: {
					get() {
						return `${this.userCode || ''} ${this.userName || ''}`.trim();
					},
					configurable: true,
					enumerable: true
				}
			});
			return source;
		};
		const extendStaffTransfer = (source = {}) => {
			const proxied = vueObj.queryCriteria;
			Object.defineProperties(source, {
				select: {
					value: {
						get options() {
							return proxied.userOptions;
						},
						set value(val) {
							proxied.userCode = val;
						},
						get value() {
							return proxied.userCode;
						},
						get disabled() {
							return proxied.transferRadios.get(proxied.transfer) != source;
						}
					},
					configurable: true,
					enumerable: true,
					writable: true
				}
			});
			return source;
		};
		const extendCusAoHistoryMainResp = (source = {}, searchParams = {}) => {
			Object.defineProperties(source, {
				outBranDisplay: {
					get() {
						return `${this.outBranCode || ''} ${this.outBranName || ''}`;
					},
					enumerable: true,
					configurable: true
				},
				outUserDisplay: {
					get() {
						return `${this.outUserCode || '解除經管'} ${this.outUserName || ''}`;
					},
					enumerable: true,
					configurable: true
				},
				inBranDisplay: {
					get() {
						return `${this.inBranCode || ''} ${this.inBranName || ''}`;
					},
					enumerable: true,
					configurable: true
				},
				inUserDisplay: {
					get() {
						return `${this.inUserCode || '解除經管'} ${this.inUserName || ''}`;
					},
					enumerable: true,
					configurable: true
				},
				createByDisplay: {
					get() {
						return `${this.createBy} ${this.transName}`;
					},
					enumerable: true,
					configurable: true
				},
				searchParams: {
					value: searchParams,
					configurable: true,
					enumerable: true,
					writable: true
				}
			});
			source.onQuery = function () {
				const { startDt, endDt, areaCode, branCode, chgType } = vueObj.queryCriteria.toParams();
				// userCode：『移轉者』若選為「業務人員」值為下拉選單所選人員
				const userCode = vueObj.queryCriteria.userCode;
				vueObj.$api.getCusAoHistoryDetailApi({ startDt, endDt, areaCode, branCode, userCode, chgType, chgId: this.chgId, page: 0, size: 1000, sort: 'CUS_CODE', direction: 'DESC' }).then((resp) => {
					const sourceContent = resp?.data?.content || [];
					sourceContent.forEach(extendCusAoHistoryDetailResp);
					const targetContent = vueObj.mainDisplay.detailModal.responseData.content;
					targetContent.clear();
					sourceContent.forEach(each => targetContent.set(each.cusCode, each));
					const sourceResponseData = { ...resp?.data, content: targetContent };
					const targetResponseData = vueObj.mainDisplay.detailModal.responseData;
					Object.assign(targetResponseData, sourceResponseData);
					vueObj.mainDisplay.detailModal.isOpen = true;
				});
			};
			return source;
		};
		const extendCusAoHistoryDetailResp = (source = {}) => {
			Object.defineProperties(source, {
				outBranDisplay: {
					get() {
						return `${this.outBranCode || ''} ${this.outBranName || ''}`;
					},
					enumerable: true,
					configurable: true
				},
				outUserDisplay: {
					get() {
						return `${this.outUserCode || '解除經管'} ${this.outUserName || ''}`;
					},
					enumerable: true,
					configurable: true
				},
				inBranDisplay: {
					get() {
						return `${this.inBranCode || ''} ${this.inBranName || ''}`;
					},
					enumerable: true,
					configurable: true
				},
				inUserDisplay: {
					get() {
						return `${this.inUserCode || '解除經管'} ${this.inUserName || ''}`;
					},
					enumerable: true,
					configurable: true
				},
				aoUserDisplay: {
					get() {
						return `${this.aoUserCode || ''} ${this.aoUserName || ''}`.trim();
					},
					configurable: true,
					enumerable: true
				},
				createByDisplay: {
					get() {
						return `${this.createBy || ''} ${this.transName || ''}`;
					},
					enumerable: true,
					configurable: true
				}
			});
			return source;
		};
		const getCusAoHistoryMain = async (page = 0) => {
			const { queryForm, userCondition } = vueObj.$refs;
			const allPass = (await Promise.all([queryForm.validate(), userCondition.validate()])).filter(pass => !pass.valid).length == 0;
			if (!allPass) {
				return;
			}
			const searchParams = vueObj.queryCriteria.toParams();
			const { startDt, endDt, areaCode, branCode } = searchParams;
			const { size, sort, direction } = vueObj.mainDisplay.pageParams;
			const createBy = vueObj.queryCriteria.userCode;
			this.$api.getCusAoHistoryMainApi({ startDt, endDt, areaCode, branCode, createBy, chgType: vueObj.queryCriteria.chgType.join(), page, size, sort, direction }).then((resp) => {
				const sourceContent = resp?.data?.content || [];
				sourceContent.forEach(each => extendCusAoHistoryMainResp(each, searchParams));
				const targetContent = vueObj.mainDisplay.responseData.content;
				targetContent.clear();
				sourceContent.forEach(each => targetContent.set(each.chgId, each));
				const sourceResponseData = { ...resp?.data, content: targetContent };
				const targetResponseData = vueObj.mainDisplay.responseData;
				Object.assign(targetResponseData, sourceResponseData);
				vueObj.mainDisplay.queryCount++;
				vueObj.lastDisplay = vueObj.mainDisplay;
			});
			// $.bi
			// 	.ajax({
			// 		url: _.toPageUrl(vueObj.config.apiPath + '/cus/cusAoHistoryMain', page, vueObj.mainDisplay.pageParams),
			// 		method: 'GET',
			// 		data: searchParams
			// 	})
			// 	.then((resp) => {
			// 		const sourceContent = resp?.data?.content || [];
			// 		sourceContent.forEach((each) => extendCusAoHistoryMainResp(each, searchParams));
			// 		const targetContent = vueObj.mainDisplay.responseData.content;
			// 		targetContent.clear();
			// 		sourceContent.forEach((each) => targetContent.set(each.chgId, each));
			// 		const sourceResponseData = { ...resp?.data, content: targetContent };
			// 		const targetResponseData = vueObj.mainDisplay.responseData;
			// 		Object.assign(targetResponseData, sourceResponseData);
			// 		vueObj.mainDisplay.queryCount++;
			// 		vueObj.lastDisplay = vueObj.mainDisplay;
			// 	});
		};
		const getCusAoHistoryDetail = (page = 0) => {
			const { startDt, endDt, areaCode, branCode, chgType } = vueObj.queryCriteria.toParams();
			const userCode = vueObj.queryCriteria.userCode;
			vueObj.$api.getCusAoHistoryDetailApi({ startDt, endDt, areaCode, branCode, userCode, chgType, chgId: this.chgId, page: 0, size: 1000, sort: 'CUS_CODE', direction: 'DESC' }).then((resp) => {
				const sourceContent = resp?.data?.content || [];
				sourceContent.forEach(extendCusAoHistoryDetailResp);
				const targetContent = vueObj.detailDisplay.responseData.content;
				targetContent.clear();
				sourceContent.forEach(each => targetContent.set(each.cusCode, each));
				const sourceResponseData = { ...resp?.data, content: targetContent };
				const targetResponseData = vueObj.detailDisplay.responseData;
				Object.assign(targetResponseData, sourceResponseData);
				vueObj.detailDisplay.queryCount++;
				vueObj.lastDisplay = vueObj.detailDisplay;
			});
		};
		{
			const self = this;
			self.$api.getMinorAreaApi({ buCode: self.buCode, majorCode: self.majorCode }).then((resp) => {
				const data = resp.data || [];
				const target = vueObj.queryCriteria.minorOptions;
				data.map(extendBranOption).forEach(each => target.set(each.branCode, each));
			});
			self.$api.getAdmCodeDetail({ codeType: 'TRANSACTION_CODE' }).then((resp) => {
				const data = resp.data || [];
				const target = vueObj.queryCriteria.chgTypeCheckboxes;
				data.filter(each => each.codeValue !== 'RE17').forEach(each => target.set(each.codeValue, each));
			});
			self.$api.getAdmCodeDetail({ codeType: 'TRANSER' }).then((resp) => {
				const data = resp.data || [];
				const target = vueObj.queryCriteria.transferRadios;
				data.unshift(createRadioTemplate());
				data.forEach((each) => {
					target.set(each.codeValue, each);
				});
				if (target.has('STAFF')) {
					extendStaffTransfer(target.get('STAFF'));
				}
			});
			self.$api.getAdmCodeDetail({ codeType: 'TRANS_SELECT_TYPE' }).then((resp) => {
				const data = resp.data || [];
				const target = vueObj.queryCriteria.searchTypeRadios;
				const funcs = [getCusAoHistoryMain, getCusAoHistoryDetail];
				data.map((each, index) => {
					each.onQuery = funcs[index];
					return each;
				}).forEach(each => target.set(each.codeValue, each));
				vueObj.queryCriteria.searchType = target.keys().next().value;
			});
		};
		return {
			lastDisplay: null,
			queryCriteria: (() => {
				let minorCode = '';
				let branCode = '';
				return {
					startDt: firstDayOfLastMonth,
					get maxStartDt() {
						return moment().format('YYYY-MM-DD');
					},
					endDt: today,
					get minEndDt() {
						return this.startDt;
					},
					get minorCode() {
						return minorCode;
					},
					set minorCode(val) {
						minorCode = val;
					},
					get branCode() {
						return branCode;
					},
					set branCode(val) {
						branCode = val;
					},
					userCode: '',
					transfer: '',
					transferRadios: new Map(),
					chgType: [],
					chgTypeCheckboxes: new Map(),
					searchType: '',
					searchTypeRadios: new Map(),
					minorOptions: new Map(),
					userOptions: new Map(),
					toParams() {
						const data = {};
						if (this.startDt) data.startDt = this.startDt;
						if (this.endDt) data.endDt = this.endDt;
						if (this.minorCode) data.areaCode = this.minorCode;
						if (this.branCode) data.branCode = this.branCode;
						if (this.userCode && this.transferRadios.get(this.transfer).select) data.userCode = this.userCode;
						if (this.chgType.length) data.chgType = this.chgType.join();
						return data;
					},
					onQuery() {
						this.searchTypeRadios.get(this.searchType)?.onQuery();
					}
				};
			})(),
			mainDisplay: (() => {
				const sortStates = createSortStates(
					[
						'OUT_BRAN_CODE',
						'OUT_USER_CODE',
						'IN_BRAN_CODE',
						'IN_USER_CODE',
						'CREATE_BY',
						'CHG_TYPE_NAME',
						'STD_DT',
						'CUS_CHG_TOTAL',
						'TRANS_CNT'
					],
					{
						sort: 'STD_DT',
						direction: 'DESC',
						callback: () => getCusAoHistoryMain()
					}
				);
				return {
					queryCount: 0,
					pageParams: {
						size: 20,
						set sort(val) {
							sortStates.sort = val;
						},
						get sort() {
							return sortStates.sort;
						},
						set direction(val) {
							sortStates.direction = val;
						},
						get direction() {
							return sortStates.direction;
						}
					},
					columns: sortStates.columns,
					responseData: {
						content: new Map()
					},
					gotoPage: getCusAoHistoryMain,
					detailModal: {
						isOpen: false,
						beforeClose() {
							this.isOpen = false;
						},
						responseData: {
							content: new Map()
						}
					}
				};
			})(),
			detailDisplay: (() => {
				const sortStates = createSortStates(
					[
						'STD_DT',
						'OUT_BRAN_CODE',
						'OUT_USER_CODE',
						'IN_BRAN_CODE',
						'IN_USER_CODE',
						'CUS_CODE',
						'CUS_NAME',
						'CREATE_BY',
						'CHG_TYPE_NAME'
					],
					{
						sort: 'STD_DT',
						direction: 'DESC',
						callback: () => getCusAoHistoryDetail()
					}
				);
				return {
					queryCount: 0,
					pageParams: {
						size: 20,
						set sort(val) {
							sortStates.sort = val;
						},
						get sort() {
							return sortStates.sort;
						},
						set direction(val) {
							sortStates.direction = val;
						},
						get direction() {
							return sortStates.direction;
						}
					},
					columns: sortStates.columns,
					responseData: {
						content: new Map()
					},
					gotoPage: getCusAoHistoryDetail
				};
			})(),
			get isMainDisplayed() {
				return this.lastDisplay == this.mainDisplay;
			},
			get isDetailDisplayed() {
				return this.lastDisplay == this.detailDisplay;
			}
		};
	}
};
</script>
