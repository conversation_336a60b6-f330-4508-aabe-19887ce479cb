<template>
	<cus-transfer-apply-query ref="query" @row-select="onRowSelect" @confirm="onConfirm" />
	<cus-transfer-apply-submit v-if="submitDisplay" :row="row" @row-submit="onRowSubmit" />
</template>
<script>
import cusTransferApplyQuery from './cusTransferApplyQuery.vue';
import cusTransferApplySubmit from './cusTransferApplySubmit.vue';
export default {
	components: {
		cusTransferApplyQuery,
		cusTransferApplySubmit
	},
	data() {
		return {
			row: {},
			submitDisplay: false
		};
	},
	methods: {
		onRowSelect(row) {
			this.row = row;
			this.submitDisplay = false;
		},
		onRowSubmit() {
			this.submitDisplay = false;
			this.$refs.query.rowKey = '';
			this.row = this.$refs.query.row;
		},
		onConfirm() {
			this.submitDisplay = true;
		}
	}
};

</script>
