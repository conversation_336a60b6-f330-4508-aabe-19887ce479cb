<template>
	<div>
		<div class="card card-form-collapse">
			<div
				class="card-header"
				data-bs-toggle="collapse"
				data-bs-target="#formSearch"
				aria-expanded="true"
			>
				<h4>查詢條件</h4>
				<span class="tx-square-bracket">為必填欄位</span>
			</div>
			<div id="formSearch" class="card-body collapse show">
				<vue-form ref="queryForm">
					<div class="row g-3 align-items-end">
						<div class="col-lg-4">
							<label class="form-label tx-require">客戶ID/統編</label><br>
							<vue-field
								v-model="cusCode"
								rules="required"
								label="客戶ID/統編"
								class="form-control"
								name="cusCode"
								type="text"
								size="15"
								maxlength="20"
							/>
						</div>
						<div class="col-lg-4">
							<label class="form-label">客戶姓名</label><br>
							<input
								v-model="cusName"
								class="form-control"
								type="text"
								size="15"
								maxlength="20"
							>
						</div>
						<div class="col-md-4 text-end">
							<button class="btn btn-primary btn-search" type="button" @click="onQuery">
								查詢
							</button>
						</div>
					</div>
				</vue-form>
			</div>
		</div>
		<div v-if="isTableDisplayed">
			<div class="card card-table mb-3">
				<div class="card-header">
					<h4>客戶查詢列表</h4>
					<vue-pagination v-bind="{gotoPage,pageable}" />
				</div>
				<div class="table-responsive">
					<table class="bih-table table table-RWD">
						<thead>
							<sort-columns v-model:sort="sort" v-model:direction="direction" @sort-change="onQuery">
								<tr>
									<th width="5%">
										選擇
									</th>
									<sort-column v-slot="sc" colname="CUS_NAME">
										<th width="10%">
											客戶姓名<i :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="CUS_CODE">
										<th width="10%">
											客戶ID/統編<i :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="RANK_NAME">
										<th width="10%">
											客戶投資屬性<i :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="GRA_NAME">
										<th width="10%">
											客戶資產等級<i :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="INV_AMT_CUS">
										<th width="11%">
											前日帳戶餘額<i :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="INV_AMT_AUM">
										<th width="11%">
											AUM<i :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="RANK_QUE_DT">
										<th width="10%">
											投資屬性問卷日期<i :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="CREATE_BY">
										<th width="10%">
											建檔人員<i :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="CREATE_BRAN_CODE">
										<th width="10%">
											建檔單位<i :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
								</tr>
							</sort-columns>
						</thead>
						<tbody>
							<tr v-for="[code, row] in rows" :key="code">
								<td data-th="選擇">
									<input
										type="radio"
										name="selected-cus"
										class="form-check-input"
										:checked="row.checked"
										:value="code"
										@click="onRowSelect"
									>
								</td>
								<td data-th="客戶姓名">
									<router-link :to="{ name: 'clientOverview', params: { cusCode: row.cusCode } }">
										{{ row.cusNameDisplay }}
									</router-link>
								</td>
								<td data-th="客戶ID/統編">
									{{ row.idnDisplay }}
								</td>
								<td data-th="客戶投資屬性">
									{{ row.rankName }}
								</td>
								<td data-th="客戶資產等級">
									{{ row.graName }}
								</td>
								<td data-th="前日帳戶餘額">
									{{ row.invAmtCusDisplay }}
								</td>
								<td data-th="AUM">
									{{ row.invAmtAumDisplay }}
								</td>
								<td data-th="投資屬性問卷日期">
									{{ row.rankQueDt }}
								</td>
								<td data-th="建檔人員">
									{{ row.creatorDisplay }}
								</td>
								<td data-th="建檔單位">
									{{ row.createBranDisplay }}
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div class="btn-end">
				<button
					class="btn btn-primary"
					type="button"
					:disabled="isConfirmDisabled"
					@click="onConfirm"
				>
					確認
				</button>
			</div>
		</div>
	</div>
</template>
<script>
import { Form, Field } from 'vee-validate';
import sortColumn from '@/views/components/sortColumn.vue';
import sortColumns from '@/views/components/sortColumns.vue';
import pagination from '@/views/components/pagination.vue';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		'sort-column': sortColumn,
		'sort-columns': sortColumns,
		'vue-pagination': pagination
	},
	props: {
		numberFormat: { default: Intl.NumberFormat() }
	},
	emits: ['rowSelect', 'confirm'],
	data() {
		return {
			cusCode: '',
			cusName: '',
			size: 20,
			sort: 'CUS_CODE',
			direction: 'ASC',
			pageable: {},
			rows: new Map(),
			rowKey: ''
		};
	},
	computed: {
		row() {
			return this.rows.get(this.rowKey) || {};
		},
		isConfirmDisabled() {
			return !this.rows.has(this.rowKey);
		},
		isTableDisplayed() {
			return !!Object.keys(this.pageable).length;
		}
	},
	methods: {
		onRowSelect(event) {
			this.rowKey = event.target.value === this.rowKey ? '' : event.target.value;
			this.$emit('rowSelect', this.row);
		},
		gotoPage(page = 0) {
			this.$refs.queryForm.validate().then(({ valid, errors }) => {
				if (!valid) {
					this.$bi.alert(Object.values(errors)[0]);
					return;
				}
				const { cusCode, cusName, size, sort, direction } = this;
				this.$api.getCusTransApplyApi({ cusCode, cusName, page, size, sort: sort + ',' + direction }).then((resp) => {
					const vueObj = this;
					this.rows.clear();
					resp.data?.content?.forEach((each) => {
						this.rows.set(each.cusCode, {
							...each,
							get cusNameDisplay() {
								return this.idnEntityType === 'N' ? vueObj.$filters.maskCusInfo(this.cusName, 1, 1) : this.cusName;
							},
							get idnDisplay() {
								return this.idnEntityType === 'N' ? vueObj.$filters.maskCusInfo(this.idn, -4) : this.idn;
							},
							get invAmtCusDisplay() {
								return this.invAmtCus ? vueObj.numberFormat.format(this.invAmtCus) : '';
							},
							get bankAuaLmDisplay() {
								return this.bankAuaLm ? vueObj.numberFormat.format(this.bankAuaLm) : '';
							},
							get invAmtAumDisplay() {
								return this.invAmtAum ? vueObj.numberFormat.format(this.invAmtAum) : '';
							},
							get creatorDisplay() {
								return `${this.createBy || ''} ${this.createName || ''}`;
							},
							get createBankDisplay() {
								return `${this.createBankCode || ''} ${this.createBankName || ''}`;
							},
							get isLocked() {
								return /^y$/i.test(this.lockYn);
							},
							get checked() {
								return vueObj.rowKey === this.cusCode;
							}
						});
					});
					this.pageable = resp.data;
				});
			});
		},
		onQuery() {
			this.gotoPage();
		},
		onConfirm() {
			this.$emit('confirm');
		}
	}
};
</script>
