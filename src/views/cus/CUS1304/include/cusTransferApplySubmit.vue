<template>
	<div>
		<div class="card card-table">
			<div class="card-header">
				<h4>查詢結果</h4>
			</div>
			<vue-form ref="submit" class="table-responsive">
				<table class="biv-table table table-RWD table-horizontal-RWD">
					<thead>
						<tr>
							<th width="15%" />
							<th width="85%">
								申請異動
							</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<th class="tx-require">
								異動類別
							</th>
							<td data-th="申請異動">
								{{ rmTransCode }}
							</td>
						</tr>
						<tr>
							<th class="tx-require">
								分行
							</th>
							<td data-th="申請異動">
								<div class="input-group wd-50p">
									<span class="input-group-text">分區</span>
									<vue-field
										v-model="areaCode"
										as="select"
										name="areaCode"
										class="form-select"
										rules="required"
										label="分區"
									>
										<option value="">
											請選擇
										</option>
										<option v-for="[code, each] in areaOptions" :key="code" :value="code">
											{{ code }} {{ each.branName }}
										</option>
									</vue-field>
									<span class="input-group-text">分行</span>
									<vue-field
										:key="branCode"
										v-model="branCode"
										as="select"
										name="branCode"
										class="form-select"
										rules="required"
										label="分行"
									>
										<option value="">
											請選擇
										</option>
										<option v-for="[code, each] in branOptions" :key="code" :value="code">
											{{ code }} {{ each.branName }}
										</option>
									</vue-field>
								</div>
							</td>
						</tr>
						<tr>
							<th class="tx-require">
								理財專員
							</th>
							<td data-th="申請異動">
								<vue-field
									:key="userCode"
									v-model="userCode"
									as="select"
									name="userCode"
									class="form-select wd-50p"
									rules="required"
									label="理財專員"
								>
									<option value="">
										請選擇
									</option>
									<option v-for="[code, each] in userOptions" :key="code" :value="code">
										{{ code }} {{ each.userName }}
									</option>
									<option v-if="isSameBran" :value="removeManaged.value.description">
										{{ removeManaged.name }}
									</option>
								</vue-field>
							</td>
						</tr>
						<tr>
							<th>AO CODE</th>
							<td data-th="申請異動">
								{{ aoCode }}
							</td>
						</tr>
						<tr>
							<th class="tx-require">
								異動說明
							</th>
							<td data-th="申請異動">
								<vue-field
									v-model="applyReason"
									as="textarea"
									name="applyReason"
									cols="30"
									rows="4"
									class="form-control wd-50p"
									label="異動說明"
									:rules="applyReasonRules"
								/>
							</td>
						</tr>
					</tbody>
				</table>
			</vue-form>
		</div>
		<div class="btn-end">
			<button type="button" class="btn btn-primary" @click="onSubmit">
				提交審核
			</button>
		</div>
	</div>
</template>

<script>
import { Form, Field } from 'vee-validate';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	props: {
		row: {
			type: Object,
			default: () => ({
				aoCode: '',
				cusCode: '',
				cusNameDisplay: ''
			})
		}
	},
	emits: ['rowSubmit'],
	data() {
		const areaOptions = new Map();
		const branOptions = new Map();
		const userOptions = new Map();

		return {
			rmTransCode: '',

			selectedAreaCode: '',
			selectedBranCode: '',
			selectedUserCode: '',
			areaOptions,
			branOptions,
			userOptions,
			applyReason: '',
			applyReasonRules: {
				required: true,
				regex: /[^\u{10000}-\u{10FFFF}]/u,
				max: 80
			},
			removeManaged: {
				name: '解除經管',
				value: Symbol.for('REMOVE_MANAGED')
			}
		};
	},

	computed: {
		areaCode: {
			get() {
				return this.selectedAreaCode;
			},
			set(val) {
				this.selectedAreaCode = val;
			}
		},
		branCode: {
			get() {
				return this.selectedBranCode;
			},
			set(val) {
				this.selectedBranCode = val;
			}
		},
		userCode: {
			get() {
				return this.selectedUserCode;
			},
			set(val) {
				this.selectedUserCode = val;
			}
		},
		isSameBran() {
			return this.row?.aoBranCode === this.branCode;
		},
		cusCode() {
			return this.row.cusCode;
		},
		aoCode() {
			return this.row.aoCode;
		}
	},

	watch: {
		selectedAreaCode(newVal) {
			// 分區變化的級聯邏輯
			this.selectedBranCode = '';
			this.selectedUserCode = '';
			this.branOptions.clear();
			this.userOptions.clear();

			if (newVal) {
				// 加載分行數據 分區代碼
				this.$api.getBranchesApi({
					minorCode: newVal
				}).then((resp) => {
					if (resp.data && resp.data.length > 0) {
						resp.data.forEach(each => this.branOptions.set(each.branCode, each));
					}
				});
			}
		},

		selectedBranCode(newVal) {
			// 分行變化的級聯邏輯
			this.selectedUserCode = '';
			this.userOptions.clear();

			if (newVal) {
				// 加載員工數據 分行代碼
				this.$api.getBranEmployeeApi({
					branCode: newVal
				}).then((resp) => {
					if (resp.data && resp.data.length > 0) {
						resp.data.forEach(each => this.userOptions.set(each.userCode, each));
					}
				});
			}
		}
	},

	created() {
		this.refetchRmTransCode();
		this.fetchAreaOptions();
	},

	methods: {
		refetchRmTransCode() {
			// 獲取異動類別
			this.$api.getAdmCodeDetail({
				codeType: 'RM_TRANS_CODE',
				codeValue: 'M'
			}).then((resp) => {
				this.rmTransCode = resp?.data?.[0]?.codeName;
			});
		},

		fetchAreaOptions() {
			if (!this.areaOptions) {
				return Promise.reject('areaOptions map not defined');
			}

			// 獲取分區數據
			return this.$api.getMinorAreaApi({
				buCode: this.buCode,
				majorCode: this.majorCode
			}).then((resp) => {
				// 分區數據加載成功
				this.areaOptions.clear();
				if (resp.data && resp.data.length > 0) {
					resp.data.forEach(each => this.areaOptions.set(each.branCode, each));
				}
			});
		},

		onSubmit() {
			this.$refs.submit.validate().then(({ valid, errors }) => {
				if (!valid) {
					// 表單驗證失敗
					this.$bi.alert(Object.values(errors)[0]);
					return;
				}

				this.$api.postCusTransLogApi({ cusCode: this.cusCode, branCode: this.branCode, userCode: this.userCode, applyReason: this.applyReason })
					.then(() => {
						this.onReset();
						this.$emit('rowSubmit');
						this.$bi.alert(`客戶${this.row.cusNameDisplay}異動申請已提交審核成功`);
					}).catch(() => {
						this.$bi.alert('提交失败，请重试');
					});
			});
		},

		onReset() {
			// 重置表單数据表單數據
			this.selectedAreaCode = '';
			this.selectedBranCode = '';
			this.selectedUserCode = '';
			this.applyReason = '';

			// 同時清空選項數據
			this.branOptions.clear();
			this.userOptions.clear();
		}
	}
};
</script>
