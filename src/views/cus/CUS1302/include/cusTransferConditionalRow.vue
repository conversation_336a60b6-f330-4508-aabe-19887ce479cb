<template>
	<tr>
		<td class="text-center">
			<input
				v-model="row.checked"
				type="checkbox"
				class="form-check-input chk"
				:disabled="!row.checkable"
				@change="onRowCheckboxChange"
			>
		</td>
		<td>
			<select
				v-model="branCode"
				:disabled="!row.selectable"
				class="form-select"
				@change="onBranCodeChange"
			>
				<option value="">
					請選擇
				</option>
				<option v-for="[code,each] in branOptions" :key="code" :value="code">
					{{ code }} {{ each.branName }}
				</option>
			</select>
		</td>
		<td>
			<select
				v-model="userCode"
				:disabled="!row.selectable"
				class="form-select"
				@change="onUserCodeChange"
			>
				<option value="">
					請選擇
				</option>
				<option v-for="[code,each] in userOptions" :key="code" :value="code">
					{{ code }} {{ each.userName }}
				</option>
				<option v-if="branCode" :value="removeManaged.value">
					{{ removeManaged.name }}
				</option>
			</select>
		</td>
		<td>{{ row.idnDisplay }}</td>
		<td>
			<router-link :to="{ name: 'clientOverview', params: { cusCode: row.cusCode } }">
				{{ row.cusNameDisplay }}
			</router-link>
		</td>
		<td>{{ row.graName }}</td>
		<td class="num">
			{{ row.aum }}
		</td>
		<td>{{ row.inUserDisplay }}</td>
		<td class="num">
			{{ row.invAmtCus }}
		</td>
		<td class="num">
			{{ row.totFee }}
		</td>
	</tr>
</template>
<script>

export default {
	components: {

	},
	props: {
		row: { type: Object, required: true },
		removeManaged: { type: Object, required: true }
	},
	emits: ['rowCheckboxChange'],
	computed: {
		checked: {
			get() {
				return this.row.checked;
			},
			set(val) {
				this.row.checked = val;
			}
		},
		branCode: {
			get() {
				return this.row.branCode;
			},
			set(val) {
				this.row.branCode = val;
			}
		},
		branOptions() {
			return this.row.branOptions;
		},
		userCode: {
			get() {
				return this.row.userCode;
			},
			set(val) {
				this.row.userCode = val;
			}
		},
		userOptions() {
			return this.row.userOptions;
		}
	},
	created() {
		this.refetchUserOptions();
	},
	methods: {
		refetchUserOptions() {
			this.userOptions.clear();
			if (!this.branCode) return;
			this.$api.getBranEmployeeApi({ branCode: this.branCode }).then((resp) => {
				resp.data?.forEach(each => this.userOptions.set(each.userCode, each));
			});
		},
		onRowCheckboxChange() {
			this.$emit('rowCheckboxChange', this.row);
		},
		onBranCodeChange() {
			this.userCode = '';
			this.refetchUserOptions();
			this.onUserCodeChange();
		},
		onUserCodeChange() {
			if (!this.userCode) {
				this.checked = false;
			}
			this.$emit('rowCheckboxChange', this.row);
		}
	}
};
</script>
