<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form card-collapse">
			<div class="card-header">
				<h4>請輸入下列資料</h4>
				<div class="btn-link" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1" />
			</div>
			<div id="collapseListGroup1" class="collapse show">
				<table class="table table-RWD table-bordered table-horizontal-RWD">
					<tbody>
						<tr>
							<th class="wd-15p">
								客戶ID/統編
							</th>
							<td class="wd-85p" colspan="3">
								<div class="input-group mb-2">
									<vue-field
										v-for="index in 5"
										v-model="idnOriList[index-1]"
										type="text"
										:name="`idnOri${index}`"
										size="10"
										maxlength="10"
										class="form-control"
									/>
								</div>
								<div class="input-group">
									<vue-field
										v-for="index in 5"
										v-model="idnOriList[index+4]"
										type="text"
										:name="`idnOri${index}`"
										size="10"
										maxlength="10"
										class="form-control"
									/>
								</div>
							</td>
						</tr>
						<tr>
							<th class="wd-15p">
								客戶姓名
							</th>
							<td class="wd-35p">
								<vue-field
									v-model="cusName"
									name="cusName"
									class="form-control"
									type="text"
									size="10"
									maxlength="10"
								/>
							</td>
							<th class="wd-15p">
								性別
							</th>
							<td class="wd-35p">
								<div class="form-check form-check-inline">
									<vue-field
										id="genderAll"
										v-model="gender"
										type="radio"
										value=""
										name="gender"
										class="form-check-input"
									/>
									<label class="form-check-label" for="genderAll">全部</label>
								</div>
								<div v-for="[code,each] in genderOptions" :key="code" class="form-check form-check-inline">
									<vue-field
										:id="`gender${code}`"
										v-model="gender"
										type="radio"
										:value="code"
										name="gender"
										class="form-check-input"
									/>
									<label class="form-check-label" :for="`gender${code}`">{{ each.codeName }}</label>
								</div>
							</td>
						</tr>
						<tr>
							<th>身分別</th>
							<td>
								<div class="form-check form-check-inline">
									<vue-field
										id="idnEntityTypeAll"
										v-model="idnEntityType"
										type="radio"
										value=""
										name="idnEntityType"
										class="form-check-input"
									/>
									<label class="form-check-label" for="idnEntityTypeAll">全部</label>
								</div>
								<div v-for="[code,each] in idnEntityTypeOptions" :key="code" class="form-check form-check-inline">
									<vue-field
										:id="`idnEntityType${code}`"
										v-model="idnEntityType"
										type="radio"
										:value="code"
										name="idnEntityType"
										class="form-check-input"
									/>
									<label class="form-check-label" :for="`idnEntityType${code}`">{{ each.codeName }}</label>
								</div>
							</td>
							<th>客戶投資風險屬性</th>
							<td>
								<div v-for="[code,each] in rankOptions" :key="code" class="form-check form-check-inline">
									<vue-field
										:id="`rankCode{code}`"
										v-model="rankCode"
										type="checkbox"
										:value="code"
										name="rankCode"
										class="form-check-input"
									/>
									<label class="form-check-label" :for="`rankCode{code}`">{{ each.rankName }}</label>
								</div>
							</td>
						</tr>
						<tr>
							<th>客戶資產等級</th>
							<td>
								<div v-for="[code,each] in gradeOptions" :key="code" class="form-check form-check-inline">
									<vue-field
										:id="`graCode${code}`"
										v-model="graCode"
										type="checkbox"
										:value="code"
										name="graCode"
										class="form-check-input"
									/>
									<label class="form-check-label" :for="`graCode${code}`">{{ each.graName }}</label>
								</div>
							</td>
							<th>完成信託開戶資料</th>
							<td>
								<div v-for="[code,each] in ynOptions" :key="code" class="form-check form-check-inline">
									<vue-field
										:id="`trust${code}`"
										v-model="trustYn"
										type="radio"
										:value="code"
										name="trust"
										class="form-check-input"
									/>
									<label class="form-check-label" :for="`trust${code}`">{{ each.codeName }}</label>
								</div>
							</td>
						</tr>
						<tr>
							<th>是否為專業投資人</th>
							<td>
								<div v-for="[code,each] in ynOptions" :key="code" class="form-check form-check-inline">
									<vue-field
										:id="`pi${code}`"
										v-model="piYn"
										type="radio"
										:value="code"
										name="pi"
										class="form-check-input"
									/>
									<label class="form-check-label" :for="`pi${code}`">{{ each.codeName }}</label>
								</div>
							</td>
							<th>客戶類別</th>
							<td>
								<div class="form-check form-check-inline">
									<vue-field
										id="cusTypeAll"
										v-model="cusType"
										type="radio"
										value=""
										name="cusType"
										class="form-check-input"
									/>
									<label class="form-check-label" for="cusTypeAll">全部</label>
								</div>
								<div v-for="[code,each] in cusTypeOptions" :key="code" class="form-check form-check-inline">
									<vue-field
										:id="`cusType${code}`"
										v-model="cusType"
										type="radio"
										:value="code"
										name="cusType"
										class="form-check-input"
									/>
									<label class="form-check-label" :for="`cusType${code}`">{{ each.codeName }}</label>
								</div>
							</td>
						</tr>
						<tr>
							<th>是否曾經申購基金</th>
							<td>
								<div v-for="[code,each] in ynOptions" :key="code" class="form-check form-check-inline">
									<vue-field
										:id="`fund${code}`"
										v-model="fundYn"
										type="radio"
										:value="code"
										name="fund"
										class="form-check-input"
									/>
									<label class="form-check-label" :for="`fund${code}`">{{ each.codeName }}</label>
								</div>
							</td>
							<th>是否曾經申購債券</th>
							<td>
								<div v-for="[code,each] in ynOptions" :key="code" class="form-check form-check-inline">
									<vue-field
										:id="`bond${code}`"
										v-model="bondYn"
										type="radio"
										:value="code"
										name="bond"
										class="form-check-input"
									/>
									<label class="form-check-label" :for="`bond${code}`">{{ each.codeName }}</label>
								</div>
							</td>
						</tr>
						<tr>
							<th>是否曾經申購保險</th>
							<td colspan="3">
								<div v-for="[code,each] in ynOptions" :key="code" class="form-check form-check-inline">
									<vue-field
										:id="`ins${code}`"
										v-model="insYn"
										type="radio"
										:value="code"
										name="ins"
										class="form-check-input"
									/>
									<label class="form-check-label" :for="`ins${code}`">{{ each.codeName }}</label>
								</div>
							</td>
						</tr>
						<tr>
							<th>未交易客戶</th>
							<td colspan="3">
								<div class="input-group">
									<span class="input-group-text">過去</span>
									<div class="form-check form-check-inline">
										<vue-field
											id="notTradeAll"
											v-model="notTrade"
											type="radio"
											value="0"
											name="notTrade"
											class="form-check-input"
										/>
										<label class="form-check-label" for="notTradeAll">全部</label>
									</div>
									<div v-for="[code,each] in notTradeOptions" :key="code" class="form-check form-check-inline">
										<vue-field
											:id="`notTrade${code}`"
											v-model="notTrade"
											type="radio"
											:value="code"
											name="notTrade"
											class="form-check-input"
										/>
										<label class="form-check-label" :for="`notTrade${code}`">{{ each.codeName }}</label>
									</div>
									<vue-field
										id="notTradeStartDt"
										v-model="notTradeStartDt"
										type="date"
										name="notTradeStartDt"
										class="form-control"
										size="13"
										maxlength="10"
									/>
									<span class="input-group-text">~</span>
									<vue-field
										id="notTradeEndDt"
										v-model="notTradeEndDt"
										type="date"
										name="notTradeEndDt"
										class="form-control"
										size="13"
										maxlength="10"
									/>
								</div>
							</td>
						</tr>
						<tr>
							<th>經管 ( 區 / 分行 / AO )</th>
							<td colspan="3">
								<div class="input-group">
									<vue-field
										id="areaCode"
										v-model="areaCode"
										as="select"
										name="areaCode"
										class="form-select"
									>
										<option value="">
											請選擇
										</option>
										<option v-for="[code,each] in areaOptions" :key="code" :value="code">
											{{ code }} {{ each.branName }}
										</option>
									</vue-field>
									<vue-field
										id="branCode"
										:key="branCode"
										v-model="branCode"
										as="select"
										name="branCode"
										class="form-select"
									>
										<option value="">
											請選擇
										</option>
										<option v-for="[code,each] in branOptions" :key="code" :value="code">
											{{ code }} {{ each.branName }}
										</option>
									</vue-field>
									<vue-field
										id="userCode"
										:key="userCode"
										v-model="userCode"
										as="select"
										name="userCode"
										class="form-select"
									>
										<option value="">
											請選擇
										</option>
										<option v-for="[code,each] in userOptions" :key="code" :value="code">
											{{ code }} {{ each.userName }}
										</option>
									</vue-field>
								</div>
							</td>
						</tr>
						<tr>
							<th>客戶撈取名單</th>
							<td colspan="3">
								<vue-field
									id="projectList"
									v-model="projectList"
									as="select"
									name="projectList"
									class="form-select"
								>
									<option value="">
										請選擇
									</option>
									<option v-for="[code,each] in projectListOptions" :key="code" :value="code">
										{{ each.projName }}
									</option>
								</vue-field>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div class="card-footer">
				<div class="text-end">
					<button class="btn btn-primary btn-search" @click="onQuery">
						查詢
					</button>
				</div>
			</div>
		</div>
		<div>
			<div class="card card-table">
				<div class="card-header">
					<h4> 欲移轉客戶列表</h4>
					<vue-pagination :pageable="pageable" :goto-page="gotoPage" />
				</div>
				<div class="table-responsive">
					<table class="table table-bordered ">
						<thead>
							<sort-columns v-model:sort="sort" v-model:direction="direction" @sort-change="onQuery">
								<tr>
									<th width="6%">
										選取
										<input
											v-model="columnCheckbox.checked"
											type="checkbox"
											class="form-check-input"
											:indeterminate.prop="columnCheckbox.indeterminate"
											:disabled="columnCheckbox.disabled"
											@change="onColumnCheckboxChange"
										>
									</th>
									<th width="10%">
										待轉入單位
									</th>
									<th width="12%">
										待轉入業務人員
									</th>
									<sort-column v-slot="sc" colname="CUS_CODE">
										<th width="10%">
											客戶ID/統編<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="CUS_NAME">
										<th width="10%">
											客戶姓名<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="GRA_CODE">
										<th width="10%">
											客戶等級<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="AUM">
										<th width="10%">
											客戶總AUM<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="IN_USER_CODE">
										<th width="12%">
											原經管業務人員<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="INV_AMT_CUS">
										<th width="12%">
											前日帳戶餘額<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="TOT_FEE">
										<th width="10%">
											近6月累計手收<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
								</tr>
							</sort-columns>
						</thead>
						<tbody>
							<cus-transfer-conditional-row
								v-for="[code,row] in rows"
								:key="code"
								:row="row"
								:remove-managed="removeManaged"
								@row-checkbox-change="onRowCheckboxChange"
							/>
						</tbody>
					</table>
				</div>
			</div>
			<div class="tx-note">
				當選擇的下拉選單無法選則時表示該客戶仍有待審核資料在主管的審核清單上，請主管先放行後才可以進行移轉分派。
			</div>
			<div class="btn-end">
				<button
					type="button"
					:disabled="!checkedRows.size"
					class="btn btn-primary"
					@click="onPreview"
				>
					下一步
				</button>
			</div>
		</div>
	</div>
</template>
<script>
import { Field } from 'vee-validate';
import pagination from '@/views/components/pagination.vue';
import sortColumn from '@/views/components/sortColumn.vue';
import sortColums from '@/views/components/sortColumns.vue';
import cusTransferConditionalRow from './cusTransferConditionalRow.vue';
export default {
	components: {
		'vue-field': Field,
		'vue-pagination': pagination,
		'sort-column': sortColumn,
		'cus-transfer-conditional-row': cusTransferConditionalRow,
		'sort-columns': sortColums
	},
	provide() {
		return {
			getSortColumns: () => this
		};
	},
	props: {
		historyRow: { type: Object, default: () => ({}) },
		removeManaged: { type: Object, required: true }
	},
	emits: ['preview'],
	data() {
		return {
			genderOptions: new Map(),
			idnEntityTypeOptions: new Map(),
			rankOptions: new Map(),
			gradeOptions: new Map(),
			ynOptions: new Map(),
			cusTypeOptions: new Map(),
			notTradeOptions: new Map(),
			projectListOptions: new Map(),
			branOptions: new Map(),
			idnOriList: Array.from(Array(10)).map(() => ''),
			chgId: this.historyRow.chgId || '',
			cusName: '',
			gender: '',
			idnEntityType: '',
			rankCode: [],
			graCode: [],
			trustYn: '',
			piYn: '',
			cusType: '',
			fundYn: '',
			bondYn: '',
			insYn: '',
			notTrade: '0',
			notTradeStartDt: '',
			notTradeEndDt: '',
			// ...this.$newCascadeSelectsBuilder().useAreaBranUserPattern().build(),
			areaOptions: new Map(),
			userOptions: new Map(),
			currentKey: '',
			areaCode: '',
			branCode: '',
			userCode: '',
			projectList: '',
			columnCheckbox: {
				checked: false,
				indeterminate: false,
				disabled: true
			},
			size: 20,
			sort: 'CUS_CODE',
			direction: 'DESC',
			pageable: {},
			rows: new Map(),
			checkedRows: new Map()
		};
	},

	watch: {
		areaCode(newVal) {
			this.branCode = ''; // 清空分行選擇
			this.userCode = ''; // 清空業務人員選擇
			this.refetchBranOptions(); // 重新取得該區域的分行
		},
		branCode(newVal) {
			this.userCode = ''; // 清空業務人員選擇
			this.refetchUserOptions();
		}
	},

	created() {
		this.refetchGenderOptions();
		this.refetchIdnEntityTypeOptions();
		this.refetchRankOptions();
		this.refetchGradeOptions();
		this.refetchYnOptions();
		this.refetchCusTypeOptions();
		this.refetchNotTradeOptions();
		this.refetchProjectListOptions();
		this.refetchBranOptions();
		this.refetchAreaOptions();
		if (this.chgId) {
			this.onQuery();
		}
	},
	methods: {
		refetchGenderOptions() {
			this.$api.getAdmCodeDetail({ codeType: 'GENDER' }).then((resp) => {
				this.genderOptions.clear();
				resp.data?.forEach(each => this.genderOptions.set(each.codeValue, each));
			});
		},
		refetchIdnEntityTypeOptions() {
			this.$api.getAdmCodeDetail({ codeType: 'IDN_ENTITY_TYPE' }).then((resp) => {
				this.idnEntityTypeOptions.clear();
				resp.data?.forEach(each => this.idnEntityTypeOptions.set(each.codeValue, each));
			});
		},
		refetchRankOptions() {
			this.$api.getCusRanksApi().then((resp) => {
				this.rankOptions.clear();
				resp.data?.forEach(each => this.rankOptions.set(each.rankCode, each));
			});
		},
		refetchGradeOptions() {
			this.$api.getCusGradesApi().then((resp) => {
				this.gradeOptions.clear();
				resp.data?.forEach(each => this.gradeOptions.set(each.graCode, each));
			});
		},
		refetchYnOptions() {
			this.$api.getAdmCodeDetail({ codeType: 'SELECT_YN' }).then((resp) => {
				this.ynOptions.clear();
				resp.data?.forEach(each => this.ynOptions.set(each.codeValue, each));
			});
		},
		refetchCusTypeOptions() {
			this.$api.getAdmCodeDetail({ codeType: 'CUS_BU' }).then((resp) => {
				this.cusTypeOptions.clear();
				resp.data?.forEach(each => this.cusTypeOptions.set(each.codeValue, each));
			});
		},
		refetchNotTradeOptions() {
			this.$api.getAdmCodeDetail({ codeType: 'CUS_NOTTRADE' }).then((resp) => {
				this.notTradeOptions.clear();
				resp.data?.forEach(each => this.notTradeOptions.set(each.codeValue, each));
			});
		},
		refetchProjectListOptions() {
			this.$api.getCusListProjectsApi().then((resp) => {
				this.projectListOptions.clear();
				resp.data?.forEach(each => this.projectListOptions.set(each.projCode, each));
			});
		},
		refetchBranOptions() {
			this.$api.getBranchesApi({ buCode: this.buCode, majorCode: this.majorCode, minorCode: this.minorCode }).then((resp) => {
				this.branOptions.clear();
				resp.data?.forEach(each => this.branOptions.set(each.branCode, each));
			});
		},
		refetchAreaOptions() {
			if (!this.areaOptions) {
				return Promise.reject('areaOptions map not defined');
			}
			return this.$api.getMinorAreaApi({ buCode: this.buCode, majorCode: this.majorCode }).then((resp) => {
				this.areaOptions.clear();
				resp.data.forEach(each => this.areaOptions.set(each.branCode, each));
			});
		},
		refetchUserOptions() {
			this.userOptions.clear();
			if (!this.branCode) return;
			this.$api.getBranEmployeeApi({ branCode: this.branCode }).then((resp) => {
				resp.data?.forEach(each => this.userOptions.set(each.userCode, each));
			});
		},
		toParams() {
			const result = {};
			if (this.chgId) result.chgId = this.chgId;
			if (this.idnOriList.some(each => !!each)) result.idnOriList = this.idnOriList.filter(each => !!each).join();
			if (this.cusName) result.cusName = this.cusName;
			if (this.gender) result.gender = this.gender;
			if (this.idnEntityType) result.idnType = this.idnEntityType;
			if (this.rankCode.length) result.rankCode = this.rankCode.join();
			if (this.graCode.length) result.graCode = this.graCode.join();
			if (this.trustYn) result.trustYn = this.trustYn;
			if (this.piYn) result.piYn = this.piYn;
			if (this.cusType) result.cusType = this.cusType;
			if (this.fundYn) result.fundYn = this.fundYn;
			if (this.bondYn) result.bondYn = this.bondYn;
			if (this.insYn) result.insYn = this.insYn;
			if (this.notTrade) result.notTrade = this.notTrade;
			if (this.notTradeStartDt) result.notTradeStartDt = this.notTradeStartDt;
			if (this.notTradeStartDt) result.notTradeStartDt = this.notTradeStartDt;
			if (this.minorCode) result.areaCode = this.minorCode;
			if (this.branCode) result.branCode = this.branCode;
			if (this.userCode) result.userCode = this.userCode;
			if (this.projectList) result.projectList = this.projectList;
			return result;
		},
		gotoPage(page = 0) {
			const { size, sort, direction } = this;
			const { chgId, notTrade } = this.toParams();
			this.$api.getCusMultiTransApi({
				idnOriList: this.idnOriList.filter(each => !!each).join(),
				cusList: this.cusList,
				cusName: this.cusName,
				gender: this.gender,
				idnType: this.idnEntityType,
				rankCode: this.rankCode.join(),
				graCode: this.graCode.join(),
				trustYn: this.trustYn,
				piYn: this.piYn,
				cusType: this.cusType,
				fundYn: this.fundYn,
				bondYn:	this.bondYn,
				insYn: this.insYn,
				notTrade,
				notTradeStartDt: this.notTradeStartDt,
				notTradeEndDt: this.notTradeEndDt,
				areaCode: this.areaCode,
				branCode: this.branCode,
				userCode: this.userCode,
				projectList: this.projectList,
				chgId: this.chgId,
				notLocked: this.notLocked,
				page: page,
				size: this.size,
				sort: this.sort,
				direction: this.direction
			}).then((resp) => {
				const vueObj = this;
				this.rows.clear();

				resp.data?.content
					?.map(each => (
						{
							...each,
							fromPage: page,
							branCode: this.checkedRows.get(each.cusCode)?.branCode || '',
							branOptions: this.branOptions,
							userCode: this.checkedRows.get(each.cusCode)?.userCode || '',
							userOptions: new Map(),
							inUserCode: each.inUserCode || this.removeManaged.value,
							checked: this.checkedRows.has(each.cusCode),

							get branDisplay() {
								return [this.branCode, this.branOptions.get(this.branCode)?.branName].filter(each => each).join(' ');
							},
							get userDisplay() {
								return this.userCode === vueObj.removeManaged.value
									? vueObj.removeManaged.name
									: [this.userCode, this.userOptions.get(this.userCode)?.userName].filter(each => each).join(' ');
							},
							get idnDisplay() {
								return this.idnEntityType === 'N' ? vueObj.$filters.maskCusInfo(this.idn, -4) : this.idn;
							},
							get cusNameDisplay() {
								return this.idnEntityType === 'N' ? vueObj.$filters.maskCusInfo(this.cusName, 1, 1) : this.cusName;
							},
							get inUserDisplay() {
								return this.inUserCode === vueObj.removeManaged.value
									? vueObj.removeManaged.name
									: [this.inUserCode, this.inUserName].filter(each => each).join(' ');
							},
							get isNotChanged() {
								return this.inUserCode === vueObj.removeManaged.value
									? this.inBranCode === this.branCode
									: this.inUserCode === this.userCode;
							},
							get selectable() {
								return this.lockYn !== 'Y';
							},
							get checkable() {
								return this.selectable && this.userCode && !this.isNotChanged;
							}
						}))
					.forEach(each => this.rows.set(each.cusCode, each));
				this.pageable = resp.data;
			});
		},
		onQuery() {
			this.gotoPage();
		},
		onRowCheckboxChange(row = {}) {
			if (row.checked) {
				this.checkedRows.set(row.cusCode, row);
			}
			else {
				this.checkedRows.delete(row.cusCode);
			}
			const checkables = Array.from(this.rows.values()).filter(each => each.checkable);
			const checkeds = checkables.filter(each => this.checkedRows.has(each.cusCode));
			if (checkables.length === 0 && this.checkedRows.size === 0) {
				Object.assign(this.columnCheckbox, { disabled: true, checked: false, indeterminate: false });
				return;
			}
			if (checkables.length === 0 && this.checkedRows.size !== 0) {
				Object.assign(this.columnCheckbox, { disabled: false, checked: true, indeterminate: false });
				return;
			}
			if (checkables.length === checkeds.length) {
				Object.assign(this.columnCheckbox, { disabled: false, checked: true, indeterminate: false });
				return;
			}
			if (checkables.length > checkeds.length && this.checkedRows.size === 0) {
				Object.assign(this.columnCheckbox, { disabled: false, checked: false, indeterminate: false });
				return;
			}
			if (checkables.length > checkeds.length && this.checkedRows.size !== 0) {
				Object.assign(this.columnCheckbox, { disabled: false, checked: false, indeterminate: true });
				return;
			}
		},
		onColumnCheckboxChange() {
			this.columnCheckbox.indeterminate = false;
			if (this.columnCheckbox.checked) {
				Array.from(this.rows.values())
					.filter(each => each.checkable)
					.forEach((each) => {
						each.checked = true;
						this.checkedRows.set(each.cusCode, each);
					});
				return;
			}
			if (!this.columnCheckbox.checked) {
				this.checkedRows.clear();
				Array.from(this.rows.values())
					.filter(each => each.checked)
					.forEach((each) => {
						each.checked = false;
					});
				return;
			}
		},
		onPreview() {
			this.$emit('preview', this.checkedRows);
		}
	}
};
</script>
