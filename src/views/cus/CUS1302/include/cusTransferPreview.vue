<template>
	<form>
		<div class="card card-table">
			<div class="card-header">
				<h4> 移轉客戶預覽清單</h4>
			</div>
			<summary-wrapper
				ref="sw"
				v-slot="sw"
				:rows="rows"
				:remove-managed="removeManaged"
			>
				<table class="bih-table table table-RWD">
					<thead>
						<tr>
							<th width="25%">
								轉入分行
							</th>
							<th width="25%">
								轉入理專
							</th>
							<th width="25%">
								本次轉入客戶數
							</th>
							<th width="25%">
								轉入後總管理客戶數
							</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="[code,row] in sw.summarizedRows" :key="code">
							<td>{{ row.branDisplay }}</td>
							<td>{{ row.userDisplay }}</td>
							<td>{{ row.transCnt }}</td>
							<td>{{ row.totalCntDisplay }}</td>
						</tr>
					</tbody>
				</table>
			</summary-wrapper>
		</div>
		<to-be-submitted v-slot="tbs" :rows="rows">
			<div class="card card-table">
				<div class="card-header">
					<h4> 分派總人數：{{ rows.size }}人</h4>
					<vue-pagination :pageable="tbs.pageable" :goto-page="tbs.gotoPage" />
				</div>
				<div class="table-responsive">
					<table class="bih-table table table-RWD">
						<thead>
							<tr>
								<th width="10%">
									待轉入單位
								</th>
								<th width="10%">
									待轉入業務人員
								</th>
								<th width="10%">
									客戶ID/統編
								</th>
								<th width="10%">
									客戶姓名
								</th>
								<th width="10%">
									客戶等級
								</th>
								<th width="10%">
									客戶總AUM
								</th>
								<th width="10%">
									原經管業務人員
								</th>
								<th width="10%">
									前日帳戶餘額
								</th>
								<th width="10%">
									近6月累計手收
								</th>
								<th width="10%">
									關係戶主戶
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="[code,row] in tbs.pagedRows" :key="code">
								<td>{{ row.branDisplay }}</td>
								<td>{{ row.userDisplay }}</td>
								<td>{{ row.idnDisplay }}</td>
								<td><a href="#">{{ row.cusNameDisplay }}</a></td>
								<td>{{ row.graName }}</td>
								<td class="num">
									{{ row.aum }}
								</td>
								<td>{{ row.inUserDisplay }}</td>
								<td class="num">
									{{ row.invAmtCus }}
								</td>
								<td class="num">
									{{ row.totFee }}
								</td>
								<td>{{ row.rafCusName }}</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</to-be-submitted>
		<div class="text-end mt-3">
			<input
				type="button"
				class="btn btn-primary"
				value="回上一頁"
				@click="onBackward"
			>
			<input
				type="submit"
				class="btn btn-primary"
				value="完成"
				@click.prevent="onSubmit"
			>
		</div>
	</form>
</template>
<script>
import vuePagination from '@/views/components/pagination.vue';

export default {
	components: {
		'vue-pagination': vuePagination,
		'summary-wrapper': {
			template: `<slot v-bind="{summarizedRows}"/>`,
			// inject: ['config'],
			props: {
				rows: { type: Map, required: true },
				removeManaged: { type: Object, required: true }
			},
			data() {
				return {
					summarizedRows: new Map(),
					transSummations: new Map(),
					transMaximums: new Map()
				};
			},
			computed: {
				userCodes() {
					const source = this.rows;
					return Array.from(source.values())
						.map(each => each.userCode)
						.filter(code => code != this.removeManaged.value);
				}
			},
			created() {
				this.onCreated();
			},
			methods: {
				summarize(rows = new Map(), transSummations = new Map(), transMaximums = new Map(), removeManaged = {}) {
					const layered = new Map();
					for (const [cusCode, each] of rows) {
						const { userCode, branCode, branOptions, userOptions, fromPage } = each;
						if (layered.has(userCode) && layered.get(userCode).has(branCode)) {
							const found = layered.get(userCode).get(branCode);
							found.transCnt++;
							found.fromPage = Math.min(fromPage, found.fromPage);
							continue;
						}
						const mapped = {
							userCode,
							branCode,
							branOptions,
							userOptions,
							fromPage,
							transCnt: 1,
							transedCnt: Number.parseInt(transSummations.get(userCode)?.totalCnt) || 0,
							cusMaxCnt: Number.parseInt(transMaximums.get(userCode)?.cusMaxCnt) || 0,
							get isRemoveManaged() {
								return this.userCode === removeManaged.value;
							},
							get branDisplay() {
								return [this.branCode, this.branOptions.get(this.branCode)?.branName].filter(each => each).join(' ');
							},
							get userDisplay() {
								return this.isRemoveManaged
									? removeManaged.name
									: [this.userCode, this.userOptions.get(this.userCode)?.userName].filter(each => each).join(' ');
							},
							get totalCnt() {
								return this.transCnt + this.transedCnt;
							},
							get isOverMaximum() {
								return !this.isRemoveManaged && this.totalCnt > this.cusMaxCnt;
							},
							get totalCntDisplay() {
								return this.isRemoveManaged ? '' : this.totalCnt;
							}
						};
						if (layered.has(userCode)) {
							layered.get(userCode).set(branCode, mapped);
							continue;
						}
						layered.set(userCode, new Map([[branCode, mapped]]));
					}
					const sorted = Array.from(layered.values())
						.flatMap(each => Array.from(each.values()))
						.sort((ax, by) => ax.fromPage - by.fromPage);
					const flattened = new Map();
					for (const each of sorted) {
						const key = [each.branCode, each.userCode]
							.filter(each => each)
							.map(each => each.toString())
							.join('.');
						flattened.set(key, each);
					}
					return flattened;
				},
				refetchTransSummations() {
					return this.$api.getCountCusAoApi({ userCode: this.userCodes.join() }).then((resp) => {
						this.transSummations.clear();
						resp.data?.forEach(each => this.transSummations.set(each.userCode, each));
					});
				},
				refetchTransMaximums() {
					return this.$api.getCusAoMaxCountApi({ userCode: this.userCodes.join() }).then((resp) => {
						this.transMaximums.clear();
						resp.data?.forEach(each => this.transMaximums.set(each.userCode, each));
					});
				},
				async onCreated() {
					await Promise.all([this.refetchTransMaximums(), this.refetchTransSummations()]);
					const summarizedRows = this.summarize(this.rows, this.transSummations, this.transMaximums, this.removeManaged);
					this.summarizedRows.clear();
					for (const [key, value] of summarizedRows) {
						this.summarizedRows.set(key, value);
					}
				}
			}
		},
		'to-be-submitted': {
			template: `<slot v-bind="{pagedRows,pageable,gotoPage}"/>`,
			props: {
				rows: { type: Map, required: true }
			},
			data() {
				return {
					pagedRows: new Map(),
					page: 0,
					size: 20
				};
			},
			created() {
				this.gotoPage();
			},
			computed: {
				pageable() {
					return {
						number: this.page,
						totalElements: this.rows.size,
						totalPages: Math.ceil(this.rows.size / this.size),
						pageable: {
							pageNumber: this.page
						}
					};
				}
			},
			methods: {
				gotoPage(page = 0) {
					if (page < 0) return;
					this.pagedRows.clear();
					const sourceEntries = Array.from(this.rows.entries());
					const skips = page * this.size;
					if (sourceEntries.length <= skips) return;
					for (let rowNum = 0; rowNum < Math.min(this.size, sourceEntries.length); rowNum++) {
						const [key, value] = sourceEntries.at(rowNum + skips);
						this.pagedRows.set(key, value);
					}
					this.page = page;
				}
			}
		}
	},
	props: {
		rows: { type: Map, required: true },
		removeManaged: { type: Object, required: true }
	},
	emits: ['backward', 'refresh'],
	computed: {},
	methods: {
		onBackward() {
			this.$emit('backward');
		},
		onSubmit() {
			if (!this.$refs.sw) return;
			const overMax = Array.from(this.$refs.sw.summarizedRows.values()).find(each => each.isOverMaximum);
			if (overMax) {
				this.$bi.alert(`移轉後業務人員已達上限數${overMax.cusMaxCnt}筆限制，無法進行分派`);
				return;
			}
			const transfers = Array.from(this.rows.values()).map((each) => {
				const result = {};
				result.cusCode = each.cusCode;
				if (each.userCode === this.removeManaged.value) {
					result.branCode = each.branCode;
					return result;
				}
				result.userCode = each.userCode;
				return result;
			});
			this.$api.postCusTransferResultApi(transfers).then((resp) => {
				this.$emit('refresh');
			});
		}
	}
};
</script>
