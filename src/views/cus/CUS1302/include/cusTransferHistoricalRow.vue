<template>
	<tr>
		<td data-th="原單位">
			{{ row.outBranDisplay }}
		</td>
		<td data-th="原經管RM">
			{{ row.outUserDisplay }}
		</td>
		<td data-th="新單位">
			{{ row.inBranDisplay }}
		</td>
		<td data-th="新業務人員">
			{{ row.inUserDisplay }}
		</td>
		<td data-th="移轉者">
			{{ row.createByDisplay }}
		</td>
		<td data-th="異動類型">
			{{ row.chgTypeName }}
		</td>
		<td data-th="移轉日期">
			{{ row.stdDt }}
		</td>
		<td data-th="客戶數" class="num">
			{{ row.cusChgTotal }}
		</td>
		<td data-th="可分派客戶數" class="num">
			<a href="" @click.prevent="onForward">{{ row.transCnt }}</a>
		</td>
		<td data-th="整批移轉">
			<form class="input-group">
				<select
					v-model="branCode"
					:disabled="!row.selectable"
					:name="`newBranCode${row.chgId}`"
					class="form-select"
					@change="onBranCodeChange"
				>
					<option value="">
						請選擇
					</option>
					<option v-for="[code, each] in branOptions" :key="code" :value="code">
						{{ code }} {{ each.branName }}
					</option>
				</select>
				<select
					v-model="userCode"
					:disabled="!row.selectable"
					:name="`newUserCode${row.chgId}`"
					class="form-select"
				>
					<option value="">
						請選擇
					</option>
					<option v-for="[code, each] in userOptions" :key="code" :value="code">
						{{ code }} {{ each.userName }}
					</option>
					<option v-if="branCode" :value="removeManaged.value">
						{{ removeManaged.name }}
					</option>
				</select>
				<button
					type="submit"
					:disabled="!row.submittable"
					class="btn btn-primary"
					@click.prevent="onSubmit"
				>
					移轉
				</button>
			</form>
		</td>
	</tr>
</template>
<script>
export default {
	inheritAttrs: false,
	props: {
		row: { type: Object, required: true },
		removeManaged: { type: Object, required: true }
	},
	emits: ['forward'],
	computed: {
		userCode: {
			set(val) {
				this.row.userCode = val;
			},
			get() {
				return this.row.userCode;
			}
		},
		userOptions() {
			return this.row.userOptions;
		},
		branCode: {
			set(val) {
				this.row.branCode = val;
			},
			get() {
				return this.row.branCode;
			}
		},
		branOptions() {
			return this.row.branOptions;
		}
	},
	methods: {
		onBranCodeChange() {
			this.userCode = '';
			this.userOptions.clear();
			if (!this.branCode) return;
			this.$api.getBranEmployeeApi({ branCode: this.branCode }).then((resp) => {
				resp.data?.forEach(each => this.userOptions.set(each.userCode, each));
			});
		},
		onForward() {
			this.$emit('forward', this.row);
		},
		onSubmit() {
			const userCode = this.isRemoveManaged ? null : this.userCode;
			this.$api.postCusTransferResultApi([{
				chgId: this.row.chgId,
				branCode: this.branCode,
				userCode
			}]).then((resp) => {
				this.$bi.alert('客戶移轉分派成功');
			});
		}
	}
};
</script>
