<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formSearch">
				<h4>查詢條件</h4>
				<span class="tx-square-bracket">為必填欄位</span>
			</div>
			<div id="formSearch" class="card-body collapse show">
				<vue-form ref="queryForm">
					<div class="row g-3 align-items-end">
						<div class="col-md-3">
							<label class="form-label tx-require">查詢期間</label>
							<div class="input-group">
								<vue-field
									id="startDt"
									v-model="startDt"
									type="date"
									label="查詢日期起"
									rules="required"
									:max="maxStartDt"
									name="startDt"
									class="form-control"
								/>
								<span class="input-group-text">~</span>
								<vue-field
									id="endDt"
									v-model="endDt"
									type="date"
									label="查詢日期迄"
									rules="required"
									name="endDt"
									:min="minEndDt"
									:max="maxEndDt"
									class="form-control"
								/>
							</div>
						</div>
						<div class="col-md-6">
							<label class="form-label tx-require"> 銷售人員 ( 區 / 分行)</label>
							<div class="input-group">
								<vue-field
									id="areaCode"
									v-model="areaCode"
									as="select"
									name="areaCode"
									label="經管區域"
									class="form-select"
									rules="required"
								>
									<option value="">
										請選擇
									</option>
									<option v-for="[code, each] in areaOptions" :key="code" :value="code">
										{{ code }} {{ each.branName }}
									</option>
								</vue-field>
								<vue-field
									id="branCode"
									:key="branCode"
									v-model="branCode"
									as="select"
									label="經管分行"
									name="branCode"
									class="form-select"
									rules="required"
								>
									<option value="">
										請選擇
									</option>
									<option v-for="[code, each] in branOptions" :key="code" :value="code">
										{{ code }} {{ each.branName }}
									</option>
								</vue-field>
							</div>
						</div>
						<div class="col-md-3">
							<label class="form-label">移轉者員編</label>
							<vue-field
								id="createBy"
								v-model="createBy"
								type="text"
								size="15"
								maxlength="10"
								name="createBy"
								class="form-control form-control-sm"
							/>
						</div>
						<div class="col-md-8">
							<label class="form-label">異動類別</label><br>
							<div v-for="[code, each] in chgTypeOptions" :key="code" class="form-check form-check-inline">
								<vue-field
									:id="`chgType${code}`"
									v-model="chgType"
									type="checkbox"
									:value="code"
									label="異動類別"
									name="chgType"
									class="form-check-input"
								/>
								<label class="form-check-label" :for="`chgType${code}`">{{ each.codeName }}</label>
							</div>
							<!-- <a class="tx-link" href="#" @click.prevent todo><u>說明</u></a> -->
						</div>
						<div class="col-md-4 text-end">
							<button type="button" class="btn btn-primary btn-search" @click="onQuery">
								查詢
							</button>
						</div>
					</div>
				</vue-form>
			</div>
		</div>
		<div>
			<div class="card card-table">
				<div class="card-header">
					<h4>客戶異動紀錄列表</h4>
					<vue-pagination :pageable="pageable" :goto-page="gotoPage" @sort-change="onQuery" />
				</div>
				<div class="table-responsive">
					<table class="bih-table table table-RWD">
						<thead>
							<sort-columns v-model:sort="sort" v-model:direction="direction" @sort-change="onQuery">
								<tr>
									<sort-column v-slot="sc" colname="OUT_BRAN_CODE">
										<th width="9%">
											原單位<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="OUT_USER_CODE">
										<th width="9%">
											原業務人員<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="IN_BRAN_CODE">
										<th width="9%">
											新單位<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="IN_USER_CODE">
										<th width="9%">
											新業務人員<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="CREATE_BY">
										<th width="8%">
											移轉者<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="CHG_TYPE_NAME">
										<th width="7%">
											異動類型<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="STD_DT">
										<th width="7%">
											移轉日期<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="CUS_CHG_TOTAL">
										<th width="7%">
											客戶數<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<sort-column v-slot="sc" colname="TRANS_CNT">
										<th width="7%">
											可分派客戶數<i class="bi" :class="sc.iconClasses" @click="sc.onSort" />
										</th>
									</sort-column>
									<th width="20%">
										整批移轉
									</th>
								</tr>
							</sort-columns>
						</thead>
						<tbody>
							<cus-transfer-historical-row
								v-for="[code, row] in rows"
								:key="code"
								:row="row"
								:remove-managed="removeManaged"
								@forward="onForward"
							/>
						</tbody>
					</table>
				</div>
			</div>
			<div class="tx-note">
				可移轉客戶數：客戶無待審資料才可進行移轉
			</div>
		</div>
	</div>
</template>
<script>
import moment from 'moment';
import cusTransferHistoricalRow from './cusTransferHistoricalRow.vue';
import { Form, Field } from 'vee-validate';
import vuePagination from '@/views/components/pagination.vue';
import sortColumn from '@/views/components/sortColumn.vue';
import sortColumns from '@/views/components/sortColumns.vue';

export default {
	components: {
		cusTransferHistoricalRow,
		'vue-form': Form,
		'vue-field': Field,
		'vue-pagination': vuePagination,
		'sort-column': sortColumn,
		'sort-columns': sortColumns
	},
	provide() {
		return {
			getSortColumns: () => this
		};
	},
	props: {
		removeManaged: { type: Object, required: true }
	},
	emits: ['forward'],
	data() {
		const now = moment();
		const today = now.format('YYYY-MM-DD');
		const firstDayOfLastMonth = now.subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
		return {
			chgTypeOptions: new Map(),
			branOptions: new Map(),
			startDt: firstDayOfLastMonth,
			maxStartDt: today,
			endDt: today,
			maxEndDt: today,
			areaOptions: new Map(),
			selectedAreaCode: '', // 單獨管理選中的區域代碼
			selectedBranCode: '', // 單獨管理選中的分行代碼
			createBy: '',
			chgType: [],
			size: 20,
			sort: 'STD_DT',
			direction: 'DESC',
			pageable: {},
			rows: new Map()
		};
	},
	computed: {
		minEndDt() {
			return this.startDt;
		},
		areaCode: {
			get() {
				return this.selectedAreaCode;
			},
			set(val) {
				this.selectedAreaCode = val;
				// 當區域改變時的邏輯...
				this.branOptions.clear();
				this.selectedBranCode = '';
				if (val) {
					this.$nextTick(() => {
						this.refetchBranOptions();
					});
				}
			}
		},
		branCode: {
			get() {
				return this.selectedBranCode;
			},
			set(val) {
				this.selectedBranCode = val;
			}
		}
	},
	created() {
		this.refetchChgTypeOptions();
		this.refetchBranOptions();
		this.fetchAreaOptions();
	},
	methods: {
		fetchAreaOptions() {
			if (!this.areaOptions) {
				return Promise.reject('areaOptions map not defined');
			}
			return this.$api.getMinorAreaApi({ buCode: this.buCode, majorCode: this.majorCode }).then((resp) => {
				this.areaOptions.clear();
				resp.data.forEach(each => this.areaOptions.set(each.branCode, each));
			});
		},

		refetchChgTypeOptions() {
			this.$api.getAdmCodeDetail({ codeType: 'TRANSACTION_CODE' }).then((resp) => {
				this.chgTypeOptions.clear();
				resp.data?.filter(each => each.codeValue !== 'RE17').forEach(each => this.chgTypeOptions.set(each.codeValue, each));
			});
		},
		refetchBranOptions() {
			return this.$api.getBranchesApi({ minorCode: this.areaCode }).then((resp) => {
				this.branOptions.clear();
				resp.data?.forEach(each => this.branOptions.set(each.branCode, each));
			});
		},
		toParams() {
			const result = {};
			if (this.startDt) result.startDt = this.startDt;
			if (this.endDt) result.endDt = this.endDt;
			if (this.areaCode) result.areaCode = this.areaCode;
			if (this.branCode) result.branCode = this.branCode;
			if (this.createBy) result.createBy = this.createBy;
			if (this.chgType.length) result.chgType = this.chgType.join();
			return result;
		},
		async gotoPage(page = 0) {
			const { valid, errors } = await this.$refs.queryForm.validate();
			if (!valid) {
				this.$bi.alert(Object.values(errors)[0]);
				return;
			}
			const { size, sort, direction } = this;
			const { startDt, endDt, areaCode, branCode, createBy, chgType } = this.toParams();
			this.$api.getCusAoHistoryMainApi({ startDt, endDt, areaCode, branCode, createBy, chgType, page, size, sort, direction }).then((resp) => {
				const vueObj = this;
				this.rows.clear();
				resp.data?.content
					?.map(each => ({
						...each,
						branCode: '',
						branOptions: this.branOptions,
						userCode: '',
						userOptions: new Map(),
						outUserCode: each.outUserCode || this.removeManaged.value,
						inUserCode: each.inUserCode || this.removeManaged.value,
						transCnt: Number.parseInt(each.transCnt) || 0,
						get outBranDisplay() {
							return [this.outBranCode, this.outBranName].filter(each => each).join(' ');
						},
						get outUserDisplay() {
							return this.outUserCode === vueObj.removeManaged.value
								? vueObj.removeManaged.name
								: [this.outUserCode, this.outUserName].filter(each => each).join(' ');
						},
						get inBranDisplay() {
							return [this.inBranCode, this.inBranName].filter(each => each).join(' ');
						},
						get inUserDisplay() {
							return this.inUserCode === vueObj.removeManaged.value
								? vueObj.removeManaged.name
								: [this.inUserCode, this.inUserName].filter(each => each).join(' ');
						},
						get createByDisplay() {
							return [this.createBy, this.transName].filter(each => each).join(' ');
						},
						get isNotChanged() {
							return this.inUserCode === vueObj.removeManaged.value
								? this.inBranCode === this.branCode
								: this.inUserCode === this.userCode;
						},
						get selectable() {
							return this.transCnt > 0;
						},
						get submittable() {
							return this.selectable && !!this.userCode && !this.isNotChanged;
						}
					}))
					.forEach(each => this.rows.set(each.chgId, each));
				this.pageable = resp.data;
			});
		},
		onQuery() {
			this.gotoPage();
		},
		onForward(row = {}) {
			this.$emit('forward', row);
		}
	}
};
</script>
