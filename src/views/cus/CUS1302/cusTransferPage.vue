<template>
	<vue-bi-tabs
		v-show="!isShowPreview"
		ref="tabs"
		:menu-code="menuCode"
		:default-tab-code="defaultTabCode"
	>
		<template #default="{ id }">
			<component
				:is="id"
				:key="contentKey"
				:history-row="historyRow"
				:remove-managed="removeManaged"
				@preview="onPreview"
				@forward="onForward"
			/>
		</template>
	</vue-bi-tabs>
	<cus-transfer-preview
		v-if="isShowPreview"
		:rows="checkedRows"
		:remove-managed="removeManaged"
		@backward="onBackward"
		@refresh="onRefresh"
	/>
</template>
<script>
import vueBiTabs from '@/views/components/biTabs.vue';
import cusTransferHistorical from './include/cusTransferHistorical.vue';
import cusTransferConditional from './include/cusTransferConditional.vue';
import cusTransferPreview from './include/cusTransferPreview.vue';
export default {
	components: {
		vueBiTabs,
		cusTransferConditional,
		cusTransferHistorical,
		cusTransferPreview
	},
	data() {
		return {
			menuCode: 'M21-00',
			defaultTabCode: 'M21-000',
			removeManaged: {
				name: '解除經管',
				value: Symbol.for('REMOVE_MANAGED')
			},
			historyRow: {},
			checkedRows: new Map(),
			isShowPreview: false,
			contentKey: 0
		};
	},
	methods: {
		onForward(row) {
			this.historyRow = row;
			this.$refs.tabs.selectedCode = 'M21-001';
		},
		onBackward() {
			this.isShowPreview = false;
		},
		onRefresh() {
			this.historyRow = {};
			this.checkedRows.clear();
			this.isShowPreview = false;
			this.contentKey++;
		},
		onPreview(rows) {
			this.checkedRows = rows;
			this.isShowPreview = true;
		}
	}
};
</script>
