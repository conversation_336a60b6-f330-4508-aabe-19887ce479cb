<template>
	<div class="asset-report-home">
		<div class="card card-table">
			<div class="card-header">
				<h4>{{ $t('cus.assetReportList') }}</h4>
				<input
					name="createReport"
					type="button"
					class="btn btn-primary"
					:value="$t('cus.createReport')"
					@click="gotoCreateReport"
				>
			</div>
			<div class="table-responsive">
				<table class="bih-table table table-RWD">
					<thead>
						<tr>
							<th>{{ $t('cus.reportName') }}</th>
							<th>{{ $t('cus.customerName') }}</th>
							<th>{{ $t('cus.execute') }}</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="report in reportList" :key="report.reportId">
							<td>{{ report.reportName }}</td>
							<td>{{ report.cusCode }} - {{ report.cusName }}</td>
							<td>
								<button
									type="button"
									class="btn btn-info btn-icon JQ-MainEdit"
									data-bs-toggle="tooltip"
									title=""
									:data-bs-original-title="$t('cus.edit')"
									:aria-label="$t('cus.edit')"
									@click="editReport(report)"
								>
									<i class="fa-solid fa-pen" />
								</button>
								<button
									type="button"
									class="btn btn-danger btn-icon"
									data-bs-toggle="tooltip"
									title=""
									:data-bs-original-title="$t('cus.delete')"
									:aria-label="$t('cus.delete')"
									@click="deleteReport(report)"
								>
									<i class="fa-solid fa-trash" />
								</button>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>

		<!-- 新增報告書 Modal -->
		<div
			v-if="showCreateModal"
			class="modal fade"
			:class="{ show: showCreateModal }"
			tabindex="-1"
			style="display: block; background-color: rgba(0,0,0,0.5);"
		>
			<div class="modal-dialog modal-dialog-centered">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('cus.createAssetReport') }}
						</h4>
						<button type="button" class="btn-close" @click="showCreateModal = false" />
					</div>
					<div class="modal-body">
						<form>
							<div class="mb-3">
								<label for="reportTitle" class="form-label">{{ $t('cus.reportName') }} <span class="text-danger">*</span></label>
								<input
									id="reportTitle"
									v-model="newReport.reportName"
									type="text"
									class="form-control"
									required
								>
							</div>

							<div class="mb-3">
								<label for="cusCode" class="form-label">{{ $t('cus.cusCode') }} <span class="text-danger">*</span></label>
								<div class="input-group">
									<input
										id="cusCode"
										v-model="newReport.settings.cusCode"
										type="text"
										class="form-control"
										required
									>
									<button class="btn btn-outline-secondary" type="button" @click="searchCustomer">
										<i class="fa-solid fa-search" />
									</button>
								</div>
								<small v-if="customerName" class="form-text text-success">
									{{ customerName }}
								</small>
							</div>

							<div class="row">
								<div class="col-md-6 mb-3">
									<label for="beginDate" class="form-label">{{ $t('adm.startDate') }}</label>
									<input
										id="beginDate"
										v-model="newReport.settings.beginDate"
										type="date"
										class="form-control"
									>
								</div>
								<div class="col-md-6 mb-3">
									<label for="endDate" class="form-label">{{ $t('adm.endDate') }}</label>
									<input
										id="endDate"
										v-model="newReport.settings.endDate"
										type="date"
										class="form-control"
									>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-white" @click="showCreateModal = false">
							{{ $t('cus.cancel') }}
						</button>
						<button type="button" class="btn btn-primary" @click="createReport">
							{{ $t('cus.add') }}
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>

export default {
	data() {
		return {
			reportList: [],

			showCreateModal: false,
			customerName: '',
			newReport: {
				reportName: '資產報告書',
				settings: {
					cusCode: '',
					beginDate: '',
					endDate: ''
				},
				pages: []
			}
		};
	},
	async created() {
		await this.getAssetReportList();
	},
	methods: {
		async getAssetReportList() {
			const result = await this.$api.getAssetReportList();
			this.reportList = result.data || [];
			console.log('資產報告書清單:', this.reportList);
		},
		gotoCreateReport() {
			this.showCreateModal = true;
		},
		// 創建報告書
		async createReport() {
			try {
				this.showCreateModal = false;

				// 建立報告書，取得id
				const result = await this.$api.createAssetReport(this.newReport);
				console.log('新報告書ID:', result);
				if (result.data) {
					this.$router.push({
						name: 'createAssetReport',
						params: { id: result.data }
					});

					this.resetForm();
				}
				else {
					throw new Error(this.$t('cus.err-cannotGetNewReportId'));
				}
			}
			catch (error) {
				console.error('創建報告出錯:', error);
				this.$notify({
					type: 'error',
					title: this.$t('cus.createFailed'),
					text: error.message || this.$t('cus.createFailed')
				});
			}
		},
		resetForm() {
			this.newReport = {
				reportName: this.$t('cus.assetReport'),
				settings: {
					cusCode: '',
					beginDate: '',
					endDate: ''
				},
				pages: []
			};
			this.customerName = '';
		},
		editReport(report) {
			console.log('reportId:', report.reportId);
			this.$router.push({
				name: 'createAssetReport',
				params: { id: report.reportId }
			});
		},
		async deleteReport(report) {
			console.log('reportId:', report.reportId);
			if (confirm(this.$t('cus.confirmDeleteReport', [report.reportName || report.reportName]))) {
				try {
					await this.$api.deleteAssetReport({ reportId: report.reportId });
					await this.getAssetReportList();
					this.$notify && this.$notify({
						type: 'success',
						title: this.$t('cus.deleteSuccess'),
						text: this.$t('cus.deleteSuccess')
					});
				}
				catch (error) {
					console.error('刪除報告失敗:', error);
					this.$notify && this.$notify({
						type: 'error',
						title: this.$t('cus.deleteFailed'),
						text: error.message || this.$t('cus.deleteFailed')
					});
				}
			}
		}
	}
};
</script>
