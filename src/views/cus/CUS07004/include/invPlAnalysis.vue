<template>
	<div ref="container" class="component-container">
		<div class="card card-table mb-3">
			<div class="card-header d-flex justify-content-between align-items-center">
				<h4 class="mb-0">
					{{ $t('cus.profitLoss') }}分析
				</h4>
				<div class="tx-memo">
					{{ $t('cus.newTaiwanDollarUnit') }}
				</div>
			</div>
			<div class="table-responsive">
				<ag-grid-vue
					ref="agGrid"
					:row-data="rowData"
					:column-defs="translatedColumnDefs"
					style="width: 100%"
					:dom-layout="'autoHeight'"
					:enable-charts="true"
					:cell-selection="true"
				/>
			</div>
		</div>
	</div>
</template>
<script>
import { AgGridVue } from 'ag-grid-vue3';
export default {
	components: {
		AgGridVue
	},
	props: {
		cusCode: {
			type: String,
			default: '' // 預設客戶代號
		},
		uuid: {
			type: String,
			required: false
		}
	},
	data() {
		return {
			rowData: [],
			columnDefs: [
				{ headerName: '產品種類', field: 'product', pinned: 'left' },
				{ headerName: '投資本金(含手續費)', field: 'balance' },
				{ headerName: '投資現值', field: 'marketValue' },
				{ headerName: '報酬率', field: 'roi' },
				{ headerName: '已實現損益', field: 'realizedProfit' },

				{
					headerName: '本季', children: [
						{ headerName: '投資本金(含手續費)', field: 'q_principal' },
						{ headerName: '報酬率', field: 'q_roi' },
						{ headerName: '區間報酬率', field: 'q_range_roi' },
						{ headerName: '已實現損益', field: 'q_profit' }
					]
				},

				{
					headerName: '年初至今', children: [
						{ headerName: '投資本金(含手續費)', field: 'ytd_principal' },
						{ headerName: '報酬率', field: 'ytd_roi' },
						{ headerName: '區間報酬率', field: 'ytd_range_roi' },
						{ headerName: '已實現損益', field: 'ytd_profit' }
					]
				},

				{
					headerName: '去年', children: [
						{ headerName: '投資本金(含手續費)', field: 'ly_principal' },
						{ headerName: '報酬率', field: 'ly_roi' },
						{ headerName: '區間報酬率', field: 'ly_range_roi' }
					]
				}
			]
		};
	},
	computed: {
		translatedColumnDefs() {
			return this.columnDefs.map((col) => {
				if (col.children) {
					return {
						...col,
						children: col.children.map(child => ({
							...child,
							headerName: this.getTranslatedHeaderName(child.headerName)
						}))
					};
				}
				else {
					return {
						...col,
						headerName: this.getTranslatedHeaderName(col.headerName)
					};
				}
			});
		}
	},
	watch: {
		'$i18n.locale': {
			handler() {
				// 當語言切換時，強制重新渲染 ag-grid
				this.$nextTick(() => {
					if (this.$refs.agGrid && this.$refs.agGrid.api) {
						this.$refs.agGrid.api.setColumnDefs(this.translatedColumnDefs);
					}
				});
			},
			immediate: true
		}
	},
	mounted() {
		const self = this;
		self.getGridData();
		this.$nextTick(() => {
			this.setupResizeObserver();
			this.reportHeight();
		});
	},
	updated() {
		this.$nextTick(() => {
			this.reportHeight();
		});
	},
	beforeUnmount() {
		if (this.resizeObserver) {
			this.resizeObserver.disconnect();
		}
	},
	methods: {
		getTranslatedHeaderName(headerName) {
			const translations = {
				'產品種類': this.$t('pro.proType'),
				'投資本金(含手續費)': this.$t('cus.investmentPrincipalIncludingFees'),
				'投資現值': this.$t('cus.currentValue'),
				'報酬率': this.$t('cus.returnRate'),
				'已實現損益': this.$t('cus.realizedProfitLoss')
			};

			return translations[headerName] || headerName;
		},
		getGridData: async function () {
			const self = this;
			const resp = await self.$api.getPlAnalysisApi({ cusCode: this.cusCode });
			self.rowData = resp.data;
		},
		// 設定 ResizeObserver 監控高度變化
		setupResizeObserver() {
			if (window.ResizeObserver) {
				this.resizeObserver = new ResizeObserver((entries) => {
					for (const entry of entries) {
						const height = entry.contentRect.height;
						this.reportHeight(height);
					}
				});

				this.resizeObserver.observe(this.$refs.container);
			}
		},

		// 回報高度給父元件
		reportHeight(customHeight = null) {
			const height = customHeight || this.$refs.container?.offsetHeight || 0;
			if (height > 0 && this.uuid) {
				this.$emit('height-changed', { uuid: this.uuid, height });
			}
		}
	}
};
</script>
