<template>
	<div ref="container" class="component-container">
		<div class="card card-table mb-3">
			<div class="card-header d-flex justify-content-between align-items-center">
				<h4 class="mb-0">
					{{ $t('cus.assetStatusInvestmentAllocation') }}
				</h4>
			</div>
			<div class="table-responsive">
				<div class="row align-items-center">
					<div class="col-xl-4 text-center">
						<div id="chart-All" class="dbchart-container" style="height:280px;">
							<!-- chart -->
						</div>
					</div>
					<div class="col-xl-8">
						<div class="text-muted text-end mb-2">
							{{ $t('cus.dataDate') }}：2025/04/04
						</div>
						<table class="bih-table table table-striped table-hover">
							<thead class="table-dark">
								<tr>
									<th width="30%">
										{{ $t('cus.productCategory') }}
									</th>
									<th width="10%">
										{{ $t('cus.allocationRatio') }}
									</th>
									<th width="23%">
										{{ $t('cus.originalInvestmentAmount') }}
									</th>
									<th width="23%">
										{{ $t('cus.marketValue') }}
									</th>
									<th width="10%">
										{{ $t('cus.returnRate') }}
									</th>
									<th width="10%">
										{{ $t('cus.dailyChange') }}
									</th>
									<th width="10%">
										{{ $t('cus.weeklyChange') }}
									</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td :data-th="$t('cus.productCategory')">
										<a href="#">{{ $t('cus.deposit') }}</a>
									</td>
									<td class="text-end">
										17.50%
									</td>
									<td class="text-end">
										472,385.43
									</td>
									<td class="text-end">
										482,400.00
									</td>
									<td class="text-end">
										2.12%
									</td>
									<td class="text-end tx-rise">
										<span class="tx-12">▲</span>5.25%
									</td>
									<td class="text-end tx-rise">
										<span class="tx-12">▲</span>3.21%
									</td>
								</tr>
								<tr>
									<td :data-th="$t('cus.productCategory')">
										<a href="#">{{ $t('cus.fund') }}</a>
									</td>
									<td class="text-end">
										8.95%
									</td>
									<td class="text-end">
										220,631.65
									</td>
									<td class="text-end">
										246,600.00
									</td>
									<td class="text-end">
										11.77%
									</td>
									<td class="text-end tx-fall">
										<span class="tx-12">▼</span>1.24%
									</td>
									<td class="text-end tx-rise">
										<span class="tx-12">▲</span>1.21%
									</td>
								</tr>
								<tr>
									<td :data-th="$t('cus.productCategory')">
										<a href="#">{{ $t('cus.stockETF') }}</a>
									</td>
									<td class="text-end">
										22.79%
									</td>
									<td class="text-end">
										554,966.89
									</td>
									<td class="text-end">
										628,500.00
									</td>
									<td class="text-end">
										13.25%
									</td>
									<td class="text-end tx-fall">
										<span class="tx-12">▼</span>3.24%
									</td>
									<td class="text-end tx-fall">
										<span class="tx-12">▼</span>2.21%
									</td>
								</tr>
								<tr>
									<td :data-th="$t('cus.productCategory')">
										<a href="#">{{ $t('cus.bond') }}</a>
									</td>
									<td class="text-end">
										12.70%
									</td>
									<td class="text-end">
										213,312.17
									</td>
									<td class="text-end">
										228,500.00
									</td>
									<td class="text-end">
										7.12%
									</td>
									<td class="text-end tx-fall">
										<span class="tx-12">▼</span>3.24%
									</td>
									<td class="text-end tx-fall">
										<span class="tx-12">▼</span>2.21%
									</td>
								</tr>
								<tr>
									<td :data-th="$t('cus.productCategory')">
										<a href="#">{{ $t('cus.structuredProduct') }}</a>
									</td>
									<td class="text-end">
										8.29%
									</td>
									<td class="text-end">
										289,262.41
									</td>
									<td class="text-end">
										350,210.00
									</td>
									<td class="text-end">
										21.07%
									</td>
									<td class="text-end tx-rise">
										<span class="tx-12">▲</span>1.04%
									</td>
									<td class="text-end tx-fall">
										<span class="tx-12">▼</span>2.21%
									</td>
								</tr>
								<tr>
									<td :data-th="$t('cus.productCategory')">
										<a href="#">{{ $t('cus.projectTrust') }}</a>
									</td>
									<td class="text-end">
										25.75%
									</td>
									<td class="text-end">
										695,466.12
									</td>
									<td class="text-end">
										710,210.00
									</td>
									<td class="text-end">
										2.12%
									</td>
									<td class="text-end tx-rise">
										<span class="tx-12">▲</span>1.04%
									</td>
									<td class="text-end tx-fall">
										<span class="tx-12">▼</span>2.21%
									</td>
								</tr>
								<tr>
									<td :data-th="$t('cus.productCategory')">
										<a href="#">{{ $t('cus.insurance') }}</a>
									</td>
									<td class="text-end">
										4.02%
									</td>
									<td class="text-end">
										100,353.23
									</td>
									<td class="text-end">
										110,800.00
									</td>
									<td class="text-end">
										10.41%
									</td>
									<td class="text-end tx-rise">
										<span class="tx-12">▲</span>1.04%
									</td>
									<td class="text-end tx-fall">
										<span class="tx-12">▼</span>2.21%
									</td>
								</tr>
							</tbody>
							<tfoot>
								<tr class="tr-sum">
									<td>{{ $t('cus.total') }}</td>
									<td class="text-end">
										100.00%
									</td>
									<td class="text-end">
										2,546,377.90
									</td>
									<td class="text-end">
										2,757,220.00
									</td>
									<td class="text-end">
										8.44%
									</td>
									<td class="text-end">
										--
									</td>
									<td class="text-end">
										--
									</td>
								</tr>
							</tfoot>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'PortfolioAnalysis',
	props: {
		uuid: {
			type: String,
			required: false
		}
	},
	data() {
		return {
			resizeObserver: null
		};
	},
	mounted() {
		this.$nextTick(() => {
			this.setupResizeObserver();
			this.reportHeight();
		});
	},
	updated() {
		this.$nextTick(() => {
			this.reportHeight();
		});
	},
	beforeUnmount() {
		if (this.resizeObserver) {
			this.resizeObserver.disconnect();
		}
	},
	methods: {
		setupResizeObserver() {
			if (window.ResizeObserver) {
				this.resizeObserver = new ResizeObserver((entries) => {
					for (const entry of entries) {
						const height = entry.contentRect.height;
						this.reportHeight(height);
					}
				});
				this.resizeObserver.observe(this.$refs.container);
			}
		},
		reportHeight(customHeight = null) {
			const height = customHeight || this.$refs.container?.offsetHeight || 0;
			if (height > 0 && this.uuid) {
				this.$emit('height-changed', { uuid: this.uuid, height });
			}
		}
	}
};
</script>

<style scoped>
.component-container {
  width: 100%;
}
.card-table {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
}

.bih-table {
  font-size: 0.9rem;
}
.bih-table th {
  font-weight: 600;
  white-space: nowrap;
}
.bih-table td {
  white-space: nowrap;
}
.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}
.text-success {
  color: #28a745 !important;
}
.text-danger {
  color: #dc3545 !important;
}
.tx-13 {
  font-size: 0.8rem;
}
.tx-rise {
  color: #28a745;
}
.tx-fall {
  color: #dc3545;
}
@media (max-width: 768px) {
  .dbchart-container {
    height: 200px;
  }
  .bih-table {
    font-size: 0.8rem;
  }
}
</style>
