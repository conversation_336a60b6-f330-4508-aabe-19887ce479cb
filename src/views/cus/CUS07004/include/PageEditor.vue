<template>
	<div class="page-editor">
		<!-- A4 頁面 -->
		<div
			class="a4-page report"
			:class="[getPageBackgroundClass(page.type), { 'preview-mode': previewMode }]"
		>
			<!-- 頁面頂部指示器 -->
			<div v-if="!previewMode" class="page-indicator">
				<span>{{ getPageTypeName(page.type) }} - {{ pageIndex + 1 }}</span>
			</div>

			<!-- 頁面內容區 -->
			<div class="page-content ">
				<!-- 封面頁 -->
				<template v-if="page.type === 'cover'">
					<div class="d-flex align-items-center min-vh-100">
						<div class="d-flex flex-column p-5 tx-reportcoverContainer">
							<span
								class="display-4 mb-4 tx-report-tilte"
								:contenteditable="!previewMode"
								@blur="updateContent('title', $event.target.textContent)"
							>
								{{ page.content?.title || '資產報告書' }}
							</span>
							<h3
								class="mb-5 text-muted p-0"
								:contenteditable="!previewMode"
								@blur="updateContent('subtitle', $event.target.textContent)"
							>
								{{ page.content?.subtitle || '副標題' }}
							</h3>
							<p
								class="lead"
								:contenteditable="!previewMode"
								@blur="updateContent('author', $event.target.textContent)"
							>
								{{ page.content?.author || '作者' }}
							</p>
						</div>
					</div>
				</template>

				<!-- 章節頁 -->
				<template v-else-if="page.type === 'transition'">
					<div class="d-flex align-items-center justify-content-center  min-vh-100">
						<div class="transition-content d-flex flex-column">
							<span
								class="display-5 mb-4 text-center tx-report-tilte"
								:contenteditable="!previewMode"
								@blur="updateContent('title', $event.target.textContent)"
							>
								{{ page.content?.title || '章節標題' }}
							</span>
							<p
								class="lead text-center text-muted"
								:contenteditable="!previewMode"
								@blur="updateContent('description', $event.target.textContent)"
							>
								{{ page.content?.description || '章節描述' }}
							</p>
						</div>
					</div>
				</template>

				<!-- 封底頁 -->
				<template v-else-if="page.type === 'backcover'">
					<div class="d-flex align-items-center justify-content-center  min-vh-100">
						<div class="backcover-content d-flex flex-column justify-content-center align-items-center h-100 p-5">
							<h1
								class="text-center tx-trport"
								:contenteditable="!previewMode"
								@blur="updateContent('content', $event.target.textContent)"
							>
								{{ page.content?.content || '感謝您的閱讀' }}
							</h1>
						</div>
					</div>
				</template>

				<!-- 內容頁 -->
				<template v-else>
					<div
						class="h-100"
						@dragover="handleDragOver"
						@drop="handleDrop"
					>
						<!-- 內容頁標題 -->
						<div class="row">
							<div class="col-3 p-2">
								<h5
									class="border-bottom d-flex align-items-center mx-4"
									contenteditable
									@blur="updateContent('title', $event.target.textContent)"
								>
									{{ page.content?.title || page.title || '頁面標題' }}
								</h5>
							</div>

							<div class="col-9 d-flex align-items-center justify-content-end report-contentHeadBg pe-5">
								<h5
									class="border-bottom d-flex align-items-center mx-4"
									contenteditable
									@blur="updateContent('title', $event.target.textContent)"
								>
									{{ page.content?.title || page.title || '頁面標題' }}
								</h5>
							</div>
						</div>

						<!-- 元件列表 -->
						<template v-if="page.components && page.components.length > 0">
							<div
								v-for="(component, index) in page.components"
								:key="component.componentId"
								class="component-wrapper mb-2"
								:class="{ active: selectedComponent === component.componentId && !previewMode, 'preview-mode': previewMode }"
								@click="selectComponent(component.componentId)"
							>
								<!-- 元件控制項 -->
								<div v-if="!previewMode" v-show="selectedComponent === component.componentId" class="component-controls">
									<button
										class="btn btn-sm btn-outline-secondary"
										:disabled="index === 0"
										@click.stop="moveComponent(index, -1)"
									>
										<i class="bi bi-arrow-up" />
									</button>
									<button
										class="btn btn-sm btn-outline-secondary"
										:disabled="index === page.components.length - 1"
										@click.stop="moveComponent(index, 1)"
									>
										<i class="bi bi-arrow-down" />
									</button>
									<button
										class="btn btn-sm btn-outline-danger"
										@click.stop="removeComponent(index)"
									>
										<i class="bi bi-trash" />
									</button>
								</div>

								<!-- 元件內容 (動態元件) -->
								<component
									:is="getComponentType(component)"
									:component-id="component.componentId"
									:preview-mode="previewMode"
									:data-component-id="component.componentId"
									v-bind="component.content"
									@update="updateComponentContent(component.componentId, $event)"
									@height-changed="handleHeightChanged"
								/>
							</div>
						</template>

						<!-- 空頁面提示 -->
						<div v-else-if="!previewMode && !isSuperTallReservedPage()" class="empty-page d-flex flex-column align-items-center justify-content-center text-muted p-5">
							<i class="bi bi-drag" style="font-size: 2.5rem;" />
							<p class="mt-3">
								拖拉元件至此區域
							</p>
						</div>

						<!-- 超高頁面保留區域 -->
						<div v-else-if="!previewMode && isSuperTallReservedPage()" class="reserved-page d-flex flex-column align-items-center justify-content-center text-muted p-5">
							<i class="bi bi-lock" style="font-size: 2.5rem;" />
							<p class="mt-3">
								此頁面為超高頁面，請在其他頁面添加元件
							</p>
							<p class="text-muted">
								無法加入其他元件
							</p>
						</div>
					</div>
				</template>
			</div>

			<!-- 頁碼 (右下角) -->
			<div v-if="page.type !== 'cover'" class="page-number">
				{{ pageIndex }}
			</div>
		</div>
	</div>
</template>

<script>
// 元件
import textEditor from './textEditor.vue';
import assetLiability from './assetLiability.vue';
import invPlan from './invPlan.vue';
import assetGrowthAnalysis from './assetGrowthAnalysis.vue';
import portfolioAnalysis from './portfolioAnalysis.vue';
import assetTypePie from './assetTypePie.vue';
import invTarget from './invTarget.vue';
import invPlAnalysis from './invPlAnalysis.vue';
import assetChange from './assetChange.vue';
import realizedProfitLoss from './realizedProfitLoss.vue';
import assetLoanChange from './assetLoanChange.vue';
import assetCategory from './assetCategory.vue';
import netValAnalysis from './netValAnalysis.vue';
import cusInvestAnalysisInvTransaction from './cusInvestAnalysisInvTransaction.vue';
import invTxAmt from './invTxAmt.vue';
import realizeProfitSrch from './realizeProfitSrch.vue';

export default {
	name: 'PageEditor',
	components: {
		textEditor,
		assetLiability,
		invPlan,
		assetGrowthAnalysis,
		portfolioAnalysis,
		assetTypePie,
		invTarget,
		invPlAnalysis,
		assetChange,
		realizedProfitLoss,
		assetLoanChange,
		assetCategory,
		netValAnalysis,
		cusInvestAnalysisInvTransaction,
		invTxAmt,
		realizeProfitSrch
	},
	props: {
		// 頁面數據
		page: {
			type: Object,
			required: true
		},
		// 頁面索引
		pageIndex: {
			type: Number,
			default: 0
		},
		// 預覽模式
		previewMode: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			selectedComponent: null
		};
	},
	methods: {
		// 取得頁面背景 class
		getPageBackgroundClass(type) {
			const backgroundClasses = {
				cover: 'report-coverbg',
				transition: 'report-cutscenebg',
				content: 'report-contentbg',
				backcover: 'report-backcoverbg'
			};

			return backgroundClasses[type] || 'report-contentbg';
		},
		// 更新頁面內容
		updateContent(key, value) {
			if (this.previewMode) return;

			if (!this.page.content) this.page.content = {};
			this.page.content[key] = value;
			this.$emit('page-updated', this.page);
		},

		// 拖放相關
		handleDragOver(event) {
			if (this.previewMode) return;

			// 如果是超高元件的預留頁，不允許拖放
			if (this.isSuperTallReservedPage()) {
				event.dataTransfer.dropEffect = 'none';
				return;
			}

			event.preventDefault();
			event.dataTransfer.dropEffect = 'copy';
		},

		handleDrop(event) {
			if (this.previewMode) return;

			// 如果是超高元件的預留頁，不允許拖放
			if (this.isSuperTallReservedPage()) {
				alert('此頁面已預留給超高元件，無法加入其他元件');
				return;
			}

			event.preventDefault();
			event.stopPropagation(); // 阻止事件傳播
			const data = event.dataTransfer.getData('component');
			if (data) {
				try {
					const componentData = JSON.parse(data);
					this.addComponent(componentData);
				}
				catch (error) {
					console.error('Failed to parse component data', error);
				}
			}
		},

		addComponent(componentData) {
			debugger;
			if (!this.page.components) this.page.components = [];

			// 防重複檢查
			const timestamp = Date.now();
			const isDuplicate = this.page.components.some(comp =>
				comp.elementId === componentData.elementId
				&& timestamp - parseInt(comp.componentId.split('-')[1]) < 500
			);

			if (isDuplicate) return;

			// 檢查頁面是否為超高元件的預留頁
			if (this.isSuperTallReservedPage()) {
				alert('此頁面已被預留給超高元件，無法加入其他元件');
				return;
			}

			const newComponent = { ...componentData,
				componentId: `comp-${timestamp}-${Math.floor(Math.random() * 1000)}`,
				content: componentData.defaultContent || {}
			};

			console.log('新增元件:', newComponent);
			this.page.components.push(newComponent);
			this.$emit('component-added', newComponent);
			this.$emit('page-updated', this.page);
			this.selectedComponent = newComponent.componentId;

			// 如果是文字元件，立即檢查溢出（固定高度74px）
			if (newComponent.elementId === 'text') {
				this.$nextTick(() => {
					this.checkOverflow({ componentId: newComponent.componentId, height: 74 });
				});
			}
		},

		moveComponent(fromIndex, direction) {
			const toIndex = fromIndex + direction;
			if (toIndex < 0 || toIndex >= this.page.components.length) return;

			// 交換位置
			const temp = this.page.components[toIndex];
			this.page.components[toIndex] = this.page.components[fromIndex];
			this.page.components[fromIndex] = temp;

			this.$emit('page-updated', this.page);
		},

		removeComponent(index) {
			this.page.components.splice(index, 1);
			this.selectedComponent = null;
			this.$emit('page-updated', this.page);
		},

		selectComponent(componentId) {
			if (this.previewMode) return;

			this.selectedComponent = this.selectedComponent === componentId ? null : componentId;
		},

		updateComponentContent(componentId, content) {
			if (this.previewMode) return;

			const component = this.page.components.find(c => c.componentId === componentId);
			if (component) {
				// 保持元件狀態不被覆蓋
				const preservedStates = {
					isSuperTall: component.isSuperTall,
					componentId: component.componentId,
					elementId: component.elementId,
					elementName: component.elementName
				};
				component.content = { ...component.content, ...content };

				// 恢復元件狀態
				Object.assign(component, preservedStates);

				this.$emit('page-updated', this.page);
			}
		},

		// 檢查頁面是否為超高元件的預留頁
		isSuperTallReservedPage() {
			return this.page.reservedForSuperTall === true;
		},

		// 檢查頁面overflow
		async checkOverflow({ componentId, height }) {
			const pageContentHeight = 1023; // A4 高度減去 padding
			let totalHeight = 0;
			let overflowIndex = -1;

			// 如果頁面包含已標記的超高元件，或頁面為超高元件預留頁，跳過正常溢出檢查
			if (this.isSuperTallReservedPage() || this.page.isSuperTallPage) {
				console.warn('頁面為超高元件預留頁或包含超高元件，跳過溢出檢查');
				return;
			}

			// 遍歷所有元件計算高度
			for (let i = 0; i < this.page.components.length; i++) {
				const component = this.page.components[i];
				let componentHeight = 0;

				if (component.componentId === componentId) {
					// 當前元件使用傳入的高度
					componentHeight = height;
				}
				else {
					// 已存在元件使用 getBoundingClientRect
					await this.$nextTick();
					const element = document.querySelector(`[data-component-id="${component.componentId}"]`); // need confirm
					if (element) {
						componentHeight = element.getBoundingClientRect().height;
					}
					else {
						// 如果找不到元素，給予預估高度
						componentHeight = component.elementId === 'text' ? 74 : 200;
					}
				}

				totalHeight += componentHeight;

				// 檢查是否超過頁面高度
				if (totalHeight > pageContentHeight) {
					overflowIndex = i;
					break;
				}
			}

			// 如果發生溢出
			if (overflowIndex >= 0) {
				console.log(`頁面溢出，從索引 ${overflowIndex} 開始的元件需要移動到下一頁`);

				// 切出溢出的元件
				const overflowComponents = this.page.components.splice(overflowIndex);

				// 發出溢出事件，由父元件處理新增頁面
				this.$emit('page-overflow', {
					pageIndex: this.pageIndex,
					overflowComponents: overflowComponents
				});

				// 更新當前頁面
				this.$emit('page-updated', this.page);
			}
		},

		// 處理元件高度變化
		handleHeightChanged({ componentId, height }) {
			console.log(`元件 ${componentId} 高度變更為 ${height}px`);

			// 檢查是否為超高元件（高度超過一頁）
			if (height > 1023) {
				console.log('檢測到超高元件，需要特殊處理');

				// 找到超高元件
				const superTallComponent = this.page.components.find(comp => comp.componentId === componentId);

				if (!superTallComponent) {
					console.warn(`找不到元件 ${componentId}，無法處理超高元件`);
					return;
				}
				// 如果超高元件已被標記isSuperTall，代表已經被處理過
				if (superTallComponent.isSuperTall) return;

				if (superTallComponent) {
					// 從當前頁面移除超高元件
					const componentIndex = this.page.components.findIndex(comp => comp.componentId === componentId);
					this.page.components.splice(componentIndex, 1);

					// 計算需要的頁面數
					const requiredPages = Math.ceil(height / 1023);

					// 發出超高元件事件
					this.$emit('super-tall-component-added', {
						component: superTallComponent,
						pageIndex: this.pageIndex,
						requiredPages: requiredPages
					});

					// 更新當前頁面
					this.$emit('page-updated', this.page);
				}
			}
			else {
				// 正常元件，檢查頁面溢出
				this.checkOverflow({ componentId, height });
			}
		},

		// 輔助方法
		getComponentType(component) {
			debugger;
			// 根據元件ID映射到實際組件
			const componentMap = {
				'text': 'textEditor',
				'ov-asset': 'assetLiability',
				'ov-need': 'invPlan',
				'ov-growth': 'assetGrowthAnalysis',
				'ov-move': 'assetChange',
				'ov-pl': 'realizedProfitLoss',
				'ov-nav': 'assetCategory',
				'ov-liability': 'assetLoanChange',
				'trade-search': 'cusInvestAnalysisInvTransaction',
				'plan-trade': 'cusInvestAnalysisInvTransaction',
				'pie': 'assetTypePie',
				'perf-nav': 'netValAnalysis',
				'perf-pl': 'invPlAnalysis',
				'perf-growth': 'assetGrowthAnalysis',
				'perf-portfolio': 'invTarget',
				'amount': 'invTxAmt',
				'realized': 'realizeProfitSrch'
			};
			debugger;
			return componentMap[component.elementId] || 'div';
		},

		getPageTypeName(type) {
			const typeNames = {
				cover: '封面',
				content: '內容',
				transition: '章節',
				backcover: '封底'
			};

			return typeNames[type] || '頁面';
		}
	}
};
</script>

<style scoped>
.page-editor {
  width: 100%;
  margin-bottom: 40px;
}

.a4-page {
  width: 794px; /* A4 寬度 (21cm) */
  min-height: 1123px; /* A4 高度 (29.7cm) */
  margin: 0 auto;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
}

.page-indicator {
  position: absolute;
  top: -24px;
  left: 0;
  font-size: 14px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 4px;
}

.page-content {
  height: 100%;
  overflow: hidden;
}

/* 頁碼樣式 */
.page-number {
  position: absolute;
  bottom: 10mm;
  right: 10mm;
  font-size: 14px;
  color: #333;
  font-family: Arial, sans-serif;
  z-index: 10;
  user-select: none; /* 防止被選取 */
}

.component-wrapper {
  position: relative;
  border: 1px solid transparent;
  padding: 5px;
  transition: all 0.2s;
  border-radius: 4px;
}

.component-wrapper:hover {
  border-color: #e0e0e0;
}

.component-wrapper.active {
  border-color: #007bff;
}

.component-controls {
  position: absolute;
  top: -16px;
  right: 10px;
  display: flex;
  gap: 4px;
  background: white;
  border-radius: 4px;
  padding: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 20;
}

.empty-page {
  height: 300px;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
}

.reserved-page {
  height: 300px;
  border: 2px dashed #ffc107;
  border-radius: 8px;
  background: rgba(255, 193, 7, 0.1);
}

[contenteditable]:hover {
  outline: 1px dashed #007bff;
  border-radius: 2px;
}

[contenteditable]:focus {
  outline: 2px solid #007bff;
  border-radius: 2px;
}

/* 預覽模式專用樣式 */
.component-wrapper.preview-mode {
  border: none !important;
  cursor: default;
}

.component-wrapper.preview-mode:hover {
  border: none !important;
}

/* 預覽模式下取消所有 contenteditable 的 hover/outline 效果 */
.a4-page.report.preview-mode [contenteditable="false"]:hover,
.a4-page.report.preview-mode [contenteditable="false"]:focus,
.a4-page.report.preview-mode [contenteditable="false"] {
  outline: none !important;
  box-shadow: none !important;
  cursor: default !important;
}
</style>
