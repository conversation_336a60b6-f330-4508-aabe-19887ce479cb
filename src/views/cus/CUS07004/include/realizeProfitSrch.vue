<template>
	<div ref="container" class="component-container">
		<div class="card card-table mb-3">
			<div class="card-header d-flex justify-content-between align-items-center">
				<h4 class="mb-0">
					{{ $t('cus.realizedPlSrch') }}
				</h4>
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-bordered">
					<thead>
						<tr>
							<th>{{ $t('cus.accNo') }}<br>{{ $t('pro.proName') }}</th>
							<th>{{ $t('cus.transactionDate') }}<br>{{ $t('cus.transactionType') }}</th>
							<th>{{ $t('cus.currency') }}</th>
							<th>{{ $t('cus.transactionAmount') }}<br>({{ $t('pro.originalCurrency') }})</th>
							<th>{{ $t('cus.originalInvestmentAmount') }}<br>({{ $t('pro.originalCurrency') }})</th>
							<th>{{ $t('cus.originalInvestmentAmount') }}<br>({{ $t('pro.localCurrency') }})</th>
							<th>{{ $t('cus.profitLoss') }}<br>({{ $t('pro.originalCurrency') }})</th>
							<th>{{ $t('cus.profitLoss') }}<br>({{ $t('pro.localCurrency') }})</th>
							<th class="text-center bg-lightblue tx-primary">
								{{ $t('cus.exchangeRatePl') }}<br>({{ $t('pro.localCurrency') }})
							</th>
							<th class="text-center bg-lightblue tx-primary">
								{{ $t('cus.dividendIncome') }}<br>({{ $t('pro.localCurrency') }})
							</th>
							<th class="text-center bg-lightblue tx-primary">
								{{ $t('cus.fee') }}<br>({{ $t('pro.localCurrency') }})
							</th>
							<th>{{ $t('cus.actualPl') }}<br>({{ $t('pro.localCurrency') }})</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="(item, index) in pageData" :key="index">
							<td>{{ item.accNo }}<br>{{ item.proName }}</td>
							<td>{{ $filters.formatDate(item.tranDt) }}<br>{{ item.tranType }}</td>
							<td>{{ item.tranCurCode }}</td>
							<td>{{ item.tranAmtNoFeeFc }}</td>
							<td>{{ item.invAmtFc }}</td>
							<td>{{ item.invAmtLc }}</td>
							<td>{{ item.plNofeeFc }}</td>
							<td>{{ item.plNofeeLc }}</td>
							<td>{{ exchangeRate(item.tranAmtLc, item.invAmtLc, item.plNofeeLc, item.totDividendLc, item.tranFeeLc) }}</td>
							<td>{{ item.totDividendLc }}</td>
							<td>{{ item.tranFeeLc }}</td>
							<td>{{ actualProfit(item.plNofeeLc, item.tranAmtLc, item.invAmtLc, item.totDividendLc, item.tranFeeLc) }}</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>
<script>
import moment from 'moment';
export default {
	props: {
		props: {
			componentId: {
				type: String,
				required: true
			},
			cusCode: {
				type: String,
				default: ''
			},
			beginDate: {
				type: String,
				default: ''
			},
			endDate: {
				type: String,
				default: ''
			},
			componentData: {
				type: Object,
				default: () => ({})
			}
		}
	},
	data() {
		return {
			form: {
				cusCode: null,
				tranDtB: null,
				tranDtE: null
			},
			pageData: [],
			lastReportHeight: 0
		};
	},
	updated() {
		this.$nextTick(() => {
			this.reportHeight();
		});
	},
	mounted() {
		const self = this;
		self.form.cusCode = self.cusCode;
		self.form.tranDtB = moment().format(this.beginDate || 'YYYY-MM-DD'); // 交易日期起
		self.form.tranDtE = moment().format(this.endDate || 'YYYY-MM-DD'); // 交易日期迄; // 交易日期起 (預設一個月前)

		self.search();

		this.$nextTick();
	},
	methods: {
		search: async function () {
			const self = this;

			const resp = await self.$api.getRealizeProfitApi(self.form);
			self.pageData = resp.data;
		},
		exchangeRate: function (tranAmtLc, invAmtLc, plNofeeLc, totDividendLc, tranFeeLc) {
			return (tranAmtLc - invAmtLc) - (plNofeeLc + totDividendLc - tranFeeLc);
		},
		actualProfit: function (plNofeeLc, tranAmtLc, invAmtLc, totDividendLc, tranFeeLc) {
			const self = this;
			return plNofeeLc + self.exchangeRate(tranAmtLc, invAmtLc, plNofeeLc, totDividendLc, tranFeeLc) + totDividendLc - tranFeeLc;
		},

		// 回報高度給父元件
		reportHeight() {
			const height = this.$refs.container?.offsetHeight || 0;

			if (this.lastReportHeight === height) {
				return; // 如果高度沒有變化，則不報告
			}

			console.log(`realizeProfitSrch 報告高度變化: ${this.lastReportHeight} -> ${height}`);
			this.lastReportHeight = height;

			if (height > 0 && this.componentId) {
				this.$emit('height-changed', { componentId: this.componentId, height });
			}
		}
	}
};
</script>
