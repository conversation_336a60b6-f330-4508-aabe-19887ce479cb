<template>
	<div ref="container" class="component-container">
		<div class="card card-table mb-3">
			<div class="card-header d-flex justify-content-between align-items-center">
				<h4 class="mb-0">
					{{ $t('cus.assetTypePie') }}
				</h4>
			</div>
			<div class="table-responsive">
				<div class="row align-items-center">
					<div class="col-xl-4 text-center mt-4 align-self-start">
						<PieChart
							v-if="chartData.length > 0"
							ref="asset-type-pie"
							:chart-id="'asset-type-pie'"
							:chart-data="chartData"
						/>
					</div>
					<div class="col-lg-8 mt-4">
						<table class="bih-table table table-striped table-hover">
							<thead>
								<tr>
									<th>
										<span>{{ $t('cus.assetCategory') }}</span>
									</th>
									<th>
										<span>{{ $t('cus.marketValue') }}</span>
									</th>
									<th>
										<span>{{ $t('cus.profitLoss') }}</span>
									</th>
									<th>
										<span>{{ $t('cus.returnRate') }}</span>
									</th>
									<th>
										<span>{{ $t('cus.allocationRatio') }}</span>
									</th>
								</tr>
							</thead>
							<tbody class="text-end">
								<tr>
									<td class="text-start">
										貨幣市場
									</td>
									<td />
									<td />
									<td />
									<td />
								</tr>
								<tr>
									<td class="text-start">
										　貨幣型基金
									</td>
									<td>-</td>
									<td>-</td>
									<td>-</td>
									<td>-</td>
								</tr>
								<tr>
									<td class="text-start">
										　貨幣型結構商品
									</td>
									<td>81,009,312</td>
									<td>891,102.43</td>
									<td>1.10%</td>
									<td>5.58%</td>
								</tr>
								<tr>
									<td class="text-start">
										債券市場
									</td>
									<td />
									<td />
									<td />
									<td />
								</tr>
								<tr>
									<td class="text-start">
										　實體債券
									</td>
									<td>900,000,000</td>
									<td>28,080,000.00</td>
									<td>3.12%</td>
									<td>65.00%</td>
								</tr>
								<tr>
									<td class="text-start">
										　結構型債券
									</td>
									<td>-</td>
									<td>-</td>
									<td>-</td>
									<td>-</td>
								</tr>
								<tr>
									<td class="text-start">
										　債券型基金
									</td>
									<td>109,981,288</td>
									<td>4,619,214.10</td>
									<td>4.20%</td>
									<td>7.94%</td>
								</tr>
								<tr>
									<td class="text-start">
										股票市場
									</td>
									<td />
									<td />
									<td />
									<td />
								</tr>
								<tr>
									<td class="text-start">
										　國內股票
									</td>
									<td>30,098,100</td>
									<td class="tx-red">
										- 391,275.30
									</td>
									<td class="tx-red">
										- 1.30%
									</td>
									<td>2.17%</td>
								</tr>
								<tr>
									<td class="text-start">
										　美國股票
									</td>
									<td>200,300,009</td>
									<td>25,117,621.13</td>
									<td>12.54%</td>
									<td>14.47%</td>
								</tr>
								<tr>
									<td class="text-start">
										　股票型基金
									</td>
									<td>-</td>
									<td>-</td>
									<td>-</td>
									<td>-</td>
								</tr>
								<tr>
									<td class="text-start">
										另類投資
									</td>
									<td />
									<td />
									<td />
									<td />
								</tr>
								<tr>
									<td class="text-start">
										　PE(Private Equity)
									</td>
									<td>-</td>
									<td>-</td>
									<td>-</td>
									<td>-</td>
								</tr>
								<tr>
									<td class="text-start">
										　衍伸性商品(權益數)
									</td>
									<td>38,190,120</td>
									<td>-</td>
									<td>-</td>
									<td>2.76%</td>
								</tr>
								<tr>
									<td colspan="5" class="border-0" />
								</tr>
								<tr>
									<td class="text-start">
										現金
									</td>
									<td>1,000,000</td>
									<td>-</td>
									<td>-</td>
									<td>0.07%</td>
								</tr>
								<tr>
									<td class="text-start">
										保險
									</td>
									<td>23,987,109</td>
									<td>-</td>
									<td>-</td>
									<td>1.73%</td>
								</tr>
								<tr class="tr-sum">
									<td class="text-start">
										投資現值
									</td>
									<td class="num">
										1,384,565,938
									</td>
									<td class="num">
										37,310,697
									</td>
									<td class="num">
										-
									</td>
									<td class="num">
										100%
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import PieChart from './amchart/PieChart.vue';
export default {
	name: 'AssetTypePie',
	components: {
		PieChart
	},
	props: {
		uuid: {
			type: String,
			required: true
		},
		cusCode: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			chartData: [
				{ value: 17.50, name: '存款' },
				{ value: 8.95, name: '基金' },
				{ value: 22.79, name: '股票/ETF' },
				{ value: 12.70, name: '債券' },
				{ value: 8.29, name: '結構型商品' },
				{ value: 25.75, name: '專案信託' },
				{ value: 4.02, name: '保險' }
			],
			resizeObserver: null
		};
	},
	mounted() {
		this.$nextTick(() => {
			this.setupResizeObserver();
			this.reportHeight();
		});
	},
	updated() {
		this.$nextTick(() => {
			this.reportHeight();
		});
	},
	beforeUnmount() {
		if (this.resizeObserver) {
			this.resizeObserver.disconnect();
		}
	},
	methods: {
		// 設定 ResizeObserver 以監控容器高度變化
		setupResizeObserver() {
			if (window.ResizeObserver) {
				this.resizeObserver = new ResizeObserver((entries) => {
					for (const entry of entries) {
						const height = entry.contentRect.height;
						this.reportHeight(height);
					}
				});
				this.resizeObserver.observe(this.$refs.container);
			}
		},

		// 回報高度給父元件
		reportHeight(customHeight = null) {
			const height = customHeight || this.$refs.container?.offsetHeight || 0;
			if (height > 0 && this.uuid) {
				this.$emit('height-changed', { uuid: this.uuid, height });
			}
		}
	}
};
</script>
<style>
.component-container {
    width: 100%;
}
.bih-table {
    font-size: 0.9rem;
}
.bih-table th {
    font-weight: 600;
    white-space: nowrap;
}
.bih-table td {
    white-space: nowrap;
}
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}
</style>
