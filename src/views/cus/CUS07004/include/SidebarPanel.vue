<template>
	<div class="d-flex h-100">
		<!-- 工具列 -->
		<div class="side-toolbar d-flex flex-column align-items-center py-3 bg-white border-end" style="width: 70px;">
			<div
				v-for="item in toolbarItems"
				:key="item.type"
				class="sidebar-icon-group text-center py-2 w-100"
				:class="{ active: panel === item.type }"
				style="cursor:pointer;"
				@click="toggle(item.type)"
			>
				<img
					:src="item.icon"
					alt=""
					style="width:32px;height:32px;display:block;margin:0 auto;"
				>
				<div class="sidebar-label mt-1" style="font-size:13px;">
					{{ item.label }}
				</div>
			</div>
		</div>

		<!-- 工具列內容 -->
		<transition name="slide">
			<div
				v-if="panel"
				class="block-panel bg-white border-end"
				style="width: 240px; min-width: 180px; max-width: 320px; box-shadow: 0 0 8px rgba(0,0,0,0.04);"
			>
				<div class="d-flex justify-content-between align-items-center px-3 py-2 border-bottom">
					<span v-if="panel === 'page'">{{ $t('cus.pages') }}</span>
					<span v-else-if="panel === 'element'">{{ $t('cus.components') }}</span>
					<button class="btn btn-close" title="關閉" @click="toggle(null)" />
				</div>
				<div class="p-3" style="height:calc(100vh - 56px - 48px);overflow-y:auto;">
					<!-- 1. 頁面管理內容 -->
					<div v-if="panel === 'page'">
						<div class="mb-3">
							<select v-model="newPageType" class="form-select form-select-sm mb-2">
								<option value="cover">
									封面頁
								</option>
								<option value="content">
									內容頁
								</option>
								<option value="transition">
									章節頁
								</option>
								<option value="backcover">
									封底頁
								</option>
							</select>
							<button class="btn btn-primary btn-sm w-100" @click="addNewPage">
								<i class="bi bi-plus" /> 新增頁面
							</button>
						</div>

						<!-- 頁面清單 -->
						<div class="fw-bold mb-2">
							頁面清單
						</div>
						<div class="page-list">
							<div
								v-for="(page, index) in pages"
								:key="page.id"
								class="page-item mb-2 p-2"
								:class="{ active: page.id === activePageId, 'is-dragging': draggedPage === page.id }"
								draggable="true"
								@click="selectPage(page.id)"
								@dragstart="onPageDragStart($event, page.id, index)"
								@dragover.prevent="onPageDragOver($event, index)"
								@dragenter.prevent="onPageDragEnter($event, index)"
								@dragleave="onPageDragLeave($event)"
								@drop="onPageDrop($event, index)"
								@dragend="onPageDragEnd"
							>
								<div class="d-flex align-items-center">
									<div class="page-handle me-2">
										<i class="bi bi-grip-vertical text-muted" />
									</div>
									<div class="page-thumbnail me-2">
										<i :class="getPageTypeIcon(page.type)" />
									</div>
									<div class="flex-grow-1">
										<div class="page-title">
											{{ page.title }}
										</div>
										<small class="page-type text-muted">{{ getPageTypeName(page.type) }}</small>
									</div>
									<button
										v-if="pages.length > 1"
										class="btn btn-sm btn-outline-danger ms-2"
										@click.stop="deletePage(page.id)"
									>
										<i class="bi bi-trash" />
									</button>
								</div>
							</div>
						</div>
					</div>

					<!-- 2. 元件內容 -->
					<div v-else-if="panel === 'element'">
						<div>
							<ul class="aside-menu list-unstyled">
								<li v-for="category in elementCategories" :key="category">
									<button
										class="btn btn-link w-100 d-flex justify-content-between align-items-center text-start"
										@click="toggleCategory(category)"
									>
										<span>{{ getCategoryName(category) }}</span>
										<i :class="expanded[category] ? 'bi bi-chevron-down' : 'bi bi-chevron-right'" />
									</button>
									<ul v-show="expanded[category]" class="list-group ms-3 mb-2">
										<li
											v-for="comp in elementMenu[category] || []"
											:key="comp.elementId"
											class="list-group-item d-flex align-items-center justify-content-between element-gap"
											draggable="true"
											@dragstart="onDragStart(comp, $event)"
										>
											<span>{{ $t(comp.elementName) }}</span>
										</li>
									</ul>
								</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</transition>
	</div>
</template>

<script>
import reportPageIcon from '@/assets/images/icon/report-page.svg';
import reportElementIcon from '@/assets/images/icon/report-element.svg';
import { useI18n } from 'vue-i18n';

export default {
	name: 'SidebarPanel',
	props: {
		// 頁面列表
		pages: {
			type: Array,
			default: () => []
		},
		// 當前活動頁面ID
		activePageId: {
			type: String,
			default: null
		}
	},
	data() {
		return {
			panel: null,
			newPageType: 'content',
			expanded: {
				basic: true,
				overview: true,
				plan: true,
				performance: true,
				trans: true
			},
			elementMenu: {},
			elementCategories: ['basic', 'overview', 'plan', 'performance', 'trans'],

			draggedPage: null,
			dragOverIndex: null
		};
	},
	computed: {
		toolbarItems() {
			return [
				{ type: 'page', icon: reportPageIcon, label: this.$t('cus.pages') },
				{ type: 'element', icon: reportElementIcon, label: this.$t('cus.components') }
			];
		}
	},
	mounted() {
		this.getElementMenu();
	},
	methods: {
		toggle(type) {
			this.panel = this.panel === type ? null : type;
		},

		// 頁面管理
		addNewPage() {
			this.$emit('page-added', this.newPageType);
		},

		selectPage(pageId) {
			this.$emit('page-selected', pageId);
		},

		deletePage(pageId) {
			if (confirm('確定要刪除此頁面嗎？')) {
				this.$emit('page-deleted', pageId);
			}
		},

		// 元件相關
		async getElementMenu() {
			try {
				const menu = await this.$api.getElementMenu();
				this.elementMenu = menu.data || {};
				console.log('元件菜單載入成功:', this.elementMenu);
			}
			catch (error) {
				console.error('載入元件失敗:', error);
			}
		},

		toggleCategory(cat) {
			this.expanded[cat] = !this.expanded[cat];
		},
		// 元件拖拉開始
		onDragStart(comp, event) {
			event.dataTransfer.setData('component', JSON.stringify(comp));
		},

		// 輔助函數
		getPageTypeIcon(type) {
			const icons = {
				cover: 'bi bi-journal-richtext',
				content: 'bi bi-journal-text',
				transition: 'bi bi-journal-check',
				backcover: 'bi bi-journal-bookmark'
			};
			return icons[type] || 'bi bi-journal';
		},

		getPageTypeName(type) {
			const names = {
				cover: '封面頁',
				content: '內容頁',
				transition: '章節頁',
				backcover: '封底頁'
			};
			return names[type] || '頁面';
		},

		getCategoryName(category) {
			const { t } = useI18n();
			const names = {
				basic: 'cus.basicComponents',
				overview: 'cus.cusOverview',
				plan: 'cus.investmentPlan',
				performance: 'cus.investmentPerformanceAnalysis',
				trans: 'cus.transactionRecordQuery'
			};
			return t(names[category]) || category;
		},
		// 頁面拖拉相關方法
		onPageDragStart(event, pageId, index) {
			this.draggedPage = pageId;
			event.dataTransfer.effectAllowed = 'move';
			event.dataTransfer.setData('text/plain', pageId);

			// 使拖動的元素半透明
			setTimeout(() => {
				event.target.classList.add('is-dragging');
			}, 0);
		},

		onPageDragOver(event, index) {
			event.preventDefault();
			event.dataTransfer.dropEffect = 'move';
			this.dragOverIndex = index;
		},

		onPageDragEnter(event, index) {
			event.target.closest('.page-item').classList.add('drag-over');
		},

		onPageDragLeave(event) {
			const item = event.target.closest('.page-item');
			if (item) {
				item.classList.remove('drag-over');
			}
		},

		onPageDrop(event, dropIndex) {
			event.preventDefault();

			// 清除樣式
			const items = document.querySelectorAll('.page-item');
			items.forEach(item => item.classList.remove('drag-over'));

			// 獲取被拖拽頁面的ID和索引
			const draggedPageId = event.dataTransfer.getData('text/plain');
			const dragIndex = this.pages.findIndex(p => p.id === draggedPageId);

			if (dragIndex === -1 || dragIndex === dropIndex) return;

			// 發送重排序事件
			this.$emit('page-reordered', { fromIndex: dragIndex, toIndex: dropIndex });

			// 重置狀態
			this.draggedPage = null;
			this.dragOverIndex = null;
		},

		onPageDragEnd() {
			this.draggedPage = null;
			this.dragOverIndex = null;

			// 清除樣式
			const items = document.querySelectorAll('.page-item');
			items.forEach((item) => {
				item.classList.remove('is-dragging');
				item.classList.remove('drag-over');
			});
		}
	}
};
</script>

<style scoped>
.side-toolbar {
  border-right: 1px solid #e2e2e2;
  background: #f7f8fa;
  padding-top: 16px;
  padding-bottom: 20px;
}

.sidebar-icon-group {
  position: relative;
  color: #333;
  transition: background 0.15s;
  border-radius: 8px;
  margin-bottom: 4px;
}

.sidebar-icon-group.active {
  background: #e3f2fd;
}

.sidebar-icon-group:hover {
  background: #f0f7ff;
}

.sidebar-label {
  color: #666;
  font-size: 13px;
  letter-spacing: 1px;
}

.block-panel {
  height: 100%;
  transition: width 0.2s;
  z-index: 1;
}

.page-list {
  max-height: 500px;
  overflow-y: auto;
}

.page-item {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.page-item:hover {
  border-color: #a0d7f7;
  background: #f8f9fa;
}

.page-item.active {
  border-color: #007bff;
  background: #e3f2fd;
}

.page-thumbnail {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  font-size: 16px;
}

.page-title {
  font-weight: 500;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.element-gap {
  margin-bottom: 4px;
}
</style>
