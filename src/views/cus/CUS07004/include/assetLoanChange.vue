<template>
	<div ref="container" class="component-container">
		<div class="card card-table mb-3">
			<div class="card-header d-flex justify-content-between align-items-center">
				<h4 class="mb-0">
					資產與負債資產往來變化
				</h4>
				<div class="tx-memo">
					{{ $t('cus.newTaiwanDollarUnit') }}
				</div>
			</div>
			<div class="table-responsive">
				<assetLoanChangeChart
					v-if="pageData.length > 0"
					v-show="pageData.length > 0"
					ref="chart-asset-loan-change"
					:chart-id="'chart-asset-loan-change'"
					:chart-data="pageData"
				/>
				<table class="bih-table table table-striped table-hover">
					<thead>
						<tr>
							<th>期間</th>
							<th class="text-end">
								上月底餘額
							</th>
							<th class="text-end">
								近3個月平均
							</th>
							<th class="text-end">
								近6個月平均
							</th>
							<th class="text-end">
								近12個月平均
							</th>
							<th class="text-end">
								去年度平均
							</th>
						</tr>
					</thead>
					<tbody v-if="tableData.assets.length > 0 && tableData.debts.length > 0">
						<tr>
							<td>資產</td>
							<td class="text-end">
								{{ tableData.assets[0].value.toLocaleString() }}
							</td>
							<td class="text-end">
								{{ tableData.assets[1].value.toLocaleString() }}
							</td>
							<td class="text-end">
								{{ tableData.assets[2].value.toLocaleString() }}
							</td>
							<td class="text-end">
								{{ tableData.assets[3].value.toLocaleString() }}
							</td>
							<td class="text-end">
								{{ tableData.assets[4].value.toLocaleString() }}
							</td>
						</tr>
						<tr>
							<td>負債</td>
							<td class="text-end">
								{{ tableData.debts[0].value.toLocaleString() }}
							</td>
							<td class="text-end">
								{{ tableData.debts[1].value.toLocaleString() }}
							</td>
							<td class="text-end">
								{{ tableData.debts[2].value.toLocaleString() }}
							</td>
							<td class="text-end">
								{{ tableData.debts[3].value.toLocaleString() }}
							</td>
							<td class="text-end">
								{{ tableData.debts[4].value.toLocaleString() }}
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>
<script>
import assetLoanChangeChart from './amchart/assetLoanChangeChart.vue';
export default {
	name: 'AssetLoanChange',
	components: {
		assetLoanChangeChart
	},
	props: {
		cusCode: {
			type: String,
			default: ''
		},
		uuid: {
			type: String,
			required: false
		}
	},
	data() {
		return {
			pageData: []
		};
	},
	computed: {
		tableData() {
			if (!this.pageData || this.pageData.length === 0) return { assets: [], debts: [] };
			const assets = [], debts = [];
			const orderMap = {
				上月底餘額: 1,
				近3個月平均: 2,
				近6個月平均: 3,
				近12個月平均: 4,
				去年度平均: 5
			};
			this.pageData.forEach((item) => {
				assets.push({
					category: item.category,
					value: item.資產,
					order: orderMap[item.category]
				});
				debts.push({
					category: item.category,
					value: item.負債,
					order: orderMap[item.category]
				});
			});
			assets.sort((a, b) => a.order - b.order);
			debts.sort((a, b) => a.order - b.order);
			return {
				assets: assets,
				debts: debts
			};
		}
	},
	mounted() {
		this.getPageData();
		this.$nextTick(() => {
			this.setupResizeObserver();
			this.reportHeight();
		});
	},
	updated() {
		this.$nextTick(() => {
			this.reportHeight();
		});
	},
	beforeUnmount() {
		if (this.resizeObserver) {
			this.resizeObserver.disconnect();
		}
	},
	methods: {
		async getPageData() {
			const res = await this.$api.getAssetLoanChangeApi({
				cusCode: this.cusCode
			});
			console.log('getPageData', res);
			this.pageData = res.data;
		},

		// 設定 ResizeObserver 監控高度變化
		setupResizeObserver() {
			if (window.ResizeObserver) {
				this.resizeObserver = new ResizeObserver((entries) => {
					for (const entry of entries) {
						const height = entry.contentRect.height;
						this.reportHeight(height);
					}
				});

				this.resizeObserver.observe(this.$refs.container);
			}
		},

		// 回報高度給父元件
		reportHeight(customHeight = null) {
			const height = customHeight || this.$refs.container?.offsetHeight || 0;
			if (height > 0 && this.uuid) {
				this.$emit('height-changed', { uuid: this.uuid, height });
			}
		}
	}
};
</script>
