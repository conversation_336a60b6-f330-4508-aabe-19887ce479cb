<template>
	<div ref="editorContainer" class="text-editor-component">
		<!-- 元件控制按鈕 - 編輯按鈕 -->
		<div v-if="!previewMode" class="component-edit-controls">
			<button
				class="btn-edit"
				:title="isEditing ? $t('cus.finishEditing') : $t('cus.editText')"
				@click="toggleEdit"
			>
				<i :class="isEditing ? 'bi bi-check' : 'bi bi-pencil'" />
			</button>
		</div>

		<!-- 元件工具列 - 僅在編輯時顯示 -->
		<div v-show="isEditing && !previewMode" class="editor-toolbar">
			<div class="btn-group btn-group-sm" role="group">
				<!-- 格式按鈕 -->
				<button
					:title="$t('cus.bold')"
					class="btn btn-light"
					:class="{ 'active': isFormatActive('bold') }"
					@click="formatText('bold')"
				>
					<img class="icon-img" src="@/assets/images/icon/bold-solid.svg" :alt="$t('cus.bold')">
				</button>
				<button
					:title="$t('cus.underline')"
					class="btn btn-light"
					:class="{ 'active': isFormatActive('underline') }"
					@click="formatText('underline')"
				>
					<img class="icon-img" src="@/assets/images/icon/underline-solid.svg" :alt="$t('cus.underline')">
				</button>
				<button
					:title="$t('cus.italic')"
					class="btn btn-light"
					:class="{ 'active': isFormatActive('italic') }"
					@click="formatText('italic')"
				>
					<i class="bi bi-type-italic" />
				</button>
			</div>

			<!-- 標題類型選擇 -->
			<div class="dropdown d-inline-block ms-2">
				<button
					class="btn btn-light btn-sm dropdown-toggle"
					data-bs-toggle="dropdown"
					:title="$t('cus.textType')"
				>
					<i class="bi bi-type" />
					{{ getCurrentHeadingText() }}
				</button>
				<div class="dropdown-menu">
					<a class="dropdown-item" @click="setHeading('h1')">
						<h1 style="margin:0;font-size:1.2rem;">{{ $t('cus.heading1') }}</h1>
					</a>
					<a class="dropdown-item" @click="setHeading('h2')">
						<h2 style="margin:0;font-size:1.1rem;">{{ $t('cus.heading2') }}</h2>
					</a>
					<a class="dropdown-item" @click="setHeading('h3')">
						<h3 style="margin:0;font-size:1rem;">{{ $t('cus.heading3') }}</h3>
					</a>
					<a class="dropdown-item" @click="setHeading('p')">{{ $t('cus.normalText') }}</a>
				</div>
			</div>

			<!-- 清單 -->
			<div class="btn-group btn-group-sm ms-2" role="group">
				<button
					:title="$t('cus.bulletList')"
					class="btn btn-light"
					:class="{ 'active': isFormatActive('insertUnorderedList') }"
					@click="formatText('insertUnorderedList')"
				>
					<img class="icon-img" src="@/assets/images/icon/list-ul-solid.svg" :alt="$t('cus.bulletList')">
				</button>
				<button
					:title="$t('cus.numberedList')"
					class="btn btn-light"
					:class="{ 'active': isFormatActive('insertOrderedList') }"
					@click="formatText('insertOrderedList')"
				>
					<img class="icon-img" src="@/assets/images/icon/list-ol-solid.svg" :alt="$t('cus.numberedList')">
				</button>
			</div>

			<!-- 文字顏色選擇 -->
			<div class="dropdown d-inline-block ms-2">
				<button
					class="btn btn-light btn-sm dropdown-toggle"
					data-bs-toggle="dropdown"
					:title="$t('cus.textColor')"
				>
					<i class="bi bi-palette" />
				</button>
				<div class="dropdown-menu color-picker">
					<div class="color-grid p-2">
						<div class="row g-1">
							<div v-for="color in colorOptions" :key="color.value" class="col-3">
								<div
									class="color-swatch"
									:style="{ backgroundColor: color.value }"
									:title="color.name"
									@click="setTextColor(color.value)"
								/>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 字體大小 -->
			<div class="dropdown d-inline-block ms-2">
				<button
					class="btn btn-light btn-sm dropdown-toggle"
					data-bs-toggle="dropdown"
					:title="$t('cus.fontSize')"
				>
					<i class="bi bi-fonts" />
					{{ currentFontSize }}
				</button>
				<div class="dropdown-menu">
					<a
						v-for="size in fontSizes"
						:key="size"
						class="dropdown-item"
						@click="setFontSize(size)"
					>
						{{ size }}px
					</a>
				</div>
			</div>

			<!-- 插入圖片 -->
			<button
				:title="$t('cus.insertImage')"
				class="btn btn-light btn-sm ms-2"
				@click="insertImage"
			>
				<img class="icon-img" src="@/assets/images/icon/image-solid.svg" :alt="$t('cus.insertImage')">
			</button>
		</div>

		<!-- 編輯區域 -->
		<div
			ref="editor"
			class="editor-content"
			:contenteditable="isEditing && !previewMode"
			:style="editorStyles"
			:data-placeholder="$t('cus.pleaseEnterText')"
			@focus="handleFocus"
			@blur="handleBlur"
		/>

		<!-- 圖片上傳 Modal -->
		<div
			v-if="showImageModal && !previewMode"
			class="modal fade show d-block"
			tabindex="-1"
			style="background:rgba(0,0,0,0.2);"
		>
			<div class="modal-dialog modal-dialog-centered">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title">
							{{ $t('cus.insertImage') }}
						</h5>
						<button type="button" class="btn-close" @click="showImageModal = false" />
					</div>
					<div class="modal-body">
						<div class="mb-3">
							<label class="form-label">{{ $t('cus.selectImageFile') }}</label>
							<input
								ref="imageInput"
								type="file"
								class="form-control"
								accept="image/*"
								@change="handleImageUpload"
							>
						</div>
						<div v-if="imagePreview" class="mb-3">
							<label class="form-label">{{ $t('cus.preview') }}</label>
							<div class="text-center">
								<img :src="imagePreview" :alt="$t('cus.preview')" style="max-width: 100%; max-height: 200px;">
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-white" @click="showImageModal = false">
							{{ $t('cus.cancel') }}
						</button>
						<button
							type="button"
							class="btn btn-primary"
							:disabled="!imagePreview"
							@click="confirmInsertImage"
						>
							{{ $t('cus.insert') }}
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'TextEditor',
	props: {
		uuid: {
			type: String,
			required: true
		},
		// componentData: {
		//   type: Object,
		//   default: () => ({})
		// },
		// initialContent: {
		//   type: String,
		//   default: ''
		// },
		content: {
			type: String,
			default: ''
		},
		previewMode: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			isEditing: false,
			localContent: this.content,
			hasContent: !!this.content,
			showImageModal: false,
			imagePreview: null,
			currentFontSize: '14',
			currentHeading: 'p',

			// 顏色選項
			colorOptions: [
				{ name: '黑色', value: '#000000' },
				{ name: '深灰', value: '#666666' },
				{ name: '淺灰', value: '#999999' },
				{ name: '白色', value: '#FFFFFF' },
				{ name: '主要藍', value: '#0469d9' },
				{ name: '資訊藍', value: '#007bff' },
				{ name: '成功綠', value: '#28a745' },
				{ name: '警告橙', value: '#ffc107' },
				{ name: '危險紅', value: '#dc3545' },
				{ name: '次要紫', value: '#6f42c1' },
				{ name: '深藍', value: '#00368c' },
				{ name: '暗綠', value: '#066AFE' }
			],

			// 字體大小
			fontSizes: [12, 14, 16, 18, 20, 24, 28, 32, 36, 48],
			resizeObserver: null,
			// 選區狀態暫存
			selectionState: null,
			isEditingActive: false
		};
	},
	computed: {
		editorStyles() {
			return {
				minHeight: this.isEditing ? '100px' : '60px',
				padding: '12px',
				border: this.isEditing ? '2px solid #007bff' : '1px solid transparent',
				borderRadius: '4px',
				outline: 'none',
				fontSize: this.currentFontSize + 'px',
				cursor: this.isEditing ? 'text' : 'default'
			};
		}
	},
	watch: {
		// 外部 content 變化
		content: {
			handler(newVal) {
				// 只有在非編輯狀態下才更新，避免編輯時被外部覆蓋
				if (!this.isEditing && newVal !== this.localContent) {
					this.localContent = newVal;
					this.hasContent = !!newVal;

					// 更新編輯器內容
					if (this.$refs.editor) {
						this.$refs.editor.innerHTML = newVal || '';
					}
				}
			},
			immediate: true // 組件創建時立即執行
		},
		// 預覽模式變化
		previewMode(newVal) {
			if (newVal && this.isEditing) {
				// 進入預覽模式時，如果正在編輯則結束編輯
				this.finishEditing();
			}
		}
	},
	mounted() {
		// 初始化編輯器內容
		if (this.content) {
			this.$refs.editor.innerHTML = this.content;
			this.hasContent = true;
		}

		// 設置輸入事件監聽
		this.setupContentObserver();
		this.setupResizeObserver();
		this.reportHeight();

		// 增加輸入事件監聽
		this.$refs.editor.addEventListener('input', this.handleContentChange);

		// 監聽編輯狀態變化，通知父元件
		this.$watch('isEditing', (newVal) => {
			this.$emit('editing-changed', { uuid: this.uuid, isEditing: newVal });
		});
	},
	updated() {
		this.$nextTick(() => {
			this.reportHeight();
		});
	},
	beforeUnmount() {
		if (this.resizeObserver) {
			this.resizeObserver.disconnect();
		}
		if (this.contentObserver) {
			this.contentObserver.disconnect();
		}
		// 移除事件監聽
		this.$refs.editor.removeEventListener('input', this.handleContentChange);
	},
	methods: {
		// 切換編輯模式
		toggleEdit() {
			if (this.previewMode) return;

			if (this.isEditing) {
				this.finishEditing();
			}
			else {
				this.startEditing();
			}
		},

		// 使用MutationObserver監聽內容變化
		setupContentObserver() {
			this.contentObserver = new MutationObserver((mutations) => {
				const content = this.$refs.editor.innerHTML.trim();
				this.hasContent = content !== ''
					&& content !== '<br>'
					&& content !== '<div><br></div>'
					&& content !== '<p><br></p>';
			});

			this.contentObserver.observe(this.$refs.editor, {
				childList: true,
				characterData: true,
				subtree: true
			});
		},

		// 保存選區
		saveSelection() {
			if (window.getSelection) {
				const sel = window.getSelection();
				if (sel.getRangeAt && sel.rangeCount) {
					this.selectionState = sel.getRangeAt(0);
				}
			}
		},

		// 恢復選區
		restoreSelection() {
			if (this.selectionState && window.getSelection) {
				const sel = window.getSelection();
				sel.removeAllRanges();
				sel.addRange(this.selectionState);
			}
		},

		// 開始編輯
		startEditing() {
			if (this.previewMode) return;

			this.isEditing = true;
			this.isEditingActive = true;
			this.$nextTick(() => {
				if (this.$refs.editor) {
					this.$refs.editor.focus();
					// 如果是空內容，將光標置於開始位置
					if (!this.hasContent) {
						const range = document.createRange();
						const sel = window.getSelection();
						range.setStart(this.$refs.editor, 0);
						range.collapse(true);
						sel.removeAllRanges();
						sel.addRange(range);
					}
				}
			});
		},

		// 完成編輯
		finishEditing() {
			this.isEditing = false;
			this.isEditingActive = false;
			this.localContent = this.$refs.editor.innerHTML;
			this.saveContent();
			this.$nextTick(() => {
				this.reportHeight();
			});
		},

		// 處理焦點獲得
		handleFocus() {
			if (!this.isEditingActive) {
				this.$refs.editor.blur();
			}
		},

		// 處理內容變化
		handleContentChange() {
			// 在輸入事件中，只標記內容變更，不直接修改content以避免光標跳躍
			const content = this.$refs.editor.innerHTML.trim();
			this.hasContent = content !== ''
				&& content !== '<br>'
				&& content !== '<div><br></div>'
				&& content !== '<p><br></p>';
			this.reportHeight();

			// 即時保存內容
			if (this.isEditing) {
				this.localContent = this.$refs.editor.innerHTML;
				this.saveContent();
			}
		},

		// 處理失去焦點 - 修改邏輯，不自動結束編輯
		handleBlur(event) {
			// 不再自動結束編輯，只有點擊完成按鈕或編輯按鈕才結束
			if (event.relatedTarget && event.relatedTarget.closest('.editor-toolbar')) {
				return;
			}
		},

		// 處理貼上 - 純文本模式避免格式混亂
		handlePaste(event) {
			if (!this.isEditing) return;

			event.preventDefault();
			const text = event.clipboardData.getData('text/plain');
			document.execCommand('insertText', false, text);
		},

		// 格式化文字 - 先保存選區再操作
		formatText(command) {
			if (!this.isEditing) return;

			this.saveSelection();
			this.$refs.editor.focus();
			this.restoreSelection();
			document.execCommand(command, false, null);
		},

		// 檢查格式是否啟用
		isFormatActive(command) {
			if (!this.isEditing) return false;
			return document.queryCommandState(command);
		},

		// 設定標題
		setHeading(tag) {
			if (!this.isEditing) return;

			this.currentHeading = tag;
			this.saveSelection();
			this.$refs.editor.focus();
			this.restoreSelection();
			document.execCommand('formatBlock', false, '<' + tag + '>');
		},

		// 取得目前標題文字
		getCurrentHeadingText() {
			switch (this.currentHeading) {
				case 'h1': return 'H1';
				case 'h2': return 'H2';
				case 'h3': return 'H3';
				default: return this.$t('cus.paragraph');
			}
		},

		// 設定文字顏色
		setTextColor(color) {
			if (!this.isEditing) return;

			this.saveSelection();
			this.$refs.editor.focus();
			this.restoreSelection();
			document.execCommand('foreColor', false, color);
		},

		// 設定字體大小 - 改進版本避免使用fontSize命令
		setFontSize(size) {
			if (!this.isEditing) return;

			this.currentFontSize = size;

			// 保存當前選擇區域
			this.saveSelection();
			this.$refs.editor.focus();
			this.restoreSelection();

			// 使用span包裹而非使用font標籤
			document.execCommand('fontSize', false, '7');

			const fontElements = this.$refs.editor.querySelectorAll('font[size="7"]');
			fontElements.forEach((el) => {
				const span = document.createElement('span');
				span.style.fontSize = size + 'px';
				span.innerHTML = el.innerHTML;
				el.parentNode.replaceChild(span, el);
			});
		},

		// 插入圖片
		insertImage() {
			if (!this.isEditing) return;

			this.saveSelection();
			this.showImageModal = true;
		},

		// 處理圖片上傳
		handleImageUpload(event) {
			const file = event.target.files[0];
			if (file && file.type.startsWith('image/')) {
				const reader = new FileReader();
				reader.onload = (e) => {
					this.imagePreview = e.target.result;
				};
				reader.readAsDataURL(file);
			}
		},

		// 確認插入圖片
		confirmInsertImage() {
			if (this.imagePreview) {
				this.$refs.editor.focus();
				this.restoreSelection();

				const img = document.createElement('img');
				img.src = this.imagePreview;
				img.style.maxWidth = '100%';
				img.style.height = 'auto';
				img.style.margin = '8px 0';
				img.alt = this.$t('cus.insertedImage');

				// 使用選區API而非execCommand
				const selection = window.getSelection();
				if (selection.rangeCount > 0) {
					const range = selection.getRangeAt(0);
					range.deleteContents();
					range.insertNode(img);
					range.setStartAfter(img);
					range.setEndAfter(img);
					selection.removeAllRanges();
					selection.addRange(range);
				}
				else {
					this.$refs.editor.appendChild(img);
				}

				// 觸發內容變化
				this.handleContentChange();

				// 關閉模態框
				this.showImageModal = false;
				this.imagePreview = null;
				this.$refs.imageInput.value = '';
			}
		},

		// 儲存內容
		saveContent() {
			// content-changed
			this.$emit('update', {
				content: this.localContent
			});
		},

		// 設定 ResizeObserver 監控高度變化
		setupResizeObserver() {
			if (window.ResizeObserver) {
				this.resizeObserver = new ResizeObserver((entries) => {
					for (const entry of entries) {
						const height = entry.contentRect.height;
						this.reportHeight(height);
					}
				});

				this.resizeObserver.observe(this.$refs.editorContainer);
			}
		},

		// 回報高度給父元件
		reportHeight(customHeight = null) {
			const height = customHeight || this.$refs.editorContainer?.offsetHeight || 0;
			if (height > 0 && this.uuid) {
				this.$emit('height-changed', { uuid: this.uuid, height });
			}
		}
	}
};
</script>

<style scoped>
.text-editor-component {
  width: 100%;
  border: 1px solid #e2e2e2;
  border-radius: 8px;
  background: #fff;
  position: relative;
}

/* 編輯按鈕樣式 */
.component-edit-controls {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 15;
  opacity: 0;
  transition: opacity 0.2s;
}

.text-editor-component:hover .component-edit-controls {
  opacity: 1;
}

.btn-edit {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  background: #007bff;
  color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-edit:hover {
  background: #0056b3;
  transform: scale(1.05);
}

.editor-toolbar {
  padding: 8px 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #e2e2e2;
  border-radius: 8px 8px 0 0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

.editor-toolbar .btn {
  border: 1px solid #ddd;
  background: #fff;
  color: #666;
  font-size: 12px;
  padding: 4px 8px;
}

.editor-toolbar .btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.editor-toolbar .btn.active {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

/* SVG圖標樣式 */
.icon-img {
  width: 14px;
  height: 14px;
  vertical-align: middle;
  filter: invert(25%);
}

.btn.active .icon-img {
  filter: invert(100%); /* 白色圖標 */
}

.editor-content {
  min-height: 60px;
  line-height: 1.6;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.editor-content:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 預設提示文字樣式 */
.editor-content:empty:before {
  content: attr(data-placeholder);
  color: #adb5bd;
  pointer-events: none;
  font-style: italic;
  display: block;
}

.placeholder-text {
  padding: 24px;
  text-align: center;
  color: #adb5bd;
  border: 2px dashed #e9ecef;
  border-radius: 8px;
  font-style: italic;
}

.placeholder-text i {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
}

.color-picker {
  min-width: 200px;
}

.color-swatch {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px #ddd;
  transition: transform 0.2s;
}

.color-swatch:hover {
  transform: scale(1.1);
  box-shadow: 0 0 0 2px #007bff;
}

/* 編輯區內容樣式 */
.editor-content h1 {
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0.5rem 0;
  color: #333;
}

.editor-content h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0.5rem 0;
  color: #333;
}

.editor-content h3 {
  font-size: 1.25rem;
  font-weight: bold;
  margin: 0.5rem 0;
  color: #333;
}

.editor-content p {
  margin: 0.5rem 0;
}

.editor-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

/* 模態框樣式調整 */
.modal .btn-white {
  background-color: #fff;
  border: 1px solid #ddd;
  color: #666;
}

.modal .btn-white:hover {
  background-color: #f8f9fa;
  border-color: #adb5bd;
}

/* 響應式調整 */
@media (max-width: 768px) {
  .editor-toolbar {
    padding: 6px 8px;
    gap: 2px;
  }

  .editor-toolbar .btn {
    padding: 3px 6px;
    font-size: 11px;
  }

  .color-swatch {
    width: 20px;
    height: 20px;
  }

  .icon-img {
    width: 12px;
    height: 12px;
  }

  .btn-edit {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }
}
</style>
