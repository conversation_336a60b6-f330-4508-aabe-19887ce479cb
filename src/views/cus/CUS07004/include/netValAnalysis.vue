<template>
	<div ref="container" class="component-container">
		<div class="card card-table mb-3">
			<div class="card-header d-flex justify-content-between align-items-center">
				<h4 class="mb-0">
					{{ $t('pro.navAnalysis') }}
				</h4>
			</div>
			<div class="table-responsive">
				<div class="card card-table mb-3">
					<div class="card-header d-flex justify-content-between align-items-center">
						<h4 class="mb-0">
							各月月底餘額 - {{ $t('cus.unrealizedProfitLoss') }}
						</h4>
						<div class="tx-memo">
							{{ $t('cus.newTaiwanDollarUnit') }}
						</div>
					</div>
					<div class="table-responsive">
						<div class="row">
							<div class="col-lg-5">
								<table class="bih-table table table-striped table-hover">
									<thead>
										<tr>
											<th>{{ $t('cus.date') }}</th>
											<th class="num">
												{{ $t('cus.investmentPrincipalIncludingFees') }}
											</th>
											<th class="num">
												{{ $t('cus.currentValue') }}
											</th>
											<th class="num">
												{{ $t('cus.unrealizedProfitLoss') }}
											</th>
											<th class="num">
												{{ $t('cus.returnRate') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="row in sortedNetVal" :key="row.year">
											<td>{{ row.year }}</td>
											<td class="num">
												{{ row.total.toLocaleString() }}
											</td>
											<td class="num">
												{{ row.capital.toLocaleString() }}
											</td>
											<td class="num" :class="txRiseFall(row.capital - row.total)">
												{{ (row.capital - row.total).toLocaleString() }}
											</td>
											<td class="num" :class="txRiseFall(row.reward)">
												{{ row.reward }}%
											</td>
										</tr>
									</tbody>
								</table>
								<div class="tx-note">
									<ol>
										<li>本表數據，為各月月底各投資產品的投資餘額，其中不包含活存、定存等被歸類為現金的產品</li>
										<li>投資成本，為已經考慮費用影響，並以投資時點匯率換算的本國幣別投資成本</li>
										<li>市值 ： 投資產品以各月月底結算報價 × 持倉單位數</li>
										<li>未實現損益 ： 市值 – 投資成本</li>
									</ol>
								</div>
							</div>
							<div class="col-lg-7">
								<netValAnalysisChart1
									v-if="sortedNetVal.length > 0"
									v-show="pageData.netVal.length > 0"
									ref="chart-net-val-analysis-1"
									:chart-id="'chart-net-val-analysis-1'"
									:chart-data="sortedNetVal"
								/>
							</div>
						</div>
					</div>
				</div>
				<div class="card card-table mb-3">
					<div class="card-header d-flex justify-content-between align-items-center">
						<h4 class="mb-0">
							各月月底{{ $t('core.realizedPnl') }}{{ $t('cus.amt') }}
						</h4>
						<div class="tx-memo">
							{{ $t('cus.newTaiwanDollarUnit') }}
						</div>
					</div>
					<div class="table-responsive">
						<div class="row">
							<div class="col-lg-6">
								<table class="bih-table table table-striped table-hover">
									<thead>
										<tr>
											<th>{{ $t('cus.date') }}</th>
											<th class="num">
												{{ $t('pro.total') }}{{ $t('cus.sell') }}{{ $t('cus.amt') }}
											</th>
											<th class="num">
												{{ $t('cus.investmentPrincipalIncludingFees') }}
											</th>
											<th class="num">
												{{ $t('cus.realizedProfitLoss') }}
											</th>
											<th class="num">
												{{ $t('cus.accumulatedDividend') }}
											</th>
											<th class="num">
												{{ $t('cus.returnRate') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="row in sortedRealizeProfit" :key="row.year">
											<td>{{ row.year }}</td>
											<td class="num">
												{{ row.total.toLocaleString() }}
											</td>
											<td class="num">
												{{ row.capital.toLocaleString() }}
											</td>
											<td class="num" :class="txRiseFall(row.capital - row.total)">
												{{ (row.capital - row.total).toLocaleString() }}
											</td>
											<td class="num">
												{{ totDividend(row.reward).toLocaleString() }}
											</td>
											<td class="num" :class="txRiseFall(row.reward)">
												{{ row.reward }}%
											</td>
										</tr>
									</tbody>
								</table>
								<div class="tx-note">
									<ol>
										<li>本表數據，為各月月底累計當月發生的賣出交易所產生的實現損益金額。<br>統計範圍僅限各投資產品的投資餘額，其中不包含活存、定存等被歸類為現金的產品</li>
										<li>賣出金額 ＝（賣出價格×當日匯率 ＋ 費用影響調整 ＋ 匯率影響調整 ＋ 利息影響調整）× 賣出單位數</li>
										<li>成本 ＝為已經考慮費用影響，並以投資時點匯率換算的本國幣別投資成本</li>
										<!-- <li>實現損益： 資料統計期間，所有賣出交易，經以下公式演算後的結果：<br/>（賣出價格×當日匯率 - 單位數本國幣計價考慮買入所發生費用的平均單位成本＋ 費用影響調整 ＋ 匯率影響調整）× 賣出單位數</li> -->
										<li>累計配息：單位數累計的平均配息金額 × 賣出單位數</li>
										<li>報酬率 ＝ （實現損益＋累計配息） / 成本</li>
									</ol>
								</div>
							</div>
							<div class="col-lg-6">
								<netValAnalysisChart2
									v-if="sortedRealizeProfit.length > 0"
									v-show="pageData.realizeProfit.length > 0"
									ref="chart-net-val-analysis-2"
									:chart-id="'chart-net-val-analysis-2'"
									:chart-data="sortedRealizeProfit"
								/>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import netValAnalysisChart1 from './amchart/netValAnalysisChart1.vue';
import netValAnalysisChart2 from './amchart/netValAnalysisChart2.vue';
export default {
	name: 'NetValAnalysis',
	components: {
		netValAnalysisChart1,
		netValAnalysisChart2
	},
	props: {
		cusCode: {
			type: String,
			default: ''
		},
		componentId: {
			type: String,
			required: false
		}
	},
	data() {
		return {
			pageData: {
				netVal: [],
				realizeProfit: [],
				lastReportHeight: 0
			}
		};
	},
	computed: {
		sortedNetVal() {
			// year representation is "MM-YYYY", sort by year then month with descending order
			return this.pageData.netVal.sort((a, b) => {
				const [monthA, yearA] = a.year.split('-').map(Number);
				const [monthB, yearB] = b.year.split('-').map(Number);
				return yearB - yearA || monthB - monthA;
			});
		},
		sortedRealizeProfit() {
			// year representation is "MM-YYYY", sort by year then month with descending order
			return this.pageData.realizeProfit.sort((a, b) => {
				const [monthA, yearA] = a.year.split('-').map(Number);
				const [monthB, yearB] = b.year.split('-').map(Number);
				return yearB - yearA || monthB - monthA;
			});
		}
	},
	mounted() {
		this.getPageData();
		this.$nextTick();
	},
	updated() {
		this.$nextTick(() => {
			this.reportHeight();
		});
	},
	methods: {
		async getPageData() {
			const res = await this.$api.getNetValAnalysisApi({ cusCode: this.cusCode });
			console.log('getNetValAnalysisApi', res);
			this.pageData.netVal = res.data.netVal || [];
			this.pageData.realizeProfit = res.data.realizeProfit || [];
		},

		// 回報高度給父元件
		reportHeight() {
			const height = this.$refs.container?.offsetHeight || 0;

			if (this.lastReportHeight === height) {
				return; // 如果高度沒有變化，則不報告
			}

			console.log(`netVal 報告高度變化: ${this.lastReportHeight} -> ${height}`);
			this.lastReportHeight = height;

			if (height > 0 && this.componentId) {
				this.$emit('height-changed', { componentId: this.componentId, height });
			}
		},

		txRiseFall(value) {
			return value > 0 ? 'tx-rise' : (value < 0 ? 'tx-fall' : '');
		},
		totDividend(reward) {
			return (Math.abs(reward) * 100).toLocaleString();
		}
	}
};
</script>
