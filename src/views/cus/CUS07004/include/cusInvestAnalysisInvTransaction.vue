<template>
	<div ref="container" class="component-container">
		<div class="card card-table mb-3">
			<div class="card-header d-flex justify-content-between align-items-center">
				<h4 class="mb-0">
					{{ $t('cus.transactionRecordQuery') }}
				</h4>
			</div>
			<div class="table-responsive">
				<div class="tx-note mb-3">
					{{ $t('cus.securitiesStockDataNote') }}
				</div>
				<div v-if="fundTranList.length > 0" class="card card-table">
					<div class="card-header">
						<h4>{{ $t('cus.trustFund') }}</h4>>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-bordered">
							<thead>
								<tr>
									<th rowspan="2">
										{{ $t('cus.transactionDate') }}
									</th>
									<th>{{ $t('cus.accountNumber') }}</th>
									<th rowspan="2">
										{{ $t('cus.transactionType') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.transactionCurrency') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.exchangeRate') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.transactionAmount') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.netValue') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.transactionUnits') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.handlingFee') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.otherFees') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.remarks') }}
									</th>
								</tr>
								<tr>
									<th>{{ $t('cus.productCodeInvestmentTarget') }}</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="tran in fundTranList">
									<td :data-th="$t('cus.transactionDate')">
										{{ $filters.formatDate(tran.tranDt) }}
									</td>
									<td :data-th="$t('cus.accountNumber') + ' ' + $t('cus.productCodeInvestmentTarget')">
										{{ tran.refNo }}<br><a class="tx-link" href="#">{{ tran.proName }}({{ tran.bankProCode }})</a>
									</td>
									<td :data-th="$t('cus.transactionType')">
										{{ tran.trantypeName }}
									</td>
									<td class="" :data-th="$t('cus.transactionCurrency')">
										<label class="num">{{ tran.tranCurCode }}</label>
									</td>
									<td class="text-end" :data-th="$t('cus.exchangeRate')">
										<label class="num">{{ $filters.formatAmt(tran.fxRate) }}</label>
									</td>
									<td class="text-end" :data-th="$t('cus.transactionAmount')">
										<label class="num">{{ $filters.formatAmt(tran.invAmtFc) }}</label>&nbsp;{{ tran.tranCurCode }}
									</td>
									<td class="text-end" :data-th="$t('cus.netValue')">
										<label class="num">{{ $filters.formatAmt(tran.price) }}</label>
									</td>
									<td class="text-end" :data-th="$t('cus.transactionUnits')">
										<label class="num">{{ $filters.formatAmt(tran.unit) }}</label>
									</td>
									<td class="text-end" :data-th="$t('cus.handlingFee')">
										<label class="num">{{ $filters.formatAmt(tran.feeFc) }}</label>&nbsp;{{ tran.fCurCode }}
									</td>
									<td class="text-end" :data-th="$t('cus.otherFees')">
										<label class="num">{{ $filters.formatAmt(tran.oFeeFc) }}</label>&nbsp;{{ tran.oCurCode }}
									</td>
									<td :data-th="$t('cus.remarks')">
										{{ tran.memo }}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div v-if="fbTranList.length > 0" class="card card-table mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.trustForeignBond') }}</h4>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-bordered">
							<thead>
								<tr>
									<th rowspan="2">
										{{ $t('cus.transactionDate') }}
									</th>
									<th>{{ $t('cus.accountNumber') }}</th>
									<th rowspan="2">
										{{ $t('cus.transactionType') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.transactionAmount') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.transactionFaceValue') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.exchangeRate') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.pricePercentage') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.frontEndInterest') }}
									</th>
									<th>{{ $t('cus.channelServiceFeeActualRate') }}</th>
								</tr>
								<tr>
									<th>{{ $t('cus.productCodeInvestmentTarget') }}</th>
									<th>{{ $t('cus.channelServiceFeeAnnualRate') }}</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="tran in fbTranList">
									<td :data-th="$t('cus.transactionDate')">
										{{ $filters.formatDate(tran.tranDt) }}
									</td>
									<td :data-th="$t('cus.accountNumber') + ' ' + $t('cus.productCodeInvestmentTarget')">
										{{ tran.refNo }}<br><a class="tx-link" href="#">{{ tran.proName }}({{ tran.bankProCode }})</a>
									</td>
									<td :data-th="$t('cus.transactionType')">
										{{ tran.trantypeName }}
									</td>
									<td class="text-end" :data-th="$t('cus.transactionAmount')">
										<label class="num">{{ $filters.formatAmt(tran.invAmtFc) }}</label>&nbsp;{{ tran.tranCurCode }}
									</td>
									<td class="text-end" :data-th="$t('cus.transactionFaceValue')" />
									<td class="text-end" :data-th="$t('cus.exchangeRate')">
										<label class="num">{{ $filters.formatAmt(tran.fxRate) }}</label>
									</td>
									<td class="text-end" :data-th="$t('cus.pricePercentage')">
										<label class="num">{{ $filters.formatAmt(tran.price) }}</label>
									</td>
									<td class="text-end" :data-th="$t('cus.frontEndInterest')">
										<label class="num">{{ $filters.formatAmt(tran.uFeeFc) }}</label>&nbsp;{{ tran.tranCurCode }}
									</td>
									<td class="text-end" :data-th="$t('cus.channelServiceFeeActualRate') + ' ' + $t('cus.channelServiceFeeAnnualRate')">
										<label class="num">{{ $filters.formatPct(tran.channelServiceRate) }}%</label><br>
										<label class="num">{{ $filters.formatPct(tran.channelServiceRateYear) }}%</label>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div v-if="etfTranList.length > 0" class="card card-table mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.trustETF') }}</h4>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-bordered">
							<thead>
								<tr>
									<th rowspan="2">
										{{ $t('cus.transactionDate') }}
									</th>
									<th>{{ $t('cus.dealSequenceNumber') }}</th>
									<th rowspan="2">
										{{ $t('cus.transactionType') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.transactionAmount') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.exchangeRate') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.dealPrice') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.shares') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.handlingFee') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.taxFee') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.trustManagementFee') }}
									</th>
								</tr>
								<tr>
									<th>{{ $t('cus.productCodeInvestmentTarget') }}</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="tran in etfTranList">
									<td :data-th="$t('cus.transactionDate')">
										{{ $filters.formatDate(tran.tranDt) }}
									</td>
									<td :data-th="$t('cus.dealSequenceNumber') + ' ' + $t('cus.productCodeInvestmentTarget')">
										{{ tran.refNo }}<br><a class="tx-link" href="#">{{ tran.proName }}({{ tran.bankProCode }})</a>
									</td>
									<td :data-th="$t('cus.transactionType')">
										{{ tran.trantypeName }}
									</td>
									<td class="text-end" :data-th="$t('cus.transactionAmount')">
										<label class="num">{{ $filters.formatAmt(tran.invAmtFc) }}</label>&nbsp;{{ tran.tranCurCode }}
									</td>
									<td class="text-end" :data-th="$t('cus.exchangeRate')">
										<label class="num">{{ $filters.formatAmt(tran.fxRate) }}</label>
									</td>
									<td class="text-end" :data-th="$t('cus.dealPrice')">
										<label class="num">{{ $filters.formatAmt(tran.price) }}</label>
									</td>
									<td class="text-end" :data-th="$t('cus.shares')">
										<label class="num">{{ $filters.formatAmt(tran.unit) }}</label>
									</td>
									<td class="text-end" :data-th="$t('cus.handlingFee')">
										<label class="num">{{ $filters.formatAmt(tran.fFeeFc) }}</label>&nbsp;{{ tran.fCurCode }}
									</td>
									<td class="text-end" :data-th="$t('cus.taxFee')">
										<label class="num">{{ $filters.formatAmt(tran.sFeeFc) }}</label>&nbsp;{{ tran.tranCurCode }}
									</td>
									<td class="text-end" :data-th="$t('cus.trustManagementFee')">
										<label class="num">{{ $filters.formatAmt(tran.mFeeFc) }}</label>&nbsp;{{ tran.tranCurCode }}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div v-if="pfdTranList.length > 0" class="card card-table mb-3">
					<div class="card-header">
						<h4>{{ $t('cus.trustForeignStock') }}</h4>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-bordered">
							<thead>
								<tr>
									<th rowspan="2">
										{{ $t('cus.transactionDate') }}
									</th>
									<th>{{ $t('cus.delegationNumber') }}</th>
									<th rowspan="2">
										{{ $t('cus.transactionType') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.investmentPrincipal') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.referenceExchangeRate') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.dealPrice') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.dealShares') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.estimatedHandlingFee') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.taxFee') }}
									</th>
									<th rowspan="2">
										{{ $t('cus.estimatedManagementFee') }}
									</th>
								</tr>
								<tr>
									<th>{{ $t('cus.productCodeInvestmentTarget') }}</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="tran in pfdTranList">
									<td :data-th="$t('cus.transactionDate')">
										{{ $filters.formatDate(tran.tranDt) }}
									</td>
									<td :data-th="$t('cus.delegationNumber') + ' ' + $t('cus.productCodeInvestmentTarget')">
										{{ tran.refNo }}<br><a class="tx-link" href="#">{{ tran.proName }}({{ tran.bankProCode }})</a>
									</td>
									<td :data-th="$t('cus.transactionType')">
										{{ tran.trantypeName }}
									</td>
									<td class="text-end" :data-th="$t('cus.investmentPrincipal')">
										<label class="num">{{ $filters.formatAmt(tran.invAmtFc) }}</label>&nbsp;{{ tran.tranCurCode }}
									</td>
									<td class="text-end" :data-th="$t('cus.referenceExchangeRate')">
										<label class="num">{{ $filters.formatAmt(tran.fxRate) }}</label>
									</td>
									<td class="text-end" :data-th="$t('cus.dealPrice')">
										<label class="num">{{ $filters.formatAmt(tran.price) }}</label>
									</td>
									<td class="text-end" :data-th="$t('cus.dealShares')">
										<label class="num">{{ $filters.formatAmt(tran.unit) }}</label>
									</td>
									<td class="text-end" :data-th="$t('cus.estimatedHandlingFee')">
										<label class="num">{{ $filters.formatAmt(tran.fFeeFc) }}</label>&nbsp;{{ tran.fCurCode }}
									</td>
									<td class="text-end" :data-th="$t('cus.taxFee')">
										<label class="num">{{ $filters.formatAmt(tran.sFeeFc) }}</label>&nbsp;{{ tran.tranCurCode }}
									</td>
									<td class="text-end" :data-th="$t('cus.estimatedManagementFee')">
										<label class="num">{{ $filters.formatAmt(tran.mFeeFc) }}</label>&nbsp;{{ tran.tranCurCode }}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="alert alert-warning mt-3" role="alert">
					<span class="ico-alert" />
					{{ $t('cus.functionalityDisclaimer') }}
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import moment from 'moment';
export default {
	props: {
		componentId: {
			type: String,
			required: true
		},
		cusCode: {
			type: String,
			default: ''
		},
		beginDate: {
			type: String,
			default: ''
		},
		endDate: {
			type: String,
			default: ''
		},
		componentData: {
			type: Object,
			default: () => ({})
		}
	},
	data: function () {
		return {
			form: {
				cusCode: null,
				tranDtB: null,
				tranDtE: null
			},
			pfcatList: [],
			trantypeList: [],
			pageData: [],
			fundTranList: [],
			fbTranList: [],
			etfTranList: [],
			pfdTranList: [],
			lastReportHeight: 0
		};
	},
	watch: {},
	mounted: function () {
		const self = this;
		self.form.cusCode = self.cusCode;
		self.form.tranDtB = moment().format(this.beginDate || 'YYYY-MM-DD'); // 交易日期起
		self.form.tranDtE = moment().format(this.endDate || 'YYYY-MM-DD'); // 交易日期迄

		this.$nextTick();
	},
	updated() {
		this.$nextTick(() => {
			this.reportHeight();
		});
	},
	methods: {
		search: function () {
			const self = this;
			self.getPageData();
		},
		gotoPage: function (page) {
			this.getPageData(page);
		},
		// 呼叫後端 API 取得「客戶交易明細」
		getPageData: async function () {
			const self = this;
			const resp = await self.$api.getTransactionLogApi(self.form);
			self.pageData = resp.data;
			self.fundTranList = self.pageData.filter((tran) => {
				return tran.pfcatCode === 'FUND';
			});
			self.fbTranList = self.pageData.filter((tran) => {
				return tran.pfcatCode === 'FB';
			});
			self.etfTranList = self.pageData.filter((tran) => {
				return tran.pfcatCode === 'ETF';
			});
			self.pfdTranList = self.pageData.filter((tran) => {
				return tran.pfcatCode === 'PFD';
			});
		},
		tranName: function (tranYn) {
			switch (tranYn) {
				case 'Y':
					return this.$t('cus.yes');
				case 'N':
					return this.$t('cus.no');
				default:
					return '';
			}
		},

		// 回報高度給父元件
		reportHeight() {
			const height = this.$refs.container?.offsetHeight || 0;

			if (this.lastReportHeight === height) {
				return; // 如果高度沒有變化，則不報告
			}

			console.log(`cusInvestAnalysisInvTransaction 報告高度變化: ${this.lastReportHeight} -> ${height}`);
			this.lastReportHeight = height;

			if (height > 0 && this.componentId) {
				this.$emit('height-changed', { componentId: this.componentId, height });
			}
		}
	}
};
</script>
