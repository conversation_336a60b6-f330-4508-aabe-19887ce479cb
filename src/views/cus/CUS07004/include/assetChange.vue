<template>
	<div ref="container" class="component-container">
		<div class="card card-table mb-3">
			<div class="card-header d-flex justify-content-between align-items-center">
				<h4 class="mb-0">
					{{ $t('cus.assetStatus') }}
				</h4>
				<div class="text-muted text-end mb-2">
					{{ $t('cus.dataDate') }}：{{ reportData.dataDate }}
				</div>
			</div>
			<div class="table-responsive">
				<assetChangeChart
					v-if="reportData.assetChangeList.length > 0"
					v-show="reportData.assetChangeList.length > 0"
					ref="chart-asset-change"
					:chart-id="'chart-asset-change'"
					:chart-data="reportData.assetChangeList"
				/>
				<table class="bih-table table table-striped table-hover">
					<thead>
						<tr>
							<th>{{ $t('pro.proType') }}</th>
							<th v-for="year in yearList" :key="year" class="text-end">
								{{ year }}
							</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="row in tableData" :key="row.product">
							<td>{{ row.product }}</td>
							<td v-for="year in yearList" :key="year" class="num">
								{{ row[year].toLocaleString() }}
							</td>
						</tr>
					</tbody>
					<tfoot>
						<tr class="tx-sum bg-total">
							<td>{{ $t('cus.total') }}</td>
							<td v-for="year in yearList" :key="year" class="num">
								{{ totRow[year].toLocaleString() }}
							</td>
						</tr>
					</tfoot>
				</table>
			</div>
		</div>
	</div>
</template>
<script>
import assetChangeChart from './amchart/assetChangeChart.vue';
export default {
	name: 'AssetChange',
	components: {
		assetChangeChart
	},
	props: {
		cusCode: {
			type: String,
			default: ''
		},
		uuid: {
			type: String,
			required: false
		}
	},
	data() {
		return {
			reportData: {
				dataDate: '',
				assetChangeList: []
			}
		};
	},
	computed: {
		yearList() {
			if (!this.reportData.assetChangeList || this.reportData.assetChangeList.length === 0) {
				return [];
			}
			const years = this.reportData.assetChangeList.map(item => item.year);
			years.sort((a, b) => a.localeCompare(b));
			return [...years, '2025年(迄今)', '2024年(全年)', '2023年(全年)'];
		},

		tableData() {
			if (!this.reportData.assetChangeList || this.reportData.assetChangeList.length === 0) {
				return [];
			}

			const productKeys = Object.keys(this.reportData.assetChangeList[0]).filter(key => key !== 'year');

			const yearGroup = {
				'2025年(迄今)': year => year.startsWith('2025'),
				'2024年(全年)': year => year.startsWith('2024'),
				'2023年(全年)': year => year.startsWith('2023')
			};

			const transposedData = productKeys.map((product) => {
				const row = { product };

				this.yearList.forEach((year) => {
					const entry = this.reportData.assetChangeList.find(d => d.year === year);
					row[year] = entry ? entry[product] : 0;
				});

				// 新增年度總和欄位
				for (const label in yearGroup) {
					row[label] = this.yearList
						.filter(year => yearGroup[label](year))
						.reduce((sum, year) => sum + (row[year] || 0), 0);
				}
				return row;
			});

			return transposedData;
		},

		totRow() {
			if (!this.tableData || this.tableData.length === 0) {
				return {};
			}

			const result = {};
			const allYearCols = [...this.yearList, '2025年(迄今)', '2024年(全年)', '2023年(全年)'];

			for (const col of allYearCols) {
				result[col] = this.tableData.reduce((sum, row) => sum + (row[col] || 0), 0);
			}

			return result;
		}
	},
	mounted() {
		this.getPageData();
		this.$nextTick(() => {
			this.setupResizeObserver();
			this.reportHeight();
		});
	},
	beforeUnmount() {
		if (this.resizeObserver) {
			this.resizeObserver.disconnect();
		}
	},
	methods: {
		async getPageData() {
			const res = await this.$api.getAssetChangeApi({ cusCode: this.cusCode });
			console.log('getAssetChangeApi', res);
			this.reportData.dataDate = res.data.dataDate;
			this.reportData.assetChangeList = res.data.assetChangeList;
		},

		// 設定 ResizeObserver 監控高度變化
		setupResizeObserver() {
			if (window.ResizeObserver) {
				this.resizeObserver = new ResizeObserver((entries) => {
					for (const entry of entries) {
						const height = entry.contentRect.height;
						this.reportHeight(height);
					}
				});

				this.resizeObserver.observe(this.$refs.container);
			}
		},

		// 回報高度給父元件
		reportHeight(customHeight = null) {
			const height = customHeight || this.$refs.container?.offsetHeight || 0;
			if (height > 0 && this.uuid) {
				this.$emit('height-changed', { uuid: this.uuid, height });
			}
		},

		// 取得會員基準幣小數位數
		getCurDecimal: function () {
			const self = this;
			// if (self.customer.baseCurCode == 'TWD') {
			//     return 0; // 台幣顯示至整數位
			// }
			return 2; // 其他顯示至小數後兩位
		},
		formatValue(value, type) {
			switch (type) {
				case 'I': // 千分位整數格式
					return this.formatInteger(value);
				case 'D': // 日期格式
					return this.formatDate(value);
				case 'F': // 浮點數轉為千分位整數格式
					return this.formatFloatAsInteger(value);
				case 'S': // 字串格式
					return value;
					// 其他類型的格式化處理可以在這裡繼續擴展
				default:
					return value; // 預設返回原始值
			}
		},
		formatInteger(value) {
			const numericValue = Number(value);
			return isNaN(numericValue) ? 0 : numeral(numericValue).format('0,0');
		},
		formatDate(value) {
			const date = new Date(value);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`; // 手動格式化為 'YYYY-MM-DD'
		},
		formatFloatAsInteger(value) {
			const numericValue = Number(value);
			return isNaN(numericValue) ? 0 : parseFloat(numericValue).toFixed(0); // 或者其他浮點數處理方式
		}
	}
};
</script>
