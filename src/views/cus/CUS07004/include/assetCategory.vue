<template>
	<div ref="container" class="component-container">
		<div class="card card-table mb-3">
			<div class="card-header d-flex justify-content-between align-items-center">
				<h4 class="mb-0">
					{{ $t('cus.assetCategory') }}
				</h4>
			</div>
			<div class="table-responsive">
				<div class="row">
					<div class="col-lg-6">
						<assetCategoryChart
							v-if="overviewChartData.length > 0"
							v-show="pageData.length > 0"
							ref="chart-asset-category-overview"
							:chart-id="'chart-asset-category-overview'"
							:chart-data="overviewChartData"
						/>
					</div>
					<div class="col-lg-6">
						<assetCategoryChart
							v-if="detailedChartData.length > 0"
							v-show="pageData.length > 0"
							ref="chart-asset-category-detailed"
							:chart-id="'chart-asset-category-detailed'"
							:chart-data="detailedChartData"
						/>
					</div>
				</div>
				<table class="bih-table table table-hover">
					<thead>
						<tr style="display: table-row;">
							<th class="text-start">
								{{ $t('cus.assetCategory') }}
							</th>
							<th>{{ $t('pro.productMainCategory') }}</th>
							<th>{{ $t('cus.investmentPrincipalIncludingFees') }}</th>
							<th>{{ $t('cus.accumulatedDividend') }}</th>
							<th>{{ $t('cus.returnRateIncludingInterest') }}</th>
						</tr>
					</thead>
					<tbody v-show="pageData.length > 0">
						<template v-for="(group, groupIndex) in pageData" :key="group.assetcatName">
							<template v-for="(item, index) in group.assetSumAmtProTypes" :key="item.pfcatName">
								<tr>
									<!-- 只在每組的第一列顯示資產分類，並設 rowspan -->
									<td v-if="index === 0" :rowspan="group.assetSumAmtProTypes.length + 1">
										{{ group.assetcatName }}
									</td>
									<td>{{ item.pfcatName }}</td>
									<td class="text-end">
										{{ $filters.formatCurAmt(item.invAmtLc, 0) }}
									</td>
									<td class="text-end">
										{{ $filters.formatCurAmt(item.totDnd, 0) }}
									</td>
									<td class="text-end">
										{{ $filters.formatPct(item.plLc / item.invAmtLc) }}%
									</td>
								</tr>
							</template>
							<!-- 小計列 -->
							<tr class="tx-sum bg-total">
								<td class="text-start">
									{{ $t('cus.subtotal') }}
								</td>
								<td class="text-end">
									{{ $filters.formatCurAmt($filters.sumTotal(group.assetSumAmtProTypes, 'invAmtLc'), 0) }}
								</td>
								<td class="text-end">
									{{ $filters.formatCurAmt($filters.sumTotal(group.assetSumAmtProTypes, 'totDnd'), 0) }}
								</td>
								<td class="text-end">
									{{ $filters.formatPct($filters.sumTotal(group.assetSumAmtProTypes, 'plLc') / $filters.sumTotal(group.assetSumAmtProTypes, 'invAmtLc')) }}%
								</td>
							</tr>
						</template>
					</tbody>
					<tfoot>
						<tr class="tx-sum bg-total">
							<td class="text-start" colspan="2">
								{{ $t('cus.total') }}
							</td>
							<td class="text-end">
								{{ $filters.formatCurAmt(total.invAmtLc, 0) }}
							</td>
							<td class="text-end">
								{{ $filters.formatCurAmt(total.totDnd, 0) }}
							</td>
							<td class="text-end">
								{{ $filters.formatPct(total.plLc / total.invAmtLc) }}%
							</td>
						</tr>
					</tfoot>
				</table>
			</div>
		</div>
	</div>
</template>
<script>
import assetCategoryChart from './amchart/assetCategoryChart.vue';
export default {
	name: 'AssetCategory',
	components: {
		assetCategoryChart
	},
	props: {
		cusCode: {
			type: String,
			default: ''
		},
		uuid: {
			type: String,
			required: false
		}
	},
	data() {
		return {
			pageData: []
		};
	},
	computed: {
		total() {
			if (!this.pageData || this.pageData.length === 0) {
				return {
					invAmtLc: 0,
					totDnd: 0,
					plLc: 0
				};
			}
			let invAmtLc = 0;
			let totDnd = 0;
			let plLc = 0;
			this.pageData.forEach((group) => {
				group.assetSumAmtProTypes.forEach((item) => {
					invAmtLc += item.invAmtLc;
					totDnd += item.totDnd;
					plLc += item.plLc;
				});
			});
			return {
				invAmtLc: invAmtLc,
				totDnd: totDnd,
				plLc: plLc
			};
		},
		overviewChartData() {
			if (!this.pageData || this.pageData.length === 0) {
				return [];
			}
			const data = [];
			this.pageData.forEach((group) => {
				const row = {
					name: group.assetcatName,
					value: 0
				};
				group.assetSumAmtProTypes.forEach((item) => {
					row.value += item.invAmtLc;
				});
				data.push(row);
			});
			return data;
		},
		detailedChartData() {
			if (!this.pageData || this.pageData.length === 0) {
				return [];
			}
			const data = [];
			this.pageData.forEach((group) => {
				group.assetSumAmtProTypes.forEach((item) => {
					data.push({
						name: item.pfcatName,
						value: item.invAmtLc
					});
				});
			});
			return data;
		}
	},
	mounted() {
		this.getPageData();
		this.$nextTick(() => {
			this.setupResizeObserver();
			this.reportHeight();
		});
	},
	updated() {
		this.$nextTick(() => {
			this.reportHeight();
		});
	},
	beforeUnmount() {
		if (this.resizeObserver) {
			this.resizeObserver.disconnect();
		}
	},
	methods: {
		async getPageData() {
			const res = await this.$api.getAssetCategoryApi({
				cusCode: this.cusCode
			});
			console.log('getAssetCategoryApi', res);
			this.pageData = res.data;
		},

		// 設定 ResizeObserver 監控高度變化
		setupResizeObserver() {
			if (window.ResizeObserver) {
				this.resizeObserver = new ResizeObserver((entries) => {
					for (const entry of entries) {
						const height = entry.contentRect.height;
						this.reportHeight(height);
					}
				});

				this.resizeObserver.observe(this.$refs.container);
			}
		},

		// 回報高度給父元件
		reportHeight(customHeight = null) {
			const height = customHeight || this.$refs.container?.offsetHeight || 0;
			if (height > 0 && this.uuid) {
				this.$emit('height-changed', { uuid: this.uuid, height });
			}
		},

		// 取得會員基準幣小數位數
		getCurDecimal: function () {
			const self = this;
			// if (self.customer.baseCurCode == 'TWD') {
			//     return 0; // 台幣顯示至整數位
			// }
			return 2; // 其他顯示至小數後兩位
		},
		formatValue(value, type) {
			switch (type) {
				case 'I': // 千分位整數格式
					return this.formatInteger(value);
				case 'D': // 日期格式
					return this.formatDate(value);
				case 'F': // 浮點數轉為千分位整數格式
					return this.formatFloatAsInteger(value);
				case 'S': // 字串格式
					return value;
					// 其他類型的格式化處理可以在這裡繼續擴展
				default:
					return value; // 預設返回原始值
			}
		},
		formatInteger(value) {
			const numericValue = Number(value);
			return isNaN(numericValue) ? 0 : numeral(numericValue).format('0,0');
		},
		formatDate(value) {
			const date = new Date(value);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`; // 手動格式化為 'YYYY-MM-DD'
		},
		formatFloatAsInteger(value) {
			const numericValue = Number(value);
			return isNaN(numericValue) ? 0 : parseFloat(numericValue).toFixed(0); // 或者其他浮點數處理方式
		}
	}
};
</script>
