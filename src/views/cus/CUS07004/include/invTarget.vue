<template>
	<div ref="container" class="component-container">
		<div class="card card-table mb-3">
			<!-- <div class="card-header d-flex justify-content-between align-items-center">
				<h4 class="mb-0">
					投資標的分析
				</h4>
			</div> -->
			<div class="table-responsive">
				<div class="card-header">
					<h5 class="text-center mb-3">
						{{ $t('cus.byAssetCategory') }}
					</h5>
				</div>
				<div class="card-body row align-items-center">
					<div class="col-xl-4 text-center p-3">
						<PieChart
							v-if="chartDataProType.length > 0"
							ref="chart-inv-pro-type"
							:chart-id="'chart-inv-pro-type'"
							:chart-data="chartDataProType"
						/>
					</div>
					<div class="col-xl-8">
						<div class="tx-13 text-end">
							{{ $t('cus.newTaiwanDollarUnit') }}
						</div>
						<table class="bih-table table table-striped table-hover">
							<thead>
								<tr style="display: table-row;">
									<th>{{ $t('cus.assetCategory') }}</th>
									<th>{{ $t('cus.investmentPrincipalIncludingFees') }}</th>
									<th>{{ $t('cus.investmentRatio') }}</th>
									<th>{{ $t('cus.unrealizedProfitLoss') }}</th>
									<th>{{ $t('cus.returnRateIncludingInterest') }}</th>
									<th>{{ $t('cus.returnRateContribution') }}</th>
								</tr>
							</thead>
							<tbody>
								<template v-for="(proTypeAssetSumAmtm, index) in assetSumAmtProTypes">
									<!-- Parent Row -->
									<tr class="accordion-header">
										<td class="text-start" :data-th="$t('cus.assetCategory')">
											<span class="accordion-toggle">
												{{ proTypeAssetSumAmtm.assetcatName || '--' }}
											</span>
										</td>
										<td :data-th="$t('cus.investmentPrincipalIncludingFees')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatCurAmt(proTypeAssetSumAmtm.invAmtLc, getCurDecimal()) }}
											</label>
										</td>
										<td :data-th="$t('cus.investmentRatio')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatPct(proTypeAssetSumAmtm.invAmtLc / $filters.sumTotal(assetSumAmtProTypes, 'invAmtLc')) }}%
											</label>
										</td>
										<td :data-th="$t('cus.unrealizedProfitLoss')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatCurAmt(proTypeAssetSumAmtm.uplLc, getCurDecimal()) }}
											</label>
										</td>
										<td :data-th="$t('cus.returnRateIncludingInterest')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatPct(proTypeAssetSumAmtm.plLc / proTypeAssetSumAmtm.invAmtLc) }}%
											</label>
										</td>
										<td :data-th="$t('cus.returnRateContribution')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatPct(proTypeAssetSumAmtm.plLc / $filters.sumTotal(assetSumAmtProTypes, 'plLc')) }}%
											</label>
										</td>
									</tr>
									<!-- Child Rows -->
									<tr
										v-for="subProTypeAssetSumAmt in proTypeAssetSumAmtm.assetSumAmtProTypes"
										v-if="proTypeAssetSumAmtm.assetSumAmtProTypes"
									>
										<td :data-th="$t('cus.assetCategory')" class="td-info">
											{{ subProTypeAssetSumAmt.pfcatName || '--' }}
										</td>
										<td :data-th="$t('cus.investmentPrincipalIncludingFees')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatCurAmt(subProTypeAssetSumAmt.invAmtLc, getCurDecimal()) }}
											</label>
										</td>
										<td :data-th="$t('cus.investmentRatio')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatPct(subProTypeAssetSumAmt.invAmtLc / proTypeAssetSumAmtm.invAmtLc) }}%
											</label>
										</td>
										<td :data-th="$t('cus.unrealizedProfitLoss')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatCurAmt(subProTypeAssetSumAmt.uplLc, getCurDecimal()) }}
											</label>
										</td>
										<td :data-th="$t('cus.returnRateIncludingInterest')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatPct(subProTypeAssetSumAmt.plLc / subProTypeAssetSumAmt.invAmtLc) }}%
											</label>
										</td>
										<td :data-th="$t('cus.returnRateContribution')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatPct(subProTypeAssetSumAmt.plLc / proTypeAssetSumAmtm.plLc) }}%
											</label>
										</td>
									</tr>
								</template>
							</tbody>
							<tfoot>
								<tr class="tx-sum bg-total">
									<td class="text-start" :data-th="$t('cus.assetCategory')">
										<label class="text-start">{{ $t('cus.subtotal') }}</label>
									</td>
									<td :data-th="$t('cus.investmentPrincipalIncludingFees')">
										<label class="JQ-valuecolor text-end">
											{{ $filters.formatCurAmt($filters.sumTotal(assetSumAmtProTypes, 'invAmtLc'), getCurDecimal()) }}
										</label>
									</td>
									<td :data-th="$t('cus.investmentRatio')">
										<label class="JQ-valuecolor text-end">
											100%
										</label>
									</td>
									<td :data-th="$t('cus.unrealizedProfitLoss')">
										<label class="JQ-valuecolor text-end">
											{{ $filters.formatCurAmt($filters.sumTotal(assetSumAmtProTypes, 'uplLc'), getCurDecimal()) }}
										</label>
									</td>
									<td :data-th="$t('cus.returnRateIncludingInterest')">
										<label class="JQ-valuecolor text-end">
											{{ $filters.formatPct($filters.sumTotal(assetSumAmtProTypes, 'plLc') / $filters.sumTotal(assetSumAmtProTypes, 'invAmtLc')) }}%
										</label>
									</td>
									<td :data-th="$t('cus.returnRateContribution')">
										<label class="JQ-valuecolor text-end">
											100%
										</label>
									</td>
								</tr>
							</tfoot>
						</table>
					</div>
				</div>
			</div>
			<div class="table-responsive">
				<div class="card-header">
					<h5 class="text-center mb-3">
						{{ $t('cus.byInvestmentTarget') }}
					</h5>
				</div>
				<div class="card-body row align-items-center">
					<div class="col-xl-4 text-center p-3">
						<PieChart
							v-if="chartDataInvTarget.length > 0"
							ref="chart-inv-target"
							:chart-id="'chart-inv-target'"
							:chart-data="chartDataInvTarget"
						/>
					</div>
					<div class="col-xl-8">
						<div class="tx-13 text-end">
							{{ $t('cus.newTaiwanDollarUnit') }}
						</div>
						<table class="bih-table table table-striped table-hover">
							<thead>
								<tr style="display: table-row;">
									<th>{{ $t('cus.investmentTarget') }}</th>
									<th>{{ $t('cus.investmentPrincipalIncludingFees') }}</th>
									<th>{{ $t('cus.investmentRatio') }}</th>
									<th>{{ $t('cus.unrealizedProfitLoss') }}</th>
									<th>{{ $t('cus.returnRateIncludingInterest') }}</th>
									<th>{{ $t('cus.returnRateContribution') }}</th>
								</tr>
							</thead>
							<tbody>
								<template v-for="(item, index) in assetSumAmtInvTargets">
									<tr class="accordion-header">
										<td class="text-start" :data-th="$t('cus.investmentTarget')">
											{{ item.label || '--' }}
										</td>
										<td :data-th="$t('cus.investmentPrincipalIncludingFees')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatCurAmt(item.invAmtLc, getCurDecimal()) }}
											</label>
										</td>
										<td :data-th="$t('cus.investmentRatio')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatPct(item.invAmtLc / $filters.sumTotal(assetSumAmtInvTargets, 'invAmtLc')) }}%
											</label>
										</td>
										<td :data-th="$t('cus.unrealizedProfitLoss')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatCurAmt(item.uplLc, getCurDecimal()) }}
											</label>
										</td>
										<td :data-th="$t('cus.returnRateIncludingInterest')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatPct(item.plLc / item.invAmtLc) }}%
											</label>
										</td>
										<td :data-th="$t('cus.returnRateContribution')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatPct(item.plLc / $filters.sumTotal(assetSumAmtInvTargets, 'plLc')) }}%
											</label>
										</td>
									</tr>
								</template>
							</tbody>
							<tfoot>
								<tr class="tx-sum bg-total">
									<td class="text-start" :data-th="$t('cus.investmentTarget')">
										<label class="text-start">{{ $t('cus.subtotal') }}</label>
									</td>
									<td :data-th="$t('cus.investmentPrincipalIncludingFees')">
										<label class="JQ-valuecolor text-end">
											{{ $filters.formatCurAmt($filters.sumTotal(assetSumAmtInvTargets, 'invAmtLc'), getCurDecimal()) }}
										</label>
									</td>
									<td :data-th="$t('cus.investmentRatio')">
										<label class="JQ-valuecolor text-end">
											100%
										</label>
									</td>
									<td :data-th="$t('cus.unrealizedProfitLoss')">
										<label class="JQ-valuecolor text-end">
											{{ $filters.formatCurAmt($filters.sumTotal(assetSumAmtInvTargets, 'uplLc'), getCurDecimal()) }}
										</label>
									</td>
									<td :data-th="$t('cus.returnRateIncludingInterest')">
										<label class="JQ-valuecolor text-end">
											{{ $filters.formatPct($filters.sumTotal(assetSumAmtInvTargets, 'plLc') / $filters.sumTotal(assetSumAmtInvTargets, 'invAmtLc')) }}%
										</label>
									</td>
									<td :data-th="$t('cus.returnRateContribution')">
										<label class="JQ-valuecolor text-end">
											100%
										</label>
									</td>
								</tr>
							</tfoot>
						</table>
					</div>
				</div>
			</div>
			<div class="table-responsive">
				<div class="card-header">
					<h5 class="text-center mb-3">
						{{ $t('cus.byInvestmentRegion') }}
					</h5>
				</div>
				<div class="card-body row align-items-center">
					<div class="col-xl-4 text-center p-3">
						<PieChart
							v-if="chartDataGeoFocus.length > 0"
							ref="chart-inv-geo-focus"
							:chart-id="'chart-inv-geo-focus'"
							:chart-data="chartDataGeoFocus"
						/>
					</div>
					<div class="col-xl-8">
						<div class="tx-13 text-end">
							{{ $t('cus.newTaiwanDollarUnit') }}
						</div>
						<table class="bih-table table table-striped table-hover">
							<thead>
								<tr style="display: table-row;">
									<th>{{ $t('pro.investmentRegion') }}</th> <!--i18n loss-->
									<th>{{ $t('cus.investmentPrincipalIncludingFees') }}</th>
									<th>{{ $t('cus.investmentRatio') }}</th>
									<th>{{ $t('cus.unrealizedProfitLoss') }}</th>
									<th>{{ $t('cus.returnRateIncludingInterest') }}</th>
									<th>{{ $t('cus.returnRateContribution') }}</th>
								</tr>
							</thead>
							<tbody>
								<template v-for="(item, index) in assetSumAmtGeoFocus">
									<tr class="accordion-header">
										<td class="text-start" :data-th="$t('pro.investmentRegion')">
											{{ item.label || '--' }}
										</td>
										<td :data-th="$t('cus.investmentPrincipalIncludingFees')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatCurAmt(item.invAmtLc, getCurDecimal()) }}
											</label>
										</td>
										<td :data-th="$t('cus.investmentRatio')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatPct(item.invAmtLc / $filters.sumTotal(assetSumAmtGeoFocus, 'invAmtLc')) }}%
											</label>
										</td>
										<td :data-th="$t('cus.unrealizedProfitLoss')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatCurAmt(item.uplLc, getCurDecimal()) }}
											</label>
										</td>
										<td :data-th="$t('cus.returnRateIncludingInterest')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatPct(item.plLc / item.invAmtLc) }}%
											</label>
										</td>
										<td :data-th="$t('cus.returnRateContribution')" class="text-end">
											<label class="JQ-valuecolor">
												{{ $filters.formatPct(item.plLc / $filters.sumTotal(assetSumAmtGeoFocus, 'plLc')) }}%
											</label>
										</td>
									</tr>
								</template>
							</tbody>
							<tfoot>
								<tr class="tx-sum bg-total">
									<td class="text-start" :data-th="$t('pro.investmentRegion')">
										<label class="text-start">{{ $t('cus.subtotal') }}</label>
									</td>
									<td :data-th="$t('cus.investmentPrincipalIncludingFees')">
										<label class="JQ-valuecolor text-end">
											{{ $filters.formatCurAmt($filters.sumTotal(assetSumAmtGeoFocus, 'invAmtLc'), getCurDecimal()) }}
										</label>
									</td>
									<td :data-th="$t('cus.investmentRatio')">
										<label class="JQ-valuecolor text-end">
											100%
										</label>
									</td>
									<td :data-th="$t('cus.unrealizedProfitLoss')">
										<label class="JQ-valuecolor text-end">
											{{ $filters.formatCurAmt($filters.sumTotal(assetSumAmtGeoFocus, 'uplLc'), getCurDecimal()) }}
										</label>
									</td>
									<td :data-th="$t('cus.returnRateIncludingInterest')">
										<label class="JQ-valuecolor text-end">
											{{ $filters.formatPct($filters.sumTotal(assetSumAmtGeoFocus, 'plLc') / $filters.sumTotal(assetSumAmtGeoFocus, 'invAmtLc')) }}%
										</label>
									</td>
									<td :data-th="$t('cus.returnRateContribution')">
										<label class="JQ-valuecolor text-end">
											100%
										</label>
									</td>
								</tr>
							</tfoot>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import PieChart from './amchart/PieChart.vue';
export default {
	name: 'InvTarget',
	components: {
		PieChart
	},
	props: {
		cusCode: {
			type: String,
			default: ''
		},
		componentId: {
			type: String,
			required: false
		}
	},
	data() {
		return {
			assetSumAmtProTypes: [],
			assetSumAmtInvTargets: [],
			assetSumAmtGeoFocus: [],
			assetSumAmtRisk: [],
			chartDataInvTarget: [],
			chartDataProType: [],
			chartDataGeoFocus: [],
			lastReportHeight: 0
		};
	},
	mounted: function () {
		const self = this;
		self.getProTypes();
		self.getInvTargets();
		self.getGeoFocus();
		self.$nextTick();
	},
	updated() {
		this.$nextTick(() => {
			this.reportHeight();
		});
	},
	methods: {
		getProTypes: async function () {
			const self = this;
			const ret = await self.$api.getInvTargetProTypes({
				cusCodes: [self.cusCode]
			});
			self.assetSumAmtProTypes = ret.data;
			self.genProTypeChartData();
		},
		genProTypeChartData: function () {
			const self = this;
			self.assetSumAmtProTypes.forEach(function (layer1) {
				// 第一層
				const chartData = {
					name: layer1.assetcatName,
					value: layer1.invAmtLc + 10
				};
				self.chartDataProType.push(chartData);
			});
		},
		getInvTargets: async function () {
			const self = this;
			const ret = await self.$api.getInvTargetApi({
				cusCodes: [self.cusCode]
			});
			self.assetSumAmtInvTargets = ret.data;
			self.genInvTargetChartData();
		},
		genInvTargetChartData: function () {
			const self = this;
			self.assetSumAmtInvTargets.forEach(function (item) {
				const chartData = {
					name: item.label,
					value: item.invAmtLc
				};
				self.chartDataInvTarget.push(chartData);
			});
		},
		getGeoFocus: async function () {
			const self = this;
			const ret = await self.$api.getInvTargetGeoFocusApi({
				cusCodes: [self.cusCode]
			});
			self.assetSumAmtGeoFocus = ret.data;
			self.genGeoFocusChartData();
		},
		genGeoFocusChartData: function () {
			const self = this;
			self.assetSumAmtGeoFocus.forEach(function (item) {
				const chartData = {
					name: item.label,
					value: item.invAmtLc
				};
				self.chartDataGeoFocus.push(chartData);
			});
		},
		// 取得會員基準幣小數位數
		getCurDecimal: function () {
			const self = this;
			// if (self.customer.baseCurCode == 'TWD') {
			//     return 0; // 台幣顯示至整數位
			// }
			return 2; // 其他顯示至小數後兩位
		},
		formatValue(value, type) {
			switch (type) {
				case 'I': // 千分位整數格式
					return this.formatInteger(value);
				case 'D': // 日期格式
					return this.formatDate(value);
				case 'F': // 浮點數轉為千分位整數格式
					return this.formatFloatAsInteger(value);
				case 'S': // 字串格式
					return value;
					// 其他類型的格式化處理可以在這裡繼續擴展
				default:
					return value; // 預設返回原始值
			}
		},
		formatInteger(value) {
			const numericValue = Number(value);
			return isNaN(numericValue) ? 0 : numeral(numericValue).format('0,0');
		},
		formatDate(value) {
			const date = new Date(value);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`; // 手動格式化為 'YYYY-MM-DD'
		},
		formatFloatAsInteger(value) {
			const numericValue = Number(value);
			return isNaN(numericValue) ? 0 : parseFloat(numericValue).toFixed(0); // 或者其他浮點數處理方式
		},

		// 回報高度給父元件
		reportHeight() {
			const height = this.$refs.container?.offsetHeight || 0;

			if (this.lastReportHeight == height) {
				return; // 如果高度沒有變化，則不報告
			}

			console.log(`invTarget 報告高度變化: ${this.lastReportHeight} -> ${height}`);
			this.lastReportHeight = height;

			if (height > 0 && this.componentId) {
				this.$emit('height-changed', { componentId: this.componentId, height });
			}
		}
	}
};
</script>
