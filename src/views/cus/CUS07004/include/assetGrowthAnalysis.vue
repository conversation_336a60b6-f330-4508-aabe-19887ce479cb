<template>
	<div ref="container" class="component-container">
		<div class="card card-table mb-3">
			<div class="card-header d-flex justify-content-between align-items-center">
				<h4 class="mb-0">
					{{ $t('cus.wealthGrowthAnalysis') }}
				</h4>
			</div>

			<div class="table-responsive">
				<div class="row">
					<div class="text-muted text-end mb-3">
						{{ $t('cus.dataDate') }}：{{ reportData.dataDate }}
					</div>

					<!-- 圖表區域 -->
					<div class="col-lg-6">
						<div class="chart-container">
							<h6 class="text-center mb-3">
								{{ $t('cus.investmentPrincipalVsCurrentValue') }}
							</h6>
							<asset-growth-analysis-chart1
								v-show="cusCode !== ''"
								ref="chart-wealth1"
								:chart-id="'chart-wealth1'"
								:cus-code="cusCode"
							/>
						</div>
					</div>

					<div class="col-lg-6">
						<div class="chart-container">
							<h6 class="text-center mb-3">
								{{ $t('cus.periodReturnTrend') }}
							</h6>
							<asset-growth-analysis-chart2
								v-show="cusCode !== ''"
								ref="chart-wealth2"
								:chart-id="'chart-wealth2'"
								:cus-code="cusCode"
							/>
						</div>
					</div>
				</div>

				<!-- 表格區域 -->
				<div class="table-section mt-4">
					<div class="tx-13 text-end mb-2">
						{{ $t('cus.newTaiwanDollarUnit') }}
					</div>
					<table class="bih-table table table-striped table-hover">
						<thead class="table-dark">
							<tr>
								<th colspan="5">
									{{ $t('cus.recent12MonthInvestmentChange') }}
								</th>
								<th colspan="3">
									{{ reportData.currentMonth }} {{ $t('cus.accountMarketValue') }}：{{ formatNumber(reportData.currentValue) }}
								</th>
							</tr>
							<tr>
								<th>{{ $t('cus.date') }}</th>
								<th class="text-end">
									{{ $t('cus.investmentPrincipalIncludingFees') }}
								</th>
								<th class="text-end">
									{{ $t('cus.realizedProfitLoss') }}
								</th>
								<th class="text-end">
									{{ $t('cus.unrealizedProfitLoss') }}
								</th>
								<th class="text-end">
									{{ $t('cus.accumulatedDividend') }}
								</th>
								<th class="text-end">
									{{ $t('cus.principalChange') }}
								</th>
								<th class="text-end">
									{{ $t('cus.currentValue') }}
								</th>
								<th class="text-end">
									{{ $t('cus.periodReturnRate') }}
								</th>
							</tr>
						</thead>
						<tbody>
							<template v-if="wealthAnalysisReversed.length">
								<tr v-for="(wealthData) in wealthAnalysisReversed">
									<td data-th="日期">
										{{ wealthData.dataYm || '--' }}
									</td>
									<td class="text-end" data-th="投資本金">
										{{ formatCurAmt(wealthData.invAmtLc, 0) }}
									</td>
									<td class="text-end" data-th="已實現損益">
										<HighLightDeltaText :value="wealthData.totRplLc">
											{{ formatCurAmt(wealthData.totRplLc, 0) }}
										</HighLightDeltaText>
									</td>
									<td class="text-end" data-th="未實現損益">
										<HighLightDeltaText :value="wealthData.totUplLc">
											{{ formatCurAmt(wealthData.totUplLc, 0) }}
										</HighLightDeltaText>
									</td>
									<td class="text-end" data-th="累計配息">
										{{ formatCurAmt(wealthData.totDividendLc, 0) }}
									</td>
									<td class="text-end" data-th="投資本金(含手續費)增減">
										{{ formatCurAmt(wealthData.totDiffAmtLc, 0) }}
									</td>
									<td class="text-end" data-th="投資現值">
										{{ formatCurAmt(wealthData.mktAmtLc, 0) }}
									</td>
									<td class="text-end" data-th="含息期間報酬率(%)">
										<HighLightDeltaText :value="wealthData.twrLc">
											{{ wealthData.twrLc.toFixed(2) }}%
										</HighLightDeltaText>
									</td>
								</tr>
							</template>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import assetGrowthAnalysisChart1 from './amchart/assetGrowthAnalysisChart1.vue';
import assetGrowthAnalysisChart2 from './amchart/assetGrowthAnalysisChart2.vue';

import {
	formatCurAmt,
	formatPct,
	formatYearMonthOnly
} from '@/utils/filter';

export default {
	name: 'AssetGrowthAnalysis',
	components: {
		assetGrowthAnalysisChart1,
		assetGrowthAnalysisChart2
	},
	props: {
		componentId: {
			type: String,
			required: true
		},
		cusCode: {
			type: String,
			default: ''
		},
		componentData: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			reportData: {},
			wealthAnalysis: [],
			// resizeObserver: null,
			lastReportHeight: 0
		};
	},
	computed: {
		wealthAnalysisReversed() {
			return this.wealthAnalysis.toSorted((a, b) => -a.dataYm.localeCompare(b.dataYm));
		}
	},
	mounted() {
		this.getTableData();
		this.$nextTick();
	},
	updated() {
		this.$nextTick(() => {
			this.reportHeight();
		});
	},
	methods: {
		formatCurAmt(value, precision, customFormat) {
			return formatCurAmt(value, precision, customFormat);
		},
		formatPct(value) {
			return formatPct(value);
		},
		formatYearMonthOnly(value) {
			return formatYearMonthOnly(value);
		},
		async getTableData() {
			const self = this;
			self.$api
				.getWealthAnalysisEachMonthApi({ cusCodes: self.cusCode })
				.then(function (ret) {
					if (!ret.data) return;
					self.wealthAnalysis = ret.data.wealthAnalysis;
					self.wealthAnalysisMonthly = ret.data.wealthAnalysisMonthly;
					// 日期排序
					self.wealthAnalysis.sort((a, b) => {
						return new Date(a.dataYm) - new Date(b.dataYm);
					});
				});
			console.log('獲取表格資料:', self.wealthAnalysis);
		},

		// 回報高度給父元件
		reportHeight() {
			const height = this.$refs.container?.offsetHeight || 0;

			if (this.lastReportHeight === height) {
				return; // 如果高度沒有變化，則不報告
			}

			console.log(`assetGrowth 報告高度變化: ${this.lastReportHeight} -> ${height}`);
			this.lastReportHeight = height;

			if (height > 0 && this.componentId) {
				this.$emit('height-changed', { componentId: this.componentId, height });
			}
		},

		// 數字格式化
		formatNumber(num) {
			if (typeof num !== 'number') return num;
			return new Intl.NumberFormat('zh-TW').format(num);
		},

		// 根據數值正負設定文字顏色
		getTextColor(value) {
			if (typeof value === 'string') {
				value = parseFloat(value.replace('%', ''));
			}
			if (value > 0) return 'text-success';
			if (value < 0) return 'text-danger';
			return '';
		}
	}
};
</script>

<style scoped>

.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.tx-13 {
  font-size: 0.8rem;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .dbchart-container {
    height: 200px;
  }

  .bih-table {
    font-size: 0.8rem;
  }
}
</style>
