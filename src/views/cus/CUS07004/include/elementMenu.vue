<template>
	<div class="card-table">
		<div class="card-header fw-bold">
			{{ $t('cus.components') }}
		</div>
		<div class="card-body p-2" style="overflow-y:auto; max-height: 80vh;">
			<ul class="aside-menu list-unstyled">
				<!-- 基礎元件 -->
				<li>
					<button class="btn btn-link w-100 d-flex justify-content-between align-items-center text-start" @click="toggleCategory('basic')">
						<span>{{ $t('cus.basicComponents') }}</span>
						<i :class="expanded.basic ? 'bi bi-chevron-down' : 'bi bi-chevron-right'" />
					</button>
					<ul v-show="expanded.basic" class="list-group ms-3 mb-2">
						<li
							v-for="comp in elementMenu.basic"
							:key="comp.id"
							class="list-group-item d-flex align-items-center justify-content-between element-gap"
							draggable="true"
							@dragstart="onDragStart(comp, $event)"
						>
							<span>{{ comp.name }}</span>
						</li>
					</ul>
				</li>
				<!-- 客戶總覽 -->
				<li>
					<button class="btn btn-link w-100 d-flex justify-content-between align-items-center text-start" @click="toggleCategory('overview')">
						<span>{{ $t('cus.customerOverview') }}</span>
						<i :class="expanded.overview ? 'bi bi-chevron-down' : 'bi bi-chevron-right'" />
					</button>
					<ul v-show="expanded.overview" class="list-group ms-3 mb-2">
						<li
							v-for="comp in elementMenu.overview"
							:key="comp.id"
							class="list-group-item d-flex align-items-center justify-content-between element-gap"
							draggable="true"
							@dragstart="onDragStart(comp, $event)"
						>
							<span>{{ comp.name }}</span>
						</li>
					</ul>
				</li>
				<!-- 投資計畫 -->
				<li>
					<button class="btn btn-link w-100 d-flex justify-content-between align-items-center text-start" @click="toggleCategory('plan')">
						<span>{{ $t('cus.investmentPlan') }}</span>
						<i :class="expanded.plan ? 'bi bi-chevron-down' : 'bi bi-chevron-right'" />
					</button>
					<ul v-show="expanded.plan" class="list-group ms-3 mb-2">
						<li
							v-for="comp in elementMenu.plan"
							:key="comp.id"
							class="list-group-item d-flex align-items-center justify-content-between element-gap"
							draggable="true"
							@dragstart="onDragStart(comp, $event)"
						>
							<span>{{ comp.name }}</span>
						</li>
					</ul>
				</li>
				<!-- 投資績效分析 -->
				<li>
					<button class="btn btn-link w-100 d-flex justify-content-between align-items-center text-start" @click="toggleCategory('performance')">
						<span>{{ $t('cus.investmentPerformanceAnalysis') }}</span>
						<i :class="expanded.performance ? 'bi bi-chevron-down' : 'bi bi-chevron-right'" />
					</button>
					<ul v-show="expanded.performance" class="list-group ms-3 mb-2">
						<li
							v-for="comp in elementMenu.performance"
							:key="comp.id"
							class="list-group-item d-flex align-items-center justify-content-between element-gap"
							draggable="true"
							@dragstart="onDragStart(comp, $event)"
						>
							<span>{{ comp.name }}</span>
						</li>
					</ul>
				</li>
				<!-- 交易紀錄查詢 -->
				<li>
					<button class="btn btn-link w-100 d-flex justify-content-between align-items-center text-start" @click="toggleCategory('trans')">
						<span>{{ $t('cus.transactionRecordQuery') }}</span>
						<i :class="expanded.trans ? 'bi bi-chevron-down' : 'bi bi-chevron-right'" />
					</button>
					<ul v-show="expanded.trans" class="list-group ms-3 mb-2">
						<li
							v-for="comp in elementMenu.trans"
							:key="comp.id"
							class="list-group-item d-flex align-items-center justify-content-between element-gap"
							draggable="true"
							@dragstart="onDragStart(comp, $event)"
						>
							<span>{{ comp.name }}</span>
						</li>
					</ul>
				</li>
			</ul>
		</div>
	</div>
</template>

<script>

export default {
	data() {
		return {
			expanded: {
				basic: true,
				overview: true,
				plan: true,
				performance: true,
				trans: true
			},
			elementMenu: {}
		};
	},
	mounted() {
		this.getElementMenu();
	},
	methods: {
		toggleCategory(cat) {
			this.expanded[cat] = !this.expanded[cat];
		},
		onDragStart(comp, event) {
			event.dataTransfer.setData('component', JSON.stringify(comp));
		},
		async getElementMenu() {
			const menu = await this.$api.getElementMenu();
			this.elementMenu = menu;
		}
	}
};
</script>

<style scoped>
.element-gap {
  margin-bottom: 4px;
}
.card-body {
  max-height: 80vh;
  overflow-y: auto;
}
</style>
