<template>
	<div class="text-center">
		<div :id="chartId" :ref="chartId" style="height:280px;" />
	</div>
</template>
<script>
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { toRaw } from 'vue';

export default {
	name: 'AssetGrowthAnalysisChart',
	props: {
		cusCode: {
			type: String,
			default: ''
		},
		chartId: String

	},
	data() {
		return {
			am5Obj: {},
			wealthChartXAxis: [],
			wealthChartData: []
		};
	},
	mounted() {
		this.getChartData();
	},
	methods: {
		async getChartData() {
			const self = this;
			const ret = await self.$api.getWealthAnalysisEachMonthApi({ cusCodes: self.cusCode });

			if (!ret.data) return;

			self.wealthAnalysis = ret.data.wealthAnalysis;
			self.wealthAnalysisMonthly = ret.data.wealthAnalysisMonthly;

			// 日期排序
			self.wealthAnalysis.sort((a, b) => {
				return new Date(a.dataYm) - new Date(b.dataYm);
			});

			self.wealthAnalysisMonthly.sort((a, b) => {
				return new Date(a.dataYm) - new Date(b.dataYm);
			});

			const invAmtLc = {
				type: 'column',
				name: '投資本金(含手續費)',
				data: []
			};
			const mktAmtLc = {
				type: 'column',
				name: '投資現值',
				data: []
			};

			self.wealthChartXAxis = [];

			// 處理圖表數據
			self.wealthAnalysis.forEach(function (wealthData) {
				self.wealthChartXAxis.push(wealthData.dataYm);
				invAmtLc.data.push(wealthData.invAmtLc);
				mktAmtLc.data.push(wealthData.mktAmtLc);
			});

			self.wealthChartData = [invAmtLc, mktAmtLc];

			console.log('獲取圖表資料:', self.wealthChartData);
			this.initChart();
		},
		initChart() {
			const self = this;
			// Create root element
			// https://www.amcharts.com/docs/v5/getting-started/#Root_element
			const { am5Obj } = self;

			let firstLoad = false;
			// 透過 toRaw 解決因為 vue data proxy 導致跟 amcharts 衝突
			let { xAxis, yAxis, root, chart, legend } = toRaw(am5Obj); // 用toRaw把元件load出來
			if (!root) {
				firstLoad = true;

				// Create root element
				// https://www.amcharts.com/docs/v5/getting-started/#Root_element
				root = am5.Root.new(self.chartId); // 由商品頁塞入id
				root._logo.dispose();

				root.setThemes([am5themes_Animated.new(root)]);

				// Create chart
				// https://www.amcharts.com/docs/v5/charts/xy-chart/
				chart = root.container.children.push(
					am5xy.XYChart.new(root, {
						panX: false,
						panY: false,
						paddingLeft: 0,
						wheelX: 'panX',
						wheelY: 'zoomX',
						layout: root.verticalLayout
					})
				);

				chart.get('colors').set('colors', [
					am5.color(0x5090DC),
					am5.color(0x666666)
				]);

				// Create axes X軸
				// https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
				const xRenderer = am5xy.AxisRendererX.new(root, {
					cellStartLocation: 0.1,
					cellEndLocation: 0.9,
					minorGridEnabled: true
				});
				xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
					categoryField: 'category',
					renderer: xRenderer,
					tooltip: am5.Tooltip.new(root, {})
				}));

				// X軸數據
				xAxis.data.setAll(self.wealthChartXAxis.map(label => ({ category: label })));

				xRenderer.grid.template.setAll({
					location: 1
				});
				xRenderer.labels.template.setAll({
					rotation: -45,
					centerY: am5.p50,
					fontSize: 12
				});
				// hide grid
				xRenderer.grid.template.set('visible', false);

				// Y軸
				yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
					renderer: am5xy.AxisRendererY.new(root, {
						strokeOpacity: 0.1
					})
				}));
			}
			else {
				chart.series.clear();
				legend.data.clear();
			}

			if (firstLoad) {
				const cursor = chart.set(
					'cursor',
					am5xy.XYCursor.new(root, {
						xAxis: xAxis
					})
				);
				cursor.lineY.set('visible', false);

				// Add legend 圖表最上面線的標示(含名稱)
				// https://www.amcharts.com/docs/v5/charts/xy-chart/legend-xy-series/
				legend = chart.children.push(
					am5.Legend.new(root, {
						centerX: am5.p50,
						x: am5.p50
					})
				);
				legend.markers.template.setAll({
					width: 6,
					height: 6
				});

				Object.assign(am5Obj, { xAxis, yAxis, root, chart, legend });
			}

			// Add series
			// https://www.amcharts.com/docs/v5/charts/xy-chart/series

			// 創建柱狀圖系列
			self.wealthChartData.forEach((seriesData) => {
				console.log('seriesData:', seriesData);
				const series = chart.series.push(
					am5xy.LineSeries.new(root, {
						name: seriesData.name,
						xAxis: xAxis,
						yAxis: yAxis,
						valueYField: 'value',
						categoryXField: 'category',
						tooltip: am5.Tooltip.new(root, {
							pointerOrientation: 'horizontal',
							labelText: '{valueY} '
						})
					})
				);

				series.strokes.template.setAll({
					strokeWidth: 1
				});
				series.fills.template.setAll({
					fillOpacity: 0.1,
					visible: true
				});

				// 設置數據
				series.data.setAll(
					seriesData.data.map((value, index) => ({
						category: self.wealthChartXAxis[index],
						value: Number(value) || 0
					}))
				);
				console.log('series.data:', series);
				series.appear(1000);
			});

			legend.data.setAll(chart.series.values);

			// Make stuff animate on load
			// https://www.amcharts.com/docs/v5/concepts/animations/
			if (firstLoad) {
				chart.appear(1000, 100);
			}
		}
	}
};
</script>
