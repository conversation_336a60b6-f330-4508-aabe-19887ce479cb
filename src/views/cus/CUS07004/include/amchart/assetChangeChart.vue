<template>
	<div class="text-center">
		<div :id="chartId" :ref="chartId" style="height: 500px;" />
	</div>
</template>
<script>
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { toRaw } from 'vue';

export default {
	name: 'AssetChangeChart',
	props: {
		chartData: Array,
		chartId: String

	},
	data() {
		return {
			am5Obj: {}
		};
	},
	mounted() {
		this.initChart();
	},
	methods: {
		initChart() {
			const self = this;
			// Create root element
			// https://www.amcharts.com/docs/v5/getting-started/#Root_element
			const { am5Obj } = self;
			// 處理props proxy data問題
			// chartData = JSON.parse(JSON.stringify(chartData));
			let firstLoad = false;
			// 透過 toRaw 解決因為 vue data proxy 導致跟 amcharts 衝突
			let { xAxis, yAxis, root, chart, legend } = toRaw(am5Obj); // 用toRaw把元件load出來
			if (!root) {
				firstLoad = true;

				// Create root element
				// https://www.amcharts.com/docs/v5/getting-started/#Root_element
				root = am5.Root.new(self.chartId); // 由商品頁塞入id
				root._logo.dispose();

				root.setThemes([am5themes_Animated.new(root)]);

				// Create chart
				// https://www.amcharts.com/docs/v5/charts/xy-chart/
				chart = root.container.children.push(
					am5xy.XYChart.new(root, {
						panX: false,
						panY: false,
						paddingLeft: 0,
						wheelX: 'panX',
						wheelY: 'zoomX',
						layout: root.verticalLayout
					})
				);

				chart.get('colors').set('colors', [
					am5.color(0x4A90E2),
					am5.color(0x357ABD),
					am5.color(0x5DB075),
					am5.color(0x4C8C6A),
					am5.color(0x72C2A2),
					am5.color(0xA9DCD6),
					am5.color(0xB5B5B5),
					am5.color(0x8FA3A8),
					am5.color(0x6D7B8D),
					am5.color(0xB0C4DE),
					am5.color(0x99B2B)
				]);

				// Create axes X軸
				// https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
				xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
					categoryField: 'year',
					renderer: am5xy.AxisRendererX.new(root, {
						minorGridEnabled: true,
						minGridDistance: 20
					}),
					tooltip: am5.Tooltip.new(root, {})
				}));
				xAxis.data.setAll(self.chartData);

				// Y軸
				yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
					min: 0,
					numberFormat: '#,###',
					calculateTotals: true,
					renderer: am5xy.AxisRendererY.new(root, {
						strokeOpacity: 0.1
					})
				}));
			}
			else {
				chart.series.clear();
				legend.data.clear();
			}

			if (firstLoad) {
				// Add legend 圖表最上面線的標示(含名稱)
				// https://www.amcharts.com/docs/v5/charts/xy-chart/legend-xy-series/
				legend = chart.children.push(
					am5.Legend.new(root, {
						centerX: am5.p50,
						x: am5.p50
					})
				);
				legend.markers.template.setAll({
					width: 6,
					height: 6
				});

				Object.assign(am5Obj, { xAxis, yAxis, root, chart, legend });
			}

			// Add series
			// https://www.amcharts.com/docs/v5/charts/xy-chart/series
			function makeSeries(name, field) {
				const series = chart.series.push(am5xy.ColumnSeries.new(root, {
					name: name,
					stacked: true,
					xAxis: xAxis,
					yAxis: yAxis,
					valueYField: field,
					categoryXField: 'year'
				}));

				series.columns.template.setAll({
					tooltipText: '{name}: {valueY}',
					tooltipY: am5.percent(90),
					width: am5.percent(50)
				});

				series.data.setAll(self.chartData);
				series.appear();
				legend.data.push(series);
			}

			makeSeries('活存', '活存');
			makeSeries('定存', '定存');
			makeSeries('信託-基金', '信託-基金');
			makeSeries('信託-海外債券', '信託-海外債券');
			makeSeries('信託-海外股票(含特別股)', '信託-海外股票(含特別股)');
			makeSeries('信託-ETF', '信託-ETF');
			makeSeries('信託-結構型商品', '信託-結構型商品');
			makeSeries('黃金存摺', '黃金存摺');
			makeSeries('財金', '財金');
			makeSeries('證券', '證券');
			makeSeries('保險', '保險');

			// Make stuff animate on load
			// https://www.amcharts.com/docs/v5/concepts/animations/
			if (firstLoad) {
				chart.appear(1000, 100);
			}
		}
	}
};
</script>
