<template>
	<div class="text-center">
		<div :id="chartId" :ref="chartId" style="height: 500px;" />
	</div>
</template>
<script>
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { toRaw } from 'vue';

export default {
	name: 'AssetLoanChangeChart',
	props: {
		chartData: Array,
		chartId: String
	},
	data() {
		return {
			am5Obj: {}
		};
	},
	mounted() {
		this.initChart();
	},
	methods: {
		initChart() {
			const self = this;
			// Create root element
			// https://www.amcharts.com/docs/v5/getting-started/#Root_element
			const { am5Obj } = self;
			// 處理props proxy data問題
			// chartData = JSON.parse(JSON.stringify(chartData));
			let firstLoad = false;
			// 透過 toRaw 解決因為 vue data proxy 導致跟 amcharts 衝突
			let { xAxis, yAxis, root, chart, legend } = toRaw(am5Obj); // 用toRaw把元件load出來
			if (!root) {
				firstLoad = true;

				// Create root element
				// https://www.amcharts.com/docs/v5/getting-started/#Root_element
				root = am5.Root.new(self.chartId); // 由商品頁塞入id
				root._logo.dispose();

				root.setThemes([am5themes_Animated.new(root)]);

				// Create chart
				// https://www.amcharts.com/docs/v5/charts/xy-chart/
				chart = root.container.children.push(
					am5xy.XYChart.new(root, {
						panX: false,
						panY: false,
						wheelX: 'none',
						wheelY: 'none',
						layout: root.verticalLayout
					})
				);

				// Create axes X軸
				// https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
				const xRenderer = am5xy.AxisRendererX.new(root, {
					minGridDistance: 30
				});
				xAxis = chart.xAxes.push(
					am5xy.CategoryAxis.new(root, {
						maxDeviation: 0.3,
						categoryField: 'category',
						renderer: xRenderer
					})
				);
				xAxis.data.setAll(self.chartData);

				// Y軸
				yAxis = chart.yAxes.push(
					am5xy.ValueAxis.new(root, {
						renderer: am5xy.AxisRendererY.new(root, {})
					})
				);
			}
			else {
				chart.series.clear();
				legend.data.clear();
			}

			if (firstLoad) {
				const cursor = chart.set(
					'cursor',
					am5xy.XYCursor.new(root, {
						xAxis: xAxis
					})
				);

				// Add legend 圖表最上面線的標示(含名稱)
				// https://www.amcharts.com/docs/v5/charts/xy-chart/legend-xy-series/
				legend = chart.children.push(
					am5.Legend.new(root, {
						centerX: am5.p50,
						x: am5.p50
					})
				);
				legend.markers.template.setAll({
					width: 6,
					height: 6
				});

				Object.assign(am5Obj, { xAxis, yAxis, root, chart, legend });
			}

			// Add series
			// https://www.amcharts.com/docs/v5/charts/xy-chart/series
			const assetSeries = chart.series.push(
				am5xy.ColumnSeries.new(root, {
					name: '資產',
					xAxis: xAxis,
					yAxis: yAxis,
					valueYField: '資產',
					categoryXField: 'category',
					fill: am5.color(0x5090DC),
					stroke: am5.color(0x5090DC),
					clustered: true,
					tooltip: am5.Tooltip.new(root, {
						labelText: '{valueY}'
					})
				})
			);
			assetSeries.columns.template.setAll({
				width: am5.percent(50)
			});
			assetSeries.bullets.push(function () {
				return am5.Bullet.new(root, {
					locationX: 0.5, // 可調整為 0.5 居中、0 靠左、1 靠右
					locationY: 1, // 基於 bar 的頂部
					sprite: am5.Label.new(root, {
						text: '{valueY}',
						fill: am5.color(0x000000),
						centerY: am5.p50,
						centerX: am5.p0,
						populateText: true,
						fontSize: 12,
						rotation: -90
					})
				});
			});
			assetSeries.data.setAll(self.chartData);

			const debtSeries = chart.series.push(
				am5xy.ColumnSeries.new(root, {
					name: '負債',
					xAxis: xAxis,
					yAxis: yAxis,
					valueYField: '負債',
					categoryXField: 'category',
					fill: am5.color(0xFFA03A),
					stroke: am5.color(0xFFA03A),
					clustered: true,
					tooltip: am5.Tooltip.new(root, {
						labelText: '{valueY}'
					})
				})
			);
			debtSeries.columns.template.setAll({
				width: am5.percent(50)
			});
			debtSeries.data.setAll(self.chartData);

			legend.data.push(assetSeries);
			legend.data.push(debtSeries);
			// Make stuff animate on load
			// https://www.amcharts.com/docs/v5/concepts/animations/
			if (firstLoad) {
				chart.appear(1000, 100);
			}
		}
	}
};
</script>
