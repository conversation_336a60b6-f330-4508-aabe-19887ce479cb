<template>
	<div class="text-center">
		<div :id="chartId" :ref="chartId" style="height: 500px;" />
	</div>
</template>
<script>
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { toRaw } from 'vue';

export default {
	name: 'NetValAnalysisChart1',
	props: {
		chartData: Array,
		chartId: String
	},
	data() {
		return {
			am5Obj: {}
		};
	},
	mounted() {
		const self = this;
		self.initChart();
	},
	methods: {
		initChart() {
			const self = this;
			// Create root element
			// https://www.amcharts.com/docs/v5/getting-started/#Root_element
			const { am5Obj } = self;
			// 處理props proxy data問題
			// chartData = JSON.parse(JSON.stringify(chartData));
			let firstLoad = false;
			// 透過 toRaw 解決因為 vue data proxy 導致跟 amcharts 衝突
			let { xAxis, yAxis, yAxis2, root, chart, legend } = toRaw(am5Obj);
			if (!root) {
				firstLoad = true;

				// Create root element
				// https://www.amcharts.com/docs/v5/getting-started/#Root_element
				root = am5.Root.new(self.chartId);
				root._logo.dispose();

				root.setThemes([am5themes_Animated.new(root)]);

				// Create chart
				// https://www.amcharts.com/docs/v5/charts/xy-chart/
				chart = root.container.children.push(
					am5xy.XYChart.new(root, {
						panX: false,
						panY: false,
						paddingLeft: 0,
						wheelX: 'panX',
						wheelY: 'zoomX',
						layout: root.verticalLayout
					})
				);

				chart.get('colors').set('colors', [
					am5.color('#5090DC'),
					am5.color('#FFA03A'),
					am5.color('#2c2c2c')
				]);

				// Create axes X軸
				// https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
				const xRenderer = am5xy.AxisRendererX.new(root, {
					cellStartLocation: 0.2,
					cellEndLocation: 0.8,
					minorGridEnabled: true
				});

				xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
					categoryField: 'year',
					renderer: xRenderer,
					tooltip: am5.Tooltip.new(root, {})
				}));

				xRenderer.grid.template.setAll({
					location: 1
				});
				xRenderer.labels.template.setAll({
					rotation: 0,
					centerY: am5.p50,
					fontSize: 12
				});

				// hide grid
				xRenderer.grid.template.set('visible', false);

				xAxis.data.setAll(self.chartData);

				// Y軸
				yAxis = chart.yAxes.push(
					am5xy.ValueAxis.new(root, {
						renderer: am5xy.AxisRendererY.new(root, {
							strokeOpacity: 0.1
						})
					})
				);
				yAxis2 = chart.yAxes.push(
					am5xy.ValueAxis.new(root, {
						maxDeviation: 0.3,
						syncWithAxis: yAxis,
						renderer: am5xy.AxisRendererY.new(root, { opposite: true })
					})
				);
			}
			else {
				chart.series.clear();
				legend.data.clear();
			}

			if (firstLoad) {
				// Add cursor
				const cursor = chart.set(
					'cursor',
					am5xy.XYCursor.new(root, {
						xAxis: xAxis
					})
				);

				// Add legend 圖表最上面線的標示(含名稱)
				// https://www.amcharts.com/docs/v5/charts/xy-chart/legend-xy-series/
				legend = chart.children.push(
					am5.Legend.new(root, {
						centerX: am5.p50,
						x: am5.p50
					})
				);
				legend.markers.template.setAll({
					width: 10,
					height: 10
				});
				Object.assign(am5Obj, { xAxis, yAxis, yAxis2, root, chart });
			}

			// Add series
			// https://www.amcharts.com/docs/v5/charts/xy-chart/series
			function makeSeries(name, field) {
				const series = chart.series.push(am5xy.ColumnSeries.new(root, {
					name: name,
					xAxis: xAxis,
					yAxis: yAxis,
					valueYField: field,
					categoryXField: 'year'
				}));

				series.columns.template.setAll({
					tooltipText: '{name}, {categoryX}: ${valueY}',
					width: am5.percent(75),
					tooltipY: 0,
					strokeOpacity: 0
				});

				series.data.setAll(self.chartData);

				// Make stuff animate on load
				// https://www.amcharts.com/docs/v5/concepts/animations/
				series.appear();

				series.bullets.push(function () {
					return am5.Bullet.new(root, {
						locationX: 0.5, // 可調整為 0.5 居中、0 靠左、1 靠右
						locationY: 1, // 基於 bar 的頂部
						sprite: am5.Label.new(root, {
							text: '{valueY}',
							fill: am5.color(0x000000),
							centerY: am5.p50,
							centerX: am5.p0,
							populateText: true,
							fontSize: 12,
							rotation: -90
						})
					});
				});

				legend.data.push(series);
			}

			makeSeries('投資本金(含手續費)', 'total');
			makeSeries('投資現值', 'capital');

			const series2 = chart.series.push(
				am5xy.LineSeries.new(root, {
					name: '報酬率',
					xAxis: xAxis,
					yAxis: yAxis2,
					valueYField: 'reward',
					categoryXField: 'year',
					tooltip: am5.Tooltip.new(root, {
						pointerOrientation: 'horizontal',
						labelText: '報酬率：{valueY} '
					})
				})
			);

			series2.strokes.template.setAll({
				strokeWidth: 1,
				templateField: 'strokeSettings'
			});

			series2.data.setAll(self.chartData);

			legend.data.push(series2);

			// Make stuff animate on load
			// https://www.amcharts.com/docs/v5/concepts/animations/
			if (firstLoad) {
				chart.appear(1000, 100);
			}
		}
	}
};
</script>
