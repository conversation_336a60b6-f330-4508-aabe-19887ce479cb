<template>
	<div class="text-center">
		<div :id="chartId" :ref="chartId" style="height: 280px;" />
	</div>
</template>
<script>
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { toRaw } from 'vue';
import { _ } from 'lodash';

export default {
	name: 'AssetGrowthChart2',
	props: {
		cusCode: {
			type: String,
			default: ''
		},
		chartId: String

	},
	data() {
		return {
			am5Obj: {},
			wealthDailyChartXAxis: [],
			wealthChartDailyData: []
		};
	},
	mounted() {
		this.getChartData();
	},
	methods: {
		async getChartData() {
			const self = this;

			const ret = await self.$api.getWealthAnalysisEachMonthApi({ cusCodes: self.cusCode });

			self.wealthAnalysis = ret.data.wealthAnalysis;
			self.wealthAnalysisMonthly = ret.data.wealthAnalysisMonthly;

			// 日期排序
			self.wealthAnalysis.sort((a, b) => {
				return new Date(a.dataYm) - new Date(b.dataYm);
			});

			self.wealthAnalysisMonthly.sort((a, b) => {
				return new Date(a.dataYm) - new Date(b.dataYm);
			});

			const twrLc = {
				type: 'column',
				name: '期間報酬率',
				data: []
			};
			const daliyReturn = {
				type: 'line',
				name: '每月期間連續報酬率',
				data: []
			};

			self.wealthDailyChartXAxis = [];
			self.wealthAnalysisMonthly.forEach(function (dailyData) {
				// X軸 (月份)
				self.wealthDailyChartXAxis.push(dailyData.dataYm);
				// 每月期間連續報酬率
				daliyReturn.data.push(dailyData.dailyReturn);
				// 期間報酬率
				const wealthData = _.find(self.wealthAnalysis, {
					dataYm: dailyData.dataYm
				});
				twrLc.data.push(wealthData ? wealthData.twrLc : null);
			});

			self.wealthChartDailyData = [twrLc, daliyReturn];

			console.log('獲取圖表資料:', self.wealthChartDailyData);

			this.initChart();
		},
		initChart() {
			const self = this;
			// Create root element
			// https://www.amcharts.com/docs/v5/getting-started/#Root_element
			const { am5Obj } = self;
			// 處理props proxy data問題

			let firstLoad = false;
			// 透過 toRaw 解決因為 vue data proxy 導致跟 amcharts 衝突
			let { xAxis, yAxis, root, chart, legend } = toRaw(am5Obj); // 用toRaw把元件load出來
			if (!root) {
				firstLoad = true;

				// Create root element
				// https://www.amcharts.com/docs/v5/getting-started/#Root_element
				root = am5.Root.new(self.chartId); // 由商品頁塞入id
				root._logo.dispose();

				root.setThemes([am5themes_Animated.new(root)]);

				// Create chart
				// https://www.amcharts.com/docs/v5/charts/xy-chart/
				chart = root.container.children.push(
					am5xy.XYChart.new(root, {
						panX: false,
						panY: false,
						paddingLeft: 0,
						wheelX: 'panX',
						wheelY: 'zoomX',
						layout: root.verticalLayout
					})
				);

				chart.get('colors').set('colors', [
					am5.color(0x5090DC),
					am5.color(0x666666)
				]);

				// Create axes X軸
				// https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
				const xRenderer = am5xy.AxisRendererX.new(root, {
					cellStartLocation: 0.1,
					cellEndLocation: 0.9,
					minorGridEnabled: true
				});
				xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
					categoryField: 'category',
					renderer: xRenderer,
					tooltip: am5.Tooltip.new(root, {})
				}));
				xRenderer.grid.template.setAll({
					location: 1
				});
				xRenderer.labels.template.setAll({
					rotation: 0,
					centerY: am5.p50,
					fontSize: 12
				});
				// hide grid
				xRenderer.grid.template.set('visible', false);

				xAxis.data.setAll(self.wealthDailyChartXAxis.map(label => ({ category: label })));
				// Y軸
				yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
					renderer: am5xy.AxisRendererY.new(root, {
						strokeOpacity: 0.1
					})
				}));
			}
			else {
				chart.series.clear();
				legend.data.clear();
			}

			if (firstLoad) {
				const cursor = chart.set(
					'cursor',
					am5xy.XYCursor.new(root, {
						xAxis: xAxis
					})
				);
				cursor.lineY.set('visible', false);

				// Add legend 圖表最上面線的標示(含名稱)
				// https://www.amcharts.com/docs/v5/charts/xy-chart/legend-xy-series/
				legend = chart.children.push(
					am5.Legend.new(root, {
						centerX: am5.p50,
						x: am5.p50
					})
				);
				legend.markers.template.setAll({
					width: 6,
					height: 6
				});

				Object.assign(am5Obj, { xAxis, yAxis, root, chart, legend });
			}

			// Add series
			// https://www.amcharts.com/docs/v5/charts/xy-chart/series
			self.wealthChartDailyData.forEach((seriesData) => {
				if (seriesData.type == 'column') {
					const series = chart.series.push(
						am5xy.ColumnSeries.new(root, {
							name: seriesData.name,
							xAxis: xAxis,
							yAxis: yAxis,
							valueYField: 'value',
							categoryXField: 'category',
							tooltip: am5.Tooltip.new(root, {
								labelText: '{name}: {valueY}%'
							})
						})
					);

					// 設置柱子樣式
					series.columns.template.setAll({
						tooltipText: '{name}, {categoryX}:{valueY}',
						width: am5.percent(60),
						tooltipY: 0,
						strokeOpacity: 0
					});
					// 設置數據
					series.data.setAll(
						seriesData.data.map((value, index) => ({
							category: self.wealthDailyChartXAxis[index],
							value: Number(value) || 0
						}))
					);
					series.appear(1000);
				}
				else if (seriesData.type == 'line') {
					const series = chart.series.push(
						am5xy.LineSeries.new(root, {
							name: seriesData.name,
							xAxis: xAxis,
							yAxis: yAxis,
							valueYField: 'value',
							categoryXField: 'category',
							tooltip: am5.Tooltip.new(root, {
								pointerOrientation: 'horizontal',
								labelText: '{valueY}% '
							})
						})
					);
					series.strokes.template.setAll({
						strokeWidth: 1,
						templateField: 'strokeSettings'
					});
					// 設置數據
					series.data.setAll(
						seriesData.data.map((value, index) => ({
							category: self.wealthDailyChartXAxis[index],
							value: Number(value) || 0
						}))
					);
					series.appear(1000);
				}
			});

			legend.data.setAll(chart.series.values);

			// Make stuff animate on load
			// https://www.amcharts.com/docs/v5/concepts/animations/
			if (firstLoad) {
				chart.appear(1000, 100);
			}
		}
	}
};
</script>
