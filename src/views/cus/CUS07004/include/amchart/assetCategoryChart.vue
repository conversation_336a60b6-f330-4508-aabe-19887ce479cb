<template>
	<div class="text-center">
		<div :id="chartId" :ref="chartId" style="height: 500px;" />
	</div>
</template>
<script>
import * as am5 from '@amcharts/amcharts5';
import * as am5percent from '@amcharts/amcharts5/percent';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { toRaw } from 'vue';

export default {
	name: 'AssetCategoryChart',
	props: {
		chartData: Array,
		chartId: String
	},
	data() {
		return {
			am5Obj: {}
		};
	},
	mounted() {
		const self = this;
		self.initChart();
	},
	methods: {
		initChart() {
			const self = this;
			// Create root element
			// https://www.amcharts.com/docs/v5/getting-started/#Root_element
			const { am5Obj } = self;
			// 處理props proxy data問題
			// chartData = JSON.parse(JSON.stringify(chartData));
			let firstLoad = false;
			let { root, chart, legend } = toRaw(am5Obj); // 用toRaw把元件load出來
			if (!root) {
				firstLoad = true;

				// Create root element
				// https://www.amcharts.com/docs/v5/getting-started/#Root_element
				root = am5.Root.new(self.chartId); // 由商品頁塞入id
				root._logo.dispose();

				root.setThemes([am5themes_Animated.new(root)]);

				// Create chart
				// https://www.amcharts.com/docs/v5/charts/pie-chart/
				chart = root.container.children.push(
					am5percent.PieChart.new(root, {
						layout: root.verticalLayout,
						innerRadius: am5.percent(50)
					})
				);
			}
			else {
				chart.series.clear();
				legend.data.clear();
			}

			if (firstLoad) {
				// Add legend 圖表最上面線的標示(含名稱)
				// https://www.amcharts.com/docs/v5/charts/xy-chart/legend-xy-series/
				legend = chart.children.push(
					am5.Legend.new(root, {
						centerX: am5.p50,
						x: am5.p50
					})
				);

				Object.assign(am5Obj, { root, chart, legend });
			}

			// Add series
			// https://www.amcharts.com/docs/v5/charts/pie-chart/#Adding_series
			const series = chart.series.push(am5percent.PieSeries.new(root, {
				valueField: 'value',
				categoryField: 'name',
				alignLabels: false
			}));

			series.labels.template.setAll({
				textType: 'circular',
				inside: true,
				centerX: 0,
				centerY: 0,
				fontSize: '10px'
			});
			series.labels.template.set('forceHidden', true);
			series.data.setAll(self.chartData);

			legend.data.setAll(series.dataItems);

			// Make stuff animate on load
			// https://www.amcharts.com/docs/v5/concepts/animations/
			if (firstLoad) {
				chart.appear(1000, 100);
			}
		}
	}
};
</script>
