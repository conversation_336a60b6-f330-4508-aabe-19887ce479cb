<template>
	<div class="text-center">
		<div :id="chartId" :ref="chartId" style="height: 280px;" />
	</div>
</template>
<script>
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { toRaw } from 'vue';

export default {
	name: 'RealizeProfitLossChart',
	props: {
		chartData: Array,
		chartId: String
	},
	data() {
		return {
			am5Obj: {},
			localChartData: []
		};
	},
	mounted() {
		const self = this;
		self.localChartData = self.chartData.map(item => ({
			...item,
			colorindex: (item.value > 0) ? 1 : 0
		}));
		self.initChart();
	},
	methods: {
		initChart() {
			const self = this;
			// Create root element
			// https://www.amcharts.com/docs/v5/getting-started/#Root_element
			const { am5Obj } = self;
			// 處理props proxy data問題
			// chartData = JSON.parse(JSON.stringify(chartData));
			let firstLoad = false;
			// 透過 toRaw 解決因為 vue data proxy 導致跟 amcharts 衝突
			let { xAxis, yAxis, root, chart, legend } = toRaw(am5Obj); // 用toRaw把元件load出來
			if (!root) {
				firstLoad = true;

				// Create root element
				// https://www.amcharts.com/docs/v5/getting-started/#Root_element
				root = am5.Root.new(self.chartId); // 由商品頁塞入id
				root._logo.dispose();

				root.setThemes([am5themes_Animated.new(root)]);

				// Create chart
				// https://www.amcharts.com/docs/v5/charts/xy-chart/
				chart = root.container.children.push(
					am5xy.XYChart.new(root, {
						panX: true,
						panY: true,
						wheelX: 'panX',
						wheelY: 'zoomX',
						pinchZoomX: true,
						paddingLeft: 0,
						paddingRight: 1
					})
				);

				chart.get('colors').set('colors', [
					am5.color(0x459D55),
					am5.color(0xEF5452)
				]);

				// Create axes X軸
				// https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
				const xRenderer = am5xy.AxisRendererX.new(root, {
					minGridDistance: 5,
					minorGridEnabled: true
				});

				xRenderer.labels.template.setAll({
					centerY: am5.p50,
					centerX: am5.p50,
					minGridDistance: 1
				});

				xRenderer.grid.template.setAll({
					location: 1
				});

				xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
					categoryField: 'year',
					renderer: xRenderer,
					maxDeviation: 0.3,
					tooltip: am5.Tooltip.new(root, {})
				}));
				xAxis.data.setAll(self.localChartData);

				// Y軸
				yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
					renderer: am5xy.AxisRendererY.new(root, {
						strokeOpacity: 0.1
					}),
					maxDeviation: 0.3,
					max: 500000
				}));
			}
			else {
				chart.series.clear();
				legend.data.clear();
			}

			if (firstLoad) {
				// Add cursor
				const cursor = chart.set(
					'cursor',
					am5xy.XYCursor.new(root, {
						xAxis: xAxis
					})
				);
				cursor.lineY.set('visible', false);

				Object.assign(am5Obj, { xAxis, yAxis, root, chart, legend });
			}

			// Add series
			// https://www.amcharts.com/docs/v5/charts/xy-chart/series
			const series = chart.series.push(
				am5xy.ColumnSeries.new(root, {
					name: 'Series1',
					xAxis: xAxis,
					yAxis: yAxis,
					valueYField: 'value',
					categoryXField: 'year',
					tooltip: am5.Tooltip.new(root, {
						labelText: '{valueY}'
					})
				})
			);
			series.columns.template.setAll({
				strokeOpacity: 0,
				width: am5.percent(50)
			});
			series.columns.template.adapters.add('fill', function (fill, target) {
				return chart.get('colors').getIndex(target.dataItem.dataContext.colorindex);
			});

			series.bullets.push(function () {
				return am5.Bullet.new(root, {
					locationX: 0.5, // 可調整為 0.5 居中、0 靠左、1 靠右
					locationY: 1, // 基於 bar 的頂部
					sprite: am5.Label.new(root, {
						text: '{valueY}',
						fill: am5.color(0x000000),
						centerY: am5.p50,
						centerX: am5.p0,
						populateText: true,
						fontSize: 12,
						rotation: -90
					})
				});
			});
			series.data.setAll(self.localChartData);
			series.appear(1000);
			// Make stuff animate on load
			// https://www.amcharts.com/docs/v5/concepts/animations/
			if (firstLoad) {
				chart.appear(1000, 100);
			}
		}
	}
};
</script>
