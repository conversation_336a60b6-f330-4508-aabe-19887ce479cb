<template>
	<div ref="container" class="component-container">
		<div class="card card-table mb-3">
			<div class="card-header d-flex justify-content-between align-items-center">
				<h4 class="mb-0">
					{{ $t('cus.realizedProfitLoss') }}
				</h4>
				<div class="text-muted text-end mb-2">
					{{ $t('cus.dataDate') }}：{{ reportData.dataDate }}
				</div>
			</div>
			<div class="table-responsive">
				<realizedProfitLossChart
					v-if="reportData.realizedProfitLossList.length > 0"
					v-show="reportData.realizedProfitLossList.length > 0"
					ref="chart-realize-profit-loss"
					:chart-id="'chart-realize-profit-loss'"
					:chart-data="chartData"
				/>
				<table class="bih-table table table-striped table-hover">
					<thead>
						<tr>
							<th>{{ $t('pro.proType') }}</th>
							<th v-for="year in yearList" :key="year" class="text-end">
								{{ year }}
							</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="row in tableData" :key="row.product">
							<td>{{ row.product }}</td>
							<td v-for="year in yearList" :key="year" class="num">
								{{ $filters.formatNumber(row[year], 0) }}
							</td>
						</tr>
					</tbody>
					<tfoot>
						<tr class="tx-sum bg-total">
							<td>{{ $t('cus.total') }}</td>
							<td v-for="year in yearList" :key="year" class="num">
								{{ $filters.formatNumber(totRow[year], 0) }}
							</td>
						</tr>
					</tfoot>
				</table>
			</div>
		</div>
	</div>
</template>
<script>
import realizedProfitLossChart from './amchart/realizedProfitLossChart.vue';
export default {
	components: {
		realizedProfitLossChart
	},
	props: {
		cusCode: {
			type: String,
			default: ''
		},
		uuid: {
			type: String,
			required: false
		}
	},
	data() {
		return {
			reportData: {
				dataDate: '',
				realizedProfitLossList: []
			},
			yearList: [],
			totRow: {},
			chartData: []
		};
	},
	computed: {
		yearList() {
			const years = this.reportData.realizedProfitLossList.map(item => item.year);
			years.sort((a, b) => a.localeCompare(b));
			return [...years, '2025年(迄今)', '2024年(全年)', '2023年(全年)'];
		},
		tableData() {
			if (!this.reportData.realizedProfitLossList || this.reportData.realizedProfitLossList.length === 0) {
				return [];
			}
			const years = this.reportData.realizedProfitLossList.map(item => item.year);
			years.sort((a, b) => a.localeCompare(b));
			this.yearList = [...years, '2025年(迄今)', '2024年(全年)', '2023年(全年)'];
			const productKeys = Object.keys(this.reportData.realizedProfitLossList[0]).filter(key => key !== 'year');
			const yearGroup = {
				'2025年(迄今)': year => year.startsWith('2025'),
				'2024年(全年)': year => year.startsWith('2024'),
				'2023年(全年)': year => year.startsWith('2023')
			};

			const transposedData = productKeys.map((product) => {
				const row = { product };
				this.yearList.forEach((year) => {
					const entry = this.reportData.realizedProfitLossList.find(d => d.year === year);
					row[year] = entry ? entry[product] : 0;
				});

				// 新增年度總和欄位
				for (const label in yearGroup) {
					row[label] = this.yearList
						.filter(year => yearGroup[label](year))
						.reduce((sum, year) => sum + (row[year] || 0), 0);
				}
				return row;
			});

			const allYearCols = [...this.yearList, ...Object.keys(yearGroup)];
			for (const col of allYearCols) {
				this.totRow[col] = transposedData.reduce((sum, row) => sum + (row[col] || 0), 0);
			}

			// 計算圖表數據
			years.forEach((year) => {
				const entry = this.reportData.realizedProfitLossList.find(d => d.year === year);
				let sum = 0;
				productKeys.forEach((product) => {
					sum += entry[product] || 0;
				});
				this.chartData.push({
					year: year,
					value: sum
				});
			});
			return transposedData;
		}
	},
	mounted() {
		this.getPageData();
		this.$nextTick(() => {
			this.setupResizeObserver();
			this.reportHeight();
		});
	},
	updated() {
		this.$nextTick(() => {
			this.reportHeight();
		});
	},
	beforeUnmount() {
		if (this.resizeObserver) {
			this.resizeObserver.disconnect();
		}
	},
	methods: {
		async getPageData() {
			const res = await this.$api.getRealizedProfitLossApi({ cusCode: this.cusCode });
			console.log('getRealizedProfitLossApi', res);
			this.reportData.dataDate = res.data.dataDate;
			this.reportData.realizedProfitLossList = res.data.realizedProfitLossList;
		},

		// 設定 ResizeObserver 監控高度變化
		setupResizeObserver() {
			if (window.ResizeObserver) {
				this.resizeObserver = new ResizeObserver((entries) => {
					for (const entry of entries) {
						const height = entry.contentRect.height;
						this.reportHeight(height);
					}
				});

				this.resizeObserver.observe(this.$refs.container);
			}
		},

		// 回報高度給父元件
		reportHeight(customHeight = null) {
			const height = customHeight || this.$refs.container?.offsetHeight || 0;
			if (height > 0 && this.uuid) {
				this.$emit('height-changed', { uuid: this.uuid, height });
			}
		}
	}
};
</script>
