<template>
	<div class="report-edit-root d-flex flex-column" style="height: 100vh;">
		<!-- TODO: 頁面過於複雜，需要重新進行翻譯鍵的綁定 -->
		<!-- 編輯模式 -->
		<template v-if="!isPreviewMode">
			<!-- 上方工具列 -->
			<div class="report-toolbar d-flex align-items-center px-3 py-2 border-bottom bg-white">
				<button class="btn btn-outline-secondary me-2" @click="gotoHome">
					<i class="bi bi-arrow-left" /> {{ $t('cus.goBack') }}
				</button>

				<!-- 報告標題 -->
				<div class="flex-grow-1 mx-3">
					<input
						v-if="editingTitle"
						v-model="reportData.reportName"
						class="form-control form-control-sm"
						style="max-width: 300px;"
						@blur="editingTitle = false"
						@keyup.enter="editingTitle = false"
					>
					<h5
						v-else
						class="mb-0"
						style="cursor: pointer;"
						@click="editingTitle = true"
					>
						{{ reportData.reportName }}
					</h5>
				</div>

				<!-- 工具按鈕 -->
				<button class="btn btn-outline-primary me-2" @click="previewReport">
					{{ $t('cus.preview') }}
				</button>
				<button class="btn btn-primary me-2" @click="saveReport">
					<i class="bi bi-save ms-1" />
				</button>
				<button class="btn btn-outline-secondary" @click="showSettingModal = true">
					<i class="bi bi-gear" />
				</button>
			</div>

			<!-- 下方主區域 -->
			<div class="report-main flex-grow-1 d-flex overflow-hidden">
				<!-- 左側側邊欄 -->
				<SidebarPanel
					:pages="reportData.pages"
					:active-page-id="activePageId"
					@page-added="handlePageAdded"
					@page-selected="handlePageSelected"
					@page-deleted="handlePageDeleted"
					@page-reordered="handlePageReordered"
				/>

				<!-- 右側編輯區 -->
				<div class="edit-area flex-grow-1 d-flex flex-column align-items-center" style="background: #f5f5f5; overflow: auto;">
					<!-- 編輯區容器 -->
					<div class="edit-container py-4">
						<PageEditor
							v-if="activePage"
							:page="activePage"
							:page-index="activePageIndex"
							@component-added="handleNewComponentAdded"
							@page-updated="handlePageUpdated"
							@page-overflow="handlePageOverflow"
							@super-tall-component-added="handleSuperTallComponentAdded"
						/>
						<div v-else class="no-page-selected">
							<div class="text-center text-muted p-5">
								<i class="bi bi-journal-plus" style="font-size: 3rem;" />
								<p class="mt-3">
									請從側邊欄選擇頁面或新增頁面
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 設定 Modal -->
			<div v-if="showSettingModal" class="setting-modal-float">
				<div class="card shadow" style="min-width:320px;">
					<div class="card-header d-flex justify-content-between align-items-center">
						<span>{{ $t('cus.settings') }}</span>
						<button class="btn btn-close" @click="showSettingModal = false" />
					</div>
					<div class="card-body">
						<!-- 設定內容參考 createAssetReport.vue -->
						<div class="mb-3">
							<label class="form-label">{{ $t('cus.customerCode') }}</label>
							<select v-model="reportData.settings.cusCode" name="cusCode" class="form-select">
								<option value="">
									--
								</option>
								<option value="A111111111">
									A111111111
								</option>
								<option value="A111111112">
									A111111112
								</option>
							</select>
							<label class="form-label mt-3">{{ $t('cus.dateRange') }}</label>
							<div class="input-group">
								<input
									v-model="reportData.settings.beginDate"
									type="date"
									name="beginDate"
									class="form-control"
								>
								<input
									v-model="reportData.settings.endDate"
									type="date"
									name="endDate"
									class="form-control"
								>
							</div>
						</div>
						<div class="text-end">
							<button class="btn btn-secondary me-2" @click="showSettingModal = false">
								{{ $t('cus.cancel') }}
							</button>
							<button class="btn btn-primary" @click="saveSettings">
								{{ $t('cus.save') }}
							</button>
						</div>
					</div>
				</div>
			</div>
		</template>

		<!-- 預覽模式 -->
		<template v-else>
			<!-- 預覽工具列 -->
			<div class="preview-toolbar d-flex align-items-center px-3 py-2 border-bottom bg-white">
				<button class="btn btn-outline-secondary me-2" @click="exitPreviewMode">
					<i class="bi bi-arrow-left" /> 返回編輯
				</button>

				<!-- 報告標題 -->
				<div class="flex-grow-1 mx-3">
					<h5 class="mb-0">
						{{ reportData.reportName }}
					</h5>
				</div>

				<!-- 頁面指示器 -->
				<div class="page-indicator me-3">
					<span class="badge bg-secondary">{{ currentPreviewPageIndex + 1 }} / {{ reportData.pages.length }}</span>
				</div>

				<!-- 導航按鈕 -->
				<button
					class="btn btn-outline-primary me-2"
					:disabled="currentPreviewPageIndex <= 0"
					@click="prevPreviewPage"
				>
					<i class="bi bi-chevron-left" /> 上一頁
				</button>
				<button
					class="btn btn-outline-primary me-2"
					:disabled="currentPreviewPageIndex >= reportData.pages.length - 1"
					@click="nextPreviewPage"
				>
					下一頁 <i class="bi bi-chevron-right" />
				</button>

				<!-- 匯出按鈕 -->
				<button class="btn btn-success" @click="exportToPdf">
					<i class="bi bi-file-pdf" /> 匯出 PDF
				</button>
			</div>

			<!-- 預覽內容區 -->
			<div class="preview-content flex-grow-1 d-flex flex-column align-items-center bg-light overflow-auto">
				<div class="preview-page-container py-4">
					<PageEditor
						v-if="currentPreviewPage"
						:page="currentPreviewPage"
						:page-index="currentPreviewPageIndex"
						:preview-mode="true"
					/>
					<div v-else class="alert alert-warning">
						報告中沒有頁面
					</div>
				</div>
			</div>

			<!-- 底部頁面導航 -->
			<div class="preview-footer bg-white border-top py-2 px-3">
				<div class="page-thumbnails d-flex overflow-auto">
					<div
						v-for="(page, index) in reportData.pages"
						:key="page.id"
						class="page-thumbnail mx-1"
						:class="{ active: index === currentPreviewPageIndex }"
						@click="goToPreviewPage(index)"
					>
						<div class="thumbnail-inner">
							<small>{{ getPageTypeName(page.type) }} {{ index + 1 }}</small>
						</div>
					</div>
				</div>
			</div>
		</template>
	</div>
</template>

<script>
import SidebarPanel from './include/SidebarPanel.vue';
import PageEditor from './include/PageEditor.vue';

export default {
	name: 'CreateAssetReport',
	components: {
		SidebarPanel,
		PageEditor
	},
	data() {
		return {
			editingTitle: false,
			showSettingModal: false,
			// 報告書資料
			reportData: {
				reportId: '',
				reportName: '',
				pages: [],
				settings: {
					cusCode: '',
					beginDate: '',
					endDate: ''
				}
			},
			activePageId: null,

			// 預覽相關
			isPreviewMode: false,
			currentPreviewPageIndex: 0
		};
	},
	computed: {
		// 當前活動頁面
		activePage() {
			return this.reportData.pages.find(p => p.id === this.activePageId);
		},
		// 當前活動頁面索引
		activePageIndex() {
			return this.reportData.pages.findIndex(p => p.id === this.activePageId);
		},
		// 當前預覽頁面
		currentPreviewPage() {
			return this.reportData.pages[this.currentPreviewPageIndex] || null;
		}
	},
	created() {
		// 初始化報告資料
		this.initReportData();
	},
	methods: {
		async initReportData() {
			try {
				const reportId = this.$route.params.id;

				if (reportId) {
					const report = await this.$api.getAssetReportDetail({ reportId: reportId });
					console.log('載入報告資料:', report);
					this.reportData = report.data;
				}

				// 確保至少有一頁
				if (!this.reportData.pages || this.reportData.pages.length === 0) {
					this.addNewPage('cover');
					this.addNewPage('content');
					this.addNewPage('backcover');
				}

				// 設置活動頁面
				this.activePageId = this.reportData.pages[0]?.id;
			}
			catch (error) {
				console.error('載入報告失敗:', error);
				// 建立新報告
				this.addNewPage('cover');
			}
		},

		saveSettings() {
			this.showSettingModal = false;
			// 參數帶入元件
			this.syncSettingsToComponents();
			// 強制通知頁面更新
			this.reportData.pages.forEach((page) => {
				this.handlePageUpdated({ ...page });
			});
		},
		syncSettingsToComponents() {
			// 將設定參數帶入所有已定義元件
			this.reportData.pages.forEach((page) => {
				if (page.components) {
					page.components.forEach((comp) => {
						comp.content = {
							...comp.content,
							cusCode: this.reportData.settings.cusCode,
							beginDate: this.reportData.settings.beginDate,
							endDate: this.reportData.settings.endDate
						};
					});
				}
			});
		},

		handleNewComponentAdded(component) {
			// 自動為新元件同步全域設定
			if (this.reportData.settings.cusCode) {
				component.content = {
					...component.content,
					cusCode: this.reportData.settings.cusCode,
					beginDate: this.reportData.settings.beginDate,
					endDate: this.reportData.settings.endDate
				};
			}
		},

		// hanldeSuperTallComponentAdded
		handleSuperTallComponentAdded({ component, pageIndex, requiredPages }) {
			console.log('新增超高元件:', component, '在頁面索引:', pageIndex, '需要的頁面數:', requiredPages);

			// 標記為超高元件
			component.isSuperTall = true;

			// 創建超高元件頁面
			const superTallPage = this.addNewPage('content', {
				atIndex: pageIndex + 1,
				components: [component]
			});

			// 標記為超高元件頁面
			superTallPage.isSuperTallPage = true;
			superTallPage.superTallComponentId = component.componentId;
			superTallPage.title = `超高元件頁面`;

			// 從第二頁開始新增預留頁
			for (let i = 2; i <= requiredPages; i++) {
				const reservedPage = this.addNewPage('content', {
					atIndex: pageIndex + i,
					components: []
				});

				reservedPage.reservedForSuperTall = true;
				reservedPage.superTallComponentId = component.componentId;
				reservedPage.title = `超高元件預留頁 ${i - 1}`;

				console.log(`新增預留頁 ${i - 1}：`, reservedPage);
			}

			// 設置活動頁面為超高元件頁面
			this.activePageId = superTallPage.id;
		},

		// 處理頁面溢出
		handlePageOverflow({ pageIndex, overflowComponents }) {
			console.log('處理頁面溢出:', pageIndex, overflowComponents);

			// 創建新頁面
			const newPage = this.addNewPage('content', {
				atIndex: pageIndex + 1,
				components: overflowComponents
			});

			// 遞迴檢查新頁面是否也會溢出
			this.$nextTick(() => {
				this.recursiveCheckOverflow(newPage.id);
			});
		},

		// 遞迴檢查頁面溢出
		async recursiveCheckOverflow(pageId) {
			const page = this.reportData.pages.find(p => p.id === pageId);

			// 增加對超高元件頁面的特殊處理，跳過正常的遞迴檢查
			if (page.isSuperTallPage || page.reservedForSuperTall) {
				console.warn('頁面為超高元件預留頁或包含超高元件，跳過溢出檢查');
				return;
			}

			if (!page || !page.components || page.components.length === 0) return;

			const pageContentHeight = 1023;
			let totalHeight = 0;
			let overflowIndex = -1;

			// 計算頁面總高度
			for (let i = 0; i < page.components.length; i++) {
				const component = page.components[i];
				// 給予預估高度，實際應該根據元件類型調整
				const componentHeight = component.elementId === 'text' ? 74 : 200;
				totalHeight += componentHeight;

				if (totalHeight > pageContentHeight) {
					overflowIndex = i;
					break;
				}
			}

			// 如果還是溢出，繼續分割
			if (overflowIndex >= 0) {
				const overflowComponents = page.components.splice(overflowIndex);
				const pageIndex = this.reportData.pages.findIndex(p => p.id === pageId);

				const newPage = this.addNewPage('content', {
					atIndex: pageIndex + 1,
					components: overflowComponents
				});

				// 繼續遞迴檢查
				this.$nextTick(() => {
					this.recursiveCheckOverflow(newPage.id);
				});
			}
		},

		// 頁面管理
		addNewPage(type = 'content', { atIndex = null, components = [] } = {}) {
			const pageTypes = {
				cover: { title: '封面', content: { title: '資產報告書', subtitle: '' } },
				content: { title: '內容頁', components: [] },
				transition: { title: '章節頁', content: { title: '章節標題' } },
				backcover: { title: '封底', content: { footer: '感謝您的閱讀' } }
			};

			const template = pageTypes[type];
			const newPage = {
				id: `page-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
				type,
				title: `${template.title} ${this.reportData.pages.filter(p => p.type === type).length + 1}`,
				...(template.content ? { content: template.content } : {}),
				components: components.length ? components : (template.components || [])
			};

			if (atIndex === null || atIndex >= this.reportData.pages.length) {
				// this situation happens when adding a new page from sidebar panel
				// or when overflow occurs at the last page
				console.log('新增頁面到最後, atIndex:', atIndex);
				this.reportData.pages.push(newPage);
			}
			else {
				console.log(`新增頁面到索引 ${atIndex}`);
				this.reportData.pages.splice(atIndex, 0, newPage);
			}
			this.activePageId = newPage.id;

			return newPage;
		},

		handlePageAdded(type) {
			return this.addNewPage(type);
		},

		handlePageSelected(pageId) {
			this.activePageId = pageId;
		},

		handlePageDeleted(pageId) {
			const index = this.reportData.pages.findIndex(p => p.id === pageId);
			if (index === -1) return;

			const pageToDelete = this.reportData.pages[index];

			// 如果刪除的是超高元件預留頁，不允許直接刪除
			if (pageToDelete.reservedForSuperTall) {
				console.warn('無法直接刪除超高元件預留頁，請先刪除相關超高元件');
				return;
			}

			// 如果刪除的是包含超高元件的頁面，也要刪除其預留頁
			if (pageToDelete.isSuperTallPage && pageToDelete.superTallComponentId) {
				const superTallComponentId = pageToDelete.superTallComponentId;

				// 找出所有相關的預留頁並刪除
				const pagesToDelete = this.reportData.pages.filter(p =>
					p.reservedForSuperTall && p.superTallComponentId === superTallComponentId
				);

				console.log(`刪除超高元件及其 ${pagesToDelete.length} 個預留頁`);

				// 從後往前刪除，避免索引變化
				pagesToDelete.forEach((reservedPage) => {
					const reservedIndex = this.reportData.pages.findIndex(p => p.id === reservedPage.id);
					if (reservedIndex >= 0) {
						this.reportData.pages.splice(reservedIndex, 1);
					}
				});
			}

			// 若刪除當前頁，則選擇另一頁
			if (pageId === this.activePageId) {
				const newIndex = Math.min(index, this.reportData.pages.length - 2);
				this.activePageId = newIndex >= 0 ? this.reportData.pages[newIndex].id : null;
			}

			// 刪除頁面
			// this.reportData.pages.splice(index, 1);
			const finalIndex = this.reportData.pages.findIndex(p => p.id === pageId);
			if (finalIndex >= 0) {
				this.reportData.pages.splice(finalIndex, 1);
			}
		},
		// 處理頁面重排序
		handlePageReordered({ fromIndex, toIndex }) {
			const [movedPage] = this.reportData.pages.splice(fromIndex, 1);

			this.reportData.pages.splice(toIndex, 0, movedPage);
			console.log('頁面已重新排序:', this.reportData.pages);
		},

		handlePageUpdated(page) {
			// debugger;
			const index = this.reportData.pages.findIndex(p => p.id === page.id);
			if (index >= 0) {
				this.reportData.pages[index] = page;
			}
		},

		// 報告操作
		previewReport() {
			this.isPreviewMode = true;
			this.currentPreviewPageIndex = 0;
		},

		// 預覽模式相關方法
		enterPreviewMode() {
			this.isPreviewMode = true;
			this.currentPreviewPageIndex = 0;
		},

		exitPreviewMode() {
			this.isPreviewMode = false;
		},

		prevPreviewPage() {
			if (this.currentPreviewPageIndex > 0) {
				this.currentPreviewPageIndex--;
			}
		},

		nextPreviewPage() {
			if (this.currentPreviewPageIndex < this.reportData.pages.length - 1) {
				this.currentPreviewPageIndex++;
			}
		},

		goToPreviewPage(index) {
			if (index >= 0 && index < this.reportData.pages.length) {
				this.currentPreviewPageIndex = index;
			}
		},

		// 新增：缺少的方法
		getPageTypeName(type) {
			const typeNames = {
				cover: '封面',
				content: '內容',
				transition: '章節',
				backcover: '封底'
			};
			return typeNames[type] || '頁面';
		},

		async saveReport() {
			console.log('儲存報告:', this.reportData);
			try {
				// 保存報告
				debugger;
				const result = await this.$api.saveAssetReport(this.reportData);
				if (result.success) {
					this.$notify({
						type: 'success',
						title: '儲存成功',
						text: '報告已成功儲存'
					});
				}
			}
			catch (error) {
				console.error('保存失敗:', error);
				this.$notify({
					type: 'error',
					title: '儲存失敗',
					text: error.message || '請稍後再試'
				});
			}
		},

		// 匯出PDF todo
		async exportToPdf() {
			try {
				console.log('匯出PDF:', this.reportData);
				this.$notify({
					type: 'success',
					title: '匯出成功',
					text: 'PDF匯出功能正在開發中'
				});
			}
			catch (error) {
				this.$notify({
					type: 'error',
					title: '匯出失敗',
					text: error.message || '匯出PDF時發生錯誤'
				});
			}
		},

		gotoHome() {
			this.$router.push({ name: 'assetReportHome' });
		}
	}
};

</script>

<style scoped>
.report-edit-root {
  background: #f7f8fa;
}

.report-toolbar {
  min-height: 56px;
  z-index: 2;
}

.edit-area {
  min-width: 0;
}

.edit-container {
  max-width: 1000px;
  width: 100%;
}

.setting-modal-float {
  position: absolute;
  top: 60px;
  right: 40px;
  z-index: 2000;
  background: transparent;
}

/* 新增：預覽模式樣式 */
.preview-content {
  min-width: 0;
}

.preview-page-container {
  max-width: 1000px;
  width: 100%;
}

.page-thumbnails {
  height: 70px;
}

.page-thumbnail {
  width: 50px;
  height: 70px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  justify-content: center;
  align-items: center;
}

.page-thumbnail.active {
  border-color: #007bff;
  background: #e6f2ff;
}

.thumbnail-inner {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  text-align: center;
  padding: 2px;
}
</style>
