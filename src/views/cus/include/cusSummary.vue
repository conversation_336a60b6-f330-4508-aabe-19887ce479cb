<template>
	<div v-show="showSummary" class="app-filemgr">
		<div class="filemgr-wrapper filemgr-wrapper-two" :class="{ 'filemgr-sidebar-close': isHideMenu }">
			<span id="filemgrMenuclose" class="cursor-pointer" :class="{ close: isHideMenu }">
				<i class="bi bi-arrow-left-square-fill"	@click="hideMenu()" />
			</span>
			<vue-cus-summary-info-sidebar
				v-if="customer"
				:cus-code="cusCode"
				:page-code="pageCode"
				:has-auth="hasAuth"
				:customer="customer"
				:set-menu-code="setMenuCode"
			/>
			<div v-show="true" class="filemgr-content">
				<div style="position: absolute; top: 50px" class="container-fluid">
					<vue-cus-client-overview
						v-if="customer && menuCode == 'M20-051'"
						:cus-code="cusCode"
						:has-auth="hasAuth"
						:customer="customer"
						:set-menu-code="setMenuCode"
					/>
					<vue-cus-assets-detail
						v-if="menuCode == 'M20-055'"
						ref="cusAssetsDetail"
						:cus-code="cusCode"
						:has-auth="hasAuth"
						:pfcat-code="pfcatCode"
					/>
					<!-- <vue-cus-service-rec v-if="menuCode == 'M20-056'" ref="cusServiceRec" :cus-code="cusCode"
            :has-auth="hasAuth"></vue-cus-service-rec> -->
					<vue-cus-invest-analysis-base
						v-if="menuCode == 'M20-058'"
						ref="cusInvestAnalysis"
						:cus-code="cusCode"
						:has-auth="hasAuth"
						:customer="customer"
					/>
					<vue-cus-info-page
						v-if="menuCode == 'M20-052'"
						ref="cusInfo"
						:cus-code="cusCode"
						:has-auth="hasAuth"
						:customer="customer"
						:set-cus-code="setCusCode"
					/>
					<!-- <vue-cus-client-price-alert v-if="menuCode == 'M20-053'" ref="cusClientPriceAlert" :cus-code="cusCode"
            :has-auth="hasAuth" :customer="customer" :base-cur-code="baseCurCode"
            :base-cur-scale="baseCurScale"></vue-cus-client-price-alert>
          <vue-cus-client-service-rec-base v-if="menuCode == 'M20-027'" ref="cusClientServiceRec" :cus-code="cusCode"
            :has-auth="hasAuth" :customer="customer"></vue-cus-client-service-rec-base> -->
					<vue-cus-bank-statement-base
						v-if="menuCode == 'M20-057'"
						ref="cusBankStatementBase"
						:cus-code="cusCode"
						:has-auth="hasAuth"
						:customer="customer"
					/>
				</div>
				<div style="position: absolute; left: 20px; top: 10px">
					<Button
						:label="$t('cus.goBackToPreviousPage')"
						color="primary"
						size="lg"
						@click="comeBack()"
					/>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import vueCusClientOverview from '../CUS0212/include/clientOverview.vue';
import vueCusSummaryInfoSidebar from './cusSummaryInfoSidebar.vue';
import vueCusInfoPage from '../CUS0211/include/cusInfo.vue';
import vueCusInvestAnalysisBase from '../CUS0214/include/investAnalysis.vue';
import vueCusBankStatementBase from '../CUS0219/include/bankStatement.vue';
import vueCusAssetsDetail from '../CUS0213/include/assetsDetail.vue';
import _ from 'lodash';
export default {
	components: {
		vueCusAssetsDetail,
		vueCusSummaryInfoSidebar,
		vueCusClientOverview,
		vueCusInfoPage,
		vueCusInvestAnalysisBase,
		vueCusBankStatementBase
	},
	props: {
		setIsShowSummary: Function,
		setShowSearchResult: Function
	},
	data: function () {
		return {
			pageCode: 1,
			showSummary: false,
			showSearchResult: false,
			menuCode: 'M20-051',
			cusCode: null,
			customer: null,
			hasAuth: false,
			pbStatusName: null,
			pfcatCode: null,
			isHideMenu: false
		};
	},
	computed: {
		writeAccessLogCondition: function () {
			const { menuCode, cusCode } = this;
			if (menuCode && cusCode) {
				return { menuCode, cusCode };
			}
			return null;
		}
	},
	watch: {
		writeAccessLogCondition: async function (newVal) {
			if (!newVal) {
				return;
			}
			await this.$api.postLoggingApi({
				menuCode: newVal.menuCode,
				cusCode: newVal.cusCode
			});
		}
	},
	mounted: function () {
	},
	methods: {
		setCusCode: function (cusCode) {
			const self = this;
			self.cusCode = cusCode;
			self.showSummary = true;
			self.initPage(cusCode);
		},
		initPage: function (cusCode) {
			const self = this;
			self.cusCode = cusCode;
			self.checkAuthAndGetCustomer(cusCode);
			self.showSummary = true;
			self.menuCode = 'M20-051';
		},
		checkAuthAndGetCustomer: async function (cusCode) {
			const self = this;
			const retAuth = await self.$api.checkCusAuthApi({
				cusCode: cusCode
			});
			if (!_.isNil(retAuth.data) && retAuth.data.authYn != 'N') {
				const ret = await self.$api.getCusInfoApi({
					cusCode: cusCode
				});
				if (!ret.data || ret.data.length == 0) {
					self.$bi.confirm(self.$t('cus.customerDoesNotExist'), {
						event: {
							confirmOk: function () {
								location.href = '/';
							},
							confirmCancel: function () {
								location.href = '/';
							}
						},
						button: {
							confirmOk: self.$t('cus.confirmOk')
						}
					});
				}
				else {
					if (retAuth.data.authYn == 'Y') {
						self.hasAuth = true;
					}
					else if (retAuth.data.authYn == 'E') {
						self.hasAuth = false;
					}
					self.customer = ret.data;
					self.customer.pbStatusName = retAuth.data.pbStatusName;
					self.customer.pbStatus = retAuth.data.authYn;
				}
			}
			else {
				self.hasAuth = false;
				self.$bi.confirm(self.$t('cus.customerNotAuthorizedToView'), {
					event: {
						confirmOk: function () {
							self.comeBack();
						},
						confirmCancel: function () {
							self.comeBack();
						}
					},
					button: {
						confirmOk: self.$t('cus.confirmOk')
					}
				});
			}
		},
		comeBack: function () {
			const self = this;
			self.setIsShowSummary(false);
			self.showSummary = false;
			self.setShowSearchResult?.(true);
			// 為了清除舊有cus資料，運用v-if讓舊資料頁面消失
			self.menuCode = 'M20-051';
		},
		setMenuCode: function (menuCode, pfcatCode) {
			const self = this;
			self.menuCode = menuCode;
			self.pfcatCode = pfcatCode;
		},
		hideMenu: function () {
			const self = this;
			self.isHideMenu = !self.isHideMenu;
		}
	}
};
</script>
