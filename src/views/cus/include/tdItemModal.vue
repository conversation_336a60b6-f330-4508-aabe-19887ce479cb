<template>
	<div v-bind="$attrs" class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
		<div class="modal-content">
			<div class="modal-header">
				<h4 id="itemModal5" class="modal-title">
					{{ $t('cus.todoItemHandling') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click.prevent="close()"
				/>
			</div>
			<div class="modal-body overflow-y-auto">
				<div class="tab-nav-pills">
					<vue-form v-slot="{ errors }" ref="tdItemModal">
						<ul id="portfolioTabs" class="nav nav-pills justify-content-center">
							<li class="nav-item" @click.prevent="changeTab(1)">
								<a class="nav-link" :class="{ active: tabCode == 1 }" href="#">{{ $t('cus.processingAndNotes') }}</a>
							</li>
							<li class="nav-item" @click.prevent="changeTab(2)">
								<a
									class="nav-link"
									:class="{ active: tabCode == 2 }"
									href="#"
									@click="getTdListLogs()"
								>{{ $t('cus.historyRecord') }}</a>
							</li>
						</ul>
						<div class="tab-content">
							<div v-show="tabCode == 1" id="Section1">
								<div
									v-show="isUpdateSuccess"
									id="msg1"
									class="alert alert-success"
									role="alert"
								>
									<span class="ico-ok" /> {{ $t('cus.saveSuccessful') }}
								</div>
								<div class="alert alert-warning" role="alert">
									<span class="ico-alert" /> [{{ cusName }}]{{ itemName }}:{{ content }}
								</div>
								<div class="card card-prointro shadow-none mb-3">
									<table class="table">
										<tbody>
											<tr>
												<td width="10%" class="clientCard-icon">
													<div class="avatar avatar-male" />
													<h5 class="mb-0">
														{{ cusName || '--' }}
													</h5>
												</td>
												<td width="90%">
													<div class="caption tx-black">
														{{ $t('cus.recentContactDate') }} ：{{ $filters.formatDate(lastConnectionDt) || '--' }}
													</div>
													<div class="row">
														<div class="col-lg-4">
															<ul class="list-unstyled profile-info-list">
																<li><i class="bi bi-clipboard-data" />{{ $t('cus.investmentProfile') }}：{{ rankName || '--' }}</li>
																<li><i class="bi bi-gift" />{{ $t('cus.birthday') }}：{{ birth || '--' }}</li>
																<li>
																	<i class="bi bi-envelope" />
																	<span v-if="emailList.length == 1">{{ $t('cus.email') }}：{{ emailList[0] || '--' }}</span>
																	<span v-else-if="emailList.length > 1">{{ $t('cus.email') }}：{{ emailList.join('/') }}</span>
																	<span v-else>{{ $t('cus.email') }}：--</span>
																</li>
															</ul>
														</div>
														<div class="col-lg-3">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-house-door mg-xl-e-5-f" />
																	<span v-if="phoneHList.length == 1">{{ $t('cus.contactPhoneHome') }}：{{ phoneHList[0] || '--' }}</span>
																	<span v-else-if="phoneHList.length > 1">{{ $t('cus.contactPhoneHome') }}：{{ phoneHList.join('/') }}</span>
																	<span v-else>{{ $t('cus.contactPhoneHome') }}：--</span>
																</li>
																<li>
																	<i class="bi bi-building mg-xl-e-5-f" />
																	<span v-if="phoneOList.length == 1">{{ $t('cus.contactPhoneOffice') }}：{{ phoneOList[0] || '--' }}</span>
																	<span v-else-if="phoneOList.length > 1">{{ $t('cus.contactPhoneOffice') }}：{{ phoneOList.join('/') }}</span>
																	<span v-else>{{ $t('cus.contactPhoneOffice') }}：--</span>
																</li>
																<li>
																	<i class="bi bi-telephone mg-xl-e-5-f" />
																	<span v-if="phoneMList.length == 1">{{ $t('cus.contactPhoneMobile') }}：{{ phoneMList[0] || '--' }}</span>
																	<span v-else-if="phoneMList.length > 1">{{ $t('cus.contactPhoneMobile') }}：{{ phoneMList.join('/') }}</span>
																	<span v-else>{{ $t('cus.contactPhoneMobile') }}：--</span>
																</li>
															</ul>
														</div>
														<div class="col-lg-5">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-journal-medical mg-xl-e-5-f" />{{ $t('cus.refuseMarketing') }}：
																	<span :class="mktPhoneYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ mktPhoneYn === 'Y' ? $t('cus.yes') : $t('cus.no') }}
																	</span>
																</li>
																<li>
																	<i class="bi bi-journal-text mg-xl-e-5-f" />{{ $t('cus.specificTrustRecommendation') }}：
																	<span :class="specRecommYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ specRecommYn === 'Y' ? $t('cus.yes') : $t('cus.no') }}
																	</span>
																</li>
																<li>
																	<i class="bi bi-journal-bookmark mg-xl-e-5-f" />{{ $t('cus.wealthSpecificCustomer') }}：
																	<span :class="specCusYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ specCusYn === 'Y' ? $t('cus.yes') : $t('cus.no') }}
																	</span>
																</li>
															</ul>
														</div>
													</div>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
								<div class="card card-form shadow-none mt-3">
									<div class="card-header">
										<h4>{{ $t('cus.eventMaintenance') }}</h4>
										<span class="tx-square-bracket">{{ $t('cus.requiredFieldNote') }}</span>
									</div>
									<div class="card-body">
										<div class="row g-3 align-items-end">
											<div class="col-lg-4">
												<label class="form-label tx-require">{{ $t('cus.processingStatus') }}</label>
												<vue-field
													id="statusCode"
													v-model="statusCode"
													name="statusCode"
													class="form-select"
													rules="required"
													:label="$t('cus.processingStatus')"
													:disabled="logsVerifyStatusCode == 'P' || doneYn == 'Y'"
													as="select"
													:class="{ 'is-invalid': errors.statusCode }"
												>
													<option value="" disabled>
														{{ $t('cus.pleaseSelect') }}
													</option>
													<option v-for="tdStatus in tdStatusMenu" :value="tdStatus.statusCode">
														{{ tdStatus.statusName }}
													</option>
												</vue-field>
												<div class="col-10" style="height: 3px">
													<span v-show="errors.statusCode" class="text-danger">{{ errors.statusCode }}</span>
												</div>
											</div>
											<div class="col-lg-4">
												<label class="form-label">{{ $t('cus.processingMethod') }}</label>
												<vue-field
													id="actionCode"
													v-model="actionCode"
													name="actionCode tx-require"
													class="form-select"
													:disabled="logsVerifyStatusCode == 'P' || doneYn == 'Y'"
													rules="required"
													:label="$t('cus.processingMethod')"
													as="select"
													:class="{ 'is-invalid': errors.actionCode }"
												>
													<option value="" disabled>
														{{ $t('cus.pleaseSelect') }}
													</option>
													<option v-for="tdAction in tdActionMenu" :value="tdAction.actionCode">
														{{ tdAction.actionName }}
													</option>
												</vue-field>
												<div class="col-10" style="height: 3px">
													<span v-show="errors.actionCode" class="text-danger">{{ errors.actionCode }}</span>
												</div>
											</div>
											<div class="col-lg-4">
												<label class="form-label">{{ $t('cus.supervisorAccompany') }}</label>
												<div class="form-check form-check-inline">
													<input
														id="chief_1"
														v-model="chiefVisitYn"
														name="chief_visit_yn"
														type="radio"
														value="Y"
														:disabled="logsVerifyStatusCode == 'P' || doneYn == 'Y'"
													>
													<label for="chief_1" :disabled="doneYn == 'Y'">{{ $t('cus.yes') }}</label>
												</div>
												<div class="form-check form-check-inline">
													<input
														id="chief_2"
														v-model="chiefVisitYn"
														name="chief_visit_yn"
														type="radio"
														value="N"
														:disabled="logsVerifyStatusCode == 'P' || doneYn == 'Y'"
													>
													<label for="chief_2" :disabled="doneYn == 'Y'">{{ $t('cus.no') }}</label>
												</div>
											</div>
											<div class="col-lg-12">
												<label class="form-label tx-require">{{ $t('cus.processingContent') }}</label>
												<vue-field
													id="memo"
													v-model="memo"
													name="memo"
													class="form-control"
													rows="3"
													cols="65"
													maxlength="200"
													rules="required"
													:label="$t('cus.processingContent')"
													:disabled="logsVerifyStatusCode == 'P' || doneYn == 'Y'"
													as="textarea"
													:class="{ 'is-invalid': errors.memo }"
												/>
												<div class="col-10" style="height: 3px">
													<span v-show="errors.memo" class="text-danger">{{ errors.memo }}</span>
												</div>
												<div style="height: 25px">
													<span v-if="forbiddenContentMsg" class="text-danger">{{ forbiddenContentMsg }}</span>
												</div>
												<div class="tx-note">
													{{ $t('cus.charactersRemaining').replace('{count}', 200 - memo.length) }}
												</div>
											</div>
											<div class="col-lg-6">
												<div class="input-group">
													<span class="input-group-text"> {{ $t('cus.commonPhrases') }}</span>
													<select
														id="reuseWord"
														v-model="reuseWord"
														name="reuseWord"
														class="form-select"
														:disabled="doneYn == 'Y'"
													>
														<template v-for="selectWords in wobReuseWords">
															<option v-if="selectWords.words" :value="selectWords.words">
																{{ selectWords.words }}
															</option>
														</template>
													</select>
													<button
														id="setContent"
														type="button"
														class="btn btn-info btn-glow"
														:disabled="doneYn == 'Y'"
														@click="appendReuseWord()"
													>
														{{ $t('cus.addToContent') }}
													</button>
												</div>
											</div>
											<div class="col-lg-6">
												<div class="input-group">
													<span class="input-group-text">{{ $t('cus.commonPhraseSetting') }}</span>
													<input
														id="words"
														v-model="newReuseWord"
														class="form-control"
														type="text"
														size="20"
														maxlength="20"
														:disabled="doneYn === 'Y'"
													>
													<button
														id="wordAdd"
														type="button"
														class="btn btn-info btn-glow"
														:disabled="doneYn === 'Y'"
														@click="insertReuseWord()"
													>
														{{ $t('cus.add') }}
													</button>
													<button
														id="wordSetting"
														type="button"
														class="btn btn-info btn-glow"
														:disabled="doneYn === 'Y'"
														@click.prevent="isOpenReuseWordModal = true"
													>
														{{ $t('cus.settings') }}
													</button>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div v-show="tabCode == 2" id="Section2">
								<div class="alert alert-warning" role="alert">
									<span class="ico-alert" /> [{{ cusName }}]{{ itemName }}:{{ content }}
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-bordered">
										<thead>
											<tr>
												<th width="11%">
													{{ $t('cus.processingDate') }}
												</th>
												<th width="17%">
													{{ $t('cus.processingStatus') }}
												</th>
												<th width="14%">
													{{ $t('cus.processor') }}
												</th>
												<th width="13%">
													{{ $t('cus.processingMethod') }}
												</th>
												<th width="20%">
													{{ $t('cus.processingContent') }}
												</th>
												<th width="14%">
													{{ $t('cus.reviewSupervisor') }}
												</th>
												<th width="11%">
													{{ $t('cus.reviewDate') }}
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="tdLogDetail in tdLogDetails">
												<td :data-th="$t('cus.processingDate')">
													{{ tdLogDetail.createDt }}
												</td>
												<td :data-th="$t('cus.processingStatus')">
													<label>{{ tdLogDetail.statusName }}</label><label v-if="tdLogDetail.verifyStatusName">/{{ tdLogDetail.verifyStatusName }}</label>
												</td>
												<td :data-th="$t('cus.processor')">
													{{ tdLogDetail.createBy }} {{ tdLogDetail.userName }}
												</td>
												<td :data-th="$t('cus.processingMethod')">
													{{ tdLogDetail.actionName }}
												</td>
												<td :data-th="$t('cus.processingContent')">
													<span>{{ tdLogDetail.memo }}</span>
												</td>
												<td :data-th="$t('cus.reviewSupervisor')">
													{{ tdLogDetail.verifyUserName }}
												</td>
												<td :data-th="$t('cus.reviewDate')">
													{{ tdLogDetail.verifyDt }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</vue-form>
				</div>
			</div>
			<div class="tx-note">
				<span class="tx-red">
					{{ $t('cus.customerInterviewNote') }} <br>
					1. {{ $t('cus.marketingNote1') }}<br>
					2. {{ $t('cus.marketingNote2') }}<br>
					3. {{ $t('cus.marketingNote3') }}
				</span>
			</div>
			<div v-show="tabCode == 1" class="modal-footer">
				<input
					v-if="logsVerifyStatusCode !== 'P' && isShowApprModalBtn"
					type="button"
					class="btn btn-primary"
					:value="$t('cus.scheduleAppointment')"
					@click="clearApptModalFormValues()"
				>
				<input
					v-if="logsVerifyStatusCode !== 'P' && doneYn == 'N'"
					type="button"
					class="btn btn-primary"
					:value="$t('cus.closeOrSubmitReview')"
					@click="updateAndAuditTdLists('Y')"
				>
				<input
					v-if="logsVerifyStatusCode !== 'P' && doneYn == 'N'"
					type="button"
					class="btn btn-primary"
					:value="$t('cus.tempSaveNotSubmit')"
					@click="updateTdLists('N')"
				>
				<input
					type="button"
					class="btn btn-white"
					:value="$t('cus.close')"
					aria-label="Close"
					@click="close"
				>
			</div>
			<div v-show="tabCode == 2" class="modal-footer">
				<input
					type="button"
					class="btn btn-white"
					:value="$t('cus.close')"
					aria-label="Close"
					@click="close"
				>
			</div>
		</div>
	</div>
	<!-- Modal 5  End -->
	<!-- Modal 訂定約訪 -->
	<vue-modal :is-open="isOpenApptModal" @close="() => isOpenApptModal = false">
		<template #content="props">
			<vue-cus-appointment
				id="apptModal"
				ref="appoModal"
				:close="props.close"
				title="訂定約訪"
				:cus-code="cusCode"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenReuseWordModal" @close="() => isOpenReuseWordModal = false">
		<template #content="props">
			<vue-cus-reuse-word-modal
				:id="'cusTdItemReuseWordModal'"
				:close="props.close"
				:wob-reuse-words="wobReuseWords"
				:super-modal-name="'itemModal'"
			/>
		</template>
	</vue-modal>
</template>
<script>
import _ from 'lodash';
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import vueCusReuseWordModal from '@/views/cus/include/reuseWordModal.vue';
import vueCusAppointment from '@/views/cus/include/cusAppointmentModal.vue';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		vueCusReuseWordModal,
		vueCusAppointment
	},
	props: {
		isWkf: Boolean,
		cusCode: String,
		tdCode: null,
		itemCode: null,
		logsVerifyStatusCode: String,
		refreshTs: 0, // 藉由監視外界傳入的 refreshTs, 達到每次都要重新叫用 APIs
		gotoPage: Function,
		close: Function
	},
	data: function () {
		return {
			isOpenModal: null,
			hasAuth: false,
			// 主要顯示資料
			tdItemHandleDetail: null,
			tdLogDetails: null,

			// 畫面顯示用參數
			// email: null, // 電子郵件
			// phoneH: '',
			// phoneM: '',
			// phoneS: '',
			gender: null,
			cusName: null, // 客戶姓名
			birth: null, // 生日

			phoneExt: null, // 區碼
			contactInfoList: [], // 聯絡方式
			emailList: [], // 電子郵件
			phoneHList: [], // 聯絡電話(住)
			phoneOList: [], // 聯絡電話(公司)
			phoneMList: [], // 聯絡電話(行動)

			rankName: null, // 投資屬性
			mktPhoneYn: null, // 是否拒絕行銷
			specRecommYn: null, // 特定金錢信託客戶投資有價證券推介同意書
			specCusYn: null, // 財富特定客戶(不得主動推介)

			//

			lastConnectionDt: null,
			doneYn: 'N',

			statusCode: '',
			statusName: {},
			actionCode: '',
			actionName: {},
			memo: '',
			verifyYn: null,
			isHis: null,
			traceYn: null,

			// 顯示用參數
			itemName: null,
			content: null,
			chiefVisitYn: 'N',
			isUpdateSuccess: false,
			isShowApprModalBtn: true,
			// 下拉選單
			tdStatusMenu: null,
			tdActionMenu: null,
			// 頁籤 id
			tabCode: 1,

			// 常用句機制
			reuseWord: null,
			newReuseWord: null,
			wobReuseWords: [],

			forbiddenContentWords: [
				'KYC',
				'借',
				'印章',
				'密碼',
				'貸',
				'對帳單',
				'跨境',
				'新加坡',
				'香港',
				'到期',
				'帳上',
				'保證',
				'停售',
				'建議',
				'解約',
				'高齡'
			], // 禁用保留字
			forbiddenContentMsg: null, // 禁用保留字錯誤提示訊息
			userCode: null,
			isOpenApptModal: false,
			isOpenReuseWordModal: false
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo'])
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				const self = this;
				if (newVal) {
					self.userCode = newVal.userCode;
				}
			}
		},
		refreshTs: function () {
			// 重新叫用 APIs
			const self = this;
			if (self.logsVerifyStatusCode === 'P') {
				self.$bi.alert(self.$t('cus.eventSubmittedForReview'));
			}
			if (self.tdCode != null) {
				self.getTdStatusMenu();
				self.getTdActionMenu();
				self.getTdListLogs();
				self.doUpdate();
				self.tabCode = 1;
			}
		},
		statusCode: function () {
			const self = this;
			const selectedStatus = self.tdStatusMenu.find(tdStatus => tdStatus.statusCode === self.statusCode);
			self.statusName = selectedStatus ? selectedStatus.statusName : '';
		},
		actionCode: function () {
			const self = this;
			const selectedAction = self.tdActionMenu.find(tdAction => tdAction.actionCode === self.actionCode);

			self.actionName = selectedAction ? selectedAction.actionName : '';
		},
		memo: function (str) {
			const self = this;
			self.checkForbiddenContentWords(str);
		}
	},
	mounted: function () {
		const self = this;
		self.setDefaultReuseWords();
		self.getReuseWords();
	},
	methods: {
		getTdStatusMenu: function () {
			const self = this;
			self.$api
				.getGetWobTdStatusApi({
					itemCode: self.itemCode
				})
				.then(function (ret) {
					self.tdStatusMenu = ret.data;
				});
		},
		getTdActionMenu: function () {
			const self = this;
			self.$api
				.getgGtWobTdActionApi({
					itemCode: self.itemCode
				})
				.then(function (ret) {
					self.tdActionMenu = ret.data;
				});
		},
		getTdListLogs: function () {
			const self = this;
			self.$api
				.getTdLogDetailsApi({
					tdCode: self.tdCode
				})
				.then(function (ret) {
					self.tdLogDetails = ret.data;
				});
		},
		getCusInfo: function () {
			const self = this;

			self.$api
				.getCustomer({
					cusCode: self.cusCode
				})
				.then(function (ret) {
					if (!ret.data) {
						self.$bi.alert(self.$t('cus.noCustomerFound'));
						self.clearValues();
						return;
					}

					self.gender = ret.data.gender;
					self.cusName = ret.data.cusName;
					self.birth = ret.data.birth;
					self.email = ret.data.email;
					self.rankName = ret.data.rankName;

					self.phoneExt = ret.data.phoneExt;
					self.contactInfoList = ret.data.contactInfoList;
					self.emailList = _.map(
						_.filter(self.contactInfoList, item => item.contactType === 'E' && !_.isEmpty(item.email?.trim())),
						'email'
					);
					self.phoneHList = _.map(
						_.filter(self.contactInfoList, item => item.contactType === 'H' && !_.isEmpty(item.phone1?.trim())),
						item => `${item.phone1}#${item.phoneExt || ''}`
					);
					self.phoneOList = _.map(
						_.filter(self.contactInfoList, item => item.contactType === 'O' && !_.isEmpty(item.phone1?.trim())),
						item => `${item.phone1}#${item.phoneExt || ''}`
					);
					self.phoneMList = _.map(
						_.filter(self.contactInfoList, item => item.contactType === 'M' && !_.isEmpty(item.phone1?.trim())),
						'phone1'
					);

					self.mktPhoneYn = ret.data.mktPhoneYn; // 是否拒絕行銷
					self.specRecommYn = ret.data.specRecommYn; // 推介同意書
					self.specCusYn = ret.data.specCusYn; // 財富特定客戶
					self.lastConnectionDt = ret.data.logCreateDt;
				});
		},
		getTdItemHandleDetail: function () {
			const self = this;
			self.$api
				.getTdItemHandleDetailApi({
					tdCode: self.tdCode
				})
				.then(function (ret) {
					self.itemName = ret.data.itemName;
					self.content = ret.data.content;
					self.statusCode = ret.data.statusCode;
					if (!self.statusCode) {
						self.statusCode = '';
					}
					self.actionCode = ret.data.actionCode;
					if (!self.actionCode) {
						self.actionCode = '';
					}
					self.memo = ret.data.memo;
					if (!self.memo) {
						self.memo = '';
					}
					self.verifyYn = ret.data.verifyYn;
					self.isHis = ret.data.isHis;
					self.traceYn = ret.data.traceYn;
					self.doneYn = ret.data.doneYn;

					// 重置 validator
					self.$refs.queryForm.resetForm();
				});
		},
		getReuseWords: function () {
			const self = this;
			const type = self.$route.path.split('/').filter(Boolean)[0].toUpperCase();
			self.$api.getReuseWordsApi({ type: type }).then(function (ret) {
				ret.data.forEach(function (item) {
					const index = item.wordsId - 1;
					self.wobReuseWords[index].words = item.words;
				});
			});
		},
		insertReuseWord: function () {
			const self = this;
			if (self.newReuseWord) {
				if (self.wobReuseWords.find(w => w.words === self.newReuseWord)) {
					self.$bi.alert(self.$t('cus.alreadySetSamePhrase'));
					return;
				}
				const type = self.$route.path.split('/').filter(Boolean)[0].toUpperCase();
				self.$api
					.postReuseWordsApi({
						type,
						words: self.newReuseWord
					})
					.then(function (ret) {
						self.getReuseWords({ type });
					});
			}
		},
		updateTdLists: function (isUpdateAndDone) {
			const self = this;
			self.$refs.tdItemModal.validate().then(function (pass) {
				if (pass.valid) {
					self.$api
						.patchUpdateTdListsApi({
							itemCode: self.itemCode,
							tdCode: self.tdCode,
							statusCode: self.statusCode,
							statusName: self.statusName,
							actionCode: self.actionCode,
							actionName: self.actionName,
							memo: self.memo,
							chiefVistYn: self.chiefVisitYn,
							verifyYn: 'N',
							traceYn: self.traceYn,
							isUpdateAndDone: isUpdateAndDone,
							isHis: self.isHis
						})
						.then(function (ret) {
							self.$bi.alert(self.$t('cus.updateSuccess'));
							self.isUpdateSuccess = true;
							self.gotoPage(0);
							self.$props.close();
						});
				}
			});
		},
		updateAndAuditTdLists: function (isUpdateAndDone) {
			const self = this;
			self.$refs.tdItemModal.validate().then(function (pass) {
				if (pass.valid) {
					self.$api
						.patchUpdateAndAuditTdListsApi({
							itemCode: self.itemCode,
							tdCode: self.tdCode,
							statusCode: self.statusCode,
							statusName: self.statusName,
							actionCode: self.actionCode,
							actionName: self.actionName,
							memo: self.memo,
							chiefVisitYn: self.chiefVisitYn,
							verifyYn: self.verifyYn,
							traceYn: self.traceYn,
							isHis: self.isHis,
							isUpdateAndDone: isUpdateAndDone
						})
						.then(function (ret) {
							self.$bi.alert(self.$t('cus.updateSuccess'));
							self.isUpdateSuccess = true;
							self.cleanForm();
							self.gotoPage(0);
							self.$props.close();
						});
				}
			});
		},
		doUpdate: function () {
			const self = this;

			self.isUpdateSuccess = false;
			self.getCusInfo();
			self.getTdItemHandleDetail();
		},

		setDefaultReuseWords: function () {
			const self = this;
			self.wobReuseWords = [];
			for (let i = 0; i < 10; i++) {
				const tempWordObj = {
					wordsId: i + 1,
					words: null
				};
				self.wobReuseWords.push(tempWordObj);
			}
		},

		appendReuseWord: function () {
			const self = this;
			if (!self.memo) {
				self.memo = '';
			}
			self.memo = self.memo + self.reuseWord;
		},
		// 禁用字檢核
		checkForbiddenContentWords: function (content) {
			const self = this;
			let forbiddenWord = null;
			if (content) {
				_.forEach(self.forbiddenContentWords, function (word) {
					if (_.includes(content, word) || _.includes(content, word.toLowerCase())) {
						forbiddenWord = word;
						return false;
					}
				});
			}

			if (forbiddenWord) {
				self.forbiddenContentMsg = self.$t('cus.forbiddenWordWarning').replace('{word}', forbiddenWord);
			}
			else {
				self.forbiddenContentMsg = null;
			}
		},
		cleanForm: function () {
			const self = this;

			self.cusName = null;
			self.rankName = null;
			self.birth = null;
			self.email = null;

			self.statusCode = null;
			self.actionCode = null;

			self.phoneHList = null;
			self.phoneSList = null;
			self.phoneMList = null;
			self.phoneOList = null;

			self.mktPhoneYn = null;
			self.mktPhoneYn = null;
			self.specRecommYn = null;
			self.specCusYn = null;

			self.$refs.tdItemModal.resetForm();
		},
		clearValues: function () {
			const self = this;

			self.cusName = null;
			self.rankName = null;
			self.gender = null;
			self.birth = null;
			self.email = null;

			self.phoneH = null;
			self.phoneS = null;
			self.phoneM = null;

			self.mktPhoneYn = null;
			self.mktPhoneYn = null;
			self.specRecommYn = null;
			self.specCusYn = null;
		},
		// 切換頁籤
		changeTab(tabCode) {
			const self = this;
			self.tabCode = tabCode;
		},
		clearApptModalFormValues: function () {
			const self = this;
			self.$refs.appoModal.clearFormValues();
			self.isOpenApptModal = true;
		},
		// checkIsShowApprModalBtn: function () {
		// 	var self = this;
		// 	if (self.doneYn != 'N') {
		// 		self.isShowApprModalBtn = false;
		// 		return;
		// 	}

		// 	var url = self.config.apiPath + '/cus/aoInfo';
		// 	$.bi
		// 		.ajax({
		// 			url: url,
		// 			method: 'GET',
		// 			data: {
		// 				cusCode: self.cusCode
		// 			}
		// 		})
		// 		.then(function (ret) {
		// 			self.isShowApprModalBtn = false;
		// 			ret.data.forEach(function (item) {
		// 				if (item.aoUserCode == self.userCode) {
		// 					self.isShowApprModalBtn = true;
		// 				}
		// 			});
		// 		});
		// },
		closeModal: function () {
			this.isOpenModal = false;
		},
		openModal: function () {
			this.isOpenModal = true;
		}
	}
};
</script>
