<template>
	<div>
		<!-- Modal 4 常用句設定 start -->
		<vue-modal :is-open="isOpenModal" @close="closeModal">
			<template #content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								{{ $t('cus.commonPhraseSettings') }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="handleClose(props.close)"
							/>
						</div>
						<div class="modal-body">
							<div v-show="showMsg" id="reuserWordMsg">
								<div id="reuseWordAlert" class="alert alert-success alert-dismissible" role="alert">
									<span class="ico-ok" /> {{ $t('cus.saveSuccessful') }}
									<button
										type="button"
										class="btn-close"
										aria-label="Close"
										@click="showMsg = false"
									/>
								</div>
							</div>
							<div class="table-responsive">
								<table class="table table-RWD table-bordered table-striped">
									<thead>
										<tr>
											<th width="8%" class="text-center">
												{{ $t('cus.sequenceNumber') }}
											</th>
											<th>{{ $t('cus.commonPhrase') }}</th>
										</tr>
									</thead>
									<tbody v-if="wobReuseWords && wobReuseWords.length > 0">
										<tr v-for="(item, index) in wobReuseWords" :key="index">
											<td :data-th="$t('cus.sequenceNumber')" class="text-center">
												{{ index + 1 }}
											</td>
											<td :data-th="$t('cus.commonPhrase')">
												<input
													v-model="item.words"
													:name="'wobReuseWords[' + index + '].words'"
													class="form-control"
													type="text"
													size="20"
													maxlength="20"
												>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div id="reuseWordSettingFooter" class="modal-footer">
							<input
								class="btn btn-white"
								type="button"
								:value="$t('cus.close')"
								@click.prevent="handleClose(props.close)"
							>
							<input
								id="save3"
								class="btn btn-primary"
								type="button"
								:value="$t('cus.save')"
								@click="updateReuseWords()"
							>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal 4 End -->
	</div>
</template>
<script>
import _ from 'lodash';
import vueModal from '@/views/components/model.vue';

export default {
	components: {
		vueModal
	},
	props: {
		wobReuseWords: Array,
		onClose: Function
	},
	data: function () {
		return {
			showMsg: false,
			isOpenModal: false,
			reuseWord: null,
			newReuseWord: null
		};
	},
	mounted: function () {
		const self = this;
		self.getReuseWords();
	},
	methods: {
		openModal: function () {
			this.isOpenModal = true;
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		handleClose: function (modalClose) {
			const self = this;
			modalClose(); // 關閉當前modal
			// 確保當前modal完全關閉後再打開原modal
			if (self.onClose) {
				setTimeout(() => {
					self.onClose();
				}, 300);
			}
		},
		getReuseWords: async function () {
			const self = this;
			const ret = await self.$api.getReuseWordsApi();
			ret.data.forEach(function (item) {
				const index = item.wordsId - 1;
				if (self.wobReuseWords[index]) {
					self.wobReuseWords[index].words = item.words;
				}
			});
		},
		updateReuseWords: async function () {
			const self = this;
			const reuseWordsUpdateReq = [];
			for (let i = 0; i < 10; i++) {
				const wordObj = {};
				if (!_.isBlank(self.wobReuseWords[i].words)) {
					wordObj.wordsId = i + 1;
					wordObj.words = self.wobReuseWords[i].words;
					reuseWordsUpdateReq.push(wordObj);
				}
			}

			const ret = await self.$api.updateReuseWordsApi(reuseWordsUpdateReq);
			self.getReuseWords();
			self.showMsg = true;
		}
	}
};
</script>
