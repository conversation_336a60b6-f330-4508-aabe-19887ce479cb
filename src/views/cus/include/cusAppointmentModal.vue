<template>
	<div v-bind="$attrs" :class="modalClass">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ displayTitle }}
				</h4>
				<button type="button" :class="buttonClass" @click="changeModalSize()">
					<i class="bi bi-arrows-fullscreen" />
				</button>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click.prevent="close()"
				/>
			</div>
			<div class="modal-body">
				<div id="msg_2" style="display: none">
					<div id="reuseWordAlert" class="alert alert-success alert-dismissible" role="alert">
						<span class="ico-ok" /> {{ $t('cus.appointmentRecordCreated') }}
						<button
							type="button"
							class="btn-close"
							data-bs-dismiss="alert"
							aria-label="Close"
						/>
					</div>
				</div>
				<div class="card-clientCard">
					<div class="card shadow-none mb-3">
						<table>
							<tbody>
								<tr>
									<td width="10%" class="clientCard-icon">
										<div class="avatar avatar-male">
											<img th:src="@{/images/avatar/man-1.png}" class="rounded-circle bg-info">
											<!--														<img th:src="@{/images/avatar/man-3.png}" class="rounded-circle bg-info" v-if="gender =='F'">-->
										</div>
										<h5 class="mb-0">
											{{ cusInfo.cusName || '--' }}
										</h5>
									</td>
									<td width="90%">
										<div class="caption tx-black">
											<span>{{ $t('cus.recentContactDate') }}：{{ $filters.formatDate(cusInfo.lastConnectionDt) || '--' }}</span>
										</div>
										<div class="row">
											<div class="col-lg-4">
												<ul class="list-unstyled profile-info-list">
													<li><i class="bi bi-clipboard-data mg-xl-e-5-f" />{{ $t('cus.investmentProfile') }}：{{ cusInfo.rankName || '--' }}</li>
													<li><i class="bi bi-gift mg-xl-e-5-f" />{{ $t('cus.birthday') }}：{{ cusInfo.birth || '--' }}</li>
													<li>
														<i class="bi bi-envelope mg-xl-e-5-f" />{{ $t('cus.email') }}：{{
															cusInfo.email || cusInfo.contactEmail || '--'
														}}
													</li>
												</ul>
											</div>
											<div class="col-lg-3">
												<ul class="list-unstyled profile-info-list">
													<li>
														<i class="bi bi-house-door mg-xl-e-5-f" />{{ $t('cus.contactPhoneHome') }}：{{
															cusInfo.phoneH || cusInfo.contactPhoneHome || '--'
														}}
													</li>
													<li>
														<i class="bi bi-building mg-xl-e-5-f" />{{ $t('cus.contactPhoneOffice') }}：{{
															cusInfo.phoneO || cusInfo.contactOffice || '--'
														}}
													</li>
													<li>
														<i class="bi bi-telephone mg-xl-e-5-f" />{{ $t('cus.contactPhoneMobile') }}：{{
															cusInfo.phoneM || cusInfo.contactPhone || '--'
														}}
													</li>
												</ul>
											</div>
											<div class="col-lg-5">
												<ul class="list-unstyled profile-info-list">
													<li>
														<i class="bi bi-journal-medical mg-xl-e-5-f" />{{ $t('cus.refuseMarketing') }}：
														<span :class="cusInfo.mktPhoneYn === 'Y' ? 'tx-red' : 'tx-green'">
															{{ cusInfo.mktPhoneYn === 'Y' ? $t('cus.yes') : $t('cus.no') }}
														</span>
													</li>
													<li>
														<i class="bi bi-journal-text mg-xl-e-5-f" />{{ $t('cus.specificTrustRecommendation') }}：
														<span :class="cusInfo.specRecommYn === 'Y' ? 'tx-red' : 'tx-green'">
															{{ cusInfo.specRecommYn === 'Y' ? $t('cus.yes') : $t('cus.no') }}
														</span>
													</li>
													<li>
														<i class="bi bi-journal-bookmark mg-xl-e-5-f" />{{ $t('cus.wealthSpecificCustomer') }}：
														<span :class="cusInfo.specCusYn === 'Y' ? 'tx-red' : 'tx-green'">
															{{ cusInfo.specCusYn === 'Y' ? $t('cus.yes') : $t('cus.no') }}
														</span>
													</li>
												</ul>
											</div>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<!-- 約訪紀錄 -->
				<div class="card card-form shadow-none">
					<div class="card-header">
						<h4>{{ $t('cus.appointmentRecord') }}</h4>
						<span class="tx-square-bracket">{{ $t('cus.requiredFieldNote') }}</span>
					</div>
					<div class="card-body">
						<vue-form v-slot="{ errors, validate }" ref="cusAppointmentModal">
							<div class="row g-3 align-items-end">
								<div class="col-lg-6 col-xl-6">
									<label class="form-label tx-require">{{ $t('cus.appointmentDate') }}</label>
									<vue-field
										id="nextRemindDt"
										v-model="nextRemindDt"
										type="date"
										name="nextRemindDt"
										value=""
										class="form-control"
										rules="required"
										:label="$t('cus.appointmentDate')"
										:disabled="!editable"
										:class="{ 'is-invalid': errors.nextRemindDt }"
									/>
									<div style="height: 25px">
										<span v-show="errors.nextRemindDt" class="text-danger">{{ errors.nextRemindDt }}</span>
									</div>
								</div>
								<div class="col-lg-6 col-xl-3">
									<label class="form-label tx-require">{{ $t('cus.appointmentTime') }}</label>
									<div class="input-group">
										<vue-field
											id="appoHour"
											v-model="appoHour"
											class="form-select"
											name="appoHour"
											rules="required"
											:label="$t('cus.appointmentTime')"
											:disabled="!editable"
											as="select"
											:class="{ 'is-invalid': errors.appoHour }"
										>
											<option value="00">
												00
											</option>
											<option value="01">
												01
											</option>
											<option value="02">
												02
											</option>
											<option value="03">
												03
											</option>
											<option value="04">
												04
											</option>
											<option value="05">
												05
											</option>
											<option value="06">
												06
											</option>
											<option value="07">
												07
											</option>
											<option value="08">
												08
											</option>
											<option value="09">
												09
											</option>
											<option value="10">
												10
											</option>
											<option value="11">
												11
											</option>
											<option value="12">
												12
											</option>
											<option value="13">
												13
											</option>
											<option value="14">
												14
											</option>
											<option value="15">
												15
											</option>
											<option value="16">
												16
											</option>
											<option value="17">
												17
											</option>
											<option value="18">
												18
											</option>
											<option value="19">
												19
											</option>
											<option value="20">
												20
											</option>
											<option value="21">
												21
											</option>
											<option value="22">
												22
											</option>
											<option value="23">
												23
											</option>
										</vue-field>
										<span class="input-group-text">{{ $t('cus.hour') }}</span>
										<vue-field
											id="appoMiniute"
											v-model="appoMiniute"
											class="form-select"
											name="appoMiniute"
											:disabled="!editable"
											rules="required"
											:label="$t('cus.appointmentTime')"
											as="select"
											:class="{ 'is-invalid': errors.appoMiniute }"
										>
											<option selected="selected" value="00">
												00
											</option>
											<option value="15">
												15
											</option>
											<option value="30">
												30
											</option>
											<option value="45">
												45
											</option>
										</vue-field>
										<span class="input-group-text">{{ $t('cus.minute') }}</span>
									</div>
									<div style="height: 25px">
										<span v-show="errors.appoHour" class="text-danger">{{ errors.appoHour }}</span>
										<span v-show="errors.appoMiniute" class="text-danger">{{ errors.appoMiniute }}</span>
									</div>
								</div>

								<div class="col-lg-6 col-xl-3">
									<label class="form-label tx-require">{{ $t('cus.visitPurpose') }}</label>
									<vue-field
										id="visitPurTypes"
										v-model="visitPurCode"
										class="form-select"
										name="visitPurCode"
										rules="required"
										:label="$t('cus.visitPurpose')"
										:disabled="!editable"
										as="select"
										:class="{ 'is-invalid': errors.visitPurCode }"
									>
										<option value="" disabled selected="selected">
											請選擇
										</option>
										<option v-for="(visitPur, index) in visitPurMenu" :key="index" :value="visitPur.codeValue">
											{{ visitPur.codeName }}
										</option>
									</vue-field>
									<div style="height: 25px">
										<span v-show="errors.visitPurCode" class="text-danger">{{ errors.visitPurCode }}</span>
									</div>
								</div>
								<div class="col-lg-6 col-xl-3">
									<label class="form-label tx-require">{{ $t('cus.visitMethod') }}</label>
									<vue-field
										id="visitAprTypes"
										v-model="visitAprCode"
										class="form-select"
										name="visitAprCode"
										rules="required"
										:label="$t('cus.visitMethod')"
										:disabled="!editable"
										as="select"
										:class="{ 'is-invalid': errors.visitAprCode }"
									>
										<option value="" disabled selected="selected">
											請選擇
										</option>
										<option v-for="(visitApr, index) in visitAprMenu" :key="index" :value="visitApr.codeValue">
											{{ visitApr.codeName }}
										</option>
									</vue-field>
									<div style="height: 25px">
										<span v-show="errors.visitAprCode" class="text-danger">{{ errors.visitAprCode }}</span>
									</div>
								</div>
								<div class="col-lg-6 col-xl-3">
									<label class="form-label tx-require">{{ $t('cus.appointmentSubject') }}</label>
									<vue-field
										v-model="title"
										class="form-control"
										name="title"
										type="text"
										size="30"
										value=""
										:class="{ 'is-invalid': errors.title }"
										rules="required"
										:label="$t('cus.appointmentSubject')"
										:disabled="!editable"
									/>
									<div style="height: 25px">
										<span v-show="errors.title" class="text-danger">{{ errors.title }}</span>
									</div>
								</div>
								<div class="col-lg-8 col-xl-6">
									<label class="form-label tx-require">{{ $t('cus.expiryNotificationSetting') }}</label> <br>
									<div class="form-check form-check-inline">
										<vue-field
											id="adv_NN"
											v-model="advNce"
											class="form-check-input"
											type="radio"
											checked="checked"
											value="N"
											name="advNce"
											rules="required"
											:label="$t('cus.expiryNotificationSetting')"
											:disabled="!editable"
										/>
										<label class="form-check-label" for="adv_NN">{{ $t('cus.noAdvanceNotification') }}</label>
									</div>
									<div class="form-check form-check-inline">
										<vue-field
											id="adv_YY"
											v-model="advNce"
											class="form-check-input"
											type="radio"
											value="Y"
											name="advNce"
											rules="required"
											:label="$t('cus.expiryNotificationSetting')"
											:disabled="!editable"
										/>
										<label class="form-check-label" for="adv_YY">{{ $t('cus.advanceNotification') }} </label>
									</div>
									<div class="d-inline-block">
										<div class="input-group">
											<input
												id="advNceDay"
												v-model="advNceDay"
												class="form-control"
												type="number"
												min="1"
												size="2"
												maxlength="2"
												:disabled="!editable || advNce === 'N'"
											>
											<select
												id="advNcePrd"
												v-model="advNcePrd"
												class="form-select"
												:disabled="!editable || advNce === 'N'"
											>
												<option value="">
													{{ $t('cus.pleaseSelect') }}
												</option>
												<option value="D">
													{{ $t('cus.day') }}
												</option>
												<option value="W">
													{{ $t('cus.week') }}
												</option>
											</select>
											<span class="input-group-text">{{ $t('cus.notify') }} </span>
										</div>
									</div>
									<div style="height: 25px">
										<span v-show="errors.advNce" class="text-danger">{{ errors.advNce }}</span>
										<span v-show="errors.advNcePrd" class="text-danger">{{ errors.advNcePrd }}</span>
										<span v-show="errors.advNceDay" class="text-danger">{{ errors.advNceDay }}</span>
									</div>
								</div>

								<div class="col-lg-12">
									<label class="form-label tx-require">{{ $t('cus.appointmentContent') }}</label>
									<vue-field
										id="content"
										v-model="content"
										as="textarea"
										class="form-control"
										name="content"
										rows="5"
										cols="50"
										size="400"
										:class="{ 'is-invalid': errors.content }"
										:disabled="!editable"
										:label="$t('cus.appointmentContent')"
										rules="required"
									/>
									<div style="height: 25px">
										<span v-if="forbiddenContentMsg" class="text-danger">{{ forbiddenContentMsg }}</span>
									</div>
									<div style="height: 3px">
										<span v-show="errors.content" class="text-danger">{{ errors.content }}</span>
									</div>
								</div>
								<div class="col-lg-6">
									<div class="input-group">
										<span class="input-group-text"> {{ $t('cus.commonPhrases') }}</span>
										<select
											id="reuseWord"
											v-model="reuseWord"
											name="reuseWord"
											class="form-select"
											:disabled="!editable"
										>
											<template v-for="selectWords in wobReuseWords">
												<option v-if="selectWords.words" :value="selectWords.words">
													{{ selectWords.words }}
												</option>
											</template>
											<!--															<option v-for="selectWords in wobReuseWords" v-if="selectWords.words" :value="selectWords.words">{{selectWords.words}}</option>-->
										</select>
										<button
											id="setContent"
											type="button"
											class="btn btn-info btn-glow"
											:disabled="!editable"
											@click="appendReuseWord()"
										>
											{{ $t('cus.addToContent') }}
										</button>
									</div>
								</div>
								<div class="col-lg-6">
									<div class="input-group">
										<span class="input-group-text">{{ $t('cus.commonPhraseSetting') }}</span>
										<input
											id="words"
											v-model="newReuseWord"
											class="form-control"
											type="text"
											size="20"
											maxlength="20"
										>
										<button
											v-if="status != 'CLOSE'"
											id="wordAdd"
											type="button"
											class="btn btn-info btn-glow"
											@click="insertReuseWord()"
										>
											{{ $t('cus.add') }}
										</button>
										<button
											v-if="status != 'CLOSE'"
											id="wordSetting"
											type="button"
											class="btn btn-info btn-glow"
											@click.prevent="isOpenReuseWordModal = true"
										>
											{{ $t('cus.settings') }}
										</button>
									</div>
								</div>
							</div>
						</vue-form>
					</div>
				</div>
			</div>

			<!--						<div class="tx-note">-->
			<!--							<span class="tx-red">-->
			<!--							請詳實填載顧客訪談紀錄內容，行銷前應特別注意以下事項(不限以下類型) <br>-->
			<!--							1. 不得對「特殊客群」就國內有價證券特定投資標的(如:基金、海外債、股票、ETFs等)進行主動推介。<br>-->
			<!--							2. 不得以不當方式行銷(如:利用顧客存款資料、以存款利率與商品報酬率比較、以及將配息或配息率即將調整作為行銷誘因、勸誘以借款或舉債方式理財、以打敗通膨或免課二代健保補充保費為訴求等)。<br>-->
			<!--							3. 不得以停售、漲價、致贈禮劵、節稅等做為招攬之訴求行銷保險商品。-->
			<!--							</span>-->
			<!--						</div>-->
			<div class="modal-footer">
				<div id="save1">
					<button
						id="apptModalCloseButton"
						type="button"
						class="btn btn-white"
						@click.prevent="close()"
					>
						{{ $t('cus.closeWindow') }}
					</button>
					<!--								<button type="button" class="btn btn-primary" @click="savaAppointment('N')" v-if="editable">儲存</button>-->
					<!--								<button type="button" class="btn btn-primary" @click="savaAppointment('Y')" v-if="editable">儲存並結案</button>-->
					<button
						v-if="editable"
						type="button"
						class="btn btn-primary"
						@click="insertAppointment()"
					>
						{{ $t('cus.save') }}
					</button>
				</div>
			</div>
		</div>
	</div>
	<!-- Modal 1 End -->
	<vue-modal :is-open="isOpenReuseWordModal" @close="() => isOpenReuseWordModal = false">
		<template #content="props">
			<vue-cus-reuse-word-modal
				:id="'cusAppoReuseWordModal'"
				:close="props.close"
				:wob-reuse-words="wobReuseWords"
				:super-modal-name="'apptModal'"
			/>
		</template>
	</vue-modal>
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import vueCusReuseWordModal from '@/views/cus/include/reuseWordModal.vue';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		vueCusReuseWordModal
	},
	props: {
		cusCode: String,
		recCode: String,
		gotoPage: Function,
		close: Function
	},
	data: function () {
		return {
			opCode: null,
			// API用參數
			nextRemindDt: null, // 約訪日期
			appoHour: '00', // 約訪時間(時)
			appoMiniute: '00', // 約訪時間(分)
			visitPurCode: '', // 訪談目的
			visitAprCode: '', // 訪談方式
			title: null, // 約訪主旨
			advNce: 'N', // 是否提前通知
			advNceDay: null, // 提前通知天數
			advNcePrd: '', // 提前通知單位
			content: null, // 約訪內容
			doneYn: null, // 是否完成
			status: null,
			verifyStatusCode: null,

			// 畫面顯示用參數
			cusInfo: {
				cusName: null,
				rankName: null,
				birth: null,
				email: null,
				phoneH: '',
				phoneM: '',
				phoneS: '',
				mktPhoneYn: null,
				specRecommYn: null,
				specCusYn: null,
				lastConnectionDt: null
			},

			editable: true,

			// 下拉選單
			visitPurMenu: [], // 訪談目的選單
			visitAprMenu: [], // 訪談方式選單

			// 常用句機制
			reuseWord: null,
			newReuseWord: null,
			wobReuseWords: [],
			// 彈窗標題
			displayTitle: '建立約訪',
			modalClass: 'modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable',
			buttonClass: 'btn-expand',
			// 禁用字檢核
			forbiddenContentWords: [
				'KYC',
				'借',
				'印章',
				'密碼',
				'貸',
				'對帳單',
				'跨境',
				'新加坡',
				'香港',
				'到期',
				'帳上',
				'保證',
				'停售',
				'建議',
				'解約',
				'高齡'
			], // 禁用保留字
			forbiddenContentMsg: null, // 禁用保留字錯誤提示訊息,
			isOpenReuseWordModal: false
		};
	},
	watch: {
		cusCode: function () {
			const self = this;
			if (self.cusCode) {
				self.getCusInfo();
			}
		},
		recCode: function (val) {
			const self = this;
			if (val) {
				self.getTdConnRec();
				if (self.status == 'CLOSE') {
					self.displayTitle = '檢視約訪';
				}
				else {
					self.displayTitle = '編輯約訪';
				}
			}
			else {
				self.clearFormValues();
			}
		},
		content: function (str) {
			const self = this;
			self.checkForbiddenContentWords(str);
		}
	},
	mounted: function () {
		const self = this;
		// Initialize default title
		if (!self.displayTitle) {
			self.displayTitle = self.$t('cus.createAppointment');
		}

		if (self.cusCode) {
			self.getCusInfo();
		}
		self.getVisitPurMenu();
		self.getVisitAprMenu();
		self.setDefaultReuseWords();
		self.getReuseWords();
	},
	methods: {
		doUpdateAppointmentRec: function (recCode) {
			const self = this;
			if (recCode) {
				self.getTdConnRec();
				if (self.status == 'CLOSE') {
					self.displayTitle = '檢視約訪';
				}
				else {
					self.displayTitle = '編輯約訪';
				}
			}
			else {
				self.displayTitle = '訂定約訪';
				self.clearFormValues();
			}
		},
		getVisitPurMenu: function () {
			const self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'VISIT_PUR_CODE'
				})
				.then(function (ret) {
					self.visitPurMenu = ret.data;
				});
		},
		getVisitAprMenu: function () {
			const self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'VISIT_APR_CODE'
				})
				.then(function (ret) {
					self.visitAprMenu = ret.data;
				});
		},
		getCusInfo: function () {
			const self = this;
			let opCode = null;

			if (self.opCode) {
				opCode = self.opCode;
			}
			self.$api
				.getCustomer({
					cusCode: self.cusCode,
					opCode: opCode
				})
				.then(function (ret) {
					if (!ret.data) {
						self.$bi.alert('查無顧客，請確認是否為久未往來顧客。');
						self.clearCusValues();
						return;
					}
					self.cusInfo = ret.data;
					self.cusInfo.lastConnectionDt = ret.data.logCreateDt;

					// 將聯絡方式分類
					ret.data.contactInfoList.forEach(function (item) {
						switch (item.contactType) {
							case 'E': // email
								self.cusInfo.email = item.email;
								break;
							case 'H': // 住家電話
								self.cusInfo.phoneH = item.phone1;
								break;
							case 'O': // 公司電話
								self.cusInfo.phoneO = item.phone1;
								break;
							case 'M': // 手機
								self.cusInfo.phoneM = item.phone1;
								break;
						}
					});
				});
		},
		savaAppointment: function (doneYn) {
			const self = this;
			if (self.recCode) {
				self.updateAppointment(doneYn);
			}
			else {
				self.updateAppointment(doneYn);
			}
		},
		// 建立約訪
		insertAppointment: function () {
			const self = this;
			const cusAppointmentModal = self.$refs.cusAppointmentModal;
			cusAppointmentModal.validate().then(function (pass) {
				if (pass.valid) {
					if (self.advNce == 'Y') {
						if (!self.advNcePrd || self.advNcePrd == '') {
							cusAppointmentModal.setFieldError('advNcePrd', '請輸入到期通知單位。');
							return;
						}

						if (!self.advNceDay) {
							cusAppointmentModal.setFieldError('advNceDay', '請輸入到期通知時間。');
							return;
						}
					}

					const nextRemindTime = self.appoHour + ':' + self.appoMiniute;
					self.$api
						.postVisit({
							cusCode: self.cusCode,
							cusName: self.cusInfo.cusName,
							nextRemindDt: self.nextRemindDt,
							nextRemindTime: nextRemindTime,
							visitPurCode: self.visitPurCode,
							visitAprCode: self.visitAprCode,
							title: self.title,
							advNce: self.advNce,
							advNceDay: self.advNceDay,
							advNcePrd: self.advNcePrd,
							content: self.content
						})
						.then(function (ret) {
							self.$bi.alert('新增成功');
							$('#apptModalCloseButton').click();
							if (self.gotoPage) {
								self.gotoPage(0);
							}
						});
				}
			});
		},
		// 更新約訪 (儲存/儲存並結案)
		updateAppointment: function (doneYn) {
			const self = this;
			self.$refs.queryForm.validate().then(function (pass) {
				if (pass.valid) {
					const url = self.config.apiPath + '/wob/appointmentRec';

					const nextRemindTime = self.appoHour + ':' + self.appoMiniute;
					$.bi
						.ajaxJson({
							url: url,
							method: 'PATCH',
							data: {
								recCode: self.recCode,
								cusCode: self.cusCode,
								nextRemindDt: self.nextRemindDt,
								nextRemindTime: nextRemindTime,
								visitPurCode: self.visitPurCode,
								visitAprCode: self.visitAprCode,
								title: self.title,
								advNce: self.advNce,
								advNceDay: self.advNceDay,
								advNcePrd: self.advNcePrd,
								content: self.content,
								doneYn: doneYn,
								verifyStatusCode: doneYn == 'Y' ? 'P' : null
							}
						})
						.then(function (ret) {
							self.$bi.alert('更新成功');
							$('#apptModalCloseButton').click();
							if (self.gotoPage) {
								self.gotoPage(0);
							}
						});
				}
			});
		},
		setDefaultReuseWords: function () {
			const self = this;
			self.wobReuseWords = [];
			for (let i = 0; i < 10; i++) {
				const tempWordObj = {
					wordsId: i + 1,
					words: null
				};
				self.wobReuseWords.push(tempWordObj);
			}
		},
		changeModalSize: function () {
			const self = this;
			if (self.modalClass === 'modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable') {
				self.modalClass = 'modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable fullscreen';
				self.buttonClass = 'btn-expand mini';
			}
			else {
				self.modalClass = 'modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable';
				self.buttonClass = 'btn-expand';
			}
		},
		getReuseWords: function () {
			const self = this;
			self.$api.getReuseWordsApi().then(function (ret) {
				ret.data.forEach(function (item) {
					const index = item.wordsId - 1;
					self.wobReuseWords[index].words = item.words;
				});
			});
		},
		insertReuseWord: function () {
			const self = this;
			if (self.newReuseWord) {
				let wordsId = null;
				for (let i = 0; i < 10; i++) {
					if (!self.wobReuseWords[i].words) {
						wordsId = i + 1;
						break;
					}
				}

				if (wordsId) {
					self.$api
						.postReuseWordsApi({
							wordsId: wordsId,
							words: self.newReuseWord
						})
						.then(function (ret) {
							self.getReuseWords();
						});
				}
				else {
					if (!self.memo) {
						self.memo = '';
					}
					self.memo = self.memo + self.newReuseWord;
					self.$bi.alert('常句已超過設定個數，請由常用句設定頁面進行調整。');
				}
				self.newReuseWord = null;
			}
		},
		appendReuseWord: function () {
			const self = this;
			if (!self.content) {
				self.content = '';
			}
			self.content = self.content + self.reuseWord;
		},
		getTdConnRec: function () {
			const self = this;
			self.$api
				.getTdRecApi({
					recCode: self.recCode
				})
				.then(function (ret) {
					self.nextRemindDt = moment(ret.data.nextRemindDt).format('YYYY-MM-DD');
					self.title = ret.data.title;
					self.visitPurCode = ret.data.visitPurCode;
					self.visitAprCode = ret.data.visitAprCode;
					self.content = ret.data.content;
					self.status = ret.data.status;
					self.verifyStatusCode = ret.data.verifyStatusCode;

					if (self.verifyStatusCode == 'A' || self.verifyStatusCode == 'P') {
						self.editable = false;
					}
					else {
						self.editable = true;
					}

					if ('CLOSE' == ret.data.status) {
						self.doneYn = 'Y';
					}
					else {
						self.doneYn = 'N';
					}

					if (ret.data.nextRemindTime) {
						const nextRemindTimeArr = ret.data.nextRemindTime.split(':');
						self.appoHour = nextRemindTimeArr[0];
						self.appoMiniute = nextRemindTimeArr[1];
					}
				});
		},
		// 禁用字檢核
		checkForbiddenContentWords: function (content) {
			const self = this;
			let forbiddenWord = null;
			if (content) {
				_.forEach(self.forbiddenContentWords, function (word) {
					if (_.includes(content, word) || _.includes(content, word.toLowerCase())) {
						forbiddenWord = word;
						return false;
					}
				});
			}

			if (forbiddenWord) {
				self.forbiddenContentMsg = self.$t('cus.contentContainsForbiddenWord').replace('{word}', forbiddenWord);
			}
			else {
				self.forbiddenContentMsg = null;
			}
		},
		clearCusValues: function () {
			const self = this;
			self.cusInfo.cusName = null;
			self.cusInfo.rankName = null;
			self.cusInfo.birth = null;
			self.cusInfo.email = null;
			self.cusInfo.phoneH = null;
			self.cusInfo.phoneS = null;
			self.cusInfo.phoneM = null;
			self.cusInfo.mktPhoneYn = null;
			self.cusInfo.specRecommYn = null;
			self.cusInfo.specCusYn = null;
			self.cusInfo.lastConnectionDt = null;
		},
		clearFormValues: function () {
			const self = this;
			self.nextRemindDt = null;
			self.appoHour = '00';
			self.appoMiniute = '00';
			self.title = null;
			self.visitPurCode = '';
			self.visitAprCode = '';
			self.content = null;
			self.doneYn = null;
			self.status = null;
			self.verifyStatusCode = null;
			self.displayTitle = '建立約訪';
			self.$refs.cusAppointmentModal.resetForm();
		},
		setOpCode: function (opCode) {
			const self = this;
			self.opCode = opCode;
		}
	}
};
</script>
