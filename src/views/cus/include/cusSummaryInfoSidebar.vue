<template>
	<div class="filemgr-sidebar heighter-cusblock">
		<div v-if="customer" class="filemgr-sidebar-header">
			<div class="text-center">
				<a href="../../CIF/Overview/clientOverview.htm"> <img
					:src="getImgURL('avatar', 'sidemenu-avatar.png')"
					alt=""
				></a>
				<h5 class="m-0">
					{{ customer.cusName }}<span class="cart-container" />
				</h5>
				<h6 class="text-secondary">
					{{ $t('cus.yearsOld', {age: customer.age}) }} {{ customer.pbStatusName ? `(${customer.pbStatusName})` : '' }}
				</h6>
			</div>
		</div>
		<div class="filemgr-sidebar-body">
			<div id="sidebarMenu" class="p-3">
				<div id="sidebarMenu" class="px-3">
					<ul class="sidebar-nav">
						<li v-for="item in summariesMenu" class="nav-item">
							<a class="nav-link cif001" @click="menuHandler(item.menuCode)">{{ item.menuName }}</a>
						</li>
					</ul>
				</div>

				<!-- 快捷功能按鈕 -->
				<div v-if="hasAuth" class="btn-fast-block">
					<h6>{{ $t('cus.quickActionButtons') }}</h6>
					<a href="../../CIF/Service/cusRecord.htm" class="btn btn-quick-icon cifqic001">
						<i class="quick-icon fa-solid fa-address-card" /><br>
						<p>{{ $t('cus.contactRecord') }}</p>
					</a>
					<a href="../../CIF/Service/cusMemo.htm" class="btn btn-quick-icon cifqic002">
						<i class="quick-icon fas fa-calendar" /><br>
						<p>{{ $t('cus.appointmentSchedule') }}</p>
					</a>
					<a href="../../CIF/Service/cusAllEvent.htm" class="btn btn-quick-icon cifqic003">
						<i class="quick-icon fas fa-flag" /><br>
						<p>{{ $t('cus.eventHandling') }}</p>
					</a>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { getImgURL } from '@/utils/imgURL';

export default {
	props: {
		cusCode: String,
		pageCode: Number,
		hasAuth: Boolean,
		customer: Object,
		setMenuCode: Function
	},
	data: function () {
		return {
			summariesMenu: []
		};
	},
	watch: {
		hasAuth: function (newVal, oldVal) {
			const self = this;
			self.checkRoleMenus();
		}
	},
	mounted: function () {
		const self = this;
		self.checkRoleMenus();
	},
	methods: {
		getImgURL,
		checkRoleMenus: async function () {
			const self = this;
			const ret = await self.$api.getCusSummariesMenuApi({
				pbStatus: self.customer.pbStatus
			});
			if (ret.data) {
				self.summariesMenu = ret.data;
			}
		},
		menuHandler: function (menuCode) {
			const self = this;
			self.setMenuCode(menuCode);
		}
	}
};
</script>
<style>
.cif001 {
	cursor: pointer;
}
</style>
