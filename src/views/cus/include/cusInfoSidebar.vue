<template>
	<div class="filemgr-sidebar heighter-cusblock">
		<div v-if="customer" class="filemgr-sidebar-header">
			<div class="d-flex align-items-center">
				<div class="avatar avatar-md me-1">
					<img v-if="customer.gender == 'M'" :src="getImgURL('avatar', 'man-2.png')" class="rounded-circle bg-info">
					<img v-if="customer.gender == 'F'" :src="getImgURL('avatar', 'man-3.png')" class="rounded-circle bg-info">
				</div>
				<div>
					<h6 class="mb-0">
						{{ $t('cus.customerColon') }}{{ customer.cusName }}
					</h6>
					<p class="tx-13 tx-color-03 mb-0">
						{{ $t('cus.ageYearsOld').replace('{age}', customer.age) }}
					</p>
				</div>
			</div>
			<ul class="list-info">
				<li>
					{{ $t('cus.assignedPBC') }}<span v-if="customer.userName">{{ customer.userName }}</span>
				</li>
			</ul>
		</div>
		<div class="filemgr-sidebar-body">
			<div id="sidebarMenu" class="p-3">
				<ul class="sidebar-nav">
					<li class="nav-item show">
						<a href="#" class="nav-link with-sub"><i />{{ $t('cus.customerOverview') }}</a>
						<nav class="nav">
							<a
								v-if="isShowCusInfo"
								class="nav-link"
								:href="config.contextPath + '/cus/cusInfo?cusCode=' + _.convertUrlSpecChar(cusCode)"
								:class="{ active: pageCode == 1 }"
							>{{ cusInfoMenuName }}</a>
							<a
								v-if="isShowClientOverview"
								class="nav-link"
								:href="config.contextPath + '/cus/clientOverview?cusCode=' + _.convertUrlSpecChar(cusCode)"
								:class="{ active: pageCode == 2 }"
							>{{ clientOverviewMenuName }}</a>
							<a
								v-if="isShowAssetsDetail"
								class="nav-link"
								:href="config.contextPath + '/cus/assetsDetail?cusCode=' + _.convertUrlSpecChar(cusCode)"
								:class="{ active: pageCode == 3 }"
							>{{ assetsDetailMenuName }}</a>
							<a
								v-if="isShowInvestAnalysis"
								class="nav-link"
								:href="config.contextPath + '/cus/investAnalysis?cusCode=' + _.convertUrlSpecChar(cusCode)"
								:class="{ active: pageCode == 4 }"
							>{{ investAnalysisMenuName }}</a>
							<a
								v-if="isShowClientServiceRec"
								class="nav-link"
								:href="config.contextPath + '/cus/clientServiceRec?cusCode=' + _.convertUrlSpecChar(cusCode)"
								:class="{ active: pageCode == 5 }"
							>{{ clientServiceRecMenuName }}</a>
							<a
								v-if="isShowClientPriceAlert"
								class="nav-link"
								:href="config.contextPath + '/cus/clientPriceAlert?cusCode=' + _.convertUrlSpecChar(cusCode)"
								:class="{ active: pageCode == 7 }"
							>{{ clientPriceAlertMenuName }}</a>
							<a
								v-if="isShowBankstatement"
								class="nav-link"
								:href="config.contextPath + '/cus/bankstatement?cusCode=' + _.convertUrlSpecChar(cusCode)"
								:class="{ active: pageCode == 9 }"
							>{{ bankstatementMenuName }}</a>
						</nav>
					</li>
				</ul>
			</div>
		</div>
		<vue-fav-customer-model />
	</div>
</template>
<script>
import _ from 'lodash';
import vueFavCustomerModel from '../CUS0201/include/favCustomerModal.vue';
import { getImgURL } from '@/utils/imgURL.js';
export default {
	components: {
		vueFavCustomerModel
	},
	props: {
		cusCode: String,
		pageCode: Number,
		setAuth: Function,
		setCustomer: Function
	},
	data: function () {
		return {
			isHideMenu: false,
			path: null,
			// 畫面判斷用邏輯
			customer: {
				cusName: null,
				idnEntityType: null
			},
			isShowCusInfo: false,
			isShowClientOverview: false,
			isShowAssetsDetail: false,
			isShowInvestAnalysis: false,
			isShowClientServiceRec: false,
			isShowClientPriceAlert: false,
			isShowBankstatement: false,

			cusInfoMenuName: '',
			clientOverviewMenuName: '',
			assetsDetailMenuName: '',
			investAnalysisMenuName: '',
			clientServiceRecMenuName: '',
			clientPriceAlertMenuName: '',
			bankstatementMenuName: ''
		};
	},
	mounted: function () {
		const self = this;
		self.checkAuth();
		self.checkRoleMenus();
	},
	methods: {
		getImgURL,
		checkAuth: async function () {
			const self = this;
			const ret = await self.$api.getCustomersApi({
				cusCode: self.cusCode
			});
			if (!ret.data || ret.data.length == 0) {
				self.setAuth(false);
				self.$bi.confirm(self.$t('cus.noCustomerData'), {
					event: {
						confirmOk: function () {
							location.href = self.config.contextPath + '/';
						}
					},
					button: {
						confirmOk: self.$t('cus.confirmOk')
					}
				});
			}
			else {
				self.setAuth(true);
				self.customer = ret.data[0];
				if (self.setCustomer) {
					self.setCustomer(self.customer);
				}
			}
		},
		checkRoleMenus: async function () {
			const self = this;
			const ret = await self.$api.getUserRoleMenuApi();
			if (ret.data) {
				ret.data.forEach(function (item) {
					switch (item.menuCode) {
						case 'M21-12':
						case 'MB2-12':
							// 基本資料
							self.isShowCusInfo = true;
							self.cusInfoMenuName = item.menuName || self.$t('cus.basicInformation');
							break;
						case 'M21-11':
						case 'MB2-11':
							// 帳戶總覽
							self.isShowClientOverview = true;
							self.clientOverviewMenuName = item.menuName || self.$t('cus.accountOverview');
							break;
						case 'M21-14':
						case 'MB2-14':
							// 帳戶明細
							self.isShowAssetsDetail = true;
							self.assetsDetailMenuName = item.menuName || self.$t('cus.accountDetails');
							break;
						case 'M21-13':
						case 'MB2-13':
							// 投資績效分析
							self.isShowInvestAnalysis = true;
							self.investAnalysisMenuName = item.menuName || self.$t('cus.investmentPerformanceAnalysis');
							break;
						case 'M21-15':
						case 'MB2-15':
							// 服務紀錄
							self.isShowClientServiceRec = true;
							self.clientServiceRecMenuName = item.menuName || self.$t('cus.serviceRecord');
							break;
						case 'M21-17':
						case 'MB2-17':
							// 報酬率警示設定
							self.isShowClientPriceAlert = true;
							self.clientPriceAlertMenuName = item.menuName || self.$t('cus.rewardRateAlertSettings');
							break;
						case 'M21-16':
						case 'MB2-16':
							// 顧客報告書
							self.isShowBankstatement = true;
							self.bankstatementMenuName = item.menuName || self.$t('cus.customerReport');
							break;
					}
				});
			}
		}
	}
};
</script>
