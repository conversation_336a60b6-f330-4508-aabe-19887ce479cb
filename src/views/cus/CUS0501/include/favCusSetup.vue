<template>
	<div class="card card-table">
		<div>
			<div class="card-header">
				<h4>{{ $t('cus.pleaseEnterFollowingData') }}</h4>
			</div>
			<div class="card-body">
				<div class="row g-3 ">
					<div class="col-md-4">
						<label class="form-label">{{ $t('cus.byCustomerAUM') }}</label>
						<div class="input-group">
							<select id="selectCusGrades" v-model="graCode" class="form-select">
								<option value="">
									{{ $t('cus.all') }}
								</option>
								<option v-for="aumData in aumMenu" :value="aumData.graCode">
									{{ aumData.graName }}
								</option>
							</select>
							<button
								id="searchByGraBtn"
								class="btn btn-primary btn-glow"
								type="button"
								@click="queryByGraCode()"
							>
								<i class="bi bi-search" />
							</button>
						</div>
					</div>
					<div class="col-md-4">
						<label class="form-label">{{ $t('cus.byCustomerIdTaxId') }}</label>
						<div class="input-group">
							<input
								id="inputCusCode"
								v-model="cusCode"
								name="inputCusCode"
								class="form-control"
								type="text"
								size="30"
								maxlength="10"
							>
							<button
								id="searchByCusCodeBtn"
								class="btn btn-primary btn-glow"
								type="button"
								@click="queryByCusCode()"
							>
								<i class="bi bi-search" />
							</button>
						</div>
					</div>
					<div class="col-md-4">
						<label class="form-label">{{ $t('cus.byCustomerName') }}</label>
						<div class="input-group">
							<input
								id="inputCusName"
								v-model="cusName"
								name="inputCusName"
								class="form-control"
								type="text"
								size="30"
								maxlength="10"
							>
							<button
								id="searchByNameBtn"
								class="btn btn-primary btn-glow"
								type="button"
								@click="queryByCusName()"
							>
								<i class="bi bi-search" />
							</button>
						</div>
					</div>
				</div>
				<div class="divider" />
				<div class="row g-0 text-center align-items-center justify-content-center">
					<div id="ShowList" class="col-5">
						<div class="tx-title">
							{{ $t('cus.availableCustomers') }}
						</div>
						<select
							id="availableCusPool"
							v-model="queryCusCodes"
							name="availableCusPool"
							class="form-select"
							size="13"
							multiple
						>
							<option v-for="cusItem in queryCus" :value="cusItem.cusCode">
								{{ cusItem.maskCusName }}
								({{ cusItem.maskCusCode }}) {{ cusItem.graName }}
							</option>
						</select>
					</div>
					<div class="col-2 ">
						<button
							id="addCus"
							type="button"
							class="btn btn-info mb-2"
							@click="addCus()"
						>
							{{ $t('cus.join') }} <i class="bi bi-arrow-right-circle" />
						</button>
						<br>
						<button
							id="removeCus"
							type="button"
							class="btn btn-info"
							@click="removeCus()"
						>
							{{ $t('cus.remove') }} <i class="bi bi-arrow-left-circle" />
						</button>
					</div>
					<div class="col-5 ">
						<div class="tx-title">
							{{ $t('cus.addedGroupCustomers') }}
						</div>
						<select
							id="selectedCusPool"
							v-model="selectCusCodes"
							name="selectedCusPool"
							class="form-select"
							size="13"
							multiple
						>
							<option v-for="cusItem in selectCus" :value="cusItem.cusCode">
								{{ cusItem.maskCusName }}
								({{ cusItem.maskCusCode }}) {{ cusItem.graName }}
							</option>
						</select>
					</div>
				</div>
			</div>
			<div class="text-end mt-3">
				<button type="button" class="btn btn-lg btn-glow btn-secondary" @click="next()">
					{{ $t('cus.cancel') }}
				</button>
				<button type="button" class="btn btn-lg btn-glow btn-primary ms-1" @click="patchCusGroupDetail()">
					{{ $t('cus.save') }}
				</button>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
export default {
	props: {
		groupName: String,
		groupCode: Number,
		homePage: Function,
		getPageData: Function
	},
	data: function () {
		return {
			// API 用參數
			cusCode: null,
			cusName: null,
			graCode: '',
			selectCusCodes: [],
			queryCusCodes: [],
			// 下拉選單
			aumMenu: [],
			// 畫面顯示用參數
			cusGroups: [],
			// 主要顯示資料
			queried: new Map(),
			queriedUpdate: 0
		};
	},
	computed: {
		selectCus: function () {
			/** @type {Map} */
			const queried = this.queried;
			return Array.from(queried.values()).filter(each => each.selected);
		},
		queryCus: function () {
			/** @type {Map} */
			const queried = this.queried;
			return Array.from(queried.values()).filter(each => !each.selected);
		}
	},
	mounted: function () {
		const self = this;
		if (!_.isBlank(self.groupCode)) {
			self.getCusGroupDetail();
		}
		self.getAumMenu();
	},
	methods: {
		getAumMenu: async function () {
			const self = this;
			const ret = await self.$api.getCusGradesApi();
			self.aumMenu = ret.data;
		},
		queryByCusCode: function () {
			const self = this;
			const queryReq = { cusCode: self.cusCode };
			self.singleQuery(queryReq);
		},
		queryByCusName: function () {
			const self = this;
			if (_.size(self.cusName) < 2) {
				self.$bi.alert(self.$t('cus.customerNameQueryMinTwoChars'));
				return;
			}
			else {
				const queryReq = { cusName: self.cusName };
				self.singleQuery(queryReq);
			}
		},
		queryByGraCode: function () {
			const self = this;
			const queryReq = { graCode: self.graCode };
			self.singleQuery(queryReq);
		},
		singleQuery: async function (queryReq) {
			const self = this;
			const ret = await self.$api.getSrchCusGroupApi({
				cusCode: queryReq.cusCode,
				cusName: queryReq.cusName,
				graCode: queryReq.graCode
			});
			if (ret.data.length == 0) {
				self.queryCus.forEach((each) => {
					self.queried.delete(each.cusCode);
				});
				self.$bi.alert(self.$t('cus.noCustomerFound'));
			}
			else {
				/** @type {Map} */
				const queried = self.queried;
				ret.data.forEach((each) => {
					if (queried.has(each.cusCode)) {
						Object.assign(queried.get(each.cusCode), each);
						return;
					}
					queried.set(each.cusCode, self.extendSrchCusGroupResp(each));
				});
				const cusCodes = ret.data.map(each => each.cusCode);
				Array.from(queried.keys()).forEach((key) => {
					if (!cusCodes.includes(key)) {
						queried.delete(key);
					}
				});
			}
			self.queriedUpdate++;
		},
		next: function () {
			const self = this;
			self.$router.push('/cus/favCusSetup');
		},
		addCus: function () {
			const self = this;
			self.queryCusCodes.forEach(function (queryCusCode) {
				self.queried.get(queryCusCode).selected = true;
			});
			self.queriedUpdate++;
		},
		removeCus: function () {
			const self = this;
			self.selectCusCodes.forEach(function (selectCusCode) {
				self.queried.get(selectCusCode).selected = false;
			});
			self.queriedUpdate++;
		},
		extendSrchCusGroupResp(srchCusGroupResp = { cusCode: '', cusName: '', idnEntityType: '' }) {
			const self = this;
			return {
				...srchCusGroupResp,
				selected: false,
				get maskCusCode() {
					if (this.idnEntityType === 'N') return self.$filters.maskCusInfo(this.cusCode, 4, 3);
					if (this.idnEntityType === 'L') return self.$filters.maskCusInfo(this.cusCode, 3, 3);
					return this.cusCode;
				},
				get maskCusName() {
					if (this.idnEntityType === 'N') return self.$filters.maskCusInfo(this.cusName, -1, 1);
					return this.cusName;
				}
			};
		},
		patchCusGroupDetail: async function () {
			const self = this;
			let groupDetail = [];
			groupDetail = _.map(self.selectCus, obj => _.pick(obj, 'cusCode'));
			await self.$api.patchCusGroupDetailApi({
				groupCode: self.groupCode,
				groupName: self.groupName,
				groupDetail
			});
			self.$bi.alert(self.$t('cus.updateSuccess'));
			self.getPageData(0);
			self.homePage();
		},
		getCusGroupDetail: async function () {
			const self = this;
			const ret = await self.$api.getCusGroupDetailApi({
				groupCode: self.groupCode
			});
			if (ret.data) {
				ret.data.groupDetail.forEach((each) => {
					const extended = self.extendSrchCusGroupResp(each);
					extended.selected = true;
					self.queried.set(each.cusCode, extended);
				});
				self.queriedUpdate++;
			}
		}
	}
};
</script>
