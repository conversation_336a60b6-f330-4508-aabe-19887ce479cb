<template>
	<div>
		<div class="row">
			<div class="col-12">
				<div role="tabpanel" class="tab-pane fade show active">
					<div class="card card-form-collapse">
						<div class="card-header" data-bs-toggle="collapse" data-bs-target="#card-body">
							<h4>{{ $t('cus.pleaseEnterFollowingData') }}</h4>
						</div>
						<vue-form v-slot="{ errors }" ref="queryForm">
							<div id="card-body" class="card-body collapse show">
								<div class="row g-3 align-items-end">
									<div class="form-group col-md-4">
										<label class="form-label">{{ $t('cus.customGroupName') }}</label>
										<vue-field
											v-model="groupName"
											type="text"
											class="form-control"
											name="groupName"
											:label="$t('cus.customGroupName')"
											:readonly="tabCode == 2"
										/>
									</div>
									<div class="form-footer">
										<button
											v-if="tabCode == 1"
											class="btn btn-primary btn-search"
											type="button"
											@click.prevent="getPageData(0)"
										>
											{{ $t('cus.search') }}
										</button>
										<button
											v-if="tabCode == 1"
											class="btn btn-primary btn-sava"
											type="button"
											@click="doCreate()"
										>
											{{ $t('cus.add') }}
										</button>
									</div>
								</div>
							</div>
						</vue-form>
					</div>
					<div v-if="tabCode == 1" class="card card-table">
						<div class="card-header">
							<h4>{{ $t('cus.keyCustomerGroup') }}</h4>
							<vue-pagination :pageable="pageData" :goto-page="getPageData" />
						</div>
						<div class="table-responsive">
							<table class="table table-RWD table-hover">
								<thead>
									<tr>
										<th>{{ $t('cus.customGroupName') }}</th>
										<th>{{ $t('cus.execute') }}</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in pageDataContent" :key="item.groupCode">
										<td :data-th="$t('cus.customGroupName')">
											<vue-field
												v-model="item.groupNameNext"
												type="text"
												:name="`group${item.groupCode}`"
												class="td-editable-input"
												:class="{ 'tx-red': item.isChanged }"
											/>
										</td>
										<td>
											<button
												v-if="item.isChanged"
												type="button"
												class="btn btn-info btn-glow btn-icon btn-edit"
												data-bs-toggle="tooltip"
												:data-bs-original-title="$t('cus.confirm')"
												@click="item.changeRemote()"
											>
												<i class="bi bi-check" />
											</button>
											<button
												v-if="item.isChanged"
												type="button"
												class="btn btn-danger btn-glow btn-icon"
												data-bs-toggle="tooltip"
												:data-bs-original-title="$t('cus.cancel')"
												@click="item.cancelChange()"
											>
												<i class="bi bi-x" />
											</button>
											<button
												v-if="!item.isChanged"
												type="button"
												class="btn btn-info btn-glow btn-icon btn-edit"
												data-bs-toggle="tooltip"
												:data-bs-original-title="$t('cus.edit')"
												@click="item.editDetail()"
											>
												<i class="bi bi-pen" />
											</button>
											<button
												v-if="!item.isChanged"
												type="button"
												class="btn btn-danger btn-glow btn-icon"
												data-bs-toggle="tooltip"
												:data-bs-original-title="$t('cus.delete')"
												@click="item.destroyRemote()"
											>
												<i class="bi bi-trash" />
											</button>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
					<vue-fav-cus-setup
						v-if="tabCode == 2"
						:title="$t('cus.modify')"
						:home-page="homePage"
						:get-page-data="getPageData"
						:group-name="groupName"
						:group-code="groupCode"
					/>
					<div class="text-end mt-3">
						<button class="btn btn-primary" type="button" @click="onBackward">
							{{ $t('cus.goBack') }}
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { Field, Form } from 'vee-validate';
import vuePagination from '@/views/components/pagination.vue';
import vueFavCusSetup from '@/views/cus/CUS0501/include/favCusSetup.vue';
import _ from 'lodash';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vuePagination,
		vueFavCusSetup
	},
	data: function () {
		return {
			customTitle: null,
			groupName: null,
			groupCode: null,

			// 主要顯示資料
			pageData: {},
			pageable: {
				page: 0,
				size: 10,
				sort: 'GROUP_CODE',
				direction: 'DESC'
			},

			// 畫面顯示用參數
			tabCode: 0,
			tabs: [
				{ tabCode: 1, label: null },
				{ tabCode: 2, label: null }
			]
		};
	},
	computed: {
		pageDataContent() {
			return (this.pageData.content || []).filter(each => !each.isDestroyed);
		}
	},
	mounted: function () {
		const self = this;
		// Initialize i18n labels
		self.tabs[0].label = self.$t('cus.customerGroupMaintenance');
		self.tabs[1].label = self.$t('cus.customerGroupMaintenanceModify');

		self.tabCode = self.tabs[0].tabCode;
		self.customTitle = self.tabs[0].label;
		self.getPageData(0);
	},
	methods: {
		changeTab: function (tabCode) {
			const self = this;
			self.tabCode = tabCode;
			for (let i = 0; i < self.tabs.length; i++) {
				const tab = self.tabs[i];
				if (tab.tabCode == tabCode) {
					self.customTitle = tab.label;
				}
			}
		},
		getPageData: async function (page) {
			const self = this;
			const url = _.toPageUrl('', page, self.pageable);
			const ret = await self.$api.getCusGroupListPageApi({
				groupName: self.groupName
			}, url);
			self.pageData = ret.data;
			ret.data.content.forEach(self.extendCusGroup);
		},
		doCreate: async function () {
			const self = this;
			if (_.isBlank(self.groupName)) {
				self.$bi.alert(self.$t('cus.customGroupNameRequired'));
				return;
			}
			const ret = await self.$api.postCusGroupApi({
				groupName: self.groupName
			});
			const isCreated = ret.data == 1;
			self.$bi.alert(isCreated ? self.$t('cus.addGroupSuccess') : self.$t('cus.groupNameAlreadyExists'));
			self.groupName = '';
		},
		homePage: function () {
			const self = this;
			self.changeTab(1);
		},
		extendCusGroup: function (cusGroup = { groupName: '', groupCode: '' }) {
			const vueObj = this;
			cusGroup.isDestroyed = false;
			cusGroup.groupNameNext = cusGroup.groupName || '';
			Object.defineProperty(cusGroup, 'isChanged', {
				get: function () {
					return this.groupName !== this.groupNameNext;
				},
				configurable: true,
				enumerable: true
			});
			cusGroup.changeRemote = async function () {
				const self = this;
				const ret = await self.$api.patchCusGroupApi({
					groupName: this.groupNameNext,
					groupCode: this.groupCode
				});
				const isUpdated = ret.data == 1;
				self.$bi.alert(isUpdated ? self.$t('cus.updateSuccess') : self.$t('cus.groupNameAlreadyExists'));
				if (isUpdated) {
					self.groupName = self.groupNameNext;
				}
				else {
					const temp = vueObj.groupName;
					vueObj.groupName = self.groupNameNext;
					vueObj.getPageData(0).then(function () {
						vueObj.groupName = temp;
					});
				}
			};
			cusGroup.cancelChange = function () {
				this.groupNameNext = this.groupName;
			};
			cusGroup.editDetail = function () {
				vueObj.groupName = this.groupName;
				vueObj.groupCode = this.groupCode;
				vueObj.changeTab(2);
			};
			cusGroup.destroyRemote = async function () {
				const self = this;
				await self.$api.deleteCusGroupApi({
					groupCode: this.groupCode
				});
				self.$bi.alert(self.$t('cus.deleteSuccess'));
				self.isDestroyed = true;
			};
		},
		onBackward() {
			window.history.back();
		}
	}
};
</script>
