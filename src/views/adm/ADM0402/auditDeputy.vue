<template>
	<div class="row">
		<div class="col-12">
			<div class="card card-form">
				<div class="card-body">
					<vue-form v-slot="{ errors }" ref="queryForm" @submit="getDeputiesLog">
						<div class="form-row">
							<div class="form-group col-lg-6 col-12">
								<label class="form-label" style="min-width: 120px !important">{{ $t('adm.queryBranchUnit') }}：</label><br>
								<div class="input-group">
									<select
										id="areaBranCode"
										v-model="groupCode"
										name="areaBranCode"
										class="form-select"
									>
										<option value="">
											{{ $t('adm.all') }}
										</option>
										<option v-for="item in areaList" :value="item.branCode">
											{{ item.branCode }} {{ item.branName }}
										</option>
									</select>
								</div>
							</div>
							<div class="form-group col-lg-6 col-12">
								<select
									id="branCode"
									v-model="branCode"
									name="branCode"
									class="form-select"
								>
									<option value="">
										{{ $t('adm.all') }}
									</option>
									<option v-for="item in branList" :value="item.branCode">
										{{ item.branCode }} {{ item.branName }}
									</option>
								</select>
							</div>
							<div class="form-group col-lg-6 col-12">
								<label class="form-label" style="min-width: 120px !important">{{ $t('adm.deputyDeputedPerson') }}：</label><br>
								<div class="input-group">
									<select
										id="deputyUser"
										v-model="deputyUser"
										name="deputyUser"
										class="form-select"
									>
										<option :value="null">
											{{ $t('adm.pleaseSelect') }}
										</option>
										<option v-for="item in deputies" :value="item.userCode">
											{{ item.userCode }} {{ item.userName }}
										</option>
									</select>
								</div>
							</div>
							<div class="form-group col-lg-6 col-12">
								<label class="form-label tx-require" style="min-width: 120px !important">{{ $t('adm.deputyDateRange') }}：</label><br>
								<div class="input-group">
									<input
										id="beginDate"
										v-model="stdDt"
										type="date"
										name="beginDate"
										value=""
										class="form-control"
									>
									<span class="input-group-text">~</span>
									<input
										id="endDate"
										v-model="endDt"
										type="date"
										name="endDate"
										value=""
										class="form-control"
									>
								</div>
							</div>
						</div>
						<div class="form-footer">
							<button type="submit" class="btn btn-primary btn-search">
								{{ $t('adm.search') }}
							</button>
						</div>
					</vue-form>
				</div>
			</div>
			<div id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('adm.deputySettingRecordList') }}</h4>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-hover text-center">
							<thead>
								<tr>
									<th>{{ $t('adm.modifyDate') }}</th>
									<th>{{ $t('adm.modifyPerson') }}</th>
									<th>{{ $t('adm.modifyType') }}</th>
									<th>
										{{ $t('adm.deputedPerson') }} <br>
										({{ $t('adm.leavePersonnel') }})
									</th>
									<th>{{ $t('adm.deputedPersonRole') }}</th>
									<th>{{ $t('adm.deputyAgent') }}</th>
									<th>{{ $t('adm.deputyAgentRole') }}</th>
									<th>{{ $t('adm.belongingBranch') }}</th>
									<th>{{ $t('adm.deputyStartDate') }}</th>
									<th>{{ $t('adm.deputyEndDate') }}</th>
									<th>{{ $t('adm.deputyDays') }} <br></th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in userDeputiesLog">
									<td>{{ item.createDt }}</td>
									<td>{{ item.createBy }} {{ item.createName }}</td>
									<td>{{ item.codeName }}</td>
									<td>{{ item.userCode }} {{ item.userName }}</td>
									<td>{{ item.roleNames }}</td>
									<td>{{ item.deputyUserCode }} {{ item.deputyUserName }}</td>
									<td>{{ item.deputyRoleNames }}</td>
									<td>{{ item.branName }}</td>
									<td>{{ item.stdDt }}</td>
									<td>{{ item.endDt }}</td>
									<td>{{ $filters.formatAmt(item.totDepDays) }}</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="tx-note">
					{{ $t('adm.deputyDaysCalendarNote') }}
				</div>
			</div>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
import _ from 'lodash';
import Swal from 'sweetalert2';
import { Form } from 'vee-validate';

export default {
	components: {
		'vue-form': Form
	},
	data: function () {
		return {
			stdDt: null,
			endDt: null,
			// 主要顯示資料
			userDeputiesLog: [],
			areaList: [],
			branList: [],
			deputies: [],
			deputyUser: '',
			groupCode: '',
			branCode: ''
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo'])
	},
	watch: {
		groupCode: function (newVal) {
			this.branCode = '';
			this.branList = [];
			if (newVal != null) {
				this.getBranList();
			}
		},
		branCode: function (newVal) {
			this.deputyUser = null;
			this.deputies = [];
			if (newVal != null) {
				this.getDeputies();
			}
		}
	},
	mounted: function () {
		const self = this;
		const now = new Date();
		self.endDt = _.formatDate(now).replaceAll('/', '-');
		self.stdDt = _.formatDate(now.setMonth(now.getMonth() - 1)).replaceAll('/', '-');
		self.getAreaList();
	},
	methods: {
		getDeputiesLog: function () {
			const self = this;

			if (_.isBlank(self.stdDt) || _.isBlank(self.endDt)) {
				Swal.fire({
					icon: 'error',
					text: self.$t('adm.pleaseEnterDateRange'),
					showCloseButton: true,
					confirmButtonText: self.$t('adm.confirm'),
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}
			else if (self.stdDt > self.endDt) {
				Swal.fire({
					icon: 'error',
					text: self.$t('adm.startDateMustBeLessThanEndDate'),
					showCloseButton: true,
					confirmButtonText: self.$t('adm.confirm'),
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			self.$api
				.getDeputiesLogApi({
					parentBranCode: self.groupCode,
					branCode: self.branCode,
					userCode: self.deputyUser,
					stdDt: _.formatDate(self.stdDt),
					endDt: _.formatDate(self.endDt)
				})
				.then(function (resp) {
					self.userDeputiesLog = resp.data;
				});
		},
		getAreaList: function () {
			var self = this;
			if (_.isEqual(self.userInfo.roleType, 'HQ')) {
				this.getAdmBranches(['10', '50']).then(function (resp) {
					self.areaList = resp.data;
				});
			}
			else {
				var self = this;
				self.$api.getMinorAreaApi({}).then(function (ret) {
					if (ret.data) {
						self.areaList = ret.data;
					}
				});
			}
		},
		getBranList: function () {
			var self = this;
			if (_.isEqual(self.userInfo.roleType, 'HQ')) {
				this.getAdmBranches(null, self.groupCode).then(function (resp) {
					self.branList = resp.data;
				});
			}
			else {
				var self = this;
				self.$api
					.getBranchesApi({
						minorCode: self.groupCode
					})
					.then(function (ret) {
						if (ret.data) {
							self.branList = ret.data;
						}
					});
			}
		},
		getAdmBranches: function (branLvlCode = null, parentBranCode = null) {
			const self = this;
			return self.$api.getAdmBranchesApi({
				parentBranCode: parentBranCode,
				branLvlCode: branLvlCode
			});
		},
		getDeputies: function () {
			const self = this;
			self.$api
				.getDeputiesApi({
					branCode: self.branCode
				})
				.then(function (ret) {
					if (ret.data) {
						self.deputies = ret.data;
					}
				});
		}
	}
};
</script>
