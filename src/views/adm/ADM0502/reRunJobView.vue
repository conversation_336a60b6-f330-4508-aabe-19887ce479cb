<template>
	<!--頁面內容 start-->
	<div class="row">
		<div class="col-12">
			<div class="card card-form">
				<div class="card-body">
					<form>
						<div class="row g-3 align-items-end">
							<div class="col-md-3">
								<label class="form-label">{{ $t('adm.groupCode') }}</label>
								<select
									id="awc_group_code"
									v-model="groupId"
									name="awc.group_code"
									class="form-select"
								>
									<option v-for="group in groupMenu" :value="group.id">
										{{ group.groupCode }}
									</option>
								</select>
							</div>

							<div class="col-md-3">
								<button type="button" class="btn btn-primary btn-glow btn-search" @click="addGroup(groupId)">
									{{ $t('adm.join') }}
								</button>
							</div>
						</div>
					</form>
				</div>
			</div>
			<div class="card card-table">
				<div class="card-header">
					<h4>{{ $t('adm.batchReRunList') }}</h4>
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-hover text-center">
						<thead>
							<tr>
								<th>{{ $t('adm.seqNo') }}</th>
								<th>{{ $t('adm.batchItem') }}</th>
								<th>{{ $t('adm.lastRunTime') }}</th>
								<th>{{ $t('adm.runResult') }}</th>
								<th>{{ $t('adm.execute') }}</th>
							</tr>
						</thead>

						<tbody>
							<tr v-for="(item, index) in reRunInfo">
								<td :data-th="$t('adm.seqNo')">
									{{ index + 1 }}
								</td>
								<td :data-th="$t('adm.batchItem')">
									{{ item.groupName }}
								</td>
								<td :data-th="$t('adm.lastRunTime')">
									{{ item.createDt }}
								</td>
								<td :data-th="$t('adm.runResult')">
									{{ item.statusName }}
								</td>
								<td :data-th="$t('adm.execute')" class="p-2 text-nowrap" style="width: 1%; white-space: nowrap;">
									<div class="d-inline-flex gap-1">
										<button
											type="button"
											class="btn btn-info btn-sm"
											:disabled="item.status == 'R'"
											@click="reRun(item.groupCode, 'E')"
										>
											{{ $t('adm.errorRerun') }}
										</button>
										<button
											type="button"
											class="btn btn-primary btn-sm"
											:disabled="item.status == 'R'"
											@click="reRun(item.groupCode, 'T')"
										>
											{{ $t('adm.fullRerun') }}
										</button>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
	<!--頁面內容 end-->
</template>

<script>
import { _ } from 'lodash';
import moment from 'moment';

export default {
	components: {
		dynamicTitle
	},
	data: function () {
		return {
			// API 參數
			statusCode: '', // 結果代碼
			groupCode: '', // 群組代碼
			inputDate: null,
			hasDateError: false,
			today: moment().format('YYYY-MM-DD'),
			startDate: null,
			endDate: null,

			// 查詢下拉選單
			batchCtrlStatusList: [], // 結果代碼列表
			batchGroupList: [], // 群組代碼列表
			pageData: {
				// 批次狀態監控列表
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'DATA_DT',
				direction: 'DESC'
			}
		};
	},
	mounted: function () {
		const self = this;
		self.getBatchCtrlStatusList();
		self.getBatchGroupList();
	},
	methods: {
		validateInputDate() {
			this.hasDateError = false;

			const regex = /^\d{4}[\/-]\d{2}[\/-]\d{2}$/;
			if (!regex.test(this.inputDate) || !this.inputDate) {
				self.$bi.alert(self.$t('adm.pleaseEnterCompleteDateFormat'));
				this.hasDateError = true;
				this.inputDate = '';

				return false;
			}

			const normalized = this.inputDate.replace(/-/g, '/');
			const [yyyy, mm, dd] = normalized.split('/');
			const date = new Date(`${yyyy}-${mm}-${dd}`);

			if (isNaN(date.getTime())) {
				self.$bi.alert(self.$t('adm.invalidDate'));
				this.hasDateError = true;
				this.inputDate = '';
				return false;
			}

			const minDate = new Date('1753-01-01');
			const maxDate = new Date();
			if (date < minDate || date > maxDate) {
				self.$bi.alert(`${self.$t('adm.dateMustBeBetween')} ${maxDate.toISOString().slice(0, 10).replace(/-/g, '/')}`);
				this.hasDateError = true;
				this.inputDate = '';
				return false;
			}

			return true;
		},
		// 取得結果代碼下拉選單
		getBatchCtrlStatusList: async function () {
			const self = this;
			self.batchCtrlStatusList = await self.$api.getAdmCodeDetail({ codeType: 'BATCH_CONTROL_STATUS' });
		},
		// 取得群組代碼下拉選單
		getBatchGroupList: function () {
			const self = this;

			self.$api.getAdmWmsBatchGroups().then(function (ret) {
				self.batchGroupList = ret.data;
			});
		},
		// 取得批次狀態監控列表
		gotoPage: function (page) {
			const self = this;
			self.pageable.page = page;
			self.getPageData(page);
		},
		getPageData: function (page) {
			if (!this.validateInputDate()) {
				return; // $t('adm.validationFailed')
			}
			const self = this;
			const momentDate = self.inputDate ? moment(self.inputDate, ['YYYY/MM/DD', 'YYYY-MM-DD']) : moment();
			var page = _.toPageUrl('', page, self.pageable);

			const data = {
				batchStartDate: null,
				batchEndDate: null,
				startDate: momentDate.clone().subtract(1, 'days').format('YYYY-MM-DD') + ' 00:00:00',
				endDate: momentDate.format('YYYY-MM-DD') + ' 00:00:00',
				groupCode: self.groupCode,
				objectName: self.objectName,
				status: self.statusCode
			};

			self.$api.getBatchJobStatus(data, page).then(function (ret) {
				self.pageData = ret.data;
				_.forEach(self.pageData.content, function (item) {
					item.option = '';
				});
			});
		},
		// 更新批次 Job 狀態
		updateJobStatus: function (groupCode, objectName, id, option) {
			if (_.isEmpty(option)) {
				return;
			}
			const self = this;
			const data = {
				groupCode: groupCode,
				objectName: objectName,
				id: id,
				status: option
			};

			self.$api.patchBatchJobStatus(data).then(function (ret) {
				self.$bi.alert(self.$t('adm.executeSuccess'));
				self.gotoPage(self.pageable.page);
			});
		}
	}

};
</script>

<style>
.wordBreak {
  word-break: break-all;
}
</style>
