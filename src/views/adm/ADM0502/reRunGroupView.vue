<template>
	<!--頁面內容 start-->
	<div class="row">
		<div class="col-12">
			<div class="card card-form">
				<div class="card-body">
					<form>
						<div class="row g-3 align-items-end">
							<div class="col-md-3">
								<label class="form-label">{{ $t('adm.groupCode') }}</label>
								<select
									id="awc_group_code"
									v-model="groupId"
									name="awc.group_code"
									class="form-select"
								>
									<option v-for="group in groupMenu" :value="group.id">
										{{ group.groupCode }}
									</option>
								</select>
							</div>

							<div class="col-md-3">
								<button type="button" class="btn btn-primary btn-glow btn-search" @click="addGroup(groupId)">
									{{ $t('adm.join') }}
								</button>
							</div>
						</div>
					</form>
				</div>
			</div>
			<div class="card card-table">
				<div class="card-header">
					<h4>{{ $t('adm.batchReRunList') }}</h4>
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-hover text-center">
						<thead>
							<tr>
								<th>{{ $t('adm.seqNo') }}</th>
								<th>{{ $t('adm.batchItem') }}</th>
								<th>{{ $t('adm.lastRunTime') }}</th>
								<th>{{ $t('adm.runResult') }}</th>
								<th>{{ $t('adm.execute') }}</th>
							</tr>
						</thead>

						<tbody>
							<tr v-for="(item, index) in reRunInfo">
								<td :data-th="$t('adm.seqNo')">
									{{ index + 1 }}
								</td>
								<td :data-th="$t('adm.batchItem')">
									{{ item.groupName }}
								</td>
								<td :data-th="$t('adm.lastRunTime')">
									{{ item.createDt }}
								</td>
								<td :data-th="$t('adm.runResult')">
									{{ item.statusName }}
								</td>
								<td :data-th="$t('adm.execute')" class="p-2 text-nowrap" style="width: 1%; white-space: nowrap;">
									<div class="d-inline-flex gap-1">
										<button
											type="button"
											class="btn btn-info btn-sm"
											:disabled="item.status == 'R'"
											@click="reRun(item.groupCode, 'E')"
										>
											{{ $t('adm.errorRerun') }}
										</button>
										<button
											type="button"
											class="btn btn-primary btn-sm"
											:disabled="item.status == 'R'"
											@click="reRun(item.groupCode, 'T')"
										>
											{{ $t('adm.fullRerun') }}
										</button>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
	<!--頁面內容 end-->
</template>

<script>
export default {
	data: function () {
		return {
			reRunInfo: [],
			groupId: '--',
			groupMenu: []
		};
	},
	mounted: function () {
		const self = this;
		self.getReRunInfo();
		self.getGroupMenu();
	},
	methods: {
		// 取得批次重跑列表
		getReRunInfo: function () {
			const self = this;

			self.$api.getBatchGroupControl().then(function (ret) {
				self.reRunInfo = ret.data;
			});
		},
		// 重跑批次
		reRun: async function (groupCode, reRunType) {
			const self = this;

			const result = await self.$bi.confirm(self.$t('adm.confirmRerun'));
			if (result.isConfirmed) {
				self.$api.patchBatchGroupControl({
					groupCode: groupCode,
					reRunType: reRunType
				}).then(function (ret) {
					$.bi.alert(self.$t('adm.executeSuccess'));
					self.getReRunInfo();
				});
			}
		},
		getGroupMenu: function () {
			const self = this;

			self.$api.getAdmWmsBatchGrptimeCfg().then(function (ret) {
				self.groupMenu = ret.data;
			});
		},
		addGroup: function (id) {
			const self = this;

			self.$api.getInsertBatchGrpControl({ id: id }).then(function (ret) {
				if (ret.data !== 0) {
					self.$bi.alert(self.$t('adm.addSuccess', { count: ret.data }));
					self.getReRunInfo();
				}
				else {
					self.$bi.alert(self.$t('adm.addFailed'));
				}
			});
		}
	}

};
</script>

<style>
.wordBreak {
  word-break: break-all;
}
</style>
