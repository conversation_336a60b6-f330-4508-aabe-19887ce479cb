<template>
	<div class="row">
		<div class="col-12">
			<vue-bi-tabs
				ref="tab"
				:menu-code="'M00-06'"
				:tab-name-decorator="showNameDecorator"
				@change-tab="changeTab"
			>
				<template #default="{ id }">
					<component :is="id" />
				</template>
			</vue-bi-tabs>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
import vueFcStepFpUpload from './include/fcStepFpUpload.vue';
import vueFcStepFpSearch from './include/fcStepFpSearch.vue';
import vueBiTabs from '@/views/components/biTabs.vue';

export default {
	components: {
		vueFcStepFpUpload,
		vueFcStepFpSearch,
		vueBiTabs
	},
	data: function () {
		return {
			// 畫面顯示用參數
			customTitle: null,
			// 畫面邏輯判斷用參數
			tabCode: 0,
			tabs: [
				{ tabCode: 1, label: this.$t('adm.regionalStaffSetting') },
				{ tabCode: 2, label: this.$t('adm.regionalBranchQuery') }
			],
			tabCodeTitleMap: {
				'M00-060': this.$t('adm.regionalStaffSetting'), // 區域人員設定
				'M00-061': this.$t('adm.regionalBranchQuery') // 區域分區查詢
			}
		};
	},
	mounted: function () {
		const self = this;
		self.tabCode = self.tabs[0].tabCode;
		self.customTitle = self.tabs[0].label;
	},
	methods: {
		changeTab: function (tabCode) {
			const self = this;
			self.customTitle = self.tabCodeTitleMap[tabCode];
		}
	}
};
</script>
