<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form mb-3">
			<div class="card-header">
				<h4>{{ $t('adm.regionSearch') }}</h4>
				<span class="tx-square-bracket">{{ $t('adm.reqField') }}</span>
			</div>
			<div class="card-body">
				<div class="row g-3 align-items-end">
					<table class="table table-RWD table-horizontal-RWD table-bordered">
						<tbody>
							<tr>
								<th class="wd-15p tx-require">
									{{ $t('adm.branchType') }}
								</th>
								<td class="wd-85p">
									<select v-model="roleCode" name="branCode" class="form-select">
										<option v-for="(item, index) in admRoleList" :value="item.roleCode">
											{{ item.roleName }}
										</option>
									</select>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div class="form-footer d-flex justify-content-end gap-2">
			<button class="btn btn-primary btn-glow btn-search" @click.prevent="getPageData(0)">
				{{ $t('adm.search') }}
			</button>
			<button class="btn btn-primary btn-glow" @click="exportPageData()">
				{{ $t('adm.downloadExcel') }}
			</button>
		</div>

		<div class="col-12">
			<div id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('adm.uploadListResult') }}</h4>
						<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
					</div>
					<template v-if="pageData.totalElements > 0">
						<div class="table-responsive">
							<table class="table table-RWD table-bordered text-center">
								<thead>
									<tr>
										<th class="text-center">
											{{ $t('adm.itemNo') }}
										</th>
										<th class="text-center">
											{{ $t('adm.branchCode') }}
										</th>
										<th class="text-center">
											{{ $t('adm.branchName') }}
										</th>
										<th class="text-center">
											{{ $t('adm.employeeNo') }}
										</th>
										<th class="text-center">
											{{ $t('adm.employeeName') }}
										</th>
										<th class="text-center">
											{{ $t('adm.uploaderStaff') }}
										</th>
										<th class="text-center">
											{{ $t('adm.modifyTime') }}
										</th>
									</tr>
								</thead>
								<tbody id="TopTenList">
									<tr v-for="(item, i) in pageData.content">
										<td :data-th="$t('adm.itemNo')">
											{{ item.orderBy }}
										</td>
										<td :data-th="$t('adm.branchCode')">
											{{ item.branCode }}
										</td>
										<td :data-th="$t('adm.branchName')">
											{{ item.branName }}
										</td>
										<td :data-th="$t('adm.employeeNo')">
											{{ item.userCode }}
										</td>
										<td :data-th="$t('adm.employeeName')">
											{{ item.userName }}
										</td>
										<td :data-th="$t('adm.uploaderStaff')">
											{{ item.createBy }} {{ item.createUserName }}
										</td>
										<td :data-th="$t('adm.modifyTime')">
											{{ formatDt(item.createDt) }}
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</template>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { Field, Form } from 'vee-validate';
import VuePagination from '@/views/components/pagination.vue';
import _ from 'lodash';
import moment from 'moment';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		VuePagination
	},
	data: function () {
		return {
			admRoleList: [], // 下拉選單
			roleCode: null,
			pageData: {},
			pageable: {
				page: 0,
				size: 20,
				sort: 'ORDER_BY',
				direction: 'ASC'
			}
		};
	},
	mounted: function () {
		const self = this;
		self.getAdmRoleByRoleType();
	},
	methods: {
		getAdmRoleByRoleType: async function () {
			const self = this;
			const ret = await self.$api.getAdmRoleByRoleTypeApi({
				roleType: 'FC'
			});
			self.admRoleList = ret.data;
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (_page) {
			const self = this;

			if (_.isNil(self.roleCode)) {
				this.$bi.alert(this.$t('adm.selectBranchTypeMsg'));
				return;
			}

			const page = _.isNumber(_page) ? _page : self.pageable.page;
			let url = '';
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=BRAN_CODE';
			const ret = await self.$api.getFcBranMapData(
				{
					roleCode: self.roleCode
				},
				url
			);
			self.pageData = ret.data;
		},
		exportPageData: async function () {
			const self = this;
			const dateStr = moment().format('YYYYMMDD');
			const fileName = self.$t('adm.regionSearchExport') + dateStr + '.xls';
			const ret = await self.$api.getFcBranExportPageData({
				roleCode: self.roleCode
			});
			const blob = new Blob([ret]);
			const link = document.createElement('a');
			const url = window.URL.createObjectURL(blob);
			link.download = fileName;
			link.href = url;
			document.body.appendChild(link);
			link.click();
			link.remove();
			setTimeout(() => window.URL.revokeObjectURL(url), 500);
		},
		formatDt: function (value) {
			return moment(value).format('YYYY/MM/DD HH:mm:ss');
		}
	}
};
</script>
