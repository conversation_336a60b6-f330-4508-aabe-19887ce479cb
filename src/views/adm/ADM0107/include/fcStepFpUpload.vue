<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form mb-3">
			<div class="card-header">
				<h4>{{ $t("adm.regionStaffSetup") }}</h4>
				<span class="tx-square-bracket">{{ $t("adm.reqField") }}</span>
			</div>
			<div class="card-body">
				<div class="row g-3 align-items-end">
					<table class="table table-RWD table-horizontal-RWD table-bordered">
						<tbody>
							<tr>
								<th class="wd-15p tx-require">
									{{ $t("adm.branchType") }}
								</th>
								<td class="wd-85p">
									<select
										v-model="roleCode"
										name="roleCode"
										class="form-select"
									>
										<option
											v-for="(item, index) in admRoleList"
											:value="item.roleCode"
										>
											{{ item.roleName }}
										</option>
									</select>
								</td>
							</tr>
							<tr>
								<th class="tx-require">
									{{ $t("adm.uploadFile") }}
								</th>
								<td>
									<div class="input-group">
										<form>
											<input
												ref="fileInput"
												name="file"
												type="file"
												class="form-control"
												accept=".xlsx,.xls,.csv"
												@change="addFile"
											>
										</form>
										<span class="input-group-text">{{
											$t("adm.csvFileNote")
										}}</span>
										<a
											class="link-underline"
											:href="csvFileUrl"
											download="區域人員設定名單範例檔.csv"
										>{{ $t("adm.sampleFile") }}</a>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div
			style="display: flex; align-items: center; justify-content: space-between"
		>
			<div class="tx-note" style="margin-top: 0; margin-left: 0px">
				{{ $t("adm.uploadNote") }}
			</div>
			<div class="text-end">
				<button class="btn btn-primary" type="button" @click="postFcBranMapChk">
					{{ $t("adm.upload") }}
				</button>
			</div>
		</div>
		<div v-if="uploadResult.length > 0" class="card card-table">
			<div class="card-header">
				<h4>{{ $t("adm.uploadResultList") }}</h4>
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-hover table-bordered text-center">
					<thead>
						<tr>
							<th>{{ $t("adm.fileName") }}</th>
							<th>{{ $t("adm.uploadResult") }}</th>
							<th style="display: flex; align-items: center; justify-content: center">
								<div class="tx-note">
									{{ $t('adm.withoutHeaderNote') }}
								</div>
								<span style="margin-left: 20px">{{ $t('adm.uploadCount') }}</span>
							</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="item in uploadResult">
							<td :data-th="$t('adm.fileName')">
								{{ item.fileName }}
							</td>
							<td :data-th="$t('adm.uploadResult')">
								<div v-for="resp in item.fcBranMapChkResp">
									{{ resp.statusMsg }}
								</div>
							</td>
							<td :data-th="$t('adm.uploadCount')">
								{{ item.totalCount }}
							</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div class="text-end mt-3">
				<button
					v-if="isSendBtn"
					class="btn btn-primary btn-glow"
					@click="postFcBranMap()"
				>
					{{ $t("adm.complete") }}
				</button>
			</div>
		</div>
	</div>
</template>
<script>
import { Field, Form } from 'vee-validate';
import _ from 'lodash';
import csvFileUrl from '/resources/adm/region_personnel_sample.csv?url';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			admRoleList: [], // 下拉選單
			roleCode: null,
			uploadResult: [], // 上傳名單list
			fileObject: null,
			validExts: ['csv', 'xlsx'],
			symbols: ['/', ':', '*', '?', '"', '<', '>', '|'],
			isSendBtn: false,
			csvFileUrl
		};
	},
	mounted: function () {
		const self = this;
		self.getAdmRoleByRoleType();
	},
	methods: {
		getAdmRoleByRoleType: function () {
			const self = this;
			self.$api
				.getAdmRoleByRoleTypeApi({ roleType: 'FC' })
				.then(function (ret) {
					self.admRoleList = ret.data;
				});
		},
		postFcBranMapChk: function () {
			const self = this;

			if (self.roleCode == null) {
				this.$bi.alert(this.$t('adm.selectBranchType'));
				return;
			}
			if (self.fileObject == '' || self.fileObject == null) {
				this.$bi.alert(this.$t('adm.selectUploadFile'));
				return;
			}
			if (
				self.fileObject.type !== 'text/csv'
				&& !self.fileObject.name.endsWith('.csv')
			) {
				this.$bi.alert(this.$t('adm.notCsvFile'));
				return;
			}

			const formData = new FormData();
			formData.append('fileObject', self.fileObject);
			formData.append('uploadRoleMetadata', self.roleCode);
			self.$api.postFcBranMapChkApi(formData).then(function (ret) {
				if (ret.data && ret.data[0].totalCount !== 0) {
					self.uploadResult.push({
						fileName: self.fileObject.name,
						fcBranMapChkResp: ret.data,
						totalCount: ret.data[0].totalCount
					});
					self.isSendBtn = !_.some(ret.data, bean => bean.statusCode === 'F');
					self.roleCode = null;
					self.fileObject = null;
					self.$refs.fileInput.value = ''; // 清除文件選擇
				}
			});
		},
		addFile: function (e) {
			const self = this;
			const file = e.target.files[0];

			self.fileObject = file;
		},
		postFcBranMap: function () {
			const self = this;
			self.$api.postFcBranMapApi().then(function (ret) {
				self.$bi.alert(self.$t('adm.saveSuccess'));
				setTimeout(function () {
					window.location.reload();
				}, 2000);
			});
		}
	}
};
</script>
