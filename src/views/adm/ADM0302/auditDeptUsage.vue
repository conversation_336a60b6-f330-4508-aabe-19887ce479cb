<template>
	<div class="row">
		<div class="col-12">
			<div class="card card-form">
				<div class="card-body">
					<div class="row g-3 align-items-end">
						<div class="col-md-3">
							<label class="form-label">{{ $t('adm.queryBranchUnit') }}</label>
							<select
								id="sltBran"
								v-model="branCode"
								name="branCodes"
								class="form-select"
								data-inline="true"
							>
								<option value="">
									{{ $t('adm.all') }}
								</option>
								<option v-for="branInfo in branMenu" :value="branInfo.branCode">
									{{ branInfo.branCode }} {{ branInfo.branName }}
								</option>
							</select>
						</div>
						<div class="col-md-6">
							<label class="form-label">{{ $t('adm.queryDateRange') }}</label>
							<div class="input-group">
								<vue-field
									id="beginDate"
									v-model="logStartDt"
									type="date"
									name="beginDate"
									size="13"
									:label="$t('adm.queryStartDate')"
									class="form-control"
									maxlength="10"
									:class="{ 'is-invalid': showErrors && errors.logStartDt }"
								/>

								<span class="input-group-text">~</span>

								<vue-field
									id="endDate"
									v-model="logEndDt"
									type="date"
									name="endDate"
									size="13"
									:label="$t('adm.queryEndDate')"
									class="form-control"
									maxlength="10"
									:min="minValidEndDt"
									:class="{ 'is-invalid': showErrors && errors.logEndDt }"
								/>
							</div>
						</div>
						<div class="col-lg-8 col-md-10">
							<label class="form-label">{{ $t('adm.functionModule') }}</label>
							<div class="row g-3">
								<div class="col-md-6">
									<div class="input-group">
										<span class="input-group-text">{{ $t('adm.systemModule') }}</span>
										<select
											id="menuListId"
											v-model="moduleMenuCode"
											name="moduleMenuCode"
											class="form-select"
											@change="getBranchFunctionMenu()"
										>
											<option value="">
												{{ $t('adm.all') }}
											</option>
											<option v-for="module in moduleMenu" :value="module.menuCode">
												{{ module.menuName }}
											</option>
										</select>
									</div>
								</div>
								<div class="col-md-6">
									<div class="input-group">
										<span class="input-group-text">{{ $t('adm.functionItem') }}</span>
										<select
											id="progListId"
											v-model="functionMenuCode"
											name="menuCode"
											class="form-select"
										>
											<option value="">
												{{ $t('adm.all') }}
											</option>
											<option v-for="branchFunction in branchFunctionMenu" :value="branchFunction.menuCode">
												{{ branchFunction.menuName }}
											</option>
										</select>
									</div>
								</div>
							</div>
						</div>
						<div class="col-md-2">
							<button role="button" class="btn btn-primary btn-glow btn-searc=h" @click.prevent="gotoPage(0)">
								{{ $t('adm.search') }}
							</button>
						</div>
					</div>
				</div>
			</div>
			<div id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('adm.branchUsageRecordList') }}</h4>
						<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-hover text-center">
							<thead>
								<tr>
									<th>{{ $t('adm.date') }}</th>
									<th>{{ $t('adm.systemModule') }}</th>
									<th class="text-start">
										{{ $t('adm.functionItem') }}
									</th>
									<th>{{ $t('adm.branchUnitCode') }}</th>
									<th>{{ $t('adm.branchUnitName') }}</th>
									<th>{{ $t('adm.times') }}</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in pageData.content">
									<td :data-th="$t('adm.date')">
										{{ item.logDt }}
									</td>
									<td :data-th="$t('adm.systemModule')">
										{{ item.menuOne }}
									</td>
									<td :data-th="$t('adm.functionItem')" class="text-start">
										{{ item.menuTwo }}
									</td>
									<td :data-th="$t('adm.branchUnitCode')">
										{{ item.branCode }}
									</td>
									<td :data-th="$t('adm.branchUnitName')">
										{{ item.branName }}
									</td>
									<td :data-th="$t('adm.times')">
										{{ item.tcnt }}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
import _ from 'lodash';
import { Field } from 'vee-validate';
import pagination from '@/views/components/pagination.vue';

export default {
	components: {
		'vue-field': Field,
		'vue-pagination': pagination
	},
	data: function () {
		return {
			// API 用參數
			buCode: null,
			branCode: '',
			moduleMenuCode: '',
			functionMenuCode: '',
			userCode: null,
			logStartDt: null,
			logEndDt: null,
			// 下拉選單
			branMenu: [],
			moduleMenu: [],
			branchFunctionMenu: [],
			// 主要顯示資料
			pageData: {
				type: Object,
				data: {
					content: [],
					number: 0,
					totalPages: 0,
					totalElements: 0,
					numberOfElements: 0
				}
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'bran_code',
				direction: 'ASC'
			}
		};
	},
	computed: {
		minValidEndDt() {
			return this.logStartDt ? this.logStartDt : null;
		}
	},
	watch: {
		logStartDt: function (newVal, oldVal) {
			this.showErrors = false;
			if (newVal && this.logEndDt && newVal > this.logEndDt) {
				this.logEndDt = null;
			}
		}
	},
	mounted: function () {
		const self = this;
		self.getBranMenu();
		self.getModuleMenu();
		self.getBranchFunctionMenu();
	},
	methods: {
		getBranMenu: function () {
			const self = this;
			self.$api.getAllBranchesMenuApi().then(function (ret) {
				self.branMenu = ret.data;
			});
		},
		getModuleMenu: function () {
			const self = this;
			self.$api.getModuleMenuApi().then(function (ret) {
				self.moduleMenu = ret.data;
			});
		},
		getBranchFunctionMenu: function () {
			const self = this;
			if (_.isBlank(self.moduleMenuCode)) {
				return;
			}

			self.$api
				.getBranchFunctionMenuApi({
					menuCode: self.moduleMenuCode
				})
				.then(function (ret) {
					self.branchFunctionMenu = ret.data;
				});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function (page) {
			const self = this;
			const url = _.toPageUrl('', page, self.pageable);

			self.$api
				.getBranAccessCntLogApi(
					{
						branCode: self.branCode,
						moduleMenuCode: self.moduleMenuCode,
						functionMenuCode: self.functionMenuCode,
						logStartDt: _.formatDate(self.logStartDt),
						logEndDt: _.formatDate(self.logEndDt)
					},
					url
				)
				.then(function (ret) {
					self.pageData = ret.data;
				});
		}
	}
};
</script>
