<template>
	<div v-bind="$attrs">
		<!--頁面內容 start-->
		<!-- 查詢條件區塊 -->
		<div class="card card-form-collapse">
			<div class="card-header">
				<h4>{{ $t('adm.searchCond') }}</h4>
				<span class="tx-square-bracket">{{ $t('adm.reqField') }}</span>
			</div>
			<div id="collapseListGroup1" class="collapse show">
				<div class="card-body">
					<VeeForm>
						<div class="row g-3 align-items-end">
							<div class="col-12 col-lg-4">
								<table class="table table-RWD table-horizontal-RWD">
									<tbody>
										<tr>
											<th class="wd-15p tx-require">
												常用句類別
											</th>
											<td class="wd-85p">
												<select
													id="sltCodeType"
													v-model="searchCriteria.type"
													name="reuseType"
													class="form-select"
												>
													<option value="">
														請選擇
													</option>
													<option
														v-for="(item, index) in reuseTypeList"
														:key="index"
														:value="item.codeValue"
													>
														{{ item.codeName }}
													</option>
												</select>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="form-footer">
							<Button
								type="button"
								class="btn btn-primary"
								@click="openAddModal()"
							>
								新增常用句
							</Button>
							<Button
								type="button"
								class="btn btn-primary btn-search"
								@click="queryReuseWords()"
							>
								{{ $t('adm.search') }}
							</Button>
						</div>
					</VeeForm>
				</div>
			</div>
		</div>

		<!-- 目前常用句清單 -->
		<div v-if="showResults" class="searchResult">
			<div class="card card-table">
				<div class="card-header">
					<h4>目前常用句清單</h4>
					<nav aria-label="Page navigation" class="bi-pages">
						<span class="page-current">共{{ reuseWordsList.length }}筆</span>
					</nav>
				</div>
				<div class="table-responsive">
					<table class="bih-table table table-RWD">
						<thead>
							<tr>
								<th>{{ $t('cus.commonPhrases') }}</th>
								<th>{{ $t('adm.execute') }}</th>
							</tr>
						</thead>
						<tbody>
							<tr v-if="reuseWordsList.length === 0">
								<td colspan="2" class="text-center">
									{{ hasSearched ? $t('core.filterNoData') : $t('core.filterThenQuery') }}
								</td>
							</tr>
							<tr v-for="(item) in reuseWordsList" :key="item.wordsId">
								<td :data-th="$t('cus.commonPhrases')">
									<!-- 隱藏的常用句序號 -->
									<input type="hidden" :value="item.wordsId">
									{{ item.words }}
								</td>
								<td :data-th="$t('adm.execute')">
									<Button
										color="info"
										icon
										:title="$t('adm.editTooltip')"
										@click="openEditModal(item)"
									>
										<i class="fa-solid fa-pen" />
									</Button>
									<!-- 刪除按鈕 -->
									<Button
										icon
										color="danger"
										:title="$t('adm.delete')"
										@click="confirmDeleteWord(item)"
									>
										<i class="fa-solid fa-trash" />
									</Button>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div class="tx-note">
				常用句設定功能提供相關人員建立自己的常用句，方便在各功能輸入資料時，快速代入。
			</div>
		</div>
	</div>

	<!-- Modal 新增/編輯常用句 start -->
	<model
		:is-open="isOpenAddModal"
		@close="closeAddModal"
	>
		<template #content="{ close }">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ isEditMode ? '編輯常用句' : '新增常用句' }}
						</h4>
						<Button
							type="button"
							class="btn-close"
							@click="close()"
						/>
					</div>
					<div class="modal-body">
						<div class="card card-form">
							<div class="card-header">
								<h4>請輸入/編輯常用句</h4>
								<div class="tx-note">
									{{ getCharacterLimitNote() }}
								</div>
							</div>
							<table class="biv-table table table-RWD table-horizontal-RWD">
								<tbody>
									<tr>
										<td>
											<textarea
												v-model="currentWord.words"
												class="form-control"
												placeholder="請輸入常用句..."
												:maxlength="getMaxLength()"
												rows="4"
											/>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>

					<div class="modal-footer">
						<div>
							<Button
								class="btn btn-primary"
								:disabled="isSaving"
								@click="saveWord()"
							>
								{{ isSaving ? '儲存中...' : '儲存' }}
							</Button>
							<Button color="white" @click="close()">
								{{ $t('adm.cancel') }}
							</Button>
						</div>
					</div>
				</div>
			</div>
		</template>
	</model>
	<!-- Modal 新增/編輯常用句 End -->

	<!-- Modal 確認刪除 start -->
	<model
		:is-open="isOpenDeleteModal"
		@close="closeDeleteModal"
	>
		<template #content="{ close }">
			<div class="modal-dialog modal-dialog-centered modal-md">
				<div class="modal-content">
					<div class="modal-body pt-5">
						<div class="icon-alert" />
						<p class="tx-20 tx-danger text-center">
							請問是否確認刪除常用句【{{ deleteTarget.words }}】?
						</p>
					</div>
					<div class="modal-footer">
						<button class="btn btn-white" @click="close()">
							取消
						</button>
						<button class="btn btn-primary" @click="executeDelete()">
							確認
						</button>
					</div>
				</div>
			</div>
		</template>
	</model>
	<!-- Modal 確認刪除 End -->
</template>

<script>
import model from '@/views/components/model.vue';
import { Form } from 'vee-validate';
export default {
	name: 'ReuseWords',
	components: {
		model,
		VeeForm: Form
	},
	data() {
		return {
			// 查詢條件
			searchCriteria: {
				type: '' // 使用者選擇的常用句類別
			},

			// 下拉選單數據
			reuseTypeList: [],

			// 查詢結果
			reuseWordsList: [],
			userCode: '', // 使用者代碼
			showResults: false,
			hasSearched: false,

			// Modal 控制
			isOpenAddModal: false,
			isOpenDeleteModal: false,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',

			// 編輯狀態
			isEditMode: false,
			currentWord: {
				id: null,
				words: ''
			},

			// 刪除確認
			deleteTarget: {},

			// 載入狀態
			isLoading: false,
			isSaving: false
		};
	},

	async mounted() {
		// 頁面載入時取得常用句類別
		await this.loadReuseTypes();
		await this.loadAllReuseWords();
	},

	methods: {
		// 取得常用句類別
		async loadReuseTypes() {
			try {
				const response = await this.$api.getAdmCodeDetail({ codeType: 'REUSE_TYPE' });
				if (response && response.status === 200) {
					this.reuseTypeList = response.data || [];
				}
			}
			catch {
				this.$swal.fire({
					title: '載入失敗',
					text: '無法載入常用句類別',
					icon: 'error',
					confirmButtonText: '確認'
				});
			}
		},
		async loadAllReuseWords() {
			try {
				const response = await this.$api.getReuseWordsApi({ type: '' });
				if (response && response.status === 200) {
					this.reuseWordsList = response.data || [];
					this.showResults = true;
					this.hasSearched = true;
				}
			}
			catch {
				this.reuseWordsList = [];
				this.showResults = true;
				this.hasSearched = true;

				this.$swal.fire({
					title: '查詢失敗',
					text: '查詢常用句時發生錯誤',
					icon: 'error',
					confirmButtonText: '確認'
				});
			}
		},
		/**
		 * 查詢常用句
		 * 點選「查詢」按鈕觸發
		 */
		async queryReuseWords() {
			try {
				if (!this.searchCriteria.type) {
					this.$swal.fire({
						title: '請選擇條件',
						text: '請選擇常用句類別',
						icon: 'warning',
						confirmButtonText: '確認'
					});
					return;
				}

				this.isLoading = true;

				const response = await this.$api.getReuseWordsApi({
					type: this.searchCriteria.type
				});

				if (response && response.status === 200) {
					this.reuseWordsList = response.data || [];
					this.showResults = true;
					this.hasSearched = true;
				}
			}
			catch {
				this.reuseWordsList = [];
				this.showResults = true;
				this.hasSearched = true;

				this.$swal.fire({
					title: '查詢失敗',
					text: '查詢常用句時發生錯誤',
					icon: 'error',
					confirmButtonText: '確認'
				});
			}
			finally {
				this.isLoading = false;
			}
		},

		/**
		 * 開啟新增常用句 Modal
		 */
		openAddModal() {
			if (!this.searchCriteria.type) {
				this.$swal.fire({
					title: '請選擇類別',
					text: '請先選擇常用句類別',
					icon: 'warning',
					confirmButtonText: '確認'
				});
				return;
			}

			this.isEditMode = false;
			this.currentWord = {
				words: ''
			};
			this.isOpenAddModal = true;
		},

		/**
		 * 開啟編輯常用句 Modal
		 */
		openEditModal(item) {
			this.isEditMode = true;
			this.currentWord = {
				wordsId: item.wordsId,
				words: item.words
			};
			this.isOpenAddModal = true;
		},

		/**
		 * 關閉新增/編輯 Modal
		 */
		closeAddModal() {
			this.isOpenAddModal = false;
			this.currentWord = {
				id: null,
				words: ''
			};
		},

		/**
		 * 儲存常用句 (新增或編輯)
		 */
		async saveWord() {
			try {
				// 檢核輸入框是否為空
				if (!this.currentWord.words || !this.currentWord.words.trim()) {
					this.$swal.fire({
						title: '輸入錯誤',
						text: '請輸入常用句，不可空白！',
						icon: 'warning',
						confirmButtonText: '確認'
					});
					return;
				}

				// 字數檢核
				const maxLength = this.getMaxLength();
				if (this.currentWord.words.length > maxLength) {
					this.$swal.fire({
						title: '字數超過限制',
						text: `${this.getSelectedTypeName()}類別字數限制為${maxLength}字`,
						icon: 'warning',
						confirmButtonText: '確認'
					});
					return;
				}

				this.isSaving = true;
				let response;

				if (this.isEditMode) {
					//  修改常用句
					response = await this.$api.patchReuseWordsApi({
						wordsId: this.currentWord.wordsId,
						words: this.currentWord.words.trim()
					});
				}
				else {
					//  新增常用句
					response = await this.$api.postReuseWordsApi({
						type: this.searchCriteria.type,
						words: this.currentWord.words.trim()
					});
				}

				if (response && response.status === 200) {
					this.$swal.fire({
						title: this.isEditMode ? '修改成功' : '新增成功',
						text: `常用句已${this.isEditMode ? '修改' : '新增'}`,
						icon: 'success',
						confirmButtonText: '確認'
					});

					this.closeAddModal();

					await this.loadAllReuseWords();
				}
			}
			catch {
				this.$swal.fire({
					title: '儲存失敗',
					text: '儲存時發生錯誤，請稍後再試',
					icon: 'error',
					confirmButtonText: '確認'
				});
			}
			finally {
				this.isSaving = false;
			}
		},

		/**
		 * 確認刪除常用句
		 */
		confirmDeleteWord(item) {
			this.deleteTarget = {
				wordsId: item.wordsId,
				words: item.words
			};
			this.isOpenDeleteModal = true;
		},

		/**
		 * 關閉刪除確認 Modal
		 */
		closeDeleteModal() {
			this.isOpenDeleteModal = false;
			this.deleteTarget = {};
		},

		/**
		 *  執行刪除常用句
		 */
		async executeDelete() {
			try {
				const response = await this.$api.deleteReuseWordsApi({
					id: this.deleteTarget.wordsId
				});

				if (response && response.status === 200) {
					this.$swal.fire({
						title: '刪除成功',
						text: '常用句已刪除',
						icon: 'success',
						confirmButtonText: '確認'
					});

					this.closeDeleteModal();
					await this.loadAllReuseWords();
				}
			}
			catch {
				this.$swal.fire({
					title: '刪除失敗',
					text: '刪除時發生錯誤，請稍後再試',
					icon: 'error',
					confirmButtonText: '確認'
				});
			}
		},

		/**
		 * 根據選擇的類別取得字數限制
		 */
		getMaxLength() {
			const selectedType = this.getSelectedTypeValue();
			// 簽核常用句：1~20字
			if (selectedType === 'SIGN') {
				return 20;
			}
			// 工作管理、客戶管理：1~200字
			return 200;
		},
		getCharacterLimitNote() {
			const maxLength = this.getMaxLength();
			const typeName = this.getSelectedTypeName();

			if (maxLength === 20) {
				return `${typeName}：可輸入1~20個字`;
			}
			return `${typeName}：可輸入1~200個字`;
		},
		getSelectedTypeName() {
			const selectedType = this.reuseTypeList.find(
				item => item.codeValue === this.searchCriteria.type
			);
			return selectedType ? selectedType.codeName : '所選類別';
		},
		getSelectedTypeValue() {
			return this.searchCriteria.type;
		}
	}
};
</script>
