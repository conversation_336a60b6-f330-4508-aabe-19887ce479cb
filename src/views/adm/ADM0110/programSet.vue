<template>
	<!-- 查詢條件-->
	<div class="card card-form-collapse">
		<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
			<h4>{{ $t('adm.searchCond') }}</h4>
			<span class="tx-square-bracket">{{ $t('adm.reqField') }}</span>
		</div>
		<div id="formsearch1" class="card-body collapse show">
			<form>
				<div class="form-row">
					<div class="form-group col-lg-6">
						<label class="form-label">{{ $t('adm.funcModule') }}</label>
						<select
							id="menuCode"
							v-model="menuCode"
							name="menuCode"
							class="form-select"
						>
							<option value="" selected="selected">
								{{ $t('adm.all') }}
							</option>
							<option v-for="functionMenu in functionMenuTree" :value="functionMenu.menuCode">
								{{ functionMenu.menuName }}
							</option>
						</select>
					</div>
				</div>
				<div class="form-footer">
					<button
						id="btnSave"
						type="button"
						class="btn btn-primary"
						@click="getPrograSet()"
					>
						{{ $t('adm.search') }}
					</button>
				</div>
			</form>
		</div>
	</div>
	<!-- 查詢結果-->
	<div id="searchResult">
		<div class="card card-table">
			<div class="card-header">
				<h4>{{ $t('adm.sysMenuFuncSetting') }}</h4>
			</div>
			<div class="table-responsive">
				<div class="m-2">
					<button type="button" class="btn btn-info btn-glow me-2" @click="toggleAll(true)">
						{{ $t('adm.expandAllBtn') }}
					</button>
					<button type="button" class="btn btn-info btn-glow" @click="toggleAll(false)">
						{{ $t('adm.collapseAllBtn') }}
					</button>
				</div>
				<table class="bih-table table table-RWD table-expandable">
					<thead>
						<tr>
							<th>{{ $t('adm.level1MenuModule') }}</th>
							<th>{{ $t('adm.level2MenuFunc') }}</th>
							<th>{{ $t('adm.level3MenuFunc') }}</th>
							<th>{{ $t('adm.level4MenuTab') }}</th>
							<th>{{ $t('adm.level5MenuSubTab') }}</th>
							<th>{{ $t('adm.modifyPerson') }}</th>
							<th>{{ $t('adm.modifyDate') }}</th>
							<th>{{ $t('adm.executeCol') }}</th>
						</tr>
					</thead>
					<tbody>
						<template v-for="module in prograSet">
							<tr>
								<td class="text-start">
									<button
										class="btn"
										type="button"
										:style="module.nodes?.length ? '' : 'pointer-events: none;'"
										@click="toggle(module)"
									>
										<i
											class="bi"
											:class="module.isExpanded ? 'bi-dash-lg' : 'bi-plus-lg'"
											:style="module.nodes?.length ? '' : 'opacity: 0'"
										/>
									</button>
									{{ module.name }}
								</td>
								<td />
								<td />
								<td />
								<td />
								<td class="text-center" :data-th="$t('adm.modifyPerson')" />
								<td class="text-center" :data-th="$t('adm.modifyDate')" />
								<td class="text-center" :data-th="$t('adm.executeCol')" />
							</tr>
							<template v-for="program in module.nodes">
								<tr v-show="module.isExpanded">
									<td />
									<td class="text-start">
										<button
											class="btn"
											type="button"
											:style="program.nodes?.length ? '' : 'pointer-events: none;'"
											@click="toggle(program)"
										>
											<i
												class="bi"
												:class="program.isExpanded ? 'bi-dash-lg' : 'bi-plus-lg'"
												:style="program.nodes?.length ? '' : 'opacity: 0'"
											/>
										</button>
										{{ program.name }}
									</td>
									<td />
									<td />
									<td />
									<td class="text-center" :data-th="$t('adm.modifyPerson')">
										{{ program.modifyBy }}
										{{ program.userName }}
									</td>
									<td class="text-center" :data-th="$t('adm.modifyDate')">
										{{ program.modifyDt }}
									</td>
									<td class="text-center" :data-th="$t('adm.executeCol')">
										<div v-if="!isExcludedMenu(program.menuCode)" class="form-check form-check-inline">
											<input
												:id="program.menuCode + 'Y'"
												:key="program.menuCode"
												class="form-check-input"
												type="radio"
												:name="program.menuCode"
												:checked="program.activeYn == 'Y'"
												@change="updateActiveArray(program, 'Y')"
											>
											<label class="form-check-label" :for="program.menuCode + 'Y'">{{ $t('adm.execute') }}</label>
										</div>
										<div v-if="!isExcludedMenu(program.menuCode)" class="form-check form-check-inline">
											<input
												:id="program.menuCode + 'N'"
												:key="program.menuCode"
												class="form-check-input"
												type="radio"
												:name="program.menuCode"
												:checked="program.activeYn == 'N'"
												@change="updateActiveArray(program, 'N')"
											>
											<label class="form-check-label" :for="program.menuCode + 'N'">{{ $t('adm.disable') }}</label>
										</div>
									</td>
								</tr>
								<template v-for="page in program.nodes">
									<tr v-show="program.isExpanded && module.isExpanded">
										<td />
										<td />
										<td class="text-start">
											<button
												class="btn"
												type="button"
												:style="page.nodes?.length ? '' : 'pointer-events: none;'"
												@click="toggle(page)"
											>
												<i
													class="bi"
													:class="page.isExpanded ? 'bi-dash-lg' : 'bi-plus-lg'"
													:style="page.nodes?.length ? '' : 'opacity: 0'"
												/>
											</button>
											{{ page.name }}
										</td>
										<td />
										<td />
										<td class="text-center" :data-th="$t('adm.modifyPerson')">
											{{ page.modifyBy }}
											{{ page.userName }}
										</td>
										<td class="text-center" :data-th="$t('adm.modifyDate')">
											{{ page.modifyDt }}
										</td>
										<td class="text-center" :data-th="$t('adm.executeCol')">
											<div v-if="!isExcludedMenu(page.menuCode)" class="form-check form-check-inline">
												<input
													:id="page.menuCode + 'Y'"
													:key="page.id"
													class="form-check-input"
													type="radio"
													:name="page.menuCode"
													:checked="page.activeYn == 'Y'"
													@change="updateActiveArray(page, 'Y')"
												>
												<label class="form-check-label" :for="page.menuCode + 'Y'">{{ $t('adm.execute') }}</label>
											</div>
											<div v-if="!isExcludedMenu(page.menuCode)" class="form-check form-check-inline">
												<input
													:id="page.menuCode + 'N'"
													:key="page.id"
													class="form-check-input"
													type="radio"
													:name="page.menuCode"
													:checked="page.activeYn == 'N'"
													@change="updateActiveArray(page, 'N')"
												>
												<label class="form-check-label" :for="page.menuCode + 'N'">{{ $t('adm.disable') }}</label>
											</div>
										</td>
									</tr>
									<template v-for="tab in page.nodes">
										<tr v-show="page.isExpanded && program.isExpanded && module.isExpanded">
											<td />
											<td />
											<td />
											<td class="text-start">
												<button
													class="btn"
													type="button"
													:style="tab.nodes?.length ? '' : 'pointer-events: none'"
													@click="toggle(tab)"
												>
													<i
														class="bi"
														:class="tab.isExpanded ? 'bi-dash-lg' : 'bi-plus-lg'"
														:style="tab.nodes?.length ? '' : 'opacity: 0'"
													/>
												</button>
												{{ tab.name }}
											</td>
											<td />
											<td class="text-center" :data-th="$t('adm.modifyPerson')">
												{{ tab.modifyBy }}
												{{ tab.userName }}
											</td>
											<td class="text-center" :data-th="$t('adm.modifyDate')">
												{{ tab.modifyDt }}
											</td>
											<td class="text-center" :data-th="$t('adm.executeCol')">
												<div v-if="!isExcludedMenu(tab.menuCode)" class="form-check form-check-inline">
													<input
														:id="tab.menuCode + 'Y'"
														:key="tab.menuCode"
														class="form-check-input"
														type="radio"
														:name="tab.menuCode"
														:checked="tab.activeYn == 'Y'"
														@change="updateActiveArray(tab, 'Y')"
													>
													<label class="form-check-label" :for="tab.menuCode + 'Y'">{{ $t('adm.execute') }}</label>
												</div>
												<div v-if="!isExcludedMenu(tab.menuCode)" class="form-check form-check-inline">
													<input
														:id="tab.menuCode + 'N'"
														:key="tab.menuCode"
														class="form-check-input"
														type="radio"
														:name="tab.menuCode"
														:checked="tab.activeYn == 'N'"
														@change="updateActiveArray(tab, 'N')"
													>
													<label class="form-check-label" :for="tab.menuCode + 'N'">{{ $t('adm.disable') }}</label>
												</div>
											</td>
										</tr>
										<template v-for="subTab in tab.nodes">
											<tr v-show="tab.isExpanded && page.isExpanded && program.isExpanded && module.isExpanded">
												<td />
												<td />
												<td />
												<td />
												<td class="text-start">
													<button class="btn" type="button" :style="subTab.nodes?.length ? '' : 'pointer-events: none'">
														<i
															class="bi"
															:class="subTab.isExpanded ? 'bi-dash-lg' : 'bi-plus-lg'"
															:style="subTab.nodes?.length ? '' : 'opacity: 0'"
														/>
													</button>
													{{ subTab.name }}
												</td>
												<td class="text-center" :data-th="$t('adm.modifyPerson')">
													{{ subTab.modifyBy }}
													{{ subTab.userName }}
												</td>
												<td class="text-center" :data-th="$t('adm.modifyDate')">
													{{ subTab.modifyDt }}
												</td>
												<td class="text-center" :data-th="$t('adm.executeCol')">
													<div v-if="!isExcludedMenu(subTab.menuCode)" class="form-check form-check-inline">
														<input
															:id="subTab.menuCode + 'Y'"
															:key="subTab.menuCode"
															class="form-check-input"
															type="radio"
															:name="subTab.menuCode"
															:checked="subTab.activeYn == 'Y'"
															@change="updateActiveArray(subTab, 'Y')"
														>
														<label class="form-check-label" :for="subTab.menuCode + 'Y'">{{ $t('adm.execute') }}</label>
													</div>
													<div v-if="!isExcludedMenu(subTab.menuCode)" class="form-check form-check-inline">
														<input
															:id="subTab.menuCode + 'N'"
															:key="subTab.menuCode"
															class="form-check-input"
															type="radio"
															:name="subTab.menuCode"
															:checked="subTab.activeYn == 'N'"
															@change="updateActiveArray(subTab, 'N')"
														>
														<label class="form-check-label" :for="subTab.menuCode + 'N'">{{ $t('adm.disable') }}</label>
													</div>
												</td>
											</tr>
										</template>
									</template>
								</template>
							</template>
						</template>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<div class="btn-end">
		<button
			id="btnSave"
			type="button"
			class="btn btn-primary btn-lg"
			@click="save()"
		>
			{{ $t('adm.save') }}
		</button>
	</div>
</template>

<script>
export default {
	data: function () {
		return {
			// 下拉選單
			functionMenuTree: null,
			// Api 用參數
			menuCode: '',
			menuUpdateReq: [],

			// Api邏輯判斷用參數
			orgCheckedIds: [],
			orgUnCheckedIds: [],

			// 主要顯示資料
			prograSet: [],

			// 用來修改項目是否執行的陣列
			activeArray: []

		};
	},
	computed: {
		...mapStores(useMenusStore),
		menus: function () {
			return this.menusStore.menus;
		},
		path() {
			return this.$route.path.split('/').slice(0, 3).join('/');
		},
		// 不可控制的功能
		excludeMenuCode() {
			const set = new Set();
			const path = this.path;
			function search(menus) {
				for (const menu of menus) {
					if (menu.url && menu.url === path) set.add(menu.code);
					if (menu.nodes) search(menu.nodes);
				}
			}
			search(this.menus);
			return set;
		}
	},
	mounted: function () {
		this.getFunctionMenuTree();
	},
	methods: {
		toggle(menu) {
			menu.isExpanded = !menu.isExpanded;
		},
		toggleAll(isExpanded, menus = this.prograSet) {
			if (!Array.isArray(menus)) return;
			menus.forEach((m) => {
				m.isExpanded = isExpanded;
				if (m.nodes?.length) {
					this.toggleAll(isExpanded, m.nodes);
				}
			});
		},
		getFunctionMenuTree: async function () {
			const ret = await this.$api.getFunctionMenuTreeApi();
			this.functionMenuTree = ret.data;
		},
		isExcludedMenu: function (menuCode) {
			return this.excludeMenuCode.has(menuCode);
		},
		getPrograSet: async function () {
			this.prograSet = [];
			this.activeArray = [];

			const ret = await this.$api.getFunctionMenuTreeApi(this.menuCode);
			this.prograSet = ret.data;
		},
		updateActiveArray: function (menu, yn, direction) {
			if (menu) {
				const existingItem = this.activeArray.find(item => item.menuCode === menu.menuCode);
				menu.activeYn = yn;
				if (!existingItem) {
					const newItem = { menuCode: menu.menuCode, activeYn: yn };
					this.activeArray.push(newItem);
				}
				else {
					existingItem.activeYn = yn;
				}

				if (direction === 'up' || !direction) {
					// 往上變更父menu的狀態
					const findParentMenu = (menus, targetMenuCode) => {
						const found = menus.find(m => m.parentCode && m.menuCode === targetMenuCode);
						if (!found) {
							const foundList = menus.map(m => findParentMenu(m.nodes, targetMenuCode)).filter(m => m);
							return foundList[0] || undefined;
						}
						return found;
					};
					const parentMenu = findParentMenu(this.prograSet, menu.parentCode);
					const sameLevelActiveYn = parentMenu?.nodes.map(x => x.activeYn).filter((item, idx, arr) => arr.indexOf(item) === idx);
					// 本層全部都是N => 把父menu也變成N
					// 本曾是Y但父menu是N => 把父menu也變成Y
					if ((sameLevelActiveYn?.length === 1 && sameLevelActiveYn[0] === 'N') || (menu.activeYn === 'Y' && parentMenu.activeYn === 'N')) {
						this.updateActiveArray(parentMenu, yn, 'up');
					}
				}
				if (direction === 'down' || !direction) {
					// 往下變更全部子menu的狀態
					menu.nodes?.forEach((m) => {
						this.updateActiveArray(m, yn, 'down');
					});
				}
			}
		},
		save: async function () {
			const ret = await this.$api.patchSaveMenuActive(this.activeArray);
			if (!ret) return;
			this.menusStore.getMenus();
			this.$swal.fire({
				title: this.$t('adm.modifyMsg'),
				text: this.$t('adm.modifySuccess'),
				showCloseButton: true,
				confirmButtonText: this.$t('adm.confirmBtn'),
				buttonsStyling: false,
				customClass: {
					confirmButton: 'btn btn-primary'
				}
			});
			this.getPrograSet();
		}
	}
};
</script>
