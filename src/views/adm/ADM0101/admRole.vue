<template>
	<div class="card card-form-collapse">
		<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
			<h4>{{ $t('adm.searchCond') }}</h4>
			<span class="tx-square-bracket">{{ $t('adm.reqField') }}</span>
		</div>
		<div id="formsearch1" class="card-body collapse show">
			<div class="form-row">
				<div class="form-group col-12 col-lg-6">
					<label class="form-label">{{ $t('adm.selSysRole') }}</label>
					<select
						id="roleCode"
						v-model="roleCode"
						name="roleCode"
						class="form-select"
					>
						<option value="">
							{{ $t('adm.all') }}
						</option>
						<option v-for="roleData in roleMenu" :value="roleData.roleCode">
							[{{ roleData.roleCode }}]{{ roleData.roleName }}
						</option>
					</select>
				</div>
				<div class="form-group col-12 col-lg-6 justify-content-lg-start justify-content-end">
					<button class="btn btn-primary btn-search" @click="getAdmRoles()">
						{{ $t('adm.search') }}
					</button>
					<button class="btn btn-primary ms-1" @click="exportMenuData()">
						{{ $t('adm.excelDL') }}
					</button>
				</div>
			</div>
		</div>
	</div>
	<div class="card card-table">
		<div class="card-header">
			<h4>{{ $t('adm.sysRoleList') }}</h4>
		</div>
		<div class="table-responsive">
			<table class="table table-RWD table-hover table-bordered">
				<thead>
					<tr>
						<th width="10%">
							{{ $t('adm.code') }}
						</th>
						<th width="20%">
							{{ $t('adm.name') }}
						</th>
						<th width="10%">
							{{ $t('adm.submitDate') }}
						</th>
						<th width="10%">
							{{ $t('adm.submitter') }}
						</th>
						<th width="10%">
							{{ $t('adm.reviewDate') }}
						</th>
						<th width="10%">
							{{ $t('adm.reviewer') }}
						</th>
						<th width="10%">
							{{ $t('adm.reviewStatus') }}
						</th>
						<th width="15%">
							{{ $t('adm.permSetting') }}
						</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="item in admRoleData">
						<td :data-th="$t('adm.code')">
							{{ item.roleCode }}
						</td>
						<td :data-th="$t('adm.name')" class="text-start">
							{{ item.roleName }}
						</td>
						<td :data-th="$t('adm.submitDate')">
							{{ item.createDt }}
						</td>
						<td :data-th="$t('adm.submitter')">
							{{ item.createUserCode }}
							{{ item.createUserName }}
						</td>
						<td :data-th="$t('adm.reviewDate')">
							{{ item.verifyDt }}
						</td>
						<td :data-th="$t('adm.reviewer')">
							{{ item.verifyUserCode }}
							{{ item.verifyUserName }}
						</td>

						<td v-if="item.status == null" :data-th="$t('adm.reviewStatus')" />
						<!-- Parker 檢查權限 -->
						<td v-if="item.status == 'R'" :data-th="$t('adm.reviewStatus')">
							<a href="#" class="tx-link tx-danger" @click.prevent="showRejectContent(item)">{{ $t('adm.returnEdit') }}</a>
						</td>
						<td v-if="item.status == 'A'" :data-th="$t('adm.reviewStatus')">
							{{ $t('adm.recheckComplete') }}
						</td>
						<td v-if="item.status == 'P'" :data-th="$t('adm.reviewStatus')">
							{{ $t('adm.pendingRecheck') }}
						</td>

						<td v-if="item.status != 'P'" :data-th="$t('adm.permSetting')" class="text-center">
							<button
								type="button"
								class="btn btn-info btn-icon"
								data-bs-toggle="tooltip"
								href="javascript: void(0)"
								:title="$t('adm.edit')"
								@click="editRoleAuthority(item.roleCode)"
							>
								<i class="bi bi-pen" />
							</button>
						</td>

						<!-- 權限{{ $t('adm.view') }} -->
						<td v-if="item.status == 'P'" :data-th="$t('adm.permSetting')" class="text-center">
							<button type="button" class="btn btn-dark btn-icon" @click="viewRoleAuthority(item.eventId, item.roleName)">
								<i class="bi bi-search" />
							</button>
						</td>
						<!-- <td :data-th="$t('adm.permSetting')" class="text-center">
	<button
		type="button"
		class="btn btn-info btn-icon"
		data-bs-toggle="tooltip"
		href="javascript: void(0)"
		:title="$t('adm.edit')"
		@click="editRoleAuthority(item.roleCode)"
	>
		<i class="bi bi-pen"></i>
	</button>
</td>
<td :data-th="$t('adm.permSetting')" class="text-center">
	<button
		type="button"
		class="btn btn-dark btn-icon"
		@click="viewRoleAuthority(item.eventId, item.roleName)"
	>
		<i class="bi bi-search"></i>
	</button>
</td> -->
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<!--Detail Modal-->
	<adm-role-review-detail ref="admRoleReviewDetail" />
	<!--reject Modal-->
	<modal ref="closeAlertModal" @close="closeAlertModal">
		<template #content="props">
			<div class="modal-dialog modal-dialog-centered">
				<div class="modal-content">
					<div class="modal-body pt-5">
						<div class="icon-alert" />
						<input
							id="recipient-name"
							v-model="rejectMsg"
							type="text"
							disabled
							class="form-control form-control-plaintext tx-20 tx-danger text-center"
						>
					</div>
					<div class="modal-footer">
						<button class="btn btn-primary btn-glow" @click.prevent="props.close()">
							{{ $t('adm.confirm') }}
						</button>
					</div>
				</div>
			</div>
		</template>
	</modal>
</template>

<script>
import admRoleReviewDetail from './include/admRoleReviewDetail.vue';
import modal from '@/views/components/model.vue';

export default {
	components: {
		admRoleReviewDetail,
		modal
	},
	data: function () {
		return {
			// Api 用參數
			roleCode: '',
			roleName: null,
			// 顯示用參數
			rejectMsg: null,
			// 主要顯示資料
			roleMenu: [],
			admRoleData: [],
			roleMenuLogs: []
			// isOpenModal: null,
			// isOpenAlertModal: null
		};
	},
	mounted: function () {
		this.getRoleMenu();
		this.getAdmRoles();
	},
	methods: {
		getRoleMenu: async function () {
			const ret = await this.$api.getRoleMenuApi();
			this.roleMenu = ret.data;
		},
		getAdmRoles: async function () {
			const ret = await this.$api.getAdmRolesApi(this.roleCode);
			this.admRoleData = ret.data;
		},
		exportMenuData: function () {
			if (!this.roleCode) {
				this.$bi.alert(self.$t('adm.sysRoleRequired'));
				return;
			}

			this.$api.getUserRoleMenuExportApi({ roleCode: this.roleCode }).then((blob) => {
				const fileName = 'MenuPreview.xls';
				const link = document.createElement('a');
				link.href = window.URL.createObjectURL(blob);
				link.download = fileName;
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);
			}).catch(() => {});
		},
		viewRoleAuthority: function (eventId, roleName) {
			this.$refs.admRoleReviewDetail.getDetail(eventId, roleName);
		},
		editRoleAuthority: function (roleCode) {
			this.$router.push('/adm/admRole/AdmRoleAuthority/' + roleCode);
		},
		showRejectContent: function (item) {
			this.rejectMsg = item.reason;
			this.$refs.closeAlertModal.open();
		},
		closeAlertModal: function () {
			this.$refs.closeAlertModal.close();
		}
	}
};
</script>
