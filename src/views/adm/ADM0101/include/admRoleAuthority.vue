<template>
	<!--頁面內容 start-->
	<div class="card card-table">
		<div class="card-header">
			<h4>{{ $t('adm.funcPermMaint') }}</h4>
		</div>
		<table class="table mb-0">
			<tbody>
				<tr>
					<th colspan="4">
						{{ $t('adm.sysRole') }}：{{ roleName }}
					</th>
					<th colspan="4" class="text-center">
						{{ $t('adm.permissionScope') }}
					</th>
				</tr>
				<!--程式的start-->
				<tr>
					<td colspan="8">
						<button
							id="btnExpandAll"
							type="button"
							class="btn btn-info"
							@click="toggleAll(true)"
						>
							{{ $t('adm.expandAll') }}
						</button>
						<button
							id="btnCollapseAll"
							type="button"
							class="btn btn-info"
							@click="toggleAll(false)"
						>
							{{ $t('adm.collapseAll') }}
						</button>
					</td>
				</tr>
				<tr>
					<th>{{ $t('adm.menuLevel1') }}</th>
					<th>{{ $t('adm.menuLevel2') }}</th>
					<th>{{ $t('adm.menuLevel3') }}</th>
					<th>{{ $t('adm.menuLevel4') }}</th>
					<th>{{ $t('adm.menuLevel5') }}</th>
					<th>{{ $t('adm.query') }}</th>
					<th>{{ $t('adm.edit') }}</th>
					<th>{{ $t('adm.verify') }}</th>
					<th>{{ $t('adm.export') }}</th>
				</tr>
				<template v-for="(item) in menuEnableData">
					<tr class="accordion-header">
						<td :data-th="$t('adm.menuLevel1')" class="text-start">
							<span class="" :class="{ 'accordion-toggle': !$_.isEmpty(item.nodes), collapsed: !item.show }" @click="toggle(item)">
								<label class="text-start">{{ item.menuName }}</label>
							</span>
						</td>
						<td :data-th="$t('adm.menuLevel2')" />
						<td :data-th="$t('adm.menuLevel3')" />
						<td :data-th="$t('adm.menuLevel4')" />
						<td :data-th="$t('adm.menuLevel5')" />
						<td :data-th="$t('adm.query')">
							<div v-if="$_.isEmpty(item.nodes)" class="form-check form-check-inline">
								<input
									v-model="item.view"
									class="form-check-input"
									:name="item.menuCode"
									type="checkbox"
									:disabled="!item.progView"
									:checked="item.view"
								>
							</div>
						</td>
						<td :data-th="$t('adm.edit')">
							<div v-if="$_.isEmpty(item.nodes)" class="form-check form-check-inline">
								<input
									v-model="item.edit"
									class="form-check-input"
									:name="item.menuCode"
									type="checkbox"
									:disabled="!item.progEdit"
									:checked="item.edit"
								>
							</div>
						</td>
						<td :data-th="$t('adm.verify')">
							<div v-if="$_.isEmpty(item.nodes)" class="form-check form-check-inline">
								<input
									v-model="item.verify"
									class="form-check-input"
									:name="item.menuCode"
									type="checkbox"
									:disabled="!item.progVerify"
									:checked="item.verify"
								>
							</div>
						</td>
						<td :data-th="$t('adm.export')">
							<div v-if="$_.isEmpty(item.nodes)" class="form-check form-check-inline">
								<input
									v-model="item.export"
									class="form-check-input"
									:name="item.menuCode"
									type="checkbox"
									:disabled="!item.progExport"
									:checked="item.export"
								>
							</div>
						</td>
					</tr>
					<template v-for="(item2) in item.nodes">
						<tr v-if="item.show">
							<td :data-th="$t('adm.menuLevel1')" />
							<td :data-th="$t('adm.menuLevel2')" class="text-start">
								<span :class="{ 'accordion-toggle': !$_.isEmpty(item2.nodes), collapsed: !item2.show }" @click="toggle(item2)">
									<label class="text-start">{{ item2.menuName }}</label>
								</span>
							</td>
							<td :data-th="$t('adm.menuLevel3')" />
							<td :data-th="$t('adm.menuLevel4')" />
							<td :data-th="$t('adm.menuLevel5')" />
							<td :data-th="$t('adm.query')">
								<div v-if="$_.isEmpty(item2.nodes)" class="form-check form-check-inline">
									<input
										v-model="item2.view"
										class="form-check-input"
										:name="item2.menuCode"
										type="checkbox"
										:disabled="!item2.progView"
										:checked="item2.view"
									>
								</div>
							</td>
							<td :data-th="$t('adm.edit')">
								<div v-if="$_.isEmpty(item2.nodes)" class="form-check form-check-inline">
									<input
										v-model="item2.edit"
										class="form-check-input"
										:name="item2.menuCode"
										type="checkbox"
										:disabled="!item2.progEdit"
										:checked="item2.edit"
									>
								</div>
							</td>
							<td :data-th="$t('adm.verify')">
								<div v-if="$_.isEmpty(item2.nodes)" class="form-check form-check-inline">
									<input
										v-model="item2.verify"
										class="form-check-input"
										:name="item2.menuCode"
										type="checkbox"
										:disabled="!item2.progVerify"
										:checked="item2.verify"
									>
								</div>
							</td>
							<td :data-th="$t('adm.export')">
								<div v-if="$_.isEmpty(item2.nodes)" class="form-check form-check-inline">
									<input
										v-model="item2.export"
										class="form-check-input"
										:name="item2.menuCode"
										type="checkbox"
										:disabled="!item2.progExport"
										:checked="item2.export"
									>
								</div>
							</td>
						</tr>
						<template v-for="(item3) in item2.nodes">
							<tr v-if="item.show && item2.show">
								<td :data-th="$t('adm.menuLevel1')" />
								<td :data-th="$t('adm.menuLevel2')" />
								<td :data-th="$t('adm.menuLevel3')" class="text-start">
									<span :class="{ 'accordion-toggle': !$_.isEmpty(item3.nodes), collapsed: !item3.show }" @click="toggle(item3)">
										<label class="text-start">{{ item3.menuName }}</label>
									</span>
								</td>
								<td :data-th="$t('adm.menuLevel4')" />
								<td :data-th="$t('adm.menuLevel5')" />
								<td :data-th="$t('adm.query')">
									<div v-if="$_.isEmpty(item3.nodes)" class="form-check form-check-inline">
										<input
											v-model="item3.view"
											class="form-check-input"
											:name="item3.menuCode"
											type="checkbox"
											:disabled="!item3.progView"
											:checked="item3.view"
										>
									</div>
								</td>
								<td :data-th="$t('adm.edit')">
									<div v-if="$_.isEmpty(item3.nodes)" class="form-check form-check-inline">
										<input
											v-model="item3.edit"
											class="form-check-input"
											:name="item3.menuCode"
											type="checkbox"
											:disabled="!item3.progEdit"
											:checked="item3.edit"
										>
									</div>
								</td>
								<td :data-th="$t('adm.verify')">
									<div v-if="$_.isEmpty(item3.nodes)" class="form-check form-check-inline">
										<input
											v-model="item3.verify"
											class="form-check-input"
											:name="item3.menuCode"
											type="checkbox"
											:disabled="!item3.progVerify"
											:checked="item3.verify"
										>
									</div>
								</td>
								<td :data-th="$t('adm.export')">
									<div v-if="$_.isEmpty(item3.nodes)" class="form-check form-check-inline">
										<input
											v-model="item3.export"
											class="form-check-input"
											:name="item3.menuCode"
											type="checkbox"
											:disabled="!item3.progExport"
											:checked="item3.export"
										>
									</div>
								</td>
							</tr>
							<template v-for="(item4) in item3.nodes">
								<tr v-if="item.show && item2.show && item3.show">
									<td :data-th="$t('adm.menuLevel1')" />
									<td :data-th="$t('adm.menuLevel2')" />
									<td :data-th="$t('adm.menuLevel3')" />
									<td :data-th="$t('adm.menuLevel4')" class="text-start">
										<span
											:class="{ 'accordion-toggle': !$_.isEmpty(item4.nodes), collapsed: !item4.show }"
											@click="toggle(item4)"
										>
											<label class="text-start">{{ item4.menuName }}</label>
										</span>
									</td>
									<td :data-th="$t('adm.menuLevel5')" />
									<td :data-th="$t('adm.query')">
										<div v-if="$_.isEmpty(item4.nodes)" class="form-check form-check-inline">
											<input
												v-model="item4.view"
												class="form-check-input"
												:name="item4.menuCode"
												type="checkbox"
												:disabled="!item4.progView"
												:checked="item4.view"
											>
										</div>
									</td>
									<td :data-th="$t('adm.edit')">
										<div v-if="$_.isEmpty(item4.nodes)" class="form-check form-check-inline">
											<input
												v-model="item4.edit"
												class="form-check-input"
												:name="item4.menuCode"
												type="checkbox"
												:disabled="!item4.progEdit"
												:checked="item4.edit"
											>
										</div>
									</td>
									<td :data-th="$t('adm.verify')">
										<div v-if="$_.isEmpty(item4.nodes)" class="form-check form-check-inline">
											<input
												v-model="item4.verify"
												class="form-check-input"
												:name="item4.menuCode"
												type="checkbox"
												:disabled="!item4.progVerify"
												:checked="item4.verify"
											>
										</div>
									</td>
									<td :data-th="$t('adm.export')">
										<div v-if="$_.isEmpty(item4.nodes)" class="form-check form-check-inline">
											<input
												v-model="item4.export"
												class="form-check-input"
												:name="item4.menuCode"
												type="checkbox"
												:disabled="!item4.progExport"
												:checked="item4.export"
											>
										</div>
									</td>
								</tr>
								<template v-for="(item5) in item4.nodes">
									<tr v-if="item.show && item2.show && item3.show && item4.show">
										<td :data-th="$t('adm.menuLevel1')" />
										<td :data-th="$t('adm.menuLevel2')" />
										<td :data-th="$t('adm.menuLevel3')" />
										<td :data-th="$t('adm.menuLevel4')" />
										<td :data-th="$t('adm.menuLevel5')" class="text-start">
											<label class="text-start">{{ item5.menuName }}</label>
										</td>
										<td :data-th="$t('adm.query')">
											<div v-if="$_.isEmpty(item5.nodes)" class="form-check form-check-inline">
												<input
													v-model="item5.view"
													class="form-check-input"
													:name="item5.menuCode"
													type="checkbox"
													:disabled="!item5.progView"
													:checked="item5.view"
												>
											</div>
										</td>
										<td :data-th="$t('adm.edit')">
											<div v-if="$_.isEmpty(item5.nodes)" class="form-check form-check-inline">
												<input
													v-model="item5.edit"
													class="form-check-input"
													:name="item5.menuCode"
													type="checkbox"
													:disabled="!item5.progEdit"
													:checked="item5.edit"
												>
											</div>
										</td>
										<td :data-th="$t('adm.verify')">
											<div v-if="$_.isEmpty(item5.nodes)" class="form-check form-check-inline">
												<input
													v-model="item5.verify"
													class="form-check-input"
													:name="item5.menuCode"
													type="checkbox"
													:disabled="!item5.progVerify"
													:checked="item5.verify"
												>
											</div>
										</td>
										<td :data-th="$t('adm.export')">
											<div v-if="$_.isEmpty(item5.nodes)" class="form-check form-check-inline">
												<input
													v-model="item5.export"
													class="form-check-input"
													:name="item5.menuCode"
													type="checkbox"
													:disabled="!item5.progExport"
													:checked="item5.export"
												>
											</div>
										</td>
									</tr>
								</template>
							</template>
						</template>
					</template>
				</template>
				<!--程式的end-->
			</tbody>
		</table>
		<div>
			<div id="treeview-noborder" class="" />
		</div>
	</div>
	<div class="col-12 mt-3 text-end">
		<button class="btn btn-primary btn-lg" type="button" @click="backRolePage()">
			{{ $t('adm.back') }}
		</button>
		<button class="btn btn-primary btn-lg" type="button" @click="updateRolAuthority()">
			{{ $t('adm.submit') }}
		</button>
	</div>
	<!--頁面內容 end-->
</template>

<script>
import { convertWkfResponse } from '@/utils/convertWkfResponse';

export default {
	data: function () {
		return {
			roleName: String,
			// 主要顯示資料
			menuEnableData: [],
			// 原本勾選的選單資料
			originalEnableData: []
		};
	},
	mounted: function () {
		this.getAdmRoles();
		this.getMenuEnableTree();
	},
	methods: {
		getAdmRoles: async function () {
			const ret = await this.$api.getAdmRolesApi(this.$route.params.rolecode);
			this.roleName = ret.data[0].roleName;
		},
		getMenuEnableTree: async function () {
			const ret = await this.$api.getMenuEnableTreeApi(this.$route.params.rolecode);
			this.menuEnableData = ret.data;
			// 保存原勾選的選單資料
			this.originalEnableData = this.$_.cloneDeep(this.menuEnableData);
			console.log(this.menuEnableData);
		},
		toggle(menu) {
			menu.show = !menu.show;
		},
		toggleAll(show, menus = this.menuEnableData) {
			menus.forEach((m) => {
				m.show = show;
				if (m.nodes?.length) {
					this.toggleAll(show, m.nodes);
				}
			});
		},
		backRolePage: function () {
			this.$router.push('/adm/admRole');
		},
		updateRolAuthority: async function () {
			const menuData = [];
			const orgDate = [];
			this.getCheckedMenuCodes(this.menuEnableData, menuData);
			this.getCheckedMenuCodes(this.originalEnableData, orgDate);
			const buffer = [];
			for (let i = 0; i < menuData.length; i++) {
				if (
					menuData[i].edit != orgDate[i].edit
					|| menuData[i].view != orgDate[i].view
					|| menuData[i].export != orgDate[i].export
					|| menuData[i].verify != orgDate[i].verify
				) {
					buffer.push(menuData[i]);
				}
			}

			if (buffer.length === 0) {
				this.$bi.alert(this.$t('adm.noChanges'));
				return;
			}

			const ret = await this.$api.postUpdateRolAuthorityApi(this.$route.params.rolecode, buffer);
			const result = convertWkfResponse(ret, false);
			if (result.success && result.value) this.$bi.alert(result.value);
			else if (!result.success && result.error) this.$bi.alert(result.error);
			setTimeout(function () {
				this.$router.push('/adm/admRole');
			}, 5000);
		},
		// 取得原勾選選單資料的 MENU_CODE
		getCheckedMenuCodes: function (nodes, buffer) {
			if (this.$_.isEmpty(nodes)) {
				return;
			}

			this.$_.forEach(nodes, (node) => {
				if (!this.$_.isNil(node.menuCode) && this.$_.size(node.nodes) > 0) {
					this.getCheckedMenuCodes(node.nodes, buffer);
				}
				else {
					buffer.push(node);
					return;
				}
			});
		}
	}
};
</script>
