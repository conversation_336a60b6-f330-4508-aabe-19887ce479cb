<template>
	<!-- Modal 1-->
	<modal ref="modal" @close="closeModal">
		<template #content="props">
			<div class="modal-dialog modal-dialog-centered modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<h4 id="detailModal" class="modal-title">
							{{ $t('adm.detailTitle') }}
						</h4>
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click="props.close()"
						/>
					</div>
					<div class="modal-body" style="overflow: scroll">
						<table class="table table-bordered">
							<caption>
								{{ $t('adm.functionMenu') }}
							</caption>
							<tbody>
								<tr>
									<th colspan="8">
										{{ $t('adm.sysRole') }}：{{ roleName }}
									</th>
								</tr>
								<tr>
									<th>{{ $t('adm.menuLevel1') }}</th>
									<th>{{ $t('adm.menuLevel2') }}</th>
									<th>{{ $t('adm.menuLevel3') }}</th>
									<th>{{ $t('adm.menuLevel4') }}</th>
									<th>{{ $t('adm.menuLevel5') }}</th>
									<th>{{ $t('adm.query') }}</th>
									<th>{{ $t('adm.edit') }}</th>
									<th>{{ $t('adm.verify') }}</th>
									<th>{{ $t('adm.export') }}</th>
								</tr>
								<tr v-for="item in roleMenuLogs">
									<td :data-th="$t('adm.menuLevel1')">
										{{ item.firMenuName }}
									</td>
									<td :data-th="$t('adm.menuLevel2')" class="text-start">
										{{ item.secMenuName }}
									</td>
									<td :data-th="$t('adm.menuLevel3')">
										{{ item.thiMenuName }}
									</td>
									<td :data-th="$t('adm.menuLevel4')">
										{{ item.fouMenuName }}
									</td>
									<td :data-th="$t('adm.menuLevel5')">
										{{ item.fifMenuName }}
									</td>
									<td :data-th="$t('adm.query')">
										<div class="form-check form-check-inline">
											<input
												class="form-check-input"
												type="checkbox"
												disabled
												:checked="item.view"
											>
										</div>
									</td>
									<td :data-th="$t('adm.edit')">
										<div class="form-check form-check-inline">
											<input
												class="form-check-input"
												type="checkbox"
												disabled
												:checked="item.edit"
											>
										</div>
									</td>
									<td :data-th="$t('adm.verify')">
										<div class="form-check form-check-inline">
											<input
												class="form-check-input"
												type="checkbox"
												disabled
												:checked="item.verify"
											>
										</div>
									</td>
									<td :data-th="$t('adm.export')">
										<div class="form-check form-check-inline bi-tree-removeY">
											<input
												class="form-check-input"
												type="checkbox"
												disabled
												:checked="item.export"
											>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-white" @click.prevent="props.close()">
							{{ $t('adm.close') }}
						</button>
					</div>
				</div>
			</div>
		</template>
	</modal>
</template>

<script>
import modal from '@/views/components/model.vue';

export default {
	components: {
		modal
	},
	props: {},
	data: function () {
		return {
			roleName: String,
			roleMenuLogs: []
		};
	},
	methods: {
		getDetail: async function (eventId, roleName) {
			if (this.$_.isBlank(eventId)) {
				return;
			}
			this.roleName = roleName;
			const ret = await this.$api.getDetailApi(eventId);
			this.roleMenuLogs = ret.data;
			this.$refs.modal.open();
		},
		closeModal: function () {
			this.$refs.modal.close();
		}
	}
};
</script>
