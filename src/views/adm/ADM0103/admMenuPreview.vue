<template>
	<div class="card card-form-collapse">
		<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
			<h4>{{ $t('adm.searchCond') }}</h4>
			<span class="tx-square-bracket">{{ $t('adm.reqField') }}</span>
		</div>

		<div id="formsearch1" class="card-body collapse show">
			<Form v-slot="{ errors }" ref="queryForm">
				<div class="form-row">
					<div class="form-group col-lg-6">
						<label class="form-label">{{ $t('adm.selSysRole') }}</label>
						<select
							id="roleCode"
							v-model="roleObj"
							name="roleCode"
							class="form-select"
							label="系統角色"
							rules="required"
							:class="{ 'is-invalid': errors.roleCode }"
						>
							<option :value="{ roleCode: null, roleName: null }">
								{{ $t('adm.pleaseSelect') }}
							</option>
							<option v-for="roleMenu in admRoleData" :value="roleMenu">
								[{{ roleMenu.roleCode }}]{{ roleMenu.roleName }}
							</option>
						</select>
					</div>
				</div>

				<!-- <div style="height: 25px">
					<span class="text-danger" v-show="errors.roleCode">{{errors.roleCode}}</span>
				</div> -->

				<div class="form-footer">
					<button id="queryBtnId" class="btn btn-primary" @click.prevent="getRoleMenuTree()">
						{{ $t('adm.search') }}
					</button>
					<button class="btn btn-primary" @click.prevent="exportMenuData()">
						{{ $t('adm.excelDL') }}
					</button>
				</div>
			</Form>
		</div>
	</div>

	<p class="tx-note mb-4">
		{{ $t('adm.menuPreviewNote') }}
	</p>

	<div v-if="showSearchResult" id="searchResult" class="searchResult">
		<div class="card card-table">
			<div class="card-header">
				<h4>{{ $t('adm.funcMenuPreview') }}</h4>
			</div>
			<table class="table">
				<tbody>
					<tr>
						<th>{{ $t('adm.sysRole') }}：{{ roleName }}</th>
					</tr>
				</tbody>
				<tbody>
					<tr>
						<td>
							<button
								id="expandBtn"
								type="button"
								class="btn btn-info"
								@click="expandBtn()"
							>
								{{ $t('adm.expandAll') }}
							</button>
							<button
								id="collapseBtn"
								type="button"
								class="btn btn-info"
								@click="collapsedBtn()"
							>
								{{ $t('adm.collapseAll') }}
							</button>
						</td>
					</tr>
					<tr class="bg-white">
						<td>
							<bi-tree ref="tree" :tree-data="treeData" :generate-checkbox="false" />
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</template>

<script>
import biTree from '@/views/components/biTree.vue';
import { downloadBlob } from '@/utils/downloadBlob';
import { Form } from 'vee-validate';
export default {
	components: {
		biTree,
		Form
	},
	data: function () {
		return {
			// Api 用參數
			roleObj: {
				roleCode: null,
				roleName: null
			},
			roleName: null,
			// 主要顯示資料
			admRoleData: [],

			treeData: {},
			showSearchResult: false
		};
	},
	mounted: function () {
		this.getRoleMenuDatas();
	},
	methods: {
		getRoleMenuDatas: async function () {
			const ret = await this.$api.getRoleMenuApi();
			this.admRoleData = ret.data;
		},
		getRoleMenuTree: function () {
			this.$refs.queryForm.validate().then(async (pass) => {
				if (pass.valid) {
					if (!this.roleObj.roleCode) {
						this.$bi.alert(this.$t('adm.sysRoleRequired'));
						return;
					}

					const ret = await this.$api.getRoleMenuTreeApi(this.roleObj.roleCode);
					this.treeData = ret.data;
					this.roleName = this.roleObj.roleName;
					this.showSearchResult = true;
				}
			});
		},
		exportMenuData: async function () {
			if (!this.roleObj.roleCode) {
				this.$bi.alert(this.$t('adm.sysRoleRequired'));
				return;
			}

			const blob = await this.$api.getUserRoleMenuExportApi({ roleCode: this.roleObj.roleCode });
			downloadBlob(blob, 'MenuPreview.xls');
		},
		expandBtn: function () {
			this.$refs.tree.expandAllNodes(true);
		},
		collapsedBtn: function () {
			this.$refs.tree.expandAllNodes(false);
		}
	}
};
</script>
