<template>
	<div class="row">
		<div class="col-12">
			<div class="card card-form">
				<div class="card-body">
					<div class="row g-3 align-items-end">
						<div class="col-md-3">
							<label class="form-label">{{ $t('adm.queryBranchUnit') }}</label>
							<select
								id="sltBran"
								v-model="branCode"
								name="branCodes"
								class="form-select"
								data-inline="true"
								@change="getUserMenu()"
							>
								<option value="">
									{{ $t('adm.all') }}
								</option>
								<option v-for="branInfo in branMenu" :value="branInfo.branCode">
									{{ branInfo.branCode }} {{ branInfo.branName }}
								</option>
							</select>
						</div>
						<div class="col-md-6">
							<label class="form-label">{{ $t('adm.queryDateRange') }}</label>
							<div class="input-group">
								<vue-field
									id="beginDate"
									v-model="logStartDt"
									type="date"
									name="beginDate"
									size="13"
									:label="$t('adm.queryStartDate')"
									class="form-control"
									maxlength="10"
									:class="{ 'is-invalid': showErrors && errors.logStartDt }"
								/>

								<span class="input-group-text">~</span>
								<vue-field
									id="endDate"
									v-model="logEndDt"
									type="date"
									name="endDate"
									size="13"
									:label="$t('adm.queryEndDate')"
									class="form-control"
									maxlength="10"
									:min="minValidEndDt"
									:class="{ 'is-invalid': showErrors && errors.logEndDt }"
								/>
							</div>
						</div>
						<div class="col-md-3">
							<label class="form-label">{{ $t('adm.queryUser') }}</label>
							<select
								id="userCode"
								v-model="userCode"
								name="userCode"
								class="form-select"
							>
								<option value="">
									{{ $t('adm.all') }}
								</option>
								<option v-for="user in userMenu" :value="user.userCode">
									{{ user.userCode }} {{ user.userName }}
								</option>
							</select>
						</div>
						<div class="col-md-3">
							<label class="form-label">{{ $t('adm.functionItem') }}</label>
							<select
								id="progListId"
								v-model="functionMenuCode"
								name="menuCode"
								class="form-select"
							>
								<option value="">
									{{ $t('adm.all') }}
								</option>
								<option v-for="item in cusFunctionMenu" :value="item.menuCode">
									{{ item.menuName }}
								</option>
							</select>
						</div>
						<div class="col-md-3">
							<label class="form-label">{{ $t('adm.customerIdTaxId') }}</label>
							<input
								id="idn"
								v-model="idn"
								name="idn"
								class="form-control"
							>
						</div>
						<div class="col-md-2">
							<button role="button" class="btn btn-primary btn-glow btn-searc=h" @click.prevent="gotoPage(0)">
								{{ $t('adm.search') }}
							</button>
						</div>
					</div>
				</div>
			</div>

			<div id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('adm.userCustomerAccessRecordList') }}</h4>
						<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-hover text-center">
							<thead>
								<tr>
									<th>{{ $t('adm.dateTime') }}<a class="icon-sort" data-rel="LOG_DT" @click="sort('logDt')" /></th>
									<th>{{ $t('adm.branchUnitCode') }}<a class="icon-sort" data-rel="BRAN_CODE" @click="sort('branCode')" /></th>
									<th>{{ $t('adm.branchUnitName') }}<a class="icon-sort" data-rel="BRAN_NAME" @click="sort('branName')" /></th>
									<th>{{ $t('adm.employeeNumber') }}<a class="icon-sort" data-rel="USER_CODE" @click="sort('userCode')" /></th>
									<th>{{ $t('adm.employeeName') }}<a class="icon-sort" data-rel="USER_NAME" @click="sort('userName')" /></th>
									<th class="text-start">
										{{ $t('adm.functionModule') }}<a class="icon-sort" data-rel="MENU_NAME" @click="sort('menuName')" />
									</th>
									<th>IP<a class="icon-sort" data-rel="REMOTE_IP" @click="sort('remoteIp')" /></th>
									<th>{{ $t('adm.customerName') }}<a class="icon-sort" data-rel="CUS_NAME" @click="sort('cusName')" /></th>
									<th>{{ $t('adm.customerIdTaxId') }}<a class="icon-sort" data-rel="CUS_CODE" @click="sort('cusCode')" /></th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in pageData.content">
									<td :data-th="$t('adm.dateTime')">
										{{ item.logDt }}
									</td>
									<td :data-th="$t('adm.branchUnitCode')">
										{{ item.branCode }}
									</td>
									<td :data-th="$t('adm.branchUnitName')">
										{{ item.branName }}
									</td>
									<td :data-th="$t('adm.employeeNumber')">
										{{ item.userCode }}
									</td>
									<td :data-th="$t('adm.employeeName')">
										{{ item.userName }}
									</td>
									<td :data-th="$t('adm.functionModule')" class="text-start">
										{{ item.menuName }}
									</td>
									<td data-th="IP">
										{{ item.remoteIp }}
									</td>
									<td :data-th="$t('adm.customerName')">
										{{ item.cusName }}
									</td>
									<td :data-th="$t('adm.customerIdTaxId')">
										{{ item.cusCode }}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import { Field } from 'vee-validate';
import pagination from '@/views/components/pagination.vue';

export default {
	components: {
		'vue-pagination': pagination,
		'vue-field': Field
	},
	data: function () {
		return {
			// API 用參數
			buCode: '',
			branCode: '',
			idn: null,
			functionMenuCode: '',
			userCode: '',
			logStartDt: null,
			logEndDt: null,
			// 下拉選單
			branMenu: [],
			moduleMenu: [],
			cusFunctionMenu: [],
			userMenu: [],
			// 主要顯示資料
			columnDef: {
				logDt: { sortRef: 'log_dt' },
				branCode: { sortRef: 'bran_code' },
				branName: { sortRef: 'bran_name' },
				userCode: { sortRef: 'user_code' },
				userName: { sortRef: 'user_name' },
				remoteId: { sortRef: 'remote_ip' },
				menuName: { sortRef: 'menu_name' },
				cusName: { sortRef: 'cus_name' },
				idnOriginal: { sortRef: 'idn_original' }
			},
			pageData: {},
			pageable: {
				page: 0,
				size: 20,
				sort: 'log_dt',
				direction: 'ASC'
			}
		};
	},
	computed: {
		minValidEndDt() {
			return this.logStartDt ? this.logStartDt : null;
		}
	},
	watch: {
		logStartDt: function (newVal, oldVal) {
			this.showErrors = false;
			if (newVal && this.logEndDt && newVal > this.logEndDt) {
				this.logEndDt = null;
			}
		}
	},
	mounted: function () {
		const self = this;
		self.getBranMenu();
		self.getCusFunctionMenu();
	},
	methods: {
		getBranMenu: function () {
			const self = this;
			self.$api.getAllBranchesMenuApi().then(function (ret) {
				self.branMenu = ret.data;
			});
		},
		getUserMenu: function () {
			const self = this;
			if (_.isBlank(self.branMenu)) {
				return;
			}

			self.$api
				.getUserMenuApi({
					branCode: self.branCode
				})
				.then(function (ret) {
					self.userMenu = ret.data;
				});
		},
		getCusFunctionMenu: function () {
			const self = this;
			self.$api.getCusFunctionMenuApi().then(function (ret) {
				self.cusFunctionMenu = ret.data;
			});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function (page) {
			const self = this;
			const url = _.toPageUrl('', page, self.pageable);
			// 查詢迄日+1, 若起迄日為同一天, 才能查到當日資料
			const logEndDt = !_.isEmpty(self.logEndDt) ? moment(self.logEndDt).add(1, 'days') : self.logEndDt;

			self.$api
				.getUserAccessCusLogsApi(
					{
						branCode: self.branCode,
						userCode: self.userCode,
						cusCode: self.idn,
						menuCode: self.functionMenuCode,
						logStartDt: _.formatDate(self.logStartDt),
						logEndDt: _.formatDate(logEndDt)
					},
					url
				)
				.then(function (ret) {
					self.pageData = ret.data;
				});
		},
		sort: function (columnName) {
			if (this.pageable.sort !== this.columnDef[columnName].sortRef) {
				this.pageable.sort = this.columnDef[columnName].sortRef;
				this.pageable.direction = 'DESC';
			}
			else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}
			this.gotoPage(0);
		}
	}
};
</script>
