<template>
	<div class="row">
		<div class="col-12">
			<vue-form v-slot="{ errors }" ref="queryForm">
				<div class="card card-form">
					<div class="card-header">
						<h4>{{ $t('adm.searchCond') }}</h4>
						<span class="tx-square-bracket">{{ $t('adm.reqField') }}</span>
					</div>
					<div class="card-body">
						<div class="row g-3 align-items-end">
							<div class="col-lg-2">
								<label class="form-label">{{ $t('adm.userCode') }}</label> &nbsp;{{ user.userName || '' }}
								<input
									id="userCode"
									v-model="userCode"
									type="text"
									name="userCode"
									class="form-control"
									:label="$t('adm.userCode')"
									@blur="isUserExists"
								>
								<div style="height: 25px" />
							</div>
							<div class="col-lg-5">
								<label class="form-label tx-require">{{ $t('adm.queryDateRangeStart') }}</label>
								<div class="input-group">
									<span class="input-group-text">{{ $t('adm.dateLabel') }}</span>
									<vue-field
										id="logStartDt"
										v-model="logStartDt"
										:class="{ 'is-invalid': errors.logStartDt }"
										name="logStartDt"
										type="date"
										class="form-control wd-30p-f"
										:label="$t('adm.queryDateRangeStart')"
										rules="required"
									/>
									<span class="input-group-text">{{ $t('adm.timeLabel') }}</span>
									<select
										id="startHour"
										v-model="logStartHour"
										name="startHour"
										class="form-select"
									>
										<option value="00">
											00
										</option>
										<option value="01">
											01
										</option>
										<option value="02">
											02
										</option>
										<option value="03">
											03
										</option>
										<option value="04">
											04
										</option>
										<option value="05">
											05
										</option>
										<option value="06">
											06
										</option>
										<option value="07">
											07
										</option>
										<option value="08">
											08
										</option>
										<option value="09">
											09
										</option>
										<option value="10">
											10
										</option>
										<option value="11">
											11
										</option>
										<option value="12">
											12
										</option>
										<option value="13">
											13
										</option>
										<option value="14">
											14
										</option>
										<option value="15">
											15
										</option>
										<option value="16">
											16
										</option>
										<option value="17">
											17
										</option>
										<option value="18">
											18
										</option>
										<option value="19">
											19
										</option>
										<option value="20">
											20
										</option>
										<option value="21">
											21
										</option>
										<option value="22">
											22
										</option>
										<option value="23">
											23
										</option>
									</select>
									<select
										id="startMin"
										v-model="logStartMinute"
										name="startMin"
										class="form-select"
									>
										<option value="00">
											00
										</option>
										<option value="15">
											15
										</option>
										<option value="30">
											30
										</option>
										<option value="45">
											45
										</option>
									</select>
								</div>
								<div style="height: 25px">
									<span v-show="errors.logStartDt" class="text-danger">{{ errors.logStartDt }}</span>
								</div>
							</div>
							<div class="col-lg-5">
								<label class="form-label tx-require">{{ $t('adm.queryDateRangeEnd') }}</label>
								<div class="input-group">
									<span class="input-group-text">{{ $t('adm.dateLabel') }}</span>
									<vue-field
										id="endDate"
										v-model="logEndDt"
										:class="{ 'is-invalid': errors.logEndDt }"
										name="logEndDt"
										type="date"
										class="form-control wd-30p-f"
										rules="required"
										:label="$t('adm.queryDateRangeEnd')"
									/>
									<span class="input-group-text">{{ $t('adm.timeLabel') }}</span>
									<select
										id="endHour"
										v-model="logEndHour"
										name="endHour"
										class="form-select"
									>
										<option value="00">
											00
										</option>
										<option value="01">
											01
										</option>
										<option value="02">
											02
										</option>
										<option value="03">
											03
										</option>
										<option value="04">
											04
										</option>
										<option value="05">
											05
										</option>
										<option value="06">
											06
										</option>
										<option value="07">
											07
										</option>
										<option value="08">
											08
										</option>
										<option value="09">
											09
										</option>
										<option value="10">
											10
										</option>
										<option value="11">
											11
										</option>
										<option value="12">
											12
										</option>
										<option value="13">
											13
										</option>
										<option value="14">
											14
										</option>
										<option value="15">
											15
										</option>
										<option value="16">
											16
										</option>
										<option value="17">
											17
										</option>
										<option value="18">
											18
										</option>
										<option value="19">
											19
										</option>
										<option value="20">
											20
										</option>
										<option value="21">
											21
										</option>
										<option value="22">
											22
										</option>
										<option value="23">
											23
										</option>
									</select>
									<select
										id="endMin"
										v-model="logEndMinute"
										name="endMin"
										class="form-select"
									>
										<option value="00">
											00
										</option>
										<option value="15">
											15
										</option>
										<option value="30">
											30
										</option>
										<option value="45">
											45
										</option>
									</select>
								</div>
								<div style="height: 25px">
									<span v-show="errors.logEndDt" class="text-danger">{{ errors.logEndDt }}</span>
								</div>
							</div>
							<div class="col-md-auto">
								<label class="form-label">{{ $t('adm.executionItem') }}</label><br>
								<div v-for="actionType in itemsActionTypeMenu" class="form-check form-check-inline">
									<input
										id="selActiveSet-1"
										v-model="actionTypes"
										class="form-check-input"
										type="radio"
										name="selActiveSet"
										:value="actionType.codeValue"
										@click="getFunctionMenu(actionType.codeValue)"
									>
									<label class="form-check-label checkboxLabel">{{ actionType.codeName }}</label>
								</div>
							</div>
							<div class="col-lg-4 col-md-4">
								<label class="form-label">{{ $t('adm.functionModule') }}</label>
								<div class="input-group">
									<span class="input-group-text">{{ $t('adm.functionItem') }}</span>
									<select
										id="target"
										v-model="targetId"
										name="strTarget"
										class="form-select"
									>
										<option value="" selected="selected">
											{{ $t('adm.all') }}
										</option>
										<option v-for="carryItem in carryItemMenu" :value="carryItem.progCode">
											{{ carryItem.target }}
										</option>
									</select>
								</div>
							</div>
							<div class="col-lg-2">
								<button role="button" class="btn btn-primary btn-glow btn-search" @click.prevent="gotoPage(0)">
									{{ $t('adm.search') }}
								</button>
							</div>
						</div>
					</div>
				</div>
			</vue-form>
			<div id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('adm.userDataProductionRecord') }}</h4>
						<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-hover text-center">
							<thead>
								<tr>
									<th>{{ $t('adm.date') }}</th>
									<th>{{ $t('adm.branchUnitCode') }}</th>
									<th>{{ $t('adm.branchUnitName') }}</th>
									<th>{{ $t('adm.employeeNumber') }}</th>
									<th>{{ $t('adm.employeeName') }}</th>
									<th>{{ $t('adm.functionItem') }}</th>
									<th>{{ $t('adm.customerIdTaxIdProductCode') }}</th>
									<th>{{ $t('adm.executionItem') }}</th>
									<th>{{ $t('adm.times') }}</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in pageData.content">
									<td :data-th="$t('adm.date')">
										{{ $filters.formatDate(item.logDate) }}
									</td>
									<td :data-th="$t('adm.branchUnitCode')">
										{{ item.branCode }}
									</td>
									<td :data-th="$t('adm.branchUnitName')">
										{{ item.branName }}
									</td>
									<td :data-th="$t('adm.employeeNumber')">
										{{ item.userCode }}
									</td>
									<td :data-th="$t('adm.employeeName')">
										{{ item.userName }}
									</td>
									<td :data-th="$t('adm.functionItem')">
										{{ item.target }}
									</td>
									<td :data-th="$t('adm.customerIdTaxIdProductCode')">
										{{ item.cusCode }} {{ item.docId }}
									</td>
									<td :data-th="$t('adm.executionItem')">
										{{ item.actionType }}
									</td>
									<td :data-th="$t('adm.times')">
										{{ item.num }}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
import _ from 'lodash';
import Swal from 'sweetalert2';
import { Field, Form } from 'vee-validate';
import pagination from '@/views/components/pagination.vue';

export default {
	components: {
		'vue-pagination': pagination,
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			// API 用參數
			userCode: null,
			targetId: '',
			actionTypes: [],
			logStartDt: null,
			logStartHour: '00',
			logStartMinute: '00',
			logEndDt: null,
			logEndHour: '00',
			logEndMinute: '00',
			// 下拉選單
			carryItemMenu: [],
			itemsActionTypeMenu: [],
			// 主要顯示資料
			pageData: {},
			pageable: {
				page: 0,
				size: 20,
				sort: 'log_date',
				direction: 'ASC'
			},
			user: {}
		};
	},
	mounted: function () {
		const self = this;
		self.getItemsActionTypeMenu();
	},
	methods: {
		isUserExists() {
			const self = this;
			if (self.userCode && self.userCode.trim() !== '') {
				self.$api
					.getUserInfoApi({
						userCode: self.userCode,
						path: 'auditUsageDataProduce'
					})
					.then(function (ret) {
						if (ret.data) {
							self.user = ret.data;
						}
						else {
							self.user = {};
						}
					})
					.catch(function (ret) {
						self.user = {};
					});
			}
			else {
				self.user = {};
			}
		},
		getFunctionMenu: function (actionMode) {
			const self = this;
			self.$api
				.getCarryOutItemsActionTypeApi({
					actionMode: actionMode
				})
				.then(function (ret) {
					self.carryItemMenu = ret.data;
				});
		},
		getItemsActionTypeMenu: function () {
			const self = this;
			self.$api.getItemsActionTypeMenuApi().then(function (ret) {
				self.itemsActionTypeMenu = ret.data;
			});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function (page) {
			const self = this;

			if (self.logStartDt > self.logEndDt) {
				Swal.fire({
					icon: 'error',
					text: self.$t('adm.startDateCannotBeGreaterThanEndDate'),
					showCloseButton: true,
					confirmButtonText: self.$t('adm.confirm'),
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			const stime = new Date(self.logStartDt).getTime();
			const etime = new Date(self.logEndDt).getTime();
			const usedTime = etime - stime;
			const days = Math.floor(usedTime / (24 * 3600 * 1000));
			if (days > 30) {
				Swal.fire({
					icon: 'error',
					text: self.$t('adm.queryDateRangeCannotExceedThirtyDays'),
					showCloseButton: true,
					confirmButtonText: self.$t('adm.confirm'),
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			self.$refs.queryForm.validate().then(function (pass) {
				if (pass.valid) {
					const url = _.toPageUrl('', page, self.pageable);

					const logStdDt = self.logStartDt + ' ' + self.logStartHour + ':' + self.logStartMinute;
					const logEndDt = self.logEndDt + ' ' + self.logEndHour + ':' + self.logEndMinute;

					self.$api
						.getCarryOutItemsApi(
							{
								userCode: self.userCode,
								progCode: self.targetId,
								actionTypes: self.actionTypes,
								logStdDt: logStdDt,
								logEndDt: logEndDt
							},
							url
						)
						.then(function (ret) {
							self.pageData = ret.data;
						});
				}
			});
		}
	}
};
</script>
