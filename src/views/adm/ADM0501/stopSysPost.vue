<template>
	<div class="row">
		<div class="col-12">
			<div class="card card-form">
				<div class="card-header">
					<h4>{{ $t('adm.pleaseEnterFollowingData') }}</h4>
					<span class="tx-square-bracket">{{ $t('adm.reqField') }}</span>
				</div>
				<vue-form v-slot="{ errors }" ref="queryForm">
					<div class="card-body">
						<div class="row g-3 align-items-end">
							<div class="col-lg-6">
								<label class="form-label tx-require">{{ $t('adm.shutdownDateStart') }}</label>
								<div class="input-group">
									<span class="input-group-text">{{ $t('adm.dateLabel') }}</span>
									<vue-field
										v-model="startDate"
										:class="{ 'is-invalid': errors.startDate }"
										name="startDate"
										type="date"
										class="form-control wd-30p-f"
										:label="$t('adm.shutdownDateStartDate')"
										rules="required"
									/>
									<span class="input-group-text">{{ $t('adm.timeLabel') }}</span>
									<vue-field
										v-model="startHr"
										as="select"
										:class="{ 'is-invalid': errors.startHr }"
										name="startHr"
										class="form-select"
										:label="$t('adm.shutdownDateStartTime')"
										rules="required"
									>
										<option value="00">
											00
										</option>
										<option value="01">
											01
										</option>
										<option value="02">
											02
										</option>
										<option value="03">
											03
										</option>
										<option value="04">
											04
										</option>
										<option value="05">
											05
										</option>
										<option value="06">
											06
										</option>
										<option value="07">
											07
										</option>
										<option value="08">
											08
										</option>
										<option value="09">
											09
										</option>
										<option value="10">
											10
										</option>
										<option value="11">
											11
										</option>
										<option value="12">
											12
										</option>
										<option value="13">
											13
										</option>
										<option value="14">
											14
										</option>
										<option value="15">
											15
										</option>
										<option value="16">
											16
										</option>
										<option value="17">
											17
										</option>
										<option value="18">
											18
										</option>
										<option value="19">
											19
										</option>
										<option value="20">
											20
										</option>
										<option value="21">
											21
										</option>
										<option value="22">
											22
										</option>
										<option value="23">
											23
										</option>
									</vue-field>
									<vue-field
										id="startMin"
										v-model="startMin"
										as="select"
										name="startMin"
										class="form-select"
										:label="$t('adm.shutdownDateStartMinute')"
										rules="required"
										:class="{ 'is-invalid': errors.startMin }"
									>
										<option value="00">
											00
										</option>
										<option value="05">
											05
										</option>
										<option value="10">
											10
										</option>
										<option value="15">
											15
										</option>
										<option value="20">
											20
										</option>
										<option value="25">
											25
										</option>
										<option value="30">
											30
										</option>
										<option value="35">
											35
										</option>
										<option value="40">
											40
										</option>
										<option value="45">
											45
										</option>
										<option value="50">
											50
										</option>
										<option value="55">
											55
										</option>
									</vue-field>
								</div>
								<div style="height: 25px">
									<span v-show="errors.startDate" class="text-danger">{{ errors.startDate }}</span>
									<span v-show="errors.startHr" class="text-danger">{{ errors.startHr }}</span>
									<span v-show="errors.startMin" class="text-danger">{{ errors.startMin }}</span>
								</div>
							</div>
							<div class="col-lg-6">
								<label class="form-label tx-require">{{ $t('adm.shutdownDateEnd') }}</label>
								<div class="input-group">
									<span class="input-group-text">{{ $t('adm.dateLabel') }}</span>
									<vue-field
										id="endDate"
										v-model="endDate"
										:class="{ 'is-invalid': errors.endDate }"
										name="endDate"
										type="date"
										class="form-control wd-30p-f"
										:label="$t('adm.shutdownDateEndDate')"
										rules="required"
									/>
									<span class="input-group-text">{{ $t('adm.timeLabel') }}</span>
									<vue-field
										id="endHour"
										v-model="endHr"
										as="select"
										:class="{ 'is-invalid': errors.endHour }"
										name="endHour"
										class="form-select"
										:label="$t('adm.shutdownDateEndTime')"
										rules="required"
									>
										<option value="00">
											00
										</option>
										<option value="01">
											01
										</option>
										<option value="02">
											02
										</option>
										<option value="03">
											03
										</option>
										<option value="04">
											04
										</option>
										<option value="05">
											05
										</option>
										<option value="06">
											06
										</option>
										<option value="07">
											07
										</option>
										<option value="08">
											08
										</option>
										<option value="09">
											09
										</option>
										<option value="10">
											10
										</option>
										<option value="11">
											11
										</option>
										<option value="12">
											12
										</option>
										<option value="13">
											13
										</option>
										<option value="14">
											14
										</option>
										<option value="15">
											15
										</option>
										<option value="16">
											16
										</option>
										<option value="17">
											17
										</option>
										<option value="18">
											18
										</option>
										<option value="19">
											19
										</option>
										<option value="20">
											20
										</option>
										<option value="21">
											21
										</option>
										<option value="22">
											22
										</option>
										<option value="23">
											23
										</option>
									</vue-field>
									<vue-field
										id="endMin"
										v-model="endMin"
										as="select"
										name="endMin"
										class="form-select"
										:label="$t('adm.shutdownDateEndMinute')"
										rules="required"
										:class="{ 'is-invalid': errors.endMin }"
									>
										<option value="00">
											00
										</option>
										<option value="05">
											05
										</option>
										<option value="10">
											10
										</option>
										<option value="15">
											15
										</option>
										<option value="20">
											20
										</option>
										<option value="25">
											25
										</option>
										<option value="30">
											30
										</option>
										<option value="35">
											35
										</option>
										<option value="40">
											40
										</option>
										<option value="45">
											45
										</option>
										<option value="50">
											50
										</option>
										<option value="55">
											55
										</option>
									</vue-field>
								</div>
								<div style="height: 25px">
									<span v-show="errors.endDate" class="text-danger">{{ errors.endDate }}</span>
									<span v-show="errors.endHour" class="text-danger">{{ errors.endHour }}</span>
									<span v-show="errors.endMin" class="text-danger">{{ errors.endMin }}</span>
								</div>
							</div>
							<div class="col-lg-9">
								<label class="form-label tx-require">{{ $t('adm.shutdownDescription') }}</label>
								<vue-field
									id="shutdownDesc"
									v-model="shutdownDesc"
									as="textarea"
									:class="{ 'is-invalid': errors.shutdownDesc }"
									name="shutdownDesc"
									rows="3"
									class="textarea form-control"
									:label="$t('adm.shutdownDescription')"
									:rules="{ required: true, max: 200 }"
								/>
								<div class="tx-note">
									{{ $t('adm.maxCharacters200') }}
								</div>
								<span v-show="errors.shutdownDesc" class="text-danger" style="height: 3px">
									{{ errors.shutdownDesc }}
								</span>
							</div>
							<div class="col-lg-3 mb-lg-5">
								<button v-if="shutdownId == null" class="btn btn-primary btn-glow btn-save" @click.prevent="insertShutdownInfo">
									{{ $t('adm.save') }}
								</button>
								<button v-if="shutdownId != null" class="btn btn-secondary btn-glow btn-cancle" @click.prevent="clearForm()">
									{{ $t('adm.cancelModify') }}
								</button>
								<button v-if="shutdownId != null" class="btn btn-primary btn-glow btn-modify" @click.prevent="updateShutdownInfo">
									{{ $t('adm.modify') }}
								</button>
							</div>
						</div>
					</div>
				</vue-form>
			</div>

			<div class="card card-table">
				<div class="card-header">
					<h4>{{ $t('adm.shutdownAnnouncementList') }}</h4>
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-hover text-center">
						<thead>
							<tr>
								<th>{{ $t('adm.shutdownDateStart') }}</th>
								<th>{{ $t('adm.shutdownDateEnd') }}</th>
								<th class="text-start wd-40p">
									{{ $t('adm.shutdownDescription') }}
								</th>
								<th>{{ $t('adm.execute') }}</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="item in shutdownInfo">
								<td :data-th="$t('adm.shutdownDateStart')">
									{{ item.startDt }}
								</td>
								<td :data-th="$t('adm.shutdownDateEnd')">
									{{ item.endDt }}
								</td>
								<td :data-th="$t('adm.shutdownDescription')" class="text-start">
									{{ item.shutdownDesc }}
								</td>
								<td :data-th="$t('adm.execute')">
									<button
										v-show="isStartDateFuture(item.endDt)"
										type="button"
										class="btn btn-info btn-glow btn-icon btn-edit"
										data-bs-toggle="tooltip"
										:data-bs-original-title="$t('adm.editTooltip')"
										@click="doUpdate(item)"
									>
										<i class="bi bi-pen" />
									</button>
									<button
										v-show="isStartDateFuture(item.endDt)"
										type="button"
										class="btn btn-danger btn-glow btn-icon"
										data-bs-toggle="tooltip"
										:data-bs-original-title="$t('adm.delete')"
										@click="deleteShutdownInfo(item)"
									>
										<i class="bi bi-trash" />
									</button>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
import Swal from 'sweetalert2';
import { Field, Form } from 'vee-validate';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			// API 用參數
			shutdownId: null,
			startDt: null,
			startDate: null,
			startHr: null,
			startMin: null,
			endDt: null,
			endDate: null,
			endHr: null,
			endMin: null,
			// 畫面顯示用參數
			shutdownDesc: null,
			// 主要顯示資料
			shutdownInfo: []
		};
	},
	mounted: function () {
		const self = this;
		self.getShutdownInfo();
	},
	methods: {
		// init function
		getShutdownInfo: function () {
			const self = this;
			self.$api.getShutdownInfoApi().then(function (ret) {
				self.shutdownInfo = ret.data;
			});
		},
		doUpdate: function (item) {
			const self = this;
			self.shutdownId = item.shutdownId;
			self.startDt = new Date(item.startDt);
			self.endDt = new Date(item.endDt);
			self.shutdownDesc = item.shutdownDesc;

			self.startDate
				= self.startDt.getFullYear()
					+ '-'
					+ self.paddingLeft(self.startDt.getMonth() + 1, 2)
					+ '-'
					+ self.paddingLeft(self.startDt.getDate(), 2);
			self.startHr = self.paddingLeft(self.startDt.getHours(), 2);
			self.startMin = self.paddingLeft(self.startDt.getMinutes(), 2);

			self.endDate
				= self.endDt.getFullYear() + '-' + self.paddingLeft(self.endDt.getMonth() + 1, 2) + '-' + self.paddingLeft(self.endDt.getDate(), 2);
			self.endHr = self.paddingLeft(self.endDt.getHours(), 2);
			self.endMin = self.paddingLeft(self.endDt.getMinutes(), 2);
		},
		clearForm: function () {
			const self = this;
			self.shutdownId = null;
			self.startDt = null;
			self.startDate = null;
			self.startHr = null;
			self.startMin = null;
			self.endDt = null;
			self.endDate = null;
			self.endHr = null;
			self.endMin = null;
			self.shutdownDesc = null;
			self.$refs.queryForm.resetForm();
		},
		insertShutdownInfo: function () {
			const self = this;
			self.$refs.queryForm.validate().then(function (pass) {
				if (pass.valid) {
					self.buildQueryStr();
					self.$api
						.postShutdownInfoApi({
							startDt: self.startDt,
							endDt: self.endDt,
							shutdownDesc: self.shutdownDesc
						})
						.then(function (ret) {
							Swal.fire({
								icon: 'success',
								text: self.$t('adm.addSuccess'),
								showCloseButton: true,
								confirmButtonText: self.$t('adm.confirm'),
								buttonStyling: false,
								customClass: {
									confirmButton: 'btn btn-success'
								}
							});
							self.clearForm();
							self.getShutdownInfo();
						});
				}
			});
		},
		updateShutdownInfo: function () {
			const self = this;

			self.$refs.queryForm.validate().then(function (pass) {
				if (pass.valid) {
					self.buildQueryStr();
					self.$api
						.patchShutdownInfoApi({
							shutdownId: self.shutdownId,
							startDt: self.startDt,
							endDt: self.endDt,
							shutdownDesc: self.shutdownDesc
						})
						.then(function (ret) {
							Swal.fire({
								icon: 'success',
								text: self.$t('adm.modifySuccess'),
								showCloseButton: true,
								confirmButtonText: self.$t('adm.confirm'),
								buttonStyling: false,
								customClass: {
									confirmButton: 'btn btn-success'
								}
							});
							self.clearForm();
							self.getShutdownInfo();
						});
				}
			});
		},
		deleteShutdownInfo: async function (item) {
			const self = this;
			const result = await self.$bi.confirm(self.$t('adm.confirmDeleteData'));
			if (result.isConfirmed) {
				self.$api
					.deleteShutdownInfoApi({
						shutdownId: item.shutdownId
					})
					.then(function (ret) {
						Swal.fire({
							icon: 'success',
							text: self.$t('adm.deleteSuccess'),
							showCloseButton: true,
							confirmButtonText: self.$t('adm.confirm'),
							buttonStyling: false,
							customClass: {
								confirmButton: 'btn btn-success'
							}
						});
						self.clearForm();
						self.getShutdownInfo();
					});
			}
		},
		buildQueryStr: function () {
			const self = this;
			if (!self.startMin) {
				self.startMin = '00';
			}

			if (!self.endMin) {
				self.endMin = '00';
			}
			self.startDt = self.startDate + ' ' + self.startHr + ':' + self.startMin;
			self.endDt = self.endDate + ' ' + self.endHr + ':' + self.endMin;
		},
		paddingLeft: function (str, length) {
			const self = this;
			str = str + '';
			if (str.length >= length) {
				return str;
			}
			else {
				return self.paddingLeft('0' + str, length);
			}
		},
		isStartDateFuture: function (startDate) {
			return new Date(startDate) > new Date();
		}
	}
};
</script>
