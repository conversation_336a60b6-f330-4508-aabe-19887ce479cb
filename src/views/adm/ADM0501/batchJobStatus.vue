<script setup>
import api from '@/api/apiService';
import VueModal from '@/views/components/model.vue';
import VuePagination from '@/views/components/pagination.vue';
import { onMounted, ref } from 'vue';
import { biModule } from '@/utils/bi/module';

const isOpenModal = ref(false);
// API 用參數
const searchParams = ref({
	objectName: null,
	startDate: null,
	endDate: null,
	status: '--',
	batchStartDate: null,
	batchEndDate: null,
	groupCode: '--'
});
// 畫面顯示用參數
const historyList = ref([]);
// 下拉選單
const statusMenu = ref([]);
const groupMenu = ref([]);
const pageData = ref({
	content: []
});
const pageable = ref({
	page: 0,
	size: 20,
	sort: 'DATA_DT',
	direction: 'DESC'
});

onMounted(() => {
	getStatusMenu();
	getGroupMenu();
});

async function getStatusMenu() {
	const response = await api.getAdmCodeDetailApi({ codeType: 'BATCH_CONTROL_STATUS' });
	statusMenu.value = response.data;
}

async function getGroupMenu() {
	const response = await api.getAdminWmsBatchGroupsApi();
	groupMenu.value = response.data;
}

async function exportExcel() {
	const blob = await api.getExportJobStatusExcelApi(searchParams.value);
	const link = document.createElement('a');
	const url = URL.createObjectURL(blob);
	link.download = '批次程式監控資訊.xls';
	link.href = url;
	document.body.appendChild(link);
	link.click();
	link.remove();
	setTimeout(() => URL.revokeObjectURL(url), 1000);
}

async function searchHistoryHandler(item) {
	historyList.value = [];
	const response = await api.getBatchJobStatusHistoryApi({
		objectName: item.objectName,
		dataDateTime: item.dataDt
	});
	historyList.value = response.data;
	if (!historyList.value?.length) {
		biModule.alert('查無資料');
		return;
	}
	openModal();
}

function closeModal() {
	isOpenModal.value = false;
}

function openModal() {
	isOpenModal.value = true;
}

function gotoPage(page) {
	pageable.value.page = page;
	getPageData(page);
}

async function getPageData(page) {
	let errorType;
	if (
		!isValidDate(searchParams.value.startDate)
		|| !isValidDate(searchParams.value.endDate)
		|| !isValidDate(searchParams.value.batchStartDate)
		|| !isValidDate(searchParams.value.batchEndDate)
	) {
		errorType = 'INVALID_DATE';
	}
	else if (searchParams.value.startDate > searchParams.value.endDate) {
		errorType = 'INVALID_DATA_INTERVAL';
	}
	else if (searchParams.value.batchStartDate > searchParams.value.batchEndDate) {
		errorType = 'INVALID_BATCH_TIME_INTERVAL';
	}

	if (errorType) {
		biModule.alert({
			INVALID_DATE: '請輸入正確日期格式及真實存在日期',
			INVALID_DATA_INTERVAL: '資料日期區間(起)不可大於資料日期區間(迄)。',
			INVALID_BATCH_TIME_INTERVAL: '批次執行日期區間(起)不可大於批次執行日期區間(迄)。'
		}[errorType]);
		return;
	}

	const response = await api.getPagedBatchJobStatusApi(page, pageable.value, searchParams.value);
	pageData.value = response.data;
}

function isValidDate(val) {
	const regex = /^\d{4}-\d{2}-\d{2}$/;
	return !val || regex.test(val);
}
</script>

<template>
	<div class="row">
		<div class="col-12">
			<div class="card card-form">
				<div class="card-body">
					<form>
						<div class="row g-3 align-items-end">
							<div class="col-md-6">
								<label class="form-label">資料日期</label>
								<div class="input-group">
									<input
										id="beginDate"
										v-model="searchParams.startDate"
										type="date"
										name="startDate"
										value=""
										class="form-control"
									>
									<span class="input-group-text">~</span>
									<input
										id="endDate"
										v-model="searchParams.endDate"
										type="date"
										name="endDate"
										value=""
										class="JQ-datepicker form-control"
									>
								</div>
								<div style="height: 25px">
									<!-- <span class="text-danger" v-show="errors.startDate">{{errors.startDate}}</span> -->
								</div>
							</div>
							<div class="col-md-6">
								<label class="form-label">批次執行日期</label>
								<div class="input-group">
									<input
										id="batchStartDate"
										v-model="searchParams.batchStartDate"
										type="date"
										name="batchStartDate"
										value=""
										class="form-control"
									>
									<span class="input-group-text">~</span>
									<input
										id="batchEndDate"
										v-model="searchParams.batchEndDate"
										type="date"
										name="batchEndDate"
										class="JQ-datepicker form-control"
									>
								</div>
								<div style="height: 25px">
									<!-- <span class="text-danger" v-show="errors.batchStartDate">{{errors.batchStartDate}}</span> -->
								</div>
							</div>
							<div class="col-md-3">
								<label class="form-label">程序名稱</label>
								<input
									id="objectName"
									v-model="searchParams.objectName"
									type="text"
									name="objectName"
									class="form-control"
								>
							</div>

							<div class="col-md-3">
								<label class="form-label">結果代碼</label>
								<select
									id="status"
									v-model="searchParams.status"
									name="status"
									class="form-select"
								>
									<option value="--">
										--
									</option>
									<option v-for="status in statusMenu" :value="status.codeValue">
										{{ status.codeName }}
									</option>
								</select>
							</div>
							<div class="col-md-3">
								<label class="form-label">群組代碼</label>
								<select
									id="awc_group_code"
									v-model="searchParams.groupCode"
									name="awc.group_code"
									class="form-select"
								>
									<option value="--">
										--
									</option>
									<option v-for="group in groupMenu" :value="group.groupCode">
										{{ group.groupCode }}
									</option>
								</select>
							</div>

							<div class="col-md-3">
								<button
									type="button"
									class="btn btn-primary btn-glow btn-search"
									@click="getPageData(0)"
								>
									查詢
								</button>
							</div>
						</div>
					</form>
				</div>
			</div>

			<div id="searchRusult">
				<div class="card card-table">
					<div class="card-header">
						<h4>批次狀態監控列表</h4>
						<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-hover text-center">
							<thead>
								<tr>
									<th>資料日期</th>
									<th>群組代碼</th>
									<th class="text-start">
										程式名稱
									</th>
									<th>開始時間</th>
									<th>結束時間</th>
									<th>結果代碼與描述</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in pageData.content">
									<td data-th="資料日期">
										{{ item.dataDt || '--' }}
									</td>
									<td data-th="群組代碼">
										{{ item.groupCode || '--' }}
									</td>
									<td data-th="程式名稱">
										{{ item.objectName || '--' }}
									</td>
									<td data-th="開始時間">
										{{ $filters.formatDateTime(item.stdDt) || '--' }}
									</td>
									<td data-th="結束時間">
										{{ $filters.formatDateTime(item.endDt) || '--' }}
									</td>
									<td data-th="結果代碼與描述">
										<span class="open-modal-button" @click="searchHistoryHandler(item)">{{
											item.codeValue }}：{{
											item.codeName }}</span>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
		<div class="col-12 mt-3 text-end">
			<button class="btn btn-primary btn-lg btn-glow" type="button" @click="exportExcel()">
				產生Excel
			</button>
		</div>
	</div>
	<vue-modal :is-open="isOpenModal" @close="closeModal">
		<template #content="props">
			<div class="modal-dialog modal-dialog-centered">
				<div class="modal-content">
					<div class="modal-header">
						<h4 id="myModal1" class="modal-title">
							結果代碼與描述
						</h4>
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click.prevent="props.close()"
						/>
					</div>
					<div class="modal-body">
						<table class="table table-bordered align-middle">
							<thead>
								<tr>
									<th><span class="TC">執行步驟描述</span></th>
									<th><span class="TC">錯誤訊息描述</span></th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in historyList" :key="item.objectName">
									<td width="50%" class="break-all">
										{{ item.text }}
									</td>
									<td width="50%" class="break-all">
										{{ item.errMsg }}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
</template>

<style lang="css" scoped>
.break-all {
    word-break: break-all;
}

.open-modal-button {
    cursor: pointer;
    text-decoration: none;
    /* color: #1A1C21; */
    color: var(--font-link);
    position: relative;
    transition: 0.5s;
}

.open-modal-button:hover {
    color: var(--table-hover-c);
}

.open-modal-button:before {
    content: "";
    position: absolute;
    transition: 0.3s;
    left: 0;
    bottom: 0;
    height: 1px;
    width: 100%;
    background: var(--table-hover-c);
    transform: scale(0);
}

.open-modal-button:hover:before {
    transform: scale(1);
}
</style>
