<template>
	<div class="row">
		<div class="col-12">
			<vue-bi-tabs :menu-code="'M01-04'" @change-tab="changeTab">
				<template #default="{ id }">
					<component :is="id" />
				</template>
			</vue-bi-tabs>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
import vueBiTabs from '@/views/components/biTabs.vue';
import cfgFPNGeneral from './include/cfgFPNGeneral.vue';
export default {
	components: {
		vueBiTabs,
		'vue-cfg-fpn-general': cfgFPNGeneral
	},
	data: function () {
		return {
			// 畫面顯示用參數
			customTitle: null,
			// 畫面邏輯判斷用參數
			tabCodeTitleMap: {
				'M01-040': '一般共用參數',
				'M01-041': '教育規劃參數',
				'M01-042': '退休規劃參數'
			}
		};
	},
	methods: {
		changeTab: function (tabCode) {
			const self = this;
			self.customTitle = self.tabCodeTitleMap[tabCode];
		}
	}
};
</script>
