<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#card-body">
				<h4>{{ $t('adm.pleaseEnterFollowingData') }}</h4>
				<span class="tx-square-bracket">{{ $t('adm.reqField') }}</span>
			</div>

			<vue-form v-slot="{ errors }" ref="queryForm">
				<div id="card-body" class="card-body collapse show">
					<div class="row g-3 align-items-end">
						<div class="form-group col-lg-6">
							<label class="form-label">CA</label>
							<div class="input-group">
								<select v-model="userCode" name="userCode" class="form-select">
									<option :value="null">
										{{ $t('adm.all') }}
									</option>
									<option v-for="(item, index) in roleUserMenu" :value="item.userCode">
										{{ item.userName }}
									</option>
								</select>
								<div style="height: 25px">
									<span class="text-danger">{{ errors.userCode }}</span>
								</div>
							</div>
						</div>
						<div class="form-group col-lg-6">
							<label class="form-label mg-xl-e-10">{{ $t('adm.branch') }}</label>
							<div class="input-group">
								<select v-model="branCode" name="branCode" class="form-select">
									<option :value="null">
										{{ $t('adm.all') }}
									</option>
									<option v-for="(item, index) in branMenu" :value="item.branCode">
										{{ item.branCode }} {{ item.branName }}
									</option>
								</select>
								<div style="height: 25px">
									<span class="text-danger">{{ errors.branCode }}</span>
								</div>
							</div>
						</div>
						<div class="form-footer">
							<button class="btn btn-primary btn-glow btn-search" @click.prevent="getPageData(0)">
								{{ $t('adm.search') }}
							</button>
							<button class="btn btn-primary btn-glow" @click.prevent="exportPageData()">
								{{ $t('adm.excelDL') }}
							</button>
						</div>
					</div>
				</div>
			</vue-form>
		</div>

		<div class="col-12">
			<div id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('adm.searchResult') }}</h4>
						<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
					</div>
					<template v-if="pageData.totalElements > 0">
						<div class="table-responsive">
							<table class="table table-RWD table-bordered text-center">
								<thead>
									<tr>
										<th class="text-center">
											{{ $t('adm.itemNumber') }}
										</th>
										<th class="text-center">
											{{ $t('adm.caCode') }}
										</th>
										<th class="text-center">
											{{ $t('adm.caName') }}
										</th>
										<th class="text-center">
											{{ $t('adm.branchArea') }}
										</th>
										<th class="text-center">
											{{ $t('adm.branchGroup') }}
										</th>
										<th class="text-center">
											{{ $t('adm.branchCode') }}
										</th>
										<th class="text-center">
											{{ $t('adm.branchName') }}
										</th>
										<th class="text-center">
											{{ $t('adm.uploadUser') }}
										</th>
										<th class="text-center">
											{{ $t('adm.effectiveDate') }}
										</th>
										<th class="text-center">
											{{ $t('adm.configTime') }}
										</th>
									</tr>
								</thead>
								<tbody id="TopTenList">
									<tr v-for="(item, i) in pageData.content">
										<td :data-th="$t('adm.itemNumber')">
											{{ item.orderBy }}
										</td>
										<td :data-th="$t('adm.caCode')">
											{{ item.caUserCode }}
										</td>
										<td :data-th="$t('adm.caName')">
											{{ item.caUserName }}
										</td>
										<td :data-th="$t('adm.branchArea')">
											{{ item.branNameArea }}
										</td>
										<td :data-th="$t('adm.branchGroup')">
											{{ item.codeName }}
										</td>
										<td :data-th="$t('adm.branchCode')">
											{{ item.branCode }}
										</td>
										<td :data-th="$t('adm.branchName')">
											{{ item.branName }}
										</td>
										<td :data-th="$t('adm.uploadUser')">
											{{ item.createBy }} {{ item.userName }}
										</td>
										<td :data-th="$t('adm.effectiveDate')">
											{{ item.validDate }}
										</td>
										<td :data-th="$t('adm.configTime')">
											{{ item.createDt }}
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</template>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import { Form } from 'vee-validate';
import pagination from '@/views/components/pagination.vue';

export default {
	components: {
		'vue-form': Form,
		'vue-pagination': pagination
	},
	data: function () {
		return {
			roleUserMenu: [],
			userCode: null,
			branCode: null,
			branMenu: [],
			roleType: ['CA'],
			roleCode: [],
			pageData: {},
			pageable: {
				page: 0,
				size: 10,
				sort: 'ORDER_BY',
				direction: 'ASC'
			}
		};
	},
	mounted: function () {
		const self = this;
		self.getRoleUser();
		self.getBranListName();
	},
	methods: {
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getRoleUser: function () {
			const self = this;

			self.$api
				.getRoleUserApi({
					roleType: self.roleType
				})
				.then(function (ret) {
					self.roleUserMenu = ret.data;
				});
		},
		getBranListName: function () {
			const self = this;
			self.$api.getBranListNameApi({}).then(function (ret) {
				self.branMenu = ret.data;
			});
		},
		getPageData: function (page) {
			const self = this;
			const url = _.toPageUrl('', page, self.pageable);

			self.$api
				.getCaAccessPageDataApi(
					{
						caCode: self.userCode,
						branCode: self.branCode
					},
					url
				)
				.then(function (ret) {
					self.pageData = ret.data;
				});
		},
		exportPageData: async function () {
			const self = this;
			const fileName = self.$t('adm.caJurisdictionBranchDataFile') + '.xls';

			const ret = await self.$api.getCaAccessExportPageData({
				caCode: self.userCode,
				branCode: self.branCode
			});
			const blob = new Blob([ret]);
			const link = document.createElement('a');
			const url = window.URL.createObjectURL(blob);
			link.download = fileName;
			link.href = url;
			document.body.appendChild(link);
			link.click();
			link.remove();
			setTimeout(() => window.URL.revokeObjectURL(url), 500);
		}
	}
};
</script>
