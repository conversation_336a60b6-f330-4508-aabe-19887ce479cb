<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#card-body">
				<h4>{{ $t('adm.pleaseEnterFollowingData') }}</h4>
				<span class="tx-square-bracket">{{ $t('adm.reqField') }}</span>
			</div>
			<vue-form v-slot="{ errors }" ref="queryForm">
				<div id="card-body" class="card-body collapse show">
					<form>
						<div class="form-row">
							<div class="form-group col-lg-12 col-12">
								<label class="form-label tx-require">{{ $t('adm.uploadFile') }}</label>
								<div class="d-lg-flex align-items-center">
									<vue-field
										ref="uploadFile"
										name="uploadFile"
										type="file"
										class="form-control"
										:label="$t('adm.uploadFile')"
										prop="uploadFile"
										:class="{ 'is-invalid': errors.uploadFile }"
										:rules="{ required_file: true, mbSize: 2, ext: validExts, validateName: symbols }"
										@change="handleChange($event)"
									/>
									<span class="error invalid-feedback">
										{{ errors.uploadFile }}
									</span>
								</div>
								<div class="ms-lg-3">
									({{ $t('adm.pleaseConfirmCSVFile') }})
									<a :href="csvFileUrl" @click.prevent="downloadSample">{{ $t('adm.sampleDocument') }}</a>
								</div>
							</div>
						</div>
						<div class="form-footer">
							<a class="btn btn-primary btn-search btn-upload" @click.prevent="uploadFil">{{ $t('adm.upload') }}</a>
						</div>
					</form>
				</div>
			</vue-form>
		</div>
		<div class="tx-note">
			{{ $t('adm.uploadNote1') }}<br>
			{{ $t('adm.uploadNote2') }}<br>
			{{ $t('adm.uploadNote3') }}<br>
		</div>
		<div v-if="totalCnt > 0" class="card card-table">
			<div class="card-header">
				<h4>{{ $t('adm.uploadResultList') }}</h4>
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-hover text-center">
					<thead>
						<tr>
							<th>{{ $t('adm.status') }}</th>
							<th>{{ $t('adm.validationDescription') }}</th>
							<th style="display: flex; align-items: center; justify-content: center">
								<div class="tx-note">
									{{ $t('adm.withoutHeaderNote') }}
								</div>
								<span style="margin-left: 20px">{{ $t('adm.count') }}</span>
							</th>
							<!-- <th>{{ $t('adm.count') }}</th> -->
						</tr>
					</thead>
					<tbody>
						<tr v-for="item in caAccessFileChkResp">
							<td v-if="item.statusCode == 'S'" :data-th="$t('adm.status')">
								{{ $t('adm.success') }}
							</td>
							<td v-if="item.statusCode == 'F'" :data-th="$t('adm.status')">
								{{ $t('adm.failed') }}
							</td>
							<td :data-th="$t('adm.validationDescription')">
								{{ item.statusMsg }}
							</td>
							<td :data-th="$t('adm.count')">
								{{ item.itemCount }}
							</td>
						</tr>
						<tr class="tx-sum bg-total">
							<td colspan="2" class="text-end">
								{{ $t('adm.total') }}
							</td>
							<td>{{ totalCnt }}</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div class="text-end mt-3">
				<button v-if="isSendBtn" class="btn btn-primary btn-glow" @click="addCaAccess()">
					{{ $t('adm.submit') }}
				</button>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import Swal from 'sweetalert2';
import { Field, Form } from 'vee-validate';
import csvFileUrl from '/resources/adm/CA_config_sample.csv?url';
import { downloadBlob } from '@/utils/downloadBlob';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			uploadFile: null,
			fileTemp: null,
			totalCnt: 0,
			validExts: ['csv', 'xlsx'],
			symbols: ['/', ':', '*', '?', '"', '<', '>', '|'],
			caAccessFileChkResp: [], // 上傳名單list
			isSendBtn: false,
			csvFileUrl
		};
	},
	mounted: function () {
		const self = this;
		// self.gotoPage(0);
	},
	methods: {
		handleChange: function (event) {
			const self = this;
			self.fileTemp = event.target.files[0];
		},
		uploadFil: function () {
			const self = this;
			$.when(self.$refs.queryForm.validate()).then(function (result) {
				if (result.valid) {
					self.caAccessFileChk();
				}
			});
		},
		caAccessFileChk: function () {
			const self = this;
			const formData = new FormData();
			formData.append('fileObject', self.fileTemp);
			self.$refs.queryForm.resetForm();
			self.$refs.uploadFile.$el.value = null;

			self.$api.postCaAccessFileChkApi(formData).then(function (ret) {
				if (ret.data) {
					self.caAccessFileChkResp = ret.data;
					self.totalCnt = _.sumBy(self.caAccessFileChkResp, 'itemCount');
					self.isSendBtn = !_.some(self.caAccessFileChkResp, bean => bean.statusCode === 'F');
				}
			});
		},
		downloadSample: async function () {
			const blob = await fetch(this.csvFileUrl).then(res => res.blob());
			downloadBlob(blob, 'CA管轄分行範例檔.csv');
		},
		addCaAccess: function () {
			const self = this;

			self.$bi.alert = function (message, option) {
				const success = message.includes('成功');
				Swal.fire({
					text: message,
					icon: success ? 'success' : 'error',
					showCloseButton: true,
					confirmButtonText: self.$t('adm.confirm'),
					buttonsStyling: false,
					customClass: {
						confirmButton: 'btn btn-primary'
					}
				}).then((result) => {
					if (result.isConfirmed && success) {
						// var url = self.config.contextPath + '/adm/cfgCa';
						// window.location.href = url;
						router.push('/adm/cfgCa');
					}
				});
			};
			self.$api.postCaAccessApi().then(function (ret) {
				self.$bi.alert(self.$t('adm.updateSuccess'));
			});
		}
	}
};
</script>
