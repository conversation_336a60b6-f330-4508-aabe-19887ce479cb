<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#card-body">
				<h4>請輸入下列資料</h4>
				<span class="tx-square-bracket">為必填欄位</span>
			</div>
			<vue-form v-slot="{errors}" ref="queryForm">
				<div id="card-body" class="card-body collapse show">
					<div class="row g-3 align-items-end">
						<table class="table table-RWD table-horizontal-RWD table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p tx-require">
										通貨膨脹率
									</th>
									<td class="wd-85p">
										<div class="d-inline-block">
											<div class="input-group">
												<vue-field
													ref="inflationInput"
													v-model="inflationRate"
													type="number"
													step="any"
													class="form-control"
													:class="{'is-invalid': errors.inflationRate}"
													name="inflationRate"
													label="通貨膨脹率"
													:rules="{required: true}"
													@input="handleInput($event, 'inflationRate')"
												/>
												<span class="input-group-text">%</span>
												<div style="height: 25px">
													<span v-show="errors.inflationRate" class="text-danger">{{ errors.inflationRate }}</span>
												</div>
											</div>
										</div>
									</td>
								</tr>
								<tr>
									<th class="tx-require">
										無風險利率
									</th>
									<td>
										<div class="d-inline-block">
											<div class="input-group">
												<vue-field
													ref="noRiskInput"
													v-model="riskFreeRate"
													type="number"
													step="any"
													class="form-control"
													:class="{'is-invalid': errors.riskFreeRate}"
													name="riskFreeRate"
													label="無風險利率"
													:rules="{required: true}"
													@input="handleInput($event, 'riskFreeRate')"
												/>
												<span class="input-group-text">%</span>
												<div style="height: 25px">
													<span v-show="errors.riskFreeRate" class="text-danger">{{ errors.riskFreeRate }}</span>
												</div>
											</div>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
						<div class="form-footer">
							<button class="btn btn-primary btn-glow btn-save" :disabled="$_.isBlank(inflationRate) || $_.isBlank(riskFreeRate)" @click.prevent="doUpdate()">
								儲存
							</button>
						</div>
					</div>
				</div>
			</vue-form>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import { Field, Form } from 'vee-validate';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			inflationRate: null,
			riskFreeRate: null,
			id: null
		};
	},
	mounted: function () {
		const self = this;
		self.getAssetMgmParam();
	},
	methods: {
		getAssetMgmParam: function () {
			const self = this;
			self.$api.getAssetMgmParamApi().then(function (ret) {
				if (ret.data) {
					self.inflationRate = ret.data.inflationRate;
					self.riskFreeRate = ret.data.riskFreeRate;
					self.id = ret.data.id;
				}
			});
		},
		doUpdate: function () {
			const self = this;
			self.$refs.queryForm.validate().then(function (pass) {
				if (pass.valid) {
					self.$api.patchAssetMgmParamApi({
						id: self.id,
						inflationRate: self.inflationRate,
						riskFreeRate: self.riskFreeRate
					}).then(function (ret) {
						if (ret.data) {
							self.$bi.alert('更新成功。');
						}
					});
				}
			});
		},
		handleInput(event, variableName) {
			let value = event.target.value;
			let cursorPosition = event.target.selectionStart;

			// 禁止為負數
			if (value.startsWith('-')) {
				value = value.replace('-', '');
				cursorPosition -= 1;
			}

			// 允許單獨的小數點
			if (value === '.') {
				event.target.value = '0.';
				this[variableName] = '0.';
				return;
			}

			// 清除前導零，但允許單獨的小數點
			if (value.startsWith('0') && value[1] !== '.' && value.length > 1) {
				value = value.replace(/^0+/, '');
				cursorPosition -= 1;
			}

			// 自動補充最前方的小數點前的 0
			if (value[0] === '.') {
				value = '0' + value;
				cursorPosition += 1;
			}

			//  限制整數與小數位數 檢查小數點後的位數
			const parts = value.split('.');

			let intPart = parts[0];
			let decimalPart = parts[1] || '';

			if (intPart.length > 4) {
				intPart = intPart.slice(0, 4);
				cursorPosition = Math.min(cursorPosition, 4);
			}

			if (decimalPart.length > 4) {
				decimalPart = decimalPart.slice(0, 4);
			}

			value = decimalPart ? `${intPart}.${decimalPart}` : intPart;

			this.$nextTick(() => {
				this[variableName] = value;
				event.target.setSelectionRange(cursorPosition, cursorPosition);
			});
		}
	}
};
</script>
