<template>
	<div class="row">
		<div class="col-12">
			<div class="tab-nav-main">
				<ul class="nav nav-pills">
					<li class="nav-item" @click.prevent="changeTab(1)">
						<a class="nav-link" :class="{ active: tabCode == 1 }" href="#">{{ $t('adm.caJurisdictionBranchSetup') }}</a>
					</li>
					<li class="nav-item" @click.prevent="changeTab(2)">
						<a class="nav-link" :class="{ active: tabCode == 2 }" href="#">{{ $t('adm.caJurisdictionBranchQuery') }}</a>
					</li>
				</ul>
				<div class="tab-content">
					<vue-cfg-ca-fp-upload v-if="tabCode == 1" :title="$t('adm.caJurisdictionBranchSetup')" />
					<vue-cfg-ca-fp-search v-if="tabCode == 2" :title="$t('adm.caJurisdictionBranchQuery')" />
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import vueCfgCaFpUpload from '@/views/adm/ADM0206/include/cfgCaFpUpload.vue';
import vueCfgCaFpSearch from '@/views/adm/ADM0206/include/cfgCaFpSearch.vue';

export default {
	components: {
		vueCfgCaFpUpload,
		vueCfgCaFpSearch
	},
	data: function () {
		return {
			// 畫面顯示用參數
			title: null,
			// 畫面邏輯判斷用參數
			tabCode: 0,
			tabs: [
				{ tabCode: 1, label: () => this.$t('adm.caJurisdictionBranchSetup') },
				{ tabCode: 2, label: () => this.$t('adm.caJurisdictionBranchQuery') }
			]
		};
	},
	mounted: function () {
		const self = this;
		self.tabCode = self.tabs[0].tabCode;
		self.title = self.tabs[0].label();
	},
	methods: {
		changeTab: function (tabCode) {
			const self = this;
			self.tabCode = tabCode;
			for (let i = 0; i < self.tabs.length; i++) {
				const tab = self.tabs[i];
				if (tab.tabCode == tabCode) {
					self.title = tab.label();
				}
			}
		}
	}
};
</script>
