<template>
	<div class="row">
		<div class="col-12">
			<div class="card card-form">
				<div class="card-body">
					<div class="row g-3 align-items-end">
						<div class="col-md-6">
							<label class="form-label">{{ $t('adm.eventNotificationCategory') }}</label>
							<select
								id="wobTdItem"
								v-model="tdCat1Code"
								name="tdCat1Code"
								class="form-select"
							>
								<option value="">
									{{ $t('adm.all') }}
								</option>
								<option v-for="menu in tdItemCat1Menu" :value="menu.tdCat1Code">
									{{ menu.tdCat1Name }}
								</option>
							</select>
						</div>
						<div class="col-md-2">
							<button class="btn btn-primary btn-glow btn-search" @click="getTdItems">
								{{ $t('adm.search') }}
							</button>
						</div>
					</div>
				</div>
			</div>

			<div v-if="showYn" id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('adm.eventNotificationList') }}</h4>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-hover text-center">
							<thead>
								<tr>
									<th>{{ $t('adm.eventNotificationCategory') }}</th>
									<th class="text-start">
										{{ $t('adm.eventCategory') }}
									</th>
									<th>{{ $t('adm.executionStatus') }}</th>
									<th>{{ $t('adm.maintainer') }}</th>
									<th>{{ $t('adm.modificationDate') }}</th>
									<th>{{ $t('adm.daysInAdvanceToSendEvent') }}</th>
									<th>{{ $t('adm.reminderDays') }}</th>
									<th>{{ $t('adm.statusChange') }}</th>
								</tr>
							</thead>
							<tr v-for="item in tdItems">
								<td :data-th="$t('adm.eventNotificationCategory')">
									{{ item.tdCat1Name }}
								</td>
								<td :data-th="$t('adm.eventCategory')" class="text-start">
									{{ item.itemName }}
								</td>
								<td :data-th="$t('adm.executionStatus')">
									<span v-if="item.activeYn == 'Y'">{{ $t('adm.normalExecution') }}</span>
									<span v-if="item.activeYn == 'N'">{{ $t('adm.stopped') }}</span>
								</td>
								<td :data-th="$t('adm.maintainer')">
									{{ item.modifyBy }} {{ item.userName }}
								</td>
								<td :data-th="$t('adm.modificationDate')">
									{{ item.modifyDt }}
								</td>
								<td :data-th="$t('adm.daysInAdvanceToSendEvent')">
									<input
										v-model="item.earlySendDay"
										type="number"
										class="form-control wd-75"
										maxlength="10"
										:disabled="isReview(item)"
										@input="dataChanged(item)"
									>
								</td>
								<td :data-th="$t('adm.reminderDays')">
									<input
										v-model="item.expProcDay"
										type="number"
										class="form-control wd-75"
										maxlength="10"
										:disabled="isReview(item)"
										@input="dataChanged(item)"
									>
								</td>
								<td :data-th="$t('adm.statusChange')">
									<div class="form-check form-check-inline">
										<input
											v-model="item.activeYn"
											class="form-check-input"
											type="radio"
											:name="item.itemCode + 'R'"
											:value="'Y'"
											:disabled="isReview(item)"
											@change="dataChanged(item)"
										>
										<label class="form-check-label">{{ $t('adm.execute') }}</label>
									</div>
									<div class="form-check form-check-inline">
										<input
											v-model="item.activeYn"
											class="form-check-input"
											type="radio"
											:name="item.itemCode + 'R'"
											:value="'N'"
											:disabled="isReview(item)"
											@change="dataChanged(item)"
										>
										<label class="form-check-label">{{ $t('adm.disable') }}</label>
									</div>
								</td>
							</tr>
						</table>
					</div>
				</div>
				<p class="tx-note">
					{{ $t('adm.eventNotificationNote') }}
				</p>
				<div class="text-end">
					<input
						id="incomeAnchor"
						name=""
						type="button"
						class="btn btn-primary btn-lg btn-glow"
						:value="$t('adm.save')"
						@click="saveFromUpdate()"
					>
				</div>
			</div>
		</div>

		<div v-if="showYn" id="searchResult">
			<div class="card card-table">
				<div class="card-header">
					<h4>事件通知列表</h4>
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-hover text-center">
						<thead>
							<tr>
								<th>事件通知類別</th>
								<th class="text-start">
									事件類別
								</th>
								<th>執行狀態</th>
								<th>維護人員</th>
								<th>異動日期</th>
								<th>提前幾天<br>發送事件</th>
								<th>提醒天數</th>
								<th>狀態變更</th>
							</tr>
						</thead>
						<tr v-for="item in tdItems">
							<td data-th="事件通知類別">
								{{ item.tdCat1Name }}
							</td>
							<td data-th="事件類別" class="text-start">
								{{ item.itemName }}
							</td>
							<td data-th="執行狀態">
								<span v-if="item.activeYn == 'Y'">正常執行</span>
								<span v-if="item.activeYn == 'N'">已停止</span>
							</td>
							<td data-th="維護人員">
								{{ item.modifyBy }} {{ item.userName }}
							</td>
							<td data-th="審核狀態">
								{{ item.modifyDt }}
							</td>
							<td data-th="提前幾天 發送事件">
								<input
									v-model="item.earlySendDay"
									type="number"
									class="form-control wd-75"
									maxlength="10"
									:disabled="isReview(item)"
									@input="dataChanged(item)"
								>
							</td>
							<td data-th="提醒天數">
								<input
									v-model="item.expProcDay"
									type="number"
									class="form-control wd-75"
									maxlength="10"
									:disabled="isReview(item)"
									@input="dataChanged(item)"
								>
							</td>
							<td data-th="狀態變更">
								<div class="form-check form-check-inline">
									<input
										v-model="item.activeYn"
										class="form-check-input"
										type="radio"
										:name="item.itemCode + 'R'"
										:value="'Y'"
										:disabled="isReview(item)"
										@change="dataChanged(item)"
									>
									<label class="form-check-label">執行</label>
								</div>
								<div class="form-check form-check-inline">
									<input
										v-model="item.activeYn"
										class="form-check-input"
										type="radio"
										:name="item.itemCode + 'R'"
										:value="'N'"
										:disabled="isReview(item)"
										@change="dataChanged(item)"
									>
									<label class="form-check-label">停用</label>
								</div>
							</td>
						</tr>
					</table>
				</div>
			</div>
			<p class="tx-note">
				若設定「提前幾天發送事件」欄位，須為可提前知道發送日期的事件才會有效。
			</p>
			<div class="text-end">
				<input
					id="incomeAnchor"
					name=""
					type="button"
					class="btn btn-primary btn-lg btn-glow"
					value="儲存"
					@click="saveFromUpdate()"
				>
			</div>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
import Swal from 'sweetalert2';

export default {
	data: function () {
		return {
			showYn: false,
			// API 用參數
			tdCat1Code: '',
			// 下拉選單
			tdItemCat1Menu: [],
			// API邏輯判斷用參數
			changedTdItems: [],
			// 主要顯示資料
			tdItems: []
		};
	},
	mounted: function () {
		const self = this;
		self.getTdItemCat1Menu();
		// self.getTdItems();
	},
	methods: {
		getTdItemCat1Menu: function () {
			const self = this;
			self.$api.getTdItemCat1MenuApi().then(function (ret) {
				self.tdItemCat1Menu = ret.data;
			});
		},
		getTdItems: function () {
			const self = this;
			self.$api
				.getTdItemsApi({
					tdCat1Code: self.tdCat1Code
				})
				.then(function (ret) {
					self.tdItems = ret.data;
					self.showYn = true;
				});
		},
		dataChanged: function (changedItem) {
			const self = this;
			self.changedTdItems.forEach(function (item, index, object) {
				if (item.itemCode == changedItem.itemCode) {
					object.splice(index, 1);
				}
			});
			self.changedTdItems.push(changedItem);
		},
		isReview: function (item) {
			const self = this;
			if (item.status == 'P') {
				return true;
			}
			return false;
		},
		saveFromUpdate: function () {
			const self = this;
			let isPassValidate = true;
			self.changedTdItems.forEach(function (item, index, object) {
				if (item.earlySendDay < 0 || item.earlySendDay > 2000 || item.expProcDay < 0 || item.expProcDay > 2000) {
					Swal.fire({
						icon: 'error',
						text: this.$t('adm.validationError1to2000'),
						showCloseButton: true,
						confirmButtonText: this.$t('adm.confirm'),
						buttonStyling: false,
						customClass: {
							confirmButton: 'btn btn-danger'
						}
					});
					isPassValidate = false;
					return;
				}
			});

			if (!isPassValidate) {
				return;
			}
			self.$api.patchTdItemsApi(self.changedTdItems).then(function (ret) {
				Swal.fire({
					icon: 'success',
					text: self.$t('adm.saveSuccess'),
					showCloseButton: true,
					confirmButtonText: self.$t('adm.confirm'),
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-success'
					}
				});
				self.getTdItems();
			});
		}
	}
};
</script>
