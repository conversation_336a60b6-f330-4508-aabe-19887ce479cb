<template>
	<div class="row">
		<div class="col-12">
			<div class="card card-form-collapse">
				<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
					<h4>{{ $t('adm.personalDeputySetting') }}</h4>
				</div>
				<div id="formsearch1" class="card-body collapse show">
					<vue-form v-slot="{ errors }" ref="loginDeputy">
						<table class="table table-bordered">
							<tr>
								<th class="wd-12p">
									{{ $t('adm.userCode') }}
								</th>
								<th class="wd-8p">
									{{ $t('adm.userFullName') }}
								</th>
								<th class="wd-12p">
									{{ $t('adm.deputyPerson') }}
								</th>
								<th class="wd-22p">
									{{ $t('adm.deputyStartDate') }}
								</th>
								<th class="wd-22p">
									{{ $t('adm.deputyEndDate') }}
								</th>
								<th class="wd-7p">
									{{ $t('adm.deputyDays') }}
								</th>
								<th class="wd-7p">
									{{ $t('adm.executeCol') }}
								</th>
							</tr>
							<tr>
								<td>
									<span class="tx-16">{{ loginUserCode }}</span>
								</td>
								<td>
									<span class="tx-16">{{ loginUserName }}</span>
								</td>
								<td>
									<div class="input-group">
										<vue-field
											id="deputyUserCode"
											v-model="loginDeputyUserCode"
											type="text"
											name="loginDeputyUserCode"
											class="form-control"
											:label="$t('adm.deputyPersonCode')"
											:disabled="disableDeputy"
											data-vv-scope="loginDeputyWatch"
											@blur="onLoginDeputyUserCodeInput"
										/>
									</div>

									<div style="height: 25px">
										<span>{{ loginDeputyUserName }}</span>
										<span v-show="errors.loginDeputyUserCode" class="text-danger">{{ errors.loginDeputyUserCode }}</span>
									</div>
								</td>
								<td>
									<div class="input-group">
										<span class="input-group-text">{{ $t('adm.dateLabel') }}</span>
										<vue-field
											id="divStdDt0"
											v-model="loginStdDt"
											type="date"
											name="loginStdDt"
											:class="{ 'is-invalid': errors.loginStdDt }"
											class="form-control wd-30p-f mn-wd-120-f"
											:disabled="disableDeputy"
											:label="$t('adm.deputyStartDate')"
											rules="required"
											data-vv-scope="loginDeputyWatch"
											:min="nowDt"
										/>
										<span class="input-group-text">{{ $t('adm.timeLabel') }}</span>
										<vue-field
											id="sesBegHr"
											v-model="loginStdHr"
											as="select"
											name="loginStdHr"
											class="form-select"
											data-inline="true"
											:disabled="disableDeputy"
											:label="$t('adm.deputyStartTime')"
											rules="required"
											data-vv-scope="loginDeputyWatch"
										>
											<option v-for="time in generateTimeOptions()" :key="time" :value="time">
												{{ time }}
											</option>
										</vue-field>
									</div>
									<div style="height: 25px">
										<span class="text-danger">{{ errors.loginStdDt }}</span>
										<span class="text-danger">{{ errors.loginStdHr }}</span>
									</div>
								</td>
								<td>
									<div class="input-group">
										<span class="input-group-text">{{ $t('adm.dateLabel') }}</span>
										<vue-field
											id="divEndDt0"
											v-model="loginEndDt"
											type="date"
											name="loginEndDt"
											class="form-control wd-30p-f mn-wd-120-f"
											:disabled="disableDeputy"
											:label="$t('adm.deputyEndDate')"
											rules="required"
											data-vv-scope="loginDeputyWatch"
											:min="nowDt"
										/>
										<span class="input-group-text">{{ $t('adm.timeLabel') }}</span>
										<vue-field
											id="sesEndHr"
											v-model="loginEndHr"
											as="select"
											name="loginEndHr"
											class="form-select"
											data-inline="true"
											:disabled="disableDeputy"
											:label="$t('adm.deputyEndTime')"
											rules="required"
											data-vv-scope="loginDeputyWatch"
										>
											<option v-for="time in generateTimeOptions()" :key="time" :value="time">
												{{ time }}
											</option>
										</vue-field>
									</div>
									<div style="height: 25px">
										<span v-show="errors.loginEndDt" class="text-danger">{{ errors.loginEndDt }}</span>
										<span v-show="errors.loginEndHr" class="text-danger">{{ errors.loginEndHr }}</span>
									</div>
								</td>
								<td>
									{{ loginTotDepDays }}
								</td>
								<td>
									<div>
										<button
											v-if="!hasDeputy"
											role="button"
											class="btn btn-primary btn-glow btn-ok "
											@click.prevent="doInsertLoginDeputy()"
										>
											{{ $t('adm.confirm') }}
										</button>
									</div>
									<div>
										<button
											v-if="hasDeputy && !isDeputyStarted"
											role="button"
											class="btn btn-primary btn-glow btn-ok"
											@click.prevent="cancelLoginDeputy()"
										>
											{{ $t('adm.terminateDeputy') }}
										</button>
									</div>
								</td>
							</tr>
						</table>
					</vue-form>
				</div>
			</div>

			<div v-if="isRmMgr || loginRoleType == 'HQ'" id="editDiv">
				<div class="card card-form-collapse">
					<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch2">
						<h4>{{ $t('adm.subordinateDeputySetting') }}</h4>
					</div>
					<div id="formsearch2" class="card-body collapse show">
						<vue-form v-slot="{ errors }" ref="deputy">
							<table class="table table-bordered">
								<tr>
									<th class="wd-12p">
										{{ $t('adm.userCode') }}
									</th>
									<th class="wd-8p">
										{{ $t('adm.userFullName') }}
									</th>
									<th class="wd-12p">
										{{ $t('adm.deputyPerson') }}
									</th>
									<th class="wd-22p">
										{{ $t('adm.deputyStartDate') }}
									</th>
									<th class="wd-22p">
										{{ $t('adm.deputyEndDate') }}
									</th>
									<th class="wd-7p">
										{{ $t('adm.deputyDays') }}
									</th>
									<th class="wd-7p">
										{{ $t('adm.executeCol') }}
									</th>
								</tr>
								<tr>
									<td>
										<span class="tx-16">{{ userCode }}</span>
									</td>
									<td>
										<span class="tx-16">{{ userName }}</span>
									</td>
									<td>
										<div class="input-group">
											<vue-field
												id="deputyUserCode"
												v-model="deputyUserCode"
												type="text"
												name="deputyUserCode"
												class="form-control"
												:label="$t('adm.deputyPersonCode')"
												:disabled="!hasUpdateItem"
												data-vv-scope="deputyWatch"
												@blur="onDeputyUserCodeInput"
											/>
										</div>

										<div style="height: 25px">
											<span>{{ deputyUserName }}</span>
											<span v-show="errors.deputyUserCode" class="text-danger">{{ errors.deputyUserCode }}</span>
										</div>
									</td>
									<td>
										<div class="input-group">
											<span class="input-group-text">{{ $t('adm.dateLabel') }}</span>
											<vue-field
												id="divStdDt0"
												v-model="stdDt"
												type="date"
												name="stdDt"
												:class="{ 'is-invalid': errors.stdDt }"
												class="form-control wd-30p-f mn-wd-120-f"
												:disabled="!hasUpdateItem"
												:label="$t('adm.deputyStartDate')"
												rules="required"
												data-vv-scope="deputyWatch"
												:min="nowDt"
											/>
											<span class="input-group-text">{{ $t('adm.timeLabel') }}</span>
											<vue-field
												id="sesBegHr"
												v-model="stdHr"
												as="select"
												name="stdHr"
												class="form-select"
												data-inline="true"
												:disabled="!hasUpdateItem"
												:label="$t('adm.deputyStartTime')"
												rules="required"
												data-vv-scope="deputyWatch"
											>
												<option v-for="time in generateTimeOptions()" :key="time" :value="time">
													{{ time }}
												</option>
											</vue-field>
										</div>
										<div style="height: 25px">
											<span class="text-danger">{{ errors.stdDt }}</span>
											<span class="text-danger">{{ errors.stdHr }}</span>
										</div>
									</td>
									<td>
										<div class="input-group">
											<span class="input-group-text">{{ $t('adm.dateLabel') }}</span>
											<vue-field
												id="divEndDt0"
												v-model="endDt"
												type="date"
												name="endDt"
												class="form-control wd-30p-f mn-wd-120-f"
												:disabled="!hasUpdateItem"
												:label="$t('adm.deputyEndDate')"
												rules="required"
												data-vv-scope="deputyWatch"
												:min="nowDt"
											/>
											<span class="input-group-text">{{ $t('adm.timeLabel') }}</span>
											<vue-field
												id="sesEndHr"
												v-model="endHr"
												as="select"
												name="endHr"
												class="form-select"
												data-inline="true"
												:disabled="!hasUpdateItem"
												:label="$t('adm.deputyEndTime')"
												rules="required"
												data-vv-scope="deputyWatch"
											>
												<option v-for="time in generateTimeOptions()" :key="time" :value="time">
													{{ time }}
												</option>
											</vue-field>
										</div>
										<div style="height: 25px">
											<span v-show="errors.endDt" class="text-danger">{{ errors.endDt }}</span>
											<span v-show="errors.endHr" class="text-danger">{{ errors.endHr }}</span>
										</div>
									</td>
									<td>
										{{ updateItem != null ? updateItem.totDepDays : '' }}
									</td>
									<td>
										<div>
											<button
												v-if="userCode"
												:disabled="updateItem.updated"
												role="button"
												class="btn btn-primary btn-glow btn-ok"
												@click.prevent="doInsertDeputy()"
											>
												{{ $t('adm.confirm') }}
											</button>
											<button
												v-if="userCode"
												role="button"
												class="btn btn-primary btn-glow btn-ok"
												@click.prevent="clearDeputyForm()"
											>
												{{ $t('adm.cancel') }}
											</button>
										</div>
									</td>
								</tr>
							</table>
						</vue-form>
					</div>
				</div>
				<div class="divider" />
				<div v-if="!isRmMgr || loginRoleType == 'HQ'" class="form-group">
					<label class="form-label">{{ $t('adm.organization') }}</label>
					<div class="col-lg-2 col-12">
						<select
							id="areaBranCode"
							v-model="groupCode"
							name="areaBranCode"
							class="form-select"
						>
							<option value="" selected>
								{{ $t('adm.all') }}
							</option>
							<option v-for="item in areaList" :value="item.branCode">
								{{ item.branCode }} {{ item.branName }}
							</option>
						</select>
					</div>
						&nbsp;
					<div class="col-lg-2 col-12">
						<select
							id="branCode"
							v-model="branCode"
							name="branCode"
							class="form-select"
						>
							<option value="" selected>
								{{ $t('adm.all') }}
							</option>
							<option v-for="item in branList" :value="item.branCode">
								{{ item.branCode }} {{ item.branName }}
							</option>
						</select>
					</div>
						&nbsp;
					<button
						type="button"
						class="btn btn-icon btn-glow"
						data-bs-toggle="tooltip"
						data-bs-original-title=""
						@click.prevent="getUnderUserDeputies()"
					>
						<i class="bi bi-search" />
					</button>
				</div>
			</div>

			<div v-if="isRmMgr || loginRoleType == 'HQ'" class="card card-table">
				<div class="card-header">
					<h4>{{ $t('adm.subordinateDeputyList') }}</h4>
					<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
				</div>
				<template v-if="pageData.totalElements > 0">
					<div class="table-responsive">
						<table id="tblRangeData" class="table table-RWD table-hover table-bordered">
							<thead>
								<tr>
									<th width="18%">
										{{ $t('adm.branch') }}
									</th>
									<th width="10%">
										{{ $t('adm.userCode') }}
									</th>
									<th width="10%">
										{{ $t('adm.userFullName') }}
									</th>
									<th class="text-start" width="15%">
										{{ $t('adm.userRole') }}
									</th>
									<th width="13%">
										{{ $t('adm.deputy') }}
									</th>
									<th width="14%">
										{{ $t('adm.deputyStartDate') }}
									</th>
									<th width="14%">
										{{ $t('adm.deputyEndDate') }}
									</th>
									<th width="14%">
										{{ $t('adm.deputyDays') }}
									</th>
									<th width="6%">
										{{ $t('adm.executeCol') }}
									</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(item) in pageData.content">
									<td :data-th="$t('adm.branch')">
										{{ item.branCode }} {{ item.branName }}
									</td>
									<td :data-th="$t('adm.userCode')">
										{{ item.userCode }}
									</td>
									<td :data-th="$t('adm.userFullName')">
										{{ item.userName }}
									</td>
									<td :data-th="$t('adm.userRole')" class="text-start" style="max-width: 100px; overflow: hidden; white-space: normal">
										{{ item.userRoleNames }}
									</td>
									<td :data-th="$t('adm.deputy')">
										{{ item.deputyUserCode }} {{ item.deputyUserName }}
									</td>
									<td :data-th="$t('adm.deputyStartDate')">
										{{ item.stdDt }}
									</td>
									<td :data-th="$t('adm.deputyEndDate')">
										{{ item.endDt }}
									</td>
									<td :data-th="$t('adm.deputyDays')">
										{{ $filters.formatAmt(item.totDepDays) }}
									</td>
									<td :data-th="$t('adm.executeCol')">
										<button
											v-if="item.deputyUserCode == null"
											type="button"
											class="btn btn-info btn-icon btn-glow"
											data-bs-toggle="tooltip"
											:data-bs-original-title="$t('adm.editTooltip')"
											@click="doUpdateDeputies(item)"
										>
											<i class="bi bi-pen" />
										</button>
										<button
											v-if="item.deputyUserCode != null"
											type="button"
											class="btn btn-danger btn-icon btn-glow"
											data-bs-toggle="tooltip"
											:data-bs-original-title="$t('adm.terminateDeputy')"
											@click="cancelDeputy(item)"
										>
											<i class="bi bi-slash-circle" />
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</template>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import Swal from 'sweetalert2';
import moment from 'moment';
import { Field, Form } from 'vee-validate';
import pagination from '@/views/components/pagination.vue';
import userCodeComplement from '../../../utils/mixin/userCodeComplement';

export default {
	components: {
		'vue-pagination': pagination,
		'vue-form': Form,
		'vue-field': Field
	},
	mixins: [userCodeComplement],
	data: function () {
		return {
			// API 用參數
			loginUserCode: null,
			loginUserName: null,
			loginRoleType: null,
			loginCreateByName: null,
			loginStdDt: null,
			loginStdHr: '07:00',
			loginEndDt: null,
			loginEndHr: '07:00',
			loginTotDepDays: null,
			loginDeputyUserCode: null,
			loginDeputyUserName: null,
			loginDeputyBranCode: null,

			updateItem: null,
			userCode: null,
			userName: null,
			totDepDays: null,
			stdDt: null,
			stdHr: '07:00',
			endDt: null,
			endHr: '07:00',
			deputyUserCode: null,
			deputyUserName: null,
			deputyBranCode: null,

			nowDt: moment().format('YYYY-MM-DD'),

			groupCode: '',
			branCode: '',

			// 畫面邏輯判斷用參數
			hasDeputy: false,
			isRmMgr: false,
			isDeputyStarted: false,
			disableDeputy: false,
			// API邏輯判斷用參數
			hasUpdateItem: false,

			// 畫面顯示用參數
			deputiesRms: [],
			branList: [],
			areaList: [],
			// 主要顯示資料
			underUserDeputies: [],
			pageData: {},
			pageable: {
				page: 0,
				size: 10,
				sort: 'user_code',
				direction: 'ASC'
			}
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo'])
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				const self = this;
				if (newVal) {
					self.loginUserName = newVal.userName;
					self.loginUserCode = newVal.userCode;
					self.loginRoleType = newVal.roleType;
					self.checkIsDeputiesRmMgr();
				}
			}
		},
		updateItem: function () {
			const self = this;
			if (self.updateItem != null) {
				self.hasUpdateItem = true;
			}
			else {
				self.hasUpdateItem = false;
			}
		},
		groupCode: function (newVal) {
			this.branCode = '';
			this.branList = [];
			if (newVal != null) {
				this.getBranList();
			}
		}
	},
	mounted: function () {
		const self = this;
		self.getLoginUserDeputies();
		self.getAreaList();
	},
	methods: {
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getUnderUserDeputies(page);
		},
		getLoginUserDeputies: async function () {
			const self = this;
			await self.$api.getUserDeputiesApi().then(function (ret) {
				if (ret.data != null) {
					const nowData = new Date();
					const stdDt = new Date(ret.data.stdDt);
					const endDt = new Date(ret.data.endDt);
					self.disableDeputy = true;
					if (!_.isEmpty(ret.data.stdDt)) {
						self.hasDeputy = true;
						if (stdDt.getTime() < nowData.getTime()) {
							self.isDeputyStarted = true;
						}
					}

					if (_.isEmpty(ret.data.endDt) || endDt.getTime() < nowData.getTime()) {
						self.hasDeputy = false;
						self.isDeputyStarted = false;
					}

					self.loginDeputyUserCode = ret.data.deputyUserCode;
					self.loginStdDt = ret.data.stdDate;
					self.loginStdHr = ret.data.stdTime;
					self.loginEndDt = ret.data.endDate;
					self.loginEndHr = ret.data.endTime;
					self.loginTotDepDays = ret.data.totDepDays;
					self.getAdmUsersList({
						userCode: self.loginDeputyUserCode,
						targetName: 'loginDeputyUserName'
					});
				}
			});
		},
		checkIsDeputiesRmMgr: function () {
			const self = this;
			self.$api.getDeputiesRmMgrApi().then(function (ret) {
				self.deputiesRms = ret.data;
				if (self.deputiesRms.includes(self.userInfo.roleCode)) {
					self.isRmMgr = true;
					self.getUnderUserDeputies();
				}
			});
		},
		getUnderUserDeputies: function (page) {
			const self = this;
			const url = _.toPageUrl('', page, self.pageable);
			self.$api
				.getUnderUserDeputiesPageDataApi(
					{
						groupCode: self.groupCode,
						branCode: self.branCode
					},
					url
				)
				.then(function (ret) {
					self.pageData = ret.data;
				});
		},
		// init update form
		doUpdateDeputies: function (item) {
			const self = this;
			self.updateItem = item;
			self.userCode = item.userCode;
			self.userName = item.userName;
			self.totDepDays = item.totDepDays;
			self.stdDt = item.stdDate;
			self.stdHr = item.stdTime;
			self.endDt = item.endDate;
			self.endHr = item.endTime;
		},
		// update
		doInsertLoginDeputy: function () {
			const self = this;

			const stdDt = new Date(self.loginStdDt + ' ' + self.loginStdHr + ':00');
			const endDt = new Date(self.loginEndDt + ' ' + self.loginEndHr + ':00');
			if (!self.checkDateRange(stdDt, endDt)) {
				return;
			}
			if (self.loginDeputyUserCode == self.loginUserCode) {
				Swal.fire({
					icon: 'error',
					text: self.$t('adm.deputyNotSelf'),
					showCloseButton: true,
					confirmButtonText: self.$t('adm.confirm'),
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}
			self.$refs.loginDeputy.validate().then(function (pass) {
				if (pass.valid) {
					self.checkLoginDeputyUserCode();
				}
			});
		},
		// input deputy user check
		checkLoginDeputyUserCode: function () {
			const self = this;
			self.$api
				.getDeputyUserCodeApi({
					deputyUserCode: self.loginDeputyUserCode
				})
				.then(function (ret) {
					if (ret.data == null) {
						Swal.fire({
							icon: 'error',
							text: self.$t('adm.invalidDeputyCode'),
							showCloseButton: true,
							confirmButtonText: self.$t('adm.confirm'),
							buttonStyling: false,
							customClass: {
								confirmButton: 'btn btn-danger'
							}
						});
						return;
					}
					else {
						self.loginDeputyBranCode = ret.data.deputyBranCode;
						self.chkValidDeputiesTime();
					}
				});
		},
		insertLoginDeputy: function () {
			const self = this;

			const stdDt = self.loginStdDt + ' ' + self.loginStdHr;
			const endDt = self.loginEndDt + ' ' + self.loginEndHr;

			self.$api
				.postInsertDeputyApi({
					userCode: self.loginUserCode,
					roleMetadata: self.userInfo.roleCode,
					branCode: self.userInfo.branCode,
					deputyUserCode: self.loginDeputyUserCode,
					deputyBranCode: self.loginDeputyBranCode,
					stdDt: stdDt,
					endDt: endDt
				})
				.then(function () {
					Swal.fire({
						icon: 'success',
						text: self.$t('adm.addDeputySuccess'),
						showCloseButton: true,
						confirmButtonText: self.$t('adm.confirm'),
						buttonStyling: false,
						customClass: {
							confirmButton: 'btn btn-success'
						}
					});
					self.getLoginUserDeputies();
				});
		},
		chkValidDeputiesTime: function () {
			const self = this;

			const stdDt = self.loginStdDt + ' ' + self.loginStdHr;
			const endDt = self.loginEndDt + ' ' + self.loginEndHr;

			self.$api
				.getchkValidDeputiesTimeApi({
					stdDt: stdDt,
					endDt: endDt
				})
				.then(async function (ret) {
					if (ret.data.validYn == 'N') {
						Swal.fire({
							icon: 'error',
							text: self.$t('adm.duplicateTimeSlot'),
							showCloseButton: true,
							confirmButtonText: self.$t('adm.confirm'),
							buttonStyling: false,
							customClass: {
								confirmButton: 'btn btn-danger'
							}
						});
						return;
					}
					else {
						const checkLoginStdDtBusinessDt = await self.doCheckIsBusinessDt(self.loginStdDt);
						const checkLoginEndDtBusinessDt = await self.doCheckIsBusinessDt(self.loginEndDt);
						if (!_.isBlank(checkLoginStdDtBusinessDt)) {
							Swal.fire({
								icon: 'error',
								text: self.$t('adm.startDateNotBusinessDay'),
								showCloseButton: true,
								confirmButtonText: self.$t('adm.confirm'),
								buttonStyling: false,
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}
						if (!_.isBlank(checkLoginEndDtBusinessDt)) {
							Swal.fire({
								icon: 'error',
								text: self.$t('adm.endDateNotBusinessDay'),
								showCloseButton: true,
								confirmButtonText: self.$t('adm.confirm'),
								buttonStyling: false,
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}
						self.insertLoginDeputy();
					}
				});
		},
		doCheckIsBusinessDt: async function (date) {
			const self = this;
			const ret = await self.$api.getdoCheckIsBusinessDtApi({ date: date });
			return ret.data;
		},
		doInsertDeputy: function () {
			const self = this;
			self.$refs.deputy.validate().then(function (pass) {
				if (pass.valid) {
					self.checkDeputyUserCode();
				}
			});
		},
		checkDeputyUserCode: function () {
			const self = this;
			const item = self.updateItem;

			self.$api
				.getcheckDeputyUserCodeApi({
					userCode: item.userCode,
					deputyUserCode: self.deputyUserCode
				})
				.then(async function (ret) {
					if (ret.data == null) {
						Swal.fire({
							icon: 'error',
							text: self.$t('adm.invalidDeputyCode'),
							showCloseButton: true,
							confirmButtonText: self.$t('adm.confirm'),
							buttonStyling: false,
							customClass: {
								confirmButton: 'btn btn-danger'
							}
						});
						return;
					}
					else {
						const checkStdDtBusinessDt = await self.doCheckIsBusinessDt(self.stdDt);
						const checkEndDtBusinessDt = await self.doCheckIsBusinessDt(self.endDt);
						if (!_.isBlank(checkStdDtBusinessDt)) {
							Swal.fire({
								icon: 'error',
								text: self.$t('adm.startDateNotBusinessDay'),
								showCloseButton: true,
								confirmButtonText: self.$t('adm.confirm'),
								buttonStyling: false,
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}
						if (!_.isBlank(checkEndDtBusinessDt)) {
							Swal.fire({
								icon: 'error',
								text: self.$t('adm.endDateNotBusinessDay'),
								showCloseButton: true,
								confirmButtonText: self.$t('adm.confirm'),
								buttonStyling: false,
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							});
							return;
						}
						self.deputyBranCode = ret.data.deputyBranCode;
						self.insertDeputy();
					}
				});
		},
		insertDeputy: function () {
			const self = this;

			// 代理日期時間檢核
			const chkStdDt = new Date(self.stdDt + ' ' + self.stdHr + ':00');
			const chkEndDt = new Date(self.endDt + ' ' + self.endHr + ':00');
			if (!self.checkDateRange(chkStdDt, chkEndDt)) {
				return;
			}

			const item = self.updateItem;
			const stdDt = self.stdDt + ' ' + self.stdHr + ':00';
			const endDt = self.endDt + ' ' + self.endHr + ':00';

			self.$api
				.postInsertDeputyApi({
					userCode: item.userCode,
					roleMetadata: item.userRoleCodes,
					branCode: item.branCode,
					deputyUserCode: self.deputyUserCode,
					deputyBranCode: self.deputyBranCode,
					stdDt: stdDt,
					endDt: endDt
				})
				.then(function (ret) {
					Swal.fire({
						icon: 'success',
						text: self.$t('adm.addDeputySuccess'),
						showCloseButton: true,
						confirmButtonText: self.$t('adm.confirm'),
						buttonStyling: false,
						customClass: {
							confirmButton: 'btn btn-success'
						}
					});
					self.clearDeputyForm();
					self.checkIsDeputiesRmMgr();
					self.getLoginUserDeputies();
					self.getUnderUserDeputies(self.pageable.page);
				});
		},
		// delete
		cancelLoginDeputy: function () {
			const self = this;
			this.$bi.confirm(self.$t('adm.confirmDeleteData'), {
				event: {
					confirmOk: function () {
						self.$api
							.deleteUserdeputyApi({
								userCode: self.loginUserCode,
								deputyUserCode: self.loginDeputyUserCode
							})
							.then(function (ret) {
								Swal.fire({
									icon: 'success',
									text: self.$t('adm.deleteDeputySuccess'),
									showCloseButton: true,
									confirmButtonText: self.$t('adm.confirm'),
									buttonStyling: false,
									customClass: {
										confirmButton: 'btn btn-success'
									}
								});
								self.$refs.loginDeputy.resetForm(); // 重置表單，清除錯誤提示
								self.loginStdDt = null;
								self.loginStdHr = '07:00';
								self.loginEndDt = null;
								self.loginEndHr = '07:00';
								self.loginDeputyUserCode = null;
								self.loginDeputyUserName = null;
								self.loginDeputyBranCode = null;
								self.loginTotDepDays = null;
								self.hasDeputy = false;
								self.disableDeputy = false;
								self.checkIsDeputiesRmMgr();
							});
					}
				}
			});
		},
		clearDeputyForm: function () {
			const self = this;
			self.updateItem = null;
			self.userCode = null;
			self.userName = '';
			self.deputyUserName = null;
			self.totDepDays = '';
			self.$refs.deputy.resetForm();
		},
		cancelDeputy: function (item) {
			const self = this;
			self.$bi.confirm('確定要刪除此筆資料嗎?', {
				event: {
					confirmOk: function () {
						self.$api
							.deleteUserdeputyApi({
								userCode: item.userCode,
								deputyUserCode: item.deputyUserCode
							})
							.then(function (ret) {
								Swal.fire({
									icon: 'success',
									text: self.$t('adm.deleteDeputySuccess'),
									showCloseButton: true,
									confirmButtonText: self.$t('adm.confirm'),
									buttonStyling: false,
									customClass: {
										confirmButton: 'btn btn-success'
									}
								});
								self.getUnderUserDeputies();
							});
					}
				}
			});
		},
		// util function
		isValidate: function (userCode, roleCodes, branCode, deputyUserCode, deputyBranCode, stdDtDate, stdDtTime, endDtDate, endDtTime) {
			const self = this;
			let isValidate = true;

			if (
				_.isBlank(userCode)
				|| _.isBlank(roleCodes)
				|| _.isBlank(branCode)
				|| _.isBlank(deputyUserCode)
				|| _.isBlank(deputyBranCode)
				|| _.isBlank(stdDtDate)
				|| _.isBlank(stdDtTime)
				|| _.isBlank(endDtDate)
				|| _.isBlank(endDtTime)
			) {
				Swal.fire({
					icon: 'error',
					text: self.$t('adm.missingRequiredFields'),
					showCloseButton: true,
					confirmButtonText: self.$t('adm.confirm'),
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				isValidate = false;
			}
			return isValidate;
		},
		// 檢核代理日期區間
		checkDateRange: function (stdDt, endDt) {
			const self = this;
			// 起日是否大於迄日
			if (stdDt > endDt) {
				Swal.fire({
					icon: 'error',
					text: self.$t('adm.startDateGreaterThanEnd'),
					showCloseButton: true,
					confirmButtonText: self.$t('adm.confirm'),
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return false;
			}
			// 代理日起日<現在日期時間
			const nowData = new Date();
			if (stdDt < nowData) {
				Swal.fire({
					icon: 'error',
					text: self.$t('adm.pastTimeDeputy'),
					showCloseButton: true,
					confirmButtonText: self.$t('adm.confirm'),
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return false;
			}
			return true;
		},
		generateTimeOptions() {
			const startTime = 7 * 60; // 07:00 in minutes
			const endTime = 18 * 60; // 18:00 in minutes
			const interval = 10; // 10 minutes

			return _.map(_.range(startTime, endTime + interval, interval), (minutes) => {
				const hours = Math.floor(minutes / 60);
				const mins = minutes % 60;
				return `${_.padStart(hours.toString(), 2, '0')}:${_.padStart(mins.toString(), 2, '0')}`;
			});
		},
		getAreaList: function () {
			const self = this;
			this.getAdmBranches(['10', '50'], null).then(function (resp) {
				self.areaList = resp.data;
			});
		},
		getBranList: function () {
			const self = this;
			this.getAdmBranches(null, self.groupCode).then(function (resp) {
				self.branList = resp.data;
			});
		},
		getAdmBranches: function (branLvlCode = null, parentBranCode = null) {
			const self = this;
			return self.$api.getAdmBranchesApi({
				parentBranCode: parentBranCode,
				branLvlCode: branLvlCode
			});
		},
		getAdmUsersList: function (params) {
			const self = this;

			self.$api
				.getAdmUsersListApi({
					branCode: '',
					userCode: params.userCode,
					parentBranCode: '',
					roleCode: ''
				})
				.then(function (ret) {
					if (ret.data == null || ret.data.length === 0) {
						self[params.targetName] = null;
						Swal.fire({
							icon: 'error',
							text: self.$t('adm.employeeNotExists'),
							showCloseButton: true,
							confirmButtonText: self.$t('adm.confirm'),
							buttonStyling: false,
							customClass: {
								confirmButton: 'btn btn-danger'
							}
						});
						return;
					}
					else {
						self[params.targetName] = ret.data[0].userName; // 取得代理人姓名
					}
				});
		},
		onLoginDeputyUserCodeInput: function () {
			const self = this;
			self.loginDeputyUserCode = self.complementUserCode(self.loginDeputyUserCode);
			if (self.loginDeputyUserCode) {
				self.getAdmUsersList({
					userCode: self.loginDeputyUserCode,
					targetName: 'loginDeputyUserName'
				});
			}
			else {
				self.loginDeputyUserName = '';
			}
		},
		onDeputyUserCodeInput: function () {
			const self = this;
			self.deputyUserCode = self.complementUserCode(self.deputyUserCode);
			if (self.deputyUserCode) {
				self.getAdmUsersList({
					userCode: self.deputyUserCode,
					targetName: 'deputyUserName'
				});
			}
			else {
				self.deputyUserName = '';
			}
		}
	}
};
</script>
