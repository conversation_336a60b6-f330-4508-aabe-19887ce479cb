<template>
	<div class="row">
		<div class="col-12">
			<div class="tab-nav-main">
				<ul class="nav nav-pills">
					<li class="nav-item">
						<a class="nav-link active" href="#">事件通知參數</a>
					</li>
				</ul>
				<div class="tab-content">
					<div role="tabpanel" class="tab-pane fade show active">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#card-body">
								<h4>請設定發送金額上限</h4>
								<span class="tx-square-bracket">為必填欄位</span>
							</div>
							<vue-form v-slot="{errors}" ref="queryForm">
								<div id="card-body" class="card-body collapse show">
									<div class="row g-3 align-items-end">
										<div class="form-group col-lg-4">
											<label class="form-label tx-require mg-xl-e-80">配息事件通知發送金額上限</label>
											<div class="input-group">
												<vue-field
													v-model="divWobAmtUl"
													type="number"
													class="form-control"
													:class="{'is-invalid': errors.divWobAmtUl}"
													name="divWobAmtUl"
													label="配息事件通知發送金額上限"
													:rules="{required: true, numeric:true}"
												/>
											</div>
											<span class="input-group-text">萬</span>
											<div style="height: 25px">
												<span v-show="errors.divWobAmtUl" class="text-danger">{{ errors.divWobAmtUl }}</span>
											</div>
										</div>

										<div class="form-group col-lg-4">
											<label class="form-label tx-require mg-xl-e-80">定存到期事件通知</label>
											<div class="input-group">
												<vue-field
													v-model="fdMatWobAmt"
													type="number"
													class="form-control"
													:class="{'is-invalid': errors.fdMatWobAmt}"
													name="fdMatWobAmt"
													label="定存到期事件通知"
													:rules="{required: true, numeric:true}"
												/>
											</div>
											<span class="input-group-text">萬</span>
											<div style="height: 25px">
												<span v-show="errors.fdMatWobAmt" class="text-danger">{{ errors.fdMatWobAmt }}</span>
											</div>
										</div>

										<div class="form-group col-lg-4">
											<label class="form-label tx-require mg-xl-e-80">AUM大額差異事件通知</label>
											<div class="input-group">
												<vue-field
													v-model="lgAmtDiffWob"
													type="number"
													class="form-control"
													:class="{'is-invalid': errors.lgAmtDiffWob}"
													name="lgAmtDiffWob"
													label="AUM大額差異事件通知"
													:rules="{required: true, numeric:true}"
												/>
											</div>
											<span class="input-group-text">萬</span>
											<div style="height: 25px">
												<span v-show="errors.lgAmtDiffWob" class="text-danger">{{ errors.lgAmtDiffWob }}</span>
											</div>
										</div>
										<div class="form-footer">
											<button class="btn btn-primary btn-glow btn-save" :disabled="$_.isNil(divWobAmtUl) || $_.isNil(fdMatWobAmt) || $_.isNil(lgAmtDiffWob)" @click.prevent="insertAdmParam()">
												儲存
											</button>
										</div>
									</div>
								</div>
							</vue-form>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!--頁面內容 end-->
</template>

<script>
import _ from 'lodash';
import { Field, Form } from 'vee-validate';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			divWobAmtUl: null,
			fdMatWobAmt: null,
			lgAmtDiffWob: null
		};
	},
	mounted: function () {
		const self = this;
		self.getParamValue('CFG_WOB_CONFIG', 'DIV_WOB_AMT_Ul');
		self.getParamValue('CFG_WOB_CONFIG', 'FD_MAT_WOB_AMT');
		self.getParamValue('CFG_WOB_CONFIG', 'LG_AMT_DIFF_WOB');
	},
	methods: {
		getParamValue: function (paramType, paramCode, retrieveData) {
			const self = this;
			self.$api
				.getAdmParamApi({
					paramType: paramType,
					paramCode: paramCode
				})
				.then(function (ret) {
					if (ret.data) {
						const data = _.first(ret.data);
						if (data.paramCode == 'divWobAmtUl') {
							self.divWobAmtUl = data.paramValue;
						}
						else if (data.paramCode == 'fdMatWobAmt') {
							self.fdMatWobAmt = data.paramValue;
						}
						else if (data.paramCode == 'lgAmtDiffWob') {
							self.lgAmtDiffWob = data.paramValue;
						}
					}
				});
		},
		patchAdmParam: function (paramType, paramCode, paramValue) {
			const self = this;
			self.$api.patchAdmParamApi({
				paramType: paramType,
				paramCode: paramCode,
				paramValue: paramValue
			});
		},
		insertAdmParam: function () {
			const self = this;
			Promise.all([
				self.patchAdmParam('CFG_WOB_CONFIG', 'DIV_WOB_AMT_Ul', self.divWobAmtUl),
				self.patchAdmParam('CFG_WOB_CONFIG', 'FD_MAT_WOB_AMT', self.fdMatWobAmt),
				self.patchAdmParam('CFG_WOB_CONFIG', 'LG_AMT_DIFF_WOB', self.lgAmtDiffWob)
			]).then(() => {
				self.$bi.alert('更新成功。');
			});
		}
	}
};
</script>
