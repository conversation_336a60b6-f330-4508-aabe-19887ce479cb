<template>
	<div class="row">
		<div class="col-12">
			<vue-bi-tabs :menu-code="'M00-07'" @change-tab="changeTab">
				<template #default="{ id }">
					<component :is="id" />
				</template>
			</vue-bi-tabs>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
import vueBiTabs from '@/views/components/biTabs.vue';
import vueGroupBranFpUpload from './include/groupBranFpUpload.vue';
import vueGroupBranFpSearch from './include/groupBranFpSearch.vue';

export default {
	components: {
		vueBiTabs,
		vueGroupBranFpUpload,
		vueGroupBranFpSearch
	},
	data: function () {
		return {
			// 畫面顯示用參數
			customTitle: null,
			// 畫面邏輯判斷用參數
			tabCodeTitleMap: {
				'M00-070': this.$t('adm.branchGroupSetting'),
				'M00-071': this.$t('adm.branchGroupQuery')
			}
		};
	},
	methods: {
		changeTab: function (tabCode) {
			const self = this;
			self.customTitle = self.tabCodeTitleMap[tabCode];
		}
	}
};
</script>
