<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form mb-3">
			<div class="card-header">
				<h4>{{ $t('adm.branchGroupQuery') }}</h4>
				<span class="tx-square-bracket">{{ $t('adm.reqField') }}</span>
			</div>
			<div class="card-body">
				<div class="row g-3 align-items-end">
					<table class="table table-RWD table-horizontal-RWD table-bordered">
						<tbody>
							<tr>
								<th class="wd-15p tx-require">
									{{ $t('adm.branchGroupCategory') }}
								</th>
								<td class="wd-85p">
									<select v-model="groupCode" name="groupCode" class="form-select">
										<option selected="selected" value="">
											{{ $t('adm.all') }}
										</option>
										<option v-for="(item, index) in groupBranNameList" :value="item.groupCode">
											{{ item.groupName }}
										</option>
									</select>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<div class="form-footer d-flex justify-content-end gap-2">
			<button class="btn btn-primary btn-glow btn-search" @click.prevent="getPageData(0)">
				{{ $t('adm.search') }}
			</button>
			<button class="btn btn-primary btn-glow" @click="exportPageData()">
				{{ $t('adm.downloadExcel') }}
			</button>
		</div>

		<div class="col-12">
			<div id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('adm.uploadListResult') }}</h4>
						<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
					</div>
					<template v-if="pageData.totalElements > 0">
						<div class="table-responsive">
							<table class="table table-RWD table-bordered text-center">
								<thead>
									<tr>
										<th class="text-center">
											{{ $t('adm.itemNo') }}
										</th>
										<th class="text-center">
											{{ $t('adm.groupCode') }}
										</th>
										<th class="text-center">
											{{ $t('adm.groupName') }}
										</th>
										<th class="text-center">
											{{ $t('adm.branchCode') }}
										</th>
										<th class="text-center">
											{{ $t('adm.branchName') }}
										</th>
										<th class="text-center">
											{{ $t('adm.effectiveDate') }}
										</th>
										<th class="text-center">
											{{ $t('adm.uploaderStaff') }}
										</th>
										<th class="text-center">
											{{ $t('adm.modifyTime') }}
										</th>
									</tr>
								</thead>
								<tbody id="TopTenList">
									<tr v-for="(item, i) in pageData.content">
										<td :data-th="$t('adm.itemNo')">
											{{ item.orderBy }}
										</td>
										<td :data-th="$t('adm.groupCode')">
											{{ item.groupCode }}
										</td>
										<td :data-th="$t('adm.groupName')">
											{{ item.groupName }}
										</td>
										<td :data-th="$t('adm.branchCode')">
											{{ item.branCode }}
										</td>
										<td :data-th="$t('adm.branchName')">
											{{ item.branName }}
										</td>
										<td :data-th="$t('adm.effectiveDate')">
											{{ item.validDate }}
										</td>
										<td :data-th="$t('adm.uploaderStaff')">
											{{ item.createBy }} {{ item.createUserName }}
										</td>
										<td :data-th="$t('adm.modifyTime')">
											{{ item.createDt }}
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</template>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import vuePagination from '@/views/components/pagination.vue';
import _ from 'lodash';
import moment from 'moment';
export default {
	components: {
		vuePagination
	},
	data: function () {
		return {
			groupBranNameList: [], // 下拉選單
			groupCode: '',
			pageData: {},
			pageable: {
				page: 0,
				size: 20,
				sort: 'ORDER_BY',
				direction: 'ASC'
			}
		};
	},
	mounted: function () {
		const self = this;
		self.getGroupBranName();
	},
	methods: {
		getGroupBranName: function () {
			const self = this;

			self.$api.getGroupBranNameApi().then(function (ret) {
				self.groupBranNameList = ret.data;
			});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (_page) {
			const self = this;

			const page = _.isNumber(_page) ? _page : self.pageable.page;
			let url = '';
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=GROUP_CODE' + ',' + self.pageable.direction;
			const ret = await self.$api.getBranPageData(
				{
					groupCode: self.groupCode
				},
				url
			);
			self.pageData = ret.data;
		},
		exportPageData: async function () {
			const self = this;
			const dateStr = moment().format('YYYYMMDD');
			const fileName = self.$t('adm.branchGroupQueryExport') + dateStr + '.xls';
			const ret = await self.$api.getBranExportPageData({
				groupCode: self.groupCode
			});
			const blob = new Blob([ret]);
			const link = document.createElement('a');
			const url = window.URL.createObjectURL(blob);
			link.download = fileName;
			link.href = url;
			document.body.appendChild(link);
			link.click();
			link.remove();
			setTimeout(() => window.URL.revokeObjectURL(url), 500);
		}
	}
};
</script>
