<template>
	<div class="row">
		<div class="col-12">
			<div class="card card-form">
				<div class="card-header">
					<h4>{{ $t('adm.searchCond') }}</h4>
					<span class="tx-square-bracket">{{ $t('adm.reqField') }}</span>
				</div>
				<vue-form v-slot="{ errors }" ref="queryForm" autocomplete="off">
					<div class="card-body">
						<div class="row g-3 align-items-end">
							<div class="col-lg-2">
								<label class="form-label tx-require">{{ $t('adm.userCode') }}</label> &nbsp;{{ user.userName || '' }}
								<vue-field
									id="userCode"
									v-model="userCode"
									type="text"
									name="userCode"
									rules="required"
									:class="{ 'is-invalid': errors.userCode }"
									class="form-control"
									:label="$t('adm.userCode')"
									@blur="isUserExists"
								/>
								<div style="height: 25px">
									<span v-show="errors.userCode" class="text-danger">{{ errors.userCode }}</span>
								</div>
							</div>
							<div class="col-lg-5">
								<label class="form-label tx-require">{{ $t('adm.queryDateRangeStart') }}</label>
								<div class="input-group">
									<span class="input-group-text">{{ $t('adm.dateLabel') }}</span>
									<vue-field
										v-model="logStartDt"
										name="logStartDt"
										type="date"
										:class="{ 'is-invalid': errors.logStartDt }"
										class="form-control wd-30p-f"
										:label="$t('adm.queryDateRangeStart')"
										rules="required"
									/>
									<span class="input-group-text">{{ $t('adm.timeLabel') }}</span>
									<VueSelect
										id="startHour"
										v-model="logStartHour"
										name="startHour"
										class="form-select"
										:options="hourOptions"
									/>
									<VueSelect
										id="startMin"
										v-model="logStartMinute"
										name="startMin"
										class="form-select"
										:options="minuteOptions"
									/>
								</div>
								<div style="height: 25px">
									<span v-show="errors.logStartDt" class="text-danger">{{ errors.logStartDt }}</span>
								</div>
							</div>

							<div class="col-lg-5">
								<label class="form-label tx-require">{{ $t('adm.queryDateRangeEnd') }}</label>
								<div class="input-group">
									<span class="input-group-text">{{ $t('adm.dateLabel') }}</span>
									<vue-field
										v-model="logEndDt"
										name="logEndDt"
										type="date"
										v-bind="logEndDateTimeBoundary"
										class="form-control wd-30p-f"
										:class="{ 'is-invalid': errors.logEndDt }"
										rules="required"
										:label="$t('adm.queryDateRangeEnd')"
									/>
									<span class="input-group-text">{{ $t('adm.timeLabel') }}</span>
									<VueSelect
										id="endHour"
										v-model="logEndHour"
										name="endHour"
										class="form-select"
										:options="endHourOptions"
									/>
									<VueSelect
										id="endMin"
										v-model="logEndMinute"
										name="endMin"
										class="form-select"
										:options="endMinuteOptions"
									/>
								</div>
								<div style="height: 25px">
									<span v-show="errors.logEndDt" class="text-danger">{{ errors.logEndDt }}</span>
								</div>
							</div>
							<div class="col-lg-10">
								<label class="form-label">{{ $t('adm.functionModule') }}</label>
								<div class="row g-3">
									<div class="col-lg-3 col-md-6">
										<div class="input-group">
											<span class="input-group-text">{{ $t('adm.systemModule') }}</span>
											<VueSelect
												id="menuListId"
												v-model="moduleIndex"
												name="moduleMenuCode"
												class="form-select"
												:options="moduleOptions"
											/>
										</div>
									</div>
									<div class="col-lg-3 col-md-6">
										<div class="input-group">
											<span class="input-group-text">{{ $t('adm.functionItem') }}</span>
											<VueSelect
												id="progListId"
												v-model="funcIndex"
												name="progCode"
												class="form-select"
												:options="functionOptions"
											/>
										</div>
									</div>
									<div class="col-lg-3 col-md-6">
										<div class="input-group">
											<span class="input-group-text">{{ $t('adm.functionDetail') }}</span>
											<VueSelect
												id="progListId"
												v-model="funcDetailIndex"
												name="progCode"
												class="form-select"
												:options="functionDetailOptions"
											/>
										</div>
									</div>
									<div class="col-lg-3 col-md-6">
										<div class="input-group">
											<span class="input-group-text">{{ $t('adm.tab') }}</span>
											<VueSelect
												id="tabListId"
												v-model="functionBookMarkMenuCode"
												name="tablCode"
												class="form-select"
												:options="bookmarkOptions"
												option-value="menuCode"
												option-label="menuName"
											/>
										</div>
									</div>
								</div>
							</div>
							<div class="col-lg-2">
								<button role="button" class="btn btn-primary btn-glow btn-searc=h" @click.prevent="gotoPage(0)">
									{{ $t('adm.search') }}
								</button>
							</div>
						</div>
					</div>
				</vue-form>
			</div>
			<DataTable
				:title="$t('adm.userUsageDetailRecordList')"
				:rows="pageData.content"
				:columns="columns"
				:pagination="pageData"
				@page="gotoPage"
			/>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
import _ from 'lodash';
import Swal from 'sweetalert2';
import { Field, Form } from 'vee-validate';
import userCodeComplement from '../../../utils/mixin/userCodeComplement';
import Select from '@/components/Select.vue';
import DataTable from '@/components/DataTable.vue';
import moment from 'moment';

const hourOptions = Array.from({ length: 24 }, (_, i) => ({
	value: i,
	label: String(i).padStart(2, '0')
}));

const minuteOptions = Array.from({ length: 4 }, (_, i) => ({
	value: i * 15,
	label: String(i * 15).padStart(2, '0')
}));

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		'VueSelect': Select,
		DataTable
	},
	mixins: [userCodeComplement],
	data: function () {
		return {
			// API 用參數
			userCode: null,
			logStartDt: null,
			logStartHour: 0,
			logStartMinute: 0,
			logEndDt: null,
			logEndHour: 0,
			logEndMinute: 0,
			moduleMenuCode: null,
			functionMenuCode: null,
			functionDetailMenuCode: null,
			functionBookMarkMenuCode: '',

			// API邏輯判斷用參數
			moduleStrset: null,
			functioneStrset: null,
			functionDetailStrset: null,
			// 畫面邏輯判斷用參數
			moduleIndex: -1,
			funcIndex: -1,
			funcDetailIndex: -1,

			// 下拉選單
			moduleMenu: [],
			functionMenu: [],
			functionDetailMenu: [],
			functionBookMarkMenu: [],

			// 主要顯示資料
			pageData: {},
			pageable: {
				page: 0,
				size: 20,
				sort: 'log_dt',
				direction: 'ASC'
			},

			// 判斷使用人代碼是否存在
			user: {},
			hourOptions,
			minuteOptions,
			columns: [
				{ field: 'logDt', label: this.$t('adm.date') },
				{ field: 'logTime', label: this.$t('adm.time') },
				{ field: 'menuOne', label: this.$t('adm.systemModule') },
				{ field: 'menuTwo', label: this.$t('adm.functionItem') },
				{ field: 'menuThird', label: this.$t('adm.functionDetail') },
				{ field: 'menuFour', label: this.$t('adm.tab') },
				{ field: it => `${it.branCode} ${it.branName}`, label: this.$t('adm.branchUnitName') },
				{ field: it => `${it.userCode} ${it.userName}`, label: this.$t('adm.user') },
				{ field: 'remoteIp', label: 'IP' }
			]
		};
	},
	computed: {
		endHourOptions() {
			return this.logStartDt != null && this.logEndDt === this.logStartDt
				? hourOptions.filter(it => it.value >= this.logStartHour)
				: hourOptions;
		},
		endMinuteOptions() {
			if (this.logStartDt == null || this.logEndDt !== this.logStartDt) return minuteOptions;
			if (this.logStartHour !== this.logEndHour) return minuteOptions;
			return minuteOptions.filter(it => it.value >= this.logStartMinute);
		},
		moduleOptions() {
			return [
				{ value: -1, label: this.$t('adm.all') },
				...this.moduleMenu?.map(({ menuName }, index) => ({
					label: menuName,
					value: index
				})) ?? []
			];
		},
		functionOptions() {
			return [
				{ value: -1, label: this.$t('adm.all') },
				...this.functionMenu?.map(({ menuName }, index) => ({
					label: menuName,
					value: index
				})) ?? []
			];
		},
		functionDetailOptions() {
			return [
				{ value: -1, label: this.$t('adm.all') },
				...this.functionDetailMenu?.map(({ menuName }, index) => ({
					label: menuName,
					value: index
				})) ?? []
			];
		},
		bookmarkOptions() {
			return [{ menuCode: '', menuName: this.$t('adm.all') }, ...this.functionBookMarkMenu];
		},
		logEndDateTimeBoundary() {
			if (!this.logStartDt) return undefined;
			return {
				min: this.logStartDt,
				max: moment(this.logStartDt).add(7, 'days').format('YYYY-MM-DD')
			};
		}
	},
	watch: {
		logStartDt: function (newVal) {
			if (!newVal || !this.logEndDt) return;
			const endDateTime = moment(this.logEndDt);
			const diffInDays = endDateTime.diff(newVal, 'days');
			if (diffInDays < 0) this.logEndDt = newVal;
			else if (diffInDays > 7) this.logEndDt = moment(newVal).add(7, 'days').format('YYYY-MM-DD');
		},
		moduleIndex: function () {
			const self = this;
			if (self.moduleIndex == -1) {
				self.moduleMenuCode = null;
				self.moduleStrset = null;
				self.functionMenuCode = null;
				self.functioneStrset = null;
				self.functionDetailMenuCode = null;
				self.functionDetailStrset = null;
			}
			else {
				const module = self.moduleMenu[self.moduleIndex];
				self.moduleMenuCode = module.menuCode;
				self.moduleStrset = module.strset;
				self.funcIndex = -1;
				self.funcDetailIndex = -1;
			}
			self.functionBookMarkMenuCode = '';
			self.getFunctionMenu();
			self.getFunctionDetailMenu();
			self.getFunctionBookMarkMenu();
		},
		funcIndex: function () {
			const self = this;
			if (self.funcIndex == -1) {
				self.functionMenuCode = null;
				self.functioneStrset = null;
				self.functionDetailMenuCode = null;
				self.functionDetailStrset = null;
			}
			else {
				const func = self.functionMenu[self.funcIndex];
				self.functionMenuCode = func.menuCode;
				self.functioneStrset = func.strset;
				self.funcDetailIndex = -1;
			}
			self.functionBookMarkMenuCode = '';
			self.getFunctionDetailMenu();
			self.getFunctionBookMarkMenu();
		},
		funcDetailIndex: function () {
			const self = this;
			if (self.funcDetailIndex == -1) {
				self.functionDetailMenuCode = null;
				self.functionDetailStrset = null;
			}
			else {
				const funcDetail = self.functionDetailMenu[self.funcDetailIndex];
				self.functionDetailMenuCode = funcDetail.menuCode;
				self.functionDetailStrset = funcDetail.strset;
			}
			self.functionBookMarkMenuCode = '';
			self.getFunctionBookMarkMenu();
		}
	},
	mounted: function () {
		const self = this;
		self.getModuleMenu();
	},
	methods: {
		getModuleMenu: function () {
			const self = this;
			self.$api.getModuleMenuApi().then(function (ret) {
				self.moduleMenu = ret.data;
			});
		},
		getFunctionMenu: function () {
			const self = this;
			if (_.isBlank(self.moduleStrset)) {
				return;
			}

			self.$api
				.getUserFunctionMenuApi({
					depths: 2,
					strset: self.moduleStrset
				})
				.then(function (ret) {
					self.functionMenu = ret.data;
				});
		},
		getFunctionDetailMenu: function () {
			const self = this;
			if (_.isBlank(self.functioneStrset)) {
				return;
			}

			self.$api
				.getUserFunctionMenuApi({
					depths: 3,
					strset: self.functioneStrset
				})
				.then(function (ret) {
					self.functionDetailMenu = ret.data;
				});
		},
		getFunctionBookMarkMenu: function () {
			const self = this;
			if (_.isBlank(self.functionDetailStrset)) {
				return;
			}

			self.$api
				.getUserFunctionMenuApi({
					depths: 4,
					strset: self.functionDetailStrset
				})
				.then(function (ret) {
					self.functionBookMarkMenu = ret.data;
				});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		isUserExists() {
			const self = this;
			self.userCode = self.complementUserCode(self.userCode);
			if (self.userCode && self.userCode.trim() !== '') {
				self.$api
					.getUserInfoApi({
						userCode: self.userCode,
						path: 'auditUsageDetail'
					})
					.then(function (ret) {
						if (ret.data) {
							self.user = ret.data;
						}
						else {
							self.user = {};
						}
					})
					.catch(function (ret) {
						self.user = {};
					});
			}
			else {
				self.user = {};
			}
		},
		getPageData: async function (page) {
			const { valid } = await this.$refs.queryForm.validate();
			if (!valid) return;
			const startDateTimeStr = `${this.logStartDt} ${String(this.logStartHour).padStart(2, '0')}:${String(this.logStartMinute).padStart(2, '0')}`;
			const endDateTimeStr = `${this.logEndDt} ${String(this.logEndHour).padStart(2, '0')}:${String(this.logEndMinute).padStart(2, '0')}`;
			const startDateTime = new Date(startDateTimeStr);
			const endDateTime = new Date(endDateTimeStr);
			const interval = endDateTime.getTime() - startDateTime.getTime();
			const days = Math.floor(interval / (24 * 3600 * 1000));
			if (days > 7) {
				Swal.fire({
					icon: 'error',
					text: self.$t('adm.queryDateRangeCannotExceedSevenDays'),
					showCloseButton: true,
					confirmButtonText: self.$t('adm.confirm'),
					buttonStyling: false,
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			const url = _.toPageUrl('', page, this.pageable);

			const response = await this.$api
				.getUserAccessLogsApi(
					{
						userCode: this.userCode,
						logStartDt: startDateTimeStr,
						logEndDt: endDateTimeStr,
						m1MenuCode: this.moduleMenuCode,
						m2MenuCode: this.functionMenuCode,
						m3MenuCode: this.functionDetailMenuCode,
						m4MenuCode: this.functionBookMarkMenuCode
					},
					url
				);
			this.pageData = response.data;
		}
	}
};
</script>
