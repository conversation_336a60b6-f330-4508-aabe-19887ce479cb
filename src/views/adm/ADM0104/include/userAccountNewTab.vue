<template>
	<div>
		<div role="tabpanel" class="tab-pane fade show active">
			<div class="card card-form mb-0">
				<div class="card-header">
					<h4>{{ $t('adm.userBasicInfo') }}</h4>
					<span class="tx-square-bracket">{{ $t('adm.reqField') }}</span>
				</div>
				<div class="card-body">
					<div class="row g-3 align-items-end">
						<table class="table table-RWD table-horizontal-RWD table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p tx-require">
										{{ $t('adm.userCodeColon') }}
									</th>
									<td class="wd-35p">
										<div class="d-inline-block">
											<div class="input-group">
												<input
													v-model="userCode"
													type="text"
													class="form-control"
													@input="handleUserCodeInput"
												>
												<button class="btn btn-primary btn-search" type="button" @click="getEditUserInfo()">
													{{ $t('adm.search') }}
												</button>
											</div>
										</div>
									</td>
									<th class="wd-15p tx-require">
										{{ $t('adm.accountAvailability') }}
									</th>
									<td class="wd-35p">
										<div>
											<Form v-slot="{ errors }" ref="queryForm">
												<div class="btn-group btn-group-sm">
													<input
														id="rdoOn"
														v-model="startFlag"
														type="radio"
														name="startFlag"
														class="btn-check"
														autocomplete="off"
														value="Y"
														:label="$t('adm.accountAvailability')"
														rules="required"
														@click="rdoOn()"
													>
													<label class="btn btn-outline-success" for="rdoOn">
														<span class="icon">
															<i class="bi bi-check-circle" />
															<span class="text">{{ $t('adm.enabled') }}</span>
														</span>
													</label>
													<input
														id="rdoOff"
														v-model="startFlag"
														type="radio"
														name="startFlag"
														class="btn-check"
														autocomplete="off"
														value="N"
														:label="$t('adm.accountAvailability')"
														rules="required"
														@click="rdoOff()"
													>
													<label class="btn btn-outline-danger" for="rdoOff">
														<span class="icon">
															<i class="bi bi-x-circle" />
															<span class="text">{{ $t('adm.closed') }}</span>
														</span>
													</label>
													<div style="height: 25px">
														<span v-show="errors.startFlag" class="text-danger">{{ errors.startFlag }}</span>
													</div>
												</div>
											</Form>
										</div>
									</td>
								</tr>
								<tr>
									<th class="wd-15p">
										{{ $t('adm.userNameColon') }}
									</th>
									<td class="wd-35p">
										<span v-if="editUserInfo != null" class="JQdata-show tx-16">{{ editUserInfo.userName }}</span>
									</td>
									<th class="wd-15p">
										{{ $t('adm.belongingUnit') }}
									</th>
									<td class="wd-35p">
										<span v-if="editUserInfo != null" class="JQdata-show tx-16">{{ editUserInfo.branName }}</span>
									</td>
								</tr>
								<tr>
									<th class="wd-15p">
										{{ $t('adm.roleSetting') }}
									</th>
									<td class="wd-35p">
										<span v-if="editUserInfo != null" class="JQdata-show d-inline-block">
											<div class="input-group">
												<input
													id="roleBranName"
													v-model="branName"
													type="text"
													name="roleBranName"
													value=""
													class="form-control"
													readonly
												>
												<button
													id="chooseBranchId"
													type="button"
													class="btn btn-info"
													:disabled="!isHeadOffice"
													@click="getBranMenu"
												>
													{{ $t('adm.selectBranchUnit') }}
												</button>
												<button
													id="clean"
													type="button"
													class="btn btn-white"
													@click="clearForm"
												>{{ $t('adm.clearForm') }}</button>
											</div>

											<ul id="job" class="list-group list-inline-tags">
												<li v-for="(item, index) in userBranPositions" :key="index" class="list-group-item">
													<a href="javascript:void(0)">
														{{ item.branName }}-&gt;{{ item.roleName }}
														<span
															class="img-delete JQ-delet"
															data-bs-toggle="tooltip"
															:title="$t('adm.delete')"
															@click="deletePos(item)"
														/>
													</a>
												</li>
											</ul>
										</span>
										<button
											id="choosePositionId"
											type="button"
											class="btn btn-info"
											:disabled="!isHeadOffice"
											@click="getRoleMenu"
										>
											{{ $t('adm.selectRole') }}
										</button>
									</td>
									<th class="wd-15p">
										{{ $t('adm.dataCreateDate') }}
									</th>
									<td class="wd-35p">
										<span v-if="editUserInfo != null" class="JQdata-show tx-16">{{ editUserInfo.createDt }}</span>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<div class="tx-note">
				{{ $t('adm.userCodeNote') }}
			</div>
			<div class="text-end">
				<input
					id="btn"
					name="Submit"
					type="button"
					class="btn btn-primary btn-lg btn-glow"
					:value="$t('adm.submitReview')"
					@click="audit"
				>
			</div>
		</div>
		<!--Tab內容 end-->

		<!-- Bran Modal -->
		<modal ref="modal" @close="closeModal">
			<template #content="props">
				<div class="modal-dialog modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 id="selectModal" class="modal-title">
								{{ modalTitle }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click="props.close()"
							/>
						</div>
						<div class="modal-body">
							<table class="table table-bordered">
								<tbody>
									<tr>
										<td>
											<button type="button" class="btn btn-info btn-glow" @click="expandBtn">
												{{ $t('adm.expandAllModal') }}
											</button>
											<button type="button" class="btn btn-info btn-glow" @click="collapsedBtn">
												{{ $t('adm.collapseAllModal') }}
											</button>
										</td>
									</tr>
									<tr>
										<td>
											<div id="biTreeIdNew" />
											<bi-tree
												v-if="tree.data"
												key="treeKey"
												ref="tree"
												:tree-data="tree.data"
												:tree-cfg="tree.cfg"
											/>
											<div>
												<div id="treeview-noborder" />
											</div>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-white" @click="props.close()">
								{{ $t('adm.closeWindow') }}
							</button>
						</div>
					</div>
				</div>
			</template>
		</modal>
		<!-- Modal 1 End -->
	</div>
</template>

<script>
import { Form, Field } from 'vee-validate';
import modal from '@/views/components/model.vue';
import biTree from '@/views/components/biTree.vue';

export default {
	components: {
		Form,
		Field,
		modal,
		biTree
	},
	props: {
		editAccount: {
			type: Object,
			default: {}
		}
	},
	data: function () {
		return {
			// Api 用參數
			userCode: null,
			buCode: null,
			branCode: null,
			startFlag: 'Y',
			originStartFlag: null,
			userCodeZeroLength: 6,
			deletePosCodes: [],
			posCode: null,
			branName: null,
			roleName: null,

			// 畫面顯示用參數
			modalTitle: null,

			// Api邏輯判斷用參數
			editUserInfo: null,
			origUserBranPositions: [],

			// 主要顯示資料
			userBranPositions: [],
			isHeadOffice: true,

			tree: {},
			// 添加 treeKey 用於強制重新渲染
			treeKey: 0
		};
	},
	watch: {
		editAccount: {
			handler(newVal, oldVal) {
				if (this.$_.isBlank(newVal.userCode)) {
					return;
				}
				this.userCode = newVal.userCode;
				this.getEditUserInfo();
			},
			immediate: true,
			deep: true
		}
	},
	methods: {
		openModal() {
			this.$refs.modal.open();
		},
		closeModal() {
			this.$refs.modal.close();
		},

		getEditUserInfo: async function () {
			this.origUserBranPositions = [];
			const ret = await this.$api.getEditUserInfoApi(this.userCode);
			this.editUserInfo = ret.data;
			this.startFlag = ret.data.startFlag;
			this.originStartFlag = ret.data.startFlag;
			this.intUserPosBranInfo(this.userCode);
		},
		handleUserCodeInput: function () {
			if (this.userCode.length > this.userCodeZeroLength) {
				this.userCode = this.userCode.slice(-1 * this.userCodeZeroLength); // 只保留最後 N 位數字
			}
			this.userCode = this.userCode.padStart(this.userCodeZeroLength, '0');
		},
		expandBtn: function () {
			this.$refs.tree.expandAllNodes(true);
		},
		collapsedBtn: function () {
			this.$refs.tree.expandAllNodes(false);
		},
		intUserPosBranInfo: async function (userCode) {
			const ret = await this.$api.getUserBranPosInfoApi(userCode);
			this.userBranPositions = ret.data;
			this.userBranPositions.forEach((varItem) => {
				this.origUserBranPositions.push(varItem);
			});
		},
		getBranMenu: async function () {
			this.tree = {};
			this.treeKey++;
			this.modalTitle = this.$t('adm.selectBranchUnitTitle');
			const ret = await this.$api.getBranMenuApi();

			this.branMenuData = ret.data;
			this.branMenuData = this.genTreeLabel(this.branMenuData);

			this.tree = {
				data: this.branMenuData,
				cfg:
				{ tailLinks: [
					{
						type: 'anchor',
						onClick: (id, node) => {
							const name = node.querySelector('.bi-tree-text')?.textContent || '';
							const idArr = this.$_.split(id, '_', 2);
							this.buCode = idArr[0];
							this.branCode = idArr[1];
							this.branName = name;
							this.closeModal();
						},
						text: this.$t('adm.selectOption'),
						toApply: true
					}
				]
				}
			};

			this.openModal();
		},
		genTreeLabel: function (data) {
			data.forEach((item) => {
				item.text = item.branName;
				if (!this.$_.isEmpty(item.nodes)) {
					this.genTreeLabel(item.nodes);
				}
			});

			return data;
		},
		getRoleMenu: async function () {
			if (this.$_.isEmpty(this.branCode)) {
				this.$bi.alert(this.$t('adm.pleaseSelectUnitFirst'));
				return;
			}

			this.modalTitle = this.$t('adm.selectRoleTitle');
			// 清空之前的數據
			this.tree = {};
			this.treeKey++; // 強制重新渲染
			const ret = await this.$api.getPosBranMenuApi(this.branCode, this.buCode);
			if (this.$_.isBlank(ret.data) || this.$_.isEmpty(ret.data)) {
				this.$bi.alert(this.$t('adm.noAvailablePosition'));
				return;
			}

			this.roleMenuData = ret.data;
			this.roleMenuData = this.genTreeLabel(this.roleMenuData);
			this.tree = {
				data: this.roleMenuData,
				cfg: { tailLinks: [
					{
						type: 'anchor',
						onClick: (id, node) => {
							const name = node.querySelector('.bi-tree-text')?.textContent || '';
							const branPosData = { posCode: null, roleName: null, branName: null };
							branPosData.posCode = id;

							const nameArr = this.$_.split(name, '_', 3);
							branPosData.branName = nameArr[0];
							branPosData.roleName = nameArr[1];
							if (nameArr[2]) {
								branPosData.roleName = branPosData.roleName + '_' + nameArr[2];
							}

							let isValidate = true;
							this.userBranPositions.forEach((item) => {
								if (item.posCode == branPosData.posCode) {
									this.$bi.alert(this.$t('adm.roleAlreadyExists'));
									isValidate = false;
									return;
								}
							});

							if (isValidate) {
								this.userBranPositions.push(branPosData);
							}
							this.closeModal();
						},
						text: this.$t('adm.selectOption'),
						toApply: true
					}
				] }
			};

			this.openModal();
		},
		clearForm: function () {
			this.branName = null;
			this.branCode = null;
			this.buCode = null;
			this.posCode = null;
		},
		deletePos: function (target) {
			if (!this.isHeadOffice) return;

			this.userBranPositions.forEach(function (item, index, arr) {
				if (item.branCode == target.branCode && item.posCode == target.posCode) {
					arr.splice(index, 1);
				}
			});
		},
		audit: function () {
			const posCodes = [];
			const originalPosCodes = [];

			for (let i = 0; i < this.userBranPositions.length; i++) {
				posCodes.push(this.userBranPositions[i].posCode);
			}

			for (let i = 0; i < this.origUserBranPositions.length; i++) {
				originalPosCodes.push(this.origUserBranPositions[i].posCode);
			}

			if (this.$_.isEqual(posCodes, originalPosCodes) && this.$_.isEqual(this.originStartFlag, this.startFlag)) {
				this.$bi.alert(this.$t('adm.noChangesToSubmit'));
				return;
			}

			this.findDeletePosCodes(posCodes);
			this.$refs.queryForm.validate().then(async (pass) => {
				if (pass.valid) {
					const ret = await this.$api.postUserAccountApi(
						this.userCode,
						this.buCode,
						this.branCode,
						posCodes,
						this.deletePosCodes,
						this.startFlag
					);

					this.$_.handleWkfResp.call(this, ret, false);
					setTimeout(() => {
						this.$router.push('/adm/userAccount');
					}, 5000);
				}
			});
		},
		findDeletePosCodes: function (posCodes) {
			this.deletePosCodes = [];
			const origPosCodes = [];
			for (let i = 0; i < this.origUserBranPositions.length; i++) {
				origPosCodes.push(this.origUserBranPositions[i].posCode);
			}

			for (let i = 0; i < origPosCodes.length; i++) {
				if (!posCodes.includes(origPosCodes[i])) {
					this.deletePosCodes.push(origPosCodes[i]);
				}
			}
		},
		rdoOn: function () {
			this.startFlag = 'Y';
		},
		rdoOff: function () {
			this.startFlag = 'N';
		}
	}
};
</script>
