<template>
	<div>
		<div role="tabpanel" class="tab-pane fade show active">
			<div class="card card-form-collapse">
				<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
					<h4>{{ $t('adm.searchCond') }}</h4>
					<span class="tx-square-bracket">{{ $t('adm.reqField') }}</span>
				</div>

				<div id="formsearch1" class="card-body collapse show">
					<form>
						<div class="form-row">
							<div class="form-group col-lg-6">
								<label class="form-label">{{ $t('adm.selectBranchUnit') }}</label>
								<div class="input-group">
									<input
										id="branNameId"
										v-model="branName"
										type="text"
										name="branName"
										class="form-control"
										readonly="true"
									>
									<button class="btn btn-info" type="button" @click="openBranchModal">
										{{ $t('adm.selectBranchUnit') }}
									</button>
									<button class="btn btn-white" type="button" @click="clearForm()">
										{{ $t('adm.clearForm') }}
									</button>
								</div>
							</div>
							<div class="form-group col-lg-6">
								<label class="form-label">{{ $t('adm.userName') }}</label>
								<input
									v-model="userName"
									name="userName"
									type="text"
									class="form-control"
									size="20"
								>
							</div>
						</div>
						<div class="form-row">
							<div class="form-group col-lg-6">
								<label class="form-label">{{ $t('adm.userCode') }}</label>
								<input
									v-model="userCode"
									name="userCode"
									type="text"
									class="form-control"
									size="20"
									@blur="handleUserCodeBlur"
								>
							</div>
							<div class="form-group col-lg-6">
								<label class="form-label">{{ $t('adm.sysRole') }}</label>
								<select v-model="roleCode" name="roleCode" class="form-select">
									<option value="">
										{{ $t('adm.all') }}
									</option>
									<option v-for="roleData in roleMenu" :value="roleData.roleCode">
										{{ roleData.roleName }}
									</option>
								</select>
							</div>
						</div>
						<div class="form-footer">
							<button class="btn btn-primary" type="button" @click="getPageData(0)">
								{{ $t('adm.search') }}
							</button>
							<button class="btn btn-primary" type="button" @click="exportExcel()">
								{{ $t('adm.downloadExcel') }}
							</button>
						</div>
					</form>
				</div>
			</div>

			<div id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('adm.searchResult') }}</h4>
						<pagination :pageable="pageData" :goto-page="gotoPage" />
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-hover table-bordered">
							<thead>
								<tr>
									<th>{{ $t('adm.branchUnitName') }}</th>
									<th>{{ $t('adm.user') }}</th>
									<th>{{ $t('adm.positionTitle') }}</th>
									<th>{{ $t('adm.businessCategory') }}</th>
									<th>{{ $t('adm.submitDate') }}</th>
									<th>{{ $t('adm.submitter') }}</th>
									<th>{{ $t('adm.reviewDate') }}</th>
									<th>{{ $t('adm.reviewer') }}</th>
									<th>{{ $t('adm.reviewStatus') }}</th>
									<th>{{ $t('adm.roleSetting') }}</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in pageData.content" :key="item.id">
									<td :data-th="$t('adm.branchUnitName')">
										{{ item.branCode }} {{ item.branName }}
									</td>
									<td :data-th="$t('adm.user')">
										{{ item.userCode }} {{ item.userName }}
									</td>
									<td :data-th="$t('adm.positionTitle')">
										{{ item.titleName }}
									</td>
									<td :data-th="$t('adm.businessCategory')">
										{{ item.jobName }}
									</td>
									<td :data-th="$t('adm.submitDate')">
										{{ item.createDt }}
									</td>
									<td :data-th="$t('adm.submitter')">
										{{ item.createBy }} {{ item.createUserName }}
									</td>
									<td :data-th="$t('adm.reviewDate')">
										{{ item.modifyDt }}
									</td>
									<td :data-th="$t('adm.reviewer')">
										{{ item.modifyBy }} {{ item.modifyUserName }}
									</td>

									<td v-if="item.status == 'P'">
										{{ $t('adm.pendingReview') }}
									</td>
									<td v-if="item.status == 'R'">
										<a
											href="#"
											class="tx-link tx-danger"
											data-bs-toggle="modal"
											data-bs-target="#alertModal"
											@click.prevent="showRejectContent(item.reason)"
										>
											{{ $t('adm.returnEdit') }}
										</a>
									</td>
									<td v-if="item.status == 'A'">
										{{ $t('adm.reviewApproved') }}
									</td>
									<td v-if="item.status == null" />

									<td :data-th="$t('adm.roleSetting')" class="text-center">
										<button
											v-if="item.status == 'P'"
											type="button"
											class="btn btn-search btn-icon"
											@click="doViewPosEvents(item.eventId)"
										>
											<i class="bi bi-search" />
										</button>
										<a data-bs-toggle="tooltip" href="#" class="table-icon">
											<button
												v-if="item.status != 'P'"
												type="button"
												data-bs-toggle="tooltip"
												:title="$t('adm.editTooltip')"
												class="btn btn-info btn-icon"
												@click.prevent="setEditAccountToParent(item)"
											>
												<i class="fa-solid fa-pen" />
											</button>
										</a>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<p class="tx-note">
					{{ $t('adm.hrSystemNote') }}
				</p>
			</div>
		</div>
		<!--Tab內容 end-->

		<!--branchModal-->
		<modal ref="branchModal" @close="closeBranchModal">
			<template #content="props">
				<div class="modal-dialog modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 id="branchModal" class="modal-title">
								{{ $t('adm.selectBranchUnit') }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click="props.close()"
							/>
						</div>
						<div class="modal-body">
							<table class="table table-bordered">
								<tbody>
									<tr>
										<td>
											<button type="button" class="btn btn-info btn-glow" @click="expandBtn">
												{{ $t('adm.expandAllModal') }}
											</button>
											<button type="button" class="btn btn-info btn-glow" @click="collapsedBtn">
												{{ $t('adm.collapseAllModal') }}
											</button>
										</td>
									</tr>
									<tr>
										<td>
											<bi-tree
												v-if="tree.treeData"
												ref="tree"
												:tree-cfg="tree.treeCfg"
												:tree-data="tree.treeData"
											/>
											<div>
												<div id="treeview-noborder" />
											</div>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-white" @click="props.close()">
								{{ $t('adm.closeWindow') }}
							</button>
						</div>
					</div>
				</div>
			</template>
		</modal>

		<!-- Detail Modal -->
		<modal ref="detailModal" @close="closeDetailModal">
			<template #content="props">
				<div class="modal-dialog modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 id="view" class="modal-title">
								{{ $t('adm.applicationDetail') }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click="props.close()"
							/>
						</div>
						<div class="modal-body">
							<table class="table table-bordered align-middle">
								<caption>
									{{ $t('adm.roleSettingPreview') }}
								</caption>
								<tr>
									<th class="th-info">
										{{ $t('adm.role') }}
									</th>
									<th class="th-info text-center">
										{{ $t('adm.accountAvailability') }}
									</th>
								</tr>
								<tr>
									<td>
										<ol class="mb-0">
											<li v-for="eventDetail in eventDetails">
												{{ eventDetail.branName }} > {{ eventDetail.roleName }}
											</li>
										</ol>
									</td>
									<td class="text-center">
										<span v-if="accountAvailability == 'Y'" class="icon tx-success">
											<!--										<i class="bi bi-check-circle"></i>-->
											{{ $t('adm.statusEnabled') }}
										</span>
										<span v-if="accountAvailability == 'N'" class="icon tx-danger">
											<!--										<i class="bi bi-check-circle"></i>-->
											{{ $t('adm.statusDisabled') }}
										</span>
									</td>
								</tr>
							</table>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-white" @click="props.close()">
								{{ $t('adm.closeWindow') }}
							</button>
						</div>
					</div>
				</div>
			</template>
		</modal>

		<!--reject Modal-->
		<modal ref="rejectModal" @close="closeAlertModal">
			<template #content="props">
				<div class="modal-dialog modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-body pt-5">
							<div class="icon-alert" />
							<input
								id="recipient-name"
								v-model="rejectMsg"
								type="text"
								disabled
								class="form-control form-control-plaintext tx-20 tx-danger text-center"
							>
						</div>
						<div class="modal-footer">
							<button class="btn btn-primary btn-glow" @click.prevent="props.close()">
								{{ $t('adm.confirmBtn') }}
							</button>
						</div>
					</div>
				</div>
			</template>
		</modal>
	</div>
</template>

<script>
import pagination from '@/views/components/pagination.vue';
import modal from '@/views/components/model.vue';
import biTree from '@/views/components/biTree.vue';
import _ from 'lodash';
import { Tooltip } from 'bootstrap';
import userCodeComplement from '@/utils/mixin/userCodeComplement';
import { downloadBlob } from '@/utils/downloadBlob';

export default {
	components: {
		pagination,
		modal,
		biTree
	},
	mixins: [
		userCodeComplement
	],
	props: {
		setEditAccount: Function
	},
	data: function () {
		return {
			// Api 用參數
			branCode: null,
			branName: null,
			roleCode: null,
			userCode: null,
			userName: null,

			editAccount: {},
			eventDetails: [],
			accountAvailability: 'N',
			// 下拉選單
			branMenu: [],
			roleMenu: [],

			// 主要顯示資料
			pageData: {},
			pageable: {
				page: 0,
				size: 20,
				sort: 'BRAN_CODE',
				direction: 'DESC'
			},
			rejectMsg: null,

			// 分行樹狀選單
			tree: {
				treeData: null,
				treeCfg: {
					tailLinks: [
						{
							type: 'anchor',
							onClick: (id, node) => {
								const name = node.querySelector('.bi-tree-text')?.textContent || '';
								const [buCode, branCode] = id.split('_');
								this.buCode = buCode;
								this.branCode = branCode;
								this.branName = name;
								this.closeBranchModal();
							},
							text: '選擇',
							toApply: true
						}
					]
				}
			}
		};
	},
	mounted: function () {
		this.getBranMenu();
		this.getRoleMenu();
	},
	methods: {
		openBranchModal() {
			this.$refs.branchModal.open();
		},
		closeBranchModal() {
			this.$refs.branchModal.close();
		},
		openDetailModal() {
			this.$refs.detailModal.open();
		},
		closeDetailModal() {
			this.$refs.detailModal.close();
		},
		getBranMenu: async function () {
			const ret = await this.$api.getBranMenuApi();
			this.branMenu = ret.data;
			this.branMenu = this.genTreeLabel(this.branMenu);
			this.tree.treeData = this.branMenu;
		},
		genTreeLabel: function (data) {
			data.forEach((item) => {
				item.text = item.branName;
				if (!this.$_.isEmpty(item.nodes)) {
					this.genTreeLabel(item.nodes);
				}
			});

			return data;
		},
		getRoleMenu: async function () {
			const ret = await this.$api.getRoleMenuApi();
			this.roleMenu = ret.data;
		},
		expandBtn: function () {
			this.$refs.tree.expandAllNodes(true);
		},
		collapsedBtn: function () {
			this.$refs.tree.expandAllNodes(false);
		},
		handleUserCodeBlur: function () {
			this.userCode = this.complementUserCode(this.userCode);
		},
		clearForm: function () {
			this.branCode = null;
			this.branName = null;
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			this.pageable.page = _.isNumber(page) ? page : this.pageable.page;
			const ret = await this.$api.getSysUserApi(this.branCode, this.roleCode, this.userCode, this.userName, this.pageable);
			this.pageData = ret.data;

			// 確保在 Vue 完成渲染後再初始化 tooltip
			this.$nextTick(() => {
				const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
				tooltipTriggerList.forEach((el) => {
					new Tooltip(el);
				});
			});
		},
		exportExcel: async function () {
			const dateStr = this.$moment().format('YYYYMMDD');
			const fileName = this.$t('adm.sysUserQueryExport') + dateStr + '.xls'; // 加上副檔名
			const blob = await this.$api.getExportExcelApi(this.branCode, this.roleCode, this.userCode, this.userName);
			downloadBlob(blob, fileName);
		},
		showRejectContent: function (reason) {
			this.rejectMsg = reason;
			this.$refs.rejectModal.open();
		},
		closeAlertModal: function () {
			this.$refs.rejectModal.close();
		},
		setEditAccountToParent: function (item) {
			this.editAccount.tabCode = 'M00-041';
			this.editAccount.userCode = item.userCode;
			this.setEditAccount(this.editAccount);
		},
		doViewPosEvents: async function (eventId) {
			const ret = await this.$api.getUserPosEventApi(eventId);
			if (ret.data) {
				this.eventDetails = ret.data;
				this.accountAvailability = ret.data[0].startFlag;
				this.openDetailModal();
			}
			else {
				this.eventDetails = [];
				this.accountAvailability = 'N';
			}
		}
	}
};
</script>
