<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form-collapse">
			<div class="card card-table">
				<div class="card-header">
					<h4>{{ $t('adm.regularAssetReviewList') }}</h4>
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-hover text-center">
						<thead>
							<tr>
								<th>{{ $t('adm.customerAssetLevelChineseName') }}</th>
								<th>{{ $t('adm.customerAssetLevelEnglishName') }}</th>
								<th>{{ $t('adm.maintainStaff') }}</th>
								<th>{{ $t('adm.modifyDate') }}</th>
								<th>{{ $t('adm.regularReviewFrequency') }}</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="item in queryResult">
								<td :data-th="$t('adm.customerAssetLevelChineseName')">
									{{ item.graName }}
								</td>
								<td :data-th="$t('adm.customerAssetLevelEnglishName')">
									{{ item.graEname }}
								</td>
								<td :data-th="$t('adm.maintainStaff')">
									{{ item.modifyBy }} {{ item.userName }}
								</td>
								<td :data-th="$t('adm.modifyDate')">
									{{ item.modifyDt }}
								</td>
								<td :data-th="$t('adm.regularReviewFrequency')">
									每<input
										v-model="item.reviewFreqAsset"
										type="number"
										min="0"
										maxlength="9"
									>個月
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div class="form-footer" style="margin-top: 1rem; margin-bottom: 1rem">
				<button class="btn btn-primary btn-glow btn-save" @click="edit()">
					{{ $t('adm.modify') }}
				</button>&nbsp;
				<button class="btn btn-primary btn-glow btn-save" @click="cancelEdit()">
					{{ $t('adm.cancelModify') }}
				</button>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import Swal from 'sweetalert2';
import { Field, Form } from 'vee-validate';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			queryResult: []
		};
	},
	mounted: function () {
		const self = this;
		self.getGrades();
	},
	methods: {
		getGrades: function () {
			const self = this;
			self.$api.getGradesApi().then(function (ret) {
				self.queryResult = ret.data;
			});
		},
		edit: function () {
			const self = this;
			const changedGradesItems = _.clone(self.queryResult)
				.map((item) => {
					if (
						item.reviewFreqAsset == null
						|| item.reviewFreqAsset === ''
						|| item.reviewFreqAsset === '0'
						|| item.reviewFreqAsset === undefined
					) {
						item.reviewFreqAsset = 0;
					}
					else {
						if (_.isNumeric(item.reviewFreqAsset)) {
							if (item.reviewFreqAsset > 999) {
								self.sendErrMsg(self.$t('adm.regularReviewFreqCannotExceed999'));
								return null;
							}
							else {
								const num = parseInt(item.reviewFreqAsset);
								if (num < 0) {
									self.sendErrMsg(self.$t('adm.regularReviewFreqCannotBeNegative'));
									return null;
								}
								if (num > 999) {
									self.sendErrMsg(self.$t('adm.regularReviewFreqCannotExceed999')); // TODO 待SA提供
									return null;
								}
								item.reviewFreqAsset = num;
							}
						}
						else {
							self.sendErrMsg(self.$t('adm.regularReviewFreqMustBeNumber'));
							return null;
						}
					}
					return item;
				})
				.filter(item => item != null);
			if (changedGradesItems.length !== self.queryResult.length) {
				return;
			}

			self.$api
				.patchGradesApi(changedGradesItems)
				.then(function (ret) {
					Swal.fire({
						text: self.$t('adm.saveSuccess'),
						icon: 'success',
						showCloseButton: true,
						confirmButtonText: self.$t('adm.confirmBtn'),
						buttonsStyling: false,
						customClass: {
							confirmButton: 'btn btn-success'
						}
					});
					self.getGrades();
				})
				.catch(function (err) {
					self.sendErrMsg(self.$t('adm.saveFailed'));
				});
		},
		cancelEdit: function () {
			const self = this;
			self.getGrades();
		},
		sendErrMsg: function (msg) {
			Swal.fire({
				text: msg,
				icon: 'error',
				showCloseButton: true,
				confirmButtonText: '確認',
				buttonsStyling: false,
				customClass: {
					confirmButton: 'btn btn-danger'
				}
			});
			return;
		}
	}
};
</script>
