<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#card-body">
				<h4>請輸入下列資料</h4>
				<span class="tx-square-bracket">為必填欄位</span>
			</div>
			<vue-form v-slot="{ errors }" ref="queryForm">
				<div id="card-body" class="card-body collapse show">
					<div class="row g-3 align-items-end">
						<div class="col-md-4">
							<div class="form-group" style="margin-bottom: 0px">
								<label class="form-label tx-require">其他補充資料大類</label>
								<vue-field
									v-model="queSectionId"
									as="select"
									class="form-select ms-4"
									:class="{ 'is-invalid': errors.queSectionId }"
									name="queSectionId"
									label="補充資料大類"
									rules="required"
									@change="getCusExtDataQueItemsMenu()"
								>
									<option selected value>
										--
									</option>
									<option v-for="menu in cusExtDataQueSectionsMenu" :value="menu.queSectionId">
										{{ menu.queSectionName }}
									</option>
								</vue-field>
							</div>
							<div class="text-danger" style="height: 15px">
								{{ errors.queSectionId }}
							</div>
						</div>
						<div class="col-md-4">
							<div class="form-group" style="margin-bottom: 0px">
								<label class="form-label tx-require">其他補充資料類別</label>
								<vue-field
									v-model="queItemId"
									as="select"
									class="form-select ms-4"
									:class="{ 'is-invalid': errors.queItemId }"
									name="queItemId"
									label="補充資料類別"
									rules="required"
								>
									<option value selected>
										--
									</option>
									<option v-for="menu in cusExtDataQueItemsMenu" :value="menu.queItemId">
										{{ menu.queItemName }}
									</option>
								</vue-field>
							</div>
							<div class="text-danger" style="height: 15px">
								{{ errors.queItemId }}
							</div>
						</div>
						<div class="col-md-4">
							<div class="form-group" style="margin-bottom: 0px">
								<label class="form-label tx-require">屬性中文名稱</label>
								<vue-field
									v-model="queItemSelName"
									type="text"
									class="form-control"
									:class="{ 'is-invalid': errors.queItemSelName }"
									name="queItemSelName"
									label="屬性中文名稱"
									rules="required"
								/>
							</div>
							<div class="text-danger" style="height: 15px">
								{{ errors.queItemSelName }}
							</div>
						</div>
						<div class="col-md-4">
							<div class="form-group" style="margin-bottom: 0px">
								<label class="form-label">屬性英文名稱</label>
								<input v-model="queItemSelEname" type="text" class="form-control">
							</div>
							<div class="text-danger" style="height: 15px" />
						</div>
						<div class="col-md-4">
							<div class="form-group" style="margin-bottom: 0px">
								<label class="form-label tx-require">顯示順序</label>
								<vue-field
									v-model="showOrder"
									type="number"
									name="showOrder"
									:class="{ 'is-invalid': errors.showOrder }"
									min="1"
									class="form-control"
									onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')"
									label="顯示順序"
									rules="required"
								/>
							</div>
							<div class="text-danger" style="height: 15px">
								{{ errors.showOrder }}
							</div>
						</div>
						<div class="form-group col-lg-4">
							<label class="form-label me-3">選項是否有輸入框</label><br>
							<template v-for="item in optionYnList">
								<div class="form-check form-check-inline">
									<input
										:id="'inputYn' + item.codeValue"
										v-model="inputYn"
										class="form-check-input"
										type="radio"
										:value="item.codeValue"
									>
									<label class="form-check-label" :for="'inputYn' + item.codeValue">{{ item.codeName }}</label>
								</div>
							</template>
						</div>
						<div class="form-group col-lg-4">
							<label class="form-label me-3">選項是否換行</label><br>
							<template v-for="item in optionYnList">
								<div class="form-check form-check-inline">
									<input
										:id="'brLine' + item.codeValue"
										v-model="brLine"
										class="form-check-input"
										type="radio"
										:value="item.codeValue"
									>
									<label class="form-check-label" :for="'brLine' + item.codeValue">{{ item.codeName }}</label>
								</div>
							</template>
						</div>
						<div class="form-footer">
							<button v-if="isEmpty(queItemSelId)" class="btn btn-primary btn-glow btn-save" @click.prevent="insertExtDataQueItem()">
								儲存
							</button>
							<button v-if="!isEmpty(queItemSelId)" class="btn btn-secondary btn-glow btn-cancle" @click.prevent="cleanForm()">
								取消修改
							</button>&nbsp;
							<button
								v-if="!isEmpty(queItemSelId)"
								class="btn btn-primary btn-glow btn-modify ms-1"
								@click.prevent="updateExtDataQueItem()"
							>
								修改
							</button>
						</div>
					</div>
				</div>
			</vue-form>
		</div>

		<div class="card card-table">
			<div class="card-header">
				<h4>客戶其他補充資料列表</h4>
				<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-hover text-center">
					<thead>
						<tr>
							<th>補充資料大類</th>
							<th>補充資料類別</th>
							<th>屬性中文名稱</th>
							<th>屬性英文名稱</th>
							<th>顯示順序</th>
							<th>選項是否有輸入框</th>
							<th>選項是否換行</th>
							<th>執行</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="item in pageData.content">
							<td data-th="補充資料大類">
								{{ item.queSectionName }}
							</td>
							<td data-th="補充資料類別">
								{{ item.queItemName }}
							</td>
							<td data-th="屬性中文名稱">
								{{ item.queItemSelName }}
							</td>
							<td data-th="屬性英文名稱">
								{{ item.queItemSelEname }}
							</td>
							<td data-th="顯示順序">
								{{ item.showOrder }}
							</td>
							<td data-th="選項是否有輸入框">
								{{ item.inputYn }}
							</td>
							<td data-th="選項是否換行">
								{{ item.brLine }}
							</td>
							<td>
								<button
									type="button"
									class="btn btn-info btn-glow btn-icon btn-edit"
									data-bs-toggle="tooltip"
									data-bs-original-title="編輯"
									@click="doUpdate(item)"
								>
									<i class="bi bi-pen" />
								</button>
								<button
									type="button"
									class="btn btn-danger btn-glow btn-icon"
									data-bs-toggle="tooltip"
									data-bs-original-title="刪除"
									@click="doDelete(item)"
								>
									<i class="bi bi-trash" />
								</button>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>

<script>
import { _ } from 'lodash';
import Swal from 'sweetalert2';
import vuePagination from '@/views/components/pagination.vue';
import { Field, Form } from 'vee-validate';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vuePagination
	},
	data: function () {
		return {
			// API 用參數
			queItemSelId: null,
			queSectionId: null,
			queItemId: null,
			queItemSelName: null,
			queItemSelEname: null,
			showOrder: null,
			inputYn: null,
			brLine: null,
			// 下拉選單
			cusExtDataQueSectionsMenu: [],
			cusExtDataQueItemsMenu: [],

			optionYnList: [], // 啟用狀態
			// 主要顯示資料
			pageData: {},
			pageable: {
				page: 0,
				size: 20,
				sort: 'SHOW_ORDER',
				direction: 'ASC'
			}
		};
	},
	mounted: function () {
		const self = this;
		self.gotoPage(0);
		self.getCusExtDataQueSectionsMenu();
		self.getOptionYn();
	},
	methods: {
		isEmpty(value) {
			return _.isEmpty(value);
		},
		getOptionYn: function () {
			const self = this;
			const optionY = {
				codeValue: 'Y',
				codeName: '是'
			};
			const optionN = {
				codeValue: 'N',
				codeName: '否'
			};

			self.optionYnList.push(optionY);
			self.optionYnList.push(optionN);
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function (page) {
			const self = this;
			self.$api.getCusExtDataQueItemSelsApi({
				page: self.pageable.page,
				size: self.pageable.size,
				sort: self.pageable.sort,
				direction: self.pageable.direction
			}).then(function (ret) {
				self.pageData = ret.data;
			});
		},
		getCusExtDataQueSectionsMenu: function () {
			const self = this;
			self.$api.getCusExtDataQueSectionsMenuApi().then(function (ret) {
				self.cusExtDataQueSectionsMenu = ret.data;
			});
		},
		getCusExtDataQueItemsMenu: function () {
			const self = this;
			if (_.isBlank(self.queSectionId)) {
				self.cusExtDataQueItemsMenu = [];
				return;
			}
			self.$api.getCusExtDataQueItemsMenuApi({ queSectionId: self.queSectionId }).then(function (ret) {
				self.cusExtDataQueItemsMenu = ret.data;
			});
		},
		doUpdate: function (item) {
			const self = this;

			self.queItemSelId = item.queItemSelId;
			self.queSectionId = item.queSectionId;
			self.queItemId = item.queItemId;
			self.queItemSelName = item.queItemSelName;
			self.queItemSelEname = item.queItemSelEname;
			self.showOrder = item.showOrder;
			self.inputYn = item.inputYn;
			self.brLine = item.brLine;
			self.getCusExtDataQueItemsMenu();
			document.body.scrollTop = 0;
			document.documentElement.scrollTop = 0;
		},
		cleanForm: function () {
			const self = this;
			self.queItemSelEname = null;
			self.inputYn = null;
			self.brLine = null;
			self.queItemSelId = null;
			self.getCusExtDataQueItemsMenu();
			self.$refs.queryForm.resetForm();
		},
		insertExtDataQueItem: function () {
			const self = this;
			if (self.requireValueValid() == false) {
				return;
			}
			const queryForm = self.$refs.queryForm;
			if (self.showOrder < 0) {
				queryForm.setFieldError('showOrder', '顯示順序需大於0。');
				return;
			}
			queryForm.validate().then(function (pass) {
				if (pass.valid) {
					self.$api
						.postCusExtDataQueItemSelsApi({
							queItemSelId: self.queItemSelId,
							queSectionId: self.queSectionId,
							queItemId: self.queItemId,
							queItemSelName: self.queItemSelName,
							queItemSelEname: self.queItemSelEname,
							showOrder: self.showOrder,
							inputYn: self.inputYn,
							brLine: self.brLine
						})
						.then(function (ret) {
							Swal.fire({
								icon: 'success',
								title: '新增成功',
								confirmButtonText: '確認'
							});
							self.gotoPage(self.pageable.page);
							self.cleanForm();
						});
				}
			});
		},
		updateExtDataQueItem: function () {
			const self = this;
			if (self.requireValueValid() == false) {
				return;
			}
			self.$refs.queryForm.validate().then(function (pass) {
				if (pass.valid) {
					self.$api
						.patchCusExtDataQueItemSelsApi({
							queItemSelId: self.queItemSelId,
							queSectionId: self.queSectionId,
							queItemId: self.queItemId,
							queItemSelName: self.queItemSelName,
							queItemSelEname: self.queItemSelEname,
							showOrder: self.showOrder,
							inputYn: self.inputYn,
							brLine: self.brLine
						})
						.then(function (ret) {
							Swal.fire({
								icon: 'success',
								title: '更新成功',
								confirmButtonText: '確認'
							});
							self.gotoPage(self.pageable.page);
							self.cleanForm();
						});
				}
			});
		},
		doDelete: function (item) {
			const self = this;
			this.$bi.confirm('確定要刪除此筆資料嗎?', {
				event: {
					confirmOk: function () {
						self.$api.deleteCusExtDataQueItemSelsApi({ queItemSelId: item.queItemSelId }).then(function (ret) {
							Swal.fire({
								icon: 'success',
								title: '刪除成功',
								confirmButtonText: '確認'
							});
							self.gotoPage(self.pageable.page);
						});
					}
				}
			});
		},
		sendErrMsg: function (msg) {
			Swal.fire({
				text: msg,
				icon: 'error',
				showCloseButton: true,
				confirmButtonText: '確認',
				buttonsStyling: false,
				customClass: {
					confirmButton: 'btn btn-danger'
				}
			});
			return;
		},
		requireValueValid: function () {
			const self = this;
			if (_.isBlank(self.queSectionId)) {
				self.sendErrMsg('請輸入其他補充資料大類');
				return false;
			}
			if (_.isBlank(self.queItemId)) {
				self.sendErrMsg('請輸入其他補充資料類別');
				return false;
			}
			if (_.isBlank(self.queItemSelName)) {
				self.sendErrMsg('請輸入屬性中文名稱');
				return false;
			}
			if (_.isBlank(self.showOrder)) {
				self.sendErrMsg('請輸入顯示順序');
				return false;
			}

			return true;
		}
	}
};
</script>
