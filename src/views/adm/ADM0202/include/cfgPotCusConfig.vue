<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#card-body">
				<h4>{{ $t('adm.pleaseEnterFollowingData') }}</h4>
				<span class="tx-square-bracket">{{ $t('adm.reqField') }}</span>
			</div>

			<vue-form v-slot="{ errors }" ref="queryForm">
				<div id="card-body" class="card-body collapse show">
					<div class="row g-3 align-items-end">
						<div class="col-md-4">
							<div class="form-group" style="margin-bottom: 0px">
								<label class="form-label tx-require">{{ $t('adm.minRmLevel') }}</label>
								<vue-field
									v-model="rmsLvlCode"
									as="select"
									class="form-select"
									:class="{ 'is-invalid': errors.rmsLvlCode }"
									style="margin-left: 3.5rem"
									name="rmsLvlCode"
									:label="$t('adm.minRmLevel')"
									rules="required"
								>
									<option selected value>
										--
									</option>
									<option v-for="option in rmsLvlCodeList" :value="option.rmLvlCode">
										{{ option.rmLvlName }}
									</option>
								</vue-field>
							</div>
							<div class="text-danger" style="height: 25px">
								{{ errors.rmsLvlCode }}
							</div>
						</div>
						<div class="col-md-4">
							<div class="form-group" style="margin-bottom: 0px">
								<label style="margin-bottom: 0.5rem; font-weight: bold; margin-right: 1.5rem">{{ $t('adm.customerLevelCode') }}</label><br>
								<label style="margin-bottom: 0.5rem; font-weight: bold">{{ graCode }}</label>
							</div>
							<div style="height: 25px" />
						</div>
						<div class="col-md-4">
							<div class="form-group" style="margin-bottom: 0px">
								<label class="form-label tx-require">{{ $t('adm.levelChineseName') }}</label>
								<vue-field
									v-model="graName"
									type="text"
									class="form-control"
									:class="{ 'is-invalid': errors.graName }"
									name="graName"
									:label="$t('adm.levelChineseName')"
									rules="required"
								/>
							</div>
							<div class="text-danger" style="height: 25px">
								{{ errors.graName }}
							</div>
						</div>
						<div class="col-md-4">
							<div class="form-group" style="margin-bottom: 0px">
								<label class="form-label tx-require">{{ $t('adm.auaLowerLimit') }}</label>
								<vue-field
									v-model="auaMin"
									type="text"
									class="form-control ms-4"
									:class="{ 'is-invalid': errors.auaMin }"
									name="auaMin"
									:label="$t('adm.auaLowerLimit')"
									rules="required"
								/>
							</div>
							<span class="text-danger" style="height: 25px">{{ errors.auaMin }}</span>
						</div>
						<div class="col-md-4">
							<div class="form-group" style="margin-bottom: 0px">
								<label class="form-label tx-require">{{ $t('adm.auaUpperLimit') }}</label>
								<vue-field
									v-model="auaMax"
									type="text"
									name="auaMax"
									:class="{ 'is-invalid': errors.auaMax }"
									min="1"
									class="form-control ms-4"
									:label="$t('adm.auaUpperLimit')"
									rules="required"
								/>
							</div>
							<div class="text-danger" style="height: 25px">
								{{ errors.auaMax }}
							</div>
						</div>
						<div class="form-footer">
							<button class="btn btn-primary btn-glow btn-save" @click.prevent="edit()">
								{{ $t('adm.modify') }}
							</button>&nbsp;
							<button class="btn btn-primary btn-glow btn-save" @click="cancelEdit()">
								{{ $t('adm.cancelModify') }}
							</button>
						</div>
					</div>
				</div>
			</vue-form>
		</div>

		<div class="card card-table">
			<div class="card-header">
				<h4>{{ $t('adm.customerAssetLevelList') }}</h4>
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-hover text-center">
					<thead>
						<tr>
							<th>{{ $t('adm.customerLevelCode') }}</th>
							<th>{{ $t('adm.assetLevelChineseName') }}</th>
							<th>{{ $t('adm.assetRange') }}</th>
							<th>{{ $t('adm.minRmLevel') }}</th>
							<th>{{ $t('adm.maintainStaff') }}</th>
							<th>{{ $t('adm.modifyDate') }}</th>
							<th>{{ $t('adm.execute') }}</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="item in queryResultList">
							<td :data-th="$t('adm.customerLevelCode')">
								{{ item.graCode }}
							</td>
							<td :data-th="$t('adm.assetLevelChineseName')">
								{{ item.graName }}
							</td>
							<td :data-th="$t('adm.assetRange')">
								{{ item.auaMin }} ~ {{ item.auaMax }}
							</td>
							<td :data-th="$t('adm.minRmLevel')">
								{{ item.rmLvlName }}
							</td>
							<td :data-th="$t('adm.maintainStaff')">
								{{ item.modifyBy }} {{ item.userName }}
							</td>
							<td :data-th="$t('adm.modifyDate')">
								{{ item.modifyDt }}
							</td>
							<td>
								<button
									type="button"
									class="btn btn-info btn-glow btn-icon btn-edit"
									data-bs-toggle="tooltip"
									:data-bs-original-title="$t('adm.editTooltip')"
									@click="beforeEdit(item)"
								>
									<i class="bi bi-pen" />
								</button>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import Swal from 'sweetalert2';
import { Field, Form } from 'vee-validate';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			potCusUl: null,
			potCusValidDt: null,
			rmsLvlCode: null,
			graCode: null,
			graName: null,
			auaMin: null,
			auaMax: null,
			rmsLvlCodeList: [],
			queryResultList: []
		};
	},
	mounted: function () {
		const self = this;
		self.getRmLvlMenu();
		self.getCusGradesSetLists();
	},
	methods: {
		getRmLvlMenu: function () {
			const self = this;
			self.$api.getRmLvlMenuApi().then(function (ret) {
				self.rmsLvlCodeList = ret.data;
			});
		},
		getCusGradesSetLists: function () {
			const self = this;
			self.$api.getCusGradesSetListsApi().then(function (ret) {
				self.queryResultList = ret.data;
			});
		},
		beforeEdit: function (queryResult) {
			const self = this;
			self.rmsLvlCode = queryResult.rmLvlCode;
			self.graCode = queryResult.graCode;
			self.graName = queryResult.graName;
			self.auaMin = queryResult.auaMin;
			self.auaMax = queryResult.auaMax;
		},
		edit: function () {
			const self = this;
			if (_.isBlank(self.rmsLvlCode)) {
				self.sendErrMsg(self.$t('adm.pleaseEnterMinRmLevel'));
				return;
			}
			if (_.isBlank(self.graName)) {
				self.sendErrMsg(self.$t('adm.pleaseEnterLevelChineseName'));
				return;
			}
			if (_.isBlank(self.auaMin)) {
				self.sendErrMsg(self.$t('adm.pleaseEnterAuaLowerLimit'));
				return;
			}
			if (_.isBlank(self.auaMax)) {
				self.sendErrMsg(self.$t('adm.pleaseEnterAuaUpperLimit'));
				return;
			}
			if (!_.isNumeric(self.auaMin)) {
				self.sendErrMsg(self.$t('adm.auaLowerLimitNotNumber'));
				return;
			}
			if (!_.isNumeric(self.auaMax)) {
				self.sendErrMsg(self.$t('adm.auaUpperLimitNotNumber'));
				return;
			}
			if (_.toNumber(self.auaMin) > _.toNumber(self.auaMax)) {
				self.sendErrMsg(self.$t('adm.auaUpperMustGreaterThanLower'));
				return;
			}

			self.$api
				.patchCusGradesSetListsApi({
					graCode: self.graCode,
					rmLvlCode: self.rmsLvlCode,
					graName: self.graName,
					auaMin: self.auaMin,
					auaMax: self.auaMax
				})
				.then(function (ret) {
					self.getCusGradesSetLists();
				})
				.catch(function (err) {
					self.sendErrMsg(err.message);
				});
		},
		cancelEdit: function () {
			const self = this;
			self.graCode = null;
			self.rmsLvlCode = null;
			self.graName = null;
			self.auaMin = null;
			self.auaMax = null;
		},
		sendErrMsg: function (msg) {
			Swal.fire({
				text: msg,
				icon: 'error',
				showCloseButton: true,
				confirmButtonText: this.$t('adm.confirmBtn'),
				buttonsStyling: false,
				customClass: {
					confirmButton: 'btn btn-danger'
				}
			});
			return;
		}
	}
};
</script>
