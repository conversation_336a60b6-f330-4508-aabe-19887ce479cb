<template>
	<div class="row">
		<div class="col-12">
			<vue-bi-tabs :menu-code="'M01-01'" @change-tab="changeTab">
				<template #default="{ id }">
					<component :is="id" />
				</template>
			</vue-bi-tabs>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
import vueBiTabs from '@/views/components/biTabs.vue';
import vueCfgPotCusConfig from './include/cfgPotCusConfig.vue';
import vueCfgCusRegularView from './include/cfgCusRegularView.vue';
import vueCfgCusExtraInfo from './include/cfgCusExtraInfo.vue';

export default {
	components: {
		vueBiTabs,
		vueCfgCusExtraInfo,
		vueCfgPotCusConfig,
		vueCfgCusRegularView
	},
	data: function () {
		return {
			// Screen display parameters
			customTitle: null,
			// Screen logic parameters
			tabCodeTitleMap: {
				'M01-010': '客戶其他補充資料',
				'M01-011': this.$t('adm.customerAssetLevel'),
				'M01-012': this.$t('adm.customerRegularReview')
				// 'M01-013': '體驗戶參數'
			}
		};
	},
	methods: {
		changeTab: function (tabCode) {
			const self = this;
			self.customTitle = self.tabCodeTitleMap[tabCode];
		}
	}
};
</script>
