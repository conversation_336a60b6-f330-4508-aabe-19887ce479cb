<template>
	<div class="container-body">
		<div class="container">
			<div class="logo">
				<img src="../assets/images/logo/logo-ifa.svg" alt="">
			</div>

			<h2>{{ $t('core.userSelection') }}</h2>

			<div class="input-group">
				<label>{{ $t('core.user') }}</label>
				<input :value="userName" readonly>
			</div>
			<div class="input-group">
				<label for="pos-select">{{ $t('core.selectRole') }}</label>
				<select id="pos-select" v-model="selectedPosCode" class="form-select wd-100p">
					<option v-for="(item, i) in userPositions" :key="i" :value="item.posCode">
						{{ item.posName }}
					</option>
				</select>
			</div>
			<button class="btn btn-primary" type="button" @click="login">
				{{ $t('core.login') }}
			</button>
		</div>
	</div>
</template>

<script>
import { setToken } from '@/utils/auth.js';

export default {
	// mixins: [userCodeComplement],
	data: function () {
		return {
			userName: '王大明',
			jwtToken: '',
			// userCode: null, //[[${userCode ?: null}]],
			// valid: false, //[[${valid ?: false}]],
			// isUserAccountSwitch: false, //[[${isUserAccountSwitch ?: false}]],
			// pwd: null,
			// userDeputies: null,
			userPositions: [],
			// selectedUserCode: null,
			selectedPosCode: null
		};
	},
	// mounted: function () {
	// 	if (this.userCode && this.valid) {
	// 		this.getUserDeputies();
	// 	}
	// },
	watch: {
		// selectedUserCode: function (newVal, oldVal) {
		// 	if (newVal) {
		// 		this.getUserPositions();
		// 	}
		// },
		// valid: function (newVal, oldVal) {
		// 	if (newVal) {
		// 		this.getUserDeputies();
		// 	} else {
		// 		this.pwd = null;
		// 		this.userDeputies = null;
		// 		this.userPositions = null;
		// 		this.selectedUserCode = null;
		// 		this.selectedPosCode = null;
		// 	}
		// }
	},
	created() {
		this.getUserRoles();
	},
	methods: {
		getUserRoles: async function () {
			const res = await this.$api.getUserRolesApi();
			this.userPositions = res.data;
			this.selectedPosCode = this.userPositions[0].posCode;
		},
		login: async function () {
			const ret = await this.$api.authenticate(this.selectedPosCode);

			if (ret.data.accessToken && ret.data.refreshToken) {
				setToken('accessToken', ret.data.accessToken);
				setToken('refreshToken', ret.data.refreshToken);
				this.$router.push('/');
			}
			else {
				this.$swal.fire({
					title: 'error',
					text: this.$t('core.accountOrPasswordError'),
					icon: 'error'
				});
			}
		}
	}
};
</script>

<style scoped>
@import '../assets/css/bi/login.css';
</style>
