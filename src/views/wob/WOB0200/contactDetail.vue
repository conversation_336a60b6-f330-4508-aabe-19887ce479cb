<template>
	<div>
		<div class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
				<h4>{{ $t('wob.searchCond') }}</h4>
				<span class="tx-square-bracket">{{ $t('wob.requiredField') }}</span>
			</div>
			<div id="formsearch1" class="card-body collapse show">
				<vue-form v-slot="{ errors, validate }" ref="queryForm">
					<div class="row g-3 align-items-end">
						<div class="col-lg-6">
							<label class="form-label tx-require">{{ $t('wob.contactDate') }}</label>
							<div class="input-group">
								<vue-field
									id="startDt"
									v-model="startDt"
									type="date"
									name="startDt"
									size="13"
									:label="$t('wob.contactDateFrom')"
									class="form-control"
									maxlength="10"
									rules="required"
									:class="{ 'is-invalid': errors.startDt }"
								/>

								<span class="input-group-text">~</span>
								<vue-field
									id="endDt"
									v-model="endDt"
									type="date"
									name="endDt"
									size="13"
									:label="$t('wob.contactDateTo')"
									class="form-control"
									maxlength="10"
									:min="startDt"
									rules="required"
									:class="{ 'is-invalid': errors.endDt }"
								/>
								<div style="height: 25px">
									<span v-show="errors.startDt" class="text-danger">{{ errors.startDt }}</span>
									<span v-show="errors.endDt" class="text-danger">{{ errors.endDt }}</span>
								</div>
							</div>
						</div>
						<div class="col-lg-6">
							<label class="form-label">{{ $t('wob.organization') }}</label>
							<div class="input-group">
								<vue-field
									id="minorAreaBranCode"
									v-model="minorAreaBranCode"
									as="select"
									class="form-select"
									name="minorAreaBranCode"
									:label="$t('wob.organizationArea')"
								>
									<option value="ALL">
										{{ $t('wob.all') }}
									</option>
									<option v-for="(area, index) in areaMenu" :key="index" :value="area.branCode">
										{{ area.branName }}
									</option>
								</vue-field>
								<vue-field
									id="branCode"
									v-model="branCode"
									as="select"
									name="branCode"
									class="form-select"
									:label="$t('wob.organizationBranch')"
								>
									<option value="ALL">
										{{ $t('wob.all') }}
									</option>
									<option v-for="(branch, index) in branMenu" :key="index" :value="branch.branCode">
										{{ $filters.defaultValue(branch.branCode, ' ') }} {{ $filters.defaultValue(branch.branName, ' ') }}
									</option>
								</vue-field>
								<vue-field
									id="rmUserCode"
									v-model="rmUserCode"
									as="select"
									name="rmUserCode"
									class="form-select"
									:label="$t('wob.organizationAO')"
								>
									<option value="ALL">
										{{ $t('wob.all') }}
									</option>
									<option v-for="(rm, index) in userMenu" :key="index" :value="rm.userCode">
										{{ rm.userName }}
									</option>
								</vue-field>
							</div>
						</div>
						<div class="col-lg-3">
							<label class="form-label">{{ $t('wob.customerIdTaxId') }}</label><br>
							<div class="input-group">
								<input
									id="cusIdentity"
									v-model="cusCode"
									name="cusIdentity"
									class="form-control"
									type="text"
									size="15"
									maxlength="20"
								>
							</div>
						</div>
						<div class="col-lg-3">
							<label class="form-label">{{ $t('wob.category') }}</label><br>
							<div class="input-group">
								<vue-field
									id="visitEventCode"
									v-model="visitEventCode"
									as="select"
									name="visitEventCode"
									class="form-select"
									:label="$t('wob.category')"
								>
									<option value="" selected="selected">
										{{ $t('wob.pleaseSelect') }}
									</option>
									<option v-for="(visitEvent, index) in visitEventMenu" :key="index" :value="visitEvent.codeValue">
										{{ visitEvent.codeName }}
									</option>
								</vue-field>
							</div>
						</div>
						<div class="col-lg-3">
							<template v-if="visitEventCode === 'V'">
								<label class="form-label">{{ $t('wob.interviewPurpose') }}</label><br>
								<div class="input-group">
									<vue-field
										id="visitPur"
										v-model="visitPurCode"
										as="select"
										name="visitPur"
										class="form-select"
										:label="$t('wob.interviewPurpose')"
									>
										<option value="" selected="selected">
											{{ $t('wob.pleaseSelect') }}
										</option>
										<option v-for="(visitPur, index) in visitPurMenu" :key="index" :value="visitPur.codeValue">
											{{ visitPur.codeName }}
										</option>
									</vue-field>
								</div>
							</template>
						</div>
						<div class="col-lg-3 text-end">
							<button class="btn btn-primary" @click.prevent="gotoPage(0)">
								{{ $t('wob.search') }}
							</button>
						</div>
					</div>
				</vue-form>
			</div>
		</div>
		<p class="tx-note mb-3">
			{{ $t('wob.dateRangeNote') }}
		</p>
		<div v-if="pageData.totalElements >= 0" class="card card-table">
			<div class="card-header">
				<h4>{{ $t('wob.searchResults') }}</h4>
				<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-bordered">
					<thead>
						<tr>
							<th width="5%" class="text-center">
								<input
									v-model="checkAll"
									class="form-check-input"
									type="checkbox"
									@change="toggleCheckAll"
								>
							</th>
							<th width="8%">
								{{ $t('wob.createDate') }}<a class="icon-sort" @click="sort('CREATE_DT')" />
							</th>
							<th width="8%">
								{{ $t('wob.contactDate') }}<a class="icon-sort" @click="sort('VISIT_DT,NEXTREMIND_TIME')" />
							</th>
							<th width="8%">
								{{ $t('wob.customerIdTaxId') }}<a class="icon-sort" @click="sort('IDN')" />
							</th>
							<th width="8%">
								{{ $t('wob.customerName') }}<a class="icon-sort" @click="sort('CUS_NAME')" />
							</th>
							<th width="9%">
								{{ $t('wob.categoryItem') }}<a class="icon-sort" @click="sort('ITEM_NAME')" />
							</th>
							<th width="9%">
								{{ $t('wob.subject') }}<a class="icon-sort" @click="sort('TITLE')" />
							</th>
							<th width="9%">
								{{ $t('wob.processingContent') }}<a class="icon-sort" @click="sort('CONTENT')" />
							</th>
							<th width="9%">
								{{ $t('wob.processingStatus') }}<a class="icon-sort" @click="sort('STATUS_NAME')" />
							</th>
							<th width="8%">
								{{ $t('wob.processingPerson') }}<a class="icon-sort" @click="sort('CREATE_USER_NAME')" />
							</th>
							<th width="8%">
								{{ $t('wob.managingAO') }}<a class="icon-sort" @click="sort('RM_USER_NAME')" />
							</th>
							<th width="6%">
								{{ $t('wob.branch') }}<a class="icon-sort" @click="sort('BRAN_NAME')" />
							</th>
						</tr>
					</thead>
					<tbody id="wrapperList">
						<tr v-for="tdRec in pageData.content">
							<td class="text-center">
								<input
									v-model="selectedRecs"
									class="form-check-input"
									type="checkbox"
									:value="tdRec"
								>
							</td>
							<td :data-th="$t('wob.createDate')">
								{{ tdRec.createDt }}
							</td>
							<td :data-th="$t('wob.contactDate')">
								{{ tdRec.visitDt }} {{ tdRec.nextremindTime }}
							</td>
							<td :data-th="$t('wob.customerIdTaxId')">
								{{ tdRec.cusCode }}
							</td>
							<td :data-th="$t('wob.customerName')">
								{{ tdRec.cusName }}
								<span v-if="tdRec.identityCode" :class="getCusIdentityInfo(tdRec.identityCode).className">
									{{ getCusIdentityInfo(tdRec.identityCode).codeName }}
								</span>
							</td>
							<td :data-th="$t('wob.categoryItem')">
								{{ tdRec.itemName }}
							</td>
							<td :data-th="$t('wob.subject')">
								{{ tdRec.title }}
							</td>
							<td :data-th="$t('wob.processingContent')">
								{{ tdRec.content }}
							</td>
							<td :data-th="$t('wob.processingStatus')">
								{{ tdRec.statusName }}
							</td>
							<td :data-th="$t('wob.processingPerson')">
								{{ tdRec.createUserCode }}<br>{{ tdRec.createUserName }}
							</td>
							<td :data-th="$t('wob.managingAO')">
								{{ tdRec.rmUserCode }}<br>{{ tdRec.rmUserName }}
							</td>
							<td :data-th="$t('wob.branch')">
								{{ tdRec.branCode }}<br>{{ tdRec.branName }}
							</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div v-if="pageData.totalElements > 0" class="row my-3 ms-1">
				<vue-form v-slot="{ errors }" ref="cusSearch">
					<div class="col-auto">
						<div class="input-group">
							<div class="input-group-text">
								{{ $t('wob.joinKeyCustomerGroup') }}
							</div>
							<vue-field
								id="groupList"
								v-model="groupCode"
								name="groupCode"
								class="form-select"
								rules="required"
								:label="$t('wob.customerGroup')"
								as="select"
								:class="{ 'is-invalid': errors.groupCode }"
							>
								<option value="">
									{{ $t('wob.pleaseSelect') }}
								</option>
								<option v-for="groupCodeData in groupCodeMenu" :value="groupCodeData.groupCode">
									{{ groupCodeData.groupName }}
								</option>
							</vue-field>
							<Button
								color="info"
								:disabled="selectedRecs.length === 0"
								:label="$t('wob.save')"
								@click="updateCusGroup()"
							/>
							<Button
								color="white"
								link
								:label="$t('wob.editKeyCustomers')"
								@click="$router.push('/cus/favCusSetup')"
							/>
						</div>
						<div style="height: 25px">
							<span v-show="errors.groupCode" class="text-danger">{{ errors.groupCode }}</span>
							<span v-show="errors.selectedCusCodes" class="text-danger">{{ errors.selectedCusCodes }}</span>
						</div>
					</div>
				</vue-form>
			</div>
		</div>
		<!--頁面內容 end-->
	</div>
	<!-- 聯繫 -->
	<vue-modal :is-open="isOpenModal" @close="() => isOpenModal = false">
		<template #content>
			<div class="modal-dialog modal-dialog-centered modal-xl">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('wob.contact') }}
						</h4>
						<button type="button" class="btn-expand">
							<i class="bi bi-arrows-fullscreen" />
						</button>
						<button
							type="button"
							class="btn-close"
							data-bs-dismiss="modal"
							aria-label="Close"
						/>
					</div>
					<div class="modal-body">
						<div class="card card-prointro card-clientCard mb-3">
							<div class="row align-items-center">
								<div class="col-lg-1 text-center">
									<div class="avatar avatar-male" />
									<h5>{{ contactDetail.cusName }}</h5>
								</div>
								<div class="col-lg-11">
									<div class="tx-title tx-16-f mt-2">
										{{ $t('wob.identity') }}：{{ getCusIdentityInfo(contactDetail.identityCode).codeName }} / {{ $t('wob.age') }} ：{{ contactDetail.age }} / AUM
										：{{ $filters.formatAmt(contactDetail.invAmtAua) }} / {{ $t('wob.recentContactDate') }} ：{{ contactDetail.lastContactDt }}
										{{ contactDetail.lastContactTime }}
									</div>
									<ul class="profile-info-list">
										<li>
											<span class="info-list"><i class="bi bi-clipboard-data" /></span>
											{{ $t('wob.ipsAttribute') }}：<br>{{ contactDetail.ipsName }}({{ contactDetail.ipsCode }})
										</li>
										<li>
											<span class="info-list"><i class="bi bi-gift" /></span>
											{{ $t('wob.birthday') }}：{{ $filters.formatDate(contactDetail.birth) }}
										</li>
										<li>
											<span class="info-list"><i class="bi bi-envelope" /></span>
											{{ $t('wob.email') }}：{{ contactDetail.email }}
										</li>
										<li>
											<span class="info-list"><i class="bi bi-house-door" /></span>
											{{ $t('wob.contactPhoneHome') }}：{{ contactDetail.homePhone }}
										</li>
										<li>
											<span class="info-list"><i class="bi bi-building" /></span>
											{{ $t('wob.contactPhoneOffice') }}：{{ contactDetail.companyPhone }}
										</li>
										<li>
											<span class="info-list"><i class="bi bi-telephone" /></span>
											{{ $t('wob.contactPhoneMobile') }}：{{ contactDetail.mobilePhone }}
										</li>
									</ul>
								</div>
							</div>
						</div>
						<div class="card card-form">
							<table class="table table-bordered table-RWD table-horizontal-RWD">
								<tr>
									<th class="w20">
										{{ $t('wob.contactMethod') }}
									</th>
									<td class="w80">
										{{ contactDetail.contactType }}
									</td>
								</tr>
								<tr>
									<th>{{ $t('wob.contactSubject') }}</th>
									<td>{{ contactDetail.contactPurpose }}</td>
								</tr>
								<tr>
									<th>{{ $t('wob.contactTime') }}</th>
									<td>{{ contactDetail.contactDt }} {{ contactDetail.contactTime }}</td>
								</tr>
								<tr>
									<th>{{ $t('wob.contactContent') }}</th>
									<td>{{ contactDetail.content }}</td>
								</tr>
								<tr>
									<th>{{ $t('wob.initiator') }}</th>
									<td>{{ contactDetail.userCode }} {{ contactDetail.userName }}</td>
								</tr>
							</table>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-white" data-bs-dismiss="modal">
							{{ $t('wob.close') }}
						</button>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import pagination from '@/views/components/pagination.vue';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		'vue-pagination': pagination,
		'vue-modal': vueModal
	},
	data: function () {
		return {
			isAuth: false,
			authRoles: [],

			// API 用參數
			startDt: null, // 聯繫日期-起
			endDt: null, // 聯繫日期-訖
			minorAreaBranCode: 'ALL', // 區
			branCode: 'ALL', // 分行
			rmUserCode: 'ALL', // AO
			cusCode: '', // 客戶 ID
			visitEventCode: '', // 類別
			visitPurCode: '', // 訪談目的

			// 下拉選單
			areaMenu: [], // 組織-區選單
			branMenu: [], // 組織-分行選單
			userMenu: [], // 組織-AO選單

			visitEventMenu: [], // 類別選單
			visitPurMenu: [], // 訪談目的選單
			groupCodeMenu: [], // 重點客戶群組選單

			//
			idn: null,
			identityCode: '',

			// 編輯重點顧客
			groupCode: '',
			selectedRecs: [],

			// 下拉選單
			selectCa: [],
			selectIdentity: [],
			selectVisitType: [],
			selectMeetingType: [],

			// 查詢結果
			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 10,
				sort: ['VISIT_DT', 'NEXTREMIND_TIME'],
				direction: 'DESC'
			},
			checkAll: false,
			contactDetail: {},

			// 畫面控制參數
			isOpenModal: null,
			isShowSummary: false
		};
	},
	computed: {
		isShowPageTitle: function () {
			return !this.isShowSummary;
		}
	},
	watch: {
		minorAreaBranCode: function (newVal) {
			const self = this;
			self.branMenu = [];
			self.userMenu = [];
			self.branCode = 'ALL';
			self.rmUserCode = 'ALL';
			if (newVal === 'ALL') {
				return;
			}
			if (newVal) {
				self.getBranMenu();
			}
		},
		branCode: function (newVal) {
			const self = this;
			self.userMenu = [];
			self.rmUserCode = 'ALL';
			if (newVal === 'ALL') {
				return;
			}
			if (newVal) {
				self.getUserMenu();
			}
		}
	},
	mounted: function () {
		const self = this;
		self.getAreaMenu();
		self.getVisitEventList();
		self.getVisitPurMenu();
		self.getGroupCodeMenu();

		self.startDt = moment().subtract(1, 'day').format('YYYY-MM-DD');
		self.endDt = moment().format('YYYY-MM-DD');
	},
	methods: {
		// 取得組織(區) 選單
		getAreaMenu: function () {
			const self = this;
			self.$api.getMinorAreaApi({}).then(function (ret) {
				self.areaMenu = ret.data;
				if (self.areaMenu.length == 1) {
					self.minorAreaBranCode = self.areaMenu[0].branCode;
				}
			});
		},
		// 取得組織(分行)選單
		getBranMenu: function () {
			const self = this;
			self.$api
				.getBranchesApi({
					minorCode: self.minorAreaBranCode
				})
				.then(function (ret) {
					self.branMenu = ret.data;
					if (self.branMenu.length == 1) {
						self.branCode = self.branMenu[0].branCode;
					}
				});
		},
		// 取得組織(AO) 選單
		getUserMenu: function () {
			const self = this;
			self.$api
				.getBranEmployeeApi({
					branCode: self.branCode
				})
				.then(function (ret) {
					self.userMenu = ret.data;
					if (self.userMenu.length == 1) {
						self.rmUserCode = self.userMenu[0].userCode;
					}
				});
		},
		// 取得 類別 選單
		getVisitEventList: function () {
			const self = this;
			self.$api.getVisitEventMenuApi({}).then(function (ret) {
				self.visitEventMenu = ret.data;
			});
		},
		// 取得 訪談目的
		getVisitPurMenu: function () {
			const self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'VISIT_PUR_CODE'
				})
				.then(function (ret) {
					self.visitPurMenu = ret.data;
				});
		},
		// 取得重點客戶群組可設定的角色代碼
		getEditableRole: function () {
			const self = this;
			self.$api.getCusSaveResultCountApi({
				paramType: 'WOB',
				paramCode: 'CUSGROUPS_ROLE_SETUP'
			});
		},
		// getAdmCodeDetail: function (codeType, retrieveData) {
		// 	var self = this;
		// 	var codeDetailUrl = self.config.apiPath + '/adm/admCodeDetail';
		// 	$.bi.ajax({
		// 		url: codeDetailUrl,
		// 		method: 'GET',
		// 		data: { codeType: codeType },
		// 		retrieve: retrieveData
		// 	});
		// },
		getGroupCodeMenu: function () {
			const self = this;
			self.$api.getCusGroupListApi().then(function (ret) {
				self.groupCodeMenu = ret.data;
			});
		},
		getCusIdentityInfo: function (identityCode) {
			const self = this;

			const identity = self.selectIdentity.find(function (item) {
				return item.codeValue === identityCode;
			});
			if (identity) {
				let className = '';
				switch (identity.codeValue) {
					case 'P':
						className = 'badge badge-light';
						break;
					case 'E':
						className = 'badge badge-warning';
						break;
					default:
						className = '';
						break;
				}
				return {
					className: className,
					codeName: identity.codeName
				};
			}
			else {
				return {
					className: '',
					codeName: ''
				};
			}
		},
		sort: function (col) {
			this.pageable.sort = col;
			if (this.pageable.direction === 'DESC') {
				this.pageable.direction = 'ASC';
			}
			else {
				this.pageable.direction = 'DESC';
			}
			this.gotoPage(0);
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function () {
			const self = this;
			const queryForm = self.$refs.queryForm;
			queryForm.validate().then(function (pass) {
				if (pass.valid) {
					if (self.startDt > self.endDt) {
						queryForm.setFieldError('startDt', self.$t('wob.dateRangeError'));
						return;
					}

					if (moment(self.endDt).diff(moment(self.startDt), 'years', true) > 1) {
						queryForm.setFieldError('startDt', self.$t('wob.dateRangeErrorOneYear'));
						return;
					}

					const url = _.toPageUrl('', self.pageable.page, self.pageable);
					self.$api
						.getVisitLogsApi(
							{
								cusCode: self.cusCode,
								strDate: self.startDt,
								endDate: self.endDt,
								branCode: self.branCode === 'ALL' ? self.branMenu.map(b => b.branCode) : self.branCode,
								userCode: self.rmUserCode === 'ALL' ? '' : self.rmUserCode,
								eventType: self.visitEventCode,
								visitPurCode: self.visitPurCode
							},
							url
						)
						.then(function (ret) {
							self.pageData = ret.data;
						});
				}
			});
		},
		toggleCheckAll: function () {
			this.selectedRecs = [];
			if (this.checkAll) {
				this.selectedRecs = this.pageData.content;
			}
		},
		// viewContactDetail: function (recCode) {
		// 	$.bi
		// 		.ajax({
		// 			url: this.config.apiPath + '/wob/wobContactDetail',
		// 			method: 'GET',
		// 			data: {
		// 				recCode: recCode
		// 			}
		// 		})
		// 		.then((ret) => {
		// 			this.contactDetail = ret.data;
		// 			this.isOpenModal = true;
		// 		});
		// },
		updateCusGroup: function () {
			const self = this;
			self.$refs.cusSearch.validate().then(function (pass) {
				if (pass.valid) {
					self.$api
						.getCusGroupDetailApi({
							groupCode: self.groupCode
						})
						.then(function (ret) {
							const existCusCodes = ret.data.groupDetail.map(d => d.cusCode);
							const addCusCodes = self.selectedRecs.map(r => r.cusCode);
							if (_.intersection(addCusCodes, existCusCodes).length > 0) {
								self.$bi.alert(self.$t('wob.customerExistsInGroup'));
								return;
							}

							self.$api
								.postPointCustomerApi({
									groupCode: self.groupCode,
									cusCodes: addCusCodes.join()
								})
								.then(function (ret) {
									self.$bi.saveSuccessMessage();
								});
						});
				}
			});
		},
		setIsShowSummary: function (val) {
			const self = this;
			self.isShowSummary = val;
		},
		doViewSummary: function (cusCode) {
			const self = this;
			self.isShowSummary = true;
			self.connectCusCode = cusCode;
			this.$refs.cusSummary.setCusCode(cusCode);
		}
	}
};
</script>
