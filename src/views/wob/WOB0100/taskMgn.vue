<template>
	<!--頁面內容 start-->
	<div v-show="!isShowSummary" class="tab-nav-main">
		<Tabs v-model="tabCode" :tabs="tabs" variant="pill" />
		<div class="tab-content">
			<div id="WorkList" role="tabpanel" class="tab-pane fade show active">
				<div class="d-flex align-items-center justify-content-end">
					<h6 class="label mb-0 me-2">
						{{ $t('wob.keyCustomerGroup') }}
					</h6>
					<div class="btn-group-check">
						<input
							v-model="groupCode"
							type="radio"
							class="btn-check"
							name="groupCode"
							value="null"
						>
						<label class="btn btn-outline-primary" @click="checkGroupCode(null)">{{ $t('wob.all') }}</label>
						<template v-for="cusGroup in cusGroupMenu">
							<input
								v-model="groupCode"
								type="radio"
								class="btn-check"
								name="groupCode"
								:value="cusGroup.groupCode"
							>
							<label class="btn btn-outline-primary" @click="checkGroupCode(cusGroup.groupCode)">
								{{ cusGroup.groupName }}
							</label>
						</template>
					</div>
				</div>
			</div>

			<vue-wob-task-mgn-main
				v-if="tabCode === '1'"
				:group-code="groupCode"
				:set-td-cat-name="setTdCatName"
				:do-view-summary="doViewSummary"
				:redirect-params="redirectParams"
				@redirect-fin="redirectParams = {}"
			/>
			<vue-wob-task-mgn-done
				v-if="tabCode === '2'"
				:group-code="groupCode"
				:set-td-cat-name="setTdCatName"
				:do-view-summary="doViewSummary"
			/>
		</div>
	</div>
	<vue-cus-summary ref="cusSummary" :set-is-show-summary="setIsShowSummary" />
</template>
<script>
import vueCusSummary from '@/views/cus/include/cusSummary.vue';
import vueWobTaskMgnMain from '@/views/wob/WOB0100/include/taskMgnMain.vue';
import vueWobTaskMgnDone from '@/views/wob/WOB0100/include/taskMgnDone.vue';

export default {
	components: {
		vueCusSummary,
		vueWobTaskMgnMain,
		vueWobTaskMgnDone
	},
	data: function () {
		return {
			// 畫面顯示用參數
			tabCode: '1',
			groupCode: null,
			cusGroupMenu: null,
			tdCat1Name: null,
			// 畫面控制參數
			isShowSummary: false,

			// 任務項目參數
			tdCat1Code: null,
			itemCode: null,
			toDoItemType: null,
			toDoItemTypeName: null,

			// 傳導參數（例如用於頁面跳轉等）
			redirectParams: {}
		};
	},
	computed: {
		isShowPageTitle: function () {
			return this.isShowSummary ? false : true;
		},
		tabs() {
			return [
				{ code: '1', name: this.$t('wob.pendingTasks') },
				{ code: '2', name: this.$t('wob.completedTasks') }
			];
		}
	},
	mounted: function () {
		const self = this;
		// 取得URL參數
		const query = this.$route.query;
		this.tdCat1Code = query.tdCat1Code ?? null;
		this.itemCode = query.itemCode ?? null;
		this.toDoItemType = query.toDoItemType ?? null;
		this.toDoItemTypeName = query.toDoItemTypeName ?? null;

		self.getCusGroupMenu();

		// 記錄導頁參數
		this.redirectParams = {
			tdCat1Code: this.tdCat1Code,
			itemCode: this.itemCode,
			toDoItemType: this.toDoItemType,
			toDoItemTypeName: this.toDoItemTypeName
		};

		// 清除URL參數
		this.$router.replace({ ...this.$route, query: {} });
	},
	methods: {
		getCusGroupMenu: function () {
			const self = this;
			self.$api.getCusGroupListApi().then(function (ret) {
				self.cusGroupMenu = ret.data;
			});
		},
		checkGroupCode: function (groupCode) {
			const self = this;
			self.groupCode = groupCode;
		},
		setTdCatName: function (tdCat1Name) {
			const self = this;
			self.tdCat1Name = tdCat1Name;
		},
		setIsShowSummary: function (val) {
			const self = this;
			self.isShowSummary = val;
		},
		doViewSummary: function (cusCode) {
			const self = this;
			self.isShowSummary = true;
			self.connectCusCode = cusCode;
			this.$refs.cusSummary.setCusCode(cusCode);
		}
	}
};
</script>
