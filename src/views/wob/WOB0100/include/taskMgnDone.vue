<template>
	<div>
		<div class="tab-nav-tabs mt-3">
			<ul class="nav nav-tabs nav-justified">
				<li v-for="(menu, index) in tdItemCat1Menu" class="nav-item">
					<a
						class="nav-link"
						:class="{ active: tabCode == index }"
						href="#"
						@click.prevent="changeTabCode(menu.tdCat1Code, index)"
					>
						{{ menu.tdCat1Name }}({{ menu.count }})
					</a>
				</li>
			</ul>
			<div class="tab-content">
				<div class="tab-pane fade show active">
					<vue-form v-slot="{ errors }" ref="taskMgnDone">
						<div class="card card-form-collapse mb-3">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formSearch">
								<h4>{{ $t('wob.searchCond') }}</h4>
							</div>
							<div id="formSearch" class="card-body collapse show">
								<div class="row g-3 align-items-center">
									<label class="col-md-auto input-group input-date">{{ $t('wob.eventGenerateDate') }}</label>
									<div class="col-lg-6">
										<div class="input-group">
											<vue-field
												id="beginDate"
												v-model="startDate"
												type="date"
												name="startDate"
												value=""
												class="form-control"
												:label="$t('wob.eventGenerateDateFrom')"
												:class="{ 'is-invalid': errors.startDate }"
												rules="required"
											/>
											<span class="input-group-text">~</span>
											<input
												id="endDate"
												v-model="endDate"
												type="date"
												name="endDate"
												value=""
												class="JQ-datepicker form-control"
												:label="$t('wob.eventGenerateDateTo')"
											>
											<span v-show="errors.startDate" class="text-danger">{{ errors.startDate }}</span>
										</div>
									</div>
									<div class="col-lg-6 text-end">
										<button class="btn btn-primary btn-glow btn-search" @click.prevent="gotoPage(0)">
											{{ $t('wob.search') }}
										</button>
									</div>
								</div>
							</div>
						</div>
					</vue-form>

					<div class="card card-table">
						<div class="card-header">
							<h4>{{ $t('wob.completedTasks') }}-{{ tdCatName }}</h4>
							<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
						</div>
						<div class="table-responsive">
							<table class="table table-RWD table-bordered">
								<thead>
									<tr>
										<th width="12%">
											<span>{{ $t('wob.eventGenerateDate') }}</span>
											<!--span class="EN">Date</span-->
										</th>
										<th width="10%">
											<span>{{ $t('wob.customerName') }}</span>
											<!--span class="EN">Customer</span-->
										</th>
										<th width="15%">
											<span>{{ $t('wob.eventCategory') }}</span>
											<!--span class="EN">Tyep</span-->
										</th>
										<th>
											<span>{{ $t('wob.description') }}</span>
											<!--span class="EN">Desc</span-->
										</th>
										<th width="10%">
											<span>{{ $t('wob.eventDueDate') }}</span>
											<!--span class="EN">Due Date</span-->
										</th>
										<th width="10%">
											<span>{{ $t('wob.processingDate') }}</span>
											<!--span class="EN">Status</span-->
										</th>
										<th width="5%">
											<span>{{ $t('wob.execute') }}</span>
											<!--span class="EN">Note</span-->
										</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in pageData.content">
										<td :data-th="$t('wob.eventGenerateDate')">
											{{ item.listsCreateDt }}
										</td>
										<td :data-th="$t('wob.customerName')">
											<a class="link-underline" href="#" @click.prevent="doViewSummary(item.cusCode)">{{ item.cusName }}</a>
										</td>
										<td :data-th="$t('wob.eventCategory')">
											{{ item.itemName }}
										</td>
										<td :data-th="$t('wob.description')">
											{{ item.content }}
										</td>
										<td :data-th="$t('wob.eventDueDate')">
											{{ item.expireDt }}
										</td>
										<td :data-th="$t('wob.processingDate')">
											{{ item.logsCreateDt }}
										</td>
										<td class="text-center" :data-th="$t('wob.execute')">
											<button type="button" class="btn btn-info btn-glow btn-icon" @click="doViewLogHis(item)">
												<i class="bi bi-pen" />
											</button>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- Modal 1 {{ $t('wob.completedTasks') }} start -->
		<vue-modal v-model:is-open="isOpenModal" @close="() => (isOpenModal = false)">
			<template #content="{ close }">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								{{ $t('wob.completedTasks') }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click="close()"
							/>
						</div>
						<div class="modal-body">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>[{{ cusName }}]{{ itemName }}</h4>
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-bordered">
										<thead>
											<tr>
												<th width="13%">
													{{ $t('wob.processingDate') }}
												</th>
												<th width="17%">
													{{ $t('wob.processingStatus') }}
												</th>
												<th width="20%">
													{{ $t('wob.processingStaff') }}
												</th>
												<th width="15%">
													{{ $t('wob.processingMethod') }}
												</th>
												<th width="35%">
													{{ $t('wob.processingContent') }}
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="logHis in tdLogHis">
												<td :data-th="$t('wob.processingDate')">
													{{ logHis.logsCreateDt }}
												</td>
												<td :data-th="$t('wob.processingStatus')">
													{{ logHis.statusName }}
													<span v-if="logHis.verifyStatusName">/{{ logHis.verifyStatusName }}</span>
												</td>
												<td :data-th="$t('wob.processingStaff')">
													{{ logHis.logsCreateBy }}&nbsp;{{ logHis.userName }}
												</td>
												<td :data-th="$t('wob.processingMethod')">
													{{ logHis.actionName }}
												</td>
												<td :data-th="$t('wob.processingContent')">
													<span>{{ logHis.memo }}</span>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>

						<div class="modal-footer">
							<input
								type="button"
								class="btn btn-white"
								:value="$t('wob.close')"
								@click.prevent="close()"
							>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal 1 End -->
	</div>
</template>
<script>
import _ from 'lodash';
import { Field, Form } from 'vee-validate';
import pagination from '@/views/components/pagination.vue';
import vueModal from '@/views/components/model.vue';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		'vue-pagination': pagination,
		vueModal
	},
	props: {
		groupCode: Number,
		setTdCatName: Function,
		doViewSummary: Function
	},
	data: function () {
		return {
			isOpenModal: false,
			tabCode: 0,
			tdCat1Code: null,
			// groupCode: null,
			itemCode: null,
			startDate: null,
			endDate: null,

			tdLogHis: [],
			tdItemCat1Menu: null,
			tdItmeCounts: null,
			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'LISTS_CREATE_DT ',
				direction: 'DESC'
			},
			tdCatName: null,
			cusName: null,
			itemName: null
		};
	},
	watch: {
		tdCat1Code: function (val) {
			const self = this;
			self.gotoPage(0);
			self.tdItemCat1Menu.forEach(function (item) {
				if (item.tdCat1Code == val) {
					self.setTdCatName(item.tdCat1Name);
					self.tdCatName = item.tdCat1Name;
				}
			});
		},
		groupCode: function () {
			const self = this;
			self.gotoPage(0);
		}
	},
	mounted: function () {
		const self = this;
		self.getTdItemCat1Menu();
	},
	methods: {
		getTdItemCat1Menu: function () {
			const self = this;
			self.$api.getTdItemCat1ToDoMenuApi().then(function (ret) {
				self.tdItemCat1Menu = ret.data;
				self.tdCat1Code = self.tdItemCat1Menu[0].tdCat1Code;
				self.getTdItmeCounts();
			});
		},
		getTdItmeCounts: function () {
			const self = this;
			self.$api
				.getFinishedItmeCountApi({
					groupCode: self.groupCode
				})
				.then(function (ret) {
					self.tdItmeCounts = ret.data;
					self.tdItemCat1Menu.forEach(function (menu) {
						self.tdItmeCounts.forEach(function (item) {
							if (menu.tdCat1Code == item.tdCat1Code) {
								menu.count = item.count;
							}
						});

						if (!menu.count) {
							menu.count = 0;
						}
					});
				});
		},
		changeTabCode: function (tdCat1Code, index) {
			const self = this;
			self.tdCat1Code = tdCat1Code;
			self.tabCode = index;
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function (page) {
			const self = this;
			const queryForm = self.$refs.queryForm;

			if (self.startDate > self.endDate) {
				queryForm.setFieldError('startDate', this.$t('wob.dateRangeError'));
				return;
			}

			const url = _.toPageUrl('', page, self.pageable);

			self.$api
				.getFinishedItmesApi(
					{
						tdCat1Code: self.tdCat1Code,
						groupCode: self.groupCode,
						startDate: _.formatDate(self.startDate),
						endDate: _.formatDate(self.endDate)
					},
					url
				)
				.then(function (ret) {
					self.pageData = ret.data;
				});
		},
		doViewLogHis: function (item) {
			const self = this;
			self.isOpenModal = true;
			self.cusName = item.cusName;
			self.itemName = item.itemName;

			self.$api
				.getTdListLogsHisApi({
					tdCode: item.tdCode
				})
				.then(function (ret) {
					self.tdLogHis = ret.data;
				});
		}
	}
};
</script>
