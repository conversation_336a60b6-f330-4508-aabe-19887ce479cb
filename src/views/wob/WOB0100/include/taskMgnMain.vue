<template>
	<div class="tab-nav-tabs mt-3">
		<ul class="nav nav-tabs nav-justified">
			<li v-for="(menu, index) in tdItemCat1Menu" class="nav-item">
				<a
					class="nav-link"
					:class="{ active: tabCode == index }"
					href="#"
					@click.prevent="changeTabCode(menu.tdCat1Code, index)"
				>{{ menu.tdCat1Name }}
					<span v-if="!showNewTdListOnly == true">({{ menu.count }})</span>
				</a>
			</li>
		</ul>
		<div class="tab-content">
			<div id="CusWarn" class="tab-pane fade show active">
				<div class="table-responsive">
					<table v-if="pageData.content.length == 0" class="table table-RWD text-center table-bordered mb-0 table-todo">
						<thead>
							<tr>
								<th rowspan="2" data-group="group1" class="text-start">
									{{ $t('wob.eventCategory') }}
								</th>
								<th data-group="group3" colspan="3">
									{{ $t('wob.eventCount') }}
								</th>
							</tr>
							<tr>
								<th data-group="group3" width="15%">
									{{ $t('wob.newGenerated') }}
								</th>
								<th v-if="!showNewTdListOnly == true" data-group="group3" width="15%">
									{{ $t('wob.notExpiredExcludeNew') }}
								</th>
								<th v-if="!showNewTdListOnly == true" data-group="group3" width="15%">
									{{ $t('wob.expired') }}
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="item in tdItmeStats">
								<td class="text-start" :data-th="$t('wob.eventCategory')">
									{{ item.itemName }}
								</td>
								<td :data-th="$t('wob.newGenerated')">
									<a class="tx-link" href="#" @click.prevent="getTdList('TO_DAY_TD', $t('wob.newGenerated'), item.itemName, item.itemCode)">
										{{ item.todayTdCount }}
									</a>
								</td>
								<td v-if="!showNewTdListOnly == true" :data-th="$t('wob.notExpired')">
									<a class="tx-link" href="#" @click.prevent="getTdList('NOT_TO_DAY_TD', $t('wob.notExpired'), item.itemName, item.itemCode)">
										{{ item.notTodayTdCount }}
									</a>
								</td>
								<td v-if="!showNewTdListOnly == true" :data-th="$t('wob.expired')">
									<a class="tx-link" href="#" @click.prevent="getTdList('EXPIRE_TD', $t('wob.expired'), item.itemName, item.itemCode)">
										{{ item.expireTdCount }}
									</a>
								</td>
							</tr>
						</tbody>
					</table>
				</div>

				<div v-if="pageData.content.length != 0" id="CusWarn" class="tab-pane fade show active">
					<div class="card card-table">
						<div class="card-header">
							<h4>{{ itemName }}-{{ toDoItemTypeName }}</h4>
							<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
						</div>
						<div class="table-responsive">
							<table class="table table-RWD text-center table-bordered">
								<thead>
									<tr>
										<th width="3%" class="text-center">
											<Checkbox
												:model-value="selectedCusCodes.length === pageData.content.length"
												@update:model-value="selectAllCus"
											/>
										</th>
										<th>{{ $t('wob.eventGenerateDate') }}</th>
										<th width="10%">
											{{ $t('wob.customerName') }}
										</th>
										<th width="8%">
											{{ $t('wob.eventCategory') }}
										</th>
										<th width="12%">
											{{ $t('wob.description') }}
										</th>
										<th>{{ $t('wob.eventDueDate') }}</th>
										<th>{{ $t('wob.recentProcessor') }}</th>
										<th>{{ $t('wob.processingStatus') }}</th>
										<th>{{ $t('wob.processingMethod') }}</th>
										<!--										<th width="5%"><input class="form-check-input" id="selectAllTdCode" type="checkbox" @click="selectAllTdCode()"><label class="form-check-label" for="chkAllForClose">結案</label></th>-->
										<th width="5%">
											<Checkbox
												class="d-flex justify-content-center align-items-center"
												:model-value="doneTdCodes.length === pageData.content.length"
												:label="$t('wob.closeCase')"
												@update:model-value="selectAllTdCode"
											/>
										</th>
										<th width="5%">
											{{ $t('wob.execute') }}
										</th>
									</tr>
								</thead>

								<tbody>
									<tr v-for="tdList in pageData.content">
										<td :data-th="$t('wob.selectCustomer')">
											<Checkbox v-model="selectedCusCodes" class="d-flex justify-content-center" :value="tdList.cusCode" />
										</td>
										<td :data-th="$t('wob.eventGenerateDate')">
											{{ tdList.listsCreateDt }}
										</td>
										<td :data-th="$t('wob.customerName')">
											<a class="link-underline" href="#" @click.prevent="doViewSummary(tdList.cusCode)">{{ tdList.cusName }}</a>
										</td>
										<td :data-th="$t('wob.eventCategory')" class="text-start">
											{{ tdList.itemName }}
										</td>
										<td :data-th="$t('wob.description')" class="text-start">
											{{ tdList.content }}
										</td>
										<td :data-th="$t('wob.eventDueDate')">
											{{ tdList.expireDt }}
										</td>
										<td :data-th="$t('wob.recentProcessor')">
											{{ tdList.modifyName }}
										</td>
										<td :data-th="$t('wob.processingStatus')">
											{{ tdList.statusName ?? $t('cus.unprocessed') }}
											<span v-if="tdList.verifyStatusName">
												<img v-if="tdList.actionName == $t('wob.awaitingReview')" src="@/assets/images/icon/i-wait.png">
												{{ tdList.verifyStatusName }}
											</span>
										</td>
										<td :data-th="$t('wob.processingMethod')">
											{{ tdList.actionName }}
										</td>
										<td :data-th="$t('wob.closeCase')">
											<Checkbox
												v-model="doneTdCodes"
												class="d-flex justify-content-center"
												:disabled="tdList.traceYn == 'Y'"
												:value="tdList.tdCode"
											/>
										</td>
										<td :data-th="$t('wob.execute')">
											<Button
												v-if="userInfo?.roleType === 'RM'"
												color="info"
												icon
												@click="doUpdateTdItem(tdList)"
											>
												<i class="bi bi-pen" />
											</Button>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div class="col text-end my-2">
							<Button
								:disabled="doneTdCodes.length === 0"
								:label="$t('wob.closeCase')"
								color="info"
								@click="updateAndDoneTdLists"
							/>
						</div>
					</div>
					<div class="card card-form mt-2">
						<vue-form ref="groupCode">
							<table class="biv-table table table-RWD table-horizontal-RWD">
								<tbody>
									<tr>
										<th calss="wd-15p">
											<span>{{ $t('wob.joinKeyCustomerGroup') }}</span>
										</th>
										<td>
											<FormField
												v-slot="{field,meta, invalid}"
												v-model="newGroupCode"
												hide-label
												name="groupCode"
												rules="required"
												:label="$t('wob.customerGroup')"
											>
												<div class="input-group">
													<Select v-bind="field" :invalid :options="groupCodeOptions" />
													<Button
														color="info"
														:label="$t('wob.save')"
														:disabled="!meta.valid || selectedCusCodes.length === 0"
														@click="updateCusGroup()"
													/>
													<Button
														color="white"
														link
														:label="$t('wob.editKeyCustomers')"
														@click="$router.push('/cus/favCusSetup')"
													/>
												</div>
											</FormField>
										</td>
									</tr>
								</tbody>
							</table>
						</vue-form>
					</div>
				</div>
			</div>
		</div>
		<vue-modal :is-open="isOpenModal" @close="closeModal">
			<template #content="props">
				<vue-cus-client-service-td-item-modal
					id="itemModal"
					ref="itemModal"
					:close="props.close"
					:item-code="itemCode"
					:td-code="itemTdCode"
					:cus-code="itemCusCode"
					:logs-verify-status-code="logsVerifyStatusCode"
					:goto-page="gotoPage"
					:is-wkf="true"
					:refresh-ts="tdItemRefreshTs"
				/>
			</template>
		</vue-modal>
	</div>
</template>
<script>
import _ from 'lodash';
import { Field, Form } from 'vee-validate';
import pagination from '@/views/components/pagination.vue';
import vueModal from '@/views/components/model.vue';
import vueCusClientServiceTdItemModal from '@/views/cus/include/tdItemModal.vue';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		'vue-pagination': pagination,
		vueModal,
		vueCusClientServiceTdItemModal
	},
	props: {
		groupCode: Number,
		setTdCatName: Function,
		showNewTdListOnly: Boolean,
		doViewSummary: Function,
		redirectParams: {
			type: Object,
			default: () => ({})
		}
	},
	data: function () {
		return {
			isOpenModal: false,
			tabCode: 0,
			tdCat1Name: null,
			tdCat1Code: null,
			// groupCode: null,
			toDoItemType: null,
			toDoItemTypeName: null,
			itemCode: null,
			itemName: null,
			tdItemTypeName: null,
			tdItemRefreshTs: _.now(), // 重新更新 tdItemModal 控制參數

			doneTdCodes: [],
			// 編輯重點顧客
			newGroupCode: '',
			selectedCusCodes: [],

			// 應辦事項處理
			itemTdCode: null,
			itemCusCode: null,
			cusCode: null,
			logsVerifyStatusCode: null,
			// 下拉選單
			groupCodeMenu: [],
			tdItemCat1Menu: null,

			tdItmeCounts: null,
			tdItmeStats: null,
			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'CREATE_DT ',
				direction: 'DESC'
			}
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo']),
		isRedirect: function () {
			return !_.isNil(this.redirectParams) && !_.isEmpty(this.redirectParams) && !_.every(_.values(this.redirectParams), _.isNull);
		},
		groupCodeOptions() {
			return [
				{ value: '', label: this.$t('adm.pleaseSelect') },
				...this.groupCodeMenu.map(({ groupCode, groupName }) => ({ value: groupCode, label: `${groupCode}${groupName}` }))
			];
		}
	},
	watch: {
		tdCat1Code: function (val) {
			const self = this;
			self.getTdItmeStat();
			self.toDoItemType = null;
			self.toDoItemTypeName = null;
			self.itemCode = null;
			self.itemName = null;
			self.pageData.content = [];

			self.tdItemCat1Menu.forEach(function (item) {
				if (item.tdCat1Code == val) {
					self.setTdCatName(item.tdCat1Name);
					self.tdCat1Name = item.tdCat1Name;
				}
			});
		},
		groupCode: function () {
			const self = this;
			self.toDoItemType = null;
			self.toDoItemTypeName = null;
			self.itemCode = null;
			self.itemName = null;
			self.pageData.content = [];
			self.getTdItmeCounts();
			self.getTdItmeStat();
		}
	},
	mounted: async function () {
		const self = this;
		await Promise.all([self.getGroupCodeMenu(), self.getTdItemCat1Menu()]);
		if (!self.isRedirect) return;

		const foundTabIdx = self.tdItemCat1Menu.findIndex(item => item.tdCat1Code === self.redirectParams.tdCat1Code);

		if (foundTabIdx === -1) return;
		const foundTab = self.tdItemCat1Menu[foundTabIdx];
		self.changeTabCode(foundTab.tdCat1Code, foundTabIdx);
	},

	methods: {
		getGroupCodeMenu: function () {
			const self = this;
			self.$api.getCusGroupListApi().then(function (ret) {
				self.groupCodeMenu = ret.data;
			});
		},
		getTdItemCat1Menu: function () {
			const self = this;
			self.$api.getTdItemCat1ToDoMenuApi().then(function (ret) {
				self.tdItemCat1Menu = ret.data;
				if (self.isRedirect && self.tdItemCat1Menu.find(item => item.tdCat1Code === self.redirectParams.tdCat1Code)) {
					self.tdCat1Code = self.redirectParams.tdCat1Code;
				}
				else {
					self.tdCat1Code = self.tdItemCat1Menu[0].tdCat1Code;
				}
				self.getTdItmeCounts();
			});
		},
		getTdItmeCounts: function () {
			const self = this;
			self.$api
				.getTdItmeCountApi({
					groupCode: self.groupCode
				})
				.then(function (ret) {
					self.tdItmeCounts = ret.data;
					self.tdItemCat1Menu.forEach(function (menu) {
						menu.count = 0;
						self.tdItmeCounts.forEach(function (item) {
							if (menu.tdCat1Code == item.tdCat1Code) {
								menu.count = item.count;
							}
						});
					});
				});
		},
		changeTabCode: function (tdCat1Code, index) {
			const self = this;
			self.tdCat1Code = tdCat1Code;
			self.tabCode = index;
		},
		getTdItmeStat: function () {
			const self = this;
			self.$api
				.getTdItmeStatApi({
					tdCat1Code: self.tdCat1Code,
					groupCode: self.groupCode
				})
				.then(function (ret) {
					self.tdItmeStats = ret.data;
					if (self.isRedirect) {
						const foundTdItem = self.tdItmeStats.find(item => item.itemCode === self.redirectParams.itemCode);
						if (foundTdItem) {
							self.getTdList(
								self.redirectParams.toDoItemType,
								self.redirectParams.toDoItemTypeName,
								foundTdItem.itemName,
								foundTdItem.itemCode
							);
						}
						self.$emit('redirectFin');
					}
				});
		},
		getTdList: function (toDoItemType, toDoItemTypeName, itemName, itemCode) {
			const self = this;
			self.toDoItemType = toDoItemType;
			self.toDoItemTypeName = toDoItemTypeName;
			self.itemName = itemName;
			self.itemCode = itemCode;
			self.gotoPage(0);
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function (page) {
			const self = this;
			const url = _.toPageUrl('', page, self.pageable);

			self.$api
				.getTdListApi(
					{
						tdCat1Code: self.tdCat1Code,
						groupCode: self.groupCode,
						itemCode: self.itemCode,
						toDoItemType: self.toDoItemType
					},
					url
				)
				.then(function (ret) {
					self.pageData = ret.data;
				});
		},
		async updateCusGroup() {
			const pass = await this.$refs.groupCode.validate();
			if (!pass.valid) return;

			if (!this.selectedCusCodes?.length) {
				this.$refs.groupCode.setFieldError('selectedCusCodes', this.$t('wob.pleaseSelectCustomer'));
				return;
			}

			await this.$api.postCusGroupDetail({
				groupCode: this.newGroupCode,
				cusCodeList: this.selectedCusCodes
			});

			this.$bi.alert(this.$t('wob.updateSuccess'));
		},
		updateAndDoneTdLists: function () {
			const self = this;
			const queryString = '?toDoItemType=' + self.toDoItemType;

			const tdListUpdateReq = [];
			self.doneTdCodes.forEach(function (tdCode) {
				const tdList = {};
				tdList.tdCode = tdCode;
				tdList.itemCode = self.itemCode;
				tdListUpdateReq.push(tdList);
			});

			self.$api.patchUpdateAndDoneTdListApi(tdListUpdateReq, queryString).then(function (ret) {
				self.$bi.alert(self.$t('wob.updateSuccess'));
				self.gotoPage(0);
			});
		},
		doUpdateTdItem: function (item) {
			const self = this;
			self.itemTdCode = item.tdCode;
			self.itemCusCode = item.cusCode;
			self.itemCode = item.itemCode;
			self.logsVerifyStatusCode = item.logsVerifyStatusCode;
			self.tdItemRefreshTs = _.now();
			self.openModal();
		},
		selectAllCus: function (val) {
			const self = this;
			if (val) {
				self.pageData.content.forEach(function (item) {
					if (!self.selectedCusCodes.includes(item.cusCode)) {
						self.selectedCusCodes.push(item.cusCode);
					}
				});
			}
			else {
				self.pageData.content.forEach(function (target) {
					self.selectedCusCodes.forEach(function (item, index, arr) {
						if (item == target.cusCode) {
							arr.splice(index, 1);
						}
					});
				});
			}
		},
		selectAllTdCode: function (val) {
			const self = this;

			if (val) {
				self.pageData.content.forEach(function (item) {
					if (item.traceYn == 'N') {
						if (!self.doneTdCodes.includes(item.tdCode)) {
							self.doneTdCodes.push(item.tdCode);
						}
					}
				});
			}
			else {
				self.pageData.content.forEach(function (target) {
					self.doneTdCodes.forEach(function (item, index, arr) {
						if (item == target.tdCode) {
							arr.splice(index, 1);
						}
					});
				});
			}
		},
		openModal() {
			this.isOpenModal = true;
		},
		closeModal() {
			this.isOpenModal = false;
		}
	}
};
</script>
