<template>
	<!--頁面內容 start-->
	<div v-if="!isShowSummary" v-bind="$attrs">
		<!--頁面內容 start-->
		<div class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
				<h4>{{ $t('wob.searchCond') }}</h4>
			</div>
			<div id="collapseListGroup1" class="collapse show">
				<div class="card-body">
					<vue-form v-slot="{errors}" ref="queryForm">
						<div class="row g-3 align-items-end">
							<div class="col-lg-6">
								<label class="form-label tx-require">{{ $t('wob.eventGenerateDate') }}</label>
								<div class="input-group">
									<div class="flex-grow-1">
										<!-- TODO: 未翻譯 -->
										<vue-field
											id="startDate"
											v-model="startDate"
											type="date"
											name="startDate"
											class="form-control"
											:class="{ 'is-invalid': errors.startDate }"
											:label="$t('wob.eventGenerateDate') + '(起)'"
											rules="required"
											:min="minDt"
											:max="maxDt"
										/>
										<span v-show="errors.startDate" class="text-danger">{{ errors.startDate }}</span>
									</div>
									<span class="input-group-text">~</span>
									<div class="flex-grow-1">
										<!-- TODO: 未翻譯 -->
										<vue-field
											id="endDate"
											v-model="endDate"
											type="date"
											name="endDate"
											class="JQ-datepicker form-control"
											:class="{ 'is-invalid': errors.endDate }"
											:label="$t('wob.eventGenerateDate') + '(迄)'"
											rules="required"
											:min="minDt"
											:max="maxDt"
										/>
										<span v-show="errors.endDate" class="text-danger">{{ errors.endDate }}</span>
									</div>
								</div>
							</div>
							<div class="col-lg-6">
								<label class="form-label">{{ $t('wob.eventCategory') }}</label>
								<div class="input-group">
									<select
										id="tdcat1Code"
										v-model="tdCat1Code"
										name="tdcat1Code"
										class="form-select"
									>
										<option selected="selected" value="">
											{{ $t('wob.pleaseSelect') }}
										</option>
										<option v-for="tdItemCat in tdItemCat1Menu" :value="tdItemCat.tdCat1Code">
											{{ tdItemCat.tdCat1Name }}
										</option>
									</select>
									<select
										id="wobTdItemsList"
										v-model="itemCode"
										name="itemCode"
										class="form-select"
									>
										<option value="">
											{{ $t('wob.pleaseSelect') }}
										</option>
										<option v-for="tdItem in tdItemsMenu" :value="tdItem.itemCode">
											{{ tdItem.itemName }}
										</option>
									</select>
								</div>
							</div>
							<div class="col-lg-6">
								<label class="form-label">{{ $t('wob.customerIdTaxId') }} </label><br>
								<vue-field
									id="cusIdentity"
									v-model="cusCode"
									name="cusIdentity"
									class="form-control"
									type="text"
									size="15"
									maxlength="20"
									:class="{ 'is-invalid': errors.cusIdentity }"
									:label="$t('wob.customerIdTaxId')"
									rules="min:8|max:20"
								/>
								<div style="height: 3px">
									<span v-show="errors.cusIdentity" class="text-danger">{{ $t('wob.pleaseEnterCompleteCustomerIdTaxId') }}</span>
								</div>
							</div>

							<div class="col-lg-6">
								<label class="form-label tx-require">{{ $t('wob.organization') }}</label>
								<div class="input-group">
									<vue-field
										id="minorAreaBranCode"
										v-model="minorAreaBranCode"
										as="select"
										class="form-select"
										:class="{ 'is-invalid': errors.minorAreaBranCode }"
										name="minorAreaBranCode"
										:label="$t('wob.organizationArea')"
										rules="required"
									>
										<option value="">
											{{ $t('wob.pleaseSelect') }}
										</option>
										<option v-for="minorAreaMenuData in minorAreaMenu" :value="minorAreaMenuData.branCode">
											{{ minorAreaMenuData.branName }}
										</option>
									</vue-field>
									<select
										id="branCode"
										v-model="branCode"
										name="branCode"
										class="form-select"
									>
										<option value="">
											{{ $t('wob.all') }}
										</option>
										<option v-for="branMenuData in branMenu" :value="branMenuData.branCode">
											{{ branMenuData.branCode }} {{ branMenuData.branName }}
										</option>
									</select>
									<select
										id="rmUserCode"
										v-model="rmUserCode"
										name="rmUserCode"
										class="form-select"
									>
										<option value="">
											{{ $t('wob.all') }}
										</option>
										<option value="no">
											{{ $t('wob.noManagement') }}
										</option>
										<option v-for="rmUser in branEmployeeMenu" :value="rmUser.userCode">
											{{ rmUser.userCode }} {{ rmUser.userName }}
										</option>
									</select>
								</div>
								<span v-show="errors.minorAreaBranCode" class="text-danger">{{ errors.minorAreaBranCode }}</span>
							</div>
							<div class="form-footer col-12">
								<div class="tx-note float-start">
									{{ $t('wob.selectAtLeastOne') }}.
								</div>
								<button class="btn btn-primary btn-search" @click.prevent="gotoPage(0)">
									{{ $t('wob.search') }}
								</button>
							</div>
						</div>
					</vue-form>
				</div>
			</div>
		</div>
		<div class="searchResult">
			<div class="card card-table">
				<div class="card-header">
					<h4>{{ $t('wob.searchResults') }}</h4>
					<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-bordered">
						<thead>
							<tr>
								<th>{{ $t('wob.eventGenerateDate') }}</th>
								<th>{{ $t('wob.branch') }}</th>
								<th>{{ $t('wob.managingPersonnel') }}</th>
								<th>{{ $t('wob.customerIdTaxId') }}<br>{{ $t('wob.customerName') }}</th>
								<th width="10%">
									{{ $t('wob.eventCategory') }}
								</th>
								<th width="10%">
									{{ $t('wob.description') }}
								</th>
								<th>{{ $t('wob.eventDueDate') }}</th>
								<th class="text-center">
									{{ $t('wob.closed') }}
								</th>
								<th class="text-center">
									{{ $t('wob.view') }}
								</th>
							</tr>
						</thead>
						<tbody id="TopTenList">
							<tr v-for="item in pageData.content">
								<td :data-th="$t('wob.eventGenerateDate')">
									{{ $filters.formatDateTime(item.listsCreateDt) }}
								</td>
								<td :data-th="$t('wob.branch')">
									{{ item.branCode }} {{ item.branName }}
								</td>
								<td :data-th="$t('wob.managingPersonnel')">
									{{ item.regUserCode }} {{ item.regUserName }}
								</td>
								<td :data-th="$t('wob.customerIdTaxId')	">
									{{ item.cusCode }}<br>
									<ColoredLink @click="doViewSummary(item.cusCode)">
										{{ item.cusName }}
									</ColoredLink>
								</td>
								<td :data-th="$t('wob.eventCategory')">
									{{ item.itemName }}
								</td>
								<td :data-th="$t('wob.description')">
									{{ item.content }}
								</td>
								<td :data-th="$t('wob.eventDueDate')">
									{{ item.expireDt }}
								</td>
								<td class="text-center" :data-th="$t('wob.status')">
									<label v-if="item.doneYn == 'Y'">V</label>
								</td>
								<td class="text-center" :data-th="$t('wob.view')">
									<Button
										color="dark"
										icon
										:title="$t('wob.view')"
										@click="doViewDetail(item)"
									>
										<i class="bi bi-search" />
									</Button>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
	<vue-cus-summary v-else ref="cusSummary" :set-is-show-summary="setIsShowSummary" />
	<!-- Modal 1 start -->
	<vue-modal :is-open="isOpenModal" @close="closeModal">
		<template #content>
			<div class="modal-dialog modal-lg modal-dialog-centered">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('wob.eventDescription') }}
						</h4>
						<button type="button" class="btn-expand">
							<i class="bi bi-arrows-fullscreen" />
						</button>
						<!-- <button type="button" class="btn-close" @click.prevent="props.close()" aria-label="Close"></button> -->
						<!-- 直接綁定點擊事件的觸發到closeModal -->
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click="closeModal"
						/>
					</div>
					<div v-if="tdListDesc" class="modal-body">
						<div class="card card-form mb-3">
							<div class="card-header">
								<h4>{{ $t('wob.customerIdTaxId') }}/{{ $t('wob.name') }}：{{ tdListDesc.cusCode }}/{{ tdListDesc.cusName }}</h4>
							</div>
							<table class="table table-RWD table-horizontal-RWD table-bordered">
								<tbody>
									<tr>
										<th>{{ $t('wob.eventCategory') }}</th>
										<td colspan="3">
											{{ tdListDesc.itemName }}
										</td>
									</tr>
									<tr>
										<th><label>{{ $t('wob.description') }}：</label></th>
										<td colspan="3">
											{{ tdListDesc.content }}
										</td>
									</tr>
									<tr>
										<th class="wd-15p">
											<label>{{ $t('wob.eventGenerateDate') }}：</label>
										</th>
										<td class="wd-35p">
											{{ tdListDesc.listsCreateDt }}
										</td>
										<th class="wd-15p">
											<label>{{ $t('wob.eventDueDate') }}：</label>
										</th>
										<td class="wd-35p">
											2{{ tdListDesc.expireDt }}
										</td>
									</tr>
									<tr>
										<th class="wd-15p">
											<label>{{ $t('wob.submitDate') }}：</label>
										</th>
										<td class="wd-35p">
											{{ tdListDesc.toVerifyDt }}
										</td>
										<th class="wd-15p">
											<label>{{ $t('wob.lastMaintenanceDate') }}：</label>
										</th>
										<td class="wd-35p">
											2{{ tdListDesc.logsCreateDt }}
										</td>
									</tr>
									<tr>
										<th><label>{{ $t('wob.reviewSupervisor') }}：</label></th>
										<td>{{ tdListDesc.verifyUserName }}</td>
										<th><label>{{ $t('wob.closingDate') }}：</label></th>
										<td>{{ tdListDesc.doneDt }}</td>
									</tr>
									<tr>
										<th><label>{{ $t('wob.reviewDate') }}：</label></th>
										<td>{{ tdListDesc.modifyDt }}</td>
										<th><label>{{ $t('wob.reviewStatus') }}：</label></th>
										<td>{{ tdListDesc.verifyStatusName }}</td>
									</tr>
									<tr>
										<th><label>{{ $t('wob.processingPersonnel') }}：</label></th>
										<td>{{ tdListDesc.createName }}</td>
										<th><label>{{ $t('wob.processingMethod') }}：</label></th>
										<td>{{ tdListDesc.actionName }}</td>
									</tr>
									<tr>
										<th><label>{{ $t('wob.processingContent') }}：</label></th>
										<td colspan="3">
											{{ tdListDesc.memo }}
										</td>
									</tr>
									<tr>
										<th><label>{{ $t('wob.processingStatus') }}：</label></th>
										<td colspan="3">
											{{ tdListDesc.statusName }}
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div id="modalFooterId" class="modal-footer">
							<!-- <input class="btn btn-white" id="over1" type="button" value="關閉" @click.prevent="props.close()" /> -->
							<input
								id="over1"
								class="btn btn-white"
								type="button"
								:value="$t('wob.close')"
								@click="closeModal"
							>
						</div>
					</div>
				</div>
			</div>
			<!-- Modal 1 End -->
		</template>
	</vue-modal>
	<!--頁面內容 end-->
</template>

<script>
import _ from 'lodash';
import Swal from 'sweetalert2';
import { Field, Form } from 'vee-validate';
import moment from 'moment';
import vueModal from '@/views/components/model.vue';
import vuePagination from '@/views/components/pagination.vue';
import vueCusSummary from '@/views/cus/include/cusSummary.vue';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		vuePagination,
		vueCusSummary
	},
	data: function () {
		return {
			isOpenModal: null,
			startDate: null,
			endDate: null,
			cusCode: null,
			buCode: '',
			minorAreaBranCode: '',
			branCode: '',
			rmUserCode: '',
			tdCat1Code: '',
			itemCode: '',
			minDt: null,
			maxDt: null,
			// 下拉選單
			minorAreaMenu: [],
			branMenu: [],
			branEmployeeMenu: [],
			tdItemCat1Menu: null,
			tdItemsMenu: null,
			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'LISTS_CREATE_DT ',
				direction: 'DESC'
			},
			tdListDesc: null,
			// 畫面控制參數
			isShowSummary: false
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo']),
		isShowPageTitle: function () {
			return !this.isShowSummary;
		}
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal, oldVal) {
				const self = this;
				if (newVal) {
					self.buCode = newVal.buCode;
				}
			}
		},
		tdCat1Code: function () {
			const self = this;
			self.itemCode = '';
			self.getTdItemsMenu();
		},
		minorAreaBranCode: function (val) {
			const self = this;
			self.branCode = '';
			self.rmUserCode = '';
			self.branMenu = [];
			self.branEmployeeMenu = [];

			if (_.isBlank(val)) {
				return;
			}
			self.getBranMenu();
		},
		branCode: function (val) {
			const self = this;
			self.rmUserCode = '';
			self.branEmployeeMenu = [];
			if (_.isBlank(val)) {
				return;
			}
			self.getBranEmployeeMenu();
		}
	},
	mounted: function () {
		const self = this;
		self.getMinorAreaMenu();
		self.getTdItemCat1Menu();
		self.maxDt = moment().format('YYYY-MM-DD');
		self.minDt = moment().subtract(2, 'years').format('YYYY-MM-DD'); // Two years ago today
	},
	methods: {
		getTdItemCat1Menu: function () {
			const self = this;
			self.$api.getTdItemCat1MenuApi().then(function (ret) {
				self.tdItemCat1Menu = ret.data;
			});
		},
		getTdItemsMenu: function () {
			const self = this;
			self.$api
				.getTdItemsApi({ tdCat1Code: self.tdCat1Code })
				.then(function (ret) {
					self.tdItemsMenu = ret.data || [];
				})
				.catch(function (error) {
					self.tdItemsMenu = [];
				});
		},
		// Get organization(area) menu
		getMinorAreaMenu: function () {
			const self = this;
			self.$api.getMinorAreaApi({ buCode: self.buCode, majorCode: self.majorCode }).then(function (ret) {
				self.minorAreaMenu = ret.data;
				if (self.minorAreaMenu.length == 1) {
					self.minorAreaBranCode = self.minorAreaMenu[0].branCode;
				}
			});
		},
		// Get organization(branch) menu
		getBranMenu: function () {
			const self = this;
			self.$api.getBranchesApi({ buCode: self.buCode, majorCode: self.majorCode, minorCode: self.minorCode }).then(function (ret) {
				self.branMenu = ret.data;
				if (self.branMenu.length === 1) {
					self.branCode = self.branMenu[0].branCode;
				}
			});
		},
		// Get organization(AO) menu
		getBranEmployeeMenu: function () {
			const self = this;
			let branCode = null;
			if (!_.isBlank(self.minorAreaBranCode)) {
				branCode = self.minorAreaBranCode;
			}
			if (!_.isBlank(self.branCode)) {
				branCode = self.branCode;
			}
			self.$api.getBranEmployeeApi({ buCode: self.buCode, branCode: branCode }).then(function (ret) {
				self.branEmployeeMenu = ret.data;
				if (self.branEmployeeMenu.length == 1) {
					self.rmUserCode = self.branEmployeeMenu[0].userCode;
				}
			});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function () {
			const self = this;
			self.$refs.queryForm.validate().then(function (pass) {
				if (moment(self.endDate).diff(moment(self.startDate), 'years', true) > 1) {
					Swal.fire({
						icon: 'error',
						text: this.$t('wob.dateRangeErrorOneYear'),
						showCloseButton: true,
						confirmButtonText: this.$t('wob.confirm'),
						buttonsStyling: false,
						customClass: {
							confirmButton: 'btn btn-danger'
						}
					});
					return;
				}
				const data = {};
				data.startDate = _.formatDate(self.startDate);
				data.endDate = _.formatDate(self.endDate);
				data.tdCat1Code = self.tdCat1Code;
				data.itemCode = self.itemCode;
				data.cusCode = self.cusCode;
				data.pageable = self.pageable;
				if (_.isBlank(self.branCode)) {
					data.allBranCode = self.branMenu.map(item => item.branCode);
				}
				else {
					data.branCode = self.branCode;
				}
				if (_.isBlank(self.rmUserCode)) {
					data.allUserCode = self.branEmployeeMenu.map(item => item.userCode);
				}
				else {
					data.userCode = self.rmUserCode;
				}

				if (pass.valid) {
					const { startDate, endDate, tdCat1Code, itemCode, cusCode, branCode, allBranCode, userCode, allUserCode, pageable } = data;
					self.$api
						.getWobSerachTdListApi({
							startDate,
							endDate,
							tdCat1Code,
							itemCode,
							cusCode,
							branCode,
							allBranCode,
							userCode,
							allUserCode,
							pageable
						})
						.then(function (ret) {
							self.pageData = ret.data;
						});
				}
			});
		},
		doViewDetail: function (item) {
			const self = this;
			self.$api.getWobTdListDescApi({ tdKind: item.tdKind, tdCode: item.tdCode }).then(function (ret) {
				self.tdListDesc = ret.data;
				self.isOpenModal = true;
			});
		},

		setIsShowSummary: function (val) {
			const self = this;
			self.isShowSummary = val;
		},
		doViewSummary: function (cusCode) {
			const self = this;
			self.isShowSummary = true;
			self.connectCusCode = cusCode;
			// $nextTick() ensures that child components are mounted before calling child component methods
			self.$nextTick(() => {
				if (self.$refs.cusSummary && typeof self.$refs.cusSummary.setCusCode === 'function') {
					self.$refs.cusSummary.setCusCode(cusCode);
				}
			});
		},
		closeModal: function () {
			this.isOpenModal = false;
		}
	}
};
</script>
