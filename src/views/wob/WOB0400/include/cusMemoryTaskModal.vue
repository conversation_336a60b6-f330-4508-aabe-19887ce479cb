<template>
	<div>
		<!-- Modal 1 Customer Important Date Maintenance -->
		<vue-modal :is-open="modalStates.modal1" @close="() => closeModal('modal1')">
			<template #content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								{{ $t('wob.customerImportantDate') }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="props.close()"
							/>
						</div>
						<div class="modal-body">
							<div class="card card-form">
								<div class="card-header">
									<h4>{{ $t('wob.customerImportantDate') }}</h4>
									<span class="tx-square-bracket">{{ $t('wob.requiredField') }}</span>
								</div>
							</div>
							<table class="biv-table table table-bordered">
								<tbody>
									<tr>
										<th class="wd-20p">
											<label>{{ $t('wob.importantDate') }}</label>
										</th>
										<td class="wd-80p">
											<label>{{ cusMemoryTask.dateDt }}</label>
										</td>
									</tr>
									<tr>
										<th class="wd-20p">
											<label>{{ $t('wob.description') }}</label>
										</th>
										<td class="wd-80p">
											<label>{{ cusMemoryTask.note }}</label>
										</td>
									</tr>
									<tr>
										<th class="wd-20p">
											<label>{{ $t('wob.expiryNotificationSetting') }}</label>
										</th>
										<td class="wd-80p">
											<label v-if="cusMemoryTask.remindYn === 'Y'">{{ $t('wob.advance') }}{{ cusMemoryTask.remindDays }}{{ $t('wob.day') }}{{ $t('wob.notify') }}</label>
											<label v-if="cusMemoryTask.remindYn === 'N'">{{ $t('wob.noAdvanceNotice') }}</label>
										</td>
									</tr>
								</tbody>
							</table>
						</div>

						<div id="modalFooterId" class="modal-footer">
							<input
								id="cusMemoryModalCloseButton"
								class="btn btn-white"
								type="button"
								:value="$t('wob.close')"
								@click.prevent="props.close()"
							>
							<input
								id="btnDelete"
								class="btn btn-danger"
								type="button"
								:value="$t('wob.delete')"
								@click="deleteMemoryDate(cusMemoryTask.id)"
							>
							<input
								id="btnModify"
								class="btn btn-primary"
								type="button"
								:value="$t('wob.modify')"
								@click.prevent="
									doUpdate();
									openModal('modal2');
								"
							>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal 1 end -->

		<!-- Modal 2 維護顧客重要日子 -->
		<vue-modal :is-open="modalStates.modal2" @close="() => closeModal('modal2')">
			<template #content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								{{ $t('wob.customerImportantDateMaintenance') }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="props.close()"
							/>
						</div>
						<div class="modal-body">
							<div class="card-clientCard">
								<div class="card shadow-none mb-3">
									<table>
										<tbody>
											<tr>
												<td width="10%" class="clientCard-icon">
													<div class="avatar avatar-male">
														<img :src="getImgURL('avatar', 'man-1.png')" class="rounded-circle bg-info">
														<!-- <img th:src="@{/images/avatar/man-3.png}" class="rounded-circle bg-info" v-if="gender =='F'">-->
													</div>
													<h5 class="mb-0">
														{{ cusInfo.cusName || '--' }}
													</h5>
												</td>
												<td width="90%">
													<div class="caption tx-black">
														<span>
															{{ $t('wob.recentContactDate') }}：{{ $filters.formatDate(cusInfo.lastConnectionDt) || '--' }}
														</span>
													</div>
													<div class="row">
														<div class="col-lg-4">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-clipboard-data mg-xl-e-5-f" />{{ $t('wob.investmentAttribute') }}：{{
																		cusInfo.rankName || '--'
																	}}
																</li>
																<li><i class="bi bi-gift mg-xl-e-5-f" />{{ $t('wob.birthday') }}：{{ cusInfo.birth || '--' }}</li>
																<li>
																	<i class="bi bi-envelope mg-xl-e-5-f" />{{ $t('wob.email') }}：{{ cusInfo.email || '--' }}
																</li>
															</ul>
														</div>
														<div class="col-lg-3">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-house-door mg-xl-e-5-f" />{{ $t('wob.contactPhoneHome') }}：{{
																		cusInfo.phoneH || '--'
																	}}
																</li>
																<li>
																	<i class="bi bi-building mg-xl-e-5-f" />{{ $t('wob.contactPhoneOffice') }}：{{
																		cusInfo.phoneO || '--'
																	}}
																</li>
																<li>
																	<i class="bi bi-telephone mg-xl-e-5-f" />{{ $t('wob.contactPhoneMobile') }}：{{
																		cusInfo.phoneM || '--'
																	}}
																</li>
															</ul>
														</div>
														<div class="col-lg-5">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-journal-medical mg-xl-e-5-f" />{{ $t('wob.minorWealthManagementConsent') }}：
																	<span :class="cusInfo.childInvYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.childInvYn === 'Y' ? $t('wob.yes') : $t('wob.no') }}
																	</span>
																</li>
																<li>
																	<i class="bi bi-journal-text mg-xl-e-5-f" />{{ $t('wob.specificTrustInvestmentConsent') }}：
																	<span :class="cusInfo.specRecommYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.specRecommYn === 'Y' ? $t('wob.yes') : $t('wob.no') }}
																	</span>
																</li>
																<li>
																	<i class="bi bi-journal-bookmark mg-xl-e-5-f" />{{ $t('wob.wealthSpecificCustomer') }}：
																	<span :class="cusInfo.specCusYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.specCusYn === 'Y' ? $t('wob.yes') : $t('wob.no') }}
																	</span>
																</li>
															</ul>
														</div>
													</div>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
							<div class="card card-form shadow-none">
								<div class="card-header">
									<h4>{{ $t('wob.customerImportantDate') }}</h4>
									<span class="tx-square-bracket">{{ $t('wob.requiredField') }}</span>
								</div>
								<div class="card-content">
									<vue-form v-slot="{ errors, validate }" ref="memoryDateTask">
										<table class="biv-table table table-RWD table-bordered">
											<tbody>
												<tr>
													<th>
														<label class="form-label tx-require">{{ $t('wob.importantDate') }}</label>
													</th>
													<td>
														<div class="input-group">
															<vue-field
																v-model="memoryDate"
																type="date"
																name="memoryDate"
																class="form-control"
																:class="{ 'is-invalid': errors.memoryDate }"
																:label="$t('wob.workDate')"
																rules="required"
															/>
															<div>
																<span v-show="errors.memoryDate" class="text-danger">{{ errors.memoryDate }}</span>
															</div>
														</div>
													</td>
												</tr>
												<tr>
													<th><label class="form-label tx-require">{{ $t('wob.content') }}</label></th>
													<td colspan="3">
														<vue-field
															id="memContent"
															v-model="memContent"
															as="textarea"
															class="form-control"
															name="memContent"
															rows="5"
															cols="50"
															size="400"
															:class="{ 'is-invalid': errors.memContent }"
															:label="$t('wob.content')"
															rules="required"
														/>
														<div>
															<span v-show="errors.memContent" class="text-danger">{{ errors.memContent }}</span>
														</div>
													</td>
												</tr>
												<tr>
													<th>
														<label class="form-label tx-require">{{ $t('wob.expiryNotificationSetting') }}</label>
													</th>
													<td colspan="3">
														<div class="input-group">
															<div class="form-check form-check-inline">
																<vue-field
																	id="memRemindYn_NN"
																	v-model="memRemindYn"
																	type="radio"
																	name="memRemindYn"
																	class="form-check-input"
																	value="N"
																	:class="{ 'is-invalid': errors.memRemindYn }"
																	rules="required"
																	:label="$t('wob.expiryNotificationSetting')"
																/>
																<label class="form-check-label">{{ $t('wob.noAdvanceNotice') }}</label>
															</div>
															<div class="form-check form-check-inline">
																<vue-field
																	id="memRemindYn_YY"
																	v-model="memRemindYn"
																	type="radio"
																	name="memRemindYn"
																	class="form-check-input"
																	value="Y"
																	:class="{ 'is-invalid': errors.memRemindYn }"
																	rules="required"
																	:label="$t('wob.expiryNotificationSetting')"
																/>
																<label class="form-check-label">{{ $t('wob.advance') }}</label>
															</div>
															<vue-field
																v-model="memRemindDays"
																class="form-control"
																name="memRemindDays"
																type="text"
																size="30"
																:rules="memRemindYn === 'Y' ? 'required' : ''"
																:class="{ 'is-invalid': errors.memRemindDays }"
																:label="$t('wob.advanceNotificationDaysWeeks')"
																rules="required"
															/>
															<div style="height: 3px">
																<span v-show="errors.memRemindDays" class="text-danger">{{
																	errors.memRemindDays
																}}</span>
															</div>
															<vue-field
																id="memRemindPrd"
																v-model="memRemindPrd"
																as="select"
																class="form-select"
																name="memRemindPrd"
																:rules="memRemindYn === 'Y' ? 'required' : ''"
																:class="{ 'is-invalid': errors.memRemindPrd }"
																:label="$t('wob.expiryNotificationSetting')"
															>
																<option value="D">
																	{{ $t('wob.day') }}
																</option>
																<option value="W">
																	{{ $t('wob.week') }}
																</option>
															</vue-field>
															<span class="input-group-text">{{ $t('wob.notify') }}</span>
															<div style="height: 3px">
																<span v-show="errors.memRemindPrd" class="text-danger">{{
																	errors.memRemindPrd
																}}</span>
															</div>
														</div>
													</td>
												</tr>
											</tbody>
										</table>
									</vue-form>
								</div>
							</div>
						</div>
						<div id="modifyTaskFooter" class="modal-footer text-alignRight">
							<input
								id="cusMemoryEditModalCloseButton"
								class="btn btn-white"
								type="button"
								:value="$t('wob.close')"
								@click.prevent="props.close()"
							>
							<input
								id="btnSave"
								class="btn btn-primary"
								type="button"
								:value="$t('wob.save')"
								@click="updateMemoryDates()"
							>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal 2 end -->
	</div>
</template>
<script>
import _ from 'lodash';
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import { getImgURL } from '@/utils/imgURL.js';

export default {
	components: {
		vueModal,
		'vue-form': Form,
		'vue-field': Field
	},
	props: {
		cusMemoryTaskRecCode: String,
		getCalendarTasks: Function
	},

	data: function () {
		return {
			modalStates: {
				modal1: false,
				modal2: false
			},
			// Display parameters
			cusMemoryTask: {},

			// Customer data
			cusCode: null,
			cusInfo: {
				cusName: null,
				birth: null,
				email: null,
				phoneH: '',
				phoneO: '',
				phoneM: '',
				rankName: null,
				childInvYn: null,
				specRecommYn: null,
				specCusYn: null,
				logsCreateDt: null
			},

			// API parameters
			recCode: null,
			memoryDate: null, // Important date
			memContent: null, // Content

			memRemindYn: null, // Expiry notification setting
			memRemindDays: null, // Advance notification days/weeks
			memRemindPrd: null // Advance notification period
		};
	},
	watch: {
		cusMemoryTaskRecCode: function (val) {
			const self = this;
			if (self.cusMemoryTaskRecCode) {
				self.getCusMemoryTask();
			}
		}
	},
	methods: {
		getImgURL,
		getCusMemoryTask: function () {
			const self = this;
			self.$api
				.getMemoryCalendarTaskApi({
					id: self.cusMemoryTaskRecCode
				})
				.then(function (ret) {
					self.cusMemoryTask = ret.data;
					self.cusInfo.cusCode = self.cusMemoryTask.cusCode;
					self.getCusInfo();
				});
		},
		getCusInfo: function () {
			const self = this;
			self.$api
				.getCustomer({
					cusCode: self.cusInfo.cusCode
				})
				.then(function (ret) {
					if (!ret.data) {
						self.$bi.alert(self.$t('wob.noCustomerFound'));
						self.clearValues();
						return;
					}
					self.cusInfo.cusName = ret.data.cusName;
					self.cusInfo.birth = ret.data.birth;
					self.cusInfo.email = ret.data.email;
					self.cusInfo.rankName = ret.data.rankName;
					self.cusInfo.childInvYn = ret.data.childInvYn;
					self.cusInfo.specRecommYn = ret.data.specRecommYn;
					self.cusInfo.specCusYn = ret.data.specCusYn;
					self.cusInfo.lastConnectionDt = ret.data.logCreateDt;

					// Categorize contact methods
					ret.data.contactInfoList.forEach(function (item) {
						switch (item.contactType) {
							case 'E': // email
								self.cusInfo.email = item.email;
								break;
							case 'H': // Home phone
								self.cusInfo.phoneH = item.phone1;
								break;
							case 'O': // Office phone
								self.cusInfo.phoneO = item.phone1;
								break;
							case 'M': // Mobile phone
								self.cusInfo.phoneM = item.phone1;
								break;
						}
					});
				});
		},
		deleteMemoryDate: function (id) {
			const self = this;
			self.$bi.confirm(self.$t('wob.confirmDeleteData'), {
				event: {
					confirmOk: function () {
						self.$api
							.deleteMemoryDateApi({
								id: id
							})
							.then(function (ret) {
								self.$bi.alert(self.$t('wob.deleteSuccess'));
								$('#cusMemoryModalCloseButton').click();
								if (self.getCalendarTasks) {
									self.getCalendarTasks();
								}
							});
					}
				}
			});
		},
		doUpdate: function () {
			const self = this;
			self.id = self.cusMemoryTask.id;
			self.memoryDate = self.cusMemoryTask.dateDt;
			self.memRemindYn = self.cusMemoryTask.remindYn;
			self.memRemindDays = self.cusMemoryTask.remindDays;
			self.memRemindPrd = 'D';
			self.memContent = self.cusMemoryTask.note;
		},
		updateMemoryDates: function () {
			const self = this;
			self.$refs.memoryDateTask.validate().then(function (pass) {
				if (pass.valid) {
					self.$api
						.patchMemoryDateApi({
							id: self.id,
							cusCode: self.cusInfo.cusCode,
							dateDt: self.memoryDate,
							remindYn: self.memRemindYn,
							remindDays: self.memRemindYn === 'Y' ? self.memRemindDays : null,
							remindPrd: self.memRemindYn === 'Y' ? self.memRemindPrd : null,
							note: self.memContent
						})
						.then(function (ret) {
							self.$bi.alert(self.$t('wob.updateSuccess'));
							$('#cusMemoryEditModalCloseButton').click();
							self.getCusMemoryTask();
							if (self.getCalendarTasks) {
								self.getCalendarTasks();
							}
						});
				}
			});
		},
		clearValues: function () {
			const self = this;

			self.cusInfo.cusName = null;
			self.cusInfo.birth = null;
			self.cusInfo.email = null;
			self.cusInfo.phoneH = '';
			self.cusInfo.phoneO = '';
			self.cusInfo.phoneM = '';
			self.cusInfo.rankName = null;
			self.cusInfo.childInvYn = null;
			self.cusInfo.specRecommYn = null;
			self.cusInfo.specCusYn = null;
			self.cusInfo.logsCreateDt = null;
		},
		closeModal: function (modalName) {
			this.modalStates[modalName] = false;
		},
		openModal: function (modalName) {
			this.modalStates[modalName] = true;
		}
	}
};
</script>
