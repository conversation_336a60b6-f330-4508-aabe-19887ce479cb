<template>
	<div>
		<!-- Modal 1 -->
		<vue-modal :is-open="modalStates.modal1" @close="() => closeModal('modal1')">
			<template #content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								{{ $t('wob.personalNotes') }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="props.close()"
							/>
						</div>
						<div class="modal-body">
							<table class="table table-RWD table-bordered table-horizontal-RWD">
								<tbody>
									<tr>
										<th class="wd-20p">
											<label>{{ $t('wob.workDate') }}</label>
										</th>
										<td class="wd-80p">
											<label>{{ tdRecTask.nextRemindDt }}</label>
										</td>
									</tr>
									<tr>
										<th class="wd-20p">
											<label>{{ $t('wob.workTime') }}</label>
										</th>
										<td class="wd-80p">
											<label>{{ tdRecTask.nextRemindTime }}</label>
										</td>
									</tr>
									<tr>
										<th class="wd-20p">
											<label>{{ $t('wob.advanceNotice') }}</label>
										</th>
										<td class="wd-80p">
											<label>{{ tdRecTask.advNceName }}</label>

											<template v-if="tdRecTask.advNce === 'Y'">
												<label> {{ tdRecTask.advNceDay }}</label>
												<label v-if="tdRecTask.advNcePrd === 'W'">{{ $t('wob.week') }}</label>
												<label v-if="tdRecTask.advNcePrd === 'D'">{{ $t('wob.day') }}</label>
											</template>
										</td>
									</tr>
									<tr>
										<th class="wd-20p">
											<label>{{ $t('wob.content') }}</label>
										</th>
										<td class="wd-80p">
											<label>{{ tdRecTask.content }}</label>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
						<div class="modal-footer">
							<input
								id="personalModalCloseButton"
								class="btn btn-white"
								type="button"
								:value="$t('wob.close')"
								@click.prevent="props.close()"
							>
							<input
								id="btnDelete"
								class="btn btn-danger"
								type="button"
								:value="$t('wob.delete')"
								@click="deleteTdRec(tdRecTask.recCode)"
							>
							<input
								id="btnModify"
								class="btn btn-primary"
								type="button"
								:value="$t('wob.modify')"
								@click="doUpdate()"
							>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal 1 end -->

		<!-- Modal 2 Personal Notes Maintenance -->
		<vue-modal :is-open="modalStates.modal2" @close="() => closeModal('modal2')">
			<template #content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								{{ $t('wob.personalNotesMaintenance') }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="closeModal('modal2')"
							/>
						</div>
						<div class="modal-body">
							<div class="card card-form shadow-none">
								<div class="card-header">
									<h4>{{ $t('wob.personalNotesItems') }}</h4>
									<span class="tx-square-bracket">{{ $t('wob.requiredField') }}</span>
								</div>

								<div class="card-content">
									<vue-form v-slot="{ errors, validate }" ref="queryForm">
										<table class="biv-table table table-RWD table-bordered">
											<tbody>
												<tr>
													<th>
														<label class="form-label tx-require">{{ $t('wob.workDate') }}</label>
													</th>
													<td>
														<div class="input-group">
															<vue-field
																v-model="nextRemindDt"
																type="date"
																name="nextRemindDt"
																class="form-control"
																rules="required"
																:class="{ 'is-invalid': errors.nextRemindDt }"
																:label="$t('wob.workDate')"
															/>
															<div>
																<span v-show="errors.nextRemindDt" class="text-danger">{{
																	errors.nextRemindDt
																}}</span>
															</div>
														</div>
													</td>
													<th>
														<label class="form-label tx-require">{{ $t('wob.workTime') }}</label>
													</th>
													<td>
														<div class="input-group">
															<vue-field
																id="psnTaskHour"
																v-model="psnTaskHour"
																as="select"
																class="form-select"
																name="psnTaskHour"
																rules="required"
																:class="{ 'is-invalid': errors.psnTaskHour }"
																:label="$t('wob.workTime')"
															>
																<option value="00">
																	00
																</option>
																<option value="01">
																	01
																</option>
																<option value="02">
																	02
																</option>
																<option value="03">
																	03
																</option>
																<option value="04">
																	04
																</option>
																<option value="05">
																	05
																</option>
																<option value="06">
																	06
																</option>
																<option value="07">
																	07
																</option>
																<option value="08">
																	08
																</option>
																<option value="09">
																	09
																</option>
																<option value="10">
																	10
																</option>
																<option value="11">
																	11
																</option>
																<option value="12">
																	12
																</option>
																<option value="13">
																	13
																</option>
																<option value="14">
																	14
																</option>
																<option value="15">
																	15
																</option>
																<option value="16">
																	16
																</option>
																<option value="17">
																	17
																</option>
																<option value="18">
																	18
																</option>
																<option value="19">
																	19
																</option>
																<option value="20">
																	20
																</option>
																<option value="21">
																	21
																</option>
																<option value="22">
																	22
																</option>
																<option value="23">
																	23
																</option>
															</vue-field>
															<span class="input-group-text">{{ $t('wob.hour') }}</span>
															<div>
																<span v-show="errors.psnTaskHour" class="text-danger">{{ errors.psnTaskHour }}</span>
															</div>
															<vue-field
																id="psnTaskMin"
																v-model="psnTaskMin"
																as="select"
																class="form-select"
																name="psnTaskMin"
																rules="required"
																:class="{ 'is-invalid': errors.psnTaskMin }"
																:label="$t('wob.workTime')"
															>
																<option selected="selected" value="00">
																	00
																</option>
																<option v-for="minute in selectMinutes" :value="minute">
																	{{ minute }}
																</option>
															</vue-field>
															<span class="input-group-text">{{ $t('wob.minute') }}</span>
															<div>
																<span v-show="errors.psnTaskMin" class="text-danger">{{ errors.psnTaskMin }}</span>
															</div>
														</div>
													</td>
												</tr>
												<tr>
													<th>
														<label class="form-label tx-require">{{ $t('wob.expiryNotificationSetting') }}</label>
													</th>
													<td colspan="3">
														<div class="input-group">
															<div class="form-check form-check-inline">
																<vue-field
																	id="apptAdvNce_NN"
																	v-model="advNce"
																	type="radio"
																	name="apptAdvNceYn"
																	class="form-check-input"
																	value="N"
																	:class="{ 'is-invalid': errors.advNce }"
																	rules="required"
																	:label="$t('wob.expiryNotificationSetting')"
																/>
																<label class="form-check-label">{{ $t('wob.noAdvanceNotice') }}</label>
															</div>
															<div class="form-check form-check-inline">
																<vue-field
																	id="apptAdvNce_YY"
																	v-model="advNce"
																	type="radio"
																	name="apptAdvNceYn"
																	class="form-check-input"
																	value="Y"
																	:class="{ 'is-invalid': errors.advNce }"
																	rules="required"
																	:label="$t('wob.expiryNotificationSetting')"
																/>
																<label class="form-check-label">{{ $t('wob.advanceNotice') }}</label>
															</div>

															<vue-field
																v-model="advNceDay"
																class="form-control"
																name="advNceDay"
																type="text"
																size="30"
																value="advNceDay"
																:class="{ 'is-invalid': errors.advNceDay }"
																:label="$t('wob.advanceNotificationDaysWeeks')"
																:rules="advNce === 'Y' ? 'required' : ''"
															/>
															<div style="height: 3px">
																<span v-show="errors.advNceDay" class="text-danger">{{ errors.advNceDay }}</span>
															</div>

															<vue-field
																id="advNcePrd"
																v-model="advNcePrd"
																as="select"
																class="form-select"
																name="advNcePrd"
																:class="{ 'is-invalid': errors.advNcePrd }"
																:rules="advNcePrd === 'Y' ? 'required' : ''"
																:label="$t('wob.advanceNotificationPeriod')"
															>
																<option value="D">
																	{{ $t('wob.day') }}
																</option>
																<option value="W">
																	{{ $t('wob.week') }}
																</option>
															</vue-field>
															<div style="height: 3px">
																<span v-show="errors.advNcePrd" class="text-danger">{{ errors.advNcePrd }}</span>
															</div>

															<span class="input-group-text">{{ $t('wob.notify') }}</span>
														</div>
													</td>
												</tr>
												<tr>
													<th><label class="form-label tx-require">{{ $t('wob.subject') }}</label></th>
													<td colspan="3">
														<div class="input-group">
															<vue-field
																v-model="title"
																class="form-control"
																name="title"
																type="text"
																size="30"
																value="title"
																:class="{ 'is-invalid': errors.title }"
																:label="$t('wob.subject')"
																rules="required"
															/>
															<div style="height: 3px">
																<span v-show="errors.title" class="text-danger">{{ errors.title }}</span>
															</div>
														</div>
													</td>
												</tr>
												<tr>
													<th>
														<label class="form-label tx-require">{{ $t('wob.content') }}</label>
													</th>
													<td colspan="3">
														<vue-field
															id="content"
															v-model="content"
															as="textarea"
															class="form-control"
															name="content"
															rows="5"
															cols="50"
															size="400"
															:class="{ 'is-invalid': errors.content }"
															:label="$t('wob.content')"
															rules="required"
														/>
														<div style="height: 3px">
															<span v-show="errors.content" class="text-danger">{{ errors.content }}</span>
														</div>
													</td>
												</tr>
												<tr>
													<th>
														<span class="form-label">{{ $t('wob.commonPhrases') }}</span>
													</th>
													<td colspan="3">
														<div class="input-group">
															<select
																id="reuseWord"
																v-model="reuseWord"
																name="reuseWord"
																class="form-select"
															>
																<option
																	v-for="selectWords in wobReuseWords"
																	v-show="selectWords.words"
																	:value="selectWords.words"
																>
																	{{ selectWords.words }}
																</option>
															</select>
															<button
																id="setContent"
																type="button"
																class="btn btn-primary"
																@click="appendReuseWord('content')"
															>
																{{ $t('wob.join') }}
															</button>
															<input
																id="words"
																v-model="newReuseWord"
																class="form-control"
																type="text"
																size="20"
																maxlength="20"
															>
															<button
																id="wordAdd"
																type="button"
																class="btn btn-primary"
																@click="insertReuseWord()"
															>
																{{ $t('wob.add') }}
															</button>
															<button
																id="wordSetting"
																type="button"
																class="btn btn-primary"
																@click.prevent="openModal('reuseWordModal')"
															>
																{{ $t('wob.setting') }}
															</button>
														</div>
													</td>
												</tr>
											</tbody>
										</table>
									</vue-form>
								</div>
							</div>
						</div>
						<div id="modifyTaskFooter" class="modal-footer text-alignRight">
							<input
								id="personalTaskModalCloseButton"
								class="btn btn-white"
								type="button"
								:value="$t('wob.close')"
								@click.prevent="closeModal('modal2')"
							>
							<input
								id="btnSave"
								class="btn btn-primary"
								type="button"
								:value="$t('wob.save')"
								@click="updatePersonalTdRec()"
							>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal 2 end -->
		<vue-modal :is-open="modalStates.reuseWordModal" @close="closeModal('reuseWordModal')">
			<template #content="props">
				<vue-cus-reuse-word-modal
					:id="'personalReuseWordModal'"
					:close="props.close"
					:wob-reuse-words="wobReuseWords"
					:super-modal-name="'updatePersonalTaskModal'"
				/>
			</template>
		</vue-modal>
	</div>
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import vueCusReuseWordModal from '@/views/cus/include/reuseWordModal.vue';

export default {
	components: {
		vueModal,
		vueCusReuseWordModal,
		'vue-form': Form,
		'vue-field': Field
	},
	props: {
		recCode: String,
		getCalendarTasks: Function
	},

	data: function () {
		const minutes = [];
		for (let i = 1; i < 60; i++) {
			minutes.push(String(i).padStart(2, '0'));
		}
		return {
			modalStates: {
				modal1: false,
				modal2: false,
				reuseWordModal: false
			},

			// Menu
			selectMinutes: minutes,

			// Display parameters
			tdRecTask: {},

			// Common phrases mechanism
			reuseWord: null,
			newReuseWord: null,
			wobReuseWords: null,
			// API parameters
			// recCode: null,
			nextRemindDt: null, // Work date
			psnTaskHour: '00', // Work time (hour)
			psnTaskMin: '00', // Work time (minute)
			advNce: 'D', // Expiry notification setting
			advNceDay: null, // Expiry notification days
			advNcePrd: null, // Expiry notification unit
			title: null, // Work subject
			content: null, // Work content

			forbiddenContentWords: [
				'KYC',
				'borrow',
				'seal',
				'password',
				'loan',
				'statement',
				'crossBorder',
				'singapore',
				'hongKong',
				'expire',
				'onAccount',
				'guarantee',
				'discontinued',
				'suggest',
				'cancellation',
				'elderly'
			], // Forbidden reserved words
			forbiddenContentMsg: null // Forbidden reserved words error message
		};
	},
	watch: {
		recCode: function (val) {
			const self = this;
			if (self.recCode) {
				self.getPersonalTask();
			}
		},
		content: function (str) {
			const self = this;
			self.checkForbiddenContentWords(str);
		}
	},
	mounted: function () {
		const self = this;
		self.getReuseWords();
		self.setDefaultReuseWords();
	},
	methods: {
		setDefaultReuseWords: function () {
			const self = this;
			self.wobReuseWords = [];
			for (let i = 0; i < 10; i++) {
				const tempWordObj = {
					wordsId: i + 1,
					words: null
				};
				self.wobReuseWords.push(tempWordObj);
			}
		},
		getReuseWords: function () {
			const self = this;
			// 根據router獲取當前模組
			const type = self.$route.path.split('/').filter(Boolean)[0].toUpperCase();
			self.$api.getReuseWordsApi({ type }).then(function (ret) {
				self.wobReuseWords = ret.data || [];
			});
		},
		getPersonalTask: function () {
			const self = this;
			self.$api
				.getTdRecApi({
					recCode: self.recCode
				})
				.then(function (ret) {
					self.tdRecTask = ret.data;
				});
		},
		deleteTdRec: function (recCode) {
			const self = this;
			if (!recCode) {
				self.$bi.alert(this.$t('wob.pleaseSelectEventToDelete'));
				return;
			}

			self.$bi.confirm(this.$t('wob.confirmDeleteData'), {
				event: {
					confirmOk: function () {
						self.$api
							.deleteTdRecApi({
								recCode: recCode
							})
							.then(function (ret) {
								self.$bi.alert(this.$t('wob.deleteSuccess'));
								$('#personalModalCloseButton').click();
								if (self.getCalendarTasks) {
									self.getCalendarTasks();
								}
							});
					}
				}
			});
		},
		doUpdate: function () {
			const self = this;
			self.modalStates.modal2 = true;
			self.nextRemindDt = moment(self.tdRecTask.nextRemindDt).format('YYYY-MM-DD');
			self.advNce = self.tdRecTask.advNce;
			self.advNceDay = self.tdRecTask.advNceDay;
			self.advNcePrd = self.tdRecTask.advNcePrd;
			self.title = self.tdRecTask.title;
			self.content = self.tdRecTask.content;
			const nextRemindTime = self.tdRecTask.nextRemindTime.split(':');
			self.psnTaskHour = nextRemindTime[0];
			self.psnTaskMin = nextRemindTime[1];

			// Common phrases mechanism
			self.getReuseWords();
		},
		updatePersonalTdRec: function () {
			const self = this;
			self.modalStates.modal1 = true;

			self.$refs.queryForm.validate().then(function (pass) {
				if (pass.valid) {
					const nextRemindTime = self.psnTaskHour + ':' + self.psnTaskMin;

					self.$api
						.patchTdPersonalRecApi({
							tdRec: self.recCode,
							nextRemindDt: moment(self.nextRemindDt).format('YYYY-MM-DD'),
							nextRemindTime: nextRemindTime,
							advNce: self.advNce,
							advNceDay: self.advNce === 'Y' ? self.advNceDay : null,
							advNcePrd: self.advNce === 'Y' ? self.advNcePrd : null,
							title: self.title,
							content: self.content
						})
						.then(function (ret) {
							self.$bi.alert(this.$t('wob.updateSuccess'));
							$('#personalTaskModalCloseButton').click();
							self.getCalendarTasks();
							self.getPersonalTask();
						});
				}
			});
		},
		insertReuseWord: function () {
			const self = this;
			if (self.newReuseWord) {
				const type = self.$route.path.split('/').filter(Boolean)[0].toUpperCase();
				self.$api
					.postReuseWordsApi({
						type,
						words: self.newReuseWord
					})
					.then(function (ret) {
						self.newReuseWord = null;
						self.getReuseWords();
					});
			}
			else {
				if (!self.memo) {
					self.memo = '';
				}
				self.memo = self.memo + self.newReuseWord;
				self.$bi.alert(this.$t('wob.commonPhrasesExceedLimit'));
			}
		},
		appendReuseWord: function () {
			const self = this;
			if (!self.content) {
				self.content = '';
			}
			self.content = self.content + self.reuseWord;
		},
		// Forbidden words validation
		checkForbiddenContentWords: function (content) {
			const self = this;
			let forbiddenWord = null;
			if (content) {
				_.forEach(self.forbiddenContentWords, function (word) {
					if (_.includes(content, word) || _.includes(content, word.toLowerCase())) {
						forbiddenWord = word;
						return false;
					}
				});
			}

			if (forbiddenWord) {
				self.forbiddenContentMsg = this.$t('wob.forbiddenContentMessage', { word: forbiddenWord });
			}
			else {
				self.forbiddenContentMsg = null;
			}
		},
		// Close child window
		doClose: function () {
			const self = this;
			self.reuseWord = null;
			self.newReuseWord = null;
		},
		closeModal: function (modalName) {
			this.modalStates[modalName] = false;
		},
		openModal: function (modalName) {
			this.modalStates[modalName] = true;
		}
	}
};
</script>
