<template>
	<div>
		<!-- Modal 1  -->
		<vue-modal :is-open="modalStates.modal1" @close="() => closeModal('modal1')">
			<template #content="props">
				<div class="modal-dialog modal-lg modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								{{ $t('wob.appointment') }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="closeModal('modal1')"
							/>
						</div>
						<div class="modal-body">
							<div class="alert alert-info" role="alert">
								[ {{ tdRecTask.cusName }} ] {{ tdRecTask.nextRemindDt }}, {{ tdRecTask.nextRemindTime }}
							</div>
							<div class="card card-form">
								<div class="card-header">
									<h4>{{ $t('wob.appointmentRecord') }}</h4>
								</div>
								<table class="biv-table table table-bordered table-RWD table-horizontal-RWD">
									<tbody>
										<tr>
											<th class="w20">
												{{ $t('wob.expiryNotificationSetting') }}
											</th>
											<td class="w80">
												{{ tdRecTask.advNceName }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('wob.interviewPurpose') }}</th>
											<td>{{ tdRecTask.visitPurName }}</td>
											<th>{{ $t('wob.interviewMethod') }}</th>
											<td>{{ tdRecTask.visitAprName }}</td>
										</tr>
										<tr>
											<th>{{ $t('wob.appointmentSubject') }}</th>
											<td>{{ tdRecTask.title }}</td>
										</tr>
										<tr>
											<th>{{ $t('wob.appointmentContent') }}</th>
											<td>{{ tdRecTask.content }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="modal-footer">
							<button
								name="btnClose"
								type="button"
								class="btn btn-white"
								aria-label="Close"
								@click.prevent="closeModal('modal1')"
							>
								{{ $t('wob.close') }}
							</button>
							<button
								name="btnDelete"
								type="button"
								class="btn btn-danger"
								@click="deleteTdRec()"
							>
								{{ $t('wob.delete') }}
							</button>
							<button
								name="btnModify"
								type="button"
								class="btn btn-primary"
								@click.prevent="doUpdate()"
							>
								{{ $t('wob.modify') }}
							</button>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal 1 end -->

		<!-- Modal 2 約訪事件維護 -->
		<vue-modal :is-open="modalStates.modal2" @close="() => closeModal('modal2')">
			<template #content="props">
				<div class="modal-dialog modal-lg modal-dialog-scrollable">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								{{ $t('wob.appointment') }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="closeModal('modal2')"
							/>
						</div>
						<div class="modal-body overflow-scroll">
							<div class="card-clientCard">
								<div class="card shadow-none mb-3">
									<table>
										<tbody>
											<tr>
												<td width="10%" class="clientCard-icon">
													<div class="avatar avatar-male">
														<img :src="getImgURL('avatar', 'man-1.png')" class="rounded-circle bg-info">
														<!-- <img th:src="@{/images/avatar/man-3.png}" class="rounded-circle bg-info" v-if="gender =='F'">-->
													</div>
													<h5 class="mb-0">
														{{ cusInfo.cusName || '--' }}
													</h5>
												</td>
												<td width="90%">
													<div class="caption tx-black">
														<span>
															{{ $t('wob.recentContactDate') }}：{{ $filters.formatDate(cusInfo.lastConnectionDt) || '--' }}
														</span>
													</div>
													<div class="row">
														<div class="col-lg-4">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-clipboard-data mg-xl-e-5-f" />{{ $t('wob.investmentAttribute') }}：{{
																		cusInfo.rankName || '--'
																	}}
																</li>
																<li><i class="bi bi-gift mg-xl-e-5-f" />{{ $t('wob.birthday') }}：{{ cusInfo.birth || '--' }}</li>
																<li>
																	<i class="bi bi-envelope mg-xl-e-5-f" />{{ $t('wob.email') }}：{{ cusInfo.email || '--' }}
																</li>
															</ul>
														</div>
														<div class="col-lg-3">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-house-door mg-xl-e-5-f" />{{ $t('wob.contactPhoneHome') }}：{{
																		cusInfo.phoneH || '--'
																	}}
																</li>
																<li>
																	<i class="bi bi-building mg-xl-e-5-f" />{{ $t('wob.contactPhoneOffice') }}：{{
																		cusInfo.phoneO || '--'
																	}}
																</li>
																<li>
																	<i class="bi bi-telephone mg-xl-e-5-f" />{{ $t('wob.contactPhoneMobile') }}：{{
																		cusInfo.phoneM || '--'
																	}}
																</li>
															</ul>
														</div>
														<div class="col-lg-5">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-journal-medical mg-xl-e-5-f" />{{ $t('wob.minorWealthManagementConsent') }}：
																	<span :class="cusInfo.childInvYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.childInvYn === 'Y' ? $t('wob.yes') : $t('wob.no') }}
																	</span>
																</li>
																<li>
																	<i class="bi bi-journal-text mg-xl-e-5-f" />{{ $t('wob.specificTrustInvestmentConsent') }}：
																	<span :class="cusInfo.specRecommYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.specRecommYn === 'Y' ? $t('wob.yes') : $t('wob.no') }}
																	</span>
																</li>
																<li>
																	<i class="bi bi-journal-bookmark mg-xl-e-5-f" />{{ $t('wob.wealthSpecificCustomer') }}：
																	<span :class="cusInfo.specCusYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.specCusYn === 'Y' ? $t('wob.yes') : $t('wob.no') }}
																	</span>
																</li>
															</ul>
														</div>
													</div>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
							<!-- 約訪紀錄 -->
							<div class="card card-form shadow-none">
								<div class="card-header">
									<h4>{{ $t('wob.appointmentSchedule') }}</h4>
									<span class="tx-square-bracket">{{ $t('wob.requiredField') }}</span>
								</div>
								<div class="card-body">
									<vue-form v-slot="{ errors }" ref="appointmentTask">
										<div class="row g-3 align-items-end">
											<div class="col-lg-6 col-xl-6">
												<label class="form-label tx-require">{{ $t('wob.appointmentDate') }}</label>
												<vue-field
													v-model="visitDate"
													type="date"
													name="visitDate"
													class="form-control"
													:class="{ 'is-invalid': errors.visitDate }"
													:label="$t('wob.appointmentDate')"
													rules="required"
												/>
												<div>
													<span v-show="errors.visitDate" class="text-danger">{{ errors.visitDate }}</span>
												</div>
											</div>
											<div class="col-lg-3 col-xl-3">
												<label class="form-label tx-require">{{ $t('wob.appointmentTime') }}</label>
												<div class="input-group">
													<vue-field
														id="visitHour"
														v-model="visitHour"
														as="select"
														class="form-select"
														name="visitHour"
														:class="{ 'is-invalid': errors.visitHour }"
														rules="required"
														:label="$t('wob.appointmentTime')"
													>
														<option value="00">
															00
														</option>
														<option value="01">
															01
														</option>
														<option value="02">
															02
														</option>
														<option value="03">
															03
														</option>
														<option value="04">
															04
														</option>
														<option value="05">
															05
														</option>
														<option value="06">
															06
														</option>
														<option value="07">
															07
														</option>
														<option value="08">
															08
														</option>
														<option value="09">
															09
														</option>
														<option value="10">
															10
														</option>
														<option value="11">
															11
														</option>
														<option value="12">
															12
														</option>
														<option value="13">
															13
														</option>
														<option value="14">
															14
														</option>
														<option value="15">
															15
														</option>
														<option value="16">
															16
														</option>
														<option value="17">
															17
														</option>
														<option value="18">
															18
														</option>
														<option value="19">
															19
														</option>
														<option value="20">
															20
														</option>
														<option value="21">
															21
														</option>
														<option value="22">
															22
														</option>
														<option value="23">
															23
														</option>
													</vue-field>
													<span class="input-group-text">{{ $t('wob.hour') }}</span>
													<div>
														<span v-show="errors.visitHour" class="text-danger">{{ errors.visitHour }}</span>
													</div>
													<vue-field
														id="visitMin"
														v-model="visitMin"
														as="select"
														class="form-select"
														name="visitMin"
														:class="{ 'is-invalid': errors.visitMin }"
														rules="required"
														:label="$t('wob.appointmentTime')"
													>
														<option selected="selected" value="00">
															00
														</option>
														<option v-for="minute in selectMinutes" :value="minute">
															{{ minute }}
														</option>
													</vue-field>
													<span class="input-group-text">{{ $t('wob.minute') }}</span>
													<div>
														<span v-show="errors.visitMin" class="text-danger">{{ errors.visitMin }}</span>
													</div>
												</div>
											</div>
											<div class="col-lg-3 col-xl-3">
												<label class="form-label tx-require">{{ $t('wob.interviewPurpose') }}</label>
												<vue-field
													id="apptVisitPurCode"
													v-model="apptVisitPurCode"
													as="select"
													class="form-select"
													name="apptVisitPurCode"
													:class="{ 'is-invalid': errors.apptVisitPurCode }"
													rules="required"
													:label="$t('wob.interviewPurpose')"
												>
													<option disabled selected value="">
														{{ $t('wob.pleaseSelect') }}
													</option>
													<option v-for="visitPurpose in visitPurMenu" :value="visitPurpose.codeValue">
														{{ visitPurpose.codeName }}
													</option>
												</vue-field>
												<div style="height: 3px">
													<span v-show="errors.apptVisitPurCode" class="text-danger">{{ errors.apptVisitPurCode
													}}</span>
												</div>
											</div>
											<div class="col-lg-3 col-xl-3">
												<label class="form-label tx-require">{{ $t('wob.interviewMethod') }}</label>
												<vue-field
													id="apptVisitAprCode"
													v-model="apptVisitAprCode"
													as="select"
													class="form-select"
													name="apptVisitAprCode"
													:class="{ 'is-invalid': errors.apptVisitAprCode }"
													rules="required"
													:label="$t('wob.interviewMethod')"
												>
													<option disabled selected value="">
														{{ $t('wob.pleaseSelect') }}
													</option>
													<option v-for="visitType in visitAprMenu" :value="visitType.codeValue">
														{{ visitType.codeName }}
													</option>
												</vue-field>
												<div style="height: 3px">
													<span v-show="errors.apptVisitAprCode" class="text-danger">{{ errors.apptVisitAprCode
													}}</span>
												</div>
											</div>
											<div class="col-lg-3 col-xl-3">
												<label class="form-label tx-require">{{ $t('wob.interviewSubject') }}</label>
												<vue-field
													v-model="apptTitle"
													class="form-control"
													name="apptTitle"
													type="text"
													size="30"
													value="apptTitle"
													:class="{ 'is-invalid': errors.apptTitle }"
													:label="$t('wob.interviewSubject')"
													rules="required"
												/>
												<div style="height: 3px">
													<span v-show="errors.apptTitle" class="text-danger">{{ errors.apptTitle }}</span>
												</div>
											</div>
											<div class="col-lg-6 col-xl-6">
												<label class="form-label tx-require">{{ $t('wob.expiryNotificationSetting') }}</label>
												<br>
												<div class="form-check form-check-inline">
													<vue-field
														id="apptAdvNce_NN"
														v-model="apptAdvNce"
														type="radio"
														name="apptAdvNceYn"
														class="form-check-input"
														value="N"
														:class="{ 'is-invalid': errors.apptAdvNce }"
														rules="required"
														:label="$t('wob.expiryNotificationSetting')"
													/>
													<label class="form-check-label">{{ $t('wob.noAdvanceNotice') }}</label>
												</div>
												<div class="form-check form-check-inline">
													<vue-field
														id="apptAdvNce_YY"
														v-model="apptAdvNce"
														type="radio"
														name="apptAdvNceYn"
														class="form-check-input"
														value="Y"
														:class="{ 'is-invalid': errors.apptAdvNce }"
														rules="required"
														:label="$t('wob.expiryNotificationSetting')"
													/>
													<label class="form-check-label">{{ $t('wob.advanceNotice') }}</label>
												</div>

												<div class="d-inline-block">
													<div class="input-group">
														<vue-field
															v-model="apptAdvNceDay"
															class="form-control"
															name="apptAdvNceDay"
															type="text"
															size="30"
															:class="{ 'is-invalid': errors.apptAdvNceDay }"
															:label="$t('wob.advanceNotificationDaysWeeks')"
															:rules="apptAdvNce === 'Y' ? 'required' : ''"
														/>
														<div style="height: 3px">
															<span v-show="errors.apptAdvNceDay" class="text-danger">{{ errors.apptAdvNceDay }}</span>
														</div>
														<vue-field
															id="apptAdvNcePrd"
															v-model="apptAdvNcePrd"
															as="select"
															class="form-select"
															name="apptAdvNcePrd"
															:rules="apptAdvNce === 'Y' ? 'required' : ''"
															:class="{ 'is-invalid': errors.apptAdvNcePrd }"
															:label="$t('wob.expiryNotificationSetting')"
														>
															<option value="D">
																{{ $t('wob.day') }}
															</option>
															<option value="W">
																{{ $t('wob.week') }}
															</option>
														</vue-field>
														<span class="input-group-text">{{ $t('wob.notify') }}</span>
														<div style="height: 3px">
															<span v-show="errors.apptAdvNce" class="text-danger">{{ errors.apptAdvNce }}</span>
														</div>
													</div>
												</div>
											</div>
											<div class="col-lg-12">
												<label class="form-label tx-require">{{ $t('wob.interviewContent') }}</label>
												<vue-field
													id="content"
													v-model="content"
													as="textarea"
													class="form-control"
													name="content"
													rows="5"
													cols="50"
													size="400"
													:class="{ 'is-invalid': errors.content }"
													rules="required"
													:label="$t('wob.interviewContent')"
												/>
												<div style="height: 3px">
													<span v-show="errors.content" class="text-danger">{{ errors.content }}</span>
												</div>
											</div>
											<div class="col-lg-6">
												<div class="input-group">
													<span class="input-group-text">{{ $t('wob.commonPhrases') }}</span>
													<select
														id="reuseWord"
														v-model="reuseWord"
														name="reuseWord"
														class="form-select"
													>
														<option
															v-for="selectWords in wobReuseWords"
															v-show="selectWords.words"
															:value="selectWords.words"
														>
															{{ selectWords.words }}
														</option>
													</select>
													<button
														id="setContent"
														type="button"
														class="btn btn-primary"
														@click="appendReuseWord()"
													>
														{{ $t('wob.join') }}
													</button>
												</div>
											</div>
											<div class="col-lg-6">
												<div class="input-group">
													<span class="input-group-text">{{ $t('wob.commonPhrasesManagement') }}</span>
													<input
														id="words"
														v-model="newReuseWord"
														class="form-control"
														type="text"
														size="20"
														maxlength="20"
													>
													<button
														id="wordAdd"
														type="button"
														class="btn btn-primary"
														@click="insertReuseWord()"
													>
														{{ $t('wob.add') }}
													</button>
													<button
														id="wordSetting"
														type="button"
														class="btn btn-primary"
														@click.prevent="isOpenReuseWordModal = true"
													>
														{{ $t('wob.setting') }}
													</button>
												</div>
											</div>
										</div>
									</vue-form>
								</div>
							</div>
						</div>
						<div id="modifyTaskFooter" class="modal-footer">
							<input
								id="apptEditModalCloseButton"
								class="btn btn-white"
								type="button"
								:value="$t('wob.close')"
								@click.prevent="closeModal('modal2')"
							>
							<input
								id="btnSave"
								class="btn btn-primary"
								type="button"
								:value="$t('wob.save')"
								@click="updateTdRec('N')"
							>
							<input
								id="btnSaveAndClose"
								class="btn btn-primary"
								type="button"
								:value="$t('wob.saveAndClose')"
								@click="updateTdRec('Y')"
							>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal 2 end -->
		<vue-modal :is-open="isOpenReuseWordModal" @close="isOpenReuseWordModal = false">
			<template #content="props">
				<vue-cus-reuse-word-modal
					:id="'appoReuseWordModal'"
					:close="props.close"
					:wob-reuse-words="wobReuseWords"
					:super-modal-name="'updateAppointmentTaskModal'"
				/>
			</template>
		</vue-modal>
	</div>
</template>
<script>
import _ from 'lodash';
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import vueCusReuseWordModal from '@/views/cus/include/reuseWordModal.vue';
import { getImgURL } from '@/utils/imgURL.js';

export default {
	components: {
		vueModal,
		vueCusReuseWordModal,
		'vue-form': Form,
		'vue-field': Field
	},
	props: {
		recCode: String,
		apptTaskTdRec: Object,
		getCalendarTasks: Function,
		roleShowEditBtn: Boolean,
		userInfo: Object
	},
	data: function () {
		const minutes = [];
		for (let i = 1; i < 60; i++) {
			minutes.push(String(i).padStart(2, '0'));
		}
		return {
			isOpenReuseWordModal: false,
			modalStates: {
				modal1: false,
				modal2: false
			},
			tdRecTask: {},

			// 選單
			selectMinutes: minutes,
			visitPurMenu: [],
			visitAprMenu: [],

			// 客戶資料
			cusCode: null,
			cusInfo: {
				cusName: null,
				birth: null,
				email: null,
				phoneH: '',
				phoneO: '',
				phoneM: '',
				rankName: null,
				childInvYn: null,
				specRecommYn: null,
				specCusYn: null,
				logsCreateDt: null
			},

			// API用參數
			visitDate: null, // 約訪日期
			visitHour: null, // 約訪時間(時)
			visitMin: null, // 約訪時間(分)
			apptVisitPurCode: '', // 訪談目的
			apptVisitAprCode: '', // 訪談方式
			apptTitle: null, // 訪談主旨
			content: '', // 訪談內容
			apptAdvNce: null, // 到期通知設定
			apptAdvNceDay: null, // 到期通知天數
			apptAdvNcePrd: null, // 到期通知週期（日/週）

			// 畫面顯示用參數
			showEditBtn: false,
			// 常用句機制
			reuseWord: null,
			newReuseWord: null,
			wobReuseWords: null,
			editModal: null
		};
	},
	watch: {
		recCode: function (val) {
			const self = this;
			if (self.recCode) {
				self.getTdRec();
			}
		}
	},
	mounted: function () {
		const self = this;
		self.getVisitPurMenu();
		self.getVisitAprMenu();
		self.getReuseWords();
		self.setDefaultReuseWords();
		self.showEditBtn = false;
		self.editModal = null; // new bootstrap.Modal(self.$refs.editModal);
	},
	methods: {
		getImgURL,
		closeModal: function (modalName) {
			this.modalStates[modalName] = false;
			if (modalName == 'modal2') {
				this.modalStates.modal1 = true;
			}
		},
		openModal: function (modalName) {
			this.modalStates[modalName] = true;
		},
		getTdRec: function () {
			const self = this;
			self.$api
				.getAppointmentTdRecApi({
					recCode: self.recCode
				})
				.then(function (ret) {
					self.tdRecTask = ret.data;
					self.cusInfo.cusCode = self.tdRecTask.cusCode;
					self.cusInfo.birth = _.formatDate(ret.data.birth);

					// 設定編輯資料
					self.visitDate = self.tdRecTask.nextRemindDt.replaceAll('/', '-');
					const nextVisitTime = self.tdRecTask.nextRemindTime.split(':');
					self.visitHour = nextVisitTime[0];
					self.visitMin = nextVisitTime[1];
					self.apptVisitPurCode = self.tdRecTask.visitPurCode;
					self.apptVisitAprCode = self.tdRecTask.visitAprCode;
					self.apptTitle = self.tdRecTask.title;
					self.content = self.tdRecTask.content;
					self.apptAdvNce = self.tdRecTask.advNce;
					self.apptAdvNceDay = self.tdRecTask.advNceDay;
					self.apptAdvNcePrd = self.tdRecTask.advNcePrd;
				});
		},
		deleteTdRec: function () {
			const self = this;
			self.$bi.confirm(this.$t('wob.confirmDeleteData'), {
				event: {
					confirmOk: function () {
						self.$api
							.deleteTdRecApi({
								recCode: self.recCode
							})
							.then(function (ret) {
								self.$bi.alert(this.$t('wob.deleteSuccess'));
								self.modalStates.modal1 = false;
								if (self.getCalendarTasks) {
									self.getCalendarTasks();
								}
							});
					}
				}
			});
		},
		doUpdate: function () {
			const self = this;
			self.modalStates.modal1 = false;
			// 開啟第二個modal
			self.modalStates.modal2 = true;
			// self.editModal.show();
			self.getCusInfo();

			// 設定編輯資料
			self.visitDate = self.tdRecTask.nextRemindDt.replaceAll('/', '-');
			const nextVisitTime = self.tdRecTask.nextRemindTime.split(':');
			self.visitHour = nextVisitTime[0];
			self.visitMin = nextVisitTime[1];
			self.apptVisitPurCode = self.tdRecTask.visitPurCode;
			self.apptVisitAprCode = self.tdRecTask.visitAprCode;
			self.apptTitle = self.tdRecTask.title;
			self.content = self.tdRecTask.content;
			self.apptAdvNce = self.tdRecTask.advNce;
			self.apptAdvNceDay = self.tdRecTask.advNceDay;
			self.apptAdvNcePrd = self.tdRecTask.advNcePrd;
		},
		// 取得訪談目的選單
		getVisitPurMenu: function () {
			const self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'VISIT_PUR_CODE'
				})
				.then(function (ret) {
					self.visitPurMenu = ret.data;
				});
		},
		// 取得訪談方式選單
		getVisitAprMenu: function () {
			const self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'VISIT_APR_CODE'
				})
				.then(function (ret) {
					self.visitAprMenu = ret.data;
				});
		},
		getCusInfo: function () {
			const self = this;
			self.$api
				.getCustomer({
					cusCode: self.cusInfo.cusCode
				})
				.then(function (ret) {
					if (!ret.data) {
						self.$bi.alert(this.$t('wob.noCustomerFound'));
						self.clearValues();
						return;
					}
					self.cusInfo.cusName = ret.data.cusName;
					self.cusInfo.birth = ret.data.birth;
					self.cusInfo.email = ret.data.email;
					self.cusInfo.rankName = ret.data.rankName;
					self.cusInfo.childInvYn = ret.data.childInvYn;
					self.cusInfo.specRecommYn = ret.data.specRecommYn;
					self.cusInfo.specCusYn = ret.data.specCusYn;
					self.cusInfo.lastConnectionDt = ret.data.logCreateDt;

					// 將聯絡方式分類
					ret.data.contactInfoList.forEach(function (item) {
						switch (item.contactType) {
							case 'E': // email
								self.cusInfo.email = item.email;
								break;
							case 'H': // 住家電話
								self.cusInfo.phoneH = item.phone1;
								break;
							case 'O': // 公司電話
								self.cusInfo.phoneO = item.phone1;
								break;
							case 'M': // 手機
								self.cusInfo.phoneM = item.phone1;
								break;
						}
					});
				});
		},
		updateTdRec: function (doneYn) {
			const self = this;
			self.$refs.appointmentTask.validate().then(function (pass) {
				if (pass.valid) {
					const content = _.isNil(self.content) ? '' : self.content;
					self.$api
						.patchAppointmentTdRecApi({
							recCode: self.recCode,
							cusCode: self.cusInfo.cusCode,
							nextRemindDt: self.visitDate,
							nextRemindTime: self.visitHour + ':' + self.visitMin,
							advNce: self.apptAdvNce,
							advNceDay: self.apptAdvNce === 'Y' ? self.apptAdvNceDay : null,
							advNcePrd: self.apptAdvNce === 'Y' ? self.apptAdvNcePrd : null,
							visitAprCode: self.apptVisitAprCode,
							visitPurCode: self.apptVisitPurCode,
							title: self.apptTitle,
							content: content,
							doneYn: doneYn
						})
						.then(function (ret) {
							self.$bi.alert(this.$t('wob.updateSuccess'));
							// self.editModal.hide();
							self.modalStates.modal2 = false;

							if (self.getCalendarTasks) {
								self.getCalendarTasks();
							}
							self.getTdRec();
						});
				}
			});
		},
		setDefaultReuseWords: function () {
			const self = this;
			self.wobReuseWords = [];
			for (let i = 0; i < 10; i++) {
				const tempWordObj = {
					wordsId: i + 1,
					words: null
				};
				self.wobReuseWords.push(tempWordObj);
			}
		},
		getReuseWords: function () {
			const self = this;
			const type = self.$route.path.split('/').filter(Boolean)[0].toUpperCase();
			self.$api.getReuseWordsApi({ type }).then(function (ret) {
				self.wobReuseWords = ret.data || [];
			});
		},

		insertReuseWord: async function () {
			const self = this;
			const type = self.$route.path.split('/').filter(Boolean)[0].toUpperCase();
			const ret = await self.$api
				.postReuseWordsApi({
					type,
					words: self.newReuseWord
				});

			if (ret && ret.status === 200) {
				this.$swal.fire({
					title: '新增成功',
					text: `常用句已新增`,
					icon: 'success',
					confirmButtonText: '確認'
				});
				self.getReuseWords();
			}
			else {
				if (!self.memo) {
					self.memo = '';
				}
				self.memo = self.memo + self.newReuseWord;
				self.$bi.alert(this.$t('wob.commonPhrasesExceedLimit'));
			}
		},
		appendReuseWord: function () {
			const self = this;
			if (!self.content) {
				self.content = '';
			}
			self.content = self.content + self.reuseWord;
		},
		doClose: function () {
			const self = this;
			self.reuseWord = null;
			self.newReuseWord = null;
		},
		clearValues: function () {
			const self = this;

			self.cusInfo.cusName = null;
			self.cusInfo.birth = null;
			self.cusInfo.email = null;
			self.cusInfo.phoneH = '';
			self.cusInfo.phoneO = '';
			self.cusInfo.phoneM = '';
			self.cusInfo.rankName = null;
			self.cusInfo.childInvYn = null;
			self.cusInfo.specRecommYn = null;
			self.cusInfo.specCusYn = null;
			self.cusInfo.logsCreateDt = null;
		}
	}
};
</script>
