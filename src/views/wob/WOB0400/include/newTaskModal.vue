<template>
	<div>
		<!-- newTaskModal Add Task -->
		<vue-modal :is-open="isOpenModal" @close="closeModal">
			<template #content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								{{ $t('wob.newTask') }}
							</h4>
							<button type="button" class="btn-expand">
								<i class="bi bi-arrows-fullscreen" />
							</button>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="props.close()"
							/>
						</div>
						<div class="modal-body bg-white p-3">
							<!-- Switch new task type -->
							<div class="tab-nav-main">
								<ul class="nav nav-pills">
									<li v-if="!showSection || showSection == 1" class="nav-item">
										<a
											href="#"
											class="nav-link"
											:class="{ active: sectionCode == 1 }"
											@click.prevent="changeSession(1)"
										>{{ $t('wob.personalNotes') }}</a>
									</li>
									<li v-if="!showSection || showSection == 2" class="nav-item">
										<a
											v-if="userInfo?.roleType === 'RM'"
											href="#"
											class="nav-link"
											:class="{ active: sectionCode == 2 }"
											@click.prevent="changeSession(2)"
										>{{ $t('wob.appointmentSchedule') }}</a>
									</li>
									<li v-if="!showSection || showSection == 3" class="nav-item">
										<a
											v-if="userInfo?.roleType === 'RM'"
											href="#"
											class="nav-link"
											:class="{ active: sectionCode == 3 }"
											@click.prevent="changeSession(3)"
										>{{ $t('wob.contactRecord') }}</a>
									</li>
									<li v-if="!showSection || showSection == 4" class="nav-item">
										<a
											v-if="userInfo?.roleType === 'RM'"
											href="#"
											class="nav-link"
											:class="{ active: sectionCode == 4 }"
											@click.prevent="changeSession(4)"
										>{{ $t('wob.customerImportantDate') }}</a>
									</li>
								</ul>
							</div>

							<div class="tab-content">
								<div v-if="sectionCode !== 1" class="tab-pane fade show active">
									<table v-if="!propCusCode" class="biv-table table table-RWD table-horizontal-RWD">
										<tbody>
											<tr>
												<th>
													<label v-if="sectionCode == 2" class="form-label tx-require">{{ $t('wob.appointmentEvent') }}</label>
													<label v-if="sectionCode == 3" class="form-label tx-require">{{ $t('wob.contactCustomer') }}</label>
													<label v-if="sectionCode == 4" class="form-label tx-require">{{ $t('wob.customerImportantDate') }}</label>
												</th>
												<td>
													<div class="input-group">
														<div class="input-group-text">
															{{ $t('wob.customerGroup') }}
														</div>
														<select v-model="groupCode" name="groupCode" class="form-select">
															<option selected disabled value="">
																{{ $t('wob.pleaseSelect') }}
															</option>
															<option v-for="apptCusGroup in selectCusGroup" :value="apptCusGroup.groupCode">
																{{ apptCusGroup.groupName }}
															</option>
														</select>
														<div class="input-group-text">
															{{ $t('wob.customer') }}
														</div>
														<select v-model="queryCusCode" class="form-select">
															<option selected disabled value="">
																{{ $t('wob.pleaseSelect') }}
															</option>
															<option v-for="apptGroupCustomer in selectCusList" :value="apptGroupCustomer.cusCode">
																{{ apptGroupCustomer.cusName }} &nbsp; {{ apptGroupCustomer.idn }}
															</option>
														</select>
													</div>
													<div class="input-group">
														<div class="input-group-text">
															{{ $t('wob.customerIdTaxId') }}
														</div>
														<input v-model="queryIdn" class="form-control" type="text">
														<button class="btn btn-primary btn-glow" type="button" @click="getCusInfo()">
															{{ $t('wob.search') }}
														</button>
													</div>
												</td>
											</tr>
										</tbody>
									</table>
									<div v-if="isShowCusInfo" class="card-clientCard">
										<div class="card shadow-none mb-3">
											<table>
												<tbody>
													<tr>
														<td width="10%" class="clientCard-icon">
															<div class="avatar avatar-male">
																<img :src="getImgURL('avatar', 'man-1.png')" class="rounded-circle bg-info">
																<!-- <img th:src="@{/images/avatar/man-3.png}" class="rounded-circle bg-info" v-if="gender =='F'">-->
															</div>
															<h5 class="mb-0">
																{{ cusInfo.cusName || '--' }}
															</h5>
														</td>
														<td width="90%">
															<div class="caption tx-black">
																<span class=" ">{{ $t('wob.recentContactDate') }}：{{ $filters.formatDate(cusInfo.lastConnectionDt) || '--' }}
																</span>
															</div>
															<div class="row">
																<div class="col-lg-4">
																	<ul class="list-unstyled profile-info-list">
																		<li>
																			<i class="bi bi-clipboard-data mg-xl-e-5-f" />{{ $t('wob.investmentAttribute') }}：{{
																				cusInfo.rankName || '--'
																			}}
																		</li>
																		<li>
																			<i class="bi bi-gift mg-xl-e-5-f" />{{ $t('wob.birthday') }}：{{ cusInfo.birth || '--' }}
																		</li>
																		<li>
																			<i class="bi bi-envelope mg-xl-e-5-f" />{{ $t('wob.email') }}：{{
																				cusInfo.email || '--'
																			}}
																		</li>
																	</ul>
																</div>
																<div class="col-lg-3">
																	<ul class="list-unstyled profile-info-list">
																		<li>
																			<i class="bi bi-house-door mg-xl-e-5-f" />{{ $t('wob.contactPhoneHome') }}：{{
																				cusInfo.phoneH || '--'
																			}}
																		</li>
																		<li>
																			<i class="bi bi-building mg-xl-e-5-f" />{{ $t('wob.contactPhoneOffice') }}：{{
																				cusInfo.phoneO || '--'
																			}}
																		</li>
																		<li>
																			<i class="bi bi-telephone mg-xl-e-5-f" />{{ $t('wob.contactPhoneMobile') }}：{{
																				cusInfo.phoneM || '--'
																			}}
																		</li>
																	</ul>
																</div>
																<div class="col-lg-5">
																	<ul class="list-unstyled profile-info-list">
																		<li>
																			<i class="bi bi-journal-medical mg-xl-e-5-f" />{{ $t('wob.minorWealthManagementConsent') }}：
																			<span :class="cusInfo.childInvYn === 'Y' ? 'tx-red' : 'tx-green'">
																				{{ cusInfo.childInvYn === 'Y' ? $t('wob.yes') : $t('wob.no') }}
																			</span>
																		</li>
																		<li>
																			<i class="bi bi-journal-text mg-xl-e-5-f" />{{ $t('wob.specificTrustInvestmentConsent') }}：
																			<span :class="cusInfo.specRecommYn === 'Y' ? 'tx-red' : 'tx-green'">
																				{{ cusInfo.specRecommYn === 'Y' ? $t('wob.yes') : $t('wob.no') }}
																			</span>
																		</li>
																		<li>
																			<i class="bi bi-journal-bookmark mg-xl-e-5-f" />{{ $t('wob.wealthSpecificCustomer') }}：
																			<span :class="cusInfo.specCusYn === 'Y' ? 'tx-red' : 'tx-green'">
																				{{ cusInfo.specCusYn === 'Y' ? $t('wob.yes') : $t('wob.no') }}
																			</span>
																		</li>
																	</ul>
																</div>
															</div>
														</td>
													</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>

								<!-- {{ $t('wob.personalNotes') }} -->
								<div v-if="sectionCode == 1" class="tab-pane fade show active">
									<!--   <div class="card card-form shadow-none" v-if="cusInfo.cusCode">-->
									<div class="card card-form shadow-none">
										<div class="card-header">
											<h4>{{ $t('wob.personalNotes') }}</h4>
											<span class="tx-square-bracket">{{ $t('wob.requiredField') }}</span>
										</div>
										<div class="card-content">
											<vue-form v-slot="{ errors, validate }" ref="tdPersonalRec">
												<table class="biv-table table table-RWD table-bordered">
													<tbody>
														<tr>
															<th>
																<label class="form-label tx-require">{{ $t('wob.workDate') }}</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field
																		v-model="nextRemindDt"
																		type="date"
																		name="nextRemindDt"
																		class="form-control"
																		rules="required"
																		:class="{ 'is-invalid': errors.nextRemindDt }"
																		:label="$t('wob.workDate')"
																	/>
																	<div>
																		<span v-show="errors.nextRemindDt" class="text-danger">{{
																			errors.nextRemindDt
																		}}</span>
																	</div>
																</div>
															</td>
															<th>
																<label class="form-label tx-require">{{ $t('wob.workTime') }}</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field
																		id="appoHour"
																		v-model="appoHour"
																		as="select"
																		class="form-select"
																		name="appoHour"
																		rules="required"
																		:class="{ 'is-invalid': errors.appoHour }"
																		:label="$t('wob.workTime')"
																	>
																		<option value="00">
																			00
																		</option>
																		<option value="01">
																			01
																		</option>
																		<option value="02">
																			02
																		</option>
																		<option value="03">
																			03
																		</option>
																		<option value="04">
																			04
																		</option>
																		<option value="05">
																			05
																		</option>
																		<option value="06">
																			06
																		</option>
																		<option value="07">
																			07
																		</option>
																		<option value="08">
																			08
																		</option>
																		<option value="09">
																			09
																		</option>
																		<option value="10">
																			10
																		</option>
																		<option value="11">
																			11
																		</option>
																		<option value="12">
																			12
																		</option>
																		<option value="13">
																			13
																		</option>
																		<option value="14">
																			14
																		</option>
																		<option value="15">
																			15
																		</option>
																		<option value="16">
																			16
																		</option>
																		<option value="17">
																			17
																		</option>
																		<option value="18">
																			18
																		</option>
																		<option value="19">
																			19
																		</option>
																		<option value="20">
																			20
																		</option>
																		<option value="21">
																			21
																		</option>
																		<option value="22">
																			22
																		</option>
																		<option value="23">
																			23
																		</option>
																	</vue-field>
																	<span class="input-group-text">{{ $t('wob.hour') }}</span>
																	<div>
																		<span v-show="errors.appoHour" class="text-danger">{{
																			errors.appoHour
																		}}</span>
																	</div>
																	<vue-field
																		id="appoMinute"
																		v-model="appoMinute"
																		as="select"
																		class="form-select"
																		name="appoMinute"
																		rules="required"
																		:class="{ 'is-invalid': errors.appoMinute }"
																		:label="$t('wob.workTime')"
																	>
																		<option selected="selected" value="00">
																			00
																		</option>
																		<option v-for="minute in selectMinutes" :value="minute">
																			{{ minute }}
																		</option>
																	</vue-field>
																	<span class="input-group-text">{{ $t('wob.minute') }}</span>
																	<div>
																		<span v-show="errors.appoMinute" class="text-danger">{{
																			errors.appoMinute
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">{{ $t('wob.expiryNotificationSetting') }}</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<div class="form-check form-check-inline">
																		<vue-field
																			id="apptAdvNce_NN"
																			v-model="advNce"
																			type="radio"
																			name="apptAdvNceYn"
																			class="form-check-input"
																			value="N"
																			:class="{ 'is-invalid': errors.advNce }"
																			rules="required"
																			:label="$t('wob.expiryNotificationSetting')"
																		/>
																		<label class="form-check-label">{{ $t('wob.noAdvanceNotice') }}</label>
																	</div>
																	<div class="form-check form-check-inline">
																		<vue-field
																			id="apptAdvNce_YY"
																			v-model="advNce"
																			type="radio"
																			name="apptAdvNceYn"
																			class="form-check-input"
																			value="Y"
																			:class="{ 'is-invalid': errors.advNce }"
																			rules="required"
																			:label="$t('wob.expiryNotificationSetting')"
																		/>
																		<label class="form-check-label">{{ $t('wob.advance') }}</label>
																	</div>

																	<vue-field
																		v-model="advNceDay"
																		class="form-control"
																		name="advNceDay"
																		type="text"
																		size="30"
																		value="advNceDay"
																		:class="{ 'is-invalid': errors.advNceDay }"
																		:label="$t('wob.advanceNotificationDaysWeeks')"
																		:rules="advNce === 'Y' ? 'required' : ''"
																	/>
																	<div style="height: 3px">
																		<span v-show="errors.advNceDay" class="text-danger">{{
																			errors.advNceDay
																		}}</span>
																	</div>

																	<vue-field
																		id="advNcePrd"
																		v-model="advNcePrd"
																		as="select"
																		class="form-select"
																		name="advNcePrd"
																		:class="{ 'is-invalid': errors.advNcePrd }"
																		:rules="advNcePrd === 'Y' ? 'required' : ''"
																		:label="$t('wob.advanceNotificationPeriod')"
																	>
																		<option value="D">
																			{{ $t('wob.day') }}
																		</option>
																		<option value="W">
																			{{ $t('wob.week') }}
																		</option>
																	</vue-field>
																	<div style="height: 3px">
																		<span v-show="errors.advNcePrd" class="text-danger">{{
																			errors.advNcePrd
																		}}</span>
																	</div>

																	<span class="input-group-text">{{ $t('wob.notify') }}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th><label class="form-label tx-require">{{ $t('wob.subject') }}</label></th>
															<td colspan="3">
																<div class="input-group">
																	<vue-field
																		v-model="personalWorkTitle"
																		class="form-control"
																		name="personalWorkTitle"
																		type="text"
																		size="30"
																		value="personalWorkTitle"
																		:class="{ 'is-invalid': errors.personalWorkTitle }"
																		:label="$t('wob.subject')"
																		rules="required"
																	/>
																	<div style="height: 3px">
																		<span v-show="errors.personalWorkTitle" class="text-danger">{{
																			errors.personalWorkTitle
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">{{ $t('wob.content') }}</label>
															</th>
															<td colspan="3">
																<vue-field
																	id="content"
																	v-model="content"
																	as="textarea"
																	class="form-control"
																	name="content"
																	rows="5"
																	cols="50"
																	size="400"
																	:class="{ 'is-invalid': errors.content }"
																	:label="$t('wob.content')"
																	rules="required"
																/>
																<div style="height: 3px">
																	<span v-show="errors.content" class="text-danger">{{ errors.content }}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<span class="form-label">{{ $t('wob.commonPhrases') }}</span>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<select
																		id="reuseWord"
																		v-model="reuseWord"
																		name="reuseWord"
																		class="form-select"
																	>
																		<option
																			v-for="selectWords in wobReuseWords"
																			v-show="selectWords.words"
																			:value="selectWords.words"
																		>
																			{{ selectWords.words }}
																		</option>
																	</select>
																	<button
																		id="setContent"
																		type="button"
																		class="btn btn-primary"
																		@click="appendReuseWord('content')"
																	>
																		{{ $t('wob.join') }}
																	</button>
																	<input
																		id="words"
																		v-model="newReuseWord"
																		class="form-control"
																		type="text"
																		size="20"
																		maxlength="20"
																	>
																	<button
																		id="wordAdd"
																		type="button"
																		class="btn btn-primary"
																		@click="insertReuseWord()"
																	>
																		{{ $t('wob.add') }}
																	</button>
																	<!--                                                          <button type="button" class="btn btn-primary" id="wordSetting" data-bs-toggle="modal" data-bs-target="#newTaskReuseWordModal" data-bs-dismiss="modal">{{ $t('wob.setting') }}</button>-->
																	<!-- 移除設定button -->
																	<!-- <button
																		id="wordSetting"
																		type="button"
																		class="btn btn-primary"
																		@click.prevent="handleWordSettingClick(props.close)"
																	>
																		{{ $t('wob.setting') }}
																	</button> -->
																</div>
															</td>
														</tr>
													</tbody>
												</table>
											</vue-form>
										</div>
									</div>
								</div>

								<!-- 約訪紀錄 -->
								<div v-if="sectionCode == 2" class="tab-pane fade show active">
									<div class="card card-form shadow-none">
										<div class="card-header">
											<h4>約訪行程</h4>
											<span class="tx-square-bracket">{{ $t('wob.requiredField') }}</span>
										</div>
										<div class="card-content">
											<vue-form v-slot="{ errors, validate }" ref="appointmentTask">
												<table class="biv-table table table-RWD table-borderless">
													<tbody>
														<tr>
															<th>
																<label class="form-label tx-require">約訪{{ $t('wob.day') }}期</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field
																		v-model="visitDate"
																		type="date"
																		name="visitDate"
																		class="form-control"
																		:class="{ 'is-invalid': errors.visitDate }"
																		label="約訪{{ $t('wob.day') }}期"
																		rules="required"
																	/>
																	<div>
																		<span v-show="errors.visitDate" class="text-danger">{{
																			errors.visitDate
																		}}</span>
																	</div>
																</div>
															</td>
															<th>
																<label class="form-label tx-require">約訪{{ $t('wob.hour') }}間</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field
																		id="visitHour"
																		v-model="visitHour"
																		as="select"
																		class="form-select"
																		name="visitHour"
																		:class="{ 'is-invalid': errors.visitHour }"
																		rules="required"
																		label="約訪{{ $t('wob.hour') }}間"
																	>
																		<option value="00">
																			00
																		</option>
																		<option value="01">
																			01
																		</option>
																		<option value="02">
																			02
																		</option>
																		<option value="03">
																			03
																		</option>
																		<option value="04">
																			04
																		</option>
																		<option value="05">
																			05
																		</option>
																		<option value="06">
																			06
																		</option>
																		<option value="07">
																			07
																		</option>
																		<option value="08">
																			08
																		</option>
																		<option value="09">
																			09
																		</option>
																		<option value="10">
																			10
																		</option>
																		<option value="11">
																			11
																		</option>
																		<option value="12">
																			12
																		</option>
																		<option value="13">
																			13
																		</option>
																		<option value="14">
																			14
																		</option>
																		<option value="15">
																			15
																		</option>
																		<option value="16">
																			16
																		</option>
																		<option value="17">
																			17
																		</option>
																		<option value="18">
																			18
																		</option>
																		<option value="19">
																			19
																		</option>
																		<option value="20">
																			20
																		</option>
																		<option value="21">
																			21
																		</option>
																		<option value="22">
																			22
																		</option>
																		<option value="23">
																			23
																		</option>
																	</vue-field>
																	<span class="input-group-text">{{ $t('wob.hour') }}</span>
																	<div>
																		<span v-show="errors.visitHour" class="text-danger">{{
																			errors.visitHour
																		}}</span>
																	</div>
																	<vue-field
																		id="visitMin"
																		v-model="visitMin"
																		as="select"
																		class="form-select"
																		name="visitMin"
																		:class="{ 'is-invalid': errors.visitMin }"
																		rules="required"
																		label="約訪{{ $t('wob.hour') }}間"
																	>
																		<option selected="selected" value="00">
																			00
																		</option>
																		<option v-for="minute in selectMinutes" :value="minute">
																			{{ minute }}
																		</option>
																	</vue-field>
																	<span class="input-group-text">{{ $t('wob.minute') }}</span>
																	<div>
																		<span v-show="errors.visitMin" class="text-danger">{{
																			errors.visitMin
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">{{ $t('wob.expiryNotificationSetting') }}</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<div class="form-check form-check-inline">
																		<vue-field
																			id="apptAdvNce_NN"
																			v-model="apptAdvNce"
																			type="radio"
																			name="apptAdvNceYn"
																			class="form-check-input"
																			value="N"
																			:class="{ 'is-invalid': errors.apptAdvNce }"
																			rules="required"
																			:label="$t('wob.expiryNotificationSetting')"
																		/>
																		<label class="form-check-label">{{ $t('wob.noAdvanceNotice') }}</label>
																	</div>
																	<div class="form-check form-check-inline">
																		<vue-field
																			id="apptAdvNce_YY"
																			v-model="apptAdvNce"
																			type="radio"
																			name="apptAdvNceYn"
																			class="form-check-input"
																			value="Y"
																			:class="{ 'is-invalid': errors.apptAdvNce }"
																			rules="required"
																			:label="$t('wob.expiryNotificationSetting')"
																		/>
																		<label class="form-check-label">{{ $t('wob.advance') }}</label>
																	</div>
																	<vue-field
																		v-model="apptAdvNceDay"
																		class="form-control"
																		name="apptAdvNceDay"
																		type="text"
																		size="30"
																		value="apptAdvNceDay"
																		:class="{ 'is-invalid': errors.apptAdvNceDay }"
																		:label="$t('wob.advanceNotificationDaysWeeks')"
																		:rules="apptAdvNce === 'Y' ? 'required' : ''"
																	/>
																	<div style="height: 3px">
																		<span v-show="errors.apptAdvNceDay" class="text-danger">{{
																			errors.apptAdvNceDay
																		}}</span>
																	</div>
																	<vue-field
																		id="apptAdvNcePrd"
																		v-model="apptAdvNcePrd"
																		as="select"
																		class="form-select"
																		name="apptAdvNcePrd"
																		:rules="apptAdvNce === 'Y' ? 'required' : ''"
																		:class="{ 'is-invalid': errors.apptAdvNcePrd }"
																		:label="$t('wob.expiryNotificationSetting')"
																	>
																		<option value="D">
																			{{ $t('wob.day') }}
																		</option>
																		<option value="W">
																			{{ $t('wob.week') }}
																		</option>
																	</vue-field>
																	<span class="input-group-text">{{ $t('wob.notify') }}</span>
																	<div style="height: 3px">
																		<span v-show="errors.apptAdvNce" class="text-danger">{{
																			errors.apptAdvNce
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">訪談目的</label>
															</th>
															<td>
																<vue-field
																	id="apptVisitPurCode"
																	v-model="apptVisitPurCode"
																	as="select"
																	class="form-select"
																	name="apptVisitPurCode"
																	:class="{ 'is-invalid': errors.apptVisitPurCode }"
																	rules="required"
																	label="訪談目的"
																>
																	<option disabled selected value="">
																		{{ $t('wob.pleaseSelect') }}
																	</option>
																	<option v-for="visitPurpose in visitPurMenu" :value="visitPurpose.codeValue">
																		{{ visitPurpose.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span v-show="errors.apptVisitPurCode" class="text-danger">{{
																		errors.apptVisitPurCode
																	}}</span>
																</div>
															</td>
															<th>
																<label class="form-label tx-require">訪談方式</label>
															</th>
															<td>
																<vue-field
																	id="apptVisitAprCode"
																	v-model="apptVisitAprCode"
																	as="select"
																	class="form-select"
																	name="apptVisitAprCode"
																	:class="{ 'is-invalid': errors.apptVisitAprCode }"
																	rules="required"
																	label="類型"
																>
																	<option disabled selected value="">
																		{{ $t('wob.pleaseSelect') }}
																	</option>
																	<option v-for="visitType in visitAprMenu" :value="visitType.codeValue">
																		{{ visitType.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span v-show="errors.apptVisitAprCode" class="text-danger">{{
																		errors.apptVisitAprCode
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">訪談{{ $t('wob.subject') }}</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<vue-field
																		v-model="apptTitle"
																		class="form-control"
																		name="apptTitle"
																		type="text"
																		size="30"
																		value="apptTitle"
																		:class="{ 'is-invalid': errors.apptTitle }"
																		label="訪談{{ $t('wob.subject') }}"
																		rules="required"
																	/>
																	<div style="height: 3px">
																		<span v-show="errors.apptTitle" class="text-danger">{{
																			errors.apptTitle
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">訪談{{ $t('wob.content') }}</label>
															</th>
															<td colspan="3">
																<vue-field
																	id="visitContent"
																	v-model="visitContent"
																	as="textarea"
																	class="form-control"
																	name="visitContent"
																	rows="5"
																	cols="50"
																	size="400"
																	:class="{ 'is-invalid': errors.visitContent }"
																	rules="required"
																	label="訪談{{ $t('wob.content') }}"
																/>
																<div style="height: 3px">
																	<span v-show="errors.visitContent" class="text-danger">{{
																		errors.visitContent
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<span class="form-label">{{ $t('wob.commonPhrases') }}</span>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<select
																		id="reuseWord"
																		v-model="reuseWord"
																		name="reuseWord"
																		class="form-select"
																	>
																		<option
																			v-for="selectWords in wobReuseWords"
																			v-show="selectWords.words"
																			:value="selectWords.words"
																		>
																			{{ selectWords.words }}
																		</option>
																	</select>
																	<button
																		id="setContent"
																		type="button"
																		class="btn btn-primary"
																		@click="appendReuseWord('content')"
																	>
																		{{ $t('wob.join') }}
																	</button>
																	<input
																		id="words"
																		v-model="newReuseWord"
																		class="form-control"
																		type="text"
																		size="20"
																		maxlength="20"
																	>
																	<button
																		id="wordAdd"
																		type="button"
																		class="btn btn-primary"
																		@click="insertReuseWord()"
																	>
																		{{ $t('wob.add') }}
																	</button>
																	<button
																		id="wordSetting"
																		type="button"
																		class="btn btn-primary"
																		@click.prevent="
																			openReuseWordModal();
																			props.close();
																		"
																	>
																		{{ $t('wob.setting') }}
																	</button>
																</div>
															</td>
														</tr>
													</tbody>
												</table>
											</vue-form>
										</div>
									</div>
								</div>

								<!-- 聯繫紀錄 -->
								<div v-if="sectionCode == 3" class="tab-pane fade show active">
									<div class="card card-form shadow-none">
										<div class="card-header">
											<h4>聯繫紀錄</h4>
											<span class="tx-square-bracket">{{ $t('wob.requiredField') }}</span>
										</div>
										<div class="card-content">
											<vue-form v-slot="{ errors, validate }" ref="tdConnTask">
												<table class="biv-table table table-RWD table-borderless">
													<tbody>
														<tr>
															<th>
																<label class="form-label tx-require">聯繫{{ $t('wob.day') }}期</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field
																		v-model="contDate"
																		type="date"
																		name="contDate"
																		class="form-control"
																		:class="{ 'is-invalid': errors.contDate }"
																		label="聯繫{{ $t('wob.day') }}期"
																		rules="required"
																	/>
																	<div>
																		<span v-show="errors.contDate" class="text-danger">{{
																			errors.contDate
																		}}</span>
																	</div>
																</div>
															</td>
															<th>
																<label class="form-label">聯繫{{ $t('wob.hour') }}間</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field
																		id="contHour"
																		v-model="contHour"
																		as="select"
																		class="form-select"
																		name="contHour"
																		:class="{ 'is-invalid': errors.contHour }"
																		rules="required"
																		label="聯繫{{ $t('wob.hour') }}間"
																	>
																		<option value="00">
																			00
																		</option>
																		<option value="01">
																			01
																		</option>
																		<option value="02">
																			02
																		</option>
																		<option value="03">
																			03
																		</option>
																		<option value="04">
																			04
																		</option>
																		<option value="05">
																			05
																		</option>
																		<option value="06">
																			06
																		</option>
																		<option value="07">
																			07
																		</option>
																		<option value="08">
																			08
																		</option>
																		<option value="09">
																			09
																		</option>
																		<option value="10">
																			10
																		</option>
																		<option value="11">
																			11
																		</option>
																		<option value="12">
																			12
																		</option>
																		<option value="13">
																			13
																		</option>
																		<option value="14">
																			14
																		</option>
																		<option value="15">
																			15
																		</option>
																		<option value="16">
																			16
																		</option>
																		<option value="17">
																			17
																		</option>
																		<option value="18">
																			18
																		</option>
																		<option value="19">
																			19
																		</option>
																		<option value="20">
																			20
																		</option>
																		<option value="21">
																			21
																		</option>
																		<option value="22">
																			22
																		</option>
																		<option value="23">
																			23
																		</option>
																	</vue-field>
																	<span class="input-group-text">{{ $t('wob.hour') }}</span>
																	<div>
																		<span v-show="errors.contHour" class="text-danger">{{
																			errors.contHour
																		}}</span>
																	</div>
																	<vue-field
																		id="contMin"
																		v-model="contMin"
																		as="select"
																		class="form-select"
																		name="contMin"
																		:class="{ 'is-invalid': errors.contMin }"
																		rules="required"
																		label="聯繫{{ $t('wob.hour') }}間"
																	>
																		<option selected="selected" value="00">
																			00
																		</option>
																		<option v-for="minute in selectMinutes" :value="minute">
																			{{ minute }}
																		</option>
																	</vue-field>
																	<span class="input-group-text">{{ $t('wob.minute') }}</span>
																	<div>
																		<span v-show="errors.contMin" class="text-danger">{{ errors.contMin }}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">{{ $t('wob.subject') }}</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<vue-field
																		v-model="contTitle"
																		class="form-control"
																		name="contTitle"
																		type="text"
																		size="30"
																		value="contTitle"
																		:class="{ 'is-invalid': errors.contTitle }"
																		:label="$t('wob.subject')"
																		rules="required"
																	/>
																	<div style="height: 3px">
																		<span v-show="errors.contTitle" class="text-danger">{{
																			errors.contTitle
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">處理方式</label>
															</th>
															<td colspan="3">
																<vue-field
																	id="contVisitAprCode"
																	v-model="contVisitAprCode"
																	as="select"
																	class="form-select"
																	name="contVisitAprCode"
																	:class="{ 'is-invalid': errors.contVisitAprCode }"
																	rules="required"
																	label="處理方式"
																>
																	<option disabled selected value="">
																		{{ $t('wob.pleaseSelect') }}
																	</option>
																	<option v-for="visitType in visitAprMenu" :value="visitType.codeValue">
																		{{ visitType.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span v-show="errors.contVisitAprCode" class="text-danger">{{
																		errors.contVisitAprCode
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">處理{{ $t('wob.content') }}</label>
															</th>
															<td colspan="3">
																<vue-field
																	id="contContent"
																	v-model="contContent"
																	as="textarea"
																	class="form-control"
																	name="contContent"
																	rows="5"
																	cols="50"
																	size="400"
																	:class="{ 'is-invalid': errors.contContent }"
																	rules="required"
																	label="處理{{ $t('wob.content') }}"
																/>
																<div style="height: 3px">
																	<span v-show="errors.contContent" class="text-danger">{{
																		errors.contContent
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">聯絡狀況</label>
															</th>
															<td colspan="3">
																<vue-field
																	id="contStatCode"
																	v-model="contStatCode"
																	as="select"
																	class="form-select"
																	name="contStatCode"
																	:class="{ 'is-invalid': errors.contStatCode }"
																	rules="required"
																	label="聯絡狀況"
																>
																	<option disabled selected value="">
																		{{ $t('wob.pleaseSelect') }}
																	</option>
																	<option v-for="contStat in contStatMenu" :value="contStat.codeValue">
																		{{ contStat.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span v-show="errors.contStatCode" class="text-danger">{{
																		errors.contStatCode
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">後續處理</label>
															</th>
															<td colspan="3">
																<vue-field
																	id="contProcCode"
																	v-model="contProcCode"
																	as="select"
																	class="form-select"
																	name="contProcCode"
																	:class="{ 'is-invalid': errors.contProcCode }"
																	rules="required"
																	label="後續處理"
																>
																	<option disabled selected value="">
																		{{ $t('wob.pleaseSelect') }}
																	</option>
																	<option v-for="contProc in contProcMenu" :value="contProc.codeValue">
																		{{ contProc.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span v-show="errors.contProcCode" class="text-danger">{{
																		errors.contProcCode
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">是否結案</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<div class="form-check form-check-inline">
																		<vue-field
																			id="doneYn_YY"
																			v-model="doneYn"
																			type="radio"
																			name="doneYn"
																			class="form-check-input"
																			value="Y"
																			:class="{ 'is-invalid': errors.doneYn }"
																			rules="required"
																			label="是否結案"
																		/>
																		<label class="form-check-label">已結案</label>
																	</div>
																	<div class="form-check form-check-inline">
																		<vue-field
																			id="doneYn_NN"
																			v-model="doneYn"
																			type="radio"
																			name="doneYn"
																			class="form-check-input"
																			value="N"
																			:class="{ 'is-invalid': errors.doneYn }"
																			rules="required"
																			label="是否結案"
																		/>
																		<label class="form-check-label">未結案</label>
																	</div>
																	<div style="height: 3px">
																		<span v-show="errors.doneYn" class="text-danger">{{ errors.doneYn }}</span>
																	</div>
																</div>
															</td>
														</tr>
													</tbody>
												</table>
											</vue-form>
										</div>
									</div>
								</div>

								<!-- 客戶重要{{ $t('wob.day') }}子 -->
								<div v-if="sectionCode == 4" class="tab-pane fade show active">
									<div class="card card-form shadow-none">
										<div class="card-header">
											<h4>客戶重要{{ $t('wob.day') }}子</h4>
											<span class="tx-square-bracket">{{ $t('wob.requiredField') }}</span>
										</div>
										<div class="card-content">
											<vue-form v-slot="{ errors, validate }" ref="memoryDateTask">
												<table class="biv-table table table-RWD table-bordered">
													<tbody>
														<tr>
															<th>
																<label class="form-label tx-require">重要{{ $t('wob.day') }}子</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field
																		v-model="memoryDate"
																		type="date"
																		name="memoryDate"
																		class="form-control"
																		:class="{ 'is-invalid': errors.memoryDate }"
																		:label="$t('wob.workDate')"
																		rules="required"
																	/>
																	<div>
																		<span v-show="errors.memoryDate" class="text-danger">{{
																			errors.memoryDate
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>

														<tr>
															<th><label class="form-label tx-require">{{ $t('wob.content') }}</label></th>
															<td colspan="3">
																<vue-field
																	id="memContent"
																	v-model="memContent"
																	as="textarea"
																	class="form-control"
																	name="memContent"
																	rows="5"
																	cols="50"
																	size="400"
																	:class="{ 'is-invalid': errors.memContent }"
																	:label="$t('wob.content')"
																	rules="required"
																/>
																<div>
																	<span v-show="errors.memContent" class="text-danger">{{
																		errors.memContent
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">{{ $t('wob.expiryNotificationSetting') }}</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<div class="form-check form-check-inline">
																		<vue-field
																			id="memRemindYn_NN"
																			v-model="memRemindYn"
																			type="radio"
																			name="memRemindYn"
																			class="form-check-input"
																			value="N"
																			:class="{ 'is-invalid': errors.memRemindYn }"
																			rules="required"
																			:label="$t('wob.expiryNotificationSetting')"
																		/>
																		<label class="form-check-label">{{ $t('wob.noAdvanceNotice') }}</label>
																	</div>
																	<div class="form-check form-check-inline">
																		<vue-field
																			id="memRemindYn_YY"
																			v-model="memRemindYn"
																			type="radio"
																			name="memRemindYn"
																			class="form-check-input"
																			value="Y"
																			:class="{ 'is-invalid': errors.memRemindYn }"
																			rules="required"
																			:label="$t('wob.expiryNotificationSetting')"
																		/>
																		<label class="form-check-label">{{ $t('wob.advance') }}</label>
																	</div>
																	<vue-field
																		v-model="memRemindDays"
																		class="form-control"
																		name="memRemindDays"
																		type="text"
																		size="30"
																		value="memRemindDays"
																		:rules="memRemindYn === 'Y' ? 'required' : ''"
																		:class="{ 'is-invalid': errors.memRemindDays }"
																		:label="$t('wob.advanceNotificationDaysWeeks')"
																	/>
																	<div style="height: 3px">
																		<span v-show="errors.memRemindDays" class="text-danger">
																			{{ errors.memRemindDays }}
																		</span>
																	</div>
																	<vue-field
																		id="memRemindPrd"
																		v-model="memRemindPrd"
																		as="select"
																		class="form-select"
																		name="memRemindPrd"
																		:rules="memRemindYn === 'Y' ? 'required' : ''"
																		:class="{ 'is-invalid': errors.memRemindPrd }"
																		:label="$t('wob.expiryNotificationSetting')"
																	>
																		<option value="D">
																			{{ $t('wob.day') }}
																		</option>
																		<option value="W">
																			{{ $t('wob.week') }}
																		</option>
																	</vue-field>
																	<span class="input-group-text">{{ $t('wob.notify') }}</span>
																	<div style="height: 3px">
																		<span v-show="errors.memRemindPrd" class="text-danger">{{
																			errors.memRemindPrd
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
													</tbody>
												</table>
											</vue-form>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-white" @click.prevent="props.close()">
								{{ $t('wob.close') }}
							</button>
							<input
								id="btnSave"
								name="btnSave"
								class="btn btn-primary"
								type="button"
								:value="'儲存'"
								@click.prevent="
									doInsertRec();
									props.close();
								"
							>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<vue-cus-reuse-word-modal
			ref="reuseWordModal"
			:wob-reuse-words="wobReuseWords"
			:on-close="reopenMainModal"
		/>
	</div>
</template>

<script>
import { Field, Form } from 'vee-validate';
import moment from 'moment';
import vueModal from '@/views/components/model.vue';
import vueCusReuseWordModal from '@/views/cus/include/reuseWordModal.vue';
import _ from 'lodash';
import { getImgURL } from '@/utils/imgURL.js';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		vueCusReuseWordModal
	},
	props: {
		showSection: {
			// 僅可點選帶入sectionCode的tab，若無帶入則顯示所有tab
			type: Number
		},
		propCusCode: String, // 帶入客戶cusCode
		getCalendarTasks: Function,
		getSelectReuseWord: Function
	},
	data: function () {
		const minutes = [];
		for (let i = 1; i < 60; i++) {
			minutes.push(String(i).padStart(2, '0'));
		}
		return {
			type: this.$route.path.split('/').filter(Boolean)[0].toUpperCase(), // 根據router獲取當前模組
			isShowCusInfo: false,
			isOpenModal: false,
			isOpenReuseWordModal: false,
			sectionCode: 1,
			loginRoleType: null, // 登入者的角色

			// 客戶群組選擇
			groupCode: '', // 客戶群組
			queryCusCode: '', // 客戶
			queryIdn: null, // 客戶ID/統編
			cusCode: null, // 最後選擇的客戶

			selectVisitPurpose: [],
			selectVisitType: [],
			selectMinutes: minutes,
			showReuseWordSetting: false,
			selectReuseWord: [], // {{ $t('wob.commonPhrases') }}選擇
			// Api用參數
			idn: null,

			// {{ $t('wob.personalNotes') }}
			nextRemindDt: null, // {{ $t('wob.workDate') }}
			appoHour: '00', // {{ $t('wob.workTime') }}({{ $t('wob.hour') }})
			appoMinute: '00', // {{ $t('wob.workTime') }}({{ $t('wob.minute') }})
			advNce: 'N', // {{ $t('wob.expiryNotificationSetting') }}
			advNceDay: null, // {{ $t('wob.advance') }}{{ $t('wob.notify') }}天/{{ $t('wob.week') }}數
			advNcePrd: 'D', // {{ $t('wob.advance') }}{{ $t('wob.notify') }}{{ $t('wob.week') }}期
			personalWorkTitle: null, // {{ $t('wob.subject') }}
			content: null, // {{ $t('wob.content') }}

			// 客戶約訪
			visitDate: null, // 約訪{{ $t('wob.day') }}期
			visitHour: '00', // 約訪{{ $t('wob.hour') }}間({{ $t('wob.hour') }})
			visitMin: '00', // 約訪{{ $t('wob.hour') }}間({{ $t('wob.minute') }})
			apptAdvNce: 'N', // {{ $t('wob.expiryNotificationSetting') }}
			apptAdvNceDay: null, // {{ $t('wob.advance') }}{{ $t('wob.notify') }}天/{{ $t('wob.week') }}數
			apptAdvNcePrd: 'D', // {{ $t('wob.advance') }}{{ $t('wob.notify') }}{{ $t('wob.week') }}期
			apptVisitPurCode: '', // 訪談目的
			apptVisitAprCode: '', // 訪談方式
			apptTitle: null, // 訪談{{ $t('wob.subject') }}
			visitContent: null, // 訪談{{ $t('wob.content') }}

			// 聯繫紀錄
			contDate: null, // 聯繫{{ $t('wob.day') }}期
			contHour: '00', // 聯繫{{ $t('wob.hour') }}間({{ $t('wob.hour') }})
			contMin: '00', // 聯繫{{ $t('wob.hour') }}間({{ $t('wob.minute') }})
			contTitle: null, // {{ $t('wob.subject') }}
			contVisitAprCode: '', // 處理方式
			contContent: null, // 處理{{ $t('wob.content') }}
			contStatCode: '', // 聯絡狀況
			contProcCode: '', // 後續處理
			doneYn: 'N', // 是否結案

			// 客戶重要{{ $t('wob.day') }}子
			memoryDate: null, // 重要{{ $t('wob.day') }}子
			memContent: null, // {{ $t('wob.content') }}
			memRemindYn: 'N', // {{ $t('wob.expiryNotificationSetting') }}
			memRemindDays: null, // {{ $t('wob.advance') }}{{ $t('wob.notify') }}天數
			memRemindPrd: 'D', // {{ $t('wob.advance') }}{{ $t('wob.notify') }}{{ $t('wob.week') }}期

			// 常用聯繫{{ $t('wob.content') }}
			reuseWord: null, // {{ $t('wob.commonPhrases') }}選擇
			newReuseWord: null, // {{ $t('wob.add') }}{{ $t('wob.commonPhrases') }}
			wobReuseWords: [], // {{ $t('wob.commonPhrases') }}下拉選單

			// 下拉選單
			selectCusGroup: null, // 客戶群組選單
			selectCusList: [], // 客戶選單

			visitPurMenu: [], // 訪談目的選單
			visitAprMenu: [], // 訪談方式/處理方式選單
			contStatMenu: [], // 聯絡狀況選單
			contProcMenu: [], // 後續處理選單
			// 顯示用參數
			cusInfo: {},

			// 檔案處裡
			tempFile: null,
			files: [],
			maxFileCount: 3,
			validExts: ['doc', 'docx', 'pdf', 'xlsx', 'txt'],
			symbols: ['/', ':', '*', '?', '"', '<', '>', '|'],
			modal: null
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo'])
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal) {
				const self = this;
				if (newVal) {
					self.loginRoleType = newVal.roleType;
				}
			}
		},
		// 選擇客戶群組
		groupCode: function (val) {
			const self = this;
			self.queryCusCode = '';
			self.selectCusList = [];
			self.isShowCusInfo = false;
			if (self.groupCode) {
				self.getSelectCusList(val);
			}
		},
		// 選擇客戶
		queryCusCode: function (val) {
			const self = this;
			self.isShowCusInfo = false;
			if (val) {
				self.getCusInfo();
			}
		},
		// 以下為帶入單一客戶ID{{ $t('wob.hour') }}使用
		showSection: {
			immediate: true,
			handler: function (newVal, oldVal) {
				const self = this;
				if (newVal) {
					self.changeSession(newVal);
				}
			}
		},
		propCusCode: {
			immediate: true,
			handler: function (newVal, oldVal) {
				const self = this;
				if (newVal) {
					self.getCusInfo();
				}
			}
		}
	},
	mounted: function () {
		const self = this;
		self.getSelectCusGroup();
		self.getVisitPurMenu();
		self.getVisitAprMenu();
		self.getContStatMenu();
		self.getContProcMenu();
	},
	methods: {
		getImgURL,
		show: function (sectionCode) {
			if (!this.propCusCode) {
				this.resetVariables();
			}
			if (!this.getSelectReuseWord) {
				this.getSelectReuseWordSelf();
			}

			if (sectionCode) {
				this.sectionCode = sectionCode;
			}

			this.isOpenModal = true;
		},
		// 切換頁籤
		changeSession: function (val) {
			const self = this;
			self.sectionCode = val;
		},
		// 客戶群組下拉選單
		async getSelectCusGroup() {
			const self = this;
			const ret = await self.$api.getCusGroupMenuApi();
			self.selectCusGroup = ret.data;
		},
		// 客戶下拉選單
		getSelectCusList: async function (groupCode) {
			const self = this;
			const ret = await self.$api.postCusGroupDetail({
				groupCode: groupCode
			});
			if (ret.data.groupDetail) {
				self.selectCusList = ret.data.groupDetail;
			}
		},
		// 取得客戶資訊
		getCusInfo: async function () {
			const self = this;

			if (_.isBlank(self.queryCusCode) && _.isBlank(self.queryIdn) && _.isBlank(self.propCusCode)) {
				self.$bi.alert(self.$t('wob.pleaseEnterCustomerIdTaxId'));
				return;
			}

			let queryData = {};
			if (!_.isBlank(self.queryCusCode)) {
				queryData = { cusCode: self.queryCusCode };
				self.queryIdn = null;
			}
			else {
				queryData = { cusCode: self.queryIdn };
				self.queryCusCode = null;
			}

			if (!_.isBlank(self.propCusCode)) {
				queryData.cusCode = self.propCusCode;
			}

			const resp = await self.$api.getCustomer(queryData);

			if (!resp.data.cusCode || _.isEmpty(resp.data)) {
				self.$bi.alert(self.$t('wob.noCustomerFound'));
				return;
			}
			else {
				self.resetVariables('ignoreQueryFields');
				self.cusInfo = resp.data;
				self.isShowCusInfo = true;
				// $('#Section' + self.sectionCode).show();
			}
		},
		// 取得訪談目的選單
		getVisitPurMenu: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'VISIT_PUR_CODE'
			});
			self.visitPurMenu = ret.data;
		},
		// 取得訪談方式選單
		getVisitAprMenu: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'VISIT_APR_CODE'
			});
			self.visitAprMenu = ret.data;
		},
		// 取得聯絡狀況選單
		getContStatMenu: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'CONT_STAT_CODE'
			});
			self.contStatMenu = ret.data;
		},
		// 取得後續處理選單
		getContProcMenu: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: 'CONT_PROC_CODE'
			});
			self.contProcMenu = ret.data;
		},
		// 儲存按鈕
		doInsertRec: async function () {
			const self = this;
			if (self.sectionCode !== 1 && _.isBlank(self.cusInfo.cusCode)) {
				console.log('打印資訊=' + self.cusInfo.cusCode);
				self.$bi.alert(self.$t('wob.pleaseSelectCustomer'));
				return;
			}
			switch (self.sectionCode) {
				case 1: // {{ $t('wob.personalNotes') }}
					self.insertPersonalTask();
					break;
				case 2: // 客戶約訪
					self.insertVisit();
					break;
				case 3: // 聯繫紀錄
					self.insertConnect();
					break;
				case 4: // 客戶重要{{ $t('wob.day') }}子
					self.insertMemory();
					break;
			}
		},
		// {{ $t('wob.add') }}工作項目-{{ $t('wob.personalNotes') }}-儲存
		insertPersonalTask: async function () {
			const self = this;
			await self.$refs.tdPersonalRec.validate().then(async function (pass) {
				if (pass.valid) {
					const content = _.isNil(self.content) ? '' : self.content;
					const data = {
						nextRemindDt: self.nextRemindDt ? moment(self.nextRemindDt).format('YYYY-MM-DD') : null,
						nextRemindTime: self.appoHour + ':' + self.appoMinute,
						advNce: self.advNce,
						advNceDay: self.advNce === 'Y' ? self.advNceDay : null,
						advNcePrd: self.advNce === 'Y' ? self.advNcePrd : null,
						title: self.personalWorkTitle,
						content: content
					};

					const ret = await self.$api.postPersonalTaskApi(data);
					self.$bi.alert(self.$t('wob.addSuccess'));
					self.isOpenModal = +false;
					if (self.getCalendarTasks) {
						self.getCalendarTasks();
					}
				}
			});
		},
		// {{ $t('wob.add') }}工作項目-客戶約訪-儲存
		insertVisit: async function () {
			const self = this;

			await self.$refs.appointmentTask.validate().then(async function (pass) {
				if (pass.valid) {
					const content = _.isNil(self.visitContent) ? '' : self.visitContent;

					const data = {
						cusCode: self.cusInfo.cusCode,
						nextRemindDt: self.visitDate ? moment(self.visitDate).format('YYYY-MM-DD') : null,
						nextRemindTime: self.visitHour + ':' + self.visitMin,
						advNce: self.apptAdvNce,
						advNceDay: self.apptAdvNce === 'Y' ? self.apptAdvNceDay : null,
						advNcePrd: self.apptAdvNce === 'Y' ? self.apptAdvNcePrd : null,
						visitAprCode: self.apptVisitAprCode,
						visitPurCode: self.apptVisitPurCode,
						title: self.apptTitle,
						content: content
					};
					const ret = await self.$api.postVisit(data);
					self.$bi.alert(self.$t('wob.addSuccess'));
					self.isOpenModal = false;
					if (self.getCalendarTasks) {
						self.getCalendarTasks();
					}
				}
			});
		},
		// {{ $t('wob.add') }}工作項目-聯絡紀錄-儲存
		insertConnect: async function () {
			const self = this;
			await self.$refs.tdConnTask.validate().then(async function (pass) {
				if (pass.valid) {
					const content = _.isNil(self.contContent) ? '' : self.contContent;
					const ret = await self.$api.postConnect({
						cusCode: self.cusInfo.cusCode,
						nextRemindDt: self.contDate ? moment(self.contDate).format('YYYY-MM-DD') : null,
						nextRemindTime: self.contHour + ':' + self.contMin,
						title: self.contTitle,
						visitAprCode: self.contVisitAprCode,
						content: content,
						contStatCode: self.contStatCode,
						contProcCode: self.contProcCode,
						doneYn: self.doneYn
					});
					self.$bi.alert(self.$t('wob.addSuccess'));
					self.isOpenModal = false;
					if (self.getCalendarTasks) {
						self.getCalendarTasks();
					}
				}
			});
		},
		// {{ $t('wob.add') }}工作項目-客戶重要{{ $t('wob.day') }}子-儲存
		insertMemory: async function () {
			const self = this;
			self.$refs.memoryDateTask.validate().then(async function (pass) {
				if (pass.valid) {
					const content = _.isNil(self.memContent) ? '' : self.memContent;

					const ret = await self.$api.postMemoryDateApi({
						cusCode: self.cusInfo.cusCode,
						dateDt: self.memoryDate ? moment(self.memoryDate).format('YYYY-MM-DD') : null,
						note: content,
						remindYn: self.memRemindYn,
						remindDays: self.memRemindYn === 'Y' ? `${self.memRemindDays}` : null,
						remindPrd: self.memRemindYn === 'Y' ? self.memRemindPrd : null
					});
					self.$bi.alert(self.$t('wob.addSuccess'));
					self.isOpenModal = false;
					if (self.getCalendarTasks) {
						self.getCalendarTasks();
					}
				}
			});
		},
		// {{ $t('wob.commonPhrases') }}{{ $t('wob.setting') }}-儲存
		setDefaultReuseWords: function () {
			const self = this;
			self.wobReuseWords = [];
			for (let i = 0; i < 10; i++) {
				const tempWordObj = {
					wordsId: i + 1,
					words: null
				};
				self.wobReuseWords.push(tempWordObj);
			}
		},
		// 取得{{ $t('wob.commonPhrases') }}選單
		getReuseWords: async function () {
			const self = this;
			const type = self.$route.path.split('/').filter(Boolean)[0].toUpperCase();
			const ret = await self.$api.getReuseWordsApi({ type });
			self.wobReuseWords = ret.data || [];
		},
		// {{ $t('wob.add') }}{{ $t('wob.commonPhrases') }}
		insertReuseWord: async function (index) {
			const self = this;
			if (self.newReuseWord) {
				const type = self.$route.path.split('/').filter(Boolean)[0].toUpperCase();
				const ret = await self.$api.postReuseWordsApi({
					type,
					words: self.newReuseWord
				});
				if (ret && ret.status === 200) {
					this.$swal.fire({
						title: '新增成功',
						text: `常用句已新增`,
						icon: 'success',
						confirmButtonText: '確認'
					});
				}
				self.newReuseWord = null;
				if (self.getSelectReuseWord) {
					self.getSelectReuseWord();
				}
				else {
					self.getSelectReuseWordSelf();
				}
			}
		},
		// {{ $t('wob.commonPhrases') }}{{ $t('wob.join') }}{{ $t('wob.content') }}
		appendReuseWord: function (targetName) {
			const self = this;

			if (self.reuseWord != null) {
				switch (targetName) {
					case 'content': // {{ $t('wob.personalNotes') }}-{{ $t('wob.content') }}
						if (!self.content) {
							self.content = '';
						}
						self.content = self.content + self.reuseWord;
						break;
					case 'visitContent': // 客戶約訪-訪談{{ $t('wob.content') }}
						if (!self.visitContent) {
							self.visitContent = '';
						}
						self.visitContent = self.visitContent + self.reuseWord;
						break;
				}
			}
		},
		// {{ $t('wob.close') }} model 彈窗 (reset variables)
		doClose: function () {
			const self = this;
			self.reuseWord = null;
			self.newReuseWord = null;
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		handleWordSettingClick: function (closeMainModal) {
			const self = this;
			// 關閉新增工作項目彈窗
			closeMainModal();
			self.$refs.reuseWordModal.openModal();
			// 確保當前modal完全關閉後再打開原modal
			setTimeout(() => {
				self.$refs.reuseWordModal.openModal();
			}, 300);
		},

		// 重新打開modal（設定完常用句時）
		reopenMainModal: function () {
			const self = this;
			self.isOpenModal = true;

			// 更新常用句
			if (self.getSelectReuseWord) {
				self.getSelectReuseWord();
			}
			else {
				self.getSelectReuseWordSelf();
			}
		},
		// 重置變數
		resetVariables: function (ignoreType) {
			const self = this;

			if (ignoreType != 'ignoreQueryFields') {
				self.queryCusCode = '';
				self.queryIdn = null;
				self.groupCode = '';
			}

			self.cusInfo = {};

			self.showReuseWordSetting = false;

			// Api用參數
			self.idn = null;
			// 個人工作
			self.nextRemindDt = null;
			self.appoHour = '00';
			self.appoMinute = '00';
			self.personalWorkTitle = null;
			self.advNce = 'N';
			self.advNceDay = null;
			self.advNcePrd = 'D';
			self.content = null;

			// 約訪記錄
			self.visitDate = null;
			self.visitHour = '00';
			self.visitMin = '00';
			self.apptVisitPurCode = '';
			self.apptVisitAprCode = '';
			self.apptTitle = null;
			self.visitContent = '';
			self.apptAdvNce = 'N';
			self.apptAdvNceDay = null;
			self.apptAdvNcePrd = 'D';
			self.visitContent = null;

			// 聯繫紀錄
			self.contTitle = null;
			self.contVisitAprCode = '';
			self.contDate = null;
			self.contHour = '00';
			self.contMin = '00';
			self.contContent = null;
			self.contStatCode = '';
			self.contProcCode = '';
			self.doneYn = 'N';

			// 顧客重要{{ $t('wob.day') }}子
			self.memoryDate = null;
			self.memRemindYn = 'N';
			self.memRemindDays = 1;
			self.memRemindPrd = 'D';
			self.memContent = null;
			// 檔案處裡
			self.tempFile = null;
			self.files = [];
		},
		// 顯示常用聯繫紀錄{{ $t('wob.setting') }}頁面
		setShowReuseWordSetting: function () {
			const self = this;
			self.showReuseWordSetting = !self.showReuseWordSetting;
		},
		generatorId: function (name) {
			return name + '-' + _.now() + _.random(0, 99);
		},
		// 常用聯繫{{ $t('wob.content') }}{{ $t('wob.setting') }}
		getSelectReuseWordSelf: async function () {
			const self = this;
			const type = self.$route.path.split('/').filter(Boolean)[0].toUpperCase();
			const ret = await self.$api.getReuseWordsApi({ type });
			const result = ret.data;
			const needAppend = 5 - result.length;
			if (result.length < 5) {
				for (let i = 0; i < needAppend; i++) {
					result.push({ words: '', wordsId: ret.data.length + i + 1 });
				}
			}
			self.selectReuseWord = result;
			self.wobReuseWords = result;
		}
	}
};
</script>

<style scoped>
.width100 {
	width: 100%;
}
</style>
