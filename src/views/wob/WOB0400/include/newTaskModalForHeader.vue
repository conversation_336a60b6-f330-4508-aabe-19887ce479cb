<template>
	<div v-bind="$attrs">
		<!-- newTaskModal Add Task -->
		<vue-modal :is-open="isOpenModal" @close="closeModal">
			<template #content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								{{ $t('wob.newTask') }}
							</h4>
							<button type="button" class="btn-expand">
								<i class="bi bi-arrows-fullscreen" />
							</button>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="props.close()"
							/>
						</div>
						<div class="modal-body bg-white p-3">
							<div class="tab-content">
								<div class="card card-form mb-3">
									<div class="card-body">
										<div class="row g-3 align-items-end">
											<div class="col-lg-12">
												<label class="form-label tx-require">{{ $t('wob.appointmentCustomer') }}</label>
												<div class="input-group">
													<div class="input-group-text">
														{{ $t('wob.customerGroup') }}
													</div>
													<select v-model="groupSeq" name="groupSeq" class="form-select">
														<option selected disabled value="">
															{{ $t('wob.pleaseSelect') }}
														</option>
														<option v-for="apptCusGroup in selectCusGroup" :value="apptCusGroup.groupSeq">
															{{ apptCusGroup.groupName }}
														</option>
													</select>
													<div class="input-group-text">
														{{ $t('wob.customer') }}
													</div>
													<select v-model="queryCusCode" class="form-select">
														<option selected disabled value="">
															{{ $t('wob.pleaseSelect') }}
														</option>
														<option v-for="apptGroupCustomer in selectCusList" :value="apptGroupCustomer.cusCode">
															{{ apptGroupCustomer.cusName }} &nbsp; {{ apptGroupCustomer.idn }}
														</option>
													</select>
													<div class="input-group-text">
														{{ $t('wob.customerIdTaxId') }}
													</div>
													<input v-model="queryIdn" class="form-control" type="text">
													<button class="btn btn-primary btn-glow" type="button" @click="getCusInfo()">
														{{ $t('wob.search') }}
													</button>
												</div>
											</div>
										</div>
									</div>
								</div>
								<template v-if="wobSimpleCus.cusCode">
									<div id="showCustomerNaturalInfo2">
										<div class="card card-prointro card-clientCard mb-3">
											<div class="row align-items-center">
												<div class="col-lg-1 text-center">
													<div class="avatar avatar-male" />
													<h5>{{ wobSimpleCus.cusName || '--' }}</h5>
												</div>
												<div class="col-lg-11">
													<div class="tx-title tx-16-f mt-2">
														{{ $t('wob.identity') }}：{{ wobSimpleCus.identity || '--' }} / {{ $t('wob.age') }}
														：{{ wobSimpleCus.age || '--' }}{{ $t('wob.years') }} / AUM ：{{ $filters.formatAmt(wobSimpleCus.invAmtAua) }} /
														{{ $t('wob.recentContactDate') }} ：{{ $filters.formatDateTime(wobSimpleCus.lastContactDt) }}
													</div>
													<ul class="profile-info-list">
														<li>
															<span class="info-list"><i class="bi bi-clipboard-data" /></span>
															{{ $t('wob.ipsAttribute') }}：{{ wobSimpleCus.ipsName || '--' }}{{ wobSimpleCus.ipsCode ? `(${wobSimpleCus.ipsCode})` : '' }}
														</li>
														<li>
															<span class="info-list"><i class="bi bi-gift" /></span>
															{{ $t('wob.birthday') }}：{{ $filters.formatDate(wobSimpleCus.birth) }}
														</li>
														<li>
															<span class="info-list"><i class="bi bi-envelope" /></span>
															{{ $t('wob.email') }}：{{ wobSimpleCus.email || '--' }}
														</li>
														<li>
															<span class="info-list"><i class="bi bi-house-door" /></span>
															{{ $t('wob.contactPhoneHome') }}：{{ wobSimpleCus.homePhone }}
														</li>
														<li>
															<span class="info-list"><i class="bi bi-building" /></span>
															{{ $t('wob.contactPhoneOffice') }}：{{ wobSimpleCus.companyPhone }}
														</li>
														<li>
															<span class="info-list"><i class="bi bi-telephone" /></span>
															{{ $t('wob.contactPhoneMobile') }}：{{ wobSimpleCus.mobilePhone }}
														</li>
													</ul>
												</div>
											</div>
										</div>

										<div class="tab-nav-main">
											<Tabs
												v-model="sectionCode"
												variant="pill"
												:tabs="[
													{code: 1, name: $t('wob.contactRecord')},
													{code: 2, name: $t('wob.appointmentSchedule')}
												]"
											/>
										</div>
									</div>
									<!-- Appointment Event -->
									<div v-if="sectionCode == 2" class="tab-pane fade show active">
										<div v-if="wobSimpleCus.cusCode" class="card card-form shadow-none">
											<div class="card-header">
												<h4>{{ $t('wob.appointmentSchedule') }}</h4>
												<span class="tx-square-bracket">{{ $t('wob.requiredField') }}</span>
											</div>
											<div class="card-body">
												<vue-form v-slot="{ errors }" ref="appointmentTaskWatch">
													<table class="table table-RWD table-borderless">
														<tbody>
															<tr>
																<td><label class="form-label tx-require">{{ $t('wob.type') }}</label></td>
																<td>
																	<vue-field
																		v-model="apptVisitAprCode"
																		as="select"
																		class="form-select"
																		name="apptVisitAprCode"
																		:class="{ 'is-invalid': errors.apptVisitAprCode }"
																		rules="required"
																		:label="$t('wob.type')"
																	>
																		<option disabled selected value="">
																			{{ $t('wob.pleaseSelect') }}
																		</option>
																		<option v-for="visitType in selectVisitType" :value="visitType.codeValue">
																			{{ visitType.codeName }}
																		</option>
																	</vue-field>
																	<div style="height: 3px">
																		<span v-show="errors.apptVisitAprCode" class="text-danger">{{
																			errors.apptVisitAprCode }}</span>
																	</div>
																</td>
																<td><label class="form-label tx-require">{{ $t('wob.accompanyPurpose') }}</label></td>
																<td>
																	<vue-field
																		v-model="apptVisitPurCode"
																		as="select"
																		class="form-select"
																		name="apptVisitPurCode"
																		:class="{ 'is-invalid': errors.apptVisitPurCode }"
																		rules="required"
																		:label="$t('wob.accompanyPurpose')"
																	>
																		<option disabled selected value="">
																			{{ $t('wob.pleaseSelect') }}
																		</option>
																		<option v-for="visitPurpose in selectVisitPurpose" :value="visitPurpose.codeValue">
																			{{ visitPurpose.codeName }}
																		</option>
																	</vue-field>
																	<div style="height: 3px">
																		<span v-show="errors.apptVisitPurCode" class="text-danger">{{
																			errors.apptVisitPurCode }}</span>
																	</div>
																</td>
															</tr>
															<tr>
																<td><label class="form-label tx-require">{{ $t('wob.appointmentSubject') }}</label></td>
																<td colspan="3">
																	<div class="input-group">
																		<vue-field
																			v-model="apptTitle"
																			class="form-control"
																			name="apptTitle"
																			type="text"
																			size="30"
																			value="apptTitle"
																			:class="{ 'is-invalid': errors.apptTitle }"
																			:label="$t('wob.appointmentSubject')"
																			rules="required"
																		/>
																		<div style="height: 3px">
																			<span v-show="errors.apptTitle" class="text-danger">{{ errors.apptTitle }}</span>
																		</div>
																		<div class="form-check form-check-inline">
																			<vue-field
																				v-model="isWholeDay"
																				class="form-check-input"
																				name="apptAdvNce"
																				type="checkbox"
																				value="isWholeDay"
																				:label="$t('wob.allDayItem')"
																			/>
																			<label class="form-check-label">{{ $t('wob.allDayItem') }}</label>
																		</div>
																	</div>
																</td>
															</tr>
															<tr>
																<td>
																	<label class="form-label tx-require">{{ $t('wob.executeDateFrom') }}</label>
																</td>
																<td>
																	<div class="input-group">
																		<div class="col-auto">
																			<vue-field
																				v-model="visitDate"
																				type="date"
																				name="visitDate"
																				class="form-control"
																				:class="{ 'is-invalid': errors.visitDate }"
																				:label="$t('wob.executeDateFrom')"
																				rules="required"
																			/>
																		</div>
																		<div>
																			<span v-show="errors.visitDate" class="text-danger">{{ errors.visitDate }}</span>
																		</div>
																		<div class="col-auto">
																			<vue-field
																				v-model="visitHour"
																				as="select"
																				class="form-select"
																				name="visitHour"
																				:class="{ 'is-invalid': errors.visitHour }"
																				:rules="isWholeDay ? '' : 'required'"
																				:label="$t('wob.appointmentTime')"
																			>
																				<option value="00">
																					00
																				</option>
																				<option value="01">
																					01
																				</option>
																				<option value="02">
																					02
																				</option>
																				<option value="03">
																					03
																				</option>
																				<option value="04">
																					04
																				</option>
																				<option value="05">
																					05
																				</option>
																				<option value="06">
																					06
																				</option>
																				<option value="07">
																					07
																				</option>
																				<option value="08">
																					08
																				</option>
																				<option value="09">
																					09
																				</option>
																				<option value="10">
																					10
																				</option>
																				<option value="11">
																					11
																				</option>
																				<option value="12">
																					12
																				</option>
																				<option value="13">
																					13
																				</option>
																				<option value="14">
																					14
																				</option>
																				<option value="15">
																					15
																				</option>
																				<option value="16">
																					16
																				</option>
																				<option value="17">
																					17
																				</option>
																				<option value="18">
																					18
																				</option>
																				<option value="19">
																					19
																				</option>
																				<option value="20">
																					20
																				</option>
																				<option value="21">
																					21
																				</option>
																				<option value="22">
																					22
																				</option>
																				<option value="23">
																					23
																				</option>
																			</vue-field>
																		</div>
																		<span class="input-group-text">{{ $t('wob.hour') }}</span>
																		<div>
																			<span v-show="errors.visitHour" class="text-danger">{{ errors.visitHour }}</span>
																		</div>
																		<div class="col-auto">
																			<vue-field
																				v-model="visitMin"
																				as="select"
																				class="form-select"
																				name="visitMin"
																				:class="{ 'is-invalid': errors.visitMin }"
																				:rules="isWholeDay ? '' : 'required'"
																				:label="$t('wob.appointmentTime')"
																			>
																				<option selected="selected" value="00">
																					00
																				</option>
																				<option v-for="minute in selectMinutes" :value="minute">
																					{{ minute }}
																				</option>
																			</vue-field>
																		</div>
																		<span class="input-group-text">{{ $t('wob.minute') }}</span>
																		<div>
																			<span v-show="errors.visitMin" class="text-danger">{{ errors.visitMin }}</span>
																		</div>
																	</div>
																</td>
																<td><label class="form-label tx-require">{{ $t('wob.executeDateTo') }}</label></td>
																<td>
																	<div class="input-group">
																		<div class="col-auto">
																			<vue-field
																				v-model="visitDateE"
																				type="date"
																				name="visitDateE"
																				class="form-control"
																				:class="{ 'is-invalid': errors.visitDateE }"
																				:label="$t('wob.executeDateTo')"
																				rules="required"
																			/>
																		</div>
																		<div style="height: 3px">
																			<span v-show="errors.visitDateE" class="text-danger">{{ errors.visitDateE
																			}}</span>
																		</div>
																		<div class="col-auto">
																			<vue-field
																				v-model="visitHourE"
																				as="select"
																				class="form-select"
																				name="visitHourE"
																				:class="{ 'is-invalid': errors.visitHourE }"
																				:rules="isWholeDay ? '' : 'required'"
																				:label="$t('wob.appointmentTime')"
																			>
																				<option value="00">
																					00
																				</option>
																				<option value="01">
																					01
																				</option>
																				<option value="02">
																					02
																				</option>
																				<option value="03">
																					03
																				</option>
																				<option value="04">
																					04
																				</option>
																				<option value="05">
																					05
																				</option>
																				<option value="06">
																					06
																				</option>
																				<option value="07">
																					07
																				</option>
																				<option value="08">
																					08
																				</option>
																				<option value="09">
																					09
																				</option>
																				<option value="10">
																					10
																				</option>
																				<option value="11">
																					11
																				</option>
																				<option value="12">
																					12
																				</option>
																				<option value="13">
																					13
																				</option>
																				<option value="14">
																					14
																				</option>
																				<option value="15">
																					15
																				</option>
																				<option value="16">
																					16
																				</option>
																				<option value="17">
																					17
																				</option>
																				<option value="18">
																					18
																				</option>
																				<option value="19">
																					19
																				</option>
																				<option value="20">
																					20
																				</option>
																				<option value="21">
																					21
																				</option>
																				<option value="22">
																					22
																				</option>
																				<option value="23">
																					23
																				</option>
																			</vue-field>
																		</div>
																		<span class="input-group-text">{{ $t('wob.hour') }}</span>
																		<div style="height: 3px">
																			<span v-show="errors.visitHourE" class="text-danger">{{ errors.visitHourE
																			}}</span>
																		</div>
																		<div class="col-auto">
																			<vue-field
																				id="visitMinE"
																				v-model="visitMinE"
																				as="select"
																				class="form-select"
																				name="visitMinE"
																				:class="{ 'is-invalid': errors.visitMinE }"
																				:rules="isWholeDay ? '' : 'required'"
																				:label="$t('wob.appointmentTime')"
																			>
																				<option selected="selected" value="00">
																					00
																				</option>
																				<option v-for="minute in selectMinutes" :value="minute">
																					{{ minute }}
																				</option>
																			</vue-field>
																		</div>
																		<span class="input-group-text">{{ $t('wob.minute') }}</span>
																		<div style="height: 3px">
																			<span v-show="errors.visitMinE" class="text-danger">{{ errors.visitMinE }}</span>
																		</div>
																	</div>
																</td>
															</tr>
															<tr>
																<td><label class="form-label tx-require">{{ $t('wob.location') }}</label></td>
																<td colspan="3">
																	<div v-for="addr in selectPlaceType" class="form-check form-check-inline">
																		<vue-field
																			:id="'PT' + addr.codeValue"
																			v-model="apptPlaceType"
																			class="form-check-input"
																			name="apptPlaceType"
																			type="radio"
																			:value="addr.codeValue"
																			:class="{ 'is-invalid': errors.apptPlaceType }"
																			:label="$t('wob.location')"
																			rules="required"
																		/>
																		<label class="form-check-label" :for="'PT' + addr.codeValue">{{ addr.codeName
																		}}</label>
																	</div>
																	<vue-field
																		v-model="apptAddress"
																		class="form-control"
																		name="apptAddress"
																		type="text"
																		size="30"
																		value="apptAddress"
																		:class="{ 'is-invalid': errors.apptAddress }"
																		:label="$t('wob.location')"
																		rules="required"
																	/>
																	<div style="height: 3px">
																		<span v-show="errors.apptAddress" class="text-danger">{{ errors.apptAddress
																		}}</span>
																	</div>
																</td>
															</tr>
															<tr>
																<td><label class="form-label">{{ $t('wob.description') }}</label></td>
																<td colspan="3">
																	<vue-field
																		v-model="visitContent"
																		as="textarea"
																		class="form-control"
																		name="visitContent"
																		rows="5"
																		cols="50"
																		size="400"
																		:label="$t('wob.description')"
																	>
																		<textarea />
																	</vue-field>
																</td>
															</tr>
															<tr>
																<td><label class="form-label">{{ $t('gen.attachment') }}</label></td>
																<td colspan="3">
																	<vue-form v-slot="{ errors }" ref="fileForm" class="col-lg-12">
																		<div class="row g-2">
																			<div class="input-group">
																				<vue-field
																					ref="uploadFile"
																					name="uploadFile"
																					type="file"
																					class="form-control"
																					:label="$t('gen.attachment')"
																					:class="{ 'is-invalid': errors.uploadFile }"
																					:rules="{ required_file: true, mbSize: 2, ext: validExts, validateName: symbols }"
																					@change="handleChange($event)"
																				/>
																				<Button
																					name="'tempFile"
																					:disabled="!fileTemp"
																					@click="addFile"
																				>
																					{{ $t('wob.join') }}
																				</Button>
																			</div>
																		</div>
																		<ul class="list-group list-inline-tags mt-2">
																			<li v-for="file in files" class="list-group-item">
																				<ColoredLink @click.prevent="previewFile(file.fileNo)">
																					<span>{{ file.showName }}</span>
																					<span
																						class="img-delete"
																						data-bs-toggle="tooltip"
																						:title="$t('wob.delete')"
																						@click.prevent.stop="deleteFile(file.fileNo)"
																					/>
																				</ColoredLink>
																			</li>
																		</ul>
																		<div v-show="errors.uploadFile" class="text-danger">
																			{{ $filters.defaultValue(errors.uploadFile, '--') }}
																		</div>
																		<div class="tx-note-title">
																			{{ $t('wob.maxUploadAttachmentNote') }}
																		</div>
																	</vue-form>
																</td>
															</tr>
															<tr>
																<td><label class="form-label">{{ $t('wob.accompanist') }}</label></td>
																<td colspan="3">
																	<div class="input-group">
																		<div class="input-group-text">
																			{{ $t('wob.unit') }}
																		</div>
																		<select v-model="attBranch" name="attBranch" class="form-select">
																			<option selected disabled value="">
																				{{ $t('wob.pleaseSelect') }}
																			</option>
																			<option v-for="bran in selectAttBranches" :value="bran.branCode">
																				{{ bran.branName }}
																			</option>
																		</select>
																		<div class="input-group-text">
																			{{ $t('wob.personnel') }}
																		</div>
																		<select v-model="attUser" class="form-select">
																			<option selected disabled value="">
																				{{ $t('wob.pleaseSelect') }}
																			</option>
																			<option v-for="user in selectUserList" :value="user.userCode">
																				{{ user.userCode }}
																				{{ user.userName }}
																			</option>
																		</select>
																		<Button
																			:disabled="!attUser"
																			@click="addAttUser(attUser)"
																		>
																			{{ $t('wob.join') }}
																		</Button>
																	</div>
																	<div class="input-group mt-2">
																		<div class="input-group-text" @click.prevent="attList = []">
																			{{ $t('wob.addSpecificStaff') }}
																		</div>
																		<input v-model="orderAttUser" class="form-control" type="text">
																		<Button
																			:disabled="!orderAttUser"
																			@click="addOrderAttUser(orderAttUser)"
																		>
																			{{ $t('wob.join') }}
																		</Button>
																	</div>
																</td>
															</tr>
														</tbody>
													</table>
													<div class="row g-3 align-items-end">
														<div class="col-lg-12">
															<button class="btn btn-white" type="button" @click="setShowAttList()">
																{{ $t('wob.jointAppointmentList') }}({{ attList.length }})
																<i
																	class="bi"
																	:class="{ 'bi-caret-up-fill': showAttList, 'bi-caret-down-fill': !showAttList }"
																/>
															</button>
															<table v-show="showAttList" class="table table-bordered mt-4">
																<thead>
																	<tr>
																		<th>{{ $t('wob.jointAppointmentTarget') }}</th>
																		<th>{{ $t('wob.willAttend') }}</th>
																		<th>{{ $t('wob.remove') }}</th>
																	</tr>
																</thead>
																<tbody>
																	<tr v-for="(user) in attList">
																		<td>{{ user.userCode }} {{ user.userName }}</td>
																		<td />
																		<td style="text-align: center;">
																			<button
																				type="button"
																				class="btn btn-danger btn-icon"
																				@click.prevent="removeAttUser(user.userCode)"
																			>
																				<i class="bi bi-trash" />
																			</button>
																		</td>
																	</tr>
																</tbody>
															</table>
														</div>
													</div>
												</vue-form>
											</div>
										</div>
									</div>

									<!-- {{ $t('wob.contactRecord') }} -->
									<div v-if="sectionCode == 1" class="tab-pane fade show active">
										<div class="card card-form mb-3">
											<div class="card-header">
												<h4>{{ $t('wob.contactRecord') }}</h4>
											</div>
											<div class="card-body">
												<vue-form v-slot="{ errors }" ref="contTaskWatch">
													<div class="form-row">
														<div class="form-group col-12 col-lg-6">
															<label class="form-label">{{ $t('wob.contactMethod') }}</label>
															<div class="width100">
																<vue-field
																	id="contVisitAprCode"
																	v-model="contVisitAprCode"
																	as="select"
																	class="form-select"
																	name="contVisitAprCode"
																	:class="{ 'is-invalid': errors.contVisitAprCode }"
																	rules="required"
																	:label="$t('wob.contactMethod')"
																>
																	<option disabled selected value="">
																		{{ $t('wob.pleaseSelect') }}
																	</option>
																	<option v-for="visitType in selectContactType" :value="visitType.codeValue">
																		{{ visitType.codeName }}
																	</option>
																</vue-field>
																<div>
																	<span v-show="errors.contVisitAprCode" class="text-danger">{{ errors.contVisitAprCode
																	}}</span>
																</div>
															</div>
														</div>
														<div class="form-group col-12 col-lg-6">
															<label class="form-label">{{ $t('wob.contactTime') }}</label>
															<div class="input-group">
																<div class="">
																	<vue-field
																		v-model="contDate"
																		type="date"
																		name="contDate"
																		class="form-control"
																		:class="{ 'is-invalid': errors.contDate }"
																		:label="$t('wob.date')"
																		rules="required"
																	/>
																	<div style="height: 3px">
																		<span v-show="errors.contDate" class="text-danger">{{ errors.contDate }}</span>
																	</div>
																</div>
																<div class="">
																	<vue-field
																		id="contHour"
																		v-model="contHour"
																		as="select"
																		class="form-select"
																		name="contHour"
																		:class="{ 'is-invalid': errors.contHour }"
																		rules="required"
																		:label="$t('wob.hour')"
																	>
																		<option value="00">
																			00
																		</option>
																		<option value="01">
																			01
																		</option>
																		<option value="02">
																			02
																		</option>
																		<option value="03">
																			03
																		</option>
																		<option value="04">
																			04
																		</option>
																		<option value="05">
																			05
																		</option>
																		<option value="06">
																			06
																		</option>
																		<option value="07">
																			07
																		</option>
																		<option value="08">
																			08
																		</option>
																		<option value="09">
																			09
																		</option>
																		<option value="10">
																			10
																		</option>
																		<option value="11">
																			11
																		</option>
																		<option value="12">
																			12
																		</option>
																		<option value="13">
																			13
																		</option>
																		<option value="14">
																			14
																		</option>
																		<option value="15">
																			15
																		</option>
																		<option value="16">
																			16
																		</option>
																		<option value="17">
																			17
																		</option>
																		<option value="18">
																			18
																		</option>
																		<option value="19">
																			19
																		</option>
																		<option value="20">
																			20
																		</option>
																		<option value="21">
																			21
																		</option>
																		<option value="22">
																			22
																		</option>
																		<option value="23">
																			23
																		</option>
																	</vue-field>
																	<div style="height: 3px">
																		<span v-show="errors.contHour" class="text-danger">{{ errors.contHour }}</span>
																	</div>
																</div>
																<span class="input-group-text">{{ $t('wob.hour') }}</span>
																<div class="">
																	<vue-field
																		id="contMin"
																		v-model="contMin"
																		as="select"
																		class="form-select"
																		name="contMin"
																		:class="{ 'is-invalid': errors.contMin }"
																		rules="required"
																		:label="$t('wob.minute')"
																	>
																		<option selected="selected" value="00">
																			00
																		</option>
																		<option v-for="minute in selectMinutes" :value="minute">
																			{{ minute }}
																		</option>
																	</vue-field>
																	<div style="height: 3px">
																		<span v-show="errors.contMin" class="text-danger">{{ errors.contMin }}</span>
																	</div>
																</div>
																<span class="input-group-text">{{ $t('wob.minute') }}</span>
															</div>
														</div>
														<div class="form-group col-12 col-lg-6">
															<label class="form-label">{{ $t('wob.contactSubject') }}</label>
															<div class="width100">
																<vue-field
																	id="contVisitPurCode"
																	v-model="contVisitPurCode"
																	as="select"
																	class="form-select"
																	name="contVisitPurCode"
																	:class="{ 'is-invalid': errors.contVisitPurCode }"
																	rules="required"
																	:label="$t('wob.contactSubject')"
																>
																	<option disabled selected value="">
																		{{ $t('wob.pleaseSelect') }}
																	</option>
																	<option v-for="contPurpose in selectContactPurpose" :value="contPurpose.codeValue">
																		{{ contPurpose.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span v-show="errors.contVisitPurCode" class="text-danger">{{ errors.contVisitPurCode
																	}}</span>
																</div>
															</div>
														</div>
														<div class="form-group col-12 col-lg-6">
															<label class="form-label">{{ $t('wob.contactContent') }}</label>
															<div class="w-100">
																<div class="input-group">
																	<select v-model="reuseWord" name="reuseWord" class="form-select">
																		<option selected disabled value="">
																			{{ $t('wob.pleaseSelect') }}
																		</option>
																		<option
																			v-for="reuseWord in selectReuseWord.filter(item => item.words)"
																			:value="reuseWord.words"
																		>
																			{{ reuseWord.words }}
																		</option>
																	</select>
																	<button
																		class="btn btn-primary btn-glow"
																		type="button"
																		@click="appendReuseWord()"
																	>
																		{{ $t('wob.join') }}
																	</button>
																</div>
																<vue-field
																	v-model="contContent"
																	as="textarea"
																	class="form-control"
																	name="contContent"
																	rows="5"
																	cols="50"
																	size="400"
																	:label="$t('wob.contactContent')"
																/>
															</div>
														</div>
													</div>
													<div class="text-end">
														<button
															class="btn btn-white"
															type="button"
															@click="setShowReuseWordSetting()"
														>
															{{ $t('wob.commonContactContent') }}{{ $t('wob.commonPhrasesManagement') }}
														</button>
													</div>

													<div class="col-lg-12 mt-0">
														<table v-show="showReuseWordSetting" class="table table-bordered">
															<thead>
																<tr>
																	<th>{{ $t('wob.serialNumber') }}</th>
																	<th>{{ $t('wob.commonContactContent') }}</th>
																	<th>{{ $t('wob.execute') }}</th>
																</tr>
															</thead>
															<tbody>
																<tr v-for="(item, index) in wobReuseWords" :key="index">
																	<td class="text-center">
																		{{ index + 1 }}
																	</td>
																	<td>
																		<input v-model="item.words" type="text" class="form-control">
																	</td>
																	<td class="text-center">
																		<button
																			class="btn btn-primary btn-glow"
																			type="button"
																			@click="insertReuseWord(index)"
																		>
																			{{ $t('wob.save') }}
																		</button>
																	</td>
																</tr>
															</tbody>
														</table>
													</div>
												</vue-form>
											</div>
										</div>
									</div>
								</template>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-white" @click.prevent="props.close()">
								{{ $t('wob.close') }}
							</button>
							<input
								v-show="wobSimpleCus.cusCode != null"
								name="btnSave"
								class="btn btn-primary"
								type="button"
								:value="$t('wob.save')"
								@click="doInsertRec()"
							>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
	</div>
	<vue-reuse-word-modal
		:id="'newTaskReuseWordModal'"
		:wob-reuse-words="wobReuseWords"
		:super-modal-name="'newTaskModal'"
	/>
</template>
<script>
import _ from 'lodash';
import vueModal from '@/views/components/model.vue';
import { Field, Form } from 'vee-validate';
import vueReuseWordModal from '@/views/cus/include/reuseWordModal.vue';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		vueReuseWordModal
	},
	props: {
		getCalendarTasks: Function,
		getSelectReuseWord: Function
	},
	data: function () {
		const minutes = [];
		for (let i = 1; i < 60; i++) {
			minutes.push(String(i).padStart(2, '0'));
		}
		return {
			isOpenModal: false,
			sectionCode: 1,
			queryCusCode: '',
			queryIdn: null,
			cusCode: null,
			groupSeq: '',
			loginRoleType: null,
			selectIdentity: [],
			selectVisitPurpose: [],
			selectVisitType: [],
			selectMinutes: minutes,
			selectPlaceType: [],
			selectAttBranches: [],
			selectUserList: [],
			showAttList: false,
			showReuseWordSetting: false,
			selectContactPurpose: [],
			selectContactType: [],
			selectReuseWord: [],
			// API parameters
			isWholeDay: false,
			idn: null,
			// Personal work
			nextRemindDt: null,
			appoHour: null,
			appoMinute: null,
			personalWorkTitle: null,
			advNce: 'N',
			advNceDay: null,
			advNcePrd: null,
			content: null,
			// Appointment record
			visitDate: null,
			visitHour: null,
			visitMin: null,
			visitDateE: null,
			visitHourE: null,
			visitMinE: null,
			apptVisitPurCode: '',
			apptVisitAprCode: '',
			apptTitle: null,
			apptPlaceType: null,
			apptAddress: '',
			attBranch: null,
			attUser: null,
			orderAttUser: null,
			attList: [],
			apptAdvNce: 'N',
			apptAdvNceDay: null,
			apptAdvNcePrd: null,
			visitContent: null,
			// Contact Record
			contNextRemindDt: null,
			contAppoHour: null,
			contAppoMinute: null,
			contTitle: null,
			contVisitPurCode: '',
			contVisitAprCode: '',
			contDate: null,
			contHour: null,
			contMin: null,
			contContent: null,
			contStatCode: '',
			contProcCode: '',
			doneYn: 'N',
			// Customer Important Days
			memoryDate: null,
			memRemindYn: 'N',
			memRemindDays: 1,
			memRemindPrd: null,
			memContent: null,
			// Common Contact Content
			reuseWord: null,
			newReuseWord: null,
			wobReuseWords: [],
			// Dropdown menu
			selectCusGroup: null,
			selectCusList: [],
			tdActionMenu: null,
			contStatMenu: null,
			contProcMenu: null,
			// Display parameters
			wobSimpleCus: {},
			// File handling
			tempFile: null,
			files: [],
			maxFileCount: 3,
			validExts: ['doc', 'docx', 'pdf', 'xlsx', 'txt'],
			symbols: ['/', ':', '*', '?', '"', '<', '>', '|'],
			modal: null
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo'])
	},
	watch: {
		userInfo: function (newVal) {
			const self = this;
			if (!newVal) return;
			self.loginRoleType = newVal.roleType;
		},
		groupSeq: function (val) {
			const self = this;
			if (self.groupSeq) {
				self.getSelectCusList(val);
			}
		},
		apptPlaceType: function (val) {
			const self = this;
			if (val == 'H') {
				self.apptAddress = self.wobSimpleCus.addH;
			}
			if (val == 'C') {
				self.apptAddress = self.wobSimpleCus.addC;
			}
		},
		attBranch: function (val) {
			const self = this;
			if (val) {
				self.getSelectUserList(val);
			}
		},
		isWholeDay: function (val) {
			const self = this;
			if (val) {
				self.visitHour = '00';
				self.visitMin = '00';
				self.visitHourE = '23';
				self.visitMinE = '59';
				self.visitDateE = self.visitDate;
			}
		},
		visitDateE: function (val) {
			const self = this;
			if (val && self.isWholeDay) {
				self.visitDate = self.visitDateE;
			}
		},
		visitDate: function (val) {
			const self = this;
			if (val && self.isWholeDay) {
				self.visitDateE = self.visitDate;
			}
		}
	},
	mounted: function () {
		const self = this;
		self.getSelectCusGroup();
	},
	methods: {
		setSelectCusCode: function (cusCode, idn) {
			const self = this;
			self.queryCusCode = cusCode;
			self.queryIdn = idn;
		},
		show: function (sectionCode) {
			const self = this;
			self.resetVariables();
			if (!self.getSelectReuseWord) {
				self.getSelectReuseWordSelf();
			}

			if (sectionCode) {
				self.sectionCode = sectionCode;
			}
			self.isOpenModal = true;
		},
		getSelectCusGroup: async function () {
			const self = this;
			const ret = await self.$api.getCusGroupListApi();
			self.selectCusGroup = ret.data;
		},
		getTdActionMenu: async function () {
			const self = this;
			const ret = await self.$api.getVisitAprActionMenuApi();
			self.tdActionMenu = ret.data;
		},
		getContStatMenu: async function () {
			const self = this;
			const ret = await self.$api.getContStatMenuApi();
			self.contStatMenu = ret.data;
		},
		getContProcMenu: async function () {
			const self = this;
			const ret = await self.$api.getContProcMenuApi();
			self.contProcMenu = ret.data;
		},
		getSelectCusList: async function (groupSeq) {
			const self = this;
			const ret = await self.$api.getContProcMenuApi({ groupSeq });
			if (ret.data.groupDetail) {
				self.selectCusList = ret.data.groupDetail;
			}
		},
		getSelectUserList: async function (branCode) {
			const self = this;
			const ret = await self.$api.getAdmUsersListApi({ branCode });
			self.selectUserList = ret.data;
		},
		getCusInfo: async function () {
			const self = this;

			if (_.isBlank(self.queryCusCode) && _.isBlank(self.queryIdn)) {
				self.$bi.alert(self.$t('wob.pleaseEnterCustomerIdTaxId'));
				return;
			}

			let queryData = {};
			if (!_.isBlank(self.queryCusCode)) {
				queryData = { cusCode: self.queryCusCode };
				self.queryIdn = null;
			}
			else {
				queryData = { cusCode: self.queryIdn };
				self.queryCusCode = null;
			}

			const resp = await self.$api.getWobSimpleCusApi(queryData);

			if (_.isEmpty(resp.data)) {
				self.$bi.alert(self.$t('wob.noCustomerFound'));
				return;
			}
			else {
				self.resetVariables('ignoreQueryFields');
				self.wobSimpleCus = resp.data;
				self.wobSimpleCus.identity = _.find(self.selectIdentity, { codeValue: self.wobSimpleCus.identityCode })?.codeName;
				$('#Section' + self.sectionCode).show();
			}
		},
		getOtherDetail: async function () {
			const self = this;
			const ret = await self.$api.getOtherDetailApi({ cusCode: self.cusCode });
			// Contact date and other information
			self.lastConnectionDt = ret.data.logsCreateDt;
		},
		doInsertRec: async function () {
			const self = this;
			if (self.sectionCode == 1) {
				self.insertConnect();
			}
			else if (self.sectionCode == 2) {
				self.insertVisit();
			}
		},
		insertVisit: function () {
			const self = this;
			if (!self.checkEndTimeAfterStartTime()) {
				self.$bi.alert(self.$t('wob.executeDateRangeError'));
				return;
			}
			self.$refs.appointmentTaskWatch.validate().then(async function (pass) {
				if (pass.valid) {
					const content = _.isNil(self.visitContent) ? '' : self.visitContent;
					const data = {
						cusCode: self.wobSimpleCus.cusCode,
						visitAprCode: self.apptVisitAprCode,
						visitPurCode: self.apptVisitPurCode,
						visitDate: _.formatDate(self.visitDate),
						visitHour: self.visitHour,
						visitMin: self.visitMin,
						visitContent: content,
						cusName: self.wobSimpleCus.cusName,
						idn: self.wobSimpleCus.idn,
						title: self.apptTitle,
						address: self.apptAddress,
						visitDateE: _.formatDate(self.visitDateE),
						visitHourE: self.visitHourE,
						visitMinE: self.visitMinE,
						userList: self.attList.map(att => att.userCode)
					};

					const jsonString = JSON.stringify(data);

					const formData = new FormData();
					formData.append('baseInfo', new Blob([jsonString], { type: 'application/json' }));

					_.forEach(self.files, function (fileInfo) {
						if (fileInfo.file) {
							formData.append('fileNo', fileInfo.fileNo);
							formData.append('fileObject', fileInfo.file);
						}
					});

					await self.$api.postWobVisitApi({ formData });

					self.$bi.alert(self.$t('wob.addSuccess'));
					self.closeModal();
					if (self.getCalendarTasks) {
						self.getCalendarTasks();
						return;
					}
					if (this.$route.path === '/wob/taskCalendar' || this.$route.path === '/') {
						setTimeout(function () {
							location.reload();
						}, 1000);
					}
				}
			});
		},
		insertConnect: function () {
			const self = this;
			self.$refs.contTaskWatch.validate().then(async function (pass) {
				if (pass.valid) {
					const content = _.isNil(self.contContent) ? '' : self.contContent;
					const title = self.selectContactPurpose.filter(item => item.codeValue == self.contVisitPurCode).length
						? self.selectContactPurpose.filter(item => item.codeValue == self.contVisitPurCode)[0].codeName
						: self.contVisitPurCode;

					const ret = await self.$api.postWobContactApi({
						cusCode: self.wobSimpleCus.cusCode,
						visitAprCode: self.contVisitAprCode,
						visitPurCode: self.contVisitPurCode,
						visitDate: _.formatDate(self.contDate),
						visitHour: self.contHour,
						visitMin: self.contMin,
						visitContent: content,
						cusName: self.wobSimpleCus.cusName,
						idn: self.wobSimpleCus.idn
					});
					self.$bi.alert(self.$t('wob.addSuccess'));
					self.closeModal();
					if (self.getCalendarTasks) {
						self.getCalendarTasks();
						return;
					}
					if (self.$route.path === '/wob/taskCalendar' || self.$route.path === '/') {
						setTimeout(function () {
							location.reload();
						}, 1000);
					}
				}
			});
		},
		setDefaultReuseWords: function () {
			const self = this;
			self.wobReuseWords = [];
			for (let i = 0; i < 10; i++) {
				const tempWordObj = {
					wordsId: i + 1,
					words: null
				};
				self.wobReuseWords.push(tempWordObj);
			}
		},
		getReuseWords: async function () {
			const self = this;
			const type = self.$route.path.split('/').filter(Boolean)[0].toUpperCase();
			const ret = await self.$api.getReuseWordsApi({ type });
			ret.data.forEach(function (item) {
				const index = item.wordsId - 1;
				self.wobReuseWords[index].words = item.words;
			});
		},
		insertReuseWord: async function (index) {
			const self = this;
			const words = self.wobReuseWords[index].words;
			const wordsId = self.wobReuseWords[index].wordsId;

			await self.$api.ostWobReuseWordsApi({
				wordsId: wordsId,
				words: words
			});

			if (self.getSelectReuseWord) {
				self.getSelectReuseWord();
			}
			else {
				self.getSelectReuseWordSelf();
			}
		},
		appendReuseWord: function () {
			const self = this;

			if (!self.contContent) {
				self.contContent = '';
			}

			if (self.reuseWord != null) {
				self.contContent = self.contContent + self.reuseWord;
			}
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		resetVariables: function (ignoreType) {
			const self = this;

			if (ignoreType != 'ignoreQueryFields') {
				self.queryCusCode = '';
				self.queryIdn = null;
				self.groupSeq = '';
			}

			self.wobSimpleCus = {};

			self.showAttList = false;
			self.showReuseWordSetting = false;

			// API parameters
			self.isWholeDay = false;
			self.idn = null;
			// Personal work
			self.nextRemindDt = null;
			self.appoHour = null;
			self.appoMinute = null;
			self.personalWorkTitle = null;
			self.advNce = 'N';
			self.advNceDay = null;
			self.advNcePrd = null;
			self.content = null;

			// Appointment record
			self.visitDate = null;
			self.visitHour = null;
			self.visitMin = null;
			self.visitDateE = null;
			self.visitHourE = null;
			self.visitMinE = null;
			self.apptVisitPurCode = '';
			self.apptVisitAprCode = '';
			self.apptTitle = null;
			self.apptPlaceType = null;
			self.apptAddress = '';
			self.visitContent = '';
			self.attBranch = null;
			self.attUser = null;
			self.orderAttUser = null;
			self.attList = [];
			self.apptAdvNce = 'N';
			self.apptAdvNceDay = null;
			self.apptAdvNcePrd = null;
			self.visitContent = null;

			// {{ $t('wob.contactRecord') }}
			self.contNextRemindDt = null;
			self.contAppoHour = null;
			self.contAppoMinute = null;
			self.contTitle = null;
			self.contVisitPurCode = '';
			self.contVisitAprCode = '';
			self.contDate = null;
			self.contHour = null;
			self.contMin = null;
			self.contContent = null;
			self.contStatCode = '';
			self.contProcCode = '';
			self.doneYn = 'N';

			// Customer important days
			self.memoryDate = null;
			self.memRemindYn = 'N';
			self.memRemindDays = 1;
			self.memRemindPrd = null;
			self.memContent = null;
			// File handling
			self.fileTemp = null;
			self.files = [];
		},
		addAttUser: function (userCode) {
			const self = this;
			const userName = self.selectUserList.find(user => user.userCode == userCode)?.userName;
			if (!userName) return;
			if (self.attList.filter(att => att.userCode == userCode).length == 0) {
				self.attList.push({ userName: userName, userCode: userCode });
			}
		},
		addOrderAttUser: async function (userCode) {
			const self = this;
			const ret = await self.$api.getWobAttUserChkApi({ userCode });
			if (ret.data.cnt > 0) {
				const ret2 = await self.$api.etAdmUsersListApi({ userCode });
				self.addAttUser(ret2.data[0].userCode);
			}
			else {
				self.$bi.alert(self.$t('wob.noPersonnelData'));
			}
		},
		setShowAttList: function () {
			const self = this;
			self.showAttList = !self.showAttList;
		},
		setShowReuseWordSetting: function () {
			const self = this;
			self.showReuseWordSetting = !self.showReuseWordSetting;
		},
		removeAttUser: function (userCode) {
			this.attList = this.attList.filter(user => user.userCode != userCode);
		},
		// file upload
		handleChange: function (event) {
			const self = this;
			self.fileTemp = event.target.files[0];
		},
		addFile: async function () {
			const self = this;
			const result = await self.$refs.fileForm.validateField('uploadFile');
			if (!result.valid) return;
			if (self.files.length >= self.maxFileCount) {
				return;
			}

			const fileInfo = {
				fileNo: self.generatorId('file'),
				groupId: null,
				showName: self.fileTemp.name,
				fileName: null,
				contentType: self.fileTemp.type,
				filePath: null,
				file: self.fileTemp
			};
			self.files.push(fileInfo);
			self.$refs.fileForm.resetForm(0);
			self.$refs.uploadFile.$el.value = null;
		},
		previewFile: function (targetFileId) {
			const self = this;
			const index = self.files.findIndex(f => f.fileNo === targetFileId);
			const fileInfo = self.files[index];
			let url;
			// Preview file to upload
			if (fileInfo.file) {
				url = URL.createObjectURL(fileInfo.file);
				const previewWindow = window.open(url, '_blank');
				previewWindow.document.title = fileInfo.showName;
				previewWindow.addEventListener('beforeunload', () => {
					URL.revokeObjectURL(url);
				});
				// Preview server file
			}
			else {
				this.$api.downloadFileApi({ fileId: targetFileId, fileType: 5 }, fileInfo.showName);
			}
		},
		deleteFile: function (targetFileId) {
			const self = this;
			const index = self.files.findIndex(f => f.fileNo === targetFileId);
			if (index != -1) {
				self.files.splice(index, 1);
			}
		},
		generatorId: function (name) {
			return name + '-' + _.now() + _.random(0, 99);
		},
		getSelectReuseWordSelf: async function () {
			const self = this;
			const ret = await self.$api.getWobReuseWordsApi();
			const result = ret.data;
			const needAppend = 5 - result.length;
			if (result.length < 5) {
				for (let i = 0; i < needAppend; i++) {
					result.push({ words: '', wordsId: ret.data.length + i + 1 });
				}
			}
			self.selectReuseWord = result;
			self.wobReuseWords = result;
		},
		getSelectVisitPurpose: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({ codeType: 'WOB_VISIT_PURPOSE' });
			self.selectVisitPurpose = ret.data;
		},
		getSelectIdentity: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({ codeType: 'CUS_IDENTITY' });
			self.selectIdentity = ret.data;
		},
		getSelectVisitType: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({ codeType: 'WOB_VISIT_TYPE' });
			self.selectVisitType = ret.data;
		},
		getSelectPlaceType: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({ codeType: 'WOB_PLACE_TYPE' });
			self.selectPlaceType = ret.data;
		},
		getSelectContactPurpose: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({ codeType: 'WOB_CONTACT_PURPOSE' });
			self.selectContactPurpose = ret.data;
		},
		getSelectContactType: async function () {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({ codeType: 'WOB_CONTACT_TYPE' });
			self.selectContactType = ret.data;
		},
		getSelectAttBranches: async function () {
			const self = this;
			const ret = await self.$api.getWobAttBranchesApi();
			self.selectAttBranches = ret.data;
		},
		checkEndTimeAfterStartTime: function () {
			const startTime = new Date(this.visitDate);
			startTime.setHours(this.visitHour, this.visitMin, 0);

			const endTime = new Date(this.visitDateE);
			endTime.setHours(this.visitHourE, this.visitMinE, 0);

			if (endTime > startTime) {
				return true;
			}
			else {
				return false;
			}
		}
	}
};
</script>
