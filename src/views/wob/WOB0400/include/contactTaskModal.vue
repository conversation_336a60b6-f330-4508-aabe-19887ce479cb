<template>
	<div>
		<vue-modal :is-open="modalStates.modal1" @close="() => closeModal('modal1')">
			<template #content="props">
				<div class="modal-dialog modal-dialog-centered modal-lg">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								{{ $t('wob.contact') }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="closeModal('modal1')"
							/>
						</div>
						<div class="modal-body">
							<div class="alert alert-info" role="alert">
								[ {{ tdRecTask.cusName }} ] {{ tdRecTask.nextRemindDt }}, {{ tdRecTask.nextRemindTime }}
							</div>
							<div class="card card-form">
								<div class="card-header">
									<h4>{{ $t('wob.contactRecord') }}</h4>
								</div>
								<table class="biv-table table table-bordered table-RWD table-horizontal-RWD">
									<tbody>
										<tr>
											<th class="w20">
												{{ $t('wob.contactDate') }}
											</th>
											<td class="w80">
												{{ tdRecTask.nextRemindDt }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('wob.contactTime') }}</th>
											<td>{{ tdRecTask.nextRemindTime }}</td>
										</tr>
										<tr>
											<th>{{ $t('wob.subject') }}</th>
											<td>{{ tdRecTask.title }}</td>
										</tr>
										<tr>
											<th>{{ $t('wob.processingMethod') }}</th>
											<td>{{ tdRecTask.visitAprName }}</td>
										</tr>
										<tr>
											<th>{{ $t('wob.processingContent') }}</th>
											<td>{{ tdRecTask.content }}</td>
										</tr>
										<tr>
											<th>{{ $t('wob.contactStatus') }}</th>
											<td>{{ tdRecTask.contStatName }}</td>
										</tr>
										<tr>
											<th>{{ $t('wob.processingFollowUp') }}</th>
											<td>{{ tdRecTask.contProcName }}</td>
										</tr>
										<tr>
											<th>{{ $t('wob.isClosed') }}</th>
											<td>
												<span v-if="tdRecTask.status === 'CLOSE'">{{ $t('wob.closed') }}</span>
												<span v-if="tdRecTask.status === 'UNPRC'">{{ $t('wob.notClosed') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="modal-footer">
							<button
								name="btnClose"
								type="button"
								class="btn btn-white"
								aria-label="Close"
								@click.prevent="closeModal('modal1')"
							>
								{{ $t('wob.close') }}
							</button>
							<button
								name="btnDelete"
								type="button"
								class="btn btn-danger"
								@click="deleteTdRec()"
							>
								{{ $t('wob.delete') }}
							</button>
							<button
								name="btnModify"
								type="button"
								class="btn btn-primary"
								@click.prevent="doUpdate()"
							>
								{{ $t('wob.modify') }}
							</button>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>

		<!-- Modal 聯繫紀錄維護 -->
		<vue-modal :is-open="modalStates.modal2" @close="() => closeModal('modal2')">
			<template #content="props">
				<div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								{{ $t('wob.contact') }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="closeModal('modal2')"
							/>
						</div>
						<div class="modal-body overflow-scroll">
							<div class="card-clientCard">
								<div class="card shadow-none mb-3">
									<table>
										<tbody>
											<tr>
												<td width="10%" class="clientCard-icon">
													<div class="avatar avatar-male">
														<img :src="getImgURL('avatar', 'man-1.png')" class="rounded-circle bg-info">
														<!-- <img th:src="@{/images/avatar/man-3.png}" class="rounded-circle bg-info" v-if="gender =='F'">-->
													</div>
													<h5 class="mb-0">
														{{ cusInfo.cusName || '--' }}
													</h5>
												</td>
												<td width="90%">
													<div class="caption tx-black">
														<span>
															{{ $t('wob.recentContactDate') }}：{{ $filters.formatDate(cusInfo.lastConnectionDt) || '--' }}
														</span>
													</div>
													<div class="row">
														<div class="col-lg-4">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-clipboard-data mg-xl-e-5-f" />{{ $t('wob.investmentAttribute') }}：{{
																		cusInfo.rankName || '--'
																	}}
																</li>
																<li><i class="bi bi-gift mg-xl-e-5-f" />{{ $t('wob.birthday') }}：{{ cusInfo.birth || '--' }}</li>
																<li>
																	<i class="bi bi-envelope mg-xl-e-5-f" />{{ $t('wob.email') }}：{{ cusInfo.email || '--' }}
																</li>
															</ul>
														</div>
														<div class="col-lg-3">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-house-door mg-xl-e-5-f" />{{ $t('wob.contactPhoneHome') }}：{{
																		cusInfo.phoneH || '--'
																	}}
																</li>
																<li>
																	<i class="bi bi-building mg-xl-e-5-f" />{{ $t('wob.contactPhoneOffice') }}：{{
																		cusInfo.phoneO || '--'
																	}}
																</li>
																<li>
																	<i class="bi bi-telephone mg-xl-e-5-f" />{{ $t('wob.contactPhoneMobile') }}：{{
																		cusInfo.phoneM || '--'
																	}}
																</li>
															</ul>
														</div>
														<div class="col-lg-5">
															<ul class="list-unstyled profile-info-list">
																<li>
																	<i class="bi bi-journal-medical mg-xl-e-5-f" />{{ $t('wob.minorWealthManagementConsent') }}：
																	<span :class="cusInfo.childInvYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.childInvYn === 'Y' ? $t('wob.yes') : $t('wob.no') }}
																	</span>
																</li>
																<li>
																	<i class="bi bi-journal-text mg-xl-e-5-f" />{{ $t('wob.specificTrustInvestmentConsent') }}：
																	<span :class="cusInfo.specRecommYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.specRecommYn === 'Y' ? $t('wob.yes') : $t('wob.no') }}
																	</span>
																</li>
																<li>
																	<i class="bi bi-journal-bookmark mg-xl-e-5-f" />{{ $t('wob.wealthSpecificCustomer') }}：
																	<span :class="cusInfo.specCusYn === 'Y' ? 'tx-red' : 'tx-green'">
																		{{ cusInfo.specCusYn === 'Y' ? $t('wob.yes') : $t('wob.no') }}
																	</span>
																</li>
															</ul>
														</div>
													</div>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
							<div class="row">
								<div class="col-12 mt-3">
									<div class="card card-form shadow-none">
										<div class="card-header">
											<h4>{{ $t('wob.contactRecord') }}</h4>
											<span class="tx-square-bracket">{{ $t('wob.requiredField') }}</span>
										</div>
										<div class="card-content">
											<vue-form v-slot="{ errors, validate }" ref="tdConnTask">
												<table class="biv-table table table-RWD table-borderless">
													<tbody>
														<tr>
															<th>
																<label class="form-label tx-require">{{ $t('wob.contactDate') }}</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field
																		v-model="contDate"
																		type="date"
																		name="contDate"
																		class="form-control"
																		:class="{ 'is-invalid': errors.contDate }"
																		:label="$t('wob.contactDate')"
																		rules="required"
																	/>
																	<div>
																		<span v-show="errors.contDate" class="text-danger">{{
																			errors.contDate
																		}}</span>
																	</div>
																</div>
															</td>
															<th>
																<label class="form-label">{{ $t('wob.contactTime') }}</label>
															</th>
															<td>
																<div class="input-group">
																	<vue-field
																		id="contHour"
																		v-model="contHour"
																		as="select"
																		class="form-select"
																		name="contHour"
																		:class="{ 'is-invalid': errors.contHour }"
																		rules="required"
																		:label="$t('wob.contactTime')"
																	>
																		<option value="00">
																			00
																		</option>
																		<option value="01">
																			01
																		</option>
																		<option value="02">
																			02
																		</option>
																		<option value="03">
																			03
																		</option>
																		<option value="04">
																			04
																		</option>
																		<option value="05">
																			05
																		</option>
																		<option value="06">
																			06
																		</option>
																		<option value="07">
																			07
																		</option>
																		<option value="08">
																			08
																		</option>
																		<option value="09">
																			09
																		</option>
																		<option value="10">
																			10
																		</option>
																		<option value="11">
																			11
																		</option>
																		<option value="12">
																			12
																		</option>
																		<option value="13">
																			13
																		</option>
																		<option value="14">
																			14
																		</option>
																		<option value="15">
																			15
																		</option>
																		<option value="16">
																			16
																		</option>
																		<option value="17">
																			17
																		</option>
																		<option value="18">
																			18
																		</option>
																		<option value="19">
																			19
																		</option>
																		<option value="20">
																			20
																		</option>
																		<option value="21">
																			21
																		</option>
																		<option value="22">
																			22
																		</option>
																		<option value="23">
																			23
																		</option>
																	</vue-field>
																	<span class="input-group-text">{{ $t('wob.hour') }}</span>
																	<div>
																		<span v-show="errors.contHour" class="text-danger">{{
																			errors.contHour
																		}}</span>
																	</div>
																	<vue-field
																		id="contMin"
																		v-model="contMin"
																		as="select"
																		class="form-select"
																		name="contMin"
																		:class="{ 'is-invalid': errors.contMin }"
																		rules="required"
																		:label="$t('wob.contactTime')"
																	>
																		<option selected="selected" value="00">
																			00
																		</option>
																		<option v-for="minute in selectMinutes" :value="minute">
																			{{ minute }}
																		</option>
																	</vue-field>
																	<span class="input-group-text">{{ $t('wob.minute') }}</span>
																	<div>
																		<span v-show="errors.contMin" class="text-danger">{{ errors.contMin }}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">{{ $t('wob.subject') }}</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<vue-field
																		v-model="contTitle"
																		class="form-control"
																		name="contTitle"
																		type="text"
																		size="30"
																		value="contTitle"
																		:class="{ 'is-invalid': errors.contTitle }"
																		:label="$t('wob.subject')"
																		rules="required"
																	/>
																	<div style="height: 3px">
																		<span v-show="errors.contTitle" class="text-danger">{{
																			errors.contTitle
																		}}</span>
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">{{ $t('wob.processingMethod') }}</label>
															</th>
															<td colspan="3">
																<vue-field
																	id="contVisitAprCode"
																	v-model="contVisitAprCode"
																	as="select"
																	class="form-select"
																	name="contVisitAprCode"
																	:class="{ 'is-invalid': errors.contVisitAprCode }"
																	rules="required"
																	:label="$t('wob.processingMethod')"
																>
																	<option disabled selected value="">
																		{{ $t('wob.pleaseSelect') }}
																	</option>
																	<option v-for="visitType in visitAprMenu" :value="visitType.codeValue">
																		{{ visitType.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span v-show="errors.contVisitAprCode" class="text-danger">{{
																		errors.contVisitAprCode
																	}}</span>
																</div>
															</td>
														</tr>

														<tr>
															<th>
																<label class="form-label tx-require">{{ $t('wob.processingContent') }}</label>
															</th>
															<td colspan="3">
																<vue-field
																	id="contContent"
																	v-model="contContent"
																	as="textarea"
																	class="form-control"
																	name="contContent"
																	rows="5"
																	cols="50"
																	size="400"
																	:class="{ 'is-invalid': errors.contContent }"
																	rules="required"
																	:label="$t('wob.processingContent')"
																/>
																<div style="height: 3px">
																	<span v-show="errors.contContent" class="text-danger">{{
																		errors.contContent
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">{{ $t('wob.contactStatus') }}</label>
															</th>
															<td colspan="3">
																<vue-field
																	id="contStatCode"
																	v-model="contStatCode"
																	as="select"
																	class="form-select"
																	name="contStatCode"
																	:class="{ 'is-invalid': errors.contStatCode }"
																	rules="required"
																	:label="$t('wob.contactStatus')"
																>
																	<option disabled selected value="">
																		{{ $t('wob.pleaseSelect') }}
																	</option>
																	<option v-for="contStat in contStatMenu" :value="contStat.codeValue">
																		{{ contStat.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span v-show="errors.contStatCode" class="text-danger">{{
																		errors.contStatCode
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">{{ $t('wob.followUpProcessing') }}</label>
															</th>
															<td colspan="3">
																<vue-field
																	id="contProcCode"
																	v-model="contProcCode"
																	as="select"
																	class="form-select"
																	name="contProcCode"
																	:class="{ 'is-invalid': errors.contProcCode }"
																	rules="required"
																	:label="$t('wob.followUpProcessing')"
																>
																	<option disabled selected value="">
																		{{ $t('wob.pleaseSelect') }}
																	</option>
																	<option v-for="contProc in contProcMenu" :value="contProc.codeValue">
																		{{ contProc.codeName }}
																	</option>
																</vue-field>
																<div style="height: 3px">
																	<span v-show="errors.contProcCode" class="text-danger">{{
																		errors.contProcCode
																	}}</span>
																</div>
															</td>
														</tr>
														<tr>
															<th>
																<label class="form-label tx-require">{{ $t('wob.isClosed') }}</label>
															</th>
															<td colspan="3">
																<div class="input-group">
																	<div class="form-check form-check-inline">
																		<vue-field
																			id="doneYn_YY"
																			v-model="doneYn"
																			type="radio"
																			name="doneYn"
																			class="form-check-input"
																			value="Y"
																			:class="{ 'is-invalid': errors.doneYn }"
																			rules="required"
																			:label="$t('wob.isClosed')"
																		/>
																		<label class="form-check-label">{{ $t('wob.closed') }}</label>
																	</div>
																	<div class="form-check form-check-inline">
																		<vue-field
																			id="doneYn_NN"
																			v-model="doneYn"
																			type="radio"
																			name="doneYn"
																			class="form-check-input"
																			value="N"
																			:class="{ 'is-invalid': errors.doneYn }"
																			rules="required"
																			:label="$t('wob.isClosed')"
																		/>
																		<label class="form-check-label">{{ $t('wob.notClosed') }}</label>
																	</div>
																	<div style="height: 3px">
																		<span v-show="errors.doneYn" class="text-danger">{{ errors.doneYn }}</span>
																	</div>
																</div>
															</td>
														</tr>
													</tbody>
												</table>
											</vue-form>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div id="modifyTaskFooter" class="modal-footer">
							<input
								id="apptEditModalCloseButton"
								class="btn btn-white"
								type="button"
								:value="$t('wob.close')"
								@click.prevent="closeModal('modal2')"
							>
							<input
								id="btnSave"
								class="btn btn-primary"
								type="button"
								:value="$t('wob.save')"
								@click="updateTdRec()"
							>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- Modal end -->
	</div>
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import { getImgURL } from '@/utils/imgURL.js';

export default {
	components: {
		vueModal,
		'vue-form': Form,
		'vue-field': Field
	},
	props: {
		recCode: String,
		getCalendarTasks: Function,
		getSelectReuseWord: Function,
		userInfo: Object
	},
	data: function () {
		const minutes = [];
		for (let i = 1; i < 60; i++) {
			minutes.push(String(i).padStart(2, '0'));
		}
		return {
			modalStates: {
				modal1: false,
				modal2: false
			},
			tdRecTask: {},
			selectMinutes: minutes,

			// Menu
			visitAprMenu: [], // Processing method menu
			contStatMenu: [], // Contact status menu
			contProcMenu: [], // Follow-up processing menu

			selectReuseWord: [],
			showReuseWordSetting: false,

			// Customer information
			cusInfo: {
				cusCode: null,
				cusName: null,
				birth: null,
				email: null,
				phoneH: '',
				phoneO: '',
				phoneM: '',
				rankName: null,
				childInvYn: null,
				specRecommYn: null,
				specCusYn: null,
				logsCreateDt: null
			},

			// Edit parameters
			contCusCode: null, // cusCode

			contDate: null, // Contact date
			contHour: '00', // Contact time (hour)
			contMin: '00', // Contact time (minute)
			contTitle: null, // Subject
			contVisitAprCode: null, // Processing method
			contContent: null, // Processing content
			contStatCode: null, // Contact status
			contProcCode: null, // Follow-up processing
			doneYn: null, // Is closed

			reuseWord: null, // Common contact content
			wobReuseWords: []
		};
	},
	watch: {
		recCode: function (val) {
			const self = this;
			if (self.recCode) {
				self.getTdRec();
			}
		}
	},
	methods: {
		getImgURL,
		closeModal: function (modalName) {
			this.modalStates[modalName] = false;
		},
		openModal: function (modalName) {
			this.modalStates[modalName] = true;
		},
		// Get customer data
		getCusInfo: function () {
			const self = this;
			self.$api
				.getCustomer({
					cusCode: self.cusInfo.cusCode
				})
				.then(function (ret) {
					if (!ret.data) {
						self.$bi.alert(self.$t('wob.noCustomerFound'));
						self.clearValues();
						return;
					}
					self.cusInfo.cusName = ret.data.cusName;
					self.cusInfo.birth = ret.data.birth;
					self.cusInfo.email = ret.data.email;
					self.cusInfo.rankName = ret.data.rankName;
					self.cusInfo.childInvYn = ret.data.childInvYn;
					self.cusInfo.specRecommYn = ret.data.specRecommYn;
					self.cusInfo.specCusYn = ret.data.specCusYn;
					self.cusInfo.lastConnectionDt = ret.data.logCreateDt;

					// Categorize contact methods
					ret.data.contactInfoList.forEach(function (item) {
						switch (item.contactType) {
							case 'E': // email
								self.cusInfo.email = item.email;
								break;
							case 'H': // Home phone
								self.cusInfo.phoneH = item.phone1;
								break;
							case 'O': // Office phone
								self.cusInfo.phoneO = item.phone1;
								break;
							case 'M': // Mobile phone
								self.cusInfo.phoneM = item.phone1;
								break;
						}
					});
				});
		},
		// Get contact record
		getTdRec: function () {
			const self = this;
			self.$api
				.getTdConnRecApi({
					recCode: self.recCode
				})
				.then(function (ret) {
					self.tdRecTask = ret.data;
					self.cusInfo.cusCode = self.tdRecTask.cusCode;
					self.cusInfo.birth = _.formatDate(ret.data.birth);
					self.contactDt = ret.data.contactDt.replaceAll('/', '-');
				});
		},
		// Delete contact record
		deleteTdRec: function () {
			const self = this;
			self.$bi.confirm(self.$t('wob.confirmDeleteData'), {
				event: {
					confirmOk: function () {
						self.$api
							.deleteTdRecApi({
								recCode: self.recCode
							})
							.then(function (ret) {
								self.$bi.alert(self.$t('wob.deleteSuccess'));
								self.modalStates.modal1 = false;
								if (self.getCalendarTasks) {
									self.getCalendarTasks();
								}
							});
					}
				}
			});
		},
		// Modify contact record
		doUpdate: function () {
			const self = this;
			self.getCusInfo();
			self.getVisitAprMenu();
			self.getContStatMenu();
			self.getContProcMenu();
			self.modalStates.modal2 = true;

			self.contDate = self.tdRecTask.nextRemindDt.replaceAll('/', '-');
			self.contCusCode = self.tdRecTask.cusCode;
			self.contTitle = self.tdRecTask.title;
			self.contContent = self.tdRecTask.content;

			self.contVisitAprCode = self.tdRecTask.visitAprCode;
			self.contStatCode = self.tdRecTask.contStatCode;
			self.contProcCode = self.tdRecTask.contProcCode;
			self.doneYn = self.tdRecTask.status === 'CLOSE' ? 'Y' : self.tdRecTask.status === 'UNPRC' ? 'N' : null;
			const contTime = self.tdRecTask.nextRemindTime.split(':');
			self.contHour = contTime[0];
			self.contMin = contTime[1];
		},
		// Update contact record
		updateTdRec: function () {
			const self = this;
			self.$refs.tdConnTask.validate().then(function (pass) {
				if (pass.valid) {
					const content = _.isNil(self.contContent) ? '' : self.contContent;

					self.$api
						.patchTdConnRecApi({
							recCode: self.recCode,
							cusCode: self.contCusCode,
							nextRemindDt: moment(self.contDate).format('YYYY-MM-DD'),
							nextRemindTime: self.contHour + ':' + self.contMin,
							title: self.contTitle,
							visitAprCode: self.contVisitAprCode,
							content: content,
							contStatCode: self.contStatCode,
							contProcCode: self.contProcCode,
							doneYn: self.doneYn
						})
						.then(function (ret) {
							self.$bi.alert(self.$t('wob.updateSuccess'));
							self.modalStates.modal2 = false;
							if (self.getCalendarTasks) {
								self.getCalendarTasks();
							}
							self.getTdRec();
						});
				}
			});
		},
		// Get processing method menu
		getVisitAprMenu: function () {
			const self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'VISIT_APR_CODE'
				})
				.then(function (ret) {
					self.visitAprMenu = ret.data;
				});
		},
		// Get contact status menu
		getContStatMenu: function () {
			const self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'CONT_STAT_CODE'
				})
				.then(function (ret) {
					self.contStatMenu = ret.data;
				});
		},
		// Get follow-up processing menu
		getContProcMenu: function () {
			const self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'CONT_PROC_CODE'
				})
				.then(function (ret) {
					self.contProcMenu = ret.data;
				});
		},
		setShowReuseWordSetting: function () {
			const self = this;
			self.showReuseWordSetting = !self.showReuseWordSetting;
		},
		insertReuseWord: function (index) {
			const self = this;
			const words = self.wobReuseWords[index].words;
			const wordsId = self.wobReuseWords[index].wordsId;

			self.$api
				.postWobReuseWordsApi({
					wordsId: wordsId,
					words: words
				})
				.then(function (ret) {
					self.getSelectReuseWord();
				});
		},
		appendReuseWord: function () {
			const self = this;

			if (!self.contContent) {
				self.contContent = '';
			}

			if (self.reuseWord != null) {
				self.contContent = self.contContent + self.reuseWord;
			}
		},
		getSelectContactReuseWord: function () {
			const self = this;
			self.$api.getWobReuseWordsApi().then(function (ret) {
				const result = ret.data;
				const needAppend = 5 - result.length;
				if (result.length < 5) {
					for (let i = 0; i < needAppend; i++) {
						result.push({ words: '', wordsId: ret.data.length + i + 1 });
					}
				}
				self.selectReuseWord = result;
				self.wobReuseWords = result;
			});
		}
	}
};
</script>
