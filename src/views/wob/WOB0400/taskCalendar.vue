<template>
	<!-- Page content start -->
	<div>
		<div v-if="!isShowSummary">
			<!-- Page content start -->

			<!-- Calendar Start -->
			<div class="mb-3">
				<button
					v-if="userInfo?.roleType === 'RM'"
					id="cal-visit"
					class="btn btn-week cal-visit me-2"
					type="button"
					@click="changeDisplayParam('A')"
				>
					<p class="mb-0 ellipsis">
						{{ $t('wob.appointmentEvent') }}
					</p>
				</button>
				<button
					id="cal-bank"
					class="btn btn-week cal-bank me-2"
					type="button"
					@click="changeDisplayParam('P')"
				>
					<p class="mb-0 ellipsis">
						{{ $t('wob.personalNotes') }}
					</p>
				</button>
				<button
					id="porduct-expire"
					class="btn btn-week cal-expire me-2"
					type="button"
					@click="changeDisplayParam('PRO')"
				>
					<p class="mb-0 ellipsis">
						{{ $t('wob.productExpiry') }}
					</p>
				</button>
				<button
					v-if="userInfo?.roleType === 'RM'"
					id="cal-FC"
					class="btn btn-week cal-FC me-2"
					type="button"
					@click="changeDisplayParam('C')"
				>
					<p class="mb-0 ellipsis">
						{{ $t('wob.customerImportantDate') }}
					</p>
				</button>
				<button
					v-if="userInfo?.roleType === 'RM'"
					id="cal-rec"
					class="btn btn-week cal-rec me-2"
					type="button"
					@click="changeDisplayParam('CNT')"
				>
					<p class="mb-0 ellipsis">
						{{ $t('wob.contactRecord') }}
					</p>
				</button>
			</div>
			<div id="calendar" ref="calendar" />
			<!-- Calendar END -->

			<!-- 到期商品檢視 -->
			<vue-modal :is-open="isOpenModal['fund']" @close="closeModal('fund')">
				<template #content="props">
					<vue-fund-modal
						ref="fundModalRef"
						:is-open-fund-modal="isOpenModal['fund']"
						:fin-req-code-menu="finReqCodeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					/>
				</template>
			</vue-modal>
			<vue-modal :is-open="isOpenModal['etf']" @close="closeModal('etf')">
				<template #content="props">
					<vue-etf-modal
						ref="etfModalRef"
						:is-open-etf-modal="isOpenModal['etf']"
						:fin-req-code-menu="finReqCodeMenu"
						:pro-price-range-menu="proPriceRangeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					/>
				</template>
			</vue-modal>
			<vue-modal :is-open="isOpenModal['bond']" @close="closeModal('bond')">
				<template #content="props">
					<vue-bond-modal
						ref="bondModalRef"
						:is-open-bond-modal="isOpenModal['bond']"
						:fin-req-code-menu="finReqCodeMenu"
						:pro-price-range-menu="proPriceRangeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					/>
				</template>
			</vue-modal>
			<!-- <vue-modal :is-open="isOpenModal['pfd']" @close="closeModal('pfd')">
				<template v-slot:content="props">
					<vue-pfd-modal
						ref="pfdModalRef"
						:is-open-pfd-modal="isOpenModal['pfd']"
						:fin-req-code-menu="finReqCodeMenu"
						:close="props.close"
					></vue-pfd-modal>
				</template>
			</vue-modal> -->
			<vue-modal :is-open="isOpenModal['sp']" @close="closeModal('sp')">
				<template #content="props">
					<vue-sp-modal
						ref="spModalRef"
						:is-open-structured-product-modal="isOpenModal['sp']"
						:fin-req-code-menu="finReqCodeMenu"
						:pro-price-range-menu="proPriceRangeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					/>
				</template>
			</vue-modal>
			<vue-modal :is-open="isOpenModal['ins']" @close="closeModal('ins')">
				<template #content="props">
					<vue-ins-modal
						ref="insModalRef"
						:is-open-ins-modal="isOpenInsModal"
						:fin-req-code-menu="finReqCodeMenu"
						:download-file="downloadFile"
						:download-other-file="downloadOtherFile"
						:close="props.close"
					/>
				</template>
			</vue-modal>

			<!-- Modal Add Calendar Note -->
			<vue-wob-new-task-modal
				ref="newTaskModal"
				:get-calendar-tasks="getCalendarTasks"
				:get-select-reuse-word="getSelectReuseWord"
			/>
			<!-- Modal 2 View Personal Notes -->
			<vue-wob-personal-task-modal
				ref="personalTaskModal"
				:rec-code="personalTaskRecCode"
				:get-calendar-tasks="getCalendarTasks"
			/>
			<!-- Modal 5 View Appointment Record -->
			<vue-wob-appointment-task-modal
				ref="appointmentTaskModal"
				:rec-code="apptTaskRecCode"
				:role-show-edit-btn="roleShowEditBtn"
				:get-calendar-tasks="getCalendarTasks"
				:user-info="userInfo"
			/>
			<!-- Modal 7 View Customer Important Date -->
			<vue-wob-cus-momory-task-modal
				ref="cusMemoryTaskModal"
				:cus-memory-task-rec-code="cusMemoryTaskRecCode"
				:get-calendar-tasks="getCalendarTasks"
			/>
			<!-- Model View Contact Record -->
			<vue-wob-contact-task-modal
				ref="contactTaskModal"
				:rec-code="contactTaskRecCode"
				:get-calendar-tasks="getCalendarTasks"
				:get-select-reuse-word="getSelectReuseWord"
				:user-info="userInfo"
			/>

			<vue-cus-summary ref="cusSummaryRef" :set-is-show-summary="setIsShowSummary" />
		</div>
		<div id="calendar" ref="calendar" />
		<!--行事曆 END-->

		<!-- 到期商品檢視 -->
		<vue-modal :is-open="isOpenModal['fund']" @close="closeModal('fund')">
			<template #content="props">
				<vue-fund-modal
					ref="fundModalRef"
					:is-open-fund-modal="isOpenModal['fund']"
					:fin-req-code-menu="finReqCodeMenu"
					:download-file="downloadFile"
					:download-other-file="downloadOtherFile"
					:close="props.close"
				/>
			</template>
		</vue-modal>
		<vue-modal :is-open="isOpenModal['etf']" @close="closeModal('etf')">
			<template #content="props">
				<vue-etf-modal
					ref="etfModalRef"
					:is-open-etf-modal="isOpenModal['etf']"
					:fin-req-code-menu="finReqCodeMenu"
					:pro-price-range-menu="proPriceRangeMenu"
					:download-file="downloadFile"
					:download-other-file="downloadOtherFile"
					:close="props.close"
				/>
			</template>
		</vue-modal>
		<vue-modal :is-open="isOpenModal['bond']" @close="closeModal('bond')">
			<template #content="props">
				<vue-bond-modal
					ref="bondModalRef"
					:is-open-bond-modal="isOpenModal['bond']"
					:fin-req-code-menu="finReqCodeMenu"
					:pro-price-range-menu="proPriceRangeMenu"
					:download-file="downloadFile"
					:download-other-file="downloadOtherFile"
					:close="props.close"
				/>
			</template>
		</vue-modal>
		<!-- <vue-modal :is-open="isOpenModal['pfd']" @close="closeModal('pfd')">
			<template v-slot:content="props">
				<vue-pfd-modal
					ref="pfdModalRef"
					:is-open-pfd-modal="isOpenModal['pfd']"
					:fin-req-code-menu="finReqCodeMenu"
					:close="props.close"
				></vue-pfd-modal>
			</template>
		</vue-modal> -->
		<vue-modal :is-open="isOpenModal['sp']" @close="closeModal('sp')">
			<template #content="props">
				<vue-sp-modal
					ref="spModalRef"
					:is-open-structured-product-modal="isOpenModal['sp']"
					:fin-req-code-menu="finReqCodeMenu"
					:pro-price-range-menu="proPriceRangeMenu"
					:download-file="downloadFile"
					:download-other-file="downloadOtherFile"
					:close="props.close"
				/>
			</template>
		</vue-modal>
		<vue-modal :is-open="isOpenModal['ins']" @close="closeModal('ins')">
			<template #content="props">
				<vue-ins-modal
					ref="insModalRef"
					:is-open-ins-modal="isOpenInsModal"
					:fin-req-code-menu="finReqCodeMenu"
					:download-file="downloadFile"
					:download-other-file="downloadOtherFile"
					:close="props.close"
				/>
			</template>
		</vue-modal>

		<!-- Modal 新增行事曆記事 -->
		<vue-wob-new-task-modal
			ref="newTaskModal"
			:get-calendar-tasks="getCalendarTasks"
			:get-select-reuse-word="getSelectReuseWord"
		/>
		<!-- Modal 2 檢視個人記事 -->
		<vue-wob-personal-task-modal
			ref="personalTaskModal"
			:rec-code="personalTaskRecCode"
			:get-calendar-tasks="getCalendarTasks"
		/>
		<!-- Modal 5 檢視約訪紀錄 -->
		<vue-wob-appointment-task-modal
			ref="appointmentTaskModal"
			:rec-code="apptTaskRecCode"
			:role-show-edit-btn="roleShowEditBtn"
			:get-calendar-tasks="getCalendarTasks"
			:user-info="userInfo"
		/>
		<!-- Modal 7 檢視顧客重要日子 -->
		<vue-wob-cus-momory-task-modal
			ref="cusMemoryTaskModal"
			:cus-memory-task-rec-code="cusMemoryTaskRecCode"
			:get-calendar-tasks="getCalendarTasks"
		/>
		<!-- Model 檢視聯繫紀錄 -->
		<vue-wob-contact-task-modal
			ref="contactTaskModal"
			:rec-code="contactTaskRecCode"
			:get-calendar-tasks="getCalendarTasks"
			:get-select-reuse-word="getSelectReuseWord"
			:user-info="userInfo"
		/>

		<vue-cus-summary ref="cusSummaryRef" :set-is-show-summary="setIsShowSummary" />
	</div>
</template>
<script>
import _ from 'lodash';
import vueModal from '@/views/components/model.vue';
import vueWobNewTaskModal from './include/newTaskModal.vue';
import vueWobPersonalTaskModal from './include/personalTaskModal.vue';
import vueWobAppointmentTaskModal from './include/appointmentTaskModal.vue';
import vueWobCusMomoryTaskModal from './include/cusMemoryTaskModal.vue';
import vueWobContactTaskModal from './include/contactTaskModal.vue';
import vueCusSummary from '@/views/cus/include/cusSummary.vue';

import vueFundModal from '@/views/pro/PRO0101/include/fundModal.vue';
import vueEtfModal from '@/views/pro/PRO0101/include/etfModal.vue';
import vueBondModal from '@/views/pro/PRO0101/include/bondModal.vue';
import vueSpModal from '@/views/pro/PRO0101/include/spModal.vue';
import vueInsModal from '@/views/pro/PRO0101/include/insModal.vue';

// Calendar package
import { Calendar } from '@fullcalendar/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';

const modules = Object.fromEntries(
	Object.entries(
		import.meta.glob([
			'/node_modules/@fullcalendar/core/locales/*.js',
			'!/node_modules/@fullcalendar/core/locales/*.global.js',
			'!/node_modules/@fullcalendar/core/locales/*.global.min.js'
		], { import: 'default' })
	).map(([file, promise]) => [file.slice(file.indexOf('/node_modules/@fullcalendar/core/locales/') + '/node_modules/@fullcalendar/core/locales/'.length, file.length - 3), promise])
);

export default {
	components: {
		vueWobNewTaskModal,
		vueWobPersonalTaskModal,
		vueWobAppointmentTaskModal,
		vueWobCusMomoryTaskModal,
		vueWobContactTaskModal,
		vueModal,
		vueFundModal,
		vueEtfModal,
		vueBondModal,
		vueSpModal,
		vueInsModal,
		vueCusSummary
	},
	data: function () {
		return {
			wobReuseWords: null,
			// searchScope: 'SELF',
			startDate: null,
			endDate: null,
			loginRoleType: null,
			range: 'month',
			// View Modal parameters
			personalTaskRecCode: null, // 個人記事
			contactTaskRecCode: null, // 聯繫紀錄
			apptTaskRecCode: null, // 約訪事件
			cusMemoryTaskRecCode: null, // 客戶重要日子

			roleShowEditBtn: true,
			// Display parameters
			calendarTasks: null,
			events: [],
			displayEvents: [],
			displayParam: '',

			fullCalendar: null, // Calendar component
			fullCalendarStartDt: null, // Calendar component display start date,
			// Display control parameters
			isShowSummary: false,
			isShowSubordinate: false,

			isOpenModal: {
				pro: false
			}
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo']),
		...mapStores(useI18nStore),
		locale() {
			return this.i18nStore.locale;
		},
		isShowPageTitle: function () {
			return !this.isShowSummary;
		}
	},
	watch: {
		userInfo: {
			immediate: true,
			handler: function (newVal) {
				const self = this;
				if (newVal) {
					self.loginRoleType = newVal.roleType;
					if (
						newVal.roleCode == 'A05'
						|| newVal.roleCode == 'A06'
						|| newVal.roleCode == 'A08'
						|| newVal.roleCode == 'A09'
						|| newVal.roleCode == 'A10'
						|| newVal.roleCode == 'A11'
						|| newVal.roleCode == 'A12'
					) {
						self.roleShowEditBtn = false;
					}
					else {
						self.roleShowEditBtn = true;
					}
					self.showSubordinate(newVal.roleType);
				}
			}
		},
		async locale(newVal) {
			if (!newVal || this.fullCalendar == null) return;
			const localeModule = await this.loadCalendarLocale(newVal);
			this.fullCalendar.setOption('locale', localeModule);
		}
		// searchScope: function () {
		// 	this.getCalendarTasks();
		// 	this.getSelectReuseWord();
		// }
	},
	mounted: function () {
		const self = this;
		self.getCalendarTasks();
		self.getSelectReuseWord();
	},
	methods: {
		loadCalendarLocale(locale) {
			const promise = modules[locale.toLowerCase()];
			if (!promise) return;
			return promise();
		},
		closeModal(modalName) {
			const self = this;
			self.isOpenModal[modalName] = false;
		},
		// Get calendar information
		getCalendarTasks: async function () {
			const self = this;

			let calendar = self.fullCalendar;
			if (_.isNil(calendar)) {
				calendar = new Calendar(this.$refs.calendar, {
					plugins: [dayGridPlugin, timeGridPlugin],
					initialView: 'dayGridMonth'
				});
			}
			self.startDate = self.formatDate(calendar.view.currentStart);
			self.endDate = self.formatDate(calendar.view.currentEnd);

			self.$api
				.getCalendarTasksApi({
					startDate: _.formatDate(self.startDate),
					endDate: _.formatDate(self.endDate)
				})
				.then(function (ret) {
					self.calendarTasks = ret.data;
					// empty events
					self.events = [];

					// 個人記事
					if (self.calendarTasks.personalTasks) {
						self.calendarTasks.personalTasks.forEach(function (item) {
							const event = {};
							event.id = 'P';

							event.title = item.title;
							event.recCode = item.recCode;
							event.classNames = ['btn-week', 'cal-bank'];

							if (_.isBlank(item.nextRemindDt)) {
								event.start = item.nextRemindDt;
							}
							else {
								event.start = item.nextRemindDt + 'T' + item.nextRemindTime;
							}
							event.eventName = self.$t('wob.personalNotes');
							event.item = item;
							self.events.push(event);
						});
					}
					// {{ $t('wob.appointmentEvent') }}
					if (self.calendarTasks.appointmentTasks) {
						self.calendarTasks.appointmentTasks.forEach(function (item) {
							const event = {};
							event.id = 'A';
							event.title = item.title;
							event.recCode = item.recCode;
							event.classNames = ['btn-week', 'cal-visit'];
							if (_.isBlank(item.nextRemindTime)) {
								event.start = item.nextRemindDt;
							}
							else {
								event.start = item.nextRemindDt + 'T' + item.nextRemindTime;
							}
							if (_.isBlank(item.nextRemindTimee)) {
								event.end = item.nextRemindDte;
							}
							else {
								event.end = item.nextRemindDte + 'T' + item.nextRemindTimee;
							}
							event.eventName = self.$t('wob.appointmentEvent');
							event.item = item;
							self.events.push(event);
						});
					}
					// {{ $t('wob.customerImportantDate') }}
					if (self.calendarTasks.cusMemoryTasks) {
						self.calendarTasks.cusMemoryTasks.forEach(function (item) {
							const event = {};
							event.id = 'C';
							event.title = item.cusName + ' ' + item.note;
							event.recCode = item.id;
							event.classNames = ['btn-week', 'cal-FC'];
							if (!_.isBlank(item.dateDt)) {
								event.start = item.dateDt;
							}
							event.eventName = self.$t('wob.customerImportantDate');
							event.item = item;
							self.events.push(event);
						});
					}
					// {{ $t('wob.contactRecord') }}
					if (self.calendarTasks.contactTasks) {
						self.calendarTasks.contactTasks.forEach(function (item) {
							const event = {};
							event.id = 'CNT';
							event.title = item.cusName + ' ' + item.title;
							event.recCode = item.recCode;
							event.classNames = ['btn-week', 'cal-rec'];
							if (_.isBlank(item.nextRemindTime)) {
								event.start = item.nextRemindDt;
							}
							else {
								event.start = item.nextRemindDt + 'T' + item.nextRemindTime;
							}
							event.eventName = self.$t('wob.contactRecord');
							event.item = item;
							self.events.push(event);
						});
					}
					// Expiry product notification
					if (self.calendarTasks.expiredTasks) {
						self.calendarTasks.expiredTasks.forEach(function (item) {
							const event = {};
							event.id = 'PRO';
							event.title = item.bankProCode + ' ' + item.proName + self.$t('wob.expire');
							event.start = item.expireDt;
							event.proCode = item.proCode;
							event.pfcatCode = item.pfcatCode;
							event.classNames = ['btn-week', 'cal-expire'];
							event.eventName = self.$t('wob.productExpiry');
							event.item = item;
							self.events.push(event);
						});
					}
					self.displayEvents = self.events;

					if (self.fullCalendar != null) {
						self.reloadView();
					}
					else {
						self.renderCalendar();
					}
				});
		},
		reloadView: function () {
			const self = this;
			_.forEach(self.fullCalendar.getEventSources(), function (e) {
				e.remove();
			});
			self.fullCalendar.addEventSource(self.displayEvents);
		},
		async renderCalendar() {
			const self = this;
			const calendarEl = document.getElementById('calendar');

			self.fullCalendar = new Calendar(calendarEl, {
				plugins: [dayGridPlugin, timeGridPlugin],
				locale: await this.loadCalendarLocale(this.i18nStore.locale),
				initialDate: self.fullCalendarStartDt,
				nowIndicator: true,
				events: {

				},
				headerToolbar: {
					right: 'prev,next today addnew',
					center: 'title',
					left: 'dayGridMonth,dayGridWeek,timeGridDay'
				},
				customButtons: {
					addnew: {
						text: 'New Task',
						click: function () {
							self.$refs.newTaskModal.show();
						}
					},
					prev: {
						click: function () {
							self.fullCalendar.prev();
							self.fullCalendarStartDt = self.fullCalendar.view.currentStart;
							self.getCalendarTasks();
						}
					},
					next: {
						click: function () {
							self.fullCalendar.next();
							self.fullCalendarStartDt = self.fullCalendar.view.currentStart;
							self.getCalendarTasks();
						}
					},
					today: {
						text: 'Today',
						click: function () {
							self.fullCalendar.today();
							self.fullCalendar.changeView('dayGridMonth');
							self.fullCalendarStartDt = self.fullCalendar.view.currentStart;
							self.getCalendarTasks();
						}
					},
					dayGridMonth: {
						text: 'Month',
						click: function () {
							self.range = 'month';
							self.fullCalendar.changeView('dayGridMonth');
							self.getCalendarTasks();
						}
					},
					dayGridWeek: {
						text: 'Week',
						click: function () {
							self.range = 'week';
							self.fullCalendar.changeView('dayGridWeek');
							self.getCalendarTasks();
						}
					},
					timeGridDay: {
						text: 'Day',
						click: function () {
							self.range = 'day';
							self.fullCalendar.changeView('timeGridDay');
							self.getCalendarTasks();
						}
					}
				},
				navLinks: true,
				editable: true,
				selectable: true,
				selectMirror: true,
				dayMaxEvents: 5,
				events: self.displayEvents,
				eventDidMount: function (info) {
					if (info.event.title.length > 20) {
						info.el.querySelector('.fc-event-title').innerHTML = info.event.title.substring(0, 20) + '...';
					}
					if (info.event.end && info.event.start.getDate() !== info.event.end.getDate()) {
						info.el.classList.add('multi-day-event');
					}
				},

				eventClick: function (info) {
					if ('P' == info.event.id) {
						// {{ $t('wob.personalNotes') }}
						// self.personalTaskRecCode = info.event.extendedProps.recCode;
						// self.$refs.personalTaskModal.modalStates.modal1 = true;
						// Force close before opening modal
						self.$refs.personalTaskModal.modalStates.modal1 = false;
						self.personalTaskRecCode = info.event.extendedProps.recCode;
						self.$nextTick(() => {
							self.$refs.personalTaskModal.modalStates.modal1 = true;
						});
					}
					else if ('A' == info.event.id) {
						// {{ $t('wob.appointmentEvent') }}
						// self.apptTaskRecCode = info.event.extendedProps.recCode;
						// self.$refs.appointmentTaskModal.modalStates.modal1 = true;
						self.$refs.appointmentTaskModal.modalStates.modal1 = false;
						self.apptTaskRecCode = info.event.extendedProps.recCode;
						self.$nextTick(() => {
							self.$refs.appointmentTaskModal.modalStates.modal1 = true;
						});
					}
					else if ('C' == info.event.id) {
						// {{ $t('wob.customerImportantDate') }}
						// self.cusMemoryTaskRecCode = info.event.extendedProps.recCode;
						// self.$refs.cusMemoryTaskModal.modalStates.modal1 = true;
						self.$refs.cusMemoryTaskModal.modalStates.modal1 = false;
						self.cusMemoryTaskRecCode = info.event.extendedProps.recCode;
						self.$nextTick(() => {
							self.$refs.cusMemoryTaskModal.modalStates.modal1 = true;
						});
					}
					else if ('CNT' == info.event.id) {
						// {{ $t('wob.contactRecord') }}
						// self.contactTaskRecCode = info.event.extendedProps.recCode;
						// self.$refs.contactTaskModal.modalStates.modal1 = true;
						self.$refs.contactTaskModal.modalStates.modal1 = false;
						self.contactTaskRecCode = info.event.extendedProps.recCode;
						self.$nextTick(() => {
							self.$refs.contactTaskModal.modalStates.modal1 = true;
						});
					}
					else if ('PRO' == info.event.id) {
						// Expiry products
						const proCode = info.event.extendedProps.proCode;
						const pfcatCode = info.event.extendedProps.pfcatCode;

						switch (pfcatCode) {
							case 'FUND':
								self.fundModalHandler(proCode, pfcatCode);
								break;
							case 'ETF':
								self.etfModalHandler(proCode, pfcatCode);
								break;
							case 'FB':
								self.bondModalHandler(proCode, pfcatCode);
								break;
							case 'SP':
								self.spModalHandler(proCode, pfcatCode);
								break;
							case 'INS':
								self.insModalHandler(proCode, pfcatCode);
								break;
							// case 'DCI':
							// 	self.dciModalHandler(proCode, pfcatCode);
							// 	break;
							// case 'SEC':
							// 	self.secModalHandler(proCode, pfcatCode);
							// 	break;
							// case 'PFD':
							// 	self.pfdModalHandler(proCode, pfcatCode);
							// 	break;
						}
					}
				}
			});
			self.fullCalendar.render();
		},
		changeDisplayParam: function (displayParam) {
			const self = this;
			if (self.displayParam === displayParam) {
				self.displayParam = '';
				self.displayEvents = self.events;
			}
			else {
				self.displayParam = displayParam;
				self.displayEvents = self.events.filter(event => event.id !== displayParam);
			}
			self.renderCalendar();
		},
		formatDate: function (date) {
			let d = new Date(date),
				month = '' + (d.getMonth() + 1),
				day = '' + d.getDate(),
				year = d.getFullYear();

			if (month.length < 2) month = '0' + month;
			if (day.length < 2) day = '0' + day;

			return [year, month, day].join('-');
		},
		setIsShowSummary: function (val) {
			const self = this;
			self.isShowSummary = val;
		},
		doViewSummary: function (cusCode) {
			const self = this;
			self.isShowSummary = true;
			self.$refs.cusSummaryRef.initPage(cusCode);
		},
		getSelectReuseWord: function () {
			const self = this;
			const type = self.$route.path.split('/').filter(Boolean)[0].toUpperCase();
			self.$api.getReuseWordsApi({ type }).then(function (ret) {
				const result = ret.data;
				const needAppend = 5 - result.length;
				if (result.length < 5) {
					for (let i = 0; i < needAppend; i++) {
						result.push({ words: '', wordsId: ret.data.length + i + 1 });
					}
				}
				self.$refs.newTaskModal.selectReuseWord = result;
				self.$refs.newTaskModal.wobReuseWords = result;
				self.$refs.contactTaskModal.selectReuseWord = result;
				self.$refs.contactTaskModal.wobReuseWords = result;
			});
		},
		showSubordinate: function (roleType) {
			if (roleType != 'RM') {
				this.isShowSubordinate = true;
			}
		},
		// Expiry Product info display control
		fundModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.fundModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal['fund'] = true;
		},
		etfModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.etfModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.etfModalRef.getEtfDetail(proCode); // Product info/Fund data
			self.$refs.etfModalRef.getEtfStockHold(proCode); // Product info/ETF Holdings
			self.$refs.etfModalRef.getEtfPrice(proCode); // Product info/Price analysis data
			self.$refs.etfModalRef.getPricesChartData(proCode, 'Y', -1.0); // Product info/Price analysis chart
			self.$refs.etfModalRef.getEtfProfileNameMenu(proCode); // Product info/Performance analysis
			self.$refs.etfModalRef.getEtfProfileBenchmarksMenu(); // Product info/Performance analysis
			self.isOpenModal['etf'] = true;
		},
		bondModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.bondModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.bondModalRef.getBondPriceAna(proCode);
			self.$refs.bondModalRef.getPricesChartData(proCode, 'Y', -1.0); // Product info/Price analysis chart
			self.$refs.bondModalRef.getBondPerformances(proCode, 'Y', -1.0, null); // Product info/Performance analysis chart
			self.isOpenModal['bond'] = true;
		},
		// pfdModalHandler: function (proCode, pfcatCode) {
		// 	var self = this;
		// 	self.$refs.pfdModalRef.getProInfo(proCode, pfcatCode);
		// 	//			self.$refs.pfdModalRef.getEtfDetail(proCode); // Product info/Fund data
		// 	self.isOpenModal['pfd'] = true;
		// },
		spModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.isOpenModal['sp'] = true;
			self.$refs.spModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.spModalRef.getSpPriceAna(proCode); // Product info/Net value analysis data
			self.$refs.spModalRef.getSpNets(proCode, 'Y', -1.0); // Product info/Net value analysis chart
			self.$refs.spModalRef.getSpPerformances(proCode, 'Y', -1.0, null); // Product info/Performance analysis chart
		},
		insModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.insModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal['ins'] = true;
		}
		// dciModalHandler: function (proCode, pfcatCode) {
		// 	var self = this;
		// 	self.$refs.dciModalRef.getProInfo(proCode, pfcatCode); //Product basic data
		// 	self.$refs.dciModalRef.getDciPriceAna(proCode, 'M', -1.0); // Product info/Price analysis data
		// 	self.$refs.dciModalRef.getDciNets(proCode, 'Y', -1.0); // Product info/Price analysis chart
		// 	self.$refs.dciModalRef.getDciPerformances(proCode, 'Y', -1.0, null); // Product info/Performance analysis chart
		// 	self.isOpenModal['dci'] = true;
		// },
		// secModalHandler: function (proCode, pfcatCode) {
		// 	var self = this;
		// 	self.isOpenModal['sec'] = true;
		// 	self.$refs.secModalRef.getProInfo(proCode, pfcatCode);
		// 	self.$refs.secModalRef.getSecPriceAna(proCode); // Product info/Net value analysis data
		// 	self.$refs.secModalRef.getSecNets(proCode, 'Y', -1.0); // Product info/Net value analysis chart
		// 	self.$refs.secModalRef.getSecPerformances(proCode, 'Y', -1.0, null); // Product info/Performance analysis chart
		// }
	}
};
</script>

<style scoped>
:deep(.fc-event-main) {
	width: 100%;
}

:deep(.fc-event-title) {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

</style>
