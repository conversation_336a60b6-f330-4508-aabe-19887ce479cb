<template>
	<div>
		<!--頁面內容 start-->
		<div v-if="!isShowSummary" class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
				<h4>{{ $t('wob.searchCond') }}</h4>
			</div>
			<div id="collapseListGroup1" class="collapse show">
				<div class="card-body">
					<vue-form v-slot="{ errors }" ref="queryForm">
						<div class="row g-3 align-items-end">
							<div class="col-lg-6">
								<UserCondition
									ref="userCondition"
									:is-required="'area'"
									@change-area="minorAreaBranCode = $event"
									@change-bran="branCode = $event"
									@change-user="userCode = $event"
								/>
								<div style="height: 10px" />
							</div>
							<div class="col-lg-3">
								<label class="form-label">{{ $t('wob.customerIdTaxId') }} </label>
								<vue-field
									id="cusIdentity"
									v-model="cusCode"
									name="cusIdentity"
									class="form-control"
									type="text"
									size="15"
									maxlength="20"
									:class="{ 'is-invalid': errors.cusIdentity }"
									:label="$t('wob.customerIdTaxId')"
									rules="min:8|max:20"
								/>
								<div style="height: 10px">
									<span v-show="errors.cusIdentity" class="text-danger">{{ $t('wob.pleaseEnterCompleteCustomerIdTaxId') }}</span>
								</div>
							</div>
							<div class="col-lg-3">
								<label class="form-label">{{ $t('wob.customerName') }}</label>
								<vue-field
									id="cusName"
									v-model="cusName"
									name="cusName"
									class="form-control"
									type="text"
									size="15"
									maxlength="20"
									:class="{ 'is-invalid': errors.cusName }"
									:label="$t('wob.customerName')"
								/>
								<div style="height: 10px">
									<span v-show="errors.cusName" class="text-danger" />
								</div>
							</div>
							<div class="col-lg-6">
								<label class="form-label tx-require">{{ $t('wob.submitDateRange') }}</label>
								<div class="input-group">
									<div class="flex-grow-1">
										<vue-field
											id="verifyStartDate"
											v-model="verifyStartDate"
											type="date"
											name="verifyStartDate"
											class="form-control"
											:class="{ 'is-invalid': errors.verifyStartDate }"
											:label="$t('wob.submitDateRangeFrom')"
											rules="required"
											:min="minDt"
											:max="maxDt"
										/>
										<div style="height: 10px">
											<span
												v-show="errors.verifyStartDate"
												class="text-danger"
											>{{ errors.verifyStartDate }}</span>
										</div>
									</div>
									<span class="input-group-text">~</span>
									<div class="flex-grow-1">
										<vue-field
											id="verifyEndDate"
											v-model="verifyEndDate"
											type="date"
											name="verifyEndDate"
											class="JQ-datepicker form-control"
											:class="{ 'is-invalid': errors.verifyEndDate }"
											:label="$t('wob.submitDateRangeTo')"
											rules="required"
											:min="verifyStartDate"
											:max="maxDt"
										/>
										<div style="height: 10px">
											<span
												v-show="errors.verifyEndDate"
												class="text-danger"
											>{{ errors.verifyEndDate }}</span>
										</div>
									</div>
								</div>
							</div>
							<div class="col-lg-6">
								<label class="form-label">{{ $t('wob.processDateRange') }}</label>
								<div class="input-group">
									<div class="flex-grow-1">
										<!-- TODO: (起) 沒翻譯 -->
										<vue-field
											id="createStartDate"
											v-model="createStartDate"
											type="date"
											name="createStartDate"
											class="form-control"
											:class="{ 'is-invalid': errors.createStartDate }"
											:label="$t('wob.processDateRange') + '(起)'"
											:min="minDt"
											:max="maxDt"
										/>
										<div style="height: 10px">
											<span
												v-show="errors.createStartDate"
												class="text-danger"
											>{{ errors.createStartDate }}</span>
										</div>
									</div>
									<span class="input-group-text">~</span>
									<div class="flex-grow-1">
										<!-- TODO: (迄) 沒翻譯 -->
										<vue-field
											id="createEndDate"
											v-model="createEndDate"
											type="date"
											name="createEndDate"
											class="JQ-datepicker form-control"
											:class="{ 'is-invalid': errors.endDate }"
											:label="$t('wob.processDateRange') + '(迄)'"
											:min="createStartDate"
											:max="maxDt"
										/>
										<div style="height: 10px">
											<span
												v-show="errors.createEndDate"
												class="text-danger"
											>{{ errors.createEndDate }}</span>
										</div>
									</div>
								</div>
							</div>
							<div class="col-lg-6">
								<label class="form-label">{{ $t('wob.reviewStatus') }}</label>
								<div class="input-group">
									<div v-for="verifyStatus in verifyStatusMenu" class="form-check form-check-inline">
										<input
											v-model="verifyStatusCodes"
											class="form-check-input"
											type="checkbox"
											name="verifyStatus"
											:value="verifyStatus.codeValue"
											data-active-yn="Y"
											:label="$t('wob.reviewStatus')"
										>
										<label class="form-check-label">{{ verifyStatus.codeName }}</label>
									</div>
									<div style="height: 10px">
										<span v-show="errors.tdItems" class="text-danger" style="height: 3px">
											{{ errors.tdItems }} </span>
									</div>
								</div>
							</div>
							<div class="form-footer col-12">
								<button class="btn btn-primary btn-search" @click.prevent="gotoPage(0)">
									{{ $t('wob.search') }}
								</button>
							</div>
						</div>
					</vue-form>
				</div>
			</div>
		</div>
		<div id="searchResult">
			<div class="card card-table-list">
				<div class="card-header">
					<h4>{{ $t('wob.serviceRecordList') }}</h4>
					<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
				</div>
				<!-- 待審核清單 -->
				<div class="table-responsive">
					<table class="table table-RWD table-list text-center">
						<thead>
							<tr>
								<th>{{ $t('wob.submitDate') }}<br>{{ $t('wob.processingDate') }}</th>
								<th>
									{{ $t('wob.customerIdCard') }}<br>
									{{ $t('wob.customerName') }}
								</th>
								<th>{{ $t('wob.subject') }}</th>
								<th>{{ $t('wob.processingContent') }}</th>
								<th>{{ $t('wob.filingUnit') }}<br>{{ $t('wob.filingPersonnel') }}</th>
								<th>{{ $t('wob.reviewStatus') }}</th>
								<th>{{ $t('wob.reviewSupervisor') }}</th>
								<th class="wd-100">
									{{ $t('wob.pleaseSelect2') }}
								</th>
								<th class="wd-10p">
									{{ $t('wob.returnReason') }}
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="item in pageData.content">
								<td class="flex word-break" :data-th="$t('wob.submitDate') + ' ' + $t('wob.processingDate')">
									{{ $filters.formatDate(item.toVerifyDt) }}<br>
									<span v-if="item.originalStatusCode != 'P'">{{ $filters.formatDate(item.createDt) }}</span>
								</td>
								<td class="flex word-break" :data-th="$t('wob.customerIdCard') + ' ' + $t('wob.customerName')">
									<ul class="list-unstyled mb-0">
										<li>{{ item.cusCode }}</li>
										<li>
											<a
												class="tx-link"
												href="#"
												@click="doViewSummary(item.cusCode)"
											>{{ item.cusName }}</a>
										</li>
									</ul>
								</td>
								<td :data-th="$t('wob.subject')">
									{{ item.actionName }}
								</td>
								<td :data-th="$t('wob.processingContent')">
									{{ item.memo }}
								</td>
								<td class="word-break" :data-th="$t('wob.filingUnit') + ' ' + $t('wob.filingPersonnel')">
									{{ item.createBranCode }} {{ item.createBranName }}<br>{{ item.createBy }} {{ item.createName }}
								</td>
								<td :data-th="$t('wob.reviewStatus')">
									{{ item.verifyStatusName }}
								</td>
								<td :data-th="$t('wob.reviewSupervisor')">
									{{ item.verifyUserCode }}&nbsp;{{ item.verifyUserName }}
								</td>
								<td :data-th="$t('wob.pleaseSelect2')" class="text-start">
									<ul class="list-unstyled mb-0">
										<!-- 只有「待覆核」才會顯示審核狀態選項 -->
										<li v-if="verifyOption(item)">
											<div class="form-check">
												<input
													:id="item.tdCode + 'P0'"
													v-model="item.verifyStatusCode"
													class="form-check-input"
													type="radio"
													:name="item.tdCode"
													value="P"
												>
												<label class="form-check-label" :for="item.tdCode + 'P0'">{{ $t('wob.pendingReview') }}</label>
											</div>
											<div class="form-check">
												<input
													:id="item.tdCode + 'A0'"
													v-model="item.verifyStatusCode"
													class="form-check-input"
													type="radio"
													:name="item.tdCode"
													value="A"
												>
												<label class="form-check-label" :for="item.tdCode + 'A0'"> {{ $t('wob.reviewCompleted') }}</label>
											</div>
											<div class="form-check">
												<input
													:id="item.tdCode + 'R0'"
													v-model="item.verifyStatusCode"
													class="form-check-input"
													type="radio"
													:name="item.tdCode"
													value="R"
												>
												<label class="form-check-label" :for="item.tdCode + 'R0'">{{ $t('wob.returnForModification') }}</label>
											</div>
										</li>
									</ul>
								</td>
								<td :data-th="$t('wob.returnReason')">
									<!-- 待覆核 to 退回修改 -->
									<vue-field
										v-if="
											enableVerifyOption && item.originalStatusCode == 'P' && item.verifyStatusCode == 'R' && enableVerifyOption
										"
										v-model="item.reason"
										as="textarea"
										class="form-control"
										:name="'verifyMemo_' + item.tdCode"
										:rules="{ max: 100 }"
										:label="$t('wob.returnReason')"
										rows="3"
									/>
									<!-- 待覆核 to 覆核完成 or 待覆核 to 待覆核-->
									<vue-field
										v-if="enableVerifyOption && item.originalStatusCode == 'P' && item.verifyStatusCode !== 'R'"
										as="textarea"
										class="form-control"
										:name="'verifyMemo_' + item.tdCode"
										:rules="{ max: 100 }"
										:label="$t('wob.returnReason')"
										readonly="true"
										rows="3"
									>
										{{ item.reason }}
									</vue-field>
									<!-- 退回修改 -->
									<textarea
										v-if="item.originalStatusCode == 'R'"
										class="form-control"
										rows="3"
										readonly="true"
									>{{ item.reason }}</textarea>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>

			<div class="mt-3 text-end">
				<input
					class="btn btn-primary btn-lg btn-glow"
					type="button"
					:value="$t('wob.submit')"
					@click="savePage()"
				>
			</div>
		</div>
	</div>
	<vue-cus-summary ref="cusSummary" :set-is-show-summary="setIsShowSummary" />

	<!--頁面內容 end -->

	<vue-modal :is-open="isOpenModal" @close="() => (isOpenModal = false)">
		<template #content="props">
			<div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('wob.view') }}
						</h4>
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click.prevent="props.close()"
						/>
					</div>
					<div class="modal-body">
						<div class="table-responsive">
							<table class="table table-RWD table-bordered">
								<thead>
									<tr>
										<th>{{ $t('wob.eventNotificationCategory') }}</th>
										<th>{{ $t('wob.eventCategory') }}</th>
										<th>{{ $t('wob.daysBeforeEvent') }}<br>{{ $t('wob.beforeChange') }}</th>
										<th>{{ $t('wob.reminderDays') }}<br>{{ $t('wob.beforeChange') }}</th>
										<th>{{ $t('wob.statusChange') }}<br>{{ $t('wob.beforeChange') }}</th>

										<th>{{ $t('wob.daysBeforeEvent') }}<br>{{ $t('wob.afterChange') }}</th>
										<th>{{ $t('wob.reminderDays') }}<br>{{ $t('wob.afterChange') }}</th>
										<th>{{ $t('wob.statusChange') }}<br>{{ $t('wob.afterChange') }}</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<th :data-th="$t('wob.eventNotificationCategory')">
											{{ tdItemsVerifyView.tdcat1Name }}
										</th>
										<th :data-th="$t('wob.eventCategory')">
											{{ tdItemsVerifyView.itemName }}
										</th>
										<td :data-th="$t('wob.daysBeforeEvent') + $t('wob.beforeChange')">
											{{ tdItemsVerifyView.earlySendDayOrg }}
										</td>
										<td :data-th="$t('wob.reminderDays') + $t('wob.beforeChange')">
											{{ tdItemsVerifyView.expProcDayOrg }}
										</td>
										<td :data-th="$t('wob.statusChange') + $t('wob.beforeChange')">
											{{ tdItemsVerifyView.activeYnNameOrg }}
										</td>

										<td :data-th="$t('wob.daysBeforeEvent') + $t('wob.afterChange')">
											{{ tdItemsVerifyView.earlySendDayMod }}
										</td>
										<td :data-th="$t('wob.reminderDays') + $t('wob.afterChange')">
											{{ tdItemsVerifyView.expProcDayMod }}
										</td>
										<td :data-th="$t('wob.assetStatusChange') + $t('wob.afterChange')">
											<!-- TODO: activeYnNameMod 目前沒被 reassign，需確認 spec -->
											<span :style="{ color: activeYnNameMod == '停用' ? 'red' : 'black' }">
												{{	tdItemsVerifyView.activeYnNameMod }}
											</span>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>

					<div class="modal-footer">
						<input
							class="btn btn-white"
							type="button"
							:value="$t('wob.close')"
							@click.prevent="props.close()"
						>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import { Field, Form } from 'vee-validate';
import pagination from '@/views/components/pagination.vue';
import vueCusSummary from '@/views/cus/include/cusSummary.vue';
import vueModal from '@/views/components/model.vue';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		'vue-pagination': pagination,
		vueModal,
		vueCusSummary
	},
	data: function () {
		return {
			// API 用參數
			minorAreaBranCode: null, // 組織(區)
			allBranCodes: [], // 組織(分行)選單內所有代碼列表
			branCode: null, // 組織(分行)
			allUserCodes: [], // 業務人員選單內所有代碼列表
			userCode: null, // 業務人員
			createStartDate: null, // 處理日期區間(起)
			createEndDate: null, // 處理日期區間(迄)
			cusName: null, // 客戶姓名
			idn: null, // 客戶ID/統編
			verifyEndDate: null, // 送審日期區間(起)
			verifyStartDate: null, // 送審日期區間(迄)
			verifyStatusCodes: [], // 覆核狀態(可多選)
			activeYnNameMod: '停用', // 狀態變更異動後類型
			// 可使用審核權限角色
			enableVerifyOption: null,

			// 選單
			verifyStatusMenu: [], // 處理狀態 選單

			// 查詢結果
			pageData: {
				content: []
			},
			// 分頁元件
			pageable: {
				page: 0,
				size: 20,
				sort: 'CREATE_DT ',
				direction: 'DESC'
			},

			//

			tdItemsVerifyList: [],
			verifyOptionList: [],
			tdItemsVerifyView: [],
			isOpenModal: null,
			isShowSummary: false,
			cusCode: null,
			verifyId: null,
			itemCode: null,
			minDt: null,
			maxDt: null,
			status: null,
			verifyMemo: null
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo'])
	},
	watch: {
		minorAreaBranCode: function (newVal) {
			const self = this;
			self.branMenu = [];
			self.userMenu = [];
			self.userCode = '';
			self.branCode = '';

			if (newVal) {
				self.getBranMenu();
			}
		},
		branCode: function (newVal) {
			const self = this;
			self.userMenu = [];
			self.userCode = '';
			if (newVal === 'ALL') {
				return;
			}
			if (newVal) {
				self.getUserMenu();
			}
		}
	},
	mounted: function () {
		const self = this;
		self.getVerifyStatusMenu();
		self.getEnableVerifyOption();
	},
	methods: {
		// 分行
		getBranMenu: function () {
			const self = this;
			self.$api
				.getBranchesApi({
					minorCode: self.minorAreaBranCode
				})
				.then(function (ret) {
					self.allBranCodes = self.branMenu.map(item => item.branCode);
				});
		},
		// 業務人員選單
		getUserMenu: function () {
			const self = this;

			let branCode = null;
			if (!_.isBlank(self.minorAreaBranCode)) {
				branCode = self.minorAreaBranCode;
			}

			if (!_.isBlank(self.branCode)) {
				branCode = self.branCode;
			}

			self.$api
				.getBranEmployeeApi({
					branCode: branCode
				})
				.then(function (ret) {
					self.userMenu = ret.data;
				});
		},
		// 取得可使用審核權限角色
		getEnableVerifyOption: function () {
			const self = this;
			self.$api.getHasEnableVerifyOptionApi().then(function (ret) {
				self.enableVerifyOption = ret.data;
			});
		},

		// 處理狀態
		getVerifyStatusMenu: function () {
			const self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'WOB_STATUS_DEF'
				})
				.then(function (ret) {
					self.verifyStatusMenu = ret.data;
				});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			const self = this;
			const { queryForm, userCondition } = self.$refs;
			const allPass = (await Promise.all([queryForm.validate(), userCondition.validate()])).filter(pass => !pass.valid).length == 0;
			if (!allPass) {
				return;
			}

			const url = _.toPageUrl('', page, self.pageable);
			let allBranCodes = null;
			let branCode = null;
			let userCode = null;

			if (!_.isEmpty(self.branCode)) {
				if (self.branCode == 'ALL') {
					allBranCodes = self.allBranCodes;
				}
				else {
					branCode = self.branCode;
				}
			}

			if (!_.isEmpty(self.userCode)) {
				if (self.userCode == 'ALL') {
					allBranCodes = self.allUserCodes;
				}
				else {
					userCode = self.userCode;
				}
			}

			self.$api
				.getWobTdRecVerifiesApi(
					{
						minorAreaBranCode: self.minorAreaBranCode,
						buCode: self.userInfo.buCode,
						createStartDate: self.createStartDate ? moment(self.createStartDate).format('YYYY-MM-DD') : null,
						createEndDate: self.createEndDate ? moment(self.createEndDate).format('YYYY-MM-DD') : null,
						cusName: self.cusName,
						idn: self.idn,
						verifyStartDate: moment(self.verifyStartDate).format('YYYY-MM-DD'),
						verifyEndDate: moment(self.verifyEndDate).format('YYYY-MM-DD'),
						verifyStatusCodes: self.verifyStatusCodes?.join(','),
						allBranCodes: allBranCodes,
						branCode: branCode,
						userCode: userCode
					},
					url
				)
				.then(function (ret) {
					self.pageData = ret.data;
					_.forEach(self.pageData.content, function (item) {
						item.originalStatusCode = item.verifyStatusCode;
					});
				});
		},
		// 儲存審核狀態清單
		savePage: function () {
			const self = this;
			self.saveThenQueryPage(self.pageable.page); // 儲存後重新查詢分頁
		},
		// 儲存審核狀態清單, 再進行分業查詢
		saveThenQueryPage: function (page) {
			const self = this;
			// P:待覆核, A:覆核完成, R:退回修改

			// 頁面必填欄位檢核
			self.$refs.queryForm.validate().then(function (pass) {
				if (pass) {
					// 篩選「待覆核 to 覆核完成」, 或「待覆核 to 退回修改」的資料
					const updateItems = [];
					_.forEach(self.pageData.content, function (item) {
						if (item.originalStatusCode == 'P' && item.verifyStatusCode != 'P') {
							updateItems.push(item);
						}
					});

					if (_.isEmpty(updateItems)) {
						self.$bi.alert(self.$t('wob.noDataToUpdate'));
						return;
					}
					// 若審核狀態為「待覆核 to 退回 」，需填寫退回原因
					const isValidMemo = _.every(updateItems, function (item) {
						if (item.originalStatusCode == 'P' && item.verifyStatusCode == 'R') {
							return !_.isBlank(item.reason);
						}
						return true;
					});
					if (!isValidMemo) {
						self.$bi.alert(self.$t('wob.pleaseEnterReturnReason'));
						return;
					}

					// 儲存審核狀態清單
					self.$api.patchWobTdRecVerifiesApi(updateItems).then(function (ret) {
						if (!_.isNil(page)) {
							self.gotoPage(page);
						}
						self.$bi.alert(self.$t('wob.modificationSuccessful'));
					});
				}
			});
		},
		// 待覆核、覆核完成、退回修改按鈕是否顯示
		verifyOption: function (item) {
			const self = this;
			if (!self.enableVerifyOption) {
				return false;
			}
			if (self.userInfo.roleType === 'BM' || self.userInfo.roleType === 'RM') {
				if (item.createBranCode !== self.userInfo.branCode) {
					return false;
				}
				if (item.createBy === self.userInfo.userCode) {
					return false;
				}
			}
			if (item.originalStatusCode !== 'P') {
				return false;
			}
			return true;
		},
		setIsShowSummary: function (val) {
			const self = this;
			self.isShowSummary = val;
		},
		doViewSummary: function (cusCode) {
			const self = this;
			self.isShowSummary = true;
			console.log('isShowSummary:', self.isShowSummary);
			self.connectCusCode = cusCode;
			self.$refs.cusSummary.setCusCode(cusCode);
		}
	}
};
</script>
