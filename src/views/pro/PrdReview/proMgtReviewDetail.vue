<template>
	<!-- Modal 2 {{ $t('pro.fund') }} -->
	<vue-modal :is-open="isOpenModal['fund']" @close="closeModal('fund')">
		<template #content="props">
			<vue-pro-mgt-fund-modal
				ref="fundMgtModal"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:action-type="actionType"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			/>
		</template>
	</vue-modal>
	<!-- Modal 2 End -->

	<!-- Modal 3 {{ $t('pro.trustStructuredProducts') }} -->
	<vue-modal :is-open="isOpenModal['sp']" @close="closeModal('sp')">
		<template #content="props">
			<vue-pro-mgt-sp-modal
				ref="spMgtModal"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:action-type="actionType"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			/>
		</template>
	</vue-modal>
	<!-- Modal 3 End -->

	<!-- Modal 4 {{ $t('pro.etf') }} -->
	<vue-modal :is-open="isOpenModal['etf']" @close="closeModal('etf')">
		<template #content="props">
			<vue-pro-mgt-etf-modal
				ref="etfMgtModal"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:action-type="actionType"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			/>
		</template>
	</vue-modal>
	<!-- Modal 4 End -->

	<!-- Modal 5 {{ $t('pro.bond') }} -->
	<vue-modal :is-open="isOpenModal['bond']" @close="closeModal('bond')">
		<template #content="props">
			<vue-pro-mgt-bond-modal
				ref="bondMgtModal"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:action-type="actionType"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			/>
		</template>
	</vue-modal>
	<!-- Modal 5 End -->

	<!-- Modal 6 {{ $t('pro.insurance') }} -->
	<vue-modal :is-open="isOpenModal['ins']" @close="closeModal('ins')">
		<template #content="props">
			<vue-pro-mgt-ins-modal
				ref="insMgtModal"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:action-type="actionType"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			/>
		</template>
	</vue-modal>
	<!-- Modal 6 End -->

	<!-- Modal 7 {{ $t('pro.compositeProductsBankStructured') }} DCI -->
	<vue-modal :is-open="isOpenModal['dci']" @close="closeModal('dci')">
		<template #content="props">
			<vue-pro-mgt-dci-modal
				ref="dciMgtModal"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:action-type="actionType"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			/>
		</template>
	</vue-modal>
	<!-- Modal 7 End -->

	<!-- Modal 9 {{ $t('pro.overseasStocks') }} PFD -->
	<vue-modal :is-open="isOpenModal['pfd']" @close="closeModal('pfd')">
		<template #content="props">
			<vue-pro-mgt-pfd-modal
				ref="pfdMgtModal"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:action-type="actionType"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			/>
		</template>
	</vue-modal>
	<!-- Modal 9 End -->
</template>
<script>
import _ from 'lodash';
import vueModal from '@/views/components/model.vue';
import vueProMgtFundModal from '@/views/pro/productInfo/include/fundMgtModal.vue';
import vueProMgtSpModal from '@/views/pro/productInfo/include/spMgtModal.vue';
import vueProMgtEtfModal from '@/views/pro/productInfo/include/etfMgtModal.vue';
import vueProMgtBondModal from '@/views/pro/productInfo/include/bondMgtModal.vue';
import vueProMgtInsModal from '@/views/pro/productInfo/include/insMgtModal.vue';
import vueProMgtDciModal from '@/views/pro/productInfo/include/dciMgtModal.vue';
import vueProMgtPfdModal from '@/views/pro/productInfo/include/pfdMgtModal.vue';
export default {
	components: {
		vueModal,
		vueProMgtFundModal,
		vueProMgtSpModal,
		vueProMgtEtfModal,
		vueProMgtBondModal,
		vueProMgtInsModal,
		vueProMgtDciModal,
		vueProMgtPfdModal
	},
	props: {},
	data: function () {
		return {
			actionType: null,
			gotoPage: () => void 0,
			proPriceRangeMenu: undefined,
			finReqCodeMenu: undefined,
			isOpenModal: {
				fund: false,
				sp: false,
				etf: false,
				pfd: false,
				bond: false,
				ins: false,
				dci: false,
				sec: false
			}
		};
	},
	methods: {
		getDetail: function (eventId) {
			const self = this;
			if (_.isBlank(eventId)) {
				return;
			}
			self.$api
				.getProductLogApi({
					eventId: eventId
				})
				.then(function (ret) {
					const item = ret.data;
					self.actionType = 'VIEW';
					self.benchmarkCode = null;
					self.benchmark = null;
					let eventId = null;
					if (item.status === 'P') {
						eventId = item.eventId;
					}
					if (item.pfcatCode == 'FUND') {
						self.$refs.fundMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.fund = true;
					}
					else if (item.pfcatCode == 'ETF') {
						self.$refs.etfMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.etf = true;
					}
					else if (item.pfcatCode == 'FB') {
						self.$refs.bondMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.bond = true;
					}
					else if (item.pfcatCode == 'INS') {
						self.$refs.insMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.ins = true;
					}
					else if (item.pfcatCode == 'DCD') {
						self.$refs.dciMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.dci = true;
					}
					else if (item.pfcatCode == 'SP') {
						self.$refs.spMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.sp = true;
					}
					else if (item.pfcatCode == 'SEC') {
						self.$refs.secMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.sec = true;
					}
					else if (item.pfcatCode == 'PFD') {
						self.$refs.pfdMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
						self.isOpenModal.pfd = true;
					}
					else {
						/*
          else if (
            item.pfcatCode == 'SP' ||
            item.pfcatCode == 'SSPF' ||
            item.pfcatCode == 'TSPD' ||
            item.pfcatCode == 'TSPF' ||
            item.pfcatCode == 'FSTD'
          ) {
            self.$refs.spModal.getProDatas(item.proCode, item.pfcatCode);
          }
          */
						self.$refs.proModal.getProDatas(item.proCode, item.pfcatCode);
					}
				});
		},
		downloadFile: function (proFile) {
			const self = this;
			const eventId = proFile.eventId;
			const proFileId = proFile.proFileId;
			const url = self.config.apiPath + '/pro/proFile';
			const fileName = proFile.showName;
			self.$api
				.downloadProFileApi({
					proFileId: proFileId,
					eventId: eventId
				})
				.then(function (data) {
					const link = document.createElement('a');
					const url = URL.createObjectURL(data);
					link.download = fileName;
					link.href = url;
					document.body.appendChild(link);
					link.click();
					link.remove();
					setTimeout(() => URL.revokeObjectURL(url), 1000);
				});
		},
		downloadOtherFile: function (fileId) {
			const self = this;
			self.$api.downloadOtherFileApi({
				fileId: fileId
			});
		},
		closeModal: function (modalName) {
			this.isOpenModal[modalName] = false;
		}
	}
};
</script>
