<template>
	<!-- modal -->
	<vue-modal :is-open="isOpenModal" @close="closeModal">
		<template #content="props">
			<div class="modal-dialog modal-dialog-centered modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('pro.newProductTempListing') }}
						</h4>
						<button type="button" class="btn-expand">
							<i class="bi bi-arrows-fullscreen" />
						</button>
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click.prevent="props.close()"
						/>
					</div>
					<div class="modal-body">
						<div class="tx-title">
							{{ $t('pro.productBasicInfo') }}
						</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th width="20%">
										{{ $t('pro.productMainCategory') }}
									</th>
									<td width="30%">
										{{ proPreview.pfcatName }}
									</td>
									<th width="20%">
										{{ $t('pro.productSubCategory') }}
									</th>
									<td width="30%">
										{{ proPreview.proTypeName }}
									</td>
								</tr>
								<tr>
									<th>{{ $t('pro.proCode') }}</th>
									<td>{{ proPreview.bankProCode }}</td>
									<th>{{ $t('pro.assetCategory') }}</th>
									<td>{{ proPreview.assetcatName }}</td>
								</tr>
								<tr>
									<th>{{ $t('pro.productChineseName') }}</th>
									<td>{{ proPreview.proName }}</td>
									<th>{{ $t('pro.productRiskAttribute') }}</th>
									<td>{{ proPreview.riskName }}</td>
								</tr>
								<tr>
									<th>{{ $t('pro.pricingCurrency') }}</th>
									<td>{{ proPreview.curName }}</td>
									<th>{{ $t('pro.salesTarget') }}</th>
									<td>{{ getTargetCusBuName(proPreview.targetCusBu) }}</td>
								</tr>
								<tr>
									<th>{{ $t('pro.piOnlySales') }}</th>
									<td>{{ proPreview.profInvestorYn === 'Y' ? $t('pro.yes') : $t('pro.no') }}</td>
									<th>{{ $t('pro.isDomesticFund') }}</th>
									<td>{{ proPreview.localYn === 'Y' ? $t('pro.yes') : $t('pro.no') }}</td>
								</tr>
								<tr v-if="!disabledFields">
									<th>{{ $t('pro.maturity') }}</th>
									<td>{{ proPreview.expireDt }}</td>
									<th>{{ $t('pro.status') }}</th>
									<td>{{ proPreview.actionName }}</td>
								</tr>
								<tr v-if="!disabledFields">
									<th>{{ $t('pro.fileUpload') }}</th>
									<td colspan="3">
										<span v-for="(item, index) in proPreview.files">
											<a
												v-if="item"
												href="#"
												class="tx-link"
												@click="downloadFile(item)"
											>{{ item.fileName }}</a>
											<br>
										</span>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
					<!--
				<div class="modal-body">
					<div class="tx-title">{{ $t('pro.productBasicInfo') }}</div>
					<table class="table table-bordered">

						<tbody>
							<tr>
								<th width="20%">{{ $t('pro.productMainCategory') }}</th>
								<td width="30%">{{proPreview.pfcatName}}</td>
								<th width="20%">{{ $t('pro.productSubCategory') }}</th>
								<td width="30%">{{proPreview.proTypeName}}</td>
							</tr>
							<tr>
								<th>{{ $t('pro.proCode') }}</th>
								<td>{{proPreview.bankProCode}}</td>
								<th>{{ $t('pro.assetCategory') }}</th>
								<td>{{proPreview.assetcatName}}</td>
							</tr>
							<tr>
								<th>{{ $t('pro.productChineseName') }}</th>
								<td>{{proPreview.proName}}</td>
								<th>{{ $t('pro.productRiskAttribute') }}</th>
								<td>{{proPreview.riskName}}</td>
							</tr>
							<tr>
								<th>{{ $t('pro.pricingCurrency') }}</th>
								<td>{{proPreview.curName}}</td>
								<th>{{ $t('pro.issuer') }}</th>
								<td>{{proPreview.issuerName}}</td>
							</tr>
							<tr>
								<th>{{ $t('pro.maturity') }}</th>
								<td>{{proPreview.expireDt}}</td>
								<th>{{ $t('pro.status') }}</th>
								<td>{{proPreview.actionName}}</td>
							</tr>
							<tr>
								<th>{{ $t('pro.fileUpload') }}</th>
                				<td colspan="3">
									<span v-for="(item, index) in proPreview.files">
										<a v-if="item" href="#" class="tx-link" @click="downloadFile(item)">{{item.fileName}}</a>
										<br>
									</span>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
				-->

					<div class="modal-footer">
						<input
							id="appointmentCloseButton"
							name="btnClose"
							class="btn btn-white"
							type="button"
							:value="$t('pro.close')"
							@click.prevent="props.close()"
						>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- modal end -->
</template>
<script>
import _ from 'lodash';
export default {
	props: {},
	data: function () {
		return {
			disabledFields: true,
			proPreview: {},
			isOpenModal: false,
			buCodeMenu: [
				{ codeValue: 'Y', codeName: '全部' },
				{ codeValue: 'D', codeName: 'DBU' },
				{ codeValue: 'O', codeName: 'OBU' }
			]
		};
	},
	methods: {
		getDetail: function (eventId) {
			const self = this;
			if (_.isBlank(eventId)) {
				return;
			}
			self.$api
				.getNewShelfProductList({
					eventId: eventId
				})
				.then(function (ret) {
					if (ret.data && ret.data.length > 0) {
						self.proPreview = ret.data[0];
					}
					self.isOpenModal = true;
				});
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		getTargetCusBuName(code) {
			const self = this;
			const item = self.buCodeMenu.find(item => item.codeValue === code);
			return item ? item.codeName : '';
		},
		downloadFile: function (proFile) {
			const self = this;
			const id = proFile.proFileId;
			self.$api.downloadFileApi({ fileId: id, fileType: 'ProFileNewLog' });
		}
	}
};
</script>
