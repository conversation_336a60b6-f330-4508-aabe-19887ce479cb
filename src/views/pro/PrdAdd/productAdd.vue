<template>
	<div>
		<div class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
				<h4>{{ title }}</h4>
				<span class="tx-square-bracket">{{ $t('pro.requiredFields') }}</span>
			</div>

			<vue-form v-slot="{ errors }" ref="queryForm">
				<div id="collapseListGroup1" class="collapse show">
					<form>
						<div class="card-body">
							<div class="form-row">
								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require">{{ $t('pro.productMainCategory') }}</label>
									<div>
										<vue-field
											id="pfcatCodeForAllProd"
											v-model="pfcatCode"
											as="select"
											class="form-select"
											rules="required"
											name="pfcatCodeForAllProd"
											:label="$t('pro.productMainCategory')"
											:class="{ 'is-invalid': errors.pfcatCodeForAllProd }"
											@change="
												getProTypeMenu(pfcatCode);
												getIssuersMenu(pfcatCode);
											"
										>
											<option v-for="item in pfcatsMenu" :value="item.pfcatCode">
												{{ item.pfcatName }}
											</option>
										</vue-field>
										<div style="height: 25px">
											<span v-show="errors.pfcatCodeForAllProd" class="text-danger">{{ errors.pfcatCodeForAllProd
											}}</span>
										</div>
									</div>
								</div>
								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require">{{ $t('pro.productSubCategory') }}</label>
									<div>
										<vue-field
											v-model="proTypeCode"
											as="select"
											class="form-select JQdata-hide"
											rules="required"
											name="proTypeCode"
											:label="$t('pro.productSubCategory')"
											:class="{ 'is-invalid': errors.proTypeCode }"
										>
											<option value="">
												--
											</option>
											<option v-for="item in proTypeMenu" :value="item.proTypeCode">
												{{ item.proTypeName }}
											</option>
										</vue-field>
										<div style="height: 25px">
											<span v-show="errors.proTypeCode" class="text-danger">{{ errors.proTypeCode }}</span>
										</div>
									</div>
								</div>
								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require">{{ $t('pro.proCode') }}</label>
									<div>
										<vue-field
											v-model="proCode"
											as="input"
											type="text"
											rules="required"
											name="proCode"
											:label="$t('pro.proCode')"
											:class="{ 'is-invalid': errors.proCode }"
											class="form-control"
										/>
										<div style="height: 25px">
											<span v-show="errors.proCode" class="text-danger">{{ errors.proCode }}</span>
										</div>
									</div>
								</div>
							</div>
							<div class="form-row">
								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label">{{ $t('pro.assetCategory') }}</label>
									<div>
										{{ assetcatName }}
									</div>
								</div>

								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require">{{ $t('pro.productChineseName') }}</label>
									<div>
										<vue-field
											id="prod_chinese_name"
											v-model="proName"
											as="input"
											class="form-control JQdata-hide"
											maxlength="20"
											rules="required"
											name="proName"
											:label="$t('pro.productChineseName')"
											:class="{ 'is-invalid': errors.proName }"
											size="25"
											type="text"
											value=""
										/>
										<div style="height: 25px">
											<span v-show="errors.proName" class="text-danger">{{ errors.proName }}</span>
										</div>
									</div>
								</div>

								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require">{{ $t('pro.productRiskAttribute') }}</label>
									<div>
										<vue-field
											id="riskCode"
											v-model="riskCode"
											as="select"
											class="form-select"
											rules="required"
											name="riskCode"
											:label="$t('pro.productRiskAttribute')"
											:class="{ 'is-invalid': errors.riskCode }"
										>
											<option value="">
												--
											</option>
											<option v-for="item in riskMenu" :value="item.riskCode">
												{{ item.riskName }}
											</option>
										</vue-field>
										<div style="height: 25px">
											<span v-show="errors.riskCode" class="text-danger">{{ errors.riskCode }}</span>
										</div>
									</div>
								</div>
							</div>
							<div class="form-row">
								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require">{{ $t('pro.pricingCurrency') }}</label>
									<div>
										<vue-field
											id="curMenuAll"
											v-model="curCode"
											as="select"
											class="form-select"
											:title="$t('pro.pleaseSelectCurrency')"
											rules="required"
											name="curCode"
											:label="$t('pro.pricingCurrency')"
											:class="{ 'is-invalid': errors.curCode }"
											data-style="btn-white"
										>
											<option value="">
												--
											</option>
											<option v-for="item in curOption" :key="index" :value="item.value">
												{{ item.name }}
											</option>
										</vue-field>
										<div style="height: 25px">
											<span v-show="errors.curCode" class="text-danger">{{ errors.curCode }}</span>
										</div>
									</div>
								</div>

								<div v-if="!disabledFields" class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require">{{ $t('pro.issuer') }}</label>
									<div>
										<vue-field
											id="issuerCode"
											v-model="issuerCode"
											as="select"
											class="form-select"
											:title="$t('pro.pleaseSelectInstitution')"
											rules="required"
											name="issuerCode"
											:label="$t('pro.issuer')"
											:class="{ 'is-invalid': errors.issuerCode }"
											data-style="btn-white"
										>
											<option value="">
												--
											</option>
											<option v-for="item in issuersMenu" :key="index" :value="item.issuerCode">
												{{ item.issuerName }}
											</option>
										</vue-field>
										<div style="height: 25px">
											<span v-show="errors.issuerCode" class="text-danger">{{ errors.issuerCode }}</span>
										</div>
									</div>
								</div>

								<div class="form-group col-12 col-lg-8">
									<label style="height: 30px" class="form-label tx-require">{{ $t('pro.salesTarget') }}</label>
									<div>
										<div v-for="(item, i) in buCodeMenu" class="form-check form-check-inline">
											<vue-field
												:id="'targetCusBu_' + i"
												v-model="targetCusBu"
												type="radio"
												class="form-check-input"
												name="targetCusBu"
												:value="item.codeValue"
												rules="required"
												:label="$t('pro.salesTarget')"
												:class="{ 'is-invalid': errors.targetCusBu }"
											/>
											<label :for="'targetCusBu_' + i" class="form-check-label">{{ item.codeName }}</label>
										</div>
										<div style="height: 25px">
											<span v-show="errors.targetCusBu" class="text-danger">{{ errors.targetCusBu }}</span>
										</div>
									</div>
								</div>

								<div class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require">{{ $t('pro.piOnlySales') }}</label>
									<div>
										<div v-for="(item, i) in profInvestorYnMenu" class="form-check form-check-inline">
											<vue-field
												:id="'profInvestorYn_' + i"
												v-model="profInvestorYn"
												type="radio"
												class="form-check-input"
												name="profInvestorYn"
												:value="item.codeValue"
												rules="required"
												:label="$t('pro.piOnlySales')"
												:class="{ 'is-invalid': errors.profInvestorYn }"
											/>
											<label :for="'profInvestorYn_' + i" class="form-check-label">{{ item.codeName }}</label>
										</div>
										<div style="height: 25px">
											<span v-show="errors.profInvestorYn" class="text-danger">{{ errors.profInvestorYn }}</span>
										</div>
									</div>
								</div>

								<div v-if="pfcatCode === 'FUND'" class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label tx-require">{{ $t('pro.isDomesticFund') }}</label>
									<div>
										<div v-for="(item, i) in localYnMenu" class="form-check form-check-inline">
											<vue-field
												:id="'localYn_' + i"
												v-model="localYn"
												type="radio"
												class="form-check-input"
												name="localYn"
												:value="item.codeValue"
												rules="required"
												:label="$t('pro.isDomesticFund')"
												:class="{ 'is-invalid': errors.localYn }"
											/>
											<label :for="'localYn_' + i" class="form-check-label">{{ item.codeName }}</label>
										</div>
										<div style="height: 25px">
											<span v-show="errors.localYn" class="text-danger">{{ errors.localYn }}</span>
										</div>
									</div>
								</div>

								<div v-if="false" class="form-group col-12 col-lg-4">
									<label style="height: 30px" class="form-label">{{ $t('pro.bondRating') }}</label>
									<div>
										<select
											id="bondCode"
											v-model="bondCode"
											class="form-select JQdata-hide"
											name="bondCode"
										>
											<option value="">
												--
											</option>
											<option value="1">
												AAA
											</option>
											<option value="2">
												AA+
											</option>
											<option value="3">
												BBB
											</option>
											<option value="4">
												BB+
											</option>
											<option value="5">
												CCC
											</option>
											<option value="6">
												CC+
											</option>
										</select>
										<div style="height: 25px">
											<span class="text-danger" />
										</div>
									</div>
								</div>

								<div v-if="pfcatCode === 'ETF' || pfcatCode === 'FB' || pfcatCode === 'PFD'" class="form-row">
									<div class="form-group col-12 col-lg-4">
										<label class="form-label tx-require">{{ $t('pro.maturity') }}</label>
										<vue-field
											id="expiredDate"
											v-model="expiredDate"
											type="date"
											class="form-control JQdata-hide"
											maxlength="20"
											rules="required"
											name="expiredDate"
											:label="$t('pro.maturity')"
											:class="{ 'is-invalid': errors.expiredDate }"
											size="25"
											style="min-width: 200px"
										/>
									</div>
									<div style="height: 45px">
										<span v-show="errors.expiredDate" class="text-danger">{{ errors.expiredDate }}</span>
									</div>
								</div>
							</div>
							<div class="form-row">
								<div v-if="!disabledFields" class="form-group col-12 col-lg-8">
									<label class="form-label">{{ $t('pro.fileUpload') }}</label>
									<div class="row g-2">
										<div class="input-group">
											<input
												ref="fileInput"
												class="form-control form-file"
												type="file"
												size="30"
												accept="application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
												@change="triggerFile($event)"
											>
											<button class="btn btn-info btn-glow" type="button" @click="doUploadFile()">
												{{ $t('pro.upload') }}
											</button>
										</div>

										<ul class="list-group list-inline-tags mt-2">
											<li v-for="(item, index) in files" class="list-group-item">
												<a href="#">
													<span v-if="item">{{ item.fileName }}</span>
													<span
														v-if="item"
														class="img-delete"
														data-bs-toggle="tooltip"
														style="right: -25px"
														:title="$t('pro.delete')"
														@click="deleteFiles(item.fileId)"
													/>
												</a>
											</li>
										</ul>
									</div>
								</div>
							</div>

							<div class="form-footer">
								<input
									class="btn btn-primary JQdata-hide"
									type="button"
									:value="$t('pro.submitForReview')"
									@click="submit()"
								>
								<input
									class="btn btn-primary JQdata-show"
									type="button"
									:value="$t('pro.cancelModify')"
									@click="cancel()"
								>
							</div>
						</div>
					</form>
				</div>
			</vue-form>
		</div>

		<div class="card card-table">
			<div class="card-header">
				<h4>{{ $t('pro.newProductDataList') }}</h4>
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-hover">
					<thead>
						<tr>
							<th width="11%">
								{{ $t('pro.productMainCategory') }}
							</th>
							<th width="8%">
								{{ $t('pro.proCode') }}
							</th>
							<th width="14%">
								{{ $t('pro.productChineseName') }}
							</th>
							<th width="8%">
								{{ $t('pro.productRiskLevel') }}
							</th>
							<th width="8%">
								{{ $t('pro.pricingCurrency') }}
							</th>
							<th v-if="!disabledFields" width="8%">
								{{ $t('pro.issuer') }}
							</th>
							<th v-if="!disabledFields" width="8%">
								{{ $t('pro.maturity') }}
							</th>
							<th width="8%">
								{{ $t('pro.professionalInvestorProduct') }}
							</th>
							<th width="8%">
								{{ $t('pro.salesTarget') }}
							</th>
							<th width="8%">
								{{ $t('pro.status') }}
							</th>
							<th width="14%">
								{{ $t('pro.modificationPerson') }}
							</th>
							<th width="8%">
								{{ $t('pro.modificationTime') }}
							</th>
							<th width="10%" class="text-center">
								{{ $t('pro.execute') }}
							</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="item in productList">
							<td>{{ item.pfcatName }}</td>
							<td>{{ item.bankProCode }}</td>
							<td>{{ item.proName }}</td>
							<td>{{ item.riskName }}</td>
							<td>{{ item.curName }}</td>
							<td v-if="!disabledFields">
								{{ item.issuerName }}
							</td>
							<td v-if="!disabledFields">
								{{ item.expireDt }}
							</td>
							<td>{{ item.profInvestorYn === 'Y' ? $t('pro.yes') : $t('pro.no') }}</td>
							<td>{{ getTargetCusBuName(item.targetCusBu) }}</td>
							<td>{{ item.modifyBy }} {{ item.modifyName }}</td>
							<td>
								<template v-if="item.wkfStatus !== 'R'">
									{{ item.actionName }}
								</template>
								<a
									v-if="item.wkfStatus === 'R'"
									class="tx-link"
									href="#"
									@click="showRejectMsg(item)"
								>
									{{ item.actionName }}
								</a>
							</td>
							<td>{{ formatDtTime(item.modifyDt) }}</td>
							<td class="text-center">
								<a
									data-bs-toggle="tooltip"
									href="#"
									class="table-icon"
									:title="$t('pro.view')"
									@click="preview(item.proCode)"
								>
									<button type="button" class="btn btn-dark btn-icon"><i class="bi bi-search" /></button>
								</a>

								<a
									v-if="item.wkfStatus !== 'P'"
									id="edit"
									data-bs-toggle="tooltip"
									href="#"
									class="table-icon"
								>
									<button
										type="button"
										class="btn btn-info btn-icon"
										data-bs-toggle="tooltip"
										:title="$t('pro.edit')"
										@click="edit(item.proCode)"
									>
										<i class="fa-solid fa-pen" />
									</button>
								</a>

								<a
									v-if="item.wkfStatus !== 'P'"
									id="delete"
									data-bs-toggle="tooltip"
									href="#"
									class="table-icon"
								>
									<button
										type="button"
										class="btn btn-info btn-icon"
										data-bs-toggle="tooltip"
										:title="$t('pro.delete')"
										@click="deleteItem(item.proCode, item.eventId)"
									>
										<i class="fa-solid fa-trash" />
									</button>
								</a>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>

		<div class="tx-note">
			<ol>
				<li>{{ $t('pro.funcDataForSameDay') }}</li>
				<li>{{ $t('pro.dataDeletedDaily') }}</li>
				<li>{{ $t('pro.officialDataRefresh') }}</li>
			</ol>
		</div>

		<!-- modal -->
		<vue-modal :is-open="isOpenModal" @close="isOpenModal = false">
			<template #content="props">
				<div class="modal-dialog modal-lg modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								{{ $t('pro.newProductTempListing') }}
							</h4>
							<button type="button" class="btn-expand">
								<i class="bi bi-arrows-fullscreen" />
							</button>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="props.close()"
							/>
						</div>
						<div class="modal-body">
							<div class="tx-title">
								{{ $t('pro.productBasicInfo') }}
							</div>
							<table class="table table-bordered">
								<tbody>
									<tr>
										<th width="20%">
											{{ $t('pro.productMainCategory') }}
										</th>
										<td width="30%">
											{{ proPreview.pfcatName }}
										</td>
										<th width="20%">
											{{ $t('pro.productSubCategory') }}
										</th>
										<td width="30%">
											{{ proPreview.proTypeName }}
										</td>
									</tr>
									<tr>
										<th>{{ $t('pro.proCode') }}</th>
										<td>{{ proPreview.bankProCode }}</td>
										<th>{{ $t('pro.assetCategory') }}</th>
										<td>{{ proPreview.assetcatName }}</td>
									</tr>
									<tr>
										<th>{{ $t('pro.productChineseName') }}</th>
										<td>{{ proPreview.proName }}</td>
										<th>{{ $t('pro.productRiskAttribute') }}</th>
										<td>{{ proPreview.riskName }}</td>
									</tr>
									<tr>
										<th>{{ $t('pro.pricingCurrency') }}</th>
										<td>{{ proPreview.curName }}</td>
										<th>{{ $t('pro.salesTarget') }}</th>
										<td>{{ getTargetCusBuName(proPreview.targetCusBu) }}</td>
									</tr>
									<tr>
										<th>{{ $t('pro.piOnlySales') }}</th>
										<td>{{ proPreview.profInvestorYn === 'Y' ? $t('pro.yes') : $t('pro.no') }}</td>
										<template v-if="proPreview.pfcatCode === 'FUND'">
											<th>{{ $t('pro.isDomesticFund') }}</th>
											<td>{{ proPreview.localYn === 'Y' ? $t('pro.yes') : $t('pro.no') }}</td>
										</template>
										<template
											v-if="proPreview.pfcatCode === 'ETF' || proPreview.pfcatCode === 'FB' || proPreview.pfcatCode === 'PFD'"
										>
											<th>{{ $t('pro.maturity') }}</th>
											<td>{{ proPreview.expireDt }}</td>
										</template>
									</tr>
									<tr v-if="!disabledFields">
										<th>{{ $t('pro.status') }}</th>
										<td>{{ proPreview.actionName }}</td>
									</tr>
									<tr v-if="!disabledFields">
										<th>{{ $t('pro.fileUpload') }}</th>
										<td colspan="3">
											<span v-for="(item) in proPreview.files">
												<a
													v-if="item"
													href="#"
													class="tx-link"
													@click="downloadFile(item)"
												>{{ item.fileName }}</a>
												<br>
											</span>
										</td>
									</tr>
								</tbody>
							</table>
						</div>

						<div class="modal-footer">
							<input
								id="appointmentCloseButton"
								name="btnClose"
								class="btn btn-white"
								type="button"
								:value="$t('pro.close')"
								@click.prevent="props.close()"
							>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- modal end -->
	</div>
	<!--頁面內容 end-->
</template>
<script>
import vueModal from '@/views/components/model.vue';
import { Form, Field } from 'vee-validate';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal
	},
	data: function () {
		return {
			disabledFields: true,
			title: '新增資料',
			pfcatCode: null, // 商品主類
			proTypeCode: null, // 商品次類
			proCode: null, // 商品代碼
			assetcatCode: null, // 資產類別
			assetcatName: null, // 資產類別名稱
			proName: null, // 商品中文名稱
			riskCode: null, // 商品風險屬性
			curCode: null, // 計價幣別
			issuerCode: null, // 發行機構
			bondCode: null, // 債券評級
			expiredDate: null, // 到期日
			targetCusBu: null, // 銷售對象
			profInvestorYn: null, // 限PI銷售
			localYn: null, // 是否為國內基金
			fileList: {
				fileName: null,
				fileResult: null,
				fileCnt: 0
			},
			productList: [], // 商檢商品資料列表
			pfcatsMenu: [], // 商品主類選單
			proTypeMenu: [], // 商品次類選單
			curOption: [], // 幣別選單
			riskMenu: [], // 風險等級選單
			assetcatsMenu: [], // 資產類別選單
			issuersMenu: [], // 發行機構選單
			proPreview: {}, // 檢視欄位

			// File 用參數
			files: [],
			uploadFile: null,
			uploadFiles: [],
			buCodeMenu: [],
			profInvestorYnMenu: [],
			localYnMenu: [],
			isOpenModal: false
		};
	},
	computed: {},
	watch: {
		proTypeCode(newVal) {
			if (newVal) {
				const self = this;
				self.getAssetCasts(newVal);
			}
		},
		pfcatCode: {
			immediate: true,
			handler: function (newVal) {
				const self = this;
				if (newVal) {
					if (newVal !== 'FUND') {
						self.localYn = null;
					}
					if (!(newVal === 'ETF' || newVal === 'FB' || newVal === 'PFD')) {
						self.expiredDate = null;
					}
				}
			}
		}
	},
	mounted: function () {
		const self = this;
		Promise.all([
			self.fetchInitDataSelectYn(),
			self.fetchInitDataBuCodeMenu(),
			self.getProPfcatsMenu(),
			// self.getAssetcatsMenu(),
			self.getcurrenciesMenu(),
			self.getRiskMenu()
		]).then(() => {
			self.getNewShelfProductList(); // 取得商檢商品資料列表
			// self.getIssuersMenu(); // 取得發行機構
		});
	},
	methods: {
		fetchInitDataSelectYn: function () {
			const self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'SELECT_YN'
				})
				.then(function (ret) {
					self.profInvestorYnMenu = ret.data;
					self.localYnMenu = ret.data;
				});
		},
		fetchInitDataBuCodeMenu: function () {
			const self = this;
			self.$api
				.getAdmCodeDetail({
					codeType: 'CUS_BU'
				})
				.then(function (ret) {
					self.buCodeMenu = ret.data;
				});
		},
		// 取得商品資料列表
		getNewShelfProductList: function () {
			const self = this;
			self.$api.getNewShelfProductList().then(function (ret) {
				self.productList = ret.data;
			});
		},
		// 取得商品主類
		getProPfcatsMenu: function () {
			const self = this;
			self.$api.getNewProPfcatsMenuApi().then(function (ret) {
				// const needPfcatCode = ['SP', 'DCI', 'SEC'];
				const pfcatsMenu = [];
				ret.data.forEach((e) => {
					// if (needPfcatCode.includes(e.pfcatCode)) {
					pfcatsMenu.push(e);
					// }
				});
				self.pfcatsMenu = pfcatsMenu;
			});
		},
		getProTypeMenu(pfcatCode) {
			// 商品次類選項
			const self = this;
			self.$api
				.getProTypeListApi({
					pfcatCode: pfcatCode
				})
				.then(function (r) {
					self.proTypeMenu = r.data;
					self.getAssetCasts(self.proTypeCode);
				});
		},
		// 取得資產類別
		getAssetCasts: function (proTypeCode) {
			const self = this;
			self.assetcatCode = null;
			self.assetcatName = null;
			if (self.proTypeMenu && self.proTypeMenu.length > 0) {
				const item = self.proTypeMenu.find(e => e.proTypeCode === proTypeCode);
				if (item) {
					self.assetcatCode = item.assetcatCode;
					self.assetcatName = item.assetcatName;
				}
			}
		},
		getAssetcatsMenu: function () {
			const self = this;
			self.$api.getAssetcatsMenuApi().then(function (ret) {
				self.assetcatsMenu = ret.data;
				resolve();
			});
		},
		// 取得幣別
		getcurrenciesMenu: function () {
			const self = this;
			self.$api.groupProCurrenciesMenuApi().then(function (ret) {
				// self.currenciesMenu = ret.data;
				ret.data.forEach(function (item) {
					const obj = { value: item.curCode, name: item.curName };
					self.curOption.push(obj);
				});
			});
		},
		// 取得風險等級
		getRiskMenu: function () {
			const self = this;
			self.$api.getRiskMenuApi().then(function (ret) {
				self.riskMenu = ret.data;
			});
		},
		// 取得發行機構
		getIssuersMenu: function (pfcatCode) {
			const self = this;
			self.$api
				.getGroupIssuersMenuApi({
					pfcatCode: pfcatCode
				})
				.then(function (ret) {
					self.issuersMenu = ret.data;
					resolve();
				});
		},

		// 取得新商品臨時上架維護
		preview: function (proCode) {
			const self = this;
			/*
			var url = self.config.apiPath + '/pro/newShelfProduct';
			$.bi
				.ajax({
					url: url,
					method: 'GET',
					data: {
						proCode: proCode
					}
				})
				.then(function (r) {
					self.proPreview = r.data;
				});
			*/

			self.productList.forEach((e) => {
				if (e.proCode === proCode) {
					self.proPreview = e;
				}
			});
			self.isOpenModal = true;
		},

		getTargetCusBuName(code) {
			const self = this;
			const item = self.buCodeMenu.find(item => item.codeValue === code);
			return item ? item.codeName : '';
		},

		// 取得新商品臨時上架維護
		edit(proCode) {
			const self = this;
			/*
			var url = self.config.apiPath + '/pro/newShelfProduct';
			$.bi
				.ajax({
					url: url,
					method: 'GET',
					data: {
						proCode: proCode
					}
				})
				.then(function (r) {
					self.proTypeMenu = r.data;
				});
			*/

			let pro = {};
			self.productList.forEach((e) => {
				if (e.proCode === proCode) {
					pro = e;

					self.title = '編輯資料';
					find = true;
				}
			});

			self.pfcatCode = pro.pfcatCode;
			self.proTypeCode = pro.proTypeCode;
			self.proCode = pro.bankProCode;
			self.assetcatCode = pro.assetcatCode;
			self.proName = pro.proName;
			self.riskCode = pro.riskCode;
			self.curCode = pro.curCode;
			self.issuerCode = pro.issuerCode;
			self.bondCode = pro.bondCode;
			self.targetCusBu = pro.targetCusBu;
			self.profInvestorYn = pro.profInvestorYn;
			self.localYn = pro.localYn;
			if (pro.expireDt) {
				self.expiredDate = moment(pro.expireDt, ['YYYY-MM-DD', 'YYYY/MM/DD', 'MM-DD-YYYY'], true).format('YYYY-MM-DD');
			}

			self.getProTypeMenu(self.pfcatCode);
			self.getIssuersMenu(self.pfcatCode); // 取得發行機構

			/*
			self.uploadFiles = [];
			self.files = [];
			self.$refs.fileInput.value = null;
			self.uploadFile = null;
			*/
		},

		// 刪除
		deleteItem(proCode, eventId) {
			const self = this;

			thi.$bi.confirm('確定要刪除此筆資料嗎?', {
				event: {
					confirmOk: function () {
						self.$api
							.deleteShelfProduct({
								proCode: proCode,
								eventId: eventId
							})
							.then(function (ret) {
								self.getNewShelfProductList(); // 取得商檢商品資料列表
								self.title = '新增資料';
								self.expiredDate = null;
								const queryForm = self.$refs.queryForm;
								queryForm.resetForm();
								self.uploadFiles = [];
								self.files = [];
								self.$refs.fileInput.value = null;
								self.uploadFile = null;

								thi.$bi.alert('刪除成功');
							});
					}
				}
			});
		},

		// 檔案處裡
		handleChange: function (event) {
			const self = this;
			self.fileTemp = event.target.files[0];
		},
		// 上傳檔案
		addFile: function () {
			const self = this;
			$.when(self.$refs.fileForm.validate()).then(function (result) {
				if (result.valid) {
					if (self.files.length >= self.maxFileCount) {
						return;
					}

					const fileInfo = {
						fileNo: self.generatorId('file'),
						groupId: null,
						showName: self.fileTemp.name,
						fileName: null,
						contentType: self.fileTemp.contentType,
						filePath: null,
						file: self.fileTemp
					};
					self.files.push(fileInfo);
					self.$refs.fileForm.resetForm();
					self.$refs.uploadFile.$el.value = null;
				}
			});
		},
		previewFile: function (targetFileId) {
			const self = this;
			const index = self.files.findIndex(f => f.fileNo === targetFileId);
			const fileInfo = self.files[index];
			let url;
			// 預覽待上傳檔案
			if (fileInfo.file) {
				url = URL.createObjectURL(fileInfo.file);
				const previewWindow = window.open(url, '_blank');
				previewWindow.document.title = fileInfo.showName;
				previewWindow.addEventListener('beforeunload', () => {
					URL.revokeObjectURL(url);
				});
				// 預覽伺服器檔案
			}
			else {
				self.$api.previewServerDoc({
					fileId: targetFileId,
					fileTitle: fileInfo.showName,
					fileType: 'GenFilesLog'
				});
			}
		},
		deleteFile: function (targetFileId) {
			const self = this;
			const index = self.files.findIndex(f => f.fileId === targetFileId);
			if (index != -1) {
				self.files.splice(index, 1);
			}
		},
		cancel: function () {
			const self = this;
			self.title = '新增資料';
			self.expiredDate = null;
			const queryForm = self.$refs.queryForm;
			self.issuersMenu = null;

			queryForm.resetForm();
			/*
			self.uploadFiles = [];
			self.files = [];
			self.$refs.fileInput.value = null;
			self.uploadFile = null;
			*/
		},
		triggerFile: function (event) {
			const self = this;
			self.uploadFile = event.target.files[0];
			self.uploadFile.fileId = crypto.randomUUID();
		},
		doUploadFile: function () {
			const self = this;
			if (self.uploadFile) {
				if (self.uploadFile.size > 10485760) {
					thi.$bi.alert('檔案大小不得超過10MB！');
					return;
				}
				const fileInfo = {};

				fileInfo.fileName = self.uploadFile.name;
				fileInfo.fileId = self.uploadFile.fileId;

				fileInfo.contentType = self.uploadFile.type;
				fileInfo.fileSize = self.uploadFile.size;
				fileInfo.fileType = 'H';
				self.files.push(fileInfo);

				self.uploadFiles.push(self.uploadFile);
				self.uploadFile = null;
			}
		},
		deleteFiles: function (fileId) {
			const self = this;
			self.files.forEach((e, index, arr) => {
				if (e.fileId === fileId) {
					arr.splice(index, 1);
				}
			});

			self.uploadFiles.forEach((e, index, arr) => {
				if (e.fileId === fileId) {
					arr.splice(index, 1);
				}
			});
		},
		downloadFile: function (proFile) {
			const self = this;
			const id = proFile.proFileId;
			self.$api.downloadFileApi({ fileId: id, fileType: 'ProFileNewLog' });
		},
		showRejectMsg: function (item) {
			thi.$bi.alert(item.reason);
		},
		formatDtTime: function (value) {
			return moment(value).format('YYYY/MM/DD HH:mm');
		},
		submit: function () {
			const self = this;
			const queryForm = self.$refs.queryForm;
			const proCode = self.pfcatCode + '_' + self.proCode;
			queryForm.validate().then(function (pass) {
				if (pass.valid) {
					const url = self.config.apiPath + '/pro/insertProductNew';

					const data = {
						pfcatCode: self.pfcatCode,
						proTypeCode: self.proTypeCode,
						proCode: proCode,
						bankProCode: self.proCode,
						assetcatCode: self.assetcatCode,
						assetcatName: self.assetcatName,
						proName: self.proName,
						riskCode: self.riskCode,
						curCode: self.curCode,
						issuerCode: self.issuerCode,
						bondCode: self.bondCode,
						targetCusBu: self.targetCusBu,
						profInvestorYn: self.profInvestorYn,
						localYn: self.localYn,
						expireDt: self.expiredDate,
						files: self.files
					};
					const formData = new FormData();
					formData.append('model', JSON.stringify(data));

					// upload file
					self.uploadFiles.forEach((e) => {
						formData.append('files', e, e.fileId);
					});

					self.$api.patchProductApi(formData).then(function (ret) {
						this.$swal
							.fire({
								icon: 'success',
								text: '提交審核成功',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonsStyling: false,
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							})
							.then(function () {
								self.getNewShelfProductList(); // 取得商檢商品資料列表
								self.title = '新增資料';
								self.expiredDate = null;
								queryForm.resetForm();
								self.uploadFiles = [];
								self.files = [];
								self.$refs.fileInput.value = null;
								self.uploadFile = null;
							});
					});
				}
			});
		}
	}
};
</script>
