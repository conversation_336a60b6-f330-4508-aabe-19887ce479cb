<template>
	<div class="row" v-bind="$attrs">
		<div class="col-12">
			<vue-bi-tabs :menu-code="'M30-00'">
				<template #default="{ id }">
					<vue-pro-all
						v-if="id === 'vue-pro-all'"
						:fund-modal-handler="fundModalHandler"
						:etf-modal-handler="etfModalHandler"
						:bond-modal-handler="bondModalHandler"
						:pfd-modal-handler="pfdModalHandler"
						:sp-modal-handler="spModalHandler"
						:ins-modal-handler="insModalHandler"
						:dci-modal-handler="dciModalHandler"
						:sec-modal-handler="secModalHandler"
						:cur-option="curOption"
						:risk-menu="riskMenu"
						:favorites-handler="favoritesHandler"
						:source="source"
						:bank-pdt-code="bankPdtCode"
						:pdt-name="pdtName"
					/>
					<vue-pro-fund
						v-if="id === 'vue-pro-fund'"
						:fund-modal-handler="fundModalHandler"
						:cur-option="curOption"
						:risk-menu="riskMenu"
						:favorites-handler="favoritesHandler"
					/>
					<vue-pro-etf
						v-if="id === 'vue-pro-etf'"
						:etf-modal-handler="etfModalHandler"
						:cur-option="curOption"
						:risk-menu="riskMenu"
						:favorites-handler="favoritesHandler"
					/>
					<vue-pro-bond
						v-if="id === 'vue-pro-bond'"
						:bond-modal-handler="bondModalHandler"
						:cur-option="curOption"
						:risk-menu="riskMenu"
						:favorites-handler="favoritesHandler"
					/>
					<vue-pro-pfd
						v-if="id === 'vue-pro-pfd'"
						:pfd-modal-handler="pfdModalHandler"
						:cur-option="curOption"
						:risk-menu="riskMenu"
						:favorites-handler="favoritesHandler"
					/>
					<vue-pro-sp
						v-if="id === 'vue-pro-sp'"
						:sp-modal-handler="spModalHandler"
						:cur-option="curOption"
						:risk-menu="riskMenu"
						:favorites-handler="favoritesHandler"
					/>
					<vue-pro-ins
						v-if="id === 'vue-pro-ins'"
						:ins-modal-handler="insModalHandler"
						:cur-option="curOption"
						:risk-menu="riskMenu"
						:favorites-handler="favoritesHandler"
					/>
					<vue-pro-dci
						v-if="id === 'vue-pro-dci'"
						:dci-modal-handler="dciModalHandler"
						:cur-option="curOption"
						:risk-menu="riskMenu"
						:favorites-handler="favoritesHandler"
					/>
					<vue-pro-sec
						v-if="id === 'vue-pro-sec'"
						:sec-modal-handler="secModalHandler"
						:cur-option="curOption"
						:risk-menu="riskMenu"
						:favorites-handler="favoritesHandler"
					/>
				</template>
			</vue-bi-tabs>
		</div>
	</div>
	<vue-modal :is-open="isOpenModal['fund']" @close="closeModal('fund')">
		<template #content="props">
			<vue-fund-modal
				ref="fundModalRef"
				:is-open-fund-modal="isOpenModal['fund']"
				:fin-req-code-menu="finReqCodeMenu"
				:download-file="downloadFile"
				:download-other-file="downloadOtherFile"
				:close="props.close"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenModal['etf']" @close="closeModal('etf')">
		<template #content="props">
			<vue-etf-modal
				ref="etfModalRef"
				:is-open-etf-modal="isOpenModal['etf']"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:download-file="downloadFile"
				:download-other-file="downloadOtherFile"
				:close="props.close"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenModal['bond']" @close="closeModal('bond')">
		<template #content="props">
			<vue-bond-modal
				ref="bondModalRef"
				:is-open-bond-modal="isOpenModal['bond']"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:download-file="downloadFile"
				:download-other-file="downloadOtherFile"
				:close="props.close"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenModal['pfd']" @close="closeModal('pfd')">
		<template #content="props">
			<vue-pfd-modal
				ref="pfdModalRef"
				:is-open-pfd-modal="isOpenModal['pfd']"
				:fin-req-code-menu="finReqCodeMenu"
				:close="props.close"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenModal['sp']" @close="closeModal('sp')">
		<template #content="props">
			<vue-sp-modal
				ref="spModalRef"
				:is-open-structured-product-modal="isOpenModal['sp']"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:download-file="downloadFile"
				:download-other-file="downloadOtherFile"
				:close="props.close"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenModal['ins']" @close="closeModal('ins')">
		<template #content="props">
			<vue-ins-modal
				ref="insModalRef"
				:is-open-ins-modal="isOpenModal['ins']"
				:fin-req-code-menu="finReqCodeMenu"
				:download-file="downloadFile"
				:download-other-file="downloadOtherFile"
				:close="props.close"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenModal['dci']" @close="closeModal('dci')">
		<template #content="props">
			<vue-dci-modal
				ref="dciModalRef"
				:is-open-dci-modal="isOpenModal['dci']"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:download-file="downloadFile"
				:download-other-file="downloadOtherFile"
				:close="props.close"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenModal['sec']" @close="closeModal('sec')">
		<template #content="props">
			<vue-sec-modal
				ref="secModalRef"
				:is-open-sec-modal="isOpenModal['sec']"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:download-file="downloadFile"
				:download-other-file="downloadOtherFile"
				:close="props.close"
			/>
		</template>
	</vue-modal>
	<!--Page content end-->
</template>

<script>
import Swal from 'sweetalert2';

import biTabs from '@/views/components/biTabs.vue';
import productSearchAll from './include/productSearchAll.vue';
import vueModal from '@/views/components/model.vue';
import vueProFund from './include/productSearchFund.vue';
import vueProEtf from './include/productSearchEtf.vue';
import vueProBond from './include/productSearchBond.vue';
import vueProPfd from './include/productSearchPfd.vue';
import vueProSp from './include/productSearchSp.vue';
import vueProIns from './include/productSearchIns.vue';
import vueProDci from './include/productSearchDci.vue';
import vueProSec from './include/productSearchSec.vue';

import vueFundModal from './include/fundModal.vue';
import vueEtfModal from './include/etfModal.vue';
import vueBondModal from './include/bondModal.vue';
import vuePfdModal from './include/pfdModal.vue';
import vueSpModal from './include/spModal.vue';
import vueInsModal from './include/insModal.vue';
import vueDciModal from './include/dciModal.vue';
import vueSecModal from './include/secModal.vue';

export default {
	components: {
		'vue-bi-tabs': biTabs,
		'vue-pro-all': productSearchAll,
		vueModal,
		vueProFund,
		vueProEtf,
		vueProBond,
		vueProPfd,
		vueProSp,
		vueProIns,
		vueProDci,
		vueProSec,
		vueFundModal,
		vueEtfModal,
		vueBondModal,
		vuePfdModal,
		vueSpModal,
		vueInsModal,
		vueDciModal,
		vueSecModal
	},
	data: function () {
		return {
			tabCode: 'ALL',
			menuTab: [], // Menu permissions
			curOption: [], // Currency
			riskMenu: [], // Risk level
			finReqCodeMenu: [], // Each tab - product common data - financial requirement options,
			proPriceRangeMenu: [], // Each tab - price/NAV display range,
			source: false, // From potential customer search,
			bankPdtCode: null, // From homepage search,
			pdtName: null, // From homepage search,
			isOpenModal: {
				fund: false,
				etf: false,
				bond: false,
				pfd: false,
				sp: false,
				ins: false,
				dci: false,
				sec: false
			}
		};
	},
	created: function () {
		const self = this;
		if (self.$route?.params?.bankPdtCode) {
			self.bankPdtCode = self.$route.params.bankPdtCode;
		}
		if (self.$route?.params?.pdtName) {
			self.pdtName = self.$route.params.pdtName;
		}
	},
	beforeMount: function () {
		this.tabCode = 'ALL';
	},
	mounted: async function () {
		const self = this;
		const curData = await self.$api.groupProCurrenciesMenuApi();
		curData.data.forEach(function (item) {
			const obj = {
				value: item.curCode,
				name: item.curName + item.curCode
			};
			self.curOption.push(obj);
		});
		const riskData = await self.$api.getRiskMenuApi();
		self.riskMenu = riskData.data;
		self.getFinReqCodeMenu();
		self.getProPriceRangeMenu();
		if (self.$route.proListSource == 'PRO_POT_SEARCH_RST') {
			self.source = true;
		}
		else {
			self.source = false;
			// Delete user product lists from various function sources
			await this.$api.deleteProPotSearchRstApi();
		}
	},
	methods: {
		// Update currency frontend
		refCurSel: function () {
			// $('#curMenuAll').selectpicker('refresh');
		},

		closeModal: function (modalName) {
			this.isOpenModal[modalName] = false;
		},
		fundModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.fundModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.fundModalRef.changeTab('section1');
			self.isOpenModal['fund'] = true;
		},
		etfModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.etfModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.etfModalRef.getEtfDetail(proCode); // Product info/fund data
			self.$refs.etfModalRef.getEtfStockHold(proCode); // Product info/ETF holdings
			self.$refs.etfModalRef.getEtfPrice(proCode); // Product info/price analysis data
			self.$refs.etfModalRef.getPricesChartData(proCode, 'Y', -1.0); // Product info/price analysis chart
			self.$refs.etfModalRef.getEtfProfileNameMenu(proCode); // Product info/performance analysis
			self.$refs.etfModalRef.getEtfProfileBenchmarksMenu(); // Product info/performance analysis
			self.isOpenModal['etf'] = true;
		},
		bondModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.bondModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.bondModalRef.getBondPriceAna(proCode);
			self.$refs.bondModalRef.getPricesChartData(proCode, 'Y', -1.0); // Product info/price analysis chart
			self.$refs.bondModalRef.getBondPerformances(proCode, 'Y', -1.0, null); // Product info/performance analysis chart
			self.isOpenModal['bond'] = true;
		},
		pfdModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.pfdModalRef.getProInfo(proCode, pfcatCode);
			//			self.$refs.pfdModalRef.getEtfDetail(proCode); // Product info/fund data
			self.isOpenModal['pfd'] = true;
		},
		spModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.isOpenModal['sp'] = true;
			self.$refs.spModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.spModalRef.getSpPriceAna(proCode); // Product info/NAV analysis data
			self.$refs.spModalRef.getSpNets(proCode, 'Y', -1.0); // Product info/NAV analysis chart
			self.$refs.spModalRef.getSpPerformances(proCode, 'Y', -1.0, null); // Product info/performance analysis chart
		},
		insModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.insModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal['ins'] = true;
		},
		dciModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.dciModalRef.getProInfo(proCode, pfcatCode); // Product basic data
			self.$refs.dciModalRef.getDciPriceAna(proCode, 'M', -1.0); // Product info/price analysis data
			self.$refs.dciModalRef.getDciNets(proCode, 'Y', -1.0); // Product info/price analysis chart
			self.$refs.dciModalRef.getDciPerformances(proCode, 'Y', -1.0, null); // Product info/performance analysis chart
			self.isOpenModal['dci'] = true;
		},
		secModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.isOpenModal['sec'] = true;
			self.$refs.secModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.secModalRef.getSecPriceAna(proCode); // Product info/NAV analysis data
			self.$refs.secModalRef.getSecNets(proCode, 'Y', -1.0); // Product info/NAV analysis chart
			self.$refs.secModalRef.getSecPerformances(proCode, 'Y', -1.0, null); // Product info/performance analysis chart
		},
		async favoritesHandler(proCode, pfcatCode) {
			const self = this;
			await self.$api.addFavoriteApi({
				proCode: proCode,
				pfcatCode: pfcatCode
			});
			Swal.fire({
				icon: 'success',
				text: this.$t('pro.addedToFavorites'),
				showCloseButton: true,
				confirmButtonText: this.$t('pro.confirm'),
				buttonsStyling: false,
				customClass: {
					confirmButton: 'btn btn-success'
				}
			});
		},
		downloadFile: function (proFile) {
			const self = this;
			const proFileId = proFile.proFileId;
			const fileName = proFile.showName;
			this.$api.downloadProFileApi({ proFileId }).then(function (data) {
				const link = document.createElement('a');
				const url = URL.createObjectURL(data);
				link.download = fileName;
				link.href = url;
				document.body.appendChild(link);
				link.click();
				link.remove();
				setTimeout(() => URL.revokeObjectURL(url), 1000);
			});
		},
		downloadOtherFile: function (fileId) {
			this.$api.downloadOtherFileApi({ fileId });
		},
		// Get each tab - product common data - financial requirement options
		getFinReqCodeMenu: async function () {
			const self = this;
			const res = await self.$api.getAdmCodeDetail({ codeType: 'FIN_REQ_CODE' });
			self.finReqCodeMenu = res.data;
		},
		// Get each tab - price/NAV display range
		getProPriceRangeMenu: async function () {
			const self = this;
			const res = await self.$api.getProPriceRangeMenuApi();
			self.proPriceRangeMenu = res.data;
		}
	} // methods end
};
</script>
