<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<div class="tab-nav-tabs my-3">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'common' }"
							data-bs-toggle="tab"
							@click="changeTab('common')"
						>{{ $t('pro.generalFilter') }}</a>
					</li>
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'fast' }"
							data-bs-toggle="tab"
							@click="changeTab('fast')"
						>{{ $t('pro.quickFilter') }}</a>
					</li>
				</ul>
				<div class="tab-content">
					<div id="SectionA" class="tab-pane fade show" :class="{ 'active show': activeTab == 'common' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>{{ $t('pro.searchCond') }}</h4>
								<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
							</div>
							<div id="collapseListGroup1" class="collapse show">
								<div class="card-body">
									<form>
										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label tx-require">{{ $t('pro.assetCategory') }}</label>
												<select id="assetcatCode" v-model="assetcatCode" class="form-select">
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="item in assetcatsMenu" :value="item.assetcatCode">
														{{ $filters.defaultValue(item.assetcatName, '--') }}
													</option>
												</select>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label tx-require">{{ $t('pro.productMainCategory') }}</label>
												<select id="pfcatCodeForAllProd" v-model="pfcatCode" class="form-select">
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="item in pfcatsMenu" :value="item.pfcatCode">
														{{ $filters.defaultValue(item.pfcatName, '--') }}
													</option>
												</select>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.productCode') }}</label>
												<input
													id="prod_bank_pro_code"
													v-model="bankProCode"
													class="form-control ms-3"
													maxlength="20"
													size="25"
													type="text"
													value=""
												>
											</div>
										</div>

										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.productName') }}</label>
												<input
													id="prod_pro_name"
													v-model="proName"
													class="form-control"
													maxlength="20"
													size="45"
													type="text"
													value=""
												>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.pricingCurrency') }}</label>
												<select
													id="curMenuAll"
													ref="curMenuAll"
													v-model="curObjs"
													class="selectpicker form-control"
													multiple
													:title="$t('pro.pleaseSelectCurrency')"
													data-style="btn-white"
												>
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="(item, index) in curOption" :key="index" :value="item.value">
														{{ $filters.defaultValue(item.name, '--') }}
													</option>
												</select>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.riskLevel') }}</label>
												<div class="form-check-group">
													<div v-for="(item, index) in riskMenu" class="form-check form-check-inline">
														<input
															:id="'riskGrade-' + index"
															v-model="riskCodes"
															type="checkbox"
															class="form-check-input"
															name="riskCodes"
															:value="item.riskCode"
														>
														<label :for="'riskGrade-' + index" class="form-check-label">{{ item.riskName }}</label>
													</div>
												</div>
											</div>
										</div>
										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label tx-require">{{ $t('pro.maturityPrincipalProtection') }}</label>
												<div v-for="item in principalGuarMenu" class="form-check form-check-inline">
													<input
														:id="'principalGuar' + item.codeValue"
														v-model="principalGuarYn"
														class="form-check-input"
														type="radio"
														:value="item.codeValue"
														name="fastCode"
													>
													<label class="form-check-label" :for="'principalGuar' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label tx-require">{{ $t('pro.dividendFrequency') }}</label>
												<select v-model="intFreqUnitType" name="select" class="form-select">
													<option value="">
														{{ $t('pro.unlimited') }}
													</option>
													<option v-for="intFreqUnit in intFreqUnitMenu" :value="intFreqUnit.codeValue">
														{{ $filters.defaultValue(intFreqUnit.codeName, '--') }}
													</option>
												</select>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">ISINCODE</label>
												<input
													id="isinCode"
													v-model="isinCode"
													class="form-control"
													maxlength="20"
													size="45"
													type="text"
													value=""
												>
											</div>
										</div>
										<div class="form-footer">
											<button class="btn btn-primary" @click.prevent="gotoPage(0)">
												{{ $t('pro.search') }}
											</button>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>
					<div id="SectionB" class="tab-pane fade" :class="{ 'active show': activeTab == 'fast' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
								<h4>{{ $t('pro.searchCond') }}</h4>
								<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
							</div>
							<div id="collapseListGroup2" class="collapse show">
								<div class="card-body">
									<form>
										<div class="form-row">
											<div class="form-group col-12 col-lg-12">
												<label class="form-label tx-require">篩選條件 </label>
												<div class="form-check-group">
													<div
														v-for="item in fastFilterMenu"
														class="form-check form-check-inline"
														@change="fastChange(item.codeValue)"
													>
														<input
															:id="'fast' + item.codeValue"
															v-model="fastCode"
															class="form-check-input"
															type="radio"
															:value="item.codeValue"
															name="fastCode"
														>
														<label class="form-check-label" :for="'fast' + item.codeValue">{{
															$filters.defaultValue(item.codeName, '--')
														}}</label>
													</div>
												</div>
											</div>
											<div id="rangeFixedTrAll" class="form-group col-12 col-lg-6" style="display: none">
												<label class="form-label tx-require"> 顯示區間</label>

												<select id="prod_protype_code" v-model="timeRange" class="form-select">
													<option v-for="item in timeRangeMenu" :value="item.rangeType">
														{{ $filters.defaultValue(item.termName, '--') }}
													</option>
												</select>
											</div>

											<div id="proPerfTimeTrAll" class="form-group col-12 col-lg-6" style="display: none">
												<label class="form-label tx-require">標的績效 </label>

												<select id="vfAstStat_stat_code" v-model="perf" class="form-select">
													<option v-for="item in perfMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</div>

											<div id="maxRowIdTrAll" class="form-group col-12 col-lg-6" style="display: none">
												<label class="form-label tx-require">顯示資料筆數</label>
												<select id="maxRowId" v-model="rowNumber" class="form-select">
													<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
														{{ $filters.defaultValue(item.termName, '--') }}
													</option>
												</select>
											</div>
										</div>
										<div class="form-footer">
											<button class="btn btn-primary" @click.prevent="gotoFastPage(0)">
												查詢
											</button>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div v-if="pageData.content.length > 0" id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>查詢結果</h4>
						<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-bordered text-center">
							<thead>
								<tr>
									<th class="wd-100 text-start">
										商品主類
									</th>
									<th>
										商品代號/險種代碼<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('PFCAT_CODE')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('PFCAT_CODE')"
										/>
									</th>
									<th class="10% text-start">
										商品中文名稱<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('PRO_NAME')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('PRO_NAME')"
										/>
									</th>
									<th class="text-end">
										1Y績效(台幣)<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('LC_1Y_RETURN')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('LC_1Y_RETURN')"
										/>
									</th>
									<th class="text-end">
										1Y績效(原幣)<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('FC_1Y_RETURN')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('FC_1Y_RETURN')"
										/>
									</th>
									<th>
										配息率<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('INT_RATE')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('INT_RATE')"
										/>
									</th>
									<th>
										風險等級<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('RISK_CODE')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('RISK_CODE')"
										/>
									</th>
									<th>
										計價幣別<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('CUR_CODE')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('CUR_CODE')"
										/>
									</th>
									<th>
										是否可申購<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('BUY_YN')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('BUY_YN')"
										/>
									</th>
									<th>
										到期<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('EXPIRE_DT')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('EXPIRE_DT')"
										/>
									</th>
									<th class="text-center" width="120">
										設為觀察
									</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(item) in pageData.content">
									<td data-th="商品主類" class="text-start">
										<span>{{ $filters.defaultValue(item.pfcatName, '--') }}</span>
									</td>
									<td data-th="商品代號/險種代碼">
										<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
									</td>
									<td class="text-start" data-th="商品中文名稱">
										<span>
											<a
												v-if="item.pfcatCode === 'FUND'"
												class="tx-link"
												@click="fundModalHandler(item.proCode, item.pfcatCode)"
											>{{ $filters.defaultValue(item.proName, '--') }}</a>
											<a
												v-else-if="item.pfcatCode === 'ETF'"
												class="tx-link"
												@click="etfModalHandler(item.proCode, item.pfcatCode)"
											>{{ $filters.defaultValue(item.proName, '--') }}</a>
											<a
												v-else-if="item.pfcatCode === 'FB'"
												class="tx-link"
												@click="bondModalHandler(item.proCode, item.pfcatCode)"
											>{{ $filters.defaultValue(item.proName, '--') }}</a>
											<a
												v-else-if="item.pfcatCode === 'SP'"
												class="tx-link"
												@click="spModalHandler(item.proCode, item.pfcatCode)"
											>{{ $filters.defaultValue(item.proName, '--') }}</a>
											<a
												v-else-if="item.pfcatCode === 'INS'"
												class="tx-link"
												@click="insModalHandler(item.proCode, item.pfcatCode)"
											>{{ $filters.defaultValue(item.proName, '--') }}</a>
											<a
												v-else-if="item.pfcatCode === 'DCD'"
												class="tx-link"
												@click="dciModalHandler(item.proCode, item.pfcatCode)"
											>{{ $filters.defaultValue(item.proName, '--') }}</a>
											<a
												v-else-if="item.pfcatCode === 'SEC'"
												class="tx-link"
												@click="secModalHandler(item.proCode, item.pfcatCode)"
											>{{ $filters.defaultValue(item.proName, '--') }}</a>
											<a
												v-else-if="item.pfcatCode === 'PFD'"
												class="tx-link"
												@click="pfdModalHandler(item.proCode, item.pfcatCode)"
											>{{ $filters.defaultValue(item.proName, '--') }}</a>
											<span v-else>{{ $filters.defaultValue(item.proName, '--') }}</span>
										</span>
									</td>
									<td class="text-end" data-th="1Y績效(台幣)">
										<span>{{ $filters.formatNumber(item.lc1yReturn, '--') }}</span>
									</td>
									<td class="text-end" data-th="1Y績效(原幣)">
										<span>{{ $filters.formatNumber(item.fc1yReturn, '--') }}</span>
									</td>
									<td data-th="配息率">
										<span>{{ $filters.formatPct(item.intRate) }}%</span>
									</td>
									<td data-th="風險等級">
										<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
									</td>
									<td data-th="計價幣別">
										<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
									</td>
									<td data-th="是否可申購">
										<span v-if="item.pfcatCode === 'DCI'"> 請洽金融行銷部 </span>
										<span v-else>
											{{ $filters.defaultValue(item.buyYnName, '--') }}
										</span>
									</td>
									<td data-th="到期日">
										<span>{{ $filters.defaultValue(item.expireDt, '--') }}</span>
									</td>
									<td data-th="執行">
										<button
											v-if="activeTab === 'fast' && fastCode === '06'"
											type="button"
											class="btn btn-primary"
											title="移除我的最愛"
											@click="remove(item.proCode)"
										>
											移除最愛
										</button>
										<Button
											v-else
											color="dark"
											icon
											data-bs-toggle="tooltip"
											title="加入我的最愛"
											@click="favoritesHandler(item.proCode, item.pfcatCode)"
										>
											<i class="bi bi-eye" />
										</Button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
					<!--						<div class="form-footer" v-if="activeTab === 'fast' && fastCode === '06'">-->
					<!--							<button type="button" class="btn btn-primary" @click="remove()">移除</button>-->
					<!--						  </div>-->
				</div>
				<div class="tx-note">
					<ol>
						<li><span>資料日期：2017/10/15</span></li>
						<li><span>商品是否可申購以交易系統為主</span></li>
					</ol>
				</div>
			</div>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
import _ from 'lodash';
import pagination from '@/views/components/pagination.vue';
import $ from 'jquery';

export default {
	components: {
		'vue-pagination': pagination
	},
	props: {
		fundModalHandler: Function,
		etfModalHandler: Function,
		bondModalHandler: Function,
		spModalHandler: Function,
		insModalHandler: Function,
		dciModalHandler: Function,
		secModalHandler: Function,
		pfdModalHandler: Function,
		favoritesHandler: Function,
		curOption: Array,
		riskMenu: Array,
		source: Boolean,
		bankPdtCode: String,
		pdtName: String
	},
	data: function () {
		return {
			activeTab: 'common',
			assetcatCode: '',
			pfcatCode: '',
			proName: null,
			proCodes: [], // 快速篩選 我的最愛選擇要刪除的商品陣列
			checkboxs: [], // 加入比較選項
			lipperIds: [], // 績效比較商品代碼
			curObjs: [],
			riskCodes: [],
			bankProCode: null,
			principalGuarYn: '',
			intFreqUnitType: '', // 配息頻率
			isinCode: null,
			fastCode: '03',
			timeRange: '1',
			rowNumber: '10.000000',
			perf: 'PCTYTD',
			assetcatsMenu: [],
			pfcatsMenu: [],
			principalGuarMenu: [], // 到期保本下拉
			intFreqUnitMenu: [], // 配息頻率下拉
			fastFilterMenu: [],
			timeRangeMenu: [], // 顯示區間
			rowNumerMenu: [], // 顯示資料筆數
			perfMenu: [], // 標的績效
			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: ['BANK_PRO_CODE'],
				direction: 'ASC'
			}
		};
	},
	computed: {
		...mapState(useI18nStore, ['locale'])
	},
	watch: {
		async locale() {
			await this.$nextTick();
			$(this.$refs.curMenuAll).selectpicker('refresh');
		},
		activeTab() {
			const self = this;
			self.fastCode = '03';
			self.fastChange(self.fastCode);
		},
		curObjs(newVal, oldVal) {
			if (newVal[0] === oldVal[0]) return;
			if (newVal[0] === '') {
				$(this.$refs.curMenuAll).selectpicker('selectAll');
			}
			else if (oldVal[0] === '' && newVal[0] !== '') {
				$(this.$refs.curMenuAll).selectpicker('deselectAll');
			}
		}
	},
	mounted: async function () {
		this.initializeBootstrapSelect();
		const self = this;
		self.getAssetcatsMenu();
		self.getProPfcatsMenu();
		self.getFastFilterMenu();
		self.getTimeRangeMenu();
		self.getRowNumerMenu();
		self.getPerfMenu();
		self.getPrincipalGuarMenu();
		self.getIntFreqUnitTypeMenu();
		$(this.$refs.curMenuAll).selectpicker('refresh');
		if (self.source) {
			self.gotoPage(0, self.source, null, null);
		}
		if (self.bankPdtCode || self.pdtName) {
			self.gotoPage(0, self.source, self.bankPdtCode, self.pdtName);
		}
		self.fastChange(self.fastCode);
	},
	unmounted: function () {
		this.destroyBootstrapSelect();
	},
	methods: {
		initializeBootstrapSelect: function () {
			this.$nextTick(() => {
				$(this.$refs.curMenuAll).selectpicker();
			});
		},
		destroyBootstrapSelect: function () {
			$(this.$refs.curMenuAll).selectpicker('destroy');
		},
		// 取得資產類別
		getAssetcatsMenu: async function () {
			const res = await this.$api.getAssetcatsMenuApi();
			this.assetcatsMenu = res.data;
		},
		// 取得商品主類
		getProPfcatsMenu: async function () {
			const res = await this.$api.getProPfcatsMenuApi();
			this.pfcatsMenu = res.data;
		},
		// 取得快速篩選選單
		getFastFilterMenu: async function () {
			const res = await this.$api.getFastFilterMenuApi();
			this.fastFilterMenu = res.data;
		},
		// 取得顯示區間
		getTimeRangeMenu: async function () {
			const res = await this.$api.getTimeRangeMenuApi();
			this.timeRangeMenu = res.data;
		},
		// 取得顯示資料筆數
		getRowNumerMenu: async function () {
			const res = await this.$api.getRowNumerMenuApi();
			this.rowNumerMenu = res.data;
		},
		// 取得標的績效
		getPerfMenu: async function () {
			const res = await this.$api.getPerfMenuApi();
			this.perfMenu = res.data;
		},
		// 取得配息頻率選項
		getIntFreqUnitTypeMenu: async function () {
			const res = await this.$api.getAdmCodeDetail({ codeType: 'INT_FREQ_UNITTYPE' });
			this.intFreqUnitMenu = res.data;
		},
		// 取得到期保本選項
		getPrincipalGuarMenu: async function () {
			const self = this;
			self.principalGuarMenu = [{ codeValue: '', codeName: '不限' }];
			let selectYnList = [];
			const ret = await this.$api.getAdmCodeDetail({ codeType: 'SELECT_YN' });
			selectYnList = ret.data;
			Array.prototype.push.apply(self.principalGuarMenu, selectYnList);
		},
		// 條件Tab切換
		changeTab: function (tabName) {
			const self = this;
			self.activeTab = tabName;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
		},
		gotoPage: function (page, source, bankPdtCode, pdtName) {
			this.pageable.page = page;
			this.getPageData(page, source, bankPdtCode, pdtName);
		},
		// 由查詢結果標題觸發
		sort: function (columnName) {
			if (this.pageable.sort !== columnName) {
				this.pageable.sort = columnName;
				this.pageable.direction = 'DESC';
			}
			else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}

			this.gotoPage(0);
		},
		// 由快速查詢結果標題觸發
		sortFast: function (columnName) {
			if (this.pageable.sort !== columnName) {
				this.pageable.sort = columnName;
				this.pageable.direction = 'DESC';
			}
			else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}

			this.gotoFastPage(0, columnName);
		},
		getPageData: async function (page, source, bankPdtCode, pdtName) {
			const self = this;
			let url = '';

			page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			if (bankPdtCode) {
				self.bankProCode = bankPdtCode;
			}

			if (pdtName) {
				self.proName = pdtName;
			}

			const payload = {
				assetcatCode: self.assetcatCode,
				pfcatCode: self.pfcatCode,
				bankProCode: self.bankProCode,
				proName: self.proName,
				riskCodes: self.riskCodes,
				curCodes: self.curObjs,
				intFreqUnitType: self.intFreqUnitType,
				principalGuarYn: self.principalGuarYn,
				isinCode: self.isinCode,
				source: source
			};
			const res = await this.$api.searchProductsApi(payload, url);
			self.pageData = res.data;
		},
		fastChange(fastCode) {
			const self = this;
			self.timeRange = null; // 快速 顯示區間
			self.rowNumber = null; // 快速 顯示資料筆數
			self.pageData = {
				// 清空查詢資料
				content: []
			};
			if (fastCode === '05') {
				// 超人氣
				$('#proPerfTimeTrAll').hide();
				$('#rangeFixedTrAll').show();
				$('#maxRowIdTrAll').show();
				self.timeRange = 'D'; // 快速 顯示區間
				self.rowNumber = '10'; // 快速 顯示資料筆數
			}
			else if (fastCode === '07') {
				// 績效排行
				$('#rangeFixedTrAll').hide();
				$('#maxRowIdTrAll').show();
				$('#proPerfTimeTrAll').show();
				self.rowNumber = '10'; // 快速 顯示資料筆數
			}
			else if (fastCode === '08') {
				// 最高配息率商品
				$('#maxRowIdTrAll').show();
				$('#rangeFixedTrAll').hide();
				$('#proPerfTimeTrAll').hide();
				self.rowNumber = '10'; // 快速 顯示資料筆數
			}
			else {
				$('#maxRowIdTrAll').hide();
				$('#rangeFixedTrAll').hide();
				$('#proPerfTimeTrAll').hide();
			}
		},
		gotoFastPage: function (page, sortColumnName) {
			this.pageable.page = page;
			this.getFastPageData(page, sortColumnName);
		},
		getFastPageData: async function (page, sortColumnName) {
			const self = this;

			let rangeType = null;
			let rangeFixed = null;
			if (self.fastCode === '05') {
				const timeRangeObjs = _.filter(self.timeRangeMenu, {
					rangeType: self.timeRange
				});
				rangeType = timeRangeObjs[0].rangeType;
				rangeFixed = timeRangeObjs[0].rangeFixed;
			}

			if (sortColumnName == null) {
				// 當「篩選條件」='聚焦商品'、'超人氣商品'、'我的最愛'：以商品代號排序。
				if (self.fastCode === '03' || self.fastCode === '06' || self.fastCode === '09') {
					self.pageable.sort = 'BANK_PRO_CODE';
				}
				else if (self.fastCode === '08') {
					// 當「篩選條件」='最高配息率商品'：以配息率排序。
					self.pageable.sort = 'INT_RATE';
				}
				else if (self.fastCode === '08') {
					// 當「篩選條件」='績效排行'：以所選擇的標的績效排序。
					self.pageable.sort = 'FC_RETURN';
				}
			}

			let url = '';
			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			const payload = {
				fastCode: self.fastCode,
				rangeType: rangeType,
				rangeFixed: rangeFixed,
				perfTime: self.perf,
				rowNumber: self.rowNumber,
				queryString: url
			};

			const res = await this.$api.fastSearchProductsApi(payload);
			self.pageData = res.data;
		},
		// 加入比較checkbox選項
		checkoutPro: function (item) {
			const self = this;
			if (!self.proCodes.includes(item.proCode)) {
				self.proCodes.push(item.proCode);
			}
			else {
				_.remove(self.proCodes, code => code === item.proCode);
			}
			if (!self.lipperIds.includes(item.lipperId)) {
				self.lipperIds.push(item.lipperId);
			}
			else {
				self.lipperIds.pop(item.lipperId);
			}
		},
		// 刪除我的最愛
		remove(proCode) {
			const self = this;
			this.$api.deleteFavoriteApi({ proCode: proCode });

			this.$bi.alert('刪除成功');
			self.checkboxs = [];
			self.proCodes = [];
			self.gotoFastPage(0);
		}
	} // methods end
};
</script>
