<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<!--頁面內容 基金fund start-->
			<div class="tab-nav-tabs my-3">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'common' }"
							data-bs-toggle="tab"
							@click="changeTab('common')"
						>{{ $t('pro.generalFilter') }}</a>
					</li>
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'fast' }"
							data-bs-toggle="tab"
							@click="changeTab('fast')"
						>{{ $t('pro.quickFilter') }}</a>
					</li>
				</ul>

				<div class="tab-content">
					<div id="SectionA" class="tab-pane fade show" :class="{ 'active show': activeTab == 'common' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>{{ $t('pro.searchCond') }}</h4>
								<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
							</div>
							<div id="collapseListGroup1" class="collapse show">
								<div class="card-body">
									<form>
										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> {{ $t('pro.productCode') }} </label>
												<input
													id="prod_bank_pro_code"
													v-model="bankProCode"
													class="form-control"
													maxlength="20"
													size="25"
													type="text"
													value=""
												>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.productName') }}</label>
												<input
													id="prod_pro_name"
													v-model="proName"
													class="form-control"
													maxlength="20"
													size="45"
													type="text"
													value=""
												>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.pricingCurrency') }}</label>
												<select
													id="curMenuPfd"
													v-model="curObjs"
													class="selectpicker form-control"
													multiple
													:title="$t('pro.pleaseSelectCurrency')"
													data-style="btn-white"
												>
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="(item, index) in curOption" :key="index" :value="item.value">
														{{ $filters.defaultValue(item.name, '--') }}
													</option>
												</select>
											</div>
										</div>

										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.riskLevel') }}</label>
												<div class="form-check-group">
													<div v-for="(item, index) in riskMenu" class="form-check form-check-inline">
														<input
															:id="'riskGrade-' + index"
															v-model="riskCodes"
															type="checkbox"
															class="form-check-input"
															name="riskCodes"
															:value="item.riskCode"
														>
														<label :for="'riskGrade-' + index" class="form-check-label">{{ item.riskName }}</label>
													</div>
												</div>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">ISINCODE</label>
												<input
													id="isinCode"
													v-model="isinCode"
													class="form-control"
													maxlength="20"
													size="45"
													type="text"
												>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.piSalesOnly') }}</label>
												<div v-for="item in profInvestorMenu" class="form-check form-check-inline">
													<input
														:id="'profInvestor' + item.codeValue"
														v-model="profInvestorYn"
														class="form-check-input"
														type="radio"
														:value="item.codeValue"
														name="fastCode"
													>
													<label class="form-check-label" :for="'profInvestor' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>
										</div>

										<div class="form-row">
											<div class="form-group col-12 col-lg-12">
												<label class="form-label">{{ $t('pro.investmentRegion') }}</label>
												<div class="input-group">
													<button type="button" class="btn btn-primary" @click="groupGeoFocusModalHandler()">
														{{ $t('pro.selectInvestmentRegion') }}
													</button>
													<vue-modal :is-open="isOpenGeoFocusModal" @close="isOpenGeoFocusModal = false">
														<template #content="props">
															<vue-group-geofocus-modal
																id="groupGeoFocusModal"
																ref="groupGeoFocusModalRef"
																:close="props.close"
																:issuer-prop="geoFocusItem"
																@selected="selectedGeoFocus"
															/>
														</template>
													</vue-modal>
													<div v-for="item in geoFocusItem">
														<span class="form-check-label"> {{ $filters.defaultValue(item.name, '--') }}</span>
														<a href="#" @click="deleteGeoFocusItem(item.geoFocusCode)"><img
															:src="getImgURL('icon', 'i-cancel.png')"
														></a>
													</div>
												</div>
											</div>
										</div>

										<div class="form-footer">
											<a class="btn btn-primary" @click.prevent="gotoPage(0)">{{ $t('pro.search') }}</a>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>

					<div id="SectionB" class="tab-pane fade" :class="{ 'active show': activeTab == 'fast' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
								<h4>{{ $t('pro.searchCond') }}</h4>
								<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
							</div>
							<div id="collapseListGroup2" class="collapse show">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-12">
											<label class="form-label tx-require">{{ $t('pro.filterConditions') }}</label>

											<div class="form-check-group">
												<div
													v-for="item in pfdFastMenu"
													class="form-check form-check-inline"
													@change="fastChange(item.codeValue)"
												>
													<input
														:id="'fast' + item.codeValue"
														v-model="fastCode"
														class="form-check-input"
														name="fastCode"
														:value="item.codeValue"
														type="radio"
													>
													<label class="form-check-label" :for="'fast' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>
										</div>
									</div>

									<div class="form-footer">
										<a class="btn btn-primary" @click.prevent="gotoFastPage(0)">{{ $t('pro.search') }}</a>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div v-if="pageData.content.length > 0" id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('pro.searchResults') }}</h4>
						<div style="display: flex">
							<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
						</div>
					</div>
					<div class="table-responsive">
						<table class="table table-bordered table-blue">
							<thead>
								<tr>
									<th>
										{{ $t('pro.productCode') }}<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('BANK_PRO_CODE')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('BANK_PRO_CODE')"
										/>
									</th>
									<th>
										{{ $t('pro.productChineseName') }}<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('PRO_NAME')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('PRO_NAME')"
										/>
									</th>
									<th>
										{{ $t('pro.riskLevel') }}<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('RISK_NAME')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('RISK_NAME')"
										/>
									</th>
									<th>
										{{ $t('pro.pricingCurrency') }}<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('CUR_CODE')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('CUR_CODE')"
										/>
									</th>
									<th>
										{{ $t('pro.referenceMarketValue') }}<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('A_PRICE')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('A_PRICE')"
										/>
									</th>
									<th>
										{{ $t('pro.marketValueDate') }}<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('PRICE_DT')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('PRICE_DT')"
										/>
									</th>
									<th>
										{{ $t('pro.salesTarget') }}<br>({{ $t('pro.piLimited') }})<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('PROF_INVESTOR_YN_NAME')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('PROF_INVESTOR_YN_NAME')"
										/>
									</th>
									<th>
										{{ $t('pro.sellable') }}<a
											v-if="activeTab === 'common'"
											href="#"
											class="icon-sort"
											@click="sort('BUY_YN_NAME')"
										/><a
											v-if="activeTab === 'fast'"
											href="#"
											class="icon-sort"
											@click="sortFast('BUY_YN_NAME')"
										/>
									</th>
									<th>{{ $t('pro.execute') }}</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(item, index) in pageData.content">
									<td :data-th="$t('pro.productCode')">
										<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
									</td>
									<td class="text-start" :data-th="$t('pro.productChineseName')">
										<span>
											<a class="tx-link" @click="pfdModalHandler(item.proCode, item.pfcatCode)">{{
												$filters.defaultValue(item.proName, '--')
											}}</a>
										</span>
									</td>
									<td :data-th="$t('pro.riskLevel')">
										<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
									</td>
									<td class="text-center" :data-th="$t('pro.pricingCurrency')">
										<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
									</td>
									<td class="text-center" :data-th="$t('pro.referenceMarketValue')">
										<span>{{ $filters.formatNumber(item.aprice, '--') }}</span>
									</td>
									<td class="text-center" :data-th="$t('pro.marketValueDate')">
										<span>{{ $filters.formatDate(item.priceDt, '--') }}</span>
									</td>
									<td class="text-center" :data-th="$t('pro.salesTarget') + '<br>(' + $t('pro.piLimited') + ')'">
										<span>{{ $filters.defaultValue(item.profInvestorYnName, '--') }}</span>
									</td>
									<td class="text-center" :data-th="$t('pro.sellable')">
										<span>{{ $filters.defaultValue(item.buyYnName, '--') }}</span>
									</td>
									<td class="text-center" :data-th="$t('pro.execute')">
										<button
											v-if="activeTab === 'fast' && fastCode === '06'"
											type="button"
											class="btn btn-primary"
											:title="$t('pro.removeFromFavorites')"
											@click="remove(item.proCode)"
										>
											{{ $t('pro.removeFavorite') }}
										</button>
										<button
											v-else
											type="button"
											class="btn btn-dark btn-icon"
											data-bs-toggle="tooltip"
											:title="$t('pro.addToFavorites')"
											@click="favoritesHandler(item.proCode, item.pfcatCode)"
										>
											<i class="bi bi-heart text-danger" />
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import pagination from '@/views/components/pagination.vue';
import vueModal from '@/views/components/model.vue';
import vueGroupGeofocusModal from './groupGeoFocusModal.vue';
import { getImgURL } from '@/utils/imgURL.js';
export default {
	components: {
		'vue-pagination': pagination,
		vueModal,
		vueGroupGeofocusModal
	},
	props: {
		pfdModalHandler: Function,
		favoritesHandler: Function,
		curOption: Array,
		riskMenu: Array
	},
	data: function () {
		return {
			activeTab: 'common',
			bankProCode: null, // 商品代碼
			proName: null, // {{ $t('pro.productName') }}
			curObjs: [], // {{ $t('pro.pricingCurrency') }}
			riskCodes: [], // 風險等級
			isinCode: null,
			profInvestorYn: '', // 限PI銷售
			geoFocusItem: [], // 投資地區

			fastCode: '03', // 快速篩選
			timeRange: null, // 快速 顯示區間
			rowNumber: '', // 快速 顯示資料筆數
			curCode: 'TWD', // 績效幣別
			perf: null, // 標的績效

			profInvestorMenu: [], // 限PI銷售選項
			pfdFastMenu: [], // 快速篩選選單

			// 查詢條件
			rateCurType: 'O', // (報酬率) 報酬率幣別顯示
			selectedItems: {}, // 加入比較選項
			pageData: {
				content: []
			},
			rankPageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: ['PRO_CODE'],
				direction: 'ASC'
			},
			isOpenGeoFocusModal: false
		};
	},
	watch: {
		selectedItems: {
			handler(newValues) {
				this.lipperIds = Object.keys(newValues).filter(lipperId => newValues[lipperId]);
			},
			deep: true
		},
		activeTab(newVal, oldVal) {
			const self = this;
			self.fastCode = '03';
		},
		selectedTab(newTab, oldTab) {
			const self = this;
			self.lipperIds = [];
			self.selectedItems = {};
		},
		curObjs(newVal, oldVal) {
			const self = this;
			if (newVal[0] !== oldVal[0]) {
				if (newVal[0] === '') {
					$('#curMenuPfd').selectpicker('selectAll');
				}
				else if (oldVal[0] === '' && newVal[0] !== '') {
					$('#curMenuPfd').selectpicker('deselectAll');
				}
			}
		}
	},
	mounted: function () {
		const self = this;
		$('#curMenuPfd').selectpicker('refresh');
		self.getProfInvestorMenu(); // 限PI銷售選項
		self.getPfdFastMenu(); // 取得快速篩選條件選項
	},
	methods: {
		getImgURL,
		// 條件Tab切換
		changeTab: function (tabName) {
			const self = this;
			self.activeTab = tabName;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
		},
		// 取得限PI銷售選項
		async getProfInvestorMenu() {
			const self = this;
			self.profInvestorMenu = [{ codeValue: '', codeName: this.$t('pro.unlimited') }];
			let selectYnList = [];
			const ret = await this.$api.getAdmCodeDetail({
				codeType: 'SELECT_YN'
			});

			selectYnList = ret.data;
			Array.prototype.push.apply(self.profInvestorMenu, selectYnList);
		},
		// 一般篩選  顯示選擇投資地區 model
		groupGeoFocusModalHandler: function () {
			const self = this;
			this.$refs.groupGeoFocusModalRef.geoFocusPropItem(self.geoFocusItem);
			this.isOpenGeoFocusModal = true;
		},
		// 顯示投資地區選擇項目
		selectedGeoFocus(geoFocusItem) {
			const self = this;
			self.isOpenGeoFocusModal = false;
			self.geoFocusItem = geoFocusItem; // 取得基金公司資料
		},
		// 刪除投資地區項目
		deleteGeoFocusItem(geoFocusCode) {
			const self = this;
			_.remove(self.geoFocusItem, item => item.geoFocusCode === geoFocusCode); // 移除刪除項目
		},
		// 取得快速篩選條件選項
		getPfdFastMenu: async function () {
			const self = this;
			const ret = await this.$api.getPfdFastMenuApi();
			self.pfdFastMenu = ret.data;
		},
		// 刪除我的最愛
		async remove(proCode) {
			const self = this;
			await this.$api.deleteFavoriteApi({
				proCode: proCode
			});
			this.$bi.alert(this.$t('pro.deleteSuccess'));
			self.checkboxs = [];
			self.proCodes = [];
			self.gotoFastPage(0);
		},
		// 由查詢結果標題觸發
		sort: function (columnName) {
			if (this.pageable.sort !== columnName) {
				this.pageable.sort = columnName;
				this.pageable.direction = 'DESC';
			}
			else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}

			this.gotoPage(0);
		},
		// 由快速查詢結果標題觸發
		sortFast: function (columnName) {
			if (this.pageable.sort !== columnName) {
				this.pageable.sort = columnName;
				this.pageable.direction = 'DESC';
			}
			else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}

			this.gotoFastPage(0, columnName);
		},
		fastChange(fastCode) {
			// 快速查詢切換
			const self = this;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			const self = this;
			let url = '';
			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			const geoFocusCodeList = self.geoFocusItem.map(function (item) {
				return item.geoFocusCode;
			});
			const ret = await this.$api.getPfdProductsApi(
				{
					bankProCode: self.bankProCode,
					proName: self.proName,
					curCodes: self.curObjs,
					riskCodes: self.riskCodes,
					isinCode: self.isinCode,
					profInvestorYn: self.profInvestorYn,
					geoFocusCodes: geoFocusCodeList
				},
				url
			);
			self.pageData = ret.data;
		},
		// 快速篩選
		gotoFastPage: function (page, sortColumnName) {
			this.pageable.page = page;
			this.getFastPageData(page, sortColumnName);
		},
		getFastPageData: async function (page, sortColumnName) {
			const self = this;

			let url = '';
			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;
			const ret = await this.$api.getPfdFastPageDataApi(
				{
					filterCodeValue: self.fastCode
				},
				url
			);
			self.pageData = ret.data;
		}
	}
};
</script>
<style scoped>
.dropdown.bootstrap-select {
	min-width: 0;
}
</style>
