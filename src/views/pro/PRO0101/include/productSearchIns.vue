<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<!--頁面內容 保險ins start-->
			<div class="tab-nav-tabs my-3">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'common' }"
							data-bs-toggle="tab"
							@click="changeTab('common')"
						>{{ $t('pro.generalFilter') }}</a>
					</li>
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'fast' }"
							data-bs-toggle="tab"
							@click="changeTab('fast')"
						>{{ $t('pro.quickFilter') }}</a>
					</li>
				</ul>

				<div class="tab-content">
					<div id="SectionA" class="tab-pane fade show" :class="{ 'active show': activeTab == 'common' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>{{ $t('pro.searchCond') }}</h4>
								<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
							</div>
							<div id="collapseListGroup1" class="collapse show">
								<div class="card-body">
									<vue-form v-slot="{ errors }" ref="searchQueryForm">
										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.insuranceCode') }}</label>
												<input
													id="prod_pro_code"
													v-model="bankProCode"
													class="form-control"
													maxlength="20"
													size="45"
													type="text"
												>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.insuranceProductName') }}</label>
												<input
													id="prod_pro_name"
													v-model="proName"
													class="form-control"
													maxlength="20"
													size="45"
													type="text"
												>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.insuranceSubcategory') }}</label>
												<select
													id="proTypeCode"
													v-model="proTypeCode"
													class="form-select"
													name="proTypeCode"
												>
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="(item, index) in proTypeMenu" :key="index" :value="item.proTypeCode">
														{{ $filters.defaultValue(item.proTypeName, '--') }}
													</option>
												</select>
												<div style="height: 3px">
													<span v-show="errors.proTypeCode" class="text-danger">{{
														$filters.defaultValue(errors.proTypeCode, '--')
													}}</span>
												</div>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.riskLevel') }}</label>
												<select
													id="riskCode"
													v-model="riskCode"
													class="form-select"
													name="riskCode"
												>
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="item in riskMenu" :value="item.riskCode">
														{{ $filters.defaultValue(item.riskName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.currency') }}</label>
												<select
													id="curMenuIns"
													v-model="curObjs"
													class="selectpicker form-control"
													multiple
													:title="$t('pro.pleaseSelectCurrency')"
													data-style="btn-white"
												>
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="(item, index) in curOption" :key="index" :value="item.value">
														{{ $filters.defaultValue(item.name, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.insuranceTermYears') }}</label>
												<div class="input-group">
													<input
														v-model="insTermDtStart"
														type="text"
														class="form-control"
														size="5"
													>
													<div class="input-group-text">
														~
													</div>
													<input
														v-model="insTermDtEnd"
														type="text"
														class="form-control"
														size="5"
													>
												</div>
											</div>
											<div class="form-group col-12 col-lg-5">
												<label class="form-label">{{ $t('pro.insuranceCompany') }}</label>
												<button type="button" class="btn btn-primary" @click="groupInsCmpModalHandler()">
													{{ $t('pro.selectInsuranceCompany') }}
												</button>
												<vue-modal :is-open="isOpenInsCmpModal" @close="isOpenInsCmpModal = false">
													<template #content="props">
														<vue-group-inscmp-modal
															id="groupInsCmpModal"
															ref="groupInsCmpModalRef"
															:close="props.close"
															:issuer-prop="insCmpItem"
															@selected="selectedInsCmp"
														/>
													</template>
												</vue-modal>
											</div>
											<div class="form-group col-12 col-lg-7">
												<label class="form-label">{{ $t('pro.productListingDate') }}</label>
												<div class="input-group">
													<input
														id="prod_chinese_name"
														v-model="stdDtStart"
														class="form-control"
														maxlength="20"
														name="prod_chinese_name"
														size="10"
														type="date"
													>
													<div class="input-group-text">
														~
													</div>
													<input
														id="prod_chinese_name"
														v-model="stdDtEnd"
														class="form-control"
														maxlength="20"
														name="prod_chinese_name"
														size="10"
														type="date"
													>
												</div>
											</div>
										</div>

										<div v-for="item in insCmpItem" style="padding-left: 120px; padding-bottom: 15px">
											<span class="form-check-label"> {{ $filters.defaultValue(item.inscmpName, '--') }}</span>
											<a href="#" @click="deleteInsCmpItem(item.inscmpCode)"><img
												:src="getImgURL('icon', 'i-cancel.png')"
											></a>
										</div>

										<div class="form-footer">
											<button class="btn btn-primary" @click.prevent="gotoPage(0)">
												{{ $t('pro.search') }}
											</button>
										</div>
									</vue-form>
								</div>
							</div>
						</div>
					</div>

					<div id="SectionB" class="tab-pane fade" :class="{ 'active show': activeTab == 'fast' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
								<h4>{{ $t('pro.searchCond') }}</h4>
								<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
							</div>
							<div id="collapseListGroup2" class="collapse show">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-12">
											<label class="form-label tx-require">{{ $t('pro.filterConditions') }}</label>
											<div v-for="item in insFastMenu" class="form-check-group" @change="fastChange(item.codeValue)">
												<input
													:id="'fast' + item.codeValue"
													v-model="fastCode"
													class="form-check-input"
													name="fastCode"
													:value="item.codeValue"
													type="radio"
												>
												<label class="form-check-label" :for="'fast' + item.codeValue">{{
													$filters.defaultValue(item.codeName, '--')
												}}</label>
											</div>
										</div>
										<div id="rangeFixedTrIns" class="form-group col-12 col-lg-6" style="display: none">
											<label class="form-label tx-require">{{ $t('pro.displayRange') }}</label>
											<select id="prod_protype_code" v-model="timeRange" class="form-select">
												<option v-for="item in timeRangeMenu" :value="item">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>

										<div id="proPerfTimeTrIns" class="form-group col-12 col-lg-6" style="display: none">
											<label class="form-label tx-require">{{ $t('pro.targetPerformance') }}</label>

											<select id="vfAstStat_stat_code" v-model="perf" class="form-select">
												<option v-for="item in perfMenu" :value="item.codeValue">
													{{ $filters.defaultValue(item.codeName, '--') }}
												</option>
											</select>
										</div>

										<div id="maxRowIdTrIns" class="form-group col-12 col-lg-6" style="display: none">
											<label class="form-label tx-require">{{ $t('pro.displayRecordCount') }}</label>
											<select id="maxRowId" v-model="rowNumber" class="form-select">
												<option value="">
													{{ $t('pro.all') }}
												</option>
												<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-footer">
										<button class="btn btn-primary" @click.prevent="gotoFastPage(0)">
											{{ $t('pro.search') }}
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div v-if="pageData.content.length > 0" id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('pro.searchResults') }}</h4>
						<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-bordered text-center">
							<thead>
								<tr>
									<th>{{ $t('pro.insuranceSubcategory') }}<a href="#" class="icon-sort" @click="sort('PROTYPE_NAME')" /></th>
									<th>{{ $t('pro.insuranceType') }}</th>
									<th>{{ $t('pro.insuranceCode') }}</th>
									<th>{{ $t('pro.productName') }}<a href="#" class="icon-sort" @click="sort('PRO_NAME')" /></th>
									<th>{{ $t('pro.insuranceCompany') }}<a href="#" class="icon-sort" @click="sort('INSCMP_NAME')" /></th>
									<th>{{ $t('pro.riskLevel') }}</th>
									<th>{{ $t('pro.currency') }}<a href="#" class="icon-sort" @click="sort('CUR_CODE')" /></th>
									<th>{{ $t('pro.insuranceTerm') }}</th>
									<th>{{ $t('pro.listingDate') }}</th>
									<th class="text-center" width="120">
										{{ $t('pro.observation') }}
									</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(item, index) in pageData.content">
									<td :data-th="$t('pro.insuranceSubcategory')">
										<span>{{ $filters.defaultValue(item.proTypeName, '--') }}</span>
									</td>
									<td :data-th="$t('pro.insuranceType')">
										<span>{{ $filters.defaultValue(item.insType, '--') }}</span>
									</td>
									<td :data-th="$t('pro.insuranceCode')">
										<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
									</td>
									<td class="text-start" :data-th="$t('pro.productName')">
										<span>
											<a class="tx-link" href="#" @click="insModalHandler(item.proCode, item.pfcatCode)">{{
												$filters.defaultValue(item.proName, '--')
											}}</a>
										</span>
									</td>
									<td class="text-end" :data-th="$t('pro.insuranceCompany')">
										<span>{{ $filters.defaultValue(item.inscmpName, '--') }}</span>
									</td>
									<td class="text-end" :data-th="$t('pro.riskLevel')">
										<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
									</td>
									<td class="text-end" :data-th="$t('pro.currency')">
										<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
									</td>
									<td class="text-end" :data-th="$t('pro.insuranceTerm')">
										<span>{{ $filters.defaultValue(item.insTerm, '--') }}</span>
									</td>
									<td class="text-end" :data-th="$t('pro.listingDate')">
										<span>{{ $filters.defaultValue(item.stdDt, '--') }}</span>
									</td>
									<td class="text-center" :data-th="$t('pro.observation')">
										<button
											v-if="activeTab === 'fast' && fastCode === '06'"
											type="button"
											class="btn btn-primary"
											:title="$t('pro.removeFromFavorites')"
											@click="remove(item.proCode)"
										>
											{{ $t('pro.removeFavorite') }}
										</button>
										<button
											v-else
											type="button"
											class="btn btn-dark btn-icon"
											data-bs-toggle="tooltip"
											:title="$t('pro.addToFavorites')"
											@click="favoritesHandler(item.proCode, item.pfcatCode)"
										>
											<i class="bi bi-heart text-danger" />
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="tx-note">
					<ol>
						<li><span>{{ $t('pro.dataDate') }}：</span></li>
						<li><span>{{ $t('pro.productPurchaseAvailabilityNote') }}</span></li>
					</ol>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import pagination from '@/views/components/pagination.vue';
import { Form, Field } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import vueGroupInscmpModal from './groupInsCmpModal.vue';
import { getImgURL } from '@/utils/imgURL.js';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		'vue-pagination': pagination,
		vueModal,
		vueGroupInscmpModal
	},
	props: {
		insModalHandler: Function,
		favoritesHandler: Function,
		curOption: Array,
		riskMenu: Array
	},
	data: function () {
		return {
			activeTab: 'common',
			perf: '',
			insFastMenu: [],
			perfMenu: [],

			bankProCode: null, // 保險險種代號
			proName: null, // 保險商品名稱
			proTypeCode: '', // 保險分類
			riskCode: '', // 風險等級
			curObjs: [], // 計價幣別
			insTermDtStart: null, // 保險年期(年)-起
			insTermDtEnd: null, // 保險年期(年)-迄
			insCmpItem: [], // 保險公司
			stdDtStart: null, // 商品上架日-起
			stdDtEnd: null, // 商品上架日-迄

			fastCode: '03', // 快速 篩選條件
			timeRange: null, // 快速 顯示區間
			rowNumber: null, // 快速 顯示資料筆數

			proTypeMenu: [], // 保險分類

			proCodes: [], // 移除我的最愛按鈕、執行績效比較圖
			selectedItems: {}, // 加入比較選項
			timeRangeMenu: [], // 顯示區間
			rowNumerMenu: [], // 顯示資料筆數

			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: ['PRO_CODE'],
				direction: 'ASC'
			},
			isOpenInsCmpModal: false
		};
	},
	watch: {
		selectedItems: {
			handler(newValues) {
				this.proCodes = Object.keys(newValues).filter(proCode => newValues[proCode]);
			},
			deep: true
		},
		activeTab(newVal, oldVal) {
			const self = this;
			self.fastCode = '03';
			self.fastChange(self.fastCode);
		},
		curObjs(newVal, oldVal) {
			const self = this;
			if (newVal[0] !== oldVal[0]) {
				if (newVal[0] === '') {
					$('#curMenuIns').selectpicker('selectAll');
				}
				else if (oldVal[0] === '' && newVal[0] !== '') {
					$('#curMenuIns').selectpicker('deselectAll');
				}
			}
		}
	},
	mounted: function () {
		const self = this;
		$('#curMenuIns').selectpicker('refresh');
		self.getProTypeMenu(); // 取得保險分類選項
		self.getInsFastMenu(); // 取得快速篩選條件選項
		self.getTimeRangeMenu(); // 取得顯示區間選項
		self.getRowNumerMenu(); // 取得顯示資料筆數選項
	},
	methods: {
		getImgURL,
		// 條件Tab切換
		changeTab: function (tabName) {
			const self = this;
			self.activeTab = tabName;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
		},
		// 取得保險分類選項
		async getProTypeMenu() {
			const ret = await this.$api.getProTypeListApi({
				pfcatCode: 'INS'
			});
			this.proTypeMenu = ret.data;
		},
		// 取得快速篩選條件選項
		async getInsFastMenu() {
			const ret = await this.$api.getInsFastFilterMenuApi();
			this.insFastMenu = ret.data;
		},
		// 取得顯示區間
		async getTimeRangeMenu() {
			const ret = await this.$api.getTimeRangeMenuApi();
			this.timeRangeMenu = ret.data;
		},
		// 取得顯示資料筆數
		async getRowNumerMenu() {
			const ret = await this.$api.getRowNumerMenuApi();
			this.rowNumerMenu = ret.data;
		},
		fastChange(fastCode) {
			// 快速查詢切換
			const self = this;
			self.timeRange = {}; // 快速 顯示區間
			self.rowNumber = ''; // 快速 顯示資料筆數
			self.perf = '';
			self.pageData = {
				// 清空查詢資料
				content: []
			};
			if (fastCode === '05') {
				// 超人氣
				$('#proPerfTimeTrIns').hide();
				$('#rangeFixedTrIns').show();
				$('#maxRowIdTrIns').show();
				self.timeRange = self.timeRangeMenu[0];
			}
			else if (fastCode === '07') {
				// 績效排行
				$('#rangeFixedTrIns').hide();
				$('#maxRowIdTrIns').show();
				$('#proPerfTimeTrIns').show();
				self.perf = 'PCTYTD';
			}
			else if (fastCode === '08') {
				// 最高配息率商品
				$('#maxRowIdTrIns').show();
				$('#rangeFixedTrIns').hide();
				$('#proPerfTimeTrIns').hide();
			}
			else {
				$('#maxRowIdTrIns').hide();
				$('#rangeFixedTrIns').hide();
				$('#proPerfTimeTrIns').hide();
			}
		},
		// 由查詢結果標題觸發
		sort: function (columnName) {
			const self = this;
			if (this.pageable.sort !== columnName) {
				this.pageable.sort = columnName;
				this.pageable.direction = 'DESC';
			}
			else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}

			if (self.activeTab === 'common') {
				this.gotoPage(0);
			}
			else if (self.activeTab === 'fast') {
				this.gotoFastPage(0);
			}
		},
		gotoPage: function (page) {
			const self = this;
			self.$refs.searchQueryForm.validate().then(function (pass) {
				if (pass.valid) {
					self.pageable.page = page;
					self.getPageData(page);
				}
			});
		},
		async getPageData(_page) {
			const self = this;
			const page = _.isNumber(_page) ? _page : this.pageable.page;
			const queryString = `?page=${page}&size=${this.pageable.size}&sort=${this.pageable.sort},${this.pageable.direction}`;

			const cmpCodes = this.insCmpItem.map(item => item.inscmpCode);
			const ret = await this.$api.getInsProductsApi(
				{
					bankProCode: self.bankProCode, // 保險險種代號
					proName: self.proName, // 保險商品名稱
					protypeCode: self.proTypeCode, // 保險分類
					riskCode: self.riskCode, // 風險等級
					curCodes: self.curObjs, // 計價幣別
					insTermDtStart: self.insTermDtStart, // 保險年期(年)-起
					insTermDtEnd: self.insTermDtEnd, // 保險年期(年)-迄
					inscmpCodes: cmpCodes, // 保險公司
					stdDtStart: self.stdDtStart, // 商品上架日-起
					stdDtEnd: self.stdDtEnd // 商品上架日-迄
				},
				queryString
			);

			this.pageData = ret.data;
			this.$forceUpdate();
		},
		gotoFastPage: function (page) {
			this.pageable.page = page;
			this.getFastPageData(page);
		},
		async getFastPageData(_page) {
			const page = _.isNumber(_page) ? _page : this.pageable.page;
			const queryString = `?page=${page}&size=${this.pageable.size}&sort=${this.pageable.sort},${this.pageable.direction}`;

			const ret = await this.$api.getInsProductsFilterQueryApi(
				{
					filterCodeValue: this.fastCode,
					timeRangeType: this.timeRange.rangeType,
					timeRangeFixed: this.timeRange.rangeFixed,
					rowNumberFixed: this.rowNumber
				},
				queryString
			);

			this.pageData = ret.data;
		},
		groupInsCmpModalHandler: function () {
			// 顯示保險公司 model
			const self = this;
			this.$refs.groupInsCmpModalRef.inscmpPropItem(self.insCmpItem);
			this.isOpenInsCmpModal = true;
		},
		selectedInsCmp(insCmpItem) {
			// 顯示保險公司選擇項目
			const self = this;
			this.isOpenInsCmpModal = false;
			self.insCmpItem = insCmpItem; // 取得保險公司資料
		},
		deleteInsCmpItem(inscmpCode) {
			const self = this;
			_.remove(self.insCmpItem, item => item.inscmpCode === inscmpCode); // 移除刪除項目
		},
		// 刪除我的最愛
		async remove(proCode) {
			await this.$api.deleteFavoriteApi({
				proCode: proCode
			});
			this.$bi.alert(this.$t('pro.deleteSuccess'));
			this.checkboxs = [];
			this.proCodes = [];
			this.gotoFastPage(0);
		}
	} // methods end
};
</script>
