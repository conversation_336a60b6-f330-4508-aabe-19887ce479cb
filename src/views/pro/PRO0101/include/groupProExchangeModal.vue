<template>
	<!-- Modal group ProExchange start -->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('pro.selectExchange') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click="close()"
				/>
			</div>
			<div class="modal-body">
				<div class="row m-2">
					<div v-for="item in proExchangesMenu" class="col-3 form-check">
						<input
							:id="item.exchangeCode"
							v-model="proExchangeCode"
							class="form-check-input"
							name="finReqCodes"
							:value="item.exchangeCode"
							type="checkbox"
						>
						<label class="form-check-label" :for="item.exchangeCode">{{ item.exchangeName }}</label>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<input
					id="modaladdButton"
					type="button"
					class="btn btn-primary"
					:value="$t('pro.add')"
					@click="addProExchange()"
				>
			</div>
		</div>
	</div>
	<!-- Modal group ProExchange End -->
</template>
<script>
import _ from 'lodash';

export default {
	props: {
		proExchangeProp: Array, // 已選擇項目
		close: Function
	},
	data: function () {
		return {
			proExchangesMenu: [], // 發行機構 選項
			proExchangeCode: [], // 發行機構 選擇項目
			proExchangeItem: [] // 發行機構 選擇項目代碼與中文名稱
		};
	},
	watch: {},
	mounted: function () {
		const self = this;
		self.groupProExchangesMenu();
		self.proExchangeItem = [];
	},
	methods: {
		groupProExchangesMenu: async function () {
			// 發行機構來源資料
			const self = this;
			const ret = await this.$api.getGroupProExchangesMenuApi();
			const englishNames = [];
			const numberNames = [];
			const chineseNames = [];

			ret.data.forEach((item) => {
				const name = item.exchangeName.trim();
				const type = self.getStringType(name);

				// 判斷英文/數字/中文
				if (type === 'english') {
					englishNames.push(item);
				}
				else if (type === 'number') {
					numberNames.push(item);
				}
				else {
					chineseNames.push(item);
				}
			});

			// 合併結果
			self.proExchangesMenu = [
				...numberNames.sort((a, b) => a.exchangeName.localeCompare(b.exchangeName, undefined, { numeric: true })),
				...englishNames.sort((a, b) => a.exchangeName.localeCompare(b.exchangeName)),
				...chineseNames.sort((a, b) => a.exchangeName.localeCompare(b.exchangeName, 'zh-Hant-u-co-stroke'))
			];
		},
		addProExchange() {
			// 增加發行機構
			const self = this;
			self.proExchangeItem = [];
			if (self.proExchangeCode.length === 0) {
				this.$bi.alert(this.$t('pro.pleaseSelectAtLeastOne'));
			}
			else {
				self.proExchangeCode.forEach((code) => {
					const item = _.find(self.proExchangesMenu, { exchangeCode: code });
					self.proExchangeItem.push(item);
					self.$emit('selected', self.proExchangeItem);
					self.close();
				});
			}
		},
		proExchangePropItem(v) {
			const self = this;
			if (v.length > 0) {
				self.proExchangeItem = v;
				self.proExchangeCode = [];
				v.forEach((item) => {
					self.proExchangeCode.push(item.exchangeCode);
				});
			}
			else {
				self.proExchangeItem = [];
				self.proExchangeCode = [];
			}
		},
		// 全形轉半形
		toHalfWidth: function (str) {
			return str
				.replace(/[\uff01-\uff5e]/g, function (char) {
					return String.fromCharCode(char.charCodeAt(0) - 0xfee0);
				})
				.replace(/\u3000/g, ' ');
		},
		getStringType: function (str) {
			const convertedStr = this.toHalfWidth(str);
			const firstChar = convertedStr.charAt(0);

			if (/[a-zA-Z]/.test(firstChar)) return 'english';
			if (/[0-9]/.test(firstChar)) return 'number';
			return 'chinese';
		}
	}
	// methods end
};
</script>
