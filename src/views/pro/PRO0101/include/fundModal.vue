<template>
	<!-- Modal 1 基金 -->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('pro.trustFund') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click.prevent="close()"
				/>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-fund" />
							<h4>
								<span>{{ $t('pro.productName') }}</span> <br>{{ $filters.defaultValue(proInfo.proName, '--') }} <br><span class="tx-black">{{
									$filters.defaultValue(proInfo.proEName, '--')
								}}</span>
							</h4>
						</div>
						<h4 class="pro_value">
							<span>{{ $t('pro.latestNav') }}</span>
							<br>{{ $filters.formatNumber(proInfo.aprice, '0,0.00' || '--') }} <br><span>{{
								$filters.formatDate(proInfo.priceDt, '--')
							}}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productCode') }}</span> <br>{{ $filters.defaultValue(proInfo.bankProCode, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6>
								<span>{{ $t('pro.assetCategory') }} <br></span>{{ $filters.defaultValue(proInfo.assetcatName, '--') }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productMainCategory') }}</span> <br>{{ $filters.defaultValue(proInfo.pfcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productSubCategory') }}</span><br>{{ $filters.defaultValue(proInfo.proTypeName, '--') }}</h6>
						</div>
					</div>
				</div>
				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item">
							<a
								class="nav-link"
								href="#Section1"
								data-bs-toggle="pill"
								:class="{ active: activeTab == 'section1' }"
								@click="changeTab('section1')"
							>{{ $t('pro.productBasicData') }}</a>
						</li>
						<li class="nav-item">
							<a
								class="nav-link"
								href="#Section2"
								data-bs-toggle="pill"
								:class="{ active: activeTab == 'section2' }"
								@click="changeTab('section2')"
							>{{ $t('pro.productCommonData') }}</a>
						</li>
						<li class="nav-item">
							<a
								class="nav-link"
								href="#Section3"
								data-bs-toggle="pill"
								:class="{ active: activeTab == 'section3' }"
								@click="changeTab('section3')"
							>{{ $t('pro.productAdditionalData') }}</a>
						</li>
						<li class="nav-item">
							<a
								v-if="proInfo.fundInfo.lipperId"
								class="nav-link datainfo"
								href="#Section4"
								data-bs-toggle="pill"
								:class="{ active: activeTab == 'section4' }"
								@click="changeTab('section4')"
							>{{ $t('pro.fundData') }}</a>
						</li>
					</ul>

					<div class="tab-content">
						<div id="Section1" class="tab-pane show" :class="{ 'active show': activeTab == 'section1' }">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.fundProductInfo') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th class="wd-20p">
												{{ $t('pro.fundCompanyName') }}
											</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo && proInfo.fundInfo.compName, '--') }}
											</td>
											<th class="wd-20p">
												{{ $t('pro.lipperGlobalClassification') }}
											</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo && proInfo.fundInfo.lipperTypeName, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.fundType') }}</th>
											<td colspan="3">
												{{ $filters.defaultValue(proInfo && proInfo.fundInfo.proTypeName, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.domesticFund') }}</th>
											<td>{{ $filters.defaultValue(proInfo.fundInfo.localYn, '--') }}</td>
											<th>{{ $t('pro.fundStatus') }}</th>
											<td>{{ $filters.defaultValue(proInfo.fundInfo.status, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.hundredFund') }}</th>
											<td>{{ $filters.defaultValue(proInfo.fundInfo.hundredYn, '--') }}</td>

											<th>{{ $t('pro.backendLoadFund') }}</th>
											<td>{{ $filters.defaultValue(proInfo.fundInfo.backEndLoadYn, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.productRiskLevel') }}</th>
											<td>
												{{ $filters.defaultValue(proInfo && proInfo.fundInfo.riskName, '--') }}
											</td>
											<th>{{ $t('pro.pricingCurrency') }}</th>
											<td>
												{{ $filters.defaultValue(proInfo && proInfo.fundInfo.curCode, '--') }}
											</td>
										</tr>
										<tr>
											<th>Lipper Code</th>
											<td>
												{{ $filters.defaultValue(proInfo && proInfo.fundInfo.lipperId, '--') }}
											</td>
											<th>{{ $t('pro.internationalCode') }}</th>
											<td>
												{{ $filters.defaultValue(proInfo && proInfo.fundInfo.isinCode, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.investmentAmountLimit') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th class="wd-20p">
												{{ $t('pro.twdSingleMinInvestment') }}
											</th>
											<td class="wd-30p">
												{{ $filters.formatNumber(proInfo.fundInfo.mininvLcAmt, '0,0.00' || '--') }}
											</td>
											<th class="wd-20p">
												{{ $t('pro.twdRegularMinInvestment') }}
											</th>
											<td class="wd-30p">
												{{ $filters.formatNumber(proInfo.fundInfo.mininvLcAmtp, '0,0.00' || '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.foreignCurrencySingleMinInvestment') }}</th>
											<td>
												{{ $filters.formatNumber(proInfo.fundInfo.mininvFcAmt, '0,0.00' || '--') }}
											</td>
											<th>{{ $t('pro.foreignCurrencyRegularMinInvestment') }}</th>
											<td>
												{{ $filters.formatNumber(proInfo.fundInfo.mininvFcAmtp, '0,0.00' || '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Section2" class="tab-pane fade" :class="{ 'active show': activeTab == 'section2' }">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.salesRelatedData') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th width="20%">
												{{ $t('pro.salesArea') }}
											</th>
											<td v-if="proInfo.allYn == 'Y'" width="30%">
												{{ $t('pro.wholeBranch') }}
											</td>
											<td v-else width="30%">
												--
											</td>
											<th width="20%">
												{{ $t('pro.limitedPISubscription') }}
											</th>
											<td width="30%">
												{{ $filters.defaultValue(proInfo.profInvestorYn, '--') }}
											</td>
										</tr>
										<tr>
											<th width="20%">
												{{ $t('pro.salesTarget') }}
											</th>
											<td width="30%" colspan="3">
												{{ $filters.defaultValue(proInfo.targetCusBu, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.subscriptionAvailable') }}</th>
											<td>{{ $filters.defaultValue(proInfo.buyYn, '--') }}</td>
											<th>{{ $t('pro.redemptionAvailable') }}</th>
											<td>{{ $filters.defaultValue(proInfo.sellYn, '--') }}</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.volatilityType') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.volatilityType, '--') }}</span>
											</td>
											<th><span>{{ $t('pro.dividendFrequency') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.intFreqUnitype, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.principalRequirement') }}</th>
											<td colspan="3">
												{{ $filters.defaultValue(proInfo.principalGuarYn, '--') }}
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.financialNeeds') }}</span></th>
											<td colspan="3">
												<div v-for="item in finReqCodeMenu" class="form-check form-check-inline">
													<input
														id="c1"
														v-model="finReqCodes"
														class="form-check-input"
														name="finReqCodes"
														disabled
														:value="item.codeValue"
														type="checkbox"
													>
													<label class="form-check-label" for="c1">{{ $filters.defaultValue(item.codeName, '--') }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.productTags') }}</th>
											<td disabled="disabled" colspan="3">
												{{ $filters.defaultValue(proInfo.selprocatNames, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Section3" class="tab-pane fade" :class="{ 'active show': activeTab == 'section3' }">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.otherSettings') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>{{ $t('pro.productInvestmentTarget') }}</span></th>
											<td class="wd-80p">
												<span>{{ $filters.defaultValue(proInfo.sectorName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.productInvestmentArea') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.geoFocusName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.benchmarkSetting') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.benchmarkName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.remarks') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.memo, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.relatedAttachments') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.prospectus') }}</th>
											<td class="wd-80p">
												<a v-if="proFileB && proFileB.url" :href="proFileB.url" target="_blank">{{ proFileB.url }}</a><br v-if="proFileB && proFileB.url">
												<a v-else>--</a>
												<a
													v-if="proFileB"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileB)"
												>{{
													$filters.defaultValue(proFileB.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.investorNotice') }}</th>
											<td class="wd-80p">
												<a v-if="proFileD && proFileD.url" :href="proFileD.url" target="_blank">{{ proFileD.url }}</a><br v-if="proFileD && proFileD.url">
												<a v-else>--</a>
												<a
													v-if="proFileD"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileD)"
												>{{
													$filters.defaultValue(proFileD.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.fundMonthlyReport') }}</th>
											<td class="wd-80p">
												<a v-if="proFileE && proFileE.url" :href="proFileE.url" target="_blank">{{ proFileE.url }}</a><br v-if="proFileE && proFileE.url">
												<a v-else>--</a>
												<a
													v-if="proFileE"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileE)"
												>{{
													$filters.defaultValue(proFileE.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<a v-if="proFileF && proFileF.url" :href="proFileF.url" target="_blank">{{ proFileF.url }}</a><br v-if="proFileF && proFileF.url"><a v-else>--</a>
												<a
													v-if="proFileF"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileF)"
												>{{
													$filters.defaultValue(proFileF.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.others') }}</th>
											<td class="wd-80p">
												<a v-if="proFileG && proFileG.url" :href="proFileG.url" target="_blank">{{ proFileG.url }}</a><br v-if="proFileG && proFileG.url"><a v-else>--</a>
												<a
													v-if="proFileG"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileG)"
												>{{
													$filters.defaultValue(proFileG.showName, '--')
												}}</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.otherRelatedAttachments') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<td>
												<span v-for="(item, index) in otherFileList">
													<a
														v-if="index === otherFileList.length - 1"
														v-show="item.show"
														href="#"
														class="tx-link"
														@click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}</a>
													<a
														v-else
														v-show="item.show"
														href="#"
														class="tx-link"
														@click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}、</a>
												</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="tx-note">
								{{ $t('pro.otherRelatedAttachmentsNote') }}
							</div>
						</div>

						<!-- vue2 fund -->
						<div
							v-if="proInfo.fundInfo.lipperId"
							id="Section4"
							class="tab-pane fade"
							:class="{ 'active show': activeTab == 'section4' }"
						>
							<div class="container">
								<div class="card m-t-30">
									<div class="card-header bg-white">
										<div class="card-title">
											{{ $filters.defaultValue(fundInfo && fundInfo.fundEnName, '--') }}
										</div>
										<p>
											<b>{{ $t('pro.fundEnglishName') }}</b>&nbsp;{{ $filters.defaultValue(fundInfo && fundInfo.fundEnName, '--') }}
											<span class="hint-text"> ｜ </span>
											<b>{{ $filters.defaultValue(fundInfo && fundInfo.investmentTypeName, '--') }}</b>
											<span class="hint-text"> ｜ </span>
											<b>{{ $t('pro.lipperCode') }}</b>&nbsp;{{ $filters.defaultValue(fundInfo && fundInfo.fundCode, '--') }}
											<span class="hint-text"> ｜ </span> <b><a href="#DD" class="link">{{ $t('pro.lipperTotalReturn') }}</a></b>
											<vue-fund-lipper-score :lipper-scores="lipperScores" score-code="TOTRETOV" />
											<br>
											<span class="hint-text" /> <b>{{ $t('pro.fundManagerBenchmark') }}</b>&nbsp;
											<span
												v-if="fundInfo && getRealAssetCode(fundInfo.managerBmCode) && fundInfo.managerBmName"
												href="#"
												class="text-info p-t-5"
												style="cursor: pointer"
											>
												{{ $filters.defaultValue(fundInfo.managerBmName, '--') }}
											</span>
											<span v-else>N/A</span>
											<span class="hint-text"> ｜ </span> <b>{{ $t('pro.technicalBenchmark') }}</b>&nbsp;
											<span
												v-if="fundInfo && getRealAssetCode(fundInfo.analysisBmCode) && fundInfo.analysisBmName"
												href="#"
												class="text-info p-t-5"
												style="cursor: pointer"
											>
												{{ $filters.defaultValue(fundInfo.analysisBmName, '--') }}
											</span>
											<span v-else>N/A</span>
										</p>
									</div>
									<div id="fund-basic" class="card-body">
										<div class="row justify-content-between text-center m-t-20">
											<div class="col-6 col-md-4 col-lg-2">
												<h4 class="font-xl no-margin">
													<span v-html="formattedValue ? formattedValue : '--'" />
													<small class="font-xs">%</small>
												</h4>
												<p class="small no-margin">
													{{ $t('pro.oneYearCumulativeReturn') }}
												</p>
											</div>
											<div class="col-6 col-md-4 col-lg-2">
												<h4 class="font-xl no-margin">
													<span>
														{{ getTech(techs, 'ASHP1Y') }}
													</span>
												</h4>
												<p class="small no-margin">
													＊ {{ $t('pro.annualizedSharpeRatio') }}
												</p>
											</div>
											<div class="col-6 col-md-4 col-lg-2">
												<h4 class="font-xl no-margin">
													<span>
														{{ getTech(techs, 'AALP1Y') }}
													</span>
													<small class="font-xs">%</small>
												</h4>
												<p class="small no-margin">
													＊ {{ $t('pro.annualizedAlpha') }}
												</p>
											</div>
											<div class="col-6 col-md-4 col-lg-2">
												<h4 class="font-xl no-margin">
													<span style="color: #000000">
														{{ getTech(techs, 'ASTD1Y') }}
													</span>
													<small class="font-xs">%</small>
												</h4>
												<p class="small no-margin">
													＊ {{ $t('pro.annualizedStandardDeviation') }}
												</p>
											</div>
											<div class="col-6 col-md-4 col-lg-2">
												<h4 class="font-xl no-margin">
													<span>
														{{ getTech(techs, 'BET1Y') }}
													</span>
												</h4>
												<p class="small no-margin">
													＊ {{ $t('pro.beta') }}
												</p>
											</div>
											<div class="col-6 col-md-4 col-lg-2">
												<h4 class="font-xl no-margin">
													<span>
														{{ getTech(techs, 'RRK1Y') }}
													</span>
												</h4>
												<p class="small no-margin">
													＊ {{ $t('pro.returnRisk') }}
												</p>
											</div>
										</div>
										<p class="small mt-3 mb-0 text-right">
											(＊{{ $t('pro.calculatedFromOneYearData') }})
										</p>
										<div class="row">
											<!--左側-->
											<div class="col-md-12 col-lg-6 col-left">
												<div class="row b-a b-grey padding-10">
													<div class="col-4 text-center bg-contrast-lower padding-10">
														<p class="small no-margin">
															{{ $t('pro.fundNav') }}
														</p>
														<h4 class="font-lg no-margin">
															{{
																$filters.defaultValue(
																	fundInfo && $filters.formatNum(fundInfo.priceLc, '0,0.[0000]'),
																	'--'
																)
															}}
														</h4>
														<p class="no-margin small hint-text">
															（{{ $filters.defaultValue(fundInfo && $filters.formatDate(fundInfo.dataDate), '--') }}）
														</p>
													</div>
													<div class="col-4 text-center bg-contrast-lower padding-10">
														<p class="small no-margin">
															{{ $t('pro.fluctuation') }}
														</p>
														<h4 class="font-lg no-margin">
															<span
																v-html="
																	$filters.defaultValue(
																		fundInfo && $filters.formatFlucWithView(fundInfo.priceFlucLc),
																		'--'
																	)
																"
															/>
														</h4>
														<p>
															(<span
																v-html="
																	$filters.defaultValue(
																		fundInfo && $filters.formatFlucWithView(fundInfo.priceFlucLcRate, '%'),
																		'--'
																	)
																"
															/>)
														</p>
													</div>
													<div class="col-4 text-center bg-contrast-lower padding-10">
														<p class="small no-margin">
															{{ $t('pro.fundSize') }}
														</p>
														<h4 class="font-lg no-margin">
															{{
																$filters.defaultValue(
																	fundInfo && $filters.formatTwMillionUnit(fundInfo.tnaValueLc),
																	'--'
																)
															}}
															<small class="font-xs">({{ fundInfo && $filters.defaultValue(fundInfo.launchCurrencyName, '--') }})</small>
														</h4>
														<p class="no-margin small hint-text">
															{{ $t('pro.comparedToLastMonth') }}<span
																v-html="$filters.defaultValue($filters.formatFlucWithView(tnaValueRate, '%'), '--')"
															/>
														</p>
													</div>
												</div>
												<div class="row m-t-10">
													<div class="col-12">
														<table class="table table-bordered">
															<tbody>
																<tr>
																	<th width="20%">
																		{{ $t('pro.fundCharacteristics') }}
																	</th>
																	<td width="80%">
																		{{ $filters.defaultValue(fundInfo && fundInfo.text, '--') }}
																	</td>
																</tr>
																<tr>
																	<th>{{ $t('pro.sameLipperGlobalClassFunds') }}</th>
																	<td>
																		<div class="table-scroll ht-100" style="white-space: unset">
																			<vue-fund-same-global-class-fund
																				v-if="fundInfo && fundInfo.fundCode"
																				:fund-code="fundInfo.fundCode"
																				:global-class-code="fundInfo.globalClassCode"
																			/>
																		</div>
																	</td>
																</tr>
															</tbody>
														</table>
													</div>
												</div>
												<hr>
												<div class="row m-t-10">
													<vue-fund-tech v-if="showColumnChart" :techs="techs" />
												</div>
												<div class="row m-t-20">
													<vue-fund-price-history
														v-if="fundInfo && fundInfo.fundCode"
														:fund-code="fundInfo.fundCode"
													/>
												</div>
											</div>
											<!--右側-->
											<div class="col-md-12 col-lg-6 col-right">
												<table width="100%" class="table table-bordered">
													<tbody>
														<tr>
															<th>{{ $t('pro.fundLaunchDate') }}</th>
															<td>
																{{
																	$filters.defaultValue(fundInfo && $filters.formatDate(fundInfo.launchDate), '--')
																}}
															</td>
														</tr>
														<tr>
															<th>{{ $t('pro.domicile') }}</th>
															<td>
																{{ $filters.defaultValue(fundInfo && fundInfo.domicileName, '--') }}
															</td>
														</tr>
														<tr>
															<th>{{ $t('pro.currentPricingCurrency') }}</th>
															<td>
																{{ $filters.defaultValue(fundInfo && fundInfo.launchCurrencyName, '--') }}
															</td>
														</tr>
														<tr>
															<th>{{ $t('pro.investmentTargetLipperGlobal') }}</th>
															<td>
																{{ $filters.defaultValue(fundInfo && fundInfo.globalClassName, '--') }}
															</td>
														</tr>
														<tr>
															<th>{{ $t('pro.investmentTargetLipperRegional') }}</th>
															<td>
																{{ $filters.defaultValue(fundInfo && fundInfo.localClassName, '--') }}
															</td>
														</tr>
														<tr>
															<th>{{ $t('pro.investmentRegion') }}</th>
															<td>
																{{ $filters.defaultValue(fundInfo && fundInfo.geoFocusName, '--') }}
															</td>
														</tr>
														<tr>
															<th>{{ $t('pro.umbrellaFund') }}</th>
															<td>{{ fundInfo && fundInfo.umbrellaFundName ? fundInfo.umbrellaFundName : '--' }}</td>
														</tr>
													</tbody>
												</table>
												<vue-fund-fund-code
													v-if="fundInfo && fundInfo.fundCode"
													:fund-code="fundInfo.fundCode"
												/>
												<div v-show="fundInfo && fundInfo.divsPerYear > 0" class="row">
													<h4>{{ $t('pro.dividend') }}</h4>
													<table class="table table-bordered">
														<tbody>
															<tr>
																<th width="45%">
																	{{ $t('pro.annualDividendFrequency') }}
																</th>
																<td width="55%" class="text-right">
																	{{ $filters.formatNumber(fundInfo && fundInfo.divsPerYear, '--') }}
																</td>
															</tr>
															<tr>
																<th>{{ $t('pro.navEndOfLastMonth') }}</th>
																<td class="text-right">
																	{{
																		$filters.defaultValue(
																			fundInfo && $filters.formatNum(preMonthEndPrice, '0,0.00'),
																			'--'
																		)
																	}}
																</td>
															</tr>
															<tr>
																<th>{{ $t('pro.ytdIncomeFrom10000Investment') }}</th>
																<td class="text-right">
																	{{
																		$filters.defaultValue(
																			fundInfo && $filters.formatNum(fundInfo.incomeYtm, '0,0.00'),
																			'--'
																		)
																	}}
																</td>
															</tr>
														</tbody>
													</table>
												</div>
												<h4>{{ $t('pro.custodian') }}</h4>
												<table class="table table-bordered">
													<tbody>
														<tr>
															<th width="45%">
																{{ $t('pro.companyShortName') }}
															</th>
															<td width="55%">
																{{
																	$filters.defaultValue(
																		getCompanyField(companies, 'CUST', 'companyShortName'),
																		'--'
																	)
																}}
															</td>
														</tr>
														<tr>
															<th>{{ $t('pro.telephone') }}</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'CUST', 'telephone'), '--') }}
															</td>
														</tr>
														<tr>
															<th>{{ $t('pro.fax') }}</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'CUST', 'fax'), '--') }}
															</td>
														</tr>
														<tr>
															<th>Email</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'CUST', 'email'), '--') }}
															</td>
														</tr>
														<tr>
															<th>{{ $t('pro.website') }}</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'CUST', 'website'), '--') }}
															</td>
														</tr>
														<tr>
															<th>{{ $t('pro.fundsUnderManagement') }}</th>
															<td>
																<div class="table-scroll ht-100" style="white-space: unset">
																	<vue-fund-same-company-fund
																		v-if="fundInfo && fundInfo.fundCode"
																		:fund-code="fundInfo.fundCode"
																	/>
																</div>
															</td>
														</tr>
													</tbody>
												</table>
												<h4>{{ $t('pro.fundCompany') }}</h4>
												<table class="table table-bordered">
													<tbody>
														<tr>
															<th width="45%">
																{{ $t('pro.companyName') }}
															</th>
															<td width="55%">
																{{ $filters.defaultValue(getCompanyField(companies, 'PMGR', 'companyName'), '--') }}
															</td>
														</tr>
														<tr>
															<th>{{ $t('pro.companyShortName') }}</th>
															<td>
																{{
																	$filters.defaultValue(
																		getCompanyField(companies, 'PMGR', 'companyShortName'),
																		'--'
																	)
																}}
															</td>
														</tr>
														<tr>
															<th>{{ $t('pro.telephone') }}</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'PMGR', 'telephone'), '--') }}
															</td>
														</tr>
														<tr>
															<th>{{ $t('pro.fax') }}</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'PMGR', 'fax'), '--') }}
															</td>
														</tr>
														<tr>
															<th>Email</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'PMGR', 'email'), '--') }}
															</td>
														</tr>
														<tr>
															<th>{{ $t('pro.website') }}</th>
															<td>
																{{ $filters.defaultValue(getCompanyField(companies, 'PMGR', 'website'), '--') }}
															</td>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
										<div class="row">
											<vue-fund-risk-pct
												v-if="fundInfo"
												ref="riskPct"
												:fund-code="fundInfo.fundCode"
												:global-class-code="fundInfo.globalClassCode"
											/>
											<div class="col-sm-4 col-right">
												<table class="table table-bordered four-grid">
													<tbody>
														<tr>
															<td
																class="text-center text-complete"
																:class="{ 'bg-complete text-white': clsActive2X2('A') }"
															>
																{{ $t('pro.excellent') }}
																<p class="font-xs">
																	({{ $t('pro.quadrantTwoLabel') }})
																</p>
															</td>
															<td
																class="text-center text-success"
																:class="{ 'bg-success text-white': clsActive2X2('B') }"
															>
																{{ $t('pro.reasonable') }}
																<p class="font-xs">
																	({{ $t('pro.quadrantOneLabel') }})
																</p>
															</td>
														</tr>
														<tr>
															<td
																class="text-center text-success"
																:class="{ 'bg-success text-white': clsActive2X2('C') }"
															>
																{{ $t('pro.reasonable') }}
																<p class="font-xs">
																	({{ $t('pro.quadrantThreeLabel') }})
																</p>
															</td>
															<td
																class="text-center text-danger"
																:class="{ 'bg-danger text-white': clsActive2X2('D') }"
															>
																{{ $t('pro.poor') }}
																<p class="font-xs">
																	({{ $t('pro.quadrantFourLabel') }})
																</p>
															</td>
														</tr>
													</tbody>
												</table>
												<p class="m-t-5">
													{{ $t('pro.remarks') }}：
												</p>
												<ul class="note">
													<li>
														<p class="text-success tx-title">
															({{ $t('pro.quadrantOneLabel') }})
														</p>
														<p>
															{{ $t('pro.quadrantOneDescription') }}
														</p>
													</li>
													<li>
														<p class="text-complete tx-title">
															({{ $t('pro.quadrantTwoLabel') }})
														</p>
														<p>{{ $t('pro.quadrantTwoIntro') }}</p>
														<ul>
															<li>{{ $t('pro.lowerAnnualizedStandardDeviation') }}</li>
															<li>{{ $t('pro.higherAnnualizedReturnRate') }}</li>
														</ul>
														<p>{{ $t('pro.higherReturnPerUnitRisk') }}</p>
													</li>
													<li>
														<p class="text-success tx-title">
															({{ $t('pro.quadrantThreeLabel') }})
														</p>
														<p>
															{{ $t('pro.quadrantThreeDescription') }}
														</p>
													</li>
													<li>
														<p class="text-danger tx-title">
															({{ $t('pro.quadrantFourLabel') }})
														</p>
														<p>{{ $t('pro.quadrantTwoIntro') }}</p>
														<ul>
															<li>{{ $t('pro.higherAnnualizedStandardDeviation') }}</li>
															<li>{{ $t('pro.lowerAnnualizedReturnRate') }}</li>
														</ul>
														<p>{{ $t('pro.lowerReturnPerUnitRisk') }}</p>
													</li>
												</ul>
											</div>
										</div>
										<div v-show="fundInfo && fundInfo.divsPerYear > 0" class="row">
											<vue-fund-dividend-history
												v-if="fundInfo && fundInfo.fundCode"
												:fund-code="fundInfo.fundCode"
												:local-currency-name="fundInfo.localCurrencyName"
											/>
										</div>
										<hr>
										<div v-show="fundInfo" class="row">
											<vue-fund-fund-size-compare
												v-if="fundInfo"
												:tech-currency-code="cookieCurrency"
												:fund-info="fundInfo"
											/>
										</div>
										<div class="row">
											<vue-fund-perf-compare
												v-if="fundInfo"
												:tech-currency-code="cookieCurrency"
												:fund-info="fundInfo"
												:techs="techs"
											/>
										</div>
										<div class="row m-t-20">
											<vue-fund-fluctuation-rate
												:tech-currency-code="cookieCurrency"
												:fund-info="fundInfo"
											/>
										</div>
										<div class="row m-t-20">
											<vue-fund-holding v-if="fundInfo && fundInfo.fundCode" :fund-code="fundInfo.fundCode" />
										</div>
										<div class="row m-t-20">
											<div class="col-12">
												<h4>{{ $t('pro.otherTechnicalIndicators') }}</h4>
												<vue-fund-other-tech
													v-if="showColumnChart"
													:fund-info="fundInfo"
													:techs="techs"
													:bm-techs="bmTechs"
													:bm-name="bmName"
												/>
												<vue-fund-other-tech-acr
													v-if="showColumnChart"
													:fund-info="fundInfo"
													:twd-techs="techs"
													:twd-bm-techs="bmTechs"
													:bm-name="bmName"
												/>
											</div>
										</div>
										<div class="row m-t-20">
											<div class="col-12">
												<h4>{{ $t('pro.lipperRanking') }}</h4>
												<table class="table table-bordered m-t-15">
													<thead>
														<tr>
															<th />
															<th class="text-center">
																{{ $t('pro.totalReturn') }}
															</th>
															<th class="text-center">
																{{ $t('pro.capitalPreservation') }}
															</th>
															<th class="text-center">
																{{ $t('pro.stable') }}
															</th>
														</tr>
													</thead>
													<tbody>
														<tr>
															<td class="text-center">
																ALL
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="TOTRETOV"
																/>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CAPPRESOV"
																/>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CONSRETOV"
																/>
															</td>
														</tr>
														<tr>
															<td class="text-center">
																{{ $t('pro.threeYears') }}
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="TOTRET3YR"
																/>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CAPPRES3YR"
																/>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CONSRET3YR"
																/>
															</td>
														</tr>
														<tr>
															<td class="text-center">
																{{ $t('pro.fiveYears') }}
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="TOTRET5YR"
																/>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CAPPRES5YR"
																/>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CONSRET5YR"
																/>
															</td>
														</tr>
														<tr>
															<td class="text-center">
																{{ $t('pro.tenYears') }}
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="TOTRET10YR"
																/>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CAPPRES10YR"
																/>
															</td>
															<td class="text-center">
																<vue-fund-lipper-score
																	:lipper-scores="lipperScores"
																	score-code="CONSRET10YR"
																/>
															</td>
														</tr>
													</tbody>
												</table>
												<p class="text-center m-t-10">
													<!--                            <img th:src="@{/image/fund/lipper-rank.png}" width="250px"/>-->
												</p>
												<h4 class="text-color">
													{{ $t('pro.lipperExplanation') }}
												</h4>
												<p id="DD" class="muted">
													{{ $t('pro.lipperLeadersExplanation1') }}
												</p>
												<p class="muted">
													{{ $t('pro.lipperLeadersExplanation2') }}
												</p>
												<p class="muted">
													{{ $t('pro.lipperDisclaimer') }}
												</p>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-white" @click.prevent="close()">
					{{ $t('pro.closeWindow') }}
				</button>
			</div>
		</div>
	</div>
	<!-- Modal 1 End -->
</template>
<script>
import moment from 'moment';
import _ from 'lodash';

import vueFundLipperScore from './fundLipperScore.vue';
import vueFundSameCompanyFund from './fundSameCompanyFund.vue';
import vueFundSameGlobalClassFund from './fundSameGlobalClassFund.vue';
import vueFundPriceHistory from './fundPriceHistory.vue';
import vueFundRiskPct from './fundRiskPct.vue';
import vueFundFundCode from './fundFundCode.vue';
import vueFundDividendHistory from './fundDividendHistory.vue';
import vueFundFundSizeCompare from './fundFundSizeCompare.vue';
import vueFundPerfCompare from './fundPerfCompare.vue';
import vueFundFluctuationRate from './fluctuationRate.vue';
import vueFundTech from './fundTech.vue';
import vueFundOtherTech from './fundOtherTech.vue';
import vueFundOtherTechAcr from './fundOtherTechAcr.vue';
import vueFundHolding from './fundHolding.vue';

export default {
	components: {
		vueFundLipperScore,
		vueFundSameCompanyFund,
		vueFundSameGlobalClassFund,
		vueFundPriceHistory,
		vueFundRiskPct,
		vueFundFundCode,
		vueFundDividendHistory,
		vueFundFundSizeCompare,
		vueFundPerfCompare,
		vueFundFluctuationRate,
		vueFundTech,
		vueFundOtherTech,
		vueFundOtherTechAcr,
		vueFundHolding
	},
	props: {
		downloadFile: Function,
		downloadOtherFile: Function,
		finReqCodeMenu: Array,
		close: Function
	},
	data: function () {
		const now = moment();
		return {
			activeTab: 'section1',
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				bondInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},
			finReqCodes: [],
			proFileB: {},
			proFileD: {},
			proFileE: {},
			proFileF: {},
			proFileG: {},
			otherFileList: [],
			// vue2 fund
			fundInfo: null,
			companies: [],
			lipperScores: [],
			fundSizes: [],
			techs: null,
			bmTechs: null,
			techsOther: [],
			bmName: null,
			preMonthEndPrice: null,
			cookieCurrency: 'TWD', // TODO test data
			fundColumnChartData: []
		};
	},
	computed: {
		// vue2 fund
		tnaValueRate: function () {
			if (!this.fundSizes || this.fundSizes.length !== 2) return;
			const fundSizes = _.orderBy(this.fundSizes, ['tnaDate'], ['desc']);
			return ((fundSizes[0].tnaValue - fundSizes[1].tnaValue) / fundSizes[0].tnaValue) * 100;
		},
		showColumnChart: function () {
			if (this.techs && this.bmTechs && this.bmName) {
				return true;
			}
			else {
				return false;
			}
		},
		formattedValue() {
			const value = this.$filters.formatFlucWithView(this.getTech(this.techs, 'PCT1Y'));
			return this.$filters.defaultValue(value, '--');
		}
	},
	watch: {},
	mounted: function () {
		const self = this;
		self.activeTab = 'section1';
		self.fundInfo = {};
	},
	methods: {
		// Tab switching
		changeTab: function (tabName) {
			const self = this;
			self.activeTab = tabName;
		},
		getProInfo: async function (proCode, pfcatCode) {
			const self = this;
			const res = await this.$api.getProductInfoApi({
				proCode: proCode,
				pfcatCode: pfcatCode
			});
			if (_.isNil(res.data)) {
				res.data = {};
				this.$bi.alert(this.$t('pro.dataNotExist'));
				return;
			}
			if (_.isNil(res.data.fundInfo)) {
				res.data.fundInfo = {};
			}
			// Object.assign(self.proInfo, res.data);

			self.proInfo = res.data;

			let selectYnList = [];

			this.$api
				.getAdmCodeDetail({
					codeType: 'SELECT_YN'
				})
				.then(function (ret) {
					selectYnList = ret.data;

					if (!_.isEmpty(selectYnList)) {
						if (!_.isUndefined(self.proInfo.fundInfo.localYn)) {
							const localYnObjs = _.filter(selectYnList, {
								codeValue: self.proInfo.fundInfo.localYn
							});
							self.proInfo.fundInfo.localYn = localYnObjs[0].codeName;
						}

						if (!_.isUndefined(self.proInfo.fundInfo.hundredYn)) {
							const hundredYnObjs = _.filter(selectYnList, {
								codeValue: self.proInfo.fundInfo.hundredYn
							});
							self.proInfo.fundInfo.hundredYn = hundredYnObjs[0].codeName;
						}

						if (!_.isUndefined(self.proInfo.fundInfo.backEndLoadYn)) {
							const backEndLoadYnObjs = _.filter(selectYnList, {
								codeValue: self.proInfo.fundInfo.backEndLoadYn
							});
							self.proInfo.fundInfo.backEndLoadYn = backEndLoadYnObjs[0].codeName;
						}

						if (!_.isUndefined(self.proInfo.buyYn)) {
							const buyYnObjs = _.filter(selectYnList, {
								codeValue: self.proInfo.buyYn
							});
							self.proInfo.buyYn = buyYnObjs[0].codeName;
						}

						if (!_.isUndefined(self.proInfo.sellYn)) {
							const sellYnObjs = _.filter(selectYnList, {
								codeValue: self.proInfo.sellYn
							});
							self.proInfo.sellYn = sellYnObjs[0].codeName;
						}

						if (!_.isUndefined(self.proInfo.profInvestorYn)) {
							const profInvestorYnObjs = _.filter(selectYnList, {
								codeValue: self.proInfo.profInvestorYn
							});
							self.proInfo.profInvestorYn = profInvestorYnObjs[0].codeName;
						}
					}
				});

			if (!_.isUndefined(self.proInfo.fundInfo.status)) {
				let fundStatusList = [];
				this.$api
					.getAdmCodeDetail({
						codeType: 'FUND_STATUS'
					})
					.then(function (ret) {
						fundStatusList = ret.data;
						const statusObjs = _.filter(fundStatusList, {
							codeValue: self.proInfo.fundInfo.status
						});
						self.proInfo.fundInfo.status = statusObjs[0] && statusObjs[0].codeName;
					});
			}

			if (!_.isUndefined(self.proInfo.targetCusBu)) {
				let targetCusBuList = [];
				this.$api
					.getAdmCodeDetail({
						codeType: 'CUS_BU'
					})
					.then(function (ret) {
						targetCusBuList = ret.data;
						const targetCusBuObjs = _.filter(targetCusBuList, {
							codeValue: self.proInfo.targetCusBu
						});
						self.proInfo.targetCusBu = targetCusBuObjs[0].codeName;
					});
			}

			if (!_.isUndefined(self.proInfo.volatilityType)) {
				let volatilityTypeList = [];
				this.$api
					.getAdmCodeDetail({
						codeType: 'VOLATILITY_TYPE'
					})
					.then(function (ret) {
						volatilityTypeList = ret.data;
						const volatilityTypeObjs = _.filter(volatilityTypeList, {
							codeValue: self.proInfo.volatilityType
						});
						self.proInfo.volatilityType = volatilityTypeObjs[0].codeName;
					});
			}

			if (!_.isUndefined(self.proInfo.intFreqUnitype)) {
				let intFreqUnitypeList = [];
				this.$api
					.getAdmCodeDetail({
						codeType: 'INT_FREQ_UNITTYPE'
					})
					.then(function (ret) {
						intFreqUnitypeList = ret.data;
						const intFreqUnitypeObjs = _.filter(intFreqUnitypeList, {
							codeValue: self.proInfo.intFreqUnitype
						});
						self.proInfo.intFreqUnitype = intFreqUnitypeObjs[0].codeName;
					});
			}

			if (!_.isUndefined(self.proInfo.principalGuarYn)) {
				let principalGuarYnList = [];
				this.$api
					.getAdmCodeDetail({
						codeType: 'GUAR_YN'
					})
					.then(function (ret) {
						principalGuarYnList = ret.data;
						const principalGuarYnObjs = _.filter(principalGuarYnList, {
							codeValue: self.proInfo.principalGuarYn
						});
						self.proInfo.principalGuarYn = principalGuarYnObjs[0].codeName;
					});
			}

			if (!_.isUndefined(self.proInfo.selprocatNames)) {
				const selprocatNames = self.proInfo.selprocatNames.replaceAll(',', '、');
				self.proInfo.selprocatNames = selprocatNames;
			}

			if (!_.isUndefined(self.proInfo.finReqCode)) {
				self.finReqCodes = self.proInfo.finReqCode.split(',');
			}

			// Product additional data
			this.$api
				.getProductsCommInfo({
					proCode: proCode,
					pfcatCode: pfcatCode
				})
				.then(function (ret) {
					if (!_.isNil(ret.data)) {
						if (ret.data.proDocs) {
							self.otherFileList = ret.data.proDocs; // Other related attachments
							self.otherFileList.forEach(function (item) {
								// Other related attachments file display time range
								item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
							});
						}

						const proFileList = ret.data.proFiles;
						if (!_.isNil(proFileList)) {
							self.proFileB = proFileList.filter(proFile => proFile.fileType === 'B')[0];
							self.proFileD = proFileList.filter(proFile => proFile.fileType === 'D')[0];
							self.proFileE = proFileList.filter(proFile => proFile.fileType === 'E')[0];
							self.proFileF = proFileList.filter(proFile => proFile.fileType === 'F')[0];
							self.proFileG = proFileList.filter(proFile => proFile.fileType === 'G')[0];
						}
					}
				});

			// vue2 fund
			if (self.proInfo.fundInfo.lipperId) {
				await self.getFundInfo(self.proInfo.fundInfo.lipperId);
				await self.getTechs(self.proInfo.fundInfo.lipperId);
				await self.getCompanies(self.proInfo.fundInfo.lipperId);
				await self.getLipperScore(self.proInfo.fundInfo.lipperId);
				await self.getFundInfoFundSizeLatest2(self.proInfo.fundInfo.lipperId);
				await self.getPreMonthEndPrice(self.proInfo.fundInfo.lipperId);
			}
			this.$forceUpdate();
		},
		// vue2 fund
		getFundInfo: async function (proCode) {
			const self = this;
			const res = await this.$api.getFundInfo({
				proCode: proCode
			});
			self.fundInfo = res.data;
			self.getBmTechs();
		},
		getCompanies: function (proCode) {
			const self = this;
			return this.$api
				.getFundCompany({
					proCode: proCode
				})
				.then(function (ret) {
					self.companies = ret.data;
				});
		},
		getLipperScore: function (proCode) {
			const self = this;
			return this.$api
				.getLipperScoreApi({
					proCode: proCode
				})
				.then(function (ret) {
					self.lipperScores = ret.data;
				});
		},
		getFundInfoFundSizeLatest2: function (proCode) {
			const self = this;
			return this.$api
				.getFundInfoFundSizeLatest2Api({
					proCode: proCode
				})
				.then(function (ret) {
					self.fundSizes = ret.data;
				});
		},
		getTechs: async function (proCode) {
			const self = this;
			const res = await this.$api.getTechsApi({
				proCode: proCode,
				techCurrencyCode: self.cookieCurrency
			});
			if (!_.isNil(res.data)) {
				self.techs = res.data;
			}
		},
		getBmTechs: async function () {
			const self = this;
			const managerBmCode = self.fundInfo.managerBmCode;
			const analysisBmCode = self.fundInfo.analysisBmCode;
			let bmCode;

			if (managerBmCode && managerBmCode != '11000006' && managerBmCode != '11000000') {
				self.bmName = self.fundInfo.managerBmName;
				bmCode = managerBmCode;
			}
			else if (analysisBmCode) {
				self.bmName = self.fundInfo.analysisBmName;
				bmCode = analysisBmCode;
			}

			const res = await this.$api.getTechsApi({
				proCode: bmCode,
				techCurrencyCode: self.cookieCurrency
			});
			self.bmTechs = res.data;
		},
		getPreMonthEndPrice: async function (proCode) {
			const self = this;
			const ret = this.$api.getPreMonthEndPriceApi({
				proCode: proCode,
				beginDate: moment().subtract(1, 'months').startOf('month').format('YYYY/MM/DD'),
				endDate: moment().subtract(1, 'months').endOf('month').format('YYYY/MM/DD')
			});
			const preMonthEndPrice = _.orderBy(
				_.filter(ret.data, function (item) {
					return item.priceLc > 0;
				}),
				['dataDate'],
				['desc']
			);
			self.preMonthEndPrice = preMonthEndPrice && preMonthEndPrice[0] ? preMonthEndPrice[0].priceLc : null;
		},
		clsActive2X2: function (median) {
			const self = this;
			if (self.$refs.riskPct) {
				const pointFund = self.$refs.riskPct.pointFund;
				if (pointFund && pointFund.median === median && self.$refs.riskPct.fund2x2Perfs.length > 1) {
					return true;
				}
			}
			return false;
		},
		getTech: function (techs, statCode) {
			const tech = _.find(techs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		},
		getBmTech: function (statCode) {
			const tech = _.find(this.techsOther, { statCode: statCode });
			return tech ? tech.dvalue : null;
		},
		getCompanyField: function (value, companyRoleCode, field) {
			const company = _.find(value, { companyRoleCode: companyRoleCode });
			if (company && company[field]) {
				return company[field];
			}
			else {
				return '--';
			}
		},
		getRealAssetCode: function (value) {
			if (value && value !== '11000006' && value !== '11000000') {
				return value;
			}
			else {
				return null;
			}
		}
	} // methods end
};
</script>
