<template>
	<div class="modal-dialog modal-xl">
		<!--  海外股票-->
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('pro.trustOverseasStock') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click.prevent="close()"
				/>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-structure" />
							<h4>
								<span>{{ $t('pro.productName') }}</span> <br>{{ proInfo.proName }} <br><span class="tx-black">{{ proInfo.proEName }}</span>
							</h4>
						</div>
						<h4 class="pro_value">
							<span>{{ $t('pro.latestNav') }}</span>
							<br><span>{{ proInfo.aprice }}</span> <br><span>{{ proInfo.priceDt }}</span>
						</h4>
						<h4 class="pro_value">
							<span>{{ $t('pro.latestMarketPrice') }}</span>
							<br><span>{{ proInfo.sprice }}</span> <br><span>{{ proInfo.priceDt }}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productCode') }}</span> <br>{{ proInfo.bankProCode }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6>
								<span>{{ $t('pro.assetCategory') }} <br></span>{{ proInfo.assetcatName }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productMainCategory') }}</span> <br>{{ proInfo.pfcatName }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productSubCategory') }}</span><br>{{ proInfo.proTypeName }}</h6>
						</div>
					</div>
				</div>
				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item">
							<a class="nav-link active" href="#Sectionpfd1" data-bs-toggle="pill">{{ $t('pro.productBasicData') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#Sectionpfd2" data-bs-toggle="pill">{{ $t('pro.productCommonData') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#Sectionpfd3" data-bs-toggle="pill">{{ $t('pro.productAdditionalData') }}</a>
						</li>
					</ul>

					<div class="tab-content">
						<div id="Sectionpfd1" class="tab-pane fade show active">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.productInfo') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.riskLevel') }}</th>
											<td class="wd-30p">
												{{ proInfo.pfdInfo.riskName }}
											</td>
											<th>{{ $t('pro.currency') }}</th>
											<td class="wd-30p">
												{{ proInfo.pfdInfo.curCode }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.sellable') }}</th>
											<td>{{ proInfo.pfdInfo.buyYn }}</td>
											<th>{{ $t('pro.tradingUnit') }}</th>
											<td>{{ proInfo.pfdInfo.txUnit }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionpfd2" class="tab-pane fade">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.salesRelatedData') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.salesRegion') }}</th>
											<td v-if="proInfo.allYn == 'Y'">
												{{ $t('pro.allBranches') }}
											</td>
											<td v-else />
											<th>{{ $t('pro.piOnlyPurchase') }}</th>
											<td>{{ proInfo.profInvestorYn }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.openForSubscription') }}</th>
											<td>{{ proInfo.buyYn }}</td>
											<th>{{ $t('pro.openForRedemption') }}</th>
											<td>{{ proInfo.sellYn }}</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.capitalProtectionRequirement') }}</span></th>
											<td>
												<span v-if="actionType !== 'EDIT'">{{ proInfo.principalGuarYn }}</span>
												<span v-else>
													<div v-for="item in principalGuarMenu" class="form-check form-check-inline">
														<input
															:id="'principalGuar' + item.codeValue"
															v-model="proInfo.principalGuarYn"
															class="form-check-input"
															type="radio"
															:value="item.codeValue"
															name="fastCode"
														>
														<label class="form-check-label" :for="'principalGuar' + item.codeValue">{{
															$filters.defaultValue(item.codeName, '--')
														}}</label>
													</div>
												</span>
											</td>
											<th>{{ $t('pro.dividendRate') }}</th>
											<td>
												<template v-if="actionType !== 'EDIT' && proInfo.intRate">
													{{ proInfo.intRate * 100 }}%<span v-if="proInfo.intRate * 100 > 5">({{ $t('pro.highDividendRate') }})</span><span v-else>({{ $t('pro.normalDividendRate') }})</span>
												</template>
												<template v-if="actionType === 'EDIT'">
													<input
														id="intRate"
														v-model="intRateEdit"
														class="form-control"
														maxlength="5"
														name="intRate"
														type="text"
													>%
												</template>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.financialNeeds') }}</span></th>
											<td colspan="3">
												<div v-for="(item, index) in finReqCodeMenu" class="form-check form-check-inline">
													<input
														:id="'finReqCodes_' + index"
														v-model="finReqCodes"
														class="form-check-input"
														name="finReqCodes"
														:disabled="actionType == 'EDIT' ? false : true"
														:value="item.codeValue"
														type="checkbox"
													>
													<label class="form-check-label" :for="'finReqCodes_' + index">{{ item.codeName }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.productTags') }}</th>
											<td colspan="3" disabled="disabled">
												{{ proInfo.selprocatNames }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionpfd3" class="tab-pane fade">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.otherSettings') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>{{ $t('pro.remarks') }}</span></th>
											<td>
												<textarea
													v-model="proInfo.memo"
													class="form-control"
													cols="80"
													rows="4"
													size="200"
													maxlength="200"
													:readonly="actionType !== 'EDIT'"
												/>
												<div v-if="proInfo.memo" class="tx-note">
													{{ 200 - proInfo.memo.length }} {{ $t('pro.charactersCanEnter') }}
												</div>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.relatedAttachments') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.productProspectus') }}</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">{{ $t('pro.linkAddress') }}： </span>
													<input
														v-model="url['A']"
														type="text"
														name="url_A"
														class="form-control"
														size="40"
														maxlength="100"
													>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																id="pfdUploadFileA"
																class="form-control form-file"
																type="file"
																size="30"
																accept=".xlsx,.xls"
																@change="triggerFile($event, 'A')"
															>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('A', url['A'])">
																{{ $t('pro.upload') }}
															</button>
														</div>
													</div>
													<br>
												</template>
												<span v-if="uploadFiles['A'] && uploadFiles['A'].url && actionType !== 'EDIT'">{{ $t('pro.linkAddress') }}： <a :href="uploadFiles['A'].url" target="_blank">{{ uploadFiles['A'].url }}</a><br v-if="uploadFiles['A']">
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['A'] && uploadFiles['A'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['A'])"
													>{{ uploadFiles['A'].showName }}</span>
													<span
														v-show="uploadFiles['A'] && uploadFiles['A'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														:title="$t('pro.delete')"
														@click="deleteFiles('A', uploadFiles['A'].proFileId)"
													/>
												</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.investorNotice') }}</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">{{ $t('pro.linkAddress') }}： </span>
													<input
														v-model="url['D']"
														type="text"
														name="url_D"
														class="form-control"
														size="40"
														maxlength="100"
													>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																id="pfdUploadFileD"
																class="form-control form-file"
																type="file"
																size="30"
																accept=".xlsx,.xls"
																@change="triggerFile($event, 'D')"
															>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('D', url['D'])">
																{{ $t('pro.upload') }}
															</button>
														</div>
													</div>
													<br>
												</template>
												<span v-if="uploadFiles['D'] && uploadFiles['D'].url && actionType !== 'EDIT'">{{ $t('pro.linkAddress') }}： <a :href="uploadFiles['D'].url" target="_blank">{{ uploadFiles['D'].url }}</a><br v-if="uploadFiles['D']">
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['D'] && uploadFiles['D'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['D'])"
													>{{ uploadFiles['D'].showName }}</span>
													<span
														v-show="uploadFiles['D'] && uploadFiles['D'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														:title="$t('pro.delete')"
														@click="deleteFiles('D', uploadFiles['D'].proFileId)"
													/>
												</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">{{ $t('pro.linkAddress') }}： </span>
													<input
														v-model="url['F']"
														type="text"
														name="url_F"
														class="form-control"
														size="40"
														maxlength="100"
													>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																id="pfdUploadFileF"
																class="form-control form-file"
																type="file"
																size="30"
																accept=".xlsx,.xls"
																@change="triggerFile($event, 'F')"
															>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('F', url['F'])">
																{{ $t('pro.upload') }}
															</button>
														</div>
													</div>
													<br>
												</template>
												<span v-if="uploadFiles['F'] && uploadFiles['F'].url && actionType !== 'EDIT'">{{ $t('pro.linkAddress') }}： <a :href="uploadFiles['F'].url" target="_blank">{{ uploadFiles['F'].url }}</a><br v-if="uploadFiles['F']">
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['F'] && uploadFiles['F'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['F'])"
													>{{ uploadFiles['F'].showName }}</span>
													<span
														v-show="uploadFiles['F'] && uploadFiles['F'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														:title="$t('pro.delete')"
														@click="deleteFiles('F', uploadFiles['F'].proFileId)"
													/>
												</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.other') }}</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">{{ $t('pro.linkAddress') }}： </span>
													<input
														v-model="url['G']"
														type="text"
														name="url_G"
														class="form-control"
														size="40"
														maxlength="100"
													>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																id="pfdUploadFileG"
																class="form-control form-file"
																type="file"
																size="30"
																accept=".xlsx,.xls"
																@change="triggerFile($event, 'G')"
															>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('G', url['G'])">
																{{ $t('pro.upload') }}
															</button>
														</div>
													</div>
													<br>
												</template>
												<span v-if="uploadFiles['G'] && uploadFiles['G'].url && actionType !== 'EDIT'">{{ $t('pro.linkAddress') }}： <a :href="uploadFiles['G'].url" target="_blank">{{ uploadFiles['G'].url }}</a><br v-if="uploadFiles['G']">
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['G'] && uploadFiles['G'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['G'])"
													>{{ uploadFiles['G'].showName }}</span>
													<span
														v-show="uploadFiles['G'] && uploadFiles['G'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														:title="$t('pro.delete')"
														@click="deleteFiles('G', uploadFiles['G'].proFileId)"
													/>
												</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.otherRelatedAttachments') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tr>
										<td>
											<span v-for="(item, index) in otherFileList">
												<a
													v-if="index === otherFileList.length - 1"
													v-show="item.show"
													href="#"
													class="tx-link"
													@click="downloadOtherFile(item.docFileId)"
												>{{ $filters.defaultValue(item.showName, '--') }}</a>
												<a
													v-else
													v-show="item.show"
													href="#"
													class="tx-link"
													@click="downloadOtherFile(item.docFileId)"
												>{{ $filters.defaultValue(item.showName, '--') }}、</a>
											</span>
										</td>
									</tr>
								</table>
							</div>
							<div class="tx-note">
								{{ $t('pro.otherRelatedAttachmentsNote') }}
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<input
					id="modalCloseButton"
					type="button"
					class="btn btn-white"
					:value="$t('pro.close')"
					@click.prevent="close()"
				>
				<input
					v-if="actionType == 'EDIT' && config.btnEdit"
					type="button"
					class="btn btn-primary"
					:value="$t('pro.sendForSupervisorReview')"
					@click="
						updateProduct();
						close();
					"
				>
			</div>
		</div>
	</div>
	<!-- Modal 2 End -->
</template>
<script>
import moment from 'moment';
import _ from 'lodash';
export default {
	props: {
		finReqCodeMenu: Array,
		close: Function
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				pfdInfo: {},
				pfdInfo: {},
				bondInfo: {},
				pfdInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},
			finReqCodes: [],
			uploadFiles: [],
			actionType: '',
			otherFileList: []
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		getProInfo: async function (bankProCode, pfcatCode) {
			const self = this;
			const ret = await this.$api.getProductInfoApi({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});
			if (_.isEmpty(ret.data)) {
				ret.data = {};
				this.$bi.alert(this.$t('pro.dataNotExist'));
				return;
			}
			if (_.isEmpty(ret.data.pfdInfo)) {
				ret.data.pfdInfo = {};
			}

			self.proInfo = ret.data;
			this.$forceUpdate();

			let selectYnList = [];
			const ret2 = await this.$api.getAdmCodeDetail({
				codeType: 'SELECT_YN'
			});
			selectYnList = ret2.data;
			if (!_.isUndefined(self.proInfo.pfdInfo.buyYn)) {
				var buyYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.pfdInfo.buyYn
				});
				self.proInfo.pfdInfo.buyYn = buyYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.buyYn)) {
				var buyYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.buyYn
				});
				self.proInfo.buyYn = buyYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.sellYn)) {
				const sellYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.sellYn
				});
				self.proInfo.sellYn = sellYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.profInvestorYn)) {
				const profInvestorYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.profInvestorYn
				});
				self.proInfo.profInvestorYn = profInvestorYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.principalGuarYn)) {
				let principalGuarYnList = [];
				const ret = await this.$api.getAdmCodeDetail({
					codeType: 'GUAR_YN'
				});
				principalGuarYnList = ret.data;
				const principalGuarYnObjs = _.filter(principalGuarYnList, {
					codeValue: self.proInfo.principalGuarYn
				});
				self.proInfo.principalGuarYn = principalGuarYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.finReqCode)) {
				self.finReqCodes = self.proInfo.finReqCode.split(',');
			}

			if (!_.isUndefined(self.proInfo.selprocatNames)) {
				const selprocatNames = self.proInfo.selprocatNames.replaceAll(',', '、');
				self.proInfo.selprocatNames = selprocatNames;
			}

			this.$forceUpdate();
		}
	} // methods end
};
</script>
