<template>
	<!-- Modal group Target start -->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('pro.select') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click="close()"
				/>
			</div>
			<div class="modal-body">
				<div class="card card-form-collapse">
					<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
						<h4>{{ $t('pro.select') }}</h4>
					</div>

					<div id="formsearch1" class="card-body collapse show">
						<form>
							<div class="form-row">
								<div class="form-group col-lg-4">
									<label class="form-label"> {{ $t('pro.stock') }}</label><br>
									<input
										v-model="stockCode"
										class="form-control"
										type="text"
										size="15"
										maxlength="20"
									>
								</div>
							</div>

							<div class="form-row">
								<div class="col-lg-12 text-end">
									<a href="#" class="btn btn-primary btn-search" @click="groupTargetsMenu()">{{ $t('pro.search') }}</a>
								</div>
							</div>
						</form>
					</div>
				</div>
				<p class="tx-note mb-3">
					{{ $t('pro.stock') }}
				</p>
				<div v-if="targetsMenu.length > 0">
					<div class="card card-table">
						<div class="card-header">
							<h4>{{ $t('pro.search') }}</h4>
						</div>
						<div class="table-responsive">
							<table class="table table-RWD table-bordered">
								<thead>
									<tr>
										<th width="10%" class="text-center">
											{{ $t('pro.select') }}
										</th>
										<th width="10%">
											{{ $t('pro.stock') }}
										</th>
										<th width="20%">
											{{ $t('pro.isinCode') }}
										</th>
										<th width="10%">
											{{ $t('pro.currency') }}
										</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in targetsMenu">
										<td class="text-center">
											<input
												:id="item.stockCode"
												v-model="targetCode"
												type="checkbox"
												class="form-check-input"
												:value="item.stockCode"
											>
										</td>
										<td>{{ item.stockCode }}</td>
										<td>{{ item.isinCode }}</td>
										<td>{{ item.currencyCode }}</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<input
					id="modaladdButton"
					type="button"
					class="btn btn-primary"
					:value="$t('pro.add')"
					@click="addTarget()"
				>
			</div>
		</div>
	</div>
	<!-- Modal group Target End -->
</template>
<script>
export default {
	props: {
		targetProp: Array,
		close: Function
	},
	data: function () {
		return {
			stockCode: null,
			targetsMenu: [],
			targetCode: [],
			targetItem: []
		};
	},
	watch: {},
	mounted: function () {
		const self = this;
		self.targetItem = self.targetProp || [];
		self.targetCode = self.targetItem.map(item => item.stockCode);
	},
	methods: {
		groupTargetsMenu: async function () {
			const self = this;
			const ret = await this.$api.groupTargetMenuApi({
				stockCode: self.stockCode
			});

			self.targetsMenu = ret.data;
		},
		addTarget() {
			const self = this;
			if (self.targetCode.length === 0) {
				self.$bi.alert(self.$t('pro.pleaseSelectAtLeastOne'));
				return;
			}
			self.$emit('selected', self.targetItem);
			self.close();
		},
		targetPropItem(v) {
			const self = this;
			if (v.length > 0) {
				self.targetItem = v;
				self.targetCode = Array.from(new Set([...self.targetCode, ...v.map(item => item.stockCode)]));
			}
			else {
				self.targetItem = [];
				self.targetCode = [];
			}
		}
	}
	// methods end
};
</script>
