<template>
	<div :id="chartId" />
</template>
<script>
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { toRaw } from 'vue';
export default {
	props: {
		fundName: String,
		propChartData: Array,
		lineArray: Array,
		chartId: String
	},
	data: function () {
		return {
			am5Obj: {}
		};
	},
	computed: {},
	watch: {
		propChartData: {
			handler: function (newVal, oldVal) {
				this.initChart();
			},
			deep: true
		}
	},
	mounted: function () {},
	beforeUnmount: function () {
		this.destroyChart();
	},
	methods: {
		initChart: function () {
			const self = this;
			let valueLength = null;
			if (self.propChartData && self.propChartData[0]) {
				valueLength = Object.keys(self.propChartData[0]).length;
			}

			// let self = this;
			const { am5Obj } = self;
			let firstLoad = false;
			// 透過 toRaw 解決因為 vue data proxy 導致跟 amcharts 衝突
			let { xAxis, columnYAxis, lineYAxis, root, chart, scrollbar, sbxAxis, sbyAxis, sbseries } = toRaw(am5Obj);

			if (!root) {
				firstLoad = true;

				// Create root element
				// https://www.amcharts.com/docs/v5/getting-started/#Root_element
				root = am5.Root.new(self.chartId);
				root._logo.dispose();

				// Set themes
				// https://www.amcharts.com/docs/v5/concepts/themes/
				root.setThemes([am5themes_Animated.new(root)]);

				// Create chart
				// https://www.amcharts.com/docs/v5/charts/xy-chart/
				chart = root.container.children.push(
					am5xy.XYChart.new(root, {
						panX: true,
						panY: false,
						wheelX: 'none',
						wheelY: 'true',
						arrangeTooltips: false,
						pinchZoomX: true
					})
				);

				chart.get('colors').set('colors', [am5.color(0xfca631), am5.color(0xef4141), am5.color(0x2986cc)]);

				// make y axes stack
				chart.leftAxesContainer.set('layout', root.verticalLayout);

				// Create axes
				// https://www.amcharts.com/docs/v5/charts/xy-chart/axes/

				xAxis = chart.xAxes.push(
					am5xy.GaplessDateAxis.new(root, {
						maxDeviation: 0,
						baseInterval: {
							timeUnit: 'day',
							count: 1
						},
						renderer: am5xy.AxisRendererX.new(root, {
							minorGridEnabled: true
						}),
						tooltip: am5.Tooltip.new(root, {})
					})
				);

				// line
				lineYAxis = chart.yAxes.push(
					am5xy.ValueAxis.new(root, {
						renderer: am5xy.AxisRendererY.new(root, {}),
						tooltip: am5.Tooltip.new(root, {
							animationDuration: 0
						}),
						x: am5.p100,
						centerX: am5.p100,
						marginTop: 0 // this makes gap between axes
					})
				);

				lineYAxis.children.moveValue(
					am5.Label.new(root, {
						rotation: -90,
						text: this.$t('pro.cumulativeReturn'),
						y: am5.p50,
						centerX: am5.p50
					}),
					0
				);

				// column
				columnYAxis = chart.yAxes.push(
					am5xy.ValueAxis.new(root, {
						renderer: am5xy.AxisRendererY.new(root, {}),
						tooltip: am5.Tooltip.new(root, {
							animationDuration: 0
						}),
						x: am5.p100,
						centerX: am5.p100,
						marginTop: 40 // this makes gap between axes
					})
				);

				columnYAxis.children.moveValue(
					am5.Label.new(root, {
						rotation: -90,
						text: this.$t('pro.fundSize'),
						y: am5.p50,
						centerX: am5.p50
					}),
					0
				);
			}
			else {
				chart.series.clear();
				// chart.yAxes.clear();
			}
			xAxis.get('dateFormats')['day'] = 'yyyy-MM-dd';
			xAxis.get('periodChangeDateFormats')['day'] = 'MMM';
			xAxis.data.setAll(self.propChartData);

			// Add series
			// https://www.amcharts.com/docs/v5/charts/xy-chart/series/
			function createSeries(field, fundName, column) {
				let series;
				if (column) {
					series = chart.series.push(
						am5xy.ColumnSeries.new(root, {
							xAxis: xAxis,
							yAxis: columnYAxis,
							valueYField: field,
							valueXField: 'date',
							categoryXField: 'category',
							sequencedInterpolation: true,
							fill: am5.color(0x2894ff),
							tooltip: am5.Tooltip.new(root, {
								pointerOrientation: 'vertical',
								labelText: this.$t('pro.fundName') + ': ' + self.fundName + '\n' + this.$t('pro.fundSize') + ': {valueY}' + this.$t('pro.millions')
							})
						})
					);
				}
				else {
					series = chart.series.push(
						am5xy.LineSeries.new(root, {
							xAxis: xAxis,
							yAxis: lineYAxis,
							valueXField: 'date',
							valueYField: field,
							categoryXField: 'category',
							sequencedInterpolation: true,
							tooltip: am5.Tooltip.new(root, {
								pointerOrientation: 'horizontal',
								labelText: fundName + ': \n[bold]{valueY}%[/]'
							})
						})
					);
				}

				if (!column) {
					series.bullets.push(function () {
						return am5.Bullet.new(root, {
							locationY: 1,
							locationX: 0.5,
							sprite: am5.Circle.new(root, {
								radius: 4,
								fill: series.get('fill')
							})
						});
					});
				}

				series.data.setAll(self.propChartData);
				if (firstLoad) {
					series.appear(1000, 100);
				}
				return series;
			}

			if (firstLoad) {
				// Add scrollbar
				// https://www.amcharts.com/docs/v5/charts/xy-chart/scrollbars/
				scrollbar = am5xy.XYChartScrollbar.new(root, {
					orientation: 'horizontal',
					height: 50
				});
				chart.set('scrollbarX', scrollbar);

				sbxAxis = scrollbar.chart.xAxes.push(
					am5xy.DateAxis.new(root, {
						groupData: true,
						groupIntervals: [{ timeUnit: 'month', count: 1 }],
						baseInterval: { timeUnit: 'day', count: 5 },
						renderer: am5xy.AxisRendererX.new(root, {
							minorGridEnabled: true,
							opposite: false,
							strokeOpacity: 0
						})
					})
				);

				sbyAxis = scrollbar.chart.yAxes.push(
					am5xy.ValueAxis.new(root, {
						renderer: am5xy.AxisRendererY.new(root, {})
					})
				);

				sbseries = scrollbar.chart.series.push(
					am5xy.LineSeries.new(root, {
						xAxis: sbxAxis,
						yAxis: sbyAxis,
						valueYField: 'value0',
						valueXField: 'date'
					})
				);
			}

			createSeries('volume', null, true);
			self.lineArray.forEach(function (item) {
				createSeries(item.valueName, item.fundName, false);
			});

			sbseries.data.setAll(self.propChartData);

			// Add cursor
			// https://www.amcharts.com/docs/v5/charts/xy-chart/cursor/
			const cursor = chart.set(
				'cursor',
				am5xy.XYCursor.new(root, {
					xAxis: xAxis
				})
			);

			// show x Axis label next to the panel on which cursor currently is
			// willl move above other elements
			xAxis.set('layer', 50);

			if (firstLoad) {
				Object.assign(am5Obj, {
					xAxis,
					columnYAxis,
					lineYAxis,
					root,
					chart,
					scrollbar,
					sbxAxis,
					sbyAxis,
					sbseries
				});
				// Make stuff animate on load
				// https://www.amcharts.com/docs/v5/concepts/animations/
				chart.appear(1000, 100);
			}
		},
		destroyChart: function () {
			const { am5Obj } = this;
			const { root } = Vue.toRaw(am5Obj);
			if (root) {
				root.dispose();
			}
		}
	}
};
</script>
