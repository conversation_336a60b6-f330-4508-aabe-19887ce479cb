<template>
	<!-- Modal 3 Structured Products-->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('pro.structuredProduct') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click.prevent="close()"
				/>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-oversea" />
							<h4>
								<span>{{ $t('pro.productName') }}</span>
								<br>{{ $filters.defaultValue(proInfo.proName, '--') }} <br><span class="tx-black">{{
									$filters.defaultValue(proInfo.proEName, '--')
								}}</span>
							</h4>
						</div>
						<h4 class="pro_value">
							<span>{{ $t('pro.navDateAndReferenceNav') }}</span>
							<br>{{ $filters.formatNumber(proInfo.aprice, '0,0.00' || '--') }} <br><span>{{
								$filters.formatDate(proInfo.priceDt, '--')
							}}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productCode') }}</span> <br>{{ $filters.defaultValue(proInfo.bankProCode, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.assetCategory') }}</span> <br>{{ $filters.defaultValue(proInfo.assetcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productMainCategory') }}</span> <br>{{ $filters.defaultValue(proInfo.pfcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productSubCategory') }}</span> <br>{{ $filters.defaultValue(proInfo.proTypeName, '--') }}</h6>
						</div>
					</div>
				</div>
				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item">
							<a class="nav-link active" href="#Sectionover1" data-bs-toggle="pill">{{ $t('pro.productBasicData') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#Sectionover2" data-bs-toggle="pill">{{ $t('pro.productCommonData') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#Sectionover3" data-bs-toggle="pill">{{ $t('pro.productAdditionalData') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link datainfo" href="#Sectionover4" data-bs-toggle="pill">{{ $t('pro.navAnalysis') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link datainfo" href="#Sectionover5" data-bs-toggle="pill">{{ $t('pro.performance') }}</a>
						</li>
					</ul>

					<div class="tab-content">
						<div id="Sectionover1" class="tab-pane show active">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.productInformation') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.riskLevel') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.spInfo.riskName, '--') }}
											</td>
											<th>{{ $t('pro.currency') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.spInfo.curCode, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.productType') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.spInfo.proTypeName, '--') }}
											</td>
											<th>{{ $t('pro.issueDate') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.spInfo.issueDt, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.maturityDate') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue($filters.formatDate(proInfo.spInfo.expireDt), '--') }}
											</td>
											<th>{{ $t('pro.channelReward') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.spInfo.channelServiceRate, '--') }}%
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.principalProtection') }}</th>
											<td class="wd-30p">
												{{ proInfo.principalGuarYn === 'Y' ? '100%' : $t('pro.notPrincipalProtected') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.issuerName') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.spInfo.issueName, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.linkedTarget') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<thead>
										<tr>
											<th>{{ $t('pro.linkedTarget') }}</th>
											<th>{{ $t('pro.initialEntryPrice') }}</th>
											<th>{{ $t('pro.conversionPrice') }}</th>
											<th>{{ $t('pro.lowerLimitPrice') }}</th>
											<th>{{ $t('pro.earlyExitPrice') }}</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in proInfo.spInfo.targetList">
											<td class="wd-20p">
												{{ $filters.defaultValue(item.isinCode, '--') }}
											</td>
											<td class="wd-20p">
												{{ item.entryPrice }}
											</td>
											<td class="wd-20p">
												{{ item.convPrice }}
											</td>
											<td class="wd-20p">
												{{ item.kiPrice }}
											</td>
											<td class="wd-20p">
												{{ item.koPrice }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.investmentAmountLimits') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.twdMinInvestment') }}</th>
											<td class="wd-30p">
												{{
													$filters.defaultValue(
														proInfo.spInfo?.mininvLcAmt ? $filters.formatNumber(proInfo.spInfo.mininvLcAmt) : '--',
														'--'
													)
												}}
											</td>
											<th>{{ $t('pro.twdAccumulatedAmount') }}</th>
											<td class="wd-30p">
												{{
													$filters.defaultValue(
														proInfo.spInfo?.mininvLcAccAmt ? $filters.formatNumber(proInfo.spInfo.mininvLcAccAmt) : '--',
														'--'
													)
												}}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.foreignCurrencyMinInvestment') }}</th>
											<td>
												{{
													$filters.defaultValue(
														proInfo.spInfo?.mininvFcAmt ? $filters.formatNumber(proInfo.spInfo.mininvFcAmt) : '--',
														'--'
													)
												}}
											</td>
											<th>{{ $t('pro.foreignCurrencyAccumulatedAmount') }}</th>
											<td>
												{{
													$filters.defaultValue(
														proInfo.spInfo?.mininvFcAccAmt ? $filters.formatNumber(proInfo.spInfo.mininvFcAccAmt) : '--',
														'--'
													)
												}}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionover2" class="tab-pane fade">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.salesRelatedData') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.salesRegion') }}</th>
											<td v-if="proInfo.allYn == 'Y'" class="wd-30p">
												{{ $t('pro.allBranches') }}
											</td>
											<td v-else class="wd-30p">
												--
											</td>
											<th><span>{{ $t('pro.salesTarget') }}</span></th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.targetCusBu, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.isOpenForPurchase') }}</th>
											<td>{{ $filters.defaultValue(proInfo.buyYn, '--') }}</td>
											<th>{{ $t('pro.isOpenForRedemption') }}</th>
											<td>{{ $filters.defaultValue(proInfo.sellYn, '--') }}</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.volatilityType') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.volatilityType, '--') }}</span>
											</td>
											<th><span>{{ $t('pro.dividendFrequency') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.intFreqUnitype, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.financialNeeds') }}</span></th>
											<td colspan="3">
												<div v-for="item in finReqCodeMenu" class="form-check form-check-inline">
													<input
														id="c1"
														v-model="finReqCodes"
														class="form-check-input"
														name="finReqCodes"
														disabled
														:value="item.codeValue"
														type="checkbox"
													>
													<label class="form-check-label" for="c1">{{ $filters.defaultValue(item.codeName, '--') }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.productTags') }}</th>
											<td disabled="disabled" colspan="3">
												{{ $filters.defaultValue(proInfo.selprocatNames, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionover3" class="tab-pane fade">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.otherSettings') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>{{ $t('pro.investmentTarget') }}</span></th>
											<td class="wd-80p">
												<span>{{ $filters.defaultValue(proInfo.sectorName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.productInvestmentArea') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.geoFocusName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.benchmarkSetting') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.benchmarkName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.remarks') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.memo, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.relatedAttachments') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.productProspectus') }}</th>
											<td class="wd-80p">
												<a v-if="proFileA && proFileA.url" :href="proFileA.url" target="_blank">{{
													$filters.defaultValue(proFileA.url, '--')
												}}</a><br v-if="proFileA && proFileA.url">
												<a
													v-if="proFileA"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileA)"
												>{{
													$filters.defaultValue(proFileA.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.investorNotice') }}</th>
											<td class="wd-80p">
												<a v-if="proFileD && proFileD.url" :href="proFileD.url" target="_blank">{{
													$filters.defaultValue(proFileD.url, '--')
												}}</a><br v-if="proFileD && proFileD.url">
												<a
													v-if="proFileD"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileD)"
												>{{
													$filters.defaultValue(proFileD.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.dm') }}</th>
											<td class="wd-80p">
												<a v-if="proFileF && proFileF.url" :href="proFileF.url" target="_blank">{{
													$filters.defaultValue(proFileF.url, '--')
												}}</a><br v-if="proFileF && proFileF.url">
												<a
													v-if="proFileF"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileF)"
												>{{
													$filters.defaultValue(proFileF.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.others') }}</th>
											<td class="wd-80p">
												<a v-if="proFileG && proFileG.url" :href="proFileG.url" target="_blank">{{
													$filters.defaultValue(proFileG.url, '--')
												}}</a><br v-if="proFileG && proFileG.url">
												<a
													v-if="proFileG"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileG)"
												>{{
													$filters.defaultValue(proFileG.showName, '--')
												}}</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.otherRelatedAttachments') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tr>
										<td>
											<span v-for="(item, index) in otherFileList">
												<a
													v-if="index === otherFileList.length - 1"
													v-show="item.show"
													href="#"
													class="tx-link"
													@click="downloadOtherFile(item.docFileId)"
												>{{ $filters.defaultValue(item.showName, '--') }}</a>
												<a
													v-else
													v-show="item.show"
													href="#"
													class="tx-link"
													@click="downloadOtherFile(item.docFileId)"
												>{{ $filters.defaultValue(item.showName, '--') }}, </a>
											</span>
										</td>
									</tr>
								</table>
							</div>
							<div class="tx-note">
								{{ $t('pro.otherRelatedAttachmentsNote') }}
							</div>
						</div>

						<div id="Sectionover4" class="tab-pane fade">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.netValueAnalysis') }}</h4>
								</div>
								<table class="table table-RWD table-bordered text-end">
									<thead>
										<tr>
											<th class="wd-10p text-start">
												{{ $t('pro.item') }}
											</th>
											<th class="wd-30p">
												{{ $t('pro.netValue') }}
											</th>
											<th class="wd-30p">
												{{ $t('pro.highestNav') }}({{ $t('pro.year') }})
											</th>
											<th class="wd-30p">
												{{ $t('pro.lowestNav') }}({{ $t('pro.year') }})
											</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td class="text-start" :data-th="$t('pro.item')">
												{{ $t('pro.netValue') }}
											</td>
											<td class="text-end" :data-th="$t('pro.price')">
												<span>{{ $filters.defaultValue(spPriceAna.aprice, '--') }}({{
													$filters.defaultValue(spPriceAna.priceDt, '--')
												}})</span>
											</td>
											<td class="text-end" :data-th="$t('pro.highestNav') + '(' + $t('pro.year') + ')'">
												<span>{{ $filters.defaultValue(spPriceAna.maxAprice, '--') }}({{
													$filters.defaultValue(spPriceAna.maxPriceDt, '--')
												}})</span>
											</td>
											<td class="text-end" :data-th="$t('pro.lowestNav') + '(' + $t('pro.year') + ')'">
												<span>{{ $filters.defaultValue(spPriceAna.minAprice, '--') }}({{
													$filters.defaultValue(spPriceAna.minPriceDt, '--')
												}})</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="text-center">
									<div class="col-12">
										<div class="card-header">
											<h4>{{ $t('pro.historicalPriceTrend') }}</h4>
										</div>
										<br>
										<vue-net-chart
											ref="spNetChartRef"
											:chart-id="chartId"
											:pro-code="proInfo.proCode"
											:pro-price-range-menu="proPriceRangeMenu"
										/>
										<div class="btn-group btn-group-sm mb-4" role="group">
											<template v-for="item in proPriceRangeMenu">
												<input
													:id="'spNetPeriod' + item.termValue"
													type="radio"
													class="btn-check"
													name="time"
													:checked="item.termValue == '4'"
													@click="getSpNets(proInfo.proCode, item.rangeType, item.rangeFixed)"
												>
												<label class="btn btn-outline-secondary" :for="'spNetPeriod' + item.termValue">{{
													$filters.defaultValue(item.termName, '--')
												}}</label>
											</template>
										</div>
									</div>
								</div>

								<div class="caption">
									{{ $t('pro.last30DaysNav') }}
								</div>
								<table class="table table-RWD table-bordered text-center">
									<thead>
										<tr>
											<th>{{ $t('pro.date') }}</th>
											<th class="text-end">
												{{ $t('pro.referenceNav') }}
											</th>
											<th>{{ $t('pro.date') }}</th>
											<th class="text-end">
												{{ $t('pro.referenceNav') }}
											</th>
											<th>{{ $t('pro.date') }}</th>
											<th class="text-end">
												{{ $t('pro.referenceNav') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="(item, index) in spPriceHist">
											<td :data-th="$t('pro.date')">
												{{ $filters.defaultValue(item.priceDt1, '--') }}
											</td>
											<td class="text-end" :data-th="$t('pro.referenceNav')">
												<span>{{ $filters.defaultValue(item.aprice1, '--') }}</span>
											</td>
											<td :data-th="$t('pro.date')">
												{{ $filters.defaultValue(item.priceDt2, '--') }}
											</td>
											<td class="text-end" :data-th="$t('pro.referenceNav')">
												<span>{{ $filters.defaultValue(item.aprice2, '--') }}</span>
											</td>
											<td :data-th="$t('pro.date')">
												{{ $filters.defaultValue(item.priceDt3, '--') }}
											</td>
											<td class="text-end" :data-th="$t('pro.referenceNav')">
												<span>{{ $filters.defaultValue(item.aprice3, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionover5" class="tab-pane fade">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.performanceAnalysis') }}</h4>
								</div>
								<div class="card-body">
									<div class="row">
										<div class="col-sm-2">
											<label class="tc-blue">{{ $t('pro.selectProduct') }}:</label>
										</div>
										<div class="col-sm-8">
											<div class="input-group">
												<select v-model="proCode" class="form-select">
													<option selected value>
														--
													</option>
													<option v-for="item in productList" :value="item.proCode">
														{{ $filters.defaultValue(item.proName, '--') }}
														{{ $filters.defaultValue(item.proCode, '--') }}
													</option>
												</select>
											</div>
										</div>
										<div class="col-sm-2">
											<p>
												<input
													class="btn btn-primary text-alignRight"
													type="button"
													:value="$t('pro.addButton')"
													@click="addPro()"
												>
											</p>
										</div>
									</div>

									<div class="caption">
										{{ $t('pro.addedProducts') }}
									</div>
									<div class="table-responsive mb-3">
										<table class="table table-bordered">
											<thead>
												<tr>
													<th>{{ $t('pro.productName') }}</th>
													<th class="text-end">
														{{ $t('pro.oneYearReturn') }}
													</th>
													<th class="text-center">
														{{ $t('pro.action') }}
													</th>
												</tr>
											</thead>
											<tbody>
												<tr v-for="item in observedProList">
													<td>
														{{ $filters.defaultValue(item.proName, '--') }}{{ $filters.defaultValue(item.proCode, '--') }}
													</td>
													<td class="text-end">
														{{ $filters.defaultValue($filters.formatPct(item.fcTdReturn), '--') }}%
													</td>
													<td class="text-center">
														<button
															type="button"
															class="btn btn-danger btn-icon"
															data-bs-toggle="tooltip"
															:title="$t('pro.delete')"
															@click="deletePro(item.proCode)"
														>
															<i class="fa-solid fa-trash" />
														</button>
													</td>
												</tr>
											</tbody>
										</table>
									</div>

									<div class="text-center">
										<!-- Performance Analysis Chart -->
										<vue-performances-chart ref="spPerformancesChartRef" :chart-id="performancesId" />
										<div class="btn-group btn-group-sm mb-4" role="group">
											<template v-for="item in proPriceRangeMenu">
												<input
													:id="'performancesPeriod' + item.termValue"
													type="radio"
													class="btn-check"
													name="time"
													:checked="item.termValue == '4' ? true : false"
												>
												<label
													class="btn btn-outline-secondary"
													:for="'performancesPeriod' + item.termValue"
													@click.prevent="getSpPerformances(proCodes, item.rangeType, item.rangeFixed)"
												>{{ $filters.defaultValue(item.termName, '--') }}</label>
											</template>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-white" @click.prevent="close()">
					{{ $t('pro.closeWindow') }}
				</button>
			</div>
		</div>
	</div>
	<!-- Modal 3 End -->
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import vueModal from '@/views/components/model.vue';
import vuePerformancesChart from './performancesChart.vue';
import vueNetChart from './netChart.vue';
export default {
	components: {
		vueModal,
		vuePerformancesChart,
		vueNetChart
	},
	props: {
		downloadFile: Function,
		downloadOtherFile: Function,
		finReqCodeMenu: Array,
		proPriceRangeMenu: Array,
		close: Function
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				bondInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},
			chartsData: [], // Performance analysis chart data
			proCodes: [], // Performance analysis - added products
			productList: [], // Performance analysis - product list
			observedProList: [], // Performance analysis - added products return rate list
			proCode: null, // Performance analysis - selected product

			finReqCodes: [],
			proFileA: {},
			proFileD: {},
			proFileF: {},
			proFileG: {},
			otherFileList: [],
			spPriceAna: {},
			spPriceHist: [],
			chartId: 'spNetChartId',
			performancesId: 'spPerformancesChartId'
		};
	},
	watch: {},
	created() {},
	mounted: function () {},
	methods: {
		getProInfo: async function (bankProCode, pfcatCode) {
			const self = this;
			self.proCodes = [];
			self.proCodes.push(bankProCode); // Default added products

			const ret = await this.$api.getProductInfoApi({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});

			if (_.isNil(ret.data)) {
				ret.data = {};
				this.$bi.alert(this.$t('pro.dataNotExist'));
				return;
			}
			if (_.isNil(ret.data.spInfo)) {
				ret.data.spInfo = {};
			}

			self.proInfo = ret.data;

			// Product common data
			if (!_.isNil(self.proInfo.spInfo)) {
				self.proInfo.spInfo.principalGuarYn = self.proInfo.spInfo.principalGuarYn === 'Y' ? '100%' : this.$t('pro.noPrincipalProtection');
			}

			if (!_.isUndefined(self.proInfo.targetCusBu)) {
				const targetCusBuList = await this.$api.getAdmCodeDetail({
					codeType: 'CUS_BU'
				});
				const targetCusBuObjs = _.filter(targetCusBuList.data, {
					codeValue: self.proInfo.targetCusBu
				});
				self.proInfo.targetCusBu = targetCusBuObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.buyYn) || !_.isUndefined(self.proInfo.sellYn)) {
				const selectYnList = await this.$api.getAdmCodeDetail({
					codeType: 'SELECT_YN'
				});

				if (!_.isUndefined(self.proInfo.buyYn)) {
					const buyYnObjs = _.filter(selectYnList.data, {
						codeValue: self.proInfo.buyYn
					});
					self.proInfo.buyYn = buyYnObjs[0].codeName;
				}

				if (!_.isUndefined(self.proInfo.sellYn)) {
					const sellYnObjs = _.filter(selectYnList.data, {
						codeValue: self.proInfo.sellYn
					});
					self.proInfo.sellYn = sellYnObjs[0].codeName;
				}
			}

			if (!_.isUndefined(self.proInfo.volatilityType)) {
				const volatilityTypeList = await this.$api.getAdmCodeDetail({
					codeType: 'VOLATILITY_TYPE'
				});
				const volatilityTypeObjs = _.filter(volatilityTypeList.data, {
					codeValue: self.proInfo.volatilityType
				});
				self.proInfo.volatilityType = volatilityTypeObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.intFreqUnitype)) {
				const intFreqUnitypeList = await this.$api.getAdmCodeDetail({
					codeType: 'INT_FREQ_UNITTYPE'
				});
				const intFreqUnitypeObjs = _.filter(intFreqUnitypeList.data, {
					codeValue: self.proInfo.intFreqUnitype
				});
				self.proInfo.intFreqUnitype = intFreqUnitypeObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.selprocatNames)) {
				self.proInfo.selprocatNames = self.proInfo.selprocatNames.replaceAll(',', ', ');
			}

			if (!_.isUndefined(self.proInfo.finReqCode)) {
				self.finReqCodes = self.proInfo.finReqCode.split(',');
			}

			// Product additional data
			const ret2 = await this.$api.getProductsCommInfo({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});
			if (!_.isNil(ret2.data)) {
				if (ret2.data.proDocs) {
					self.otherFileList = ret2.data.proDocs; // Other related attachments
					self.otherFileList.forEach(function (item) {
						// Other related attachments - file display time range
						item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
					});
				}

				const proFileList = ret2.data.proFiles;
				if (!_.isNil(proFileList)) {
					self.proFileA = proFileList.filter(proFile => proFile.fileType === 'A')[0];
					self.proFileD = proFileList.filter(proFile => proFile.fileType === 'D')[0];
					self.proFileF = proFileList.filter(proFile => proFile.fileType === 'F')[0];
					self.proFileG = proFileList.filter(proFile => proFile.fileType === 'G')[0];
				}
			}
			this.$forceUpdate();
			self.observedPro(); // Performance analysis - get added products list
			self.productMenu();
		},
		// Product information/price analysis and last 30 days price
		async getSpPriceAna(proCode) {
			const proCodeArray = [proCode];
			const ret = await this.$api.getPriceAnaApi({
				proCodes: proCodeArray
			});

			if (!_.isNil(ret.data)) {
				this.spPriceAna = ret.data;
				if (!_.isNil(ret.data.priceHist)) {
					const orgPriceHis = ret.data.priceHist;
					const newPriceHis = [];
					orgPriceHis.forEach(function (item, index) {
						if (index % 3 == 0) {
							if (index + 2 < orgPriceHis.length) {
								const pricHisObj = {
									priceDt1: orgPriceHis[index].priceDt,
									aprice1: Number.parseFloat(orgPriceHis[index].aprice),
									priceDt2: orgPriceHis[index + 1].priceDt,
									aprice2: Number.parseFloat(orgPriceHis[index + 1].aprice),
									priceDt3: orgPriceHis[index + 2].priceDt,
									aprice3: Number.parseFloat(orgPriceHis[index + 2].aprice)
								};
								newPriceHis.push(pricHisObj);
							}
							else if (index + 1 < orgPriceHis.length) {
								const pricHisObj = {
									priceDt1: orgPriceHis[index].priceDt,
									aprice1: Number.parseFloat(orgPriceHis[index].aprice),
									priceDt2: orgPriceHis[index + 1].priceDt,
									aprice2: Number.parseFloat(orgPriceHis[index + 1].aprice),
									priceDt3: null,
									aprice3: null
								};
								newPriceHis.push(pricHisObj);
							}
							else {
								const pricHisObj = {
									priceDt1: orgPriceHis[index].priceDt,
									aprice1: Number.parseFloat(orgPriceHis[index].aprice),
									priceDt2: null,
									aprice2: null,
									priceDt3: null,
									aprice3: null
								};
								newPriceHis.push(pricHisObj);
							}
						}
					});
					this.spPriceHist = newPriceHis;
				}
			}
		},
		getSpNets: function (proCode, rangeType, rangeFixed) {
			const self = this;
			self.$refs.spNetChartRef.getNets(proCode, rangeType, rangeFixed);
		},
		// Performance analysis chart
		async getSpPerformances(proCodes, rangeType, rangeFixed) {
			const ret = await this.$api.getPerformanceRunChartApi({
				proCodes,
				freqType: rangeType,
				freqFixed: rangeFixed
			});

			if (!_.isEmpty(ret.data.datas)) {
				for (let i = 0; i < ret.data.length; i++) {
					ret.data[i].datas.forEach((e) => {
						e.value = e.returnFc;
						e.date = new Date(e.returnDt).getTime();
					});
				}
				this.chartsData = ret.data;
				this.$refs.spPerformancesChartRef.initChart(this.chartsData);
			}
		},
		// Performance analysis - added products list
		async observedPro() {
			const ret = await this.$api.getObservedProductsApi({
				proCodes: this.proCodes
			});
			this.observedProList = ret.data;
		},
		// Performance analysis - product selection dropdown
		async productMenu() {
			const ret = await this.$api.getProductByPfcatCodeApi({
				pfcatCode: 'sp'
			});
			this.productList = ret.data;
		},
		// Performance analysis - add selected product button
		addPro() {
			const self = this;
			let pk = null;
			pk = _.find(self.proCodes, function (item) {
				return item == self.proCode;
			});
			if (self.proCode != null && pk == null) {
				self.proCodes.push(self.proCode); // Add selected product
				self.observedPro(); // Performance analysis - get added products list
				self.getSpPerformances(self.proCodes, 'Y', -1.0); // Product info/performance analysis chart
			}
			else if (self.proCode != null && pk != null) {
				this.$bi.alert(this.$t('pro.productAlreadyAdded'));
			}
			else {
				this.$bi.alert(this.$t('pro.pleaseSelectProduct'));
			}
		},
		// Performance analysis - delete added product button
		deletePro(proCode) {
			const self = this;
			if (self.proCodes.length > 1) {
				const index = self.proCodes.indexOf(proCode); // Find index to remove
				self.proCodes.splice(index, 1); // Remove added product (index position, number of elements to delete)
				self.observedPro(); // Performance analysis - get added products list
				self.getSpPerformances(self.proCodes, 'Y', -1.0); // Product info/performance analysis chart
			}
			else {
				this.$bi.alert(this.$t('pro.atLeastOneProduct'));
			}
		}
	} // methods end
};
</script>
