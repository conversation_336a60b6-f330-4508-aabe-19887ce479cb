<template>
	<!-- Modal group InsCmp start -->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('pro.selectInsuranceCompany') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				/>
			</div>
			<div class="modal-body">
				<div class="row m-2">
					<div v-for="item in inscmpsMenu" class="col-3 form-check">
						<input
							:id="item.inscmpCode"
							v-model="inscmpCode"
							class="form-check-input"
							name="inscmpCodes"
							:value="item.inscmpCode"
							type="checkbox"
						>
						<label class="form-check-label" :for="item.inscmpCode">{{ item.inscmpCode }}{{ item.inscmpName }}</label>
					</div>
				</div>
				<!-- 展開按鈕 -->
				<div
					v-if="insOthercmpsMenu.length > 0"
					class="card-header"
					data-bs-toggle="collapse"
					data-bs-target="#collapseListGroup"
				>
					<h4>{{ $t('pro.expand') }}</h4>
				</div>

				<!-- 第二組選項 -->
				<div v-if="insOthercmpsMenu.length > 0" id="collapseListGroup" class="collapse">
					<div class="modal-body">
						<div class="row">
							<div v-for="(item, index) in insOthercmpsMenu" :key="item.compCode" class="col-3">
								<div class="form-check">
									<input
										:id="item.inscmpCode"
										v-model="inscmpCode"
										class="form-check-input"
										name="insOthercmpsMenu"
										:value="item.inscmpCode"
										type="checkbox"
									>
									<label class="form-check-label" :for="item.inscmpCode">{{ item.inscmpCode }}{{ item.inscmpName }}</label>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<input
					id="modaladdButton"
					type="button"
					class="btn btn-primary"
					:value="$t('pro.add')"
					@click="addInsCmp()"
				>
			</div>
		</div>
	</div>
	<!-- Modal group InsCmp End -->
</template>
<script>
export default {
	props: {
		inscmpProp: Array, // 已選擇項目
		close: Function
	},
	data: function () {
		return {
			inscmpsMenu: [], // 保險公司 選項
			insOthercmpsMenu: [], // 其他保險公司 選項
			inscmpCode: [], // 保險公司 選擇項目
			inscmpItem: [] // 保險公司 選擇項目代碼與中文名稱
		};
	},
	watch: {},
	mounted: function () {
		const self = this;
		self.groupInscmpsMenu();
		self.groupInsOthercmpsMenu();
		self.inscmpItem = [];
	},
	methods: {
		groupInscmpsMenu: async function () {
			// 保險公司來源資料
			const self = this;
			const ret = await this.$api.getGroupInsCompanies();
			self.inscmpsMenu = ret.data;
		},

		groupInsOthercmpsMenu: async function () {
			// 其餘保險公司來源資料
			const self = this;
			const ret = await this.$api.getOtherInsCompanies();
			self.insOthercmpsMenu = ret.data;
		},
		addInsCmp() {
			// 增加保險公司
			const self = this;
			self.inscmpItem = [];
			if (self.inscmpCode.length === 0) {
				this.$bi.alert(this.$t('pro.pleaseSelectAtLeastOne'));
			}
			else {
				self.inscmpCode.forEach((code) => {
					if (_.find(self.inscmpsMenu, { inscmpCode: code })) {
						const item = _.find(self.inscmpsMenu, { inscmpCode: code });
						self.inscmpItem.push(item);
					}
					else if (_.find(self.insOthercmpsMenu, { inscmpCode: code })) {
						const item = _.find(self.insOthercmpsMenu, { inscmpCode: code });
						self.inscmpItem.push(item);
					}
				});
				self.$emit('selected', self.inscmpItem);
				self.close();
			}
		},
		inscmpPropItem(v) {
			const self = this;
			if (v.length > 0) {
				self.inscmpItem = v;
				self.inscmpCode = [];
				v.forEach((item) => {
					self.inscmpCode.push(item.inscmpCode);
				});
			}
			else {
				self.inscmpItem = [];
				self.inscmpCode = [];
			}
		},
		// 全形轉半形
		toHalfWidth: function (str) {
			return str
				.replace(/[\uff01-\uff5e]/g, function (char) {
					return String.fromCharCode(char.charCodeAt(0) - 0xfee0);
				})
				.replace(/\u3000/g, ' ');
		},
		getStringType: function (str) {
			const convertedStr = this.toHalfWidth(str);
			const firstChar = convertedStr.charAt(0);

			if (/[a-zA-Z]/.test(firstChar)) return 'english';
			if (/[0-9]/.test(firstChar)) return 'number';
			return 'chinese';
		}
	}
	// methods end
};
</script>
