<template>
	<div class="col-12">
		<h4>
			{{ $t('pro.fluctuationProbabilityAnalysis') }}
			<small class="small-text">{{ $t('pro.tableCalculatedBasedOnRecentFiveYears', { minYear: $filters.defaultValue(minYear, '--'), maxYear: $filters.defaultValue(maxYear, '--') }) }}</small>
		</h4>
		<div class="table-responsive">
			<table width="100%" class="table table-condensed text-right">
				<thead>
					<tr>
						<th width="4%">
&nbsp;
						</th>
						<th width="7%">
&nbsp;
						</th>
						<th width="5%">
&nbsp;
						</th>
						<th width="7%" class="text-center">
							{{ $t('pro.oneMonth_alt') }}
						</th>
						<th width="7%" class="text-center">
							{{ $t('pro.february') }}
						</th>
						<th width="7%" class="text-center">
							{{ $t('pro.threeMonths_alt') }}
						</th>
						<th width="7%" class="text-center">
							{{ $t('pro.april') }}
						</th>
						<th width="7%" class="text-center">
							{{ $t('pro.may') }}
						</th>
						<th width="7%" class="text-center">
							{{ $t('pro.sixMonths_alt') }}
						</th>
						<th width="7%" class="text-center">
							{{ $t('pro.july') }}
						</th>
						<th width="7%" class="text-center">
							{{ $t('pro.august') }}
						</th>
						<th width="7%" class="text-center">
							{{ $t('pro.nineMonths') }}
						</th>
						<th width="7%" class="text-center">
							{{ $t('pro.october') }}
						</th>
						<th width="7%" class="text-center">
							{{ $t('pro.november') }}
						</th>
						<th width="7%" class="text-center">
							{{ $t('pro.december') }}
						</th>
					</tr>
					<tr>
						<th rowspan="2">
&nbsp;
						</th>
						<th rowspan="2">
&nbsp;
						</th>
						<th rowspan="2">
							{{ $t('pro.calculationPeriodMonths') }}
						</th>
						<th>{{ $t('pro.upwardProbabilityPercent') }}</th>
						<th>{{ $t('pro.upwardProbabilityPercent') }}</th>
						<th>{{ $t('pro.upwardProbabilityPercent') }}</th>
						<th>{{ $t('pro.upwardProbabilityPercent') }}</th>
						<th>{{ $t('pro.upwardProbabilityPercent') }}</th>
						<th>{{ $t('pro.upwardProbabilityPercent') }}</th>
						<th>{{ $t('pro.upwardProbabilityPercent') }}</th>
						<th>{{ $t('pro.upwardProbabilityPercent') }}</th>
						<th>{{ $t('pro.upwardProbabilityPercent') }}</th>
						<th>{{ $t('pro.upwardProbabilityPercent') }}</th>
						<th>{{ $t('pro.upwardProbabilityPercent') }}</th>
						<th>{{ $t('pro.upwardProbabilityPercent') }}</th>
					</tr>
					<tr>
						<th>{{ $t('pro.averageReturnPercent') }}</th>
						<th>{{ $t('pro.averageReturnPercent') }}</th>
						<th>{{ $t('pro.averageReturnPercent') }}</th>
						<th>{{ $t('pro.averageReturnPercent') }}</th>
						<th>{{ $t('pro.averageReturnPercent') }}</th>
						<th>{{ $t('pro.averageReturnPercent') }}</th>
						<th>{{ $t('pro.averageReturnPercent') }}</th>
						<th>{{ $t('pro.averageReturnPercent') }}</th>
						<th>{{ $t('pro.averageReturnPercent') }}</th>
						<th>{{ $t('pro.averageReturnPercent') }}</th>
						<th>{{ $t('pro.averageReturnPercent') }}</th>
						<th>{{ $t('pro.averageReturnPercent') }}</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td rowspan="2">
							{{ $t('pro.name') }}
						</td>
						<td rowspan="2" class="text-left">
							<a>{{ $filters.defaultValue(fundInfo && fundInfo.fundName, '--') }}</a>
						</td>
						<td rowspan="2">
							{{ period }}
						</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 1), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 2), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 3), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 4), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 5), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 6), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 7), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 8), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 9), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 10), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 11), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(monthPcts, 12), '0,0.[00]'), '--') }}</td>
					</tr>
					<tr>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 1)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 2)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 3)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 4)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 5)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 6)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 7)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 8)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 9)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 10)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 11)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(monthPcts, 12)), '--')" /></td>
					</tr>
					<tr>
						<td rowspan="2">
							{{ $t('pro.correspondingIndex') }}
						</td>
						<td rowspan="2" class="text-left">
							<a>{{ $filters.defaultValue(bmName, '--') }}</a>
						</td>
						<td rowspan="2">
							{{ period }}
						</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 1), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 2), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 3), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 4), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 5), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 6), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 7), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 8), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 9), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 10), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 11), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(bmMonthPcts, 12), '0,0.[00]'), '--') }}</td>
					</tr>
					<tr>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 1)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 2)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 3)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 4)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 5)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 6)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 7)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 8)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 9)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 10)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 11)), '--')" /></td>
						<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(bmMonthPcts, 12)), '--')" /></td>
					</tr>
					<tr>
						<td rowspan="2">
							{{ $t('pro.sameTypeIndex') }}
						</td>
						<td rowspan="2" class="text-left">
							<a>{{ $filters.defaultValue(fundInfo && fundInfo.globalClassBmName, '--') }}</a>
						</td>
						<td rowspan="2">
							{{ period }}
						</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 1), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 2), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 3), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 4), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 5), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 6), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 7), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 8), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 9), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 10), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 11), '0,0.[00]'), '--') }}</td>
						<td>{{ $filters.defaultValue($filters.formatNum(getMonthPctUpRate(globalClassBmMonthPcts, 12), '0,0.[00]'), '--') }}</td>
					</tr>
					<tr>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 1)), '--')" />
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 2)), '--')" />
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 3)), '--')" />
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 4)), '--')" />
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 5)), '--')" />
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 6)), '--')" />
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 7)), '--')" />
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 8)), '--')" />
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 9)), '--')" />
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 10)), '--')" />
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 11)), '--')" />
						</td>
						<td>
							<p v-html="$filters.defaultValue($filters.formatFlucWithView(getAvgMonthPctRate(globalClassBmMonthPcts, 12)), '--')" />
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
export default {
	props: {
		techCurrencyCode: String,
		fundInfo: Object
	},
	data: function () {
		return {
			period: 60,
			beginDate: moment().subtract(60, 'months').format('YYYY/MM/DD'),
			endDate: moment().format('YYYY/MM/DD'),
			monthPcts: [],
			bmMonthPcts: [],
			globalClassBmMonthPcts: [],
			bmName: null
		};
	},
	computed: {
		maxYear: function () {
			const years = _.map(_.orderBy(this.monthPcts, ['dataDate'], ['desc']), function (item) {
				return moment(item.dataDate, 'YYYY-MM-DD').format('Y');
			});
			return years ? years[0] : null;
		},
		minYear: function () {
			const years = _.map(_.orderBy(this.monthPcts, ['dataDate'], ['asc']), function (item) {
				return moment(item.dataDate, 'YYYY-MM-DD').format('Y');
			});
			return years ? years[0] : null;
		}
	},
	watch: {
		fundInfo: {
			handler: async function (newVal, oldVal) {
				await this.getMonthPcts();
				await this.getBmMonthPcts();
				await this.getGlobalClassBmMonthPcts();
			}
		}
	},
	mounted: function () {},
	methods: {
		getMonthPcts: async function () {
			const self = this;
			const ret = await this.$api.getMonthPctsApi({
				fundCode: self.fundInfo.fundCode,
				beginDate: self.beginDate,
				endDate: self.endDate,
				techCurrencyCode: self.techCurrencyCode
			});
			self.monthPcts = ret.data;
		},
		getBmMonthPcts: async function () {
			const self = this;
			const managerBmCode = self.fundInfo.managerBmCode;
			const analysisBmCode = self.fundInfo.analysisBmCode;
			let bmCode;

			if (managerBmCode && managerBmCode != '11000006' && managerBmCode != '11000000') {
				self.bmName = self.fundInfo.managerBmName;
				bmCode = managerBmCode;
			}
			else if (analysisBmCode) {
				self.bmName = self.fundInfo.analysisBmName;
				bmCode = analysisBmCode;
			}
			const ret = await this.$api.getMonthPctsApi({
				fundCode: bmCode,
				beginDate: self.beginDate,
				endDate: self.endDate,
				techCurrencyCode: self.techCurrencyCode
			});
			self.bmMonthPcts = ret.data;
		},
		getGlobalClassBmMonthPcts: async function () {
			const self = this;
			const ret = await this.$api.getMonthPctsApi({
				fundCode: self.fundInfo.globalClassBmCode,
				beginDate: self.beginDate,
				endDate: self.endDate,
				techCurrencyCode: self.techCurrencyCode
			});
			self.globalClassBmMonthPcts = ret.data;
		},
		getMonthPctUpRate: function (pcts, month) {
			const regex = /^\d+(\.\d+)?$/;
			const monthPcts = this.getSameMonthPcts(pcts, month);
			const upCount = _.filter(monthPcts, function (item) {
				return regex.test(item.dvalue);
			});
			return monthPcts.length === this.period / 12 ? (upCount.length / monthPcts.length) * 100 : null;
		},
		getAvgMonthPctRate: function (pcts, month) {
			const monthPcts = this.getSameMonthPcts(pcts, month);
			return monthPcts.length === this.period / 12 ? _.sumBy(monthPcts, 'dvalue') / monthPcts.length : null;
		},
		getSameMonthPcts: function (pcts, month) {
			let monthPcts = _.filter(pcts, function (item) {
				return moment(item.dataDate, 'YYYY-MM-DD').format('M') == month;
			});
			monthPcts = _.orderBy(monthPcts, ['dataDate'], ['desc']);
			const sameMonthPcts = []; // Fetch latest row in same month.
			_.forEach(monthPcts, function (item) {
				const yearMonth = moment(item.dataDate, 'YYYY-MM-DD').format('YYYYMM');
				const existSameMonthPct = _.find(sameMonthPcts, function (item) {
					return yearMonth == moment(item.dataDate, 'YYYY-MM-DD').format('YYYYMM');
				});
				if (!existSameMonthPct) {
					sameMonthPcts.push(item);
				}
			});
			return sameMonthPcts;
		}
	}
};
</script>
