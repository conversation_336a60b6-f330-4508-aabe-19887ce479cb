<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<!--Page content ETF start-->
			<div class="tab-nav-tabs my-3">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'common' }"
							data-bs-toggle="tab"
							@click="changeTab('common')"
						>{{ $t('pro.generalFilter') }}</a>
					</li>
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'fast' }"
							data-bs-toggle="tab"
							@click="changeTab('fast')"
						>{{ $t('pro.quickFilter') }}</a>
					</li>
				</ul>

				<div class="tab-content">
					<div class="tab-pane fade show" :class="{ 'active show': activeTab == 'common' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>{{ $t('pro.searchCond') }}</h4>
								<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
							</div>
							<div id="collapseListGroup1" class="collapse show">
								<div class="card-body">
									<form>
										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.productCode') }}</label>
												<input
													id="prod_bank_pro_code"
													v-model="bankProCode"
													class="form-control"
													maxlength="20"
													size="25"
													type="text"
													value=""
												>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.productName') }}</label>
												<input
													id="prod_pro_name"
													v-model="proName"
													class="form-control"
													maxlength="20"
													size="45"
													type="text"
													value=""
												>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.currency') }}</label>
												<select
													id="curMenuEtf"
													v-model="curObjs"
													class="selectpicker form-control"
													multiple
													:title="$t('pro.pleaseSelectCurrency')"
													data-style="btn-white"
												>
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="(item, index) in curOption" :key="index" :value="item.value">
														{{ $filters.defaultValue(item.name, '--') }}
													</option>
												</select>
											</div>
										</div>

										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.riskLevel') }}</label>
												<div class="form-check-group">
													<div v-for="(item, index) in riskMenu" class="form-check form-check-inline">
														<input
															:id="'riskGrade-' + index"
															v-model="riskCodes"
															type="checkbox"
															class="form-check-input"
															name="riskCodes"
															:value="item.riskCode"
														>
														<label :for="'riskGrade-' + index" class="form-check-label">{{ item.riskName }}</label>
													</div>
												</div>
											</div>
											<!-- TODO -->
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.piOnlySales') }}</label>
												<div v-for="item in profInvestorMenu" class="form-check form-check-inline">
													<input
														:id="'profInvestor' + item.codeValue"
														v-model="profInvestorYn"
														class="form-check-input"
														type="radio"
														:value="item.codeValue"
														name="fastCode"
													>
													<label class="form-check-label" :for="'profInvestor' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.etfType') }}</label>
												<select
													id="proType"
													v-model="proTypeCode"
													class="form-select"
													name="proType"
													:title="$t('pro.pleaseSelectType')"
													data-style="btn-white"
												>
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="item in proTypeMenu" :value="item.proTypeCode">
														{{ $filters.defaultValue(item.proTypeName, '--') }}
													</option>
												</select>
											</div>
										</div>

										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">ISINCODE</label>
												<input
													id="isinCode"
													v-model="isinCode"
													class="form-control"
													maxlength="20"
													size="45"
													type="text"
													value=""
												>
											</div>
										</div>

										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.standardDeviation') }}(%)</label>
												<div class="input-group">
													<select
														v-model="sdVaule"
														name="local"
														class="form-select"
														@change="sdMenuChangeHandler()"
													>
														<option value="">
															{{ $t('pro.all') }}
														</option>
														<option v-for="item in sdMenu" :value="item.termValue">
															{{ $filters.defaultValue(item.termName, '--') }}
														</option>
													</select>
													<input
														v-model="sdRangeMin"
														type="text"
														class="form-control sdInput"
														size="5"
													>
													<div class="input-group-text sdInput">
														~
													</div>
													<input
														v-model="sdRangeMax"
														type="text"
														class="form-control sdInput"
														size="5"
													>
												</div>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.etfSize') }}({{ $t('pro.tenMillion') }})</label>
												<div class="input-group">
													<input
														v-model="etfSizeMin"
														type="number"
														class="form-control"
														size="5"
													>
													<div class="input-group-text">
														~
													</div>
													<input
														v-model="etfSizeMax"
														type="number"
														class="form-control"
														size="5"
													>
												</div>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.targetPerformance') }}(%)</label>
												<div class="input-group">
													<select
														v-model="perfTime"
														name="lipperRankType"
														class="form-select"
														@change="perfTimeChangeHandler"
													>
														<option value="">
															{{ $t('pro.all') }}
														</option>
														<option v-for="item in perfMenu" :value="item.codeValue">
															{{ $filters.defaultValue(item.codeName, '--') }}
														</option>
													</select>
													<input
														v-model="perfMin"
														type="number"
														class="form-control perfInput"
														size="5"
													>
													<div class="input-group-text perfInput">
														~
													</div>
													<input
														v-model="perfMax"
														type="number"
														class="form-control perfInput"
														size="5"
													>
												</div>
											</div>
										</div>

										<div class="form-row">
											<div class="form-group col-12 col-lg-6">
												<label class="form-label">{{ $t('pro.issuer') }}</label>
												<button type="button" class="btn btn-primary" @click="groupIssuerModalHandler()">
													{{ $t('pro.selectIssuer') }}
												</button>
												<vue-modal :is-open="isOpenIssuerModal" @close="isOpenIssuerModal = false">
													<template #content="props">
														<vue-group-issuer-modal
															id="groupIssuerModal"
															ref="groupIssuerModalRef"
															:close="props.close"
															:pfcat-code="'ETF'"
															@selected="selectedIssuer"
														/>
													</template>
												</vue-modal>
											</div>
											<div class="form-group col-12 col-lg-6">
												<label class="form-label">{{ $t('pro.exchange') }}</label>
												<button type="button" class="btn btn-primary" @click="groupProExchangeModalHandler()">
													{{ $t('pro.selectExchange') }}
												</button>
												<vue-modal :is-open="isOpenExchangeModal" @close="isOpenExchangeModal = false">
													<template #content="props">
														<vue-group-pro-exchange-modal
															id="groupProExchangeModal"
															ref="groupProExchangeModalRef"
															:close="props.close"
															:pro-exchange-prop="proExchangeItem"
															@selected="selectedProExchange"
														/>
													</template>
												</vue-modal>
											</div>
										</div>

										<div class="form-row d-flex align-items-start">
											<div class="col">
												<div v-for="item in issuerItem" style="padding-left: 110px; padding-bottom: 15px">
													<span class="form-check-label"> {{ $filters.defaultValue(item.issuerName, '--') }}</span>
													<a href="#" @click="deleteIssuerItem(item.issuerCode)">
														<img :src="getImgURL('icon', 'i-cancel.png')">
													</a>
												</div>
											</div>

											<div class="col">
												<div v-for="item in proExchangeItem" style="padding-left: 110px; padding-bottom: 15px">
													<span class="form-check-label"> {{ $filters.defaultValue(item.exchangeName, '--') }}</span>
													<a href="#" @click="deleteProExchangeItem(item.exchangeCode)">
														<img :src="getImgURL('icon', 'i-cancel.png')">
													</a>
												</div>
											</div>
										</div>

										<div class="form-footer">
											<button class="btn btn-primary" @click.prevent="gotoPage(0)">
												{{ $t('pro.search') }}
											</button>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>

					<div class="tab-pane fade" :class="{ 'active show': activeTab == 'fast' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
								<h4>{{ $t('pro.searchCond') }}</h4>
								<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
							</div>
							<div id="collapseListGroup2" class="collapse show">
								<div class="card-body">
									<form>
										<div class="form-row">
											<div class="form-group col-12 col-lg-12">
												<label class="form-label tx-require">{{ $t('pro.filterConditions') }}</label>
												<div v-for="item in etfFastMenu" class="form-check-group" @change="fastChange(item.codeValue)">
													<input
														:id="'fast' + item.codeValue"
														v-model="fastCode"
														class="form-check-input"
														name="fastCode"
														:value="item.codeValue"
														type="radio"
													>
													<label class="form-check-label" :for="'fast' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>

											<div id="rangeFixedTrEtf" class="form-group col-12 col-lg-6" style="display: none">
												<label class="form-label tx-require">{{ $t('pro.displayRange') }}</label>

												<select id="prod_protype_code" v-model="timeRange" class="form-select">
													<option v-for="item in timeRangeMenu" :value="item">
														{{ $filters.defaultValue(item.termName, '--') }}
													</option>
												</select>
											</div>

											<div id="proPerfTimeTrEtf" class="form-group col-12 col-lg-6" style="display: none">
												<label class="form-label tx-require">{{ $t('pro.targetPerformance') }}</label>
												<select id="vfAstStat_stat_code" v-model="perf" class="form-select">
													<option v-for="item in perfMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</div>

											<div id="maxRowIdTrEtf" class="form-group col-12 col-lg-6" style="display: none">
												<label class="form-label tx-require">{{ $t('pro.displayDataCount') }}</label>
												<select id="maxRowId" v-model="rowNumber" class="form-select">
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
														{{ $filters.defaultValue(item.termName, '--') }}
													</option>
												</select>
											</div>
										</div>
									</form>

									<div class="form-footer">
										<button class="btn btn-primary" @click.prevent="gotoFastPage(0)">
											{{ $t('pro.search') }}
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div v-if="pageData.content.length > 0" id="searchResult">
				<div class="tab-nav-line">
					<ul
						class="nav nav-line"
						style="background-color: transparent; margin-top: 10px; margin-bottom: 20px; border-bottom: 2px solid #e2e2e2"
					>
						<li class="nav-item">
							<a href="#pie_chart1" data-bs-toggle="tab" class="nav-link active">{{ $t('pro.basicData') }}</a>
						</li>
						<li class="nav-item">
							<a href="#pie_chart2" data-bs-toggle="tab" class="nav-link">{{ $t('pro.marketPerformance') }}</a>
						</li>
					</ul>
					<div class="tab-content">
						<div id="pie_chart1" role="tabpanel" class="tab-pane active">
							<div class="card card-table">
								<div class="card-header">
									<h4>{{ $t('pro.searchResult') }}</h4>
									<div style="display: flex">
										<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
										<button
											type="button"
											class="btn btn-info ms-2"
											@click="performancesCompareModelHandler()"
										>
											{{ $t('pro.performanceComparisonChart') }}
										</button>
										<vue-modal :is-open="isOpenCompareModal" @close="isOpenCompareModal = false">
											<template #content="props">
												<vue-performances-compare-modal
													id="performancesCompareModal"
													ref="performancesCompareModalRef"
													:close="props.close"
												/>
											</template>
										</vue-modal>
									</div>
								</div>
								<div class="table-responsive">
									<table class="table table-bordered table-blue">
										<thead>
											<tr>
												<th>{{ $t('pro.addToComparison') }}</th>
												<th>
													{{ $t('pro.productCode') }}<a
														v-if="activeTab === 'common'"
														href="#"
														class="icon-sort"
														@click="sort('BANK_PRO_CODE')"
													/><a
														v-if="activeTab === 'fast'"
														href="#"
														class="icon-sort"
														@click="sortFast('BANK_PRO_CODE')"
													/>
												</th>
												<th>
													{{ $t('pro.productChineseName') }}<a
														v-if="activeTab === 'common'"
														href="#"
														class="icon-sort"
														@click="sort('PRO_NAME')"
													/><a
														v-if="activeTab === 'fast'"
														href="#"
														class="icon-sort"
														@click="sortFast('PRO_NAME')"
													/>
												</th>
												<th>
													{{ $t('pro.riskLevel') }}<a
														v-if="activeTab === 'common'"
														href="#"
														class="icon-sort"
														@click="sort('RISK_NAME')"
													/><a
														v-if="activeTab === 'fast'"
														href="#"
														class="icon-sort"
														@click="sortFast('RISK_NAME')"
													/>
												</th>
												<th>
													{{ $t('pro.etfType') }}<a
														v-if="activeTab === 'common'"
														href="#"
														class="icon-sort"
														@click="sort('PROTYPE_NAME')"
													/><a
														v-if="activeTab === 'fast'"
														href="#"
														class="icon-sort"
														@click="sortFast('PROTYPE_NAME')"
													/>
												</th>
												<th>
													{{ $t('pro.currency') }}<a
														v-if="activeTab === 'common'"
														href="#"
														class="icon-sort"
														@click="sort('CUR_CODE')"
													/><a
														v-if="activeTab === 'fast'"
														href="#"
														class="icon-sort"
														@click="sortFast('CUR_CODE')"
													/>
												</th>
												<th>
													{{ $t('pro.referenceMarketValue') }}<a
														v-if="activeTab === 'common'"
														href="#"
														class="icon-sort"
														@click="sort('A_PRICE')"
													/><a
														v-if="activeTab === 'fast'"
														href="#"
														class="icon-sort"
														@click="sortFast('A_PRICE')"
													/>
												</th>
												<th>
													{{ $t('pro.marketValueDate') }}<a
														v-if="activeTab === 'common'"
														href="#"
														class="icon-sort"
														@click="sort('PRICE_DT')"
													/><a
														v-if="activeTab === 'fast'"
														href="#"
														class="icon-sort"
														@click="sortFast('PRICE_DT')"
													/>
												</th>
												<th>{{ $t('pro.sellable') }}</th>
												<th>{{ $t('pro.action') }}</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="(item, index) in pageData.content">
												<td :data-th="$t('pro.addToComparison')" class="text-start text-center">
													<input
														:id="'id-' + item.bankProCode"
														class="form-check-input text-center"
														type="checkbox"
														@click="checkoutPro(item)"
													>
													<label class="form-check-label" :for="'id-' + item.bankProCode" />
												</td>
												<td :data-th="$t('pro.productCode')">
													<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
												</td>
												<td class="text-start" :data-th="$t('pro.productChineseName')">
													<span>
														<a class="tx-link" @click="etfModalHandler(item.proCode, item.pfcatCode)">{{
															$filters.defaultValue(item.proName, '--')
														}}</a>
													</span>
												</td>
												<td :data-th="$t('pro.riskLevel')">
													<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
												</td>
												<td data-th="ETF Type">
													<span>{{ $filters.defaultValue(item.proTypeName, '--') }}</span>
												</td>
												<td class="text-center" :data-th="$t('pro.currency')">
													<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
												</td>
												<td class="text-center" :data-th="$t('pro.referenceMarketValue')">
													<span>{{ $filters.formatNumber(item.aprice, '0,0' || '--') }}</span>
												</td>
												<td class="text-center" :data-th="$t('pro.marketValueDate')">
													<span>{{ $filters.formatDate(item.priceDt, '--') }}</span>
												</td>
												<td class="text-center" :data-th="$t('pro.sellable')">
													<span>{{ $filters.defaultValue(item.buyYnName, '--') }}</span>
												</td>
												<td class="text-center" :data-th="$t('pro.action')">
													<button
														v-if="activeTab === 'fast' && fastCode === '06'"
														type="button"
														class="btn btn-primary"
														:title="$t('pro.removeFavorite')"
														@click="remove(item.proCode)"
													>
														{{ $t('pro.removeFavorite') }}
													</button>
													<button
														v-else
														type="button"
														class="btn btn-dark btn-icon"
														data-bs-toggle="tooltip"
														:title="$t('pro.addToFavorite')"
														@click="favoritesHandler(item.proCode, item.pfcatCode)"
													>
														<i class="bi bi-heart text-danger" />
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
						<div id="pie_chart2" role="tabpanel" class="tab-pane">
							<div class="card card-table">
								<div class="card-header">
									<h4>{{ $t('pro.searchResult') }}</h4>
									<div style="display: flex">
										<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
										<button
											type="button"
											class="btn btn-info ms-2"
											@click="performancesCompareModelHandler()"
										>
											{{ $t('pro.performanceComparisonChart') }}
										</button>
									</div>
								</div>
								<div class="table-responsive">
									<table class="table table-bordered table-blue">
										<thead>
											<tr>
												<th rowspan="2">
													{{ $t('pro.addToComparison') }}
												</th>
												<!--<th rowspan="2" >序號</th>-->
												<th rowspan="2">
													{{ $t('pro.productCode') }}
												</th>
												<th rowspan="2">
													{{ $t('pro.productChineseName') }}
												</th>
												<th id="trHead1" colspan="7">
													{{ $t('pro.originalCurrencyReturn') }}
												</th>
												<th id="trHead2" colspan="7" style="display: none">
													{{ $t('pro.taiwanCurrencyReturn') }}
												</th>
												<th rowspan="2">
													{{ $t('pro.action') }}
												</th>
											</tr>
											<tr>
												<th>{{ $t('pro.oneDay') }}</th>
												<th>{{ $t('pro.oneMonth') }}</th>
												<th>{{ $t('pro.threeMonths') }}</th>
												<th>{{ $t('pro.sixMonths') }}</th>
												<th>{{ $t('pro.oneYear') }}</th>
												<th>{{ $t('pro.threeYears') }}</th>
												<th>{{ $t('pro.yearToDate') }}</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="item in pageData.content">
												<td :data-th="$t('pro.addToComparison')" class="text-start text-center">
													<input
														:id="'id-' + item.bankProCode"
														class="form-check-input text-center"
														type="checkbox"
														@click="checkoutPro(item)"
													>
													<label class="form-check-label" :for="'id-' + item.bankProCode" />
												</td>
												<td class="text-center" :data-th="$t('pro.productCode')">
													<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
												</td>
												<td class="text-centertext-start" :data-th="$t('pro.productChineseName')">
													<span>
														<a class="tx-link" @click="etfModalHandler(item.proCode, item.pfcatCode)">{{
															$filters.defaultValue(item.proName, '--')
														}}</a>
													</span>
												</td>
												<td class="text-center" :data-th="$t('pro.originalCurrencyReturn')">
													{{ $filters.formatNumber(item.returnFc, '0,0.00' || '--') }}
												</td>
												<td class="text-center" :data-th="$t('pro.originalCurrencyReturn')">
													{{ $filters.formatNumber(item.fc1mReturn, '0,0.00' || '--') }}
												</td>
												<td class="text-center" :data-th="$t('pro.originalCurrencyReturn')">
													{{ $filters.formatNumber(item.fc3mReturn, '0,0.00' || '--') }}
												</td>
												<td class="text-center" :data-th="$t('pro.originalCurrencyReturn')">
													{{ $filters.formatNumber(item.fc6mReturn, '0,0.00' || '--') }}
												</td>
												<td class="text-center" :data-th="$t('pro.originalCurrencyReturn')">
													{{ $filters.formatNumber(item.fc1yReturn, '0,0.00' || '--') }}
												</td>
												<td class="text-center" :data-th="$t('pro.originalCurrencyReturn')">
													{{ $filters.formatNumber(item.fc3yReturn, '0,0.00' || '--') }}
												</td>
												<td class="text-center" :data-th="$t('pro.originalCurrencyReturn')">
													{{ $filters.formatNumber(item.fcYtdReturn, '0,0.00' || '--') }}
												</td>
												<td class="text-center" :data-th="$t('pro.action')">
													<button
														v-if="activeTab === 'fast' && fastCode === '06'"
														type="button"
														class="btn btn-primary"
														:title="$t('pro.removeFavorite')"
														@click="remove(item.proCode)"
													>
														{{ $t('pro.removeFavorite') }}
													</button>
													<button
														v-else
														type="button"
														class="btn btn-dark btn-icon"
														data-bs-toggle="tooltip"
														:title="$t('pro.addToFavorite')"
														@click="favoritesHandler(item.proCode, item.pfcatCode)"
													>
														<i class="bi bi-heart text-danger" />
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>

						<div class="tx-note">
							<ol>
								<li><span>{{ $t('pro.dataDate') }}：</span></li>
								<li><span>{{ $t('pro.tradingSystemPrimary') }}</span></li>
							</ol>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';

import pagination from '@/views/components/pagination.vue';
import vueGroupIssuerModal from './groupIssuerModal.vue';
import vueGroupProExchangeModal from './groupProExchangeModal.vue';
import vueModal from '@/views/components/model.vue';
import performancesCompareModal from './performancesCompareModal.vue';
import { getImgURL } from '@/utils/imgURL.js';

export default {
	components: {
		vueGroupIssuerModal,
		vueGroupProExchangeModal,
		'vue-pagination': pagination,
		vueModal,
		'vue-performances-compare-modal': performancesCompareModal
	},
	props: {
		etfModalHandler: Function,
		favoritesHandler: Function,
		curOption: Array,
		riskMenu: Array
	},
	data: function () {
		return {
			sdRangeMin: '',
			sdRangeMax: '',
			timeRangeMenu: [],
			rowNumerMenu: [],

			activeTab: 'common',
			bankProCode: null, // 商品代號
			proName: null, // 商品名稱
			curObjs: [], // 計價幣別 陣列物件
			riskCodes: [], // 風險等級
			profInvestorYn: '', // 限PI銷售
			proTypeCode: '', // ETF類型
			isinCode: null,
			sdVaule: '', // 標準差
			etfSizeMin: null, // ETF規格-起
			etfSizeMax: null, // ETF規格-迄
			perfTime: '', // 標的績效
			perfMin: null, // 標的績效-報酬率-起
			perfMax: null, // 標的績效-報酬率-迄
			fastCode: '03', // 快速篩選
			timeRange: null, // 快速 顯示區間
			rowNumber: null, // 快速 顯示資料筆數
			perf: 'PCTYTD', // 標的績效
			profInvestorMenu: [], // 限PI銷售選項
			proTypeMenu: [], // ETF類型選項
			sdMenu: [], // 標準差選項
			perfMenu: [], // 標的績效選項
			etfFastMenu: [], // 快速查詢選單

			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: ['PRO_CODE'],
				direction: 'ASC'
			},
			proCodes: [], // 移除我的最愛按鈕、執行績效比較圖
			checkboxs: [], // 加入比較選項
			lipperIds: [], // 選擇商品做績效比較圖
			issuerItem: [], // 發行機構
			proExchangeItem: [], // 交易所

			isOpenIssuerModal: false,
			isOpenExchangeModal: false,
			isOpenCompareModal: false
		};
	},
	watch: {
		activeTab(newVal, oldVal) {
			const self = this;
			self.fastCode = '03';
			self.fastChange(self.fastCode);
		},
		curObjs(newVal, oldVal) {
			const self = this;
			if (newVal[0] !== oldVal[0]) {
				if (newVal[0] === '') {
					// $('#curMenuEtf').selectpicker('selectAll');
				}
				else if (oldVal[0] === '' && newVal[0] !== '') {
					// $('#curMenuEtf').selectpicker('deselectAll');
				}
			}
		}
	},
	mounted: function () {
		const self = this;
		// $('#curMenuEtf').selectpicker('refresh');
		$('.sdInput').hide();
		$('.perfInput').hide();
		self.getProfInvestorMenu(); // 限PI銷售選項
		self.getProTypeMenu(); // 取得ETF類型選項
		self.getSdMenu();
		self.getetfFastMenu();
		self.getTimeRangeMenu(); // 取得顯示區間選項
		self.getPerfMenu(); // 取得標的績效
		self.getRowNumerMenu(); // 取得顯示資料筆數選項
	},
	methods: {
		getImgURL,
		// 條件Tab切換
		changeTab: function (tabName) {
			const self = this;
			self.activeTab = tabName;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
		},
		// 取得限PI銷售選項
		getProfInvestorMenu: async function () {
			const self = this;
			self.profInvestorMenu = [{ codeValue: '', codeName: this.$t('pro.unlimited') }];
			let selectYnList = [];
			const ret = await this.$api.getAdmCodeDetail({
				codeType: 'SELECT_YN'
			});
			selectYnList = ret.data;
			self.profInvestorMenu.push(...selectYnList);
		},
		// ETF類型選單
		getProTypeMenu: async function () {
			const self = this;
			const r = await this.$api.getProTypeListApi({
				pfcatCode: 'ETF'
			});
			self.proTypeMenu = r.data;
		},
		getSdMenu: async function () {
			const self = this;
			const ret = await this.$api.getSdMenu();
		},
		sdMenuChangeHandler() {
			const self = this;
			if (self.sdVaule == 4) {
				$('.sdInput').show();
			}
			else {
				$('.sdInput').hide();
			}
		},
		perfTimeChangeHandler() {
			const self = this;
			if (self.perfTime !== '') {
				$('.perfInput').show();
			}
			else {
				$('.perfInput').hide();
			}
		},
		// 績效排行
		getPerfMenu: async function () {
			const self = this;
			const ret = await this.$api.getPerfMenuApi();
			self.perfMenu = ret.data;
		},
		// 取得顯示區間
		getTimeRangeMenu: async function () {
			const self = this;
			const ret = await this.$api.getTimeRangeMenuApi();
			self.timeRangeMenu = ret.data;
		},
		// 取得顯示資料筆數
		getRowNumerMenu: async function () {
			const self = this;
			const ret = await this.$api.getRowNumerMenuApi();
			self.rowNumerMenu = ret.data;
		},
		// 取得快速篩選條件選項
		getetfFastMenu: async function () {
			const self = this;
			const ret = await this.$api.getEtfFastMenuApi();
			self.etfFastMenu = ret.data;
		},
		fastChange(fastCode) {
			// 快速查詢切換
			const self = this;
			self.timeRange = {}; // 快速 顯示區間
			self.rowNumber = ''; // 快速 顯示資料筆數
			self.perf = '';
			self.pageData = {
				// 清空查詢資料
				content: []
			};
			if (fastCode === '05') {
				// 超人氣
				$('#proPerfTimeTrEtf').hide();
				$('#rangeFixedTrEtf').show();
				$('#maxRowIdTrEtf').show();
				self.timeRange = self.timeRangeMenu[0];
			}
			else if (fastCode === '07') {
				// 績效排行
				$('#rangeFixedTrEtf').hide();
				$('#maxRowIdTrEtf').show();
				$('#proPerfTimeTrEtf').show();
				self.perf = 'PCTYTD';
			}
			else if (fastCode === '08') {
				// 最高配息率商品
				$('#maxRowIdTrEtf').show();
				$('#rangeFixedTrEtf').hide();
				$('#proPerfTimeTrEtf').hide();
			}
			else {
				$('#maxRowIdTrEtf').hide();
				$('#rangeFixedTrEtf').hide();
				$('#proPerfTimeTrEtf').hide();
			}
		},
		// 由查詢結果標題觸發
		sort: function (columnName) {
			if (this.pageable.sort !== columnName) {
				this.pageable.sort = columnName;
				this.pageable.direction = 'DESC';
			}
			else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}

			this.gotoPage(0);
		},
		// 由快速查詢結果標題觸發
		sortFast: function (columnName) {
			if (this.pageable.sort !== columnName) {
				this.pageable.sort = columnName;
				this.pageable.direction = 'DESC';
			}
			else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}

			this.gotoFastPage(0, columnName);
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			const self = this;
			let url = '';

			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			const sdItem = _.find(self.sdMenu, { termValue: self.sdVaule }); // 找出對應項目
			let max = null;
			let min = null;
			let isOther = false;
			if (!_.isNil(sdItem)) {
				if (sdItem.termValue == 4) {
					isOther = true;
					max = self.sdRangeMax;
					min = self.sdRangeMin;
				}
				else {
					isOther = false;
					max = sdItem.rangeMax;
					min = sdItem.rangeMin;
				}
			}

			const issuerCodes = [];
			self.issuerItem.forEach(function (item) {
				issuerCodes.push(item.issuerCode);
			});

			const exchangeCodes = [];
			self.proExchangeItem.forEach(function (item) {
				exchangeCodes.push(item.exchangeCode);
			});
			const payload = {
				bankProCode: self.bankProCode, // 商品代號
				proName: self.proName, // 商品名稱
				curCodes: self.curObjs, // 計價幣別
				riskCodes: self.riskCodes, // 風險等級
				profInvestorYn: self.profInvestorYn, // 限PI銷售
				protypeCode: self.proTypeCode, // ETF類型
				isinCode: self.isinCode,
				sdRangeMin: min, // 標準差最小值
				sdRangeMax: max, // 標準差最大值
				isOtherSdRange: isOther, // 自訂標準差
				etfSizeMin: self.etfSizeMin, // ETF規格-起
				etfSizeMax: self.etfSizeMax, // ETF規格-迄
				perfTimeCodeValue: self.perfTime, // 標的績效
				perfMin: self.perfMin, // 標的績效-報酬率-起
				perfMax: self.perfMax, // 標的績效-報酬率-起
				issuerExchangeCodes: issuerCodes, // 發行機構
				exchangeCodes: exchangeCodes // 交易所
			};
			const ret = await this.$api.getEtfProductsApi(payload, url);

			self.pageData = ret.data;
			self.pageData.content.forEach((data) => {
				self.checkboxs.push(false);
			});
		},
		gotoFastPage: function (page, sortColumnName) {
			this.pageable.page = page;
			this.getFastPageData(page, sortColumnName);
		},
		getFastPageData: async function (page, sortColumnName) {
			const self = this;

			if (sortColumnName == null) {
				// 有指定的排序欄位則以排序欄位排序
				// 當「篩選條件」='聚焦商品'、'超人氣商品'、'我的最愛'：以商品代號排序。
				if (self.fastCode === '03' || self.fastCode === '06' || self.fastCode === '09') {
					self.pageable.sort = 'BANK_PRO_CODE';
				}
				else if (self.fastCode === '08') {
					// 當「篩選條件」='最高配息率商品'：以配息率排序。
					self.pageable.sort = 'INT_RATE';
				}
				else if (self.fastCode === '08') {
					// 當「篩選條件」='績效排行'：以所選擇的標的績效排序。
					self.pageable.sort = 'FC_RETURN';
				}
			}

			let url = '';
			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;
			const payload = {
				filterCodeValue: self.fastCode,
				timeRangeType: self.timeRange.rangeType, // 顯示區間類型
				timeRangeFixed: self.timeRange.rangeFixed, // 顯示區間數值
				rowNumberFixed: self.rowNumber,
				perfTimeCode: self.perf
			};
			const ret = await this.$api.getEtfFastPageDataApi(payload, url);
			self.pageData = ret.data;
			self.pageData.content.forEach((data) => {
				self.checkboxs.push(false);
			});
		},
		// 加入比較checkbox選項
		checkoutPro: function (item) {
			const self = this;
			if (!self.proCodes.includes(item.proCode)) {
				self.proCodes.push(item.proCode);
			}
			else {
				_.remove(self.proCodes, code => code === item.proCode);
			}
			if (!self.lipperIds.includes(item.lipperId)) {
				self.lipperIds.push(item.lipperId);
			}
			else {
				_.remove(self.lipperIds, code => code === item.lipperId);
			}
		},
		// 執行績效比較圖
		performancesCompareModelHandler: function () {
			const self = this;
			if (self.proCodes.length > 0) {
				if (self.proCodes.length > 6) {
					this.$bi.alert(this.$t('pro.maxSixItems'));
				}
				else {
					self.$refs.performancesCompareModalRef.compareEtfPropItem(self.proCodes, self.lipperIds);
					this.isOpenCompareModal = true;
				}
			}
			else {
				this.$bi.alert(this.$t('pro.selectAtLeastOne'));
			}
		},
		// 刪除我的最愛
		remove: async function (proCode) {
			const self = this;
			const ret = await this.$api.deleteFavoriteApi({ proCode: proCode });
			this.$bi.alert(this.$t('pro.deleteSuccess'));
			self.checkboxs = [];
			self.proCodes = [];
			self.gotoFastPage(0);
		},
		groupIssuerModalHandler: function () {
			// 顯示發行機構 model
			const self = this;
			this.$refs.groupIssuerModalRef.issuerPropItem(self.issuerItem);
			this.isOpenIssuerModal = true;
		},
		selectedIssuer(issuerItem) {
			// 顯示發行機構選擇項目
			const self = this;
			this.isOpenIssuerModal = false;
			self.issuerItem = issuerItem; // 取得發行機構資料
		},
		deleteIssuerItem(issuerCode) {
			const self = this;
			_.remove(self.issuerItem, item => item.issuerCode === issuerCode); // 移除刪除項目
		},
		groupProExchangeModalHandler: function () {
			// 顯示交易所 model
			const self = this;
			this.$refs.groupProExchangeModalRef.proExchangePropItem(self.proExchangeItem);
			this.isOpenExchangeModal = true;
		},
		selectedProExchange(proExchangeItem) {
			// 顯示交易所選擇項目
			const self = this;
			this.isOpenExchangeModal = false;
			self.proExchangeItem = proExchangeItem; // 取得交易所資料
		},
		deleteProExchangeItem(proExchangeCode) {
			//
			const self = this;
			const item = _.find(self.proExchangeItem, {
				exchangeCode: proExchangeCode
			}); // 找出對應項目
			const index = self.proExchangeItem.indexOf(item);
			if (index != -1) {
				self.proExchangeItem.splice(index, 1); // 移除刪除項目
			}
		}
	} // methods end
};
</script>
