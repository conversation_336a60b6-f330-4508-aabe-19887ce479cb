<template>
	<div>
		<p class="font-md bold m-t-15">
			{{ $t('pro.fundIndicators') }}
			<select v-model="selectedTech" class="select">
				<option :value="0">
					{{ $t('pro.annualizedStandardDeviation') }}
				</option>
				<option :value="1">
					{{ $t('pro.annualizedSharpeRatio') }}
				</option>
				<option :value="2">
					{{ $t('pro.annualizedAlpha') }}
				</option>
				<option :value="3">
					{{ $t('pro.beta') }}
				</option>
				<option :value="4">
					{{ $t('pro.annualizedInformationRatio') }}
				</option>
			</select>
		</p>
		<div class="row">
			<div class="col-md-12 col-lg-6">
				<!-- 長條圖 -->
				<vue-fund-column-chart
					:chart-id="techsOtherChartId"
					:prop-chart-data="techsOtherChartData"
					:prop-selected-tech="techsOtherMenu[selectedTech]"
				/>
				<div class="text-center">
					{{ $t('pro.indexCalculationRuleNote') }}
				</div>
			</div>
			<div class="col-md-12 col-lg-6">
				<div class="table-responsive">
					<table width="100%" class="table table-condensed text-right">
						<thead>
							<tr>
								<th width="30%">
&nbsp;
								</th>
								<th>{{ $filters.defaultValue(fundInfo && fundInfo.fundEnName, '--') }}</th>
								<th>{{ $filters.defaultValue(bmName, '--') }}</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td class="text-center">
									{{ $t('pro.oneYear') }}
								</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getTech(list[selectedTech][0].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									/>
								</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getBmTech(list[selectedTech][0].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									/>
								</td>
							</tr>
							<tr>
								<td class="text-center">
									{{ $t('pro.threeYears') }}
								</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getTech(list[selectedTech][1].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									/>
								</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getBmTech(list[selectedTech][1].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									/>
								</td>
							</tr>
							<tr>
								<td class="text-center">
									{{ $t('pro.fiveYears') }}
								</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getTech(list[selectedTech][2].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									/>
								</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getBmTech(list[selectedTech][2].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									/>
								</td>
							</tr>
							<tr>
								<td class="text-center">
									{{ $t('pro.tenYears') }}
								</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getTech(list[selectedTech][3].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									/>
								</td>
								<td class="text-right">
									<p
										v-html="
											$filters.defaultValue(
												$filters.formatFlucWithView(
													getBmTech(list[selectedTech][3].statCode),
													_.includes([0, 2], selectedTech) ? '%' : ''
												),
												'--'
											)
										"
									/>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
export default {
	props: {
		fundInfo: Object,
		techs: Array,
		bmTechs: Array,
		bmName: String
	},
	data: function () {
		return {
			selectedTech: 0,
			list: [
				[
					{
						name: this.$t('pro.oneYear'),
						statCode: 'ASTD1Y'
					},
					{
						name: this.$t('pro.threeYears'),
						statCode: 'ASTD3Y'
					},
					{
						name: this.$t('pro.fiveYears'),
						statCode: 'ASTD5Y'
					},
					{
						name: this.$t('pro.tenYears'),
						statCode: 'ASTD10Y'
					}
				],
				[
					{
						name: this.$t('pro.oneYear'),
						statCode: 'ASHP1Y'
					},
					{
						name: this.$t('pro.threeYears'),
						statCode: 'ASHP3Y'
					},
					{
						name: this.$t('pro.fiveYears'),
						statCode: 'ASHP5Y'
					},
					{
						name: this.$t('pro.tenYears'),
						statCode: 'ASHP10Y'
					}
				],
				[
					{
						name: this.$t('pro.oneYear'),
						statCode: 'AALP1Y'
					},
					{
						name: this.$t('pro.threeYears'),
						statCode: 'AALP3Y'
					},
					{
						name: this.$t('pro.fiveYears'),
						statCode: 'AALP5Y'
					},
					{
						name: this.$t('pro.tenYears'),
						statCode: 'AALP10Y'
					}
				],
				[
					{
						name: this.$t('pro.oneYear'),
						statCode: 'BET1Y'
					},
					{
						name: this.$t('pro.threeYears'),
						statCode: 'BET3Y'
					},
					{
						name: this.$t('pro.fiveYears'),
						statCode: 'BET5Y'
					},
					{
						name: this.$t('pro.tenYears'),
						statCode: 'BET10Y'
					}
				],
				[
					{
						name: this.$t('pro.oneYear'),
						statCode: 'AINF1Y'
					},
					{
						name: this.$t('pro.threeYears'),
						statCode: 'AINF3Y'
					},
					{
						name: this.$t('pro.fiveYears'),
						statCode: 'AINF5Y'
					},
					{
						name: this.$t('pro.tenYears'),
						statCode: 'AINF10Y'
					}
				]
			],
			techsOtherChartId: 'techsOtherChartId'
		};
	},
	computed: {
		techsOtherMenu: function () {
			const self = this;
			return [
				{
					name: self.$t('pro.annualizedStandardDeviation'),
					valueList: [
						{ name: self.$t('pro.fund'), value: 'astd' },
						{ name: self.$t('pro.correspondingIndex') + self.bmName, value: 'astdBm' }
					],
					tooltipText: '{categoryX}:{valueY}%'
				},
				{
					name: self.$t('pro.annualizedSharpeRatio'),
					valueList: [
						{ name: self.$t('pro.fund'), value: 'ashp' },
						{ name: self.$t('pro.correspondingIndex') + self.bmName, value: 'ashpBm' }
					],
					tooltipText: '{categoryX}:{valueY}'
				},
				{
					name: self.$t('pro.annualizedAlpha'),
					valueList: [
						{ name: self.$t('pro.fund'), value: 'aalp' },
						{ name: self.$t('pro.correspondingIndex') + self.bmName, value: 'aalpBm' }
					],
					tooltipText: '{categoryX}:{valueY}%'
				},
				{
					name: self.$t('pro.beta'),
					valueList: [
						{ name: self.$t('pro.fund'), value: 'bet' },
						{ name: self.$t('pro.correspondingIndex') + self.bmName, value: 'betBm' }
					],
					tooltipText: '{categoryX}:{valueY}'
				},
				{
					name: self.$t('pro.annualizedInformationRatio'),
					valueList: [
						{ name: self.$t('pro.fund'), value: 'ainf' },
						{ name: self.$t('pro.correspondingIndex') + self.bmName, value: 'ainfBm' }
					],
					tooltipText: '{categoryX}:{valueY}'
				}
			];
		},
		techsOtherChartData: function () {
			return [
				{
					year: this.$t('pro.oneYear'),
					astd: this.getTech('ASTD1Y'),
					astdBm: this.getBmTech('ASTD1Y'),
					ashp: this.getTech('ASHP1Y'),
					ashpBm: this.getBmTech('ASHP1Y'),
					aalp: this.getTech('AALP1Y'),
					bet: this.getTech('BET1Y'),
					betBm: this.getBmTech('BET1Y'),
					ainf: this.getTech('AINF1Y'),
					aalpBm: this.getBmTech('AINF1Y')
				},
				{
					year: this.$t('pro.threeYears'),
					astd: this.getTech('ASTD3Y'),
					astdBm: this.getBmTech('ASTD3Y'),
					ashp: this.getTech('ASHP3Y'),
					ashpBm: this.getBmTech('ASHP3Y'),
					aalp: this.getTech('AALP3Y'),
					bet: this.getTech('BET3Y'),
					betBm: this.getBmTech('BET3Y'),
					ainf: this.getTech('AINF3Y'),
					aalpBm: this.getBmTech('AINF3Y')
				},
				{
					year: this.$t('pro.fiveYears'),
					astd: this.getTech('ASTD5Y'),
					astdBm: this.getBmTech('ASTD5Y'),
					ashp: this.getTech('ASHP5Y'),
					ashpBm: this.getBmTech('ASHP5Y'),
					aalp: this.getTech('AALP5Y'),
					bet: this.getTech('BET5Y'),
					betBm: this.getBmTech('BET5Y'),
					ainf: this.getTech('AINF5Y'),
					aalpBm: this.getBmTech('AINF5Y')
				},
				{
					year: this.$t('pro.tenYears'),
					astd: this.getTech('ASTD10Y'),
					astdBm: this.getBmTech('ASTD10Y'),
					ashp: this.getTech('ASHP10Y'),
					ashpBm: this.getBmTech('ASHP10Y'),
					aalp: this.getTech('AALP10Y'),
					bet: this.getTech('BET10Y'),
					betBm: this.getBmTech('BET10Y'),
					ainf: this.getTech('AINF10Y'),
					aalpBm: this.getBmTech('AINF10Y')
				}
			];
		}
	},
	watch: {},
	methods: {
		getTech: function (statCode) {
			const tech = _.find(this.techs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		},
		getBmTech: function (statCode) {
			const tech = _.find(this.bmTechs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		}
	}
};
</script>
