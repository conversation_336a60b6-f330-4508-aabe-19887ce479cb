<template>
	<div>
		<p class="font-md bold m-t-15">
			{{ $t('pro.annualizedCumulativeReturn') }}<select v-model="selectedCurrencyCode" class="select">
				<option value="0">
					{{ $t('pro.twd') }}
				</option>
				<option value="1">
					{{ $t('pro.usd') }}
				</option>
			</select>
		</p>
		<div class="row">
			<div class="col-md-12 col-lg-6">
				<vue-fund-column-chart
					:chart-id="techsAcrChartId"
					:prop-chart-data="techsAcrChartData"
					:prop-selected-tech="techsAcrMenu[selectedCurrencyCode]"
				/>
			</div>
			<div class="col-md-12 col-lg-6">
				<div class="table-responsive">
					<table width="100%" class="table table-condensed text-right">
						<thead>
							<tr>
								<th width="30%">
&nbsp;
								</th>
								<th>{{ $filters.defaultValue(fundInfo && fundInfo.fundEnName, '--') }}</th>
								<th>{{ $filters.defaultValue(bmName, '--') }}</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td class="text-center">
									{{ $t('pro.threeMonths') }}
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getTech(list[0].statCode), '%'), '--')" />
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getBmTech(list[0].statCode), '%'), '--')" />
								</td>
							</tr>
							<tr>
								<td class="text-center">
									{{ $t('pro.sixMonths') }}
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getTech(list[1].statCode), '%'), '--')" />
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getBmTech(list[1].statCode), '%'), '--')" />
								</td>
							</tr>
							<tr>
								<td class="text-center">
									{{ $t('pro.oneYear') }}
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getTech(list[2].statCode), '%'), '--')" />
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getBmTech(list[2].statCode), '%'), '--')" />
								</td>
							</tr>
							<tr>
								<td class="text-center">
									{{ $t('pro.twoYears') }}
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getTech(list[3].statCode), '%'), '--')" />
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getBmTech(list[3].statCode), '%'), '--')" />
								</td>
							</tr>
							<tr>
								<td class="text-center">
									{{ $t('pro.threeYears') }}
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getTech(list[4].statCode), '%'), '--')" />
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getBmTech(list[4].statCode), '%'), '--')" />
								</td>
							</tr>
							<tr>
								<td class="text-center">
									{{ $t('pro.fiveYears') }}
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getTech(list[5].statCode), '%'), '--')" />
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getBmTech(list[5].statCode), '%'), '--')" />
								</td>
							</tr>
							<tr>
								<td class="text-center">
									{{ $t('pro.tenYears') }}
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getTech(list[6].statCode), '%'), '--')" />
								</td>
								<td class="text-right">
									<p v-html="$filters.defaultValue($filters.formatFlucWithView(getBmTech(list[6].statCode), '%'), '--')" />
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
export default {
	props: {
		fundInfo: Object,
		twdTechs: Array,
		twdBmTechs: Array,
		bmName: String
	},
	data: function () {
		return {
			selectedCurrencyCode: '0',
			techsAcrChartId: 'techsAcrChartId',
			list: [
				{
					name: this.$t('pro.threeMonths'),
					statCode: 'ACR3M'
				},
				{
					name: this.$t('pro.sixMonths'),
					statCode: 'ACR6M'
				},
				{
					name: this.$t('pro.oneYear'),
					statCode: 'ACR1Y'
				},
				{
					name: this.$t('pro.twoYears'),
					statCode: 'ACR2Y'
				},
				{
					name: this.$t('pro.threeYears'),
					statCode: 'ACR3Y'
				},
				{
					name: this.$t('pro.fiveYears'),
					statCode: 'ACR5Y'
				},
				{
					name: this.$t('pro.tenYears'),
					statCode: 'ACR10Y'
				}
			],
			usdTechs: [],
			usdBmTechs: []
		};
	},
	computed: {
		techsAcrMenu: function () {
			const self = this;
			return [
				{
					name: this.$t('pro.annualizedCumulativeReturn'),
					valueList: [
						{ name: this.$t('pro.fund'), value: 'twdAcr' },
						{ name: this.$t('pro.correspondingIndex') + self.bmName, value: 'twdAcrBm' }
					],
					tooltipText: '{categoryX}:{valueY}%'
				},
				{
					name: this.$t('pro.annualizedCumulativeReturn'),
					valueList: [
						{ name: this.$t('pro.fund'), value: 'usdAcr' },
						{ name: this.$t('pro.correspondingIndex') + self.bmName, value: 'usdAcrBm' }
					],
					tooltipText: '{categoryX}:{valueY}%'
				}
			];
		},
		techsAcrChartData: function () {
			return [
				{
					year: this.$t('pro.threeMonths'),
					twdAcr: this.getTwdTech('ACR3M'),
					twdAcrBm: this.getTwdBmTech('ACR3M'),
					usdAcr: this.getUsdTech('ACR3M'),
					usdAcrBm: this.getUsdBmTech('ACR3M')
				},
				{
					year: this.$t('pro.sixMonths'),
					twdAcr: this.getTwdTech('ACR6M'),
					twdAcrBm: this.getTwdBmTech('ACR6M'),
					usdAcr: this.getUsdTech('ACR6M'),
					usdAcrBm: this.getUsdBmTech('ACR6M')
				},
				{
					year: this.$t('pro.oneYear'),
					twdAcr: this.getTwdTech('ACR1Y'),
					twdAcrBm: this.getTwdBmTech('ACR1Y'),
					usdAcr: this.getUsdTech('ACR1Y'),
					usdAcrBm: this.getUsdBmTech('ACR1Y')
				},
				{
					year: this.$t('pro.twoYears'),
					twdAcr: this.getTwdTech('ACR2Y'),
					twdAcrBm: this.getTwdBmTech('ACR2Y'),
					usdAcr: this.getUsdTech('ACR2Y'),
					usdAcrBm: this.getUsdBmTech('ACR2Y')
				},
				{
					year: this.$t('pro.threeYears'),
					twdAcr: this.getTwdTech('ACR3Y'),
					twdAcrBm: this.getTwdBmTech('ACR3Y'),
					usdAcr: this.getUsdTech('ACR3Y'),
					usdAcrBm: this.getUsdBmTech('ACR3Y')
				},
				{
					year: this.$t('pro.fiveYears'),
					twdAcr: this.getTwdTech('ACR5Y'),
					twdAcrBm: this.getTwdBmTech('ACR5Y'),
					usdAcr: this.getUsdTech('ACR5Y'),
					usdAcrBm: this.getUsdBmTech('ACR5Y')
				},
				{
					year: this.$t('pro.tenYears'),
					twdAcr: this.getTwdTech('ACR10Y'),
					twdAcrBm: this.getTwdBmTech('ACR10Y'),
					usdAcr: this.getUsdTech('ACR10Y'),
					usdAcrBm: this.getUsdBmTech('ACR10Y')
				}
			];
		}
	},
	watch: {},
	mounted: function () {
		this.$nextTick(function () {
			this.getUsdTechs();
			this.getUsdBmTechs();
		});
	},
	methods: {
		getUsdTechs: async function () {
			const self = this;
			const ret = await this.$api.getTechsApi({
				proCode: self.fundInfo.fundCode,
				techCurrencyCode: 'USD'
			});
			self.usdTechs = ret.data;
		},
		getUsdBmTechs: async function () {
			const self = this;
			const managerBmCode = self.fundInfo.managerBmCode;
			const analysisBmCode = self.fundInfo.analysisBmCode;
			let bmCode;

			if (managerBmCode && managerBmCode != '11000006' && managerBmCode != '11000000') {
				// self.bmName = self.fundInfo.managerBmName;
				bmCode = managerBmCode;
			}
			else if (analysisBmCode) {
				// self.bmName = self.fundInfo.analysisBmName;
				bmCode = analysisBmCode;
			}
			const ret = await this.$api.getTechsApi({
				proCode: bmCode,
				techCurrencyCode: 'USD'
			});
			self.usdBmTechs = ret.data;
		},
		getTwdTech: function (statCode) {
			const tech = _.find(this.twdTechs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		},
		getTwdBmTech: function (statCode) {
			const tech = _.find(this.twdBmTechs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		},
		getUsdTech: function (statCode) {
			const tech = _.find(this.usdTechs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		},
		getUsdBmTech: function (statCode) {
			const tech = _.find(this.usdBmTechs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		},
		getTech: function (statCode) {
			const self = this;
			if (self.selectedCurrencyCode == '0') {
				var tech = _.find(this.twdBmTechs, { statCode: statCode });
				return tech ? tech.dvalue : null;
			}
			else {
				var tech = _.find(this.usdBmTechs, { statCode: statCode });
				return tech ? tech.dvalue : null;
			}
		}
	}
};
</script>
