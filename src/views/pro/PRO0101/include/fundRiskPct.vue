<template>
	<div class="col-sm-8 col-12">
		<h4>
			{{ $t('pro.riskLevel') }} <small class="small-text">{{ $t('pro.dataUpdateDate') }}： {{ $filters.defaultValue($filters.formatDate(latestDate), '--') }}</small>
		</h4>
		<table width="100%" class="table table-bordered">
			<tbody>
				<tr>
					<th>{{ $t('pro.name') }}</th>
					<td colspan="3">
						{{ pointFund.fundName }}
					</td>
				</tr>
				<tr>
					<th width="20%">
						{{ $t('pro.annualizedCumulativeReturn') }}
					</th>
					<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(pointFund.acr, '%'), '--')" /></td>
					<th width="20%">
						{{ $t('pro.annualizedStandardDeviation') }}
					</th>
					<td><p v-html="$filters.defaultValue($filters.formatFlucWithView(pointFund.astd, '%'), '--')" /></td>
				</tr>
				<tr>
					<th>{{ $t('pro.currency') }}</th>
					<td colspan="3">
						<div class="row">
							<div class="col-4">
								<select v-model="searchData.techCurrencyCode" class="form-select selectPicker w-100" @change="get2x2Perfs">
									<option value="TWD">
										{{ $t('pro.taiwanDollar') }}
									</option>
									<option value="USD">
										{{ $t('pro.usd') }}
									</option>
								</select>
							</div>
							<div class="col-4 p-l-5">
								<select v-model="searchData.dataLength2x2" class="form-select selectPicker w-100" @change="get2x2Perfs">
									<option value="DATA_LENGTH_1Y">
										{{ $t('pro.oneYear') }}
									</option>
									<option value="DATA_LENGTH_3Y">
										{{ $t('pro.threeYears') }}
									</option>
									<option value="DATA_LENGTH_5Y">
										{{ $t('pro.fiveYears') }}
									</option>
								</select>
							</div>
						</div>
					</td>
				</tr>
			</tbody>
		</table>
		<div class="row">
			<div class="col-12 text-center">
				<div v-if="fund2x2Perfs.length === 1" class="text-center">
					{{ $t('pro.fund') }}
				</div>

				<!-- 雷達圖 -->
				<vue-fund-bubble-chart
					v-if="pointFund"
					ref="riskBubbleChartRef"
					:chart-id="riskBubbleChartId"
					:prop-chart-data="chartData"
					:fund-name="pointFund.fundName"
					style="height: 500px"
				/>

				<small>{{ $t('pro.fund') }}</small>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import vueFundBubbleChart from './fundBubbleChart.vue';
export default {
	components: {
		vueFundBubbleChart
	},
	props: {
		fundCode: String,
		globalClassCode: String
	},
	data: function () {
		return {
			searchData: {
				techCurrencyCode: 'TWD',
				dataLength2x2: 'DATA_LENGTH_1Y',
				globalClassCode: null
			},
			fund2x2Perfs: [],
			riskBubbleChartId: 'riskBubbleChartId'
		};
	},
	computed: {
		latestDate: function () {
			if (this.fund2x2Perfs.length > 0) {
				return _.orderBy(
					_.filter(this.fund2x2Perfs, function (item) {
						return item.dataDate != null;
					}),
					['dataDate'],
					['desc']
				)[0].dataDate;
			}
			else {
				return null;
			}
		},
		chartData: function () {
			const self = this;
			if (self.fund2x2Perfs.length > 0) {
				return _.map(self.fund2x2Perfs, function (item) {
					if (item.fundCode === self.fundCode) {
						item.x = item.astd;
						item.y = item.acr;
					}
					return item;
				});
			}
			return null;
		},
		pointFund: function () {
			const fund2x2Perf = _.find(this.fund2x2Perfs, { fundCode: this.fundCode });
			return fund2x2Perf ? fund2x2Perf : {};
		}
	},
	watch: {
		globalClassCode: {
			handler: function (newVal, oldVal) {
				this.searchData.globalClassCode = newVal;
			}
		}
	},
	mounted: function () {},
	methods: {
		get2x2Perfs: async function () {
			const self = this;
			self.searchData.globalClassCode = self.globalClassCode;
			const ret = await get2x2PerfsApi({
				searchData: self.searchData
			});
			self.fund2x2Perfs = ret.data;
			setTimeout(function () {
				self.$refs.riskBubbleChartRef.initChart();
			}, 1000);
		}
	}
};
</script>
