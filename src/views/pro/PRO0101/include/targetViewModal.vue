<template>
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('pro.targetView') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click="close()"
				/>
			</div>
			<div class="modal-body">
				<div class="card card-form mb-3">
					<div class="card-header">
						<h4>{{ $t('pro.linkedTarget') }}</h4>
					</div>

					<table class="table table-RWD table-bordered table-horizontal-RWD">
						<thead>
							<tr>
								<th>{{ $t('pro.linkedTarget') }}<br>{{ $t('pro.initialPrice') }}</th>
								<th>{{ $t('pro.latestClosingPrice') }}</th>
								<th>{{ $t('pro.conversionPrice') }}</th>
								<th>{{ $t('pro.lowerLimitPrice') }}</th>
								<th>{{ $t('pro.earlyExitPrice') }}</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="item in targetDataList">
								<td class="">
									{{ item.isinCode }}<br>{{ item.entryPrice }}
								</td>
								<td class="text-end">
									{{ item.closePrice }}
								</td>
								<td class="text-end">
									{{ item.convPrice }} (70%)
								</td>
								<td class="text-end">
									{{ item.kiPrice }} (55%)
								</td>
								<td class="text-end">
									{{ item.koPrice }} (105%)
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div class="modal-footer">
				<input
					type="button"
					class="btn btn-primary"
					:value="$t('pro.close')"
					@click.prevent="close()"
				>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		proCode: String,
		close: Function
	},
	data: function () {
		return {
			targetDataList: []
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		getTargetDataList: async function (proCode) {
			const self = this;
			const ret = this.$api.getTargetViewDataListApi({
				proCode: proCode
			});
			self.targetDataList = ret.data;
		}
	} // methods end
};
</script>
