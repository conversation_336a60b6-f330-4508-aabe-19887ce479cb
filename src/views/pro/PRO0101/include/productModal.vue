<template>
	<vue-modal :is-open="isOpenModal['fund']" @close="closeModal('fund')">
		<template #content="props">
			<vue-fund-modal
				ref="fundModalRef"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:download-file="downloadFile"
				:download-other-file="downloadOtherFile"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenModal['etf']" @close="closeModal('etf')">
		<template #content="props">
			<vue-etf-modal
				ref="etfModalRef"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:download-file="downloadFile"
				:download-other-file="downloadOtherFile"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenModal['bond']" @close="closeModal('bond')">
		<template #content="props">
			<vue-bond-modal
				ref="bondModalRef"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:download-file="downloadFile"
				:download-other-file="downloadOtherFile"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenModal['sp']" @close="closeModal('sp')">
		<template #content="props">
			<vue-sp-modal
				ref="spModalRef"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:download-file="downloadFile"
				:download-other-file="downloadOtherFile"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenModal['ins']" @close="closeModal('ins')">
		<template #content="props">
			<vue-ins-modal
				ref="insModalRef"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:download-file="downloadFile"
				:download-other-file="downloadOtherFile"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenModal['dci']" @close="closeModal('dci')">
		<template #content="props">
			<vue-dci-modal
				ref="dciModalRef"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:download-file="downloadFile"
				:download-other-file="downloadOtherFile"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenModal['sec']" @close="closeModal('sec')">
		<template #content="props">
			<vue-sec-modal
				ref="secModalRef"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:download-file="downloadFile"
				:download-other-file="downloadOtherFile"
			/>
		</template>
	</vue-modal>
</template>
<script>
import vueModal from '@/views/components/model.vue';
import vueFundModal from './fundModal.vue';
import vueEtfModal from './etfModal.vue';
import vueBondModal from './bondModal.vue';
import vueSpModal from './spModal.vue';
import vueInsModal from './insModal.vue';
import vueDciModal from './dciModal.vue';
import vueSecModal from './secModal.vue';

export default {
	components: {
		vueModal,
		vueBondModal,
		vueFundModal,
		vueDciModal,
		vueEtfModal,
		vueInsModal,
		vueSpModal,
		vueSecModal
	},
	data: function () {
		return {
			isOpenModal: {
				fund: false,
				etf: false,
				bond: false,
				pdf: false,
				sp: false,
				ins: false,
				dci: false,
				sec: false
			}
		};
	},
	watch: {},
	mounted: function () { },
	methods: {
		open: function (proCode, pfcatCode) {
			const self = this;
			switch (pfcatCode) {
				case 'FUND':
					self.fundModalHandler(proCode, pfcatCode);
					break;
				case 'ETF':
					self.etfModalHandler(proCode, pfcatCode);
					break;
				case 'FB':
					self.bondModalHandler(proCode, pfcatCode);
					break;
				case 'PFD':
					self.pfdModalHandler(proCode, pfcatCode);
					break;
				case 'SP':
					self.spModalHandler(proCode, pfcatCode);
					break;
				case 'INS':
					self.insModalHandler(proCode, pfcatCode);
					break;
				case 'DCI':
					self.dciModalHandler(proCode, pfcatCode);
					break;
				case 'SEC':
					self.secModalHandler(proCode, pfcatCode);
					break;
			}
		},
		fundModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.fundModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal.fund = true;
		},
		etfModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.etfModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.etfModalRef.getEtfDetail(proCode); // 商品資訊/基金資料
			self.$refs.etfModalRef.getEtfStockHold(proCode); // 商品資訊/ETF持股
			self.$refs.etfModalRef.getEtfPrice(proCode); // 商品資訊/價格分析資料
			self.$refs.etfModalRef.getPricesChartData(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.etfModalRef.getEtfProfileNameMenu(); // 商品資訊/績效分析
			self.$refs.etfModalRef.getEtfProfileBenchmarksMenu(); // 商品資訊/績效分析
			self.isOpenModal.etf = true;
		},
		bondModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.bondModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.bondModalRef.getBondPriceAna(proCode);
			self.$refs.bondModalRef.getPricesChartData(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.bondModalRef.getBondPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.bond = true;
		},
		pfdModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.pfdModalRef.getProInfo(proCode, pfcatCode);
			//			self.$refs.pfdModalRef.getEtfDetail(proCode); // 商品資訊/基金資料
			self.isOpenModal.pfd = true;
		},
		spModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.spModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.spModalRef.getSpPriceAna(proCode); // 商品資訊/淨值分析資料
			self.$refs.spModalRef.getSpNets(proCode, 'Y', -1.0); // 商品資訊/淨值分析圖表
			self.$refs.spModalRef.getSpPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.sp = true;
		},
		insModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.insModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal.ins = true;
		},
		dciModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.dciModalRef.getProInfo(proCode, pfcatCode); // 商品基本資料
			self.$refs.dciModalRef.getDciPriceAna(proCode); // 商品資訊/價格分析資料
			self.$refs.dciModalRef.getDciNets(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.dciModalRef.getDciPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.dci = true;
		},
		secModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.secModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.secModalRef.getSecPriceAna(proCode); // 商品資訊/淨值分析資料
			self.$refs.secModalRef.getSecNets(proCode, 'Y', -1.0); // 商品資訊/淨值分析圖表
			self.$refs.secModalRef.getSecPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.sec = true;
		}
	} // methods end
};
</script>
