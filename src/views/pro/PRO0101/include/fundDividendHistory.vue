<template>
	<div v-if="dividends && dividends.length > 0" class="col-12">
		<h4>{{ $t('pro.dividendDataChart') }}</h4>
		<!-- <vue-fund-column-line-chart ref-div="dividendColumnLine" div-class="chart-size" :chart-config="chartConfig" :chart-data="chartData"></vue-fund-column-line-chart> -->
		<table width="100%" class="table table-bordered">
			<tr>
				<th>{{ $t('pro.exDividendDate') }}</th>
				<th>{{ $t('pro.paymentDate') }}</th>
				<th>{{ $t('pro.dividendValueOrRatio') }}</th>
				<th>{{ $t('pro.annualizedDividend') }}</th>
				<th>{{ $t('pro.currency') }}</th>
			</tr>
			<tr v-for="(item, i) in _.orderBy(dividends, ['xdDate'], ['desc'])">
				<td>{{ $filters.formatDate(item.xdDate) }}</td>
				<td>{{ $filters.formatDate(item.paymentDate) }}</td>
				<td>{{ $filters.defaultValue($filters.formatNum(item.paymentLc, '0,0.[00]'), '--') }}</td>
				<td style="color: #e7505a">
					{{ $filters.defaultValue($filters.formatNum(item.annDivLc, '0,0.[00]'), '--') }}
				</td>
				<td>{{ localCurrencyName }}</td>
			</tr>
		</table>
	</div>
</template>
<script>
import _ from 'lodash';
export default {
	props: {
		fundCode: String,
		localCurrencyName: String
	},
	data: function () {
		return {
			chartConfig: {
				axisName: [this.$t('pro.amt'), this.$t('pro.dividendRate')],
				series: [
					{
						seriesValue: 'y1',
						seriesName: this.$t('pro.amt'),
						seriesType: 'line',
						tooltipText: this.$t('pro.dividendAmountTooltip')
					},
					{
						seriesValue: 'y2',
						seriesName: this.$t('pro.dividendRate'),
						tooltipText: this.$t('pro.dividendRateTooltip')
					}
				],
				seriesFieldX: 'x',
				labels: {
					truncate: true,
					maxWidth: 200,
					rotation: -45,
					horizontalCenter: 'right',
					verticalCenter: 'middle'
				}
			},
			dividends: []
		};
	},
	computed: {
		chartData: function () {
			return _.map(this.dividends, function (item) {
				return {
					x: item.paymentDate,
					y1: item.paymentLc,
					y2: item.annDivLc
				};
			});
		}
	},
	watch: {
		fundCode: {
			handler: function (newVal, oldVal) {
				this.getDividends();
			}
		}
	},
	mounted: function () {
		const self = this;
		self.getDividends();
	},
	methods: {
		getDividends: async function () {
			const self = this;
			const ret = await getDividendsApi({
				fundCode: self.fundCode
			});
			self.dividends = _.orderBy(ret.data, ['xdDate']);
		}
	}
};
</script>
