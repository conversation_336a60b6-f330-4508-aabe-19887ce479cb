<template>
	<!--頁面內容 組合式商品dci start-->
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<div class="tab-nav-tabs my-3">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'common' }"
							data-bs-toggle="tab"
							@click="changeTab('common')"
						>{{ $t('pro.generalFilter') }}</a>
					</li>
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'fast' }"
							data-bs-toggle="tab"
							@click="changeTab('fast')"
						>{{ $t('pro.quickFilter') }}</a>
					</li>
				</ul>

				<div class="tab-content">
					<div id="SectionA" class="tab-pane fade show" :class="{ 'active show': activeTab == 'common' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>{{ $t('pro.searchCond') }}</h4>
								<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
							</div>
							<div id="collapseListGroup1" class="collapse show">
								<div class="card-body">
									<form>
										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> {{ $t('pro.productCode') }} </label>
												<input
													id="prod_bank_pro_code"
													v-model="bankProCode"
													class="form-control"
													maxlength="20"
													size="25"
													type="text"
												>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> {{ $t('pro.productName') }}</label>
												<input
													id="prod_pro_name"
													v-model="proName"
													class="form-control"
													maxlength="20"
													size="45"
													type="text"
												>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> {{ $t('pro.currency') }}</label>
												<select
													id="curMenuDci"
													v-model="curObjs"
													class="selectpicker form-control"
													multiple
													:title="$t('pro.pleaseSelectCurrency')"
													data-style="btn-white"
												>
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="(item, index) in curOption" :key="index" :value="item.value">
														{{ $filters.defaultValue(item.name, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> {{ $t('pro.riskLevel') }} </label>
												<select
													id="riskCode"
													v-model="riskCode"
													class="form-select"
													name="riskCode"
												>
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="item in riskMenu" :value="item.riskCode">
														{{ $filters.defaultValue(item.riskName, '--') }}
													</option>
												</select>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.investmentTarget') }}</label>
												<select
													id="linkTarget"
													v-model="linkTarget"
													class="form-select"
													name="linkTarget"
												>
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="item in linkTargetMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</div>
											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.productType') }}</label>
												<div class="form-check-group">
													<div class="form-check form-check-inline">
														<input
															id="imi"
															v-model="guaranteeStatus"
															class="form-check-input"
															checked
															name="guaranteeStatus"
															type="radio"
															value=""
														>
														<label class="form-check-label" for="imi">{{ $t('pro.all') }}</label>
													</div>
													<div v-for="item in dcdGuaranteeTypeMenu" class="form-check form-check-inline">
														<input
															:id="'id-' + item.codeValue"
															v-model="guaranteeStatus"
															class="form-check-input"
															name="guaranteeStatus"
															type="radio"
															:value="item.codeValue"
														>
														<label class="form-check-label" :for="'id-' + item.codeValue">{{
															$filters.defaultValue(item.codeName, '--')
														}}</label>
													</div>
												</div>
											</div>

											<div class="form-group col-12 col-lg-6">
												<label class="form-label">{{ $t('pro.subscriptionStartDate') }}</label>
												<div class="input-group">
													<input
														id="prod_chinese_name"
														v-model="intStartDtBegin"
														class="form-control"
														maxlength="20"
														name="prod_chinese_name"
														size="10"
														type="date"
														value=""
													>
													<div class="input-group-text">
														~
													</div>
													<input
														id="prod_chinese_name"
														v-model="intStartDtEnd"
														class="form-control"
														maxlength="20"
														name="prod_chinese_name"
														size="10"
														type="date"
														value=""
													>
												</div>
											</div>
											<div class="form-group col-12 col-lg-6">
												<label class="form-label">{{ $t('pro.subscriptionEndDate') }}</label>
												<div class="input-group">
													<input
														id="prod_chinese_name"
														v-model="intEndDtBegin"
														class="form-control"
														maxlength="20"
														name="prod_chinese_name"
														size="10"
														type="date"
														value=""
													>
													<div class="input-group-text">
														~
													</div>
													<input
														id="prod_chinese_name"
														v-model="intEndDtEnd"
														class="form-control"
														maxlength="20"
														name="prod_chinese_name"
														size="10"
														type="date"
														value=""
													>
												</div>
											</div>
										</div>
									</form>
								</div>
								<div class="form-footer">
									<button class="btn btn-primary" @click.prevent="gotoPage(0)">
										{{ $t('pro.search') }}
									</button>
								</div>
							</div>
						</div>
					</div>

					<div id="SectionB" class="tab-pane fade" :class="{ 'active show': activeTab == 'fast' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
								<h4>{{ $t('pro.searchCond') }}</h4>
								<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
							</div>
							<div id="collapseListGroup2" class="collapse show">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-12">
											<label class="form-label tx-require"> {{ $t('pro.filterCondition') }} </label>

											<div class="form-check-group">
												<div
													v-for="item in dciFastMenu"
													class="form-check form-check-inline"
													@change="fastChange(item.codeValue)"
												>
													<input
														:id="'fast' + item.codeValue"
														v-model="fastCode"
														class="form-check-input"
														name="fastCode"
														:value="item.codeValue"
														type="radio"
													>
													<label class="form-check-label" :for="'fast' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>
										</div>
										<div id="rangeFixedTr" class="form-group col-12 col-lg-6" style="display: none">
											<label class="form-label tx-require"> {{ $t('pro.displayRange') }}</label>

											<select id="prod_protype_code" v-model="timeRange" class="form-select">
												<option v-for="item in timeRangeMenu" :value="item">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>

										<div id="maxRowIdTr" class="form-group col-12 col-lg-6" style="display: none">
											<label class="form-label tx-require">{{ $t('pro.displayRowCount') }}</label>
											<select id="maxRowId" v-model="rowNumber" class="form-select">
												<option value="">
													{{ $t('pro.all') }}
												</option>
												<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-footer">
										<button class="btn btn-primary" @click.prevent="gotoFastPage(0)">
											{{ $t('pro.search') }}
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div v-if="pageData.content.length > 0" id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('pro.searchResult') }}</h4>
						<div style="display: flex">
							<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
							<button type="button" class="btn btn-info ms-2" @click="performancesCompareModelHandler()">
								{{ $t('pro.performanceComparisonChart') }}
							</button>
							<vue-modal :is-open="isOpenCompareModal" @close="isOpenCompareModal = false">
								<template #content="props">
									<vue-performances-compare-modal
										id="performancesCompareModal"
										ref="performancesCompareModalRef"
										:close="props.close"
									/>
								</template>
							</vue-modal>
						</div>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-bordered text-center">
							<thead>
								<tr>
									<th class="wd-100 text-start">
										{{ $t('pro.addToComparison') }}
									</th>
									<th>{{ $t('pro.productCode') }}</th>
									<th class="10% text-start">
										{{ $t('pro.productChineseName') }}
									</th>
									<th>{{ $t('pro.currency') }}</th>
									<th>{{ $t('pro.riskLevel') }}</th>
									<th>{{ $t('pro.investmentTarget') }}</th>
									<th>{{ $t('pro.productType') }}</th>
									<th>{{ $t('pro.assessmentDate') }}<br>{{ $t('pro.assessmentProfitLoss') }}</th>
									<th>{{ $t('pro.subscriptionStartDate') }}</th>
									<th>{{ $t('pro.subscriptionEndDate') }}</th>
									<th class="text-center" width="120">
										{{ $t('pro.action') }}
									</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(item, index) in pageData.content">
									<td :data-th="$t('pro.addToComparison')" class="text-start text-center">
										<input
											:id="'id-' + item.bankProCode"
											v-model="selectedItems[item.proCode]"
											class="form-check-input text-center"
											type="checkbox"
										>
										<label class="form-check-label" :for="'id-' + item.bankProCode" />
									</td>
									<td :data-th="$t('pro.productCode')">
										<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
									</td>
									<td class="text-start" :data-th="$t('pro.productChineseName')">
										<span>
											<a class="tx-link" @click="dciModalHandler(item.proCode, item.pfcatCode)">{{
												$filters.defaultValue(item.proName, '--')
											}}</a>
										</span>
									</td>
									<td class="text-end" :data-th="$t('pro.currency')">
										<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
									</td>
									<td class="text-end" :data-th="$t('pro.riskLevel')">
										<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
									</td>
									<td class="text-end" :data-th="$t('pro.investmentTarget')">
										<span>{{ $filters.defaultValue(item.linkTargetName, '--') }}</span>
									</td>
									<td class="text-end" :data-th="$t('pro.productType')">
										<span>{{ $filters.defaultValue(item.guaranteeStatusName, '--') }}</span>
									</td>
									<td class="text-end" :data-th="$t('pro.evaluationDateAndPnl')">
										<span>{{ $filters.defaultValue(item.priceDt ? item.priceDt : '--', '--') }}<br>{{
											$filters.formatNumber(item.aprice, '--')
										}}</span>
									</td>
									<td :data-th="$t('pro.subscriptionStartDate')">
										<span>{{ $filters.defaultValue(item.intStartDt, '--') }}</span>
									</td>
									<td :data-th="$t('pro.subscriptionEndDate')">
										<span>{{ $filters.defaultValue(item.intEndDt, '--') }}</span>
									</td>
									<td class="text-center" :data-th="$t('pro.action')">
										<button
											v-if="activeTab === 'fast' && fastCode === '06'"
											type="button"
											class="btn btn-primary"
											:title="$t('pro.removeFavorite')"
											@click="remove(item.proCode)"
										>
											{{ $t('pro.removeFavorite') }}
										</button>
										<button
											v-else
											type="button"
											class="btn btn-dark btn-icon"
											data-bs-toggle="tooltip"
											:title="$t('pro.addToFavorite')"
											@click="favoritesHandler(item.proCode, item.pfcatCode)"
										>
											<i class="bi bi-heart text-danger" />
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="tx-note">
					<ol>
						<li><span>{{ $t('pro.dataDate') }}:</span></li>
						<li><span>{{ $t('pro.tradingSystemPrimary') }}</span></li>
					</ol>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import pagination from '@/views/components/pagination.vue';
import vuePerformancesCompareModal from './performancesCompareModal.vue';
import vueModal from '@/views/components/model.vue';
import _ from 'lodash';

export default {
	components: {
		'vue-pagination': pagination,
		vuePerformancesCompareModal,
		vueModal
	},
	props: {
		dciModalHandler: Function,
		favoritesHandler: Function,
		curOption: Array,
		riskMenu: Array
	},
	data: function () {
		return {
			activeTab: 'common',

			bankProCode: null,
			proName: null,
			curObjs: [], // 計價幣別 陣列物件
			riskCode: '', // 風險等級
			linkTarget: '', // 投資標的
			guaranteeStatus: '', // 商品類型
			intStartDtBegin: null, // 募集起日start
			intStartDtEnd: null, // 募集起日end
			intEndDtBegin: null, // 募集迄日start
			intEndDtEnd: null, // 募集迄日end

			fastCode: '05', // 快速 篩選條件
			timeRange: null, // 快速 顯示區間
			rowNumber: null, // 快速 顯示資料筆數

			linkTargetMenu: [], // 投資標的選項
			dcdGuaranteeTypeMenu: [], // 商品類型選項

			dciFastMenu: [], // 快速篩選條件
			timeRangeMenu: [], // 顯示區間
			rowNumerMenu: [], // 顯示資料筆數
			proCodes: [], // 商品代碼
			selectedItems: {}, // 加入比較選項

			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: ['PRO_CODE'],
				direction: 'ASC'
			},
			isOpenCompareModal: false
		};
	},
	watch: {
		selectedItems: {
			handler(newValues) {
				this.proCodes = Object.keys(newValues).filter(proCode => newValues[proCode]);
			},
			deep: true
		},
		activeTab(newVal, oldVal) {
			const self = this;
			self.fastCode = '05';
			self.fastChange(self.fastCode);
		},
		curObjs(newVal, oldVal) {
			const self = this;
			if (newVal[0] !== oldVal[0]) {
				if (newVal[0] === '') {
					$('#curMenuDci').selectpicker('selectAll');
				}
				else if (oldVal[0] === '' && newVal[0] !== '') {
					$('#curMenuDci').selectpicker('deselectAll');
				}
			}
		}
	},
	mounted: function () {
		const self = this;
		$('#curMenuDci').selectpicker('refresh');
		self.getDcdLinkTargetMenu(); // 取得投資標的選項
		self.getDcdGuaranteeTypeMenu(); // 取得商品類型選項
		self.getdciFastMenu(); // 取得快速篩選條件選項
		self.getTimeRangeMenu(); // 取得顯示區間選項
		self.getRowNumerMenu(); // 取得顯示資料筆數選項
		self.fastChange(self.fastCode);
	},
	methods: {
		// 條件Tab切換
		changeTab: function (tabName) {
			const self = this;
			self.activeTab = tabName;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
		},
		// 取得投資標的選項
		async getDcdLinkTargetMenu() {
			const ret = await this.$api.getAdmCodeDetail({
				codeType: 'DCD_LINK_TARGET'
			});
			this.linkTargetMenu = ret.data;
		},
		// 取得商品類型選項
		async getDcdGuaranteeTypeMenu() {
			const ret = await this.$api.getAdmCodeDetail({
				codeType: 'DCD_GUARANTEE_TYPE'
			});
			this.dcdGuaranteeTypeMenu = ret.data;
		},

		// 取得快速篩選條件選項
		async getdciFastMenu() {
			const ret = await this.$api.getDciFastFilterMenuApi();
			this.dciFastMenu = ret.data;
		},

		// 取得顯示區間
		async getTimeRangeMenu() {
			const ret = await this.$api.getTimeRangeMenuApi();
			this.timeRangeMenu = ret.data;
		},
		// 取得顯示資料筆數
		async getRowNumerMenu() {
			const ret = await this.$api.getRowNumerMenuApi();
			this.rowNumerMenu = ret.data;
		},
		fastChange(fastCode) {
			const self = this;
			self.timeRange = {}; // 快速 顯示區間
			self.rowNumber = ''; // 快速 顯示資料筆數
			self.pageData = {
				// 清空查詢資料
				content: []
			};
			// 快速查詢切換
			if (fastCode === '05') {
				// 超人氣
				$('#rangeFixedTr').show();
				$('#maxRowIdTr').show();
				self.timeRange = self.timeRangeMenu[0];
			}
			else {
				$('#maxRowIdTr').hide();
				$('#rangeFixedTr').hide();
			}
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		async getPageData(page) {
			const queryString = `?page=${page || this.pageable.page}&size=${this.pageable.size}&sort=${this.pageable.sort},${this.pageable.direction}`;
			const ret = await this.$api.getDciProductsApi(
				{
					bankProCode: this.bankProCode, // 商品代號
					proName: this.proName, // 商品名稱
					curCodes: this.curObjs, // 計價幣別 陣列物件
					riskCode: this.riskCode, // 風險等級
					linkTarget: this.linkTarget, // 投資標的
					guaranteeStatus: this.guaranteeStatus, // 商品類型
					buyMin: this.mininvAmt, // 最低申購面額
					invAccAmt: this.mininvAccAmt, // 累加面額
					intStartDtBegin: this.intStartDtBegin, // 募集起日start
					intStartDtEnd: this.intStartDtEnd, // 募集起日end
					intEndDtBegin: this.intEndDtBegin, // 募集迄日start
					intEndDtEnd: this.intEndDtEnd // 募集迄日end
				},
				queryString
			);
			this.pageData = ret.data;
			this.pageData.content.forEach(() => {
				this.checkboxs.push(false);
			});
		},
		// 執行績效比較圖
		performancesCompareModelHandler: function () {
			const self = this;
			if (self.proCodes.length > 0) {
				if (self.proCodes.length > 6) {
					thi.$bi.alert(this.$t('pro.maxSixItems'));
				}
				else {
					this.$refs.performancesCompareModalRef.comparePropItem(self.proCodes, 'dci');
					this.isOpenCompareModal = true;
				}
			}
			else {
				thi.$bi.alert(this.$t('pro.selectAtLeastOne'));
			}
		},
		gotoFastPage: function (page) {
			this.pageable.page = page;
			this.getFastPageData(page);
		},
		async getFastPageData(page) {
			const queryString = `?page=${page || this.pageable.page}&size=${this.pageable.size}&sort=${this.pageable.sort},${this.pageable.direction}`;

			const ret = await this.$api.getDciProductsFilterQueryApi(
				{
					filterCodeValue: this.fastCode,
					timeRangeType: this.timeRange.rangeType,
					timeRangeFixed: this.timeRange.rangeFixed,
					rowNumberFixed: this.rowNumber
				},
				queryString
			);

			this.pageData = ret.data;
			this.pageData.content.forEach(() => {
				this.checkboxs.push(false);
			});
		},
		// 刪除我的最愛
		async remove(proCode) {
			await this.$api.deleteFavoriteApi({ proCode });
			this.$bi.alert(this.$t('pro.deleteSuccess'));
			this.checkboxs = [];
			this.proCodes = [];
			this.gotoFastPage(0);
		}
	} // methods end
};
</script>
<style scoped>
.dropdown.bootstrap-select {
	min-width: 0;
}
</style>
