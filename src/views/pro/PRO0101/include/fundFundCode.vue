<template>
	<div>
		<h4>{{ $t('pro.otherCorrespondingCodes') }}</h4>
		<table class="table table-bordered">
			<tbody>
				<tr v-for="(item, i) in fundCodes" v-if="i < 1">
					<th width="45%">
						{{ item.name }}
					</th>
					<td width="55%">
						{{ item.code }}
					</td>
				</tr>
			</tbody>
			<tfoot v-if="fundCodes && fundCodes.length > 1">
				<td colspan="2">
					<a href="#fundCode" class="pull-right" @click.prevent="openModal">...{{ $t('pro.更多') }}</a>
				</td>
			</tfoot>
		</table>
		<vue-modal :is-open="isOpenModal" @close="closeModal">
			<template #content="props">
				<div class="modal-dialog">
					<div class="modal-content">
						<div class="modal-header">
							<button
								type="button"
								class="close"
								aria-hidden="true"
								@click.prevent="props.close()"
							/>
							<h4 class="modal-title">
								{{ $t('pro.otherCorrespondingCodes') }}
							</h4>
						</div>
						<div class="modal-body dataTables_wrapper">
							<table class="table table-bordered">
								<tr v-for="(item, i) in fundCodes">
									<th width="50%">
										{{ item.name }}
									</th>
									<td width="50%">
										{{ item.code }}
									</td>
								</tr>
							</table>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-default" @click.prevent="props.close()">
								{{ $t('pro.closeWindow') }}
							</button>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
	</div>
</template>
<script>
export default {
	props: {
		fundCode: String
	},
	data: function () {
		return {
			isOpenModal: null,
			fundCodes: []
		};
	},
	computed: {},
	watch: {
		fundCode: {
			handler: function (newVal, oldVal) {
				this.getFundCodes();
			}
		}
	},
	mounted: function () {},
	methods: {
		openModal: function () {
			this.isOpenModal = true;
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		getFundCodes: async function () {
			const self = this;
			const ret = await this.$api.getFundCodeApi({
				fundCode
			});
			self.fundCodes = ret.data;
		}
	}
};
</script>
