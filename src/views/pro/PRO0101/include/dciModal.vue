<template>
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('pro.compositeProducts') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click.prevent="close()"
				/>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-structure" />
							<h4>
								<span>{{ $t('pro.proName') }}</span> <br>{{ $filters.defaultValue(proInfo.proName, '--') }} <br><span class="tx-black">{{
									$filters.defaultValue(proInfo.proEName, '--')
								}}</span>
							</h4>
						</div>
						<h4 class="pro_value">
							<span>{{ $t('pro.assessmentProfitLoss') }}</span>
							<br><span>{{ $filters.formatNumber(proInfo.aprice, '0,0.00' || '--') }}</span> <br><span>{{
								$filters.formatDate(proInfo.priceDt, '--')
							}}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.proCode') }}</span> <br>{{ $filters.defaultValue(proInfo.bankProCode, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6>
								<span>{{ $t('pro.assetCategory') }} <br></span>{{ $filters.defaultValue(proInfo.assetcatName, '--') }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productMainCategory') }}</span> <br>{{ $filters.defaultValue(proInfo.pfcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productSubCategory') }}</span><br>{{ $filters.defaultValue(proInfo.proTypeName, '--') }}</h6>
						</div>
					</div>
				</div>
				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item">
							<a class="nav-link active" href="#Sectionst1" data-bs-toggle="pill">{{ $t('pro.productBasicInfo') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#Sectionst2" data-bs-toggle="pill">{{ $t('pro.productCommonData') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#Sectionst3" data-bs-toggle="pill">{{ $t('pro.productAdditionalData') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link datainfo" href="#Sectionst4" data-bs-toggle="pill">{{ $t('pro.navAnalysis') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link datainfo" href="#Sectionst5" data-bs-toggle="pill">{{ $t('pro.performance') }}</a>
						</li>
					</ul>

					<div class="tab-content">
						<div id="Sectionst1" class="tab-pane fade show active">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.productInfo') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.riskLevel') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.dciInfo.riskName, '--') }}
											</td>
											<th>{{ $t('pro.pricingCurrency') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.dciInfo.curCode, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.investmentTarget') }}</th>
											<td>{{ $filters.defaultValue(proInfo.dciInfo.linkTargetDesc, '--') }}</td>
											<th>{{ $t('pro.capitalRaiseStatus') }}</th>
											<td>{{ $filters.defaultValue(proInfo.dciInfo.capitalRaiseStatusCode, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.capitalRaiseStartDate') }}</th>
											<td>{{ $filters.defaultValue(proInfo.dciInfo.intStartDt, '--') }}</td>
											<th>{{ $t('pro.capitalRaiseEndDate') }}</th>
											<td>{{ $filters.defaultValue(proInfo.dciInfo.intEndDt, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.proType') }}</th>
											<td>{{ $filters.defaultValue(proInfo.dciInfo.guaranteeStatus, '--') }}</td>
											<th>{{ $t('pro.guaranteedYieldRate') }}</th>
											<td>
												<span>{{ $filters.formatPct(proInfo.dciInfo.principalGuarRate, '') }}%</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.investmentAmountRestrictions') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.minimumDepositAmount') }}</th>
											<td class="wd-30p">
												<span>{{ $filters.formatNumber(proInfo.dciInfo.mininvLcAmt) }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionst2" class="tab-pane fade">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.salesRelatedData') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th width="20%">
												{{ $t('pro.salesRegion') }}
											</th>
											<td v-if="proInfo.allYn == 'Y'" width="30%">
												{{ $t('pro.allBranches') }}
											</td>
											<td v-else width="30%">
												--
											</td>
											<th><span>{{ $t('pro.salesTarget') }}</span></th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.targetCusBu, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.isOpenForPurchase') }}</th>
											<td>{{ $t('pro.pleaseContactFinancialMarketing') }}</td>
											<th>{{ $t('pro.isOpenForRedemption') }}</th>
											<td>{{ $filters.defaultValue(proInfo.sellYn, '--') }}</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.volatilityType') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.volatilityType, '--') }}</span>
											</td>
											<th><span>{{ $t('pro.dividendFrequency') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.intFreqUnitype, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.financialNeeds') }}</span></th>
											<td colspan="3">
												<div v-for="item in finReqCodeMenu" class="form-check form-check-inline">
													<input
														id="c1"
														v-model="finReqCodes"
														class="form-check-input"
														name="finReqCodes"
														disabled
														:value="item.codeValue"
														type="checkbox"
													>
													<label class="form-check-label" for="c1">{{ $filters.defaultValue(item.codeName, '--') }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.productTags') }}</th>
											<td disabled="disabled" colspan="3">
												{{ $filters.defaultValue(proInfo.selprocatNames, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionst3" class="tab-pane fade">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.otherSettings') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>{{ $t('pro.remarks') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.memo, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.relatedAttachments') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.productProspectus') }}</th>
											<td class="wd-80p">
												<a v-if="proFileA && proFileA.url" :href="proFileA.url" target="_blank">{{
													$filters.defaultValue(proFileA.url, '--')
												}}</a><br v-if="proFileA && proFileA.url">
												<a
													v-if="proFileA"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileA)"
												>{{
													$filters.defaultValue(proFileA.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.investorNotice') }}</th>
											<td class="wd-80p">
												<a v-if="proFileD && proFileD.url" :href="proFileD.url" target="_blank">{{
													$filters.defaultValue(proFileD.url, '--')
												}}</a><br v-if="proFileD && proFileD.url">
												<a
													v-if="proFileD"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileD)"
												>{{
													$filters.defaultValue(proFileD.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<a v-if="proFileF && proFileF.url" :href="proFileF.url" target="_blank">{{
													$filters.defaultValue(proFileF.url, '--')
												}}</a><br v-if="proFileF && proFileF.url">
												<a
													v-if="proFileF"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileF)"
												>{{
													$filters.defaultValue(proFileF.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.other') }}</th>
											<td class="wd-80p">
												<a v-if="proFileG && proFileG.url" :href="proFileG.url" target="_blank">{{
													$filters.defaultValue(proFileG.url, '--')
												}}</a><br v-if="proFileG && proFileG.url">
												<a
													v-if="proFileG"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileG)"
												>{{
													$filters.defaultValue(proFileG.showName, '--')
												}}</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.otherRelatedAttachments') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<td>
												<span v-for="(item, index) in otherFileList">
													<a
														v-if="index === otherFileList.length - 1"
														v-show="item.show"
														href="#"
														class="tx-link"
														@click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}</a>
													<a
														v-else
														v-show="item.show"
														href="#"
														class="tx-link"
														@click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}、</a>
												</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="tx-note">
								{{ $t('pro.otherRelatedAttachmentsNote') }}
							</div>
						</div>

						<div id="Sectionst4" class="tab-pane fade">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.navAnalysis') }}</h4>
								</div>
								<table class="table table-RWD table-bordered text-end">
									<thead>
										<tr>
											<th class="wd-10p text-start">
												{{ $t('pro.item') }}
											</th>
											<th class="wd-30p">
												{{ $t('pro.nav') }}
											</th>
											<th class="wd-30p">
												{{ $t('pro.highestNavYear') }}
											</th>
											<th class="wd-30p">
												{{ $t('pro.lowestNavYear') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td class="text-start" :data-th="$t('pro.item')">
												{{ $t('pro.nav') }}
											</td>
											<td class="text-end" :data-th="$t('pro.price')">
												<span>{{ $filters.defaultValue(dciPriceAna.aprice, '--') }}({{
													$filters.defaultValue(dciPriceAna.priceDt, '--')
												}})</span>
											</td>
											<td class="text-end" :data-th="$t('pro.highestPriceYear')">
												<span>{{ $filters.defaultValue(dciPriceAna.maxAprice, '--') }}({{
													$filters.defaultValue(dciPriceAna.maxPriceDt, '--')
												}})</span>
											</td>
											<td class="text-end" :data-th="$t('pro.lowestPriceYear')">
												<span>{{ $filters.defaultValue(dciPriceAna.minAprice, '--') }}({{
													$filters.defaultValue(dciPriceAna.minPriceDt, '--')
												}})</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="text-center">
									<div class="col-12">
										<div class="card-header">
											<h4>{{ $t('pro.historicalPriceTrendChart') }}{{ $filters.defaultValue(proInfo.proCode, '--') }}</h4>
										</div>
										<br>
										<vue-net-chart
											ref="dciNetChartRef"
											:chart-id="chartId"
											:pro-price-range-menu="proPriceRangeMenu"
										/>
										<div class="btn-group btn-group-sm mb-4" role="group">
											<input
												v-for="item in proPriceRangeMenu"
												:id="'dciNetPeriod' + item.termValue"
												type="radio"
												class="btn-check"
												name="time"
												:checked="item.termValue == '4'"
												@click="getDciNets(proInfo.proCode, item.rangeType, item.rangeFixed)"
											>
											<label
												v-for="item in proPriceRangeMenu"
												class="btn btn-outline-secondary"
												:for="'dciNetPeriod' + item.termValue"
											>{{ $filters.defaultValue(item.termName, '--') }}</label>
										</div>
									</div>
								</div>
								<div class="caption">
									{{ $t('pro.last30DaysNav') }}
								</div>
								<table class="table table-RWD table-bordered text-center">
									<thead>
										<tr>
											<th>{{ $t('pro.date') }}</th>
											<th class="text-end">
												{{ $t('pro.referenceNav') }}
											</th>
											<th>{{ $t('pro.date') }}</th>
											<th class="text-end">
												{{ $t('pro.referenceNav') }}
											</th>
											<th>{{ $t('pro.date') }}</th>
											<th class="text-end">
												{{ $t('pro.referenceNav') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="(item, index) in dciPriceHist">
											<td :data-th="$t('pro.date')">
												{{ $filters.defaultValue(item.priceDt1, '--') }}
											</td>
											<td class="text-end" :data-th="$t('pro.referenceNav')">
												<span>{{ $filters.defaultValue(item.aprice1, '--') }}</span>
											</td>
											<td :data-th="$t('pro.date')">
												{{ $filters.defaultValue(item.priceDt2, '--') }}
											</td>
											<td class="text-end" :data-th="$t('pro.referenceNav')">
												<span>{{ $filters.defaultValue(item.aprice2, '--') }}</span>
											</td>
											<td :data-th="$t('pro.date')">
												{{ $filters.defaultValue(item.priceDt3, '--') }}
											</td>
											<td class="text-end" :data-th="$t('pro.referenceNav')">
												<span>{{ $filters.defaultValue(item.aprice3, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionst5" class="tab-pane fade">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.performanceAnalysis') }}</h4>
								</div>
								<div class="card-body">
									<div class="row">
										<div class="col-sm-2">
											<label class="tc-blue">{{ $t('pro.selectProduct') }}:</label>
										</div>
										<div class="col-sm-8">
											<div class="input-group">
												<select v-model="issuerCode" class="form-select">
													<option selected value>
														--
													</option>
													<option v-for="item in productList" :value="item.proCode">
														{{ $filters.defaultValue(item.proName, '--') }}
														{{ $filters.defaultValue(item.proCode, '--') }}
													</option>
												</select>
											</div>
										</div>
										<div class="col-sm-2">
											<p>
												<input
													class="btn btn-primary text-alignRight"
													type="button"
													:value="$t('pro.add')"
													@click="addPro()"
												>
											</p>
										</div>
									</div>

									<div class="caption">
										{{ $t('pro.addedProducts') }}
									</div>
									<div class="table-responsive mb-3">
										<table class="table table-bordered">
											<thead>
												<tr>
													<th>{{ $t('pro.proName') }}</th>
													<th class="text-end">
														{{ $t('pro.oneYearReturn') }}
													</th>
													<th class="text-center">
														{{ $t('pro.action') }}
													</th>
												</tr>
											</thead>
											<tbody>
												<tr v-for="item in observedProList">
													<td>
														{{ $filters.defaultValue(item.proName, '--') }}{{ $filters.defaultValue(item.proCode, '--') }}
													</td>
													<td class="text-end">
														{{ $filters.formatPct(item.fcTdReturn) }}%
													</td>
													<td class="text-center">
														<button
															type="button"
															class="btn btn-danger btn-icon"
															data-bs-toggle="tooltip"
															:title="$t('pro.delete')"
															@click="deletePro(item.proCode)"
														>
															<i class="fa-solid fa-trash" />
														</button>
													</td>
												</tr>
											</tbody>
										</table>
									</div>

									<div class="text-center">
										<!-- {{ $t('pro.performanceAnalysisChart') }} -->
										<vue-performances-chart ref="dciPerformancesChartRef" :chart-id="performancesId" />
										<div class="btn-group btn-group-sm mb-4" role="group">
											<template v-for="item in proPriceRangeMenu">
												<input
													:id="'performancesPeriod' + item.termValue"
													type="radio"
													class="btn-check"
													name="time"
													:checked="item.termValue == '4' ? true : false"
												>
												<label
													class="btn btn-outline-secondary"
													:for="'performancesPeriod' + item.termValue"
													@click.prevent="getDciPerformances(proCodes, item.rangeType, item.rangeFixed)"
												>{{ $filters.defaultValue(item.termName, '--') }}</label>
											</template>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-white" @click.prevent="close()">
						{{ $t('pro.closeWindow') }}
					</button>
				</div>
			</div>
		</div>
	</div>
	<!-- Modal 2 End -->
</template>
<script>
import moment from 'moment';
import _ from 'lodash';
import vueNetChart from './netChart.vue';
import vuePerformancesChart from './performancesChart.vue';
export default {
	components: {
		vueNetChart,
		vuePerformancesChart
	},
	props: {
		downloadFile: Function,
		downloadOtherFile: Function,
		finReqCodeMenu: Array,
		proPriceRangeMenu: Array,
		close: Function
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				bondInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},
			curObjs: [],
			chartsData: [], // Performance analysis chart data
			proCodes: [], // Performance analysis added products
			productList: [], // Performance analysis product list
			observedProList: [], // Performance analysis added products return rate list
			issuerCode: null, // Performance analysis selected product

			finReqCodes: [], // Financial needs
			proFileA: {}, // Related attachments product prospectus
			proFileD: {}, // Related attachments investor notice
			proFileF: {}, // Related attachments DM
			proFileG: {}, // Related attachments other
			otherFileList: [], // Other related attachments
			dciPriceAna: {},
			dciPriceHist: [],
			chartId: 'dciNetChartId',
			performancesId: 'dciPerformancesChartId'
		};
	},
	watch: {},
	created() {},
	mounted: function () {},
	methods: {
		getProInfo: function (bankProCode, pfcatCode) {
			const self = this;
			self.proCodes = [];
			self.proCodes.push(bankProCode); // Default product to add
			self.$api
				.getProductInfoApi({
					proCode: bankProCode,
					pfcatCode: pfcatCode
				})
				.then(function (ret) {
					if (_.isNil(ret.data)) {
						ret.data = {};
						self.$bi.alert(self.$t('pro.dataNotExist'));
						return;
					}
					if (_.isNil(ret.data.dciInfo)) {
						ret.data.dciInfo = {};
					}

					self.proInfo = ret.data;

					if (!_.isUndefined(self.proInfo.dciInfo.guaranteeStatus)) {
						let dcdGuaranteeTypeList = [];
						self.$api
							.getAdmCodeDetail({
								codeType: 'DCD_GUARANTEE_TYPE'
							})
							.then(function (ret) {
								dcdGuaranteeTypeList = ret.data;
								const dcdGuaranteeTypeObjs = _.filter(dcdGuaranteeTypeList, {
									codeValue: self.proInfo.dciInfo.guaranteeStatus
								});
								self.proInfo.dciInfo.guaranteeStatus = dcdGuaranteeTypeObjs[0].codeName;
							});
					}

					if (!_.isUndefined(self.proInfo.dciInfo.linkTargetDesc)) {
						let dcdLinkTargetList = [];
						self.$api
							.getAdmCodeDetail({
								codeType: 'DCD_LINK_TARGET'
							})
							.then(function (ret) {
								dcdLinkTargetList = ret.data;
								const dcdLinkTargetObjs = _.filter(dcdLinkTargetList, {
									codeValue: self.proInfo.dciInfo.linkTargetDesc
								});
								self.proInfo.dciInfo.linkTargetDesc = dcdLinkTargetObjs[0].codeName;
							});
					}

					if (!_.isUndefined(self.proInfo.dciInfo.capitalRaiseStatusCode)) {
						let dcdRaiseStatusList = [];
						self.$api
							.getAdmCodeDetail({
								codeType: 'DCD_RAISE_STATUS'
							})
							.then(function (ret) {
								dcdRaiseStatusList = ret.data;
								if (dcdRaiseStatusList.length > 0) {
									const dcdRaiseStatusObjs = _.filter(dcdRaiseStatusList, {
										codeValue: self.proInfo.dciInfo.capitalRaiseStatusCode
									});
									self.proInfo.dciInfo.capitalRaiseStatusCode = dcdRaiseStatusObjs[0].codeName;
								}
							});
					}

					if (!_.isUndefined(self.proInfo.targetCusBu)) {
						let targetCusBuList = [];
						self.$api
							.getAdmCodeDetail({
								codeType: 'CUS_BU'
							})
							.then(function (ret) {
								targetCusBuList = ret.data;
								const targetCusBuObjs = _.filter(targetCusBuList, {
									codeValue: self.proInfo.targetCusBu
								});
								self.proInfo.targetCusBu = targetCusBuObjs[0].codeName;
							});
					}

					if (!_.isUndefined(self.proInfo.buyYn) && !_.isUndefined(self.proInfo.sellYn)) {
						let selectYnList = [];
						self.$api
							.getAdmCodeDetail({
								codeType: 'SELECT_YN'
							})
							.then(function (ret) {
								selectYnList = ret.data;

								if (!_.isUndefined(self.proInfo.buyYn)) {
									const buyYnObjs = _.filter(selectYnList, {
										codeValue: self.proInfo.buyYn
									});
									self.proInfo.buyYn = buyYnObjs[0].codeName;
								}

								if (!_.isUndefined(self.proInfo.sellYn)) {
									const sellYnObjs = _.filter(selectYnList, {
										codeValue: self.proInfo.sellYn
									});
									self.proInfo.sellYn = sellYnObjs[0].codeName;
								}
							});
					}

					if (!_.isUndefined(self.proInfo.volatilityType)) {
						let volatilityTypeList = [];
						self.$api
							.getAdmCodeDetail({
								codeType: 'VOLATILITY_TYPE'
							})
							.then(function (ret) {
								volatilityTypeList = ret.data;
								const volatilityTypeObjs = _.filter(volatilityTypeList, {
									codeValue: self.proInfo.volatilityType
								});
								self.proInfo.volatilityType = volatilityTypeObjs[0].codeName;
							});
					}

					if (!_.isUndefined(self.proInfo.intFreqUnitype)) {
						let intFreqUnitypeList = [];
						self.$api
							.getAdmCodeDetail({
								codeType: 'INT_FREQ_UNITTYPE'
							})
							.then(function (ret) {
								intFreqUnitypeList = ret.data;
								const intFreqUnitypeObjs = _.filter(intFreqUnitypeList, {
									codeValue: self.proInfo.intFreqUnitype
								});
								self.proInfo.intFreqUnitype = intFreqUnitypeObjs[0].codeName;
							});
					}

					if (!_.isUndefined(self.proInfo.selprocatNames)) {
						const selprocatNames = self.proInfo.selprocatNames.replaceAll(',', '、');
						self.proInfo.selprocatNames = selprocatNames;
					}

					if (!_.isUndefined(self.proInfo.finReqCode)) {
						self.finReqCodes = self.proInfo.finReqCode.split(',');
					}
				});

			// Product additional data
			self.$api
				.getProductsCommInfo({
					proCode: bankProCode,
					pfcatCode: pfcatCode
				})
				.then(function (ret) {
					if (!_.isNil(ret.data)) {
						if (ret.data.proDocs) {
							self.otherFileList = ret.data.proDocs; // Other related attachments
							self.otherFileList.forEach(function (item) {
								// Other related attachments file display time range
								item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
							});
						}

						const proFileList = ret.data.proFiles;
						if (!_.isNil(proFileList)) {
							self.proFileA = proFileList.filter(proFile => proFile.fileType === 'A')[0];
							self.proFileD = proFileList.filter(proFile => proFile.fileType === 'D')[0];
							self.proFileF = proFileList.filter(proFile => proFile.fileType === 'F')[0];
							self.proFileG = proFileList.filter(proFile => proFile.fileType === 'G')[0];
						}
					}
				});
			self.proCodes = []; // Clear existing data upon entry
			self.proCodes.push(bankProCode); // Add default product
			self.observedPro(); // Performance analysis get added products list
			self.productMenu(); // Performance analysis product selection dropdown
		},

		// Product info/price analysis and last 30 days prices
		getDciPriceAna: function (proCode, rangeType, rangeFixed) {
			const self = this;
			const proCodeArray = { 0: proCode };
			self.$api
				.getPriceAnaApi({
					proCodes: proCodeArray,
					freqType: rangeType,
					freqFixed: rangeFixed
				})
				.then(function (ret) {
					if (!_.isNil(ret.data)) {
						self.dciPriceAna = ret.data;
						if (!_.isNil(ret.data.priceHist)) {
							const orgPriceHis = ret.data.priceHist;
							const newPriceHis = [];
							orgPriceHis.forEach(function (item, index) {
								if (index % 3 == 0) {
									if (index + 2 < orgPriceHis.length) {
										var pricHisObj = {
											priceDt1: orgPriceHis[index].priceDt,
											aprice1: Number.parseFloat(orgPriceHis[index].aprice),
											priceDt2: orgPriceHis[index + 1].priceDt,
											aprice2: Number.parseFloat(orgPriceHis[index + 1].aprice),
											priceDt3: orgPriceHis[index + 2].priceDt,
											aprice3: Number.parseFloat(orgPriceHis[index + 2].aprice)
										};
										newPriceHis.push(pricHisObj);
									}
									else if (index + 1 < orgPriceHis.length) {
										var pricHisObj = {
											priceDt1: orgPriceHis[index].priceDt,
											aprice1: Number.parseFloat(orgPriceHis[index].aprice),
											priceDt2: orgPriceHis[index + 1].priceDt,
											aprice2: Number.parseFloat(orgPriceHis[index + 1].aprice),
											priceDt3: null,
											aprice3: null
										};
										newPriceHis.push(pricHisObj);
									}
									else {
										var pricHisObj = {
											priceDt1: orgPriceHis[index].priceDt,
											aprice1: Number.parseFloat(orgPriceHis[index].aprice),
											priceDt2: null,
											aprice2: null,
											priceDt3: null,
											aprice3: null
										};
										newPriceHis.push(pricHisObj);
									}
								}
							});
							self.dciPriceHist = newPriceHis;
						}
					}
				});
		},
		getDciPerformances: function (proCodes, rangeType, rangeFixed) {
			// Performance analysis chart
			const self = this;
			self.$api
				.getPerformanceRunChartApi({
					proCodes: proCodes,
					freqType: rangeType, // Display interval type
					freqFixed: rangeFixed // Display interval value
				})
				.then(function (ret) {
					if (!_.isEmpty(ret.data.datas)) {
						for (let i = 0; i < ret.data.length; i++) {
							ret.data[i].datas.forEach((e) => {
								e.value = e.returnFc;
								e.date = new Date(e.returnDt).getTime();
							});
						}
						self.chartsData = ret.data;
						self.$refs.dciPerformancesChartRef.initChart(self.chartsData);
					}
				});
		},
		// Product info/price analysis chart
		getDciNets: function (proCode, rangeType, rangeFixed) {
			const self = this;
			self.$refs.dciNetChartRef.getNets(proCode, rangeType, rangeFixed);
		},
		// Performance analysis added products list
		observedPro() {
			const self = this;
			self.$api
				.getObservedProductsApi({
					proCodes: self.proCodes.join()
				})
				.then(function (ret) {
					self.observedProList = ret.data;
				});
		},
		// Performance analysis product selection dropdown
		productMenu() {
			const self = this;
			self.$api
				.getProductByPfcatCodeApi({
					pfcatCode: 'DCI'
				})
				.then(function (ret) {
					self.productList = ret.data;
				});
		},
		// Performance analysis product selection add button
		addPro() {
			const self = this;
			let pk = null;
			pk = _.find(self.proCodes, function (item) {
				return item == self.issuerCode;
			});
			if (self.issuerCode != null && pk == null) {
				self.proCodes.push(self.issuerCode); // Add selected product
				self.observedPro(); // Performance analysis get added products list
				self.getDciPerformances(self.proCodes, 'Y', -1.0); // Product info/performance analysis chart
			}
			else if (self.issuerCode != null && pk != null) {
				this.$bi.alert(this.$t('pro.productAlreadyAdded'));
			}
			else {
				this.$bi.alert(this.$t('pro.pleaseSelectProduct'));
			}
		},
		// Performance analysis added products delete button
		deletePro(proCode) {
			const self = this;
			if (self.proCodes.length > 1) {
				const index = self.proCodes.indexOf(proCode); // Find index to remove
				self.proCodes.splice(index, 1); // Remove added product (index position to insert or delete, number of elements to delete)
				self.observedPro(); // Performance analysis get added products list
				self.getDciPerformances(self.proCodes, 'Y', -1.0); // Product info/performance analysis chart
			}
			else {
				this.$bi.alert(this.$t('pro.mustHaveAtLeastOneProduct'));
			}
		}
	} // methods end
};
</script>
