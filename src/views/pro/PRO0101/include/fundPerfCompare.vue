<template>
	<div class="col-12">
		<div class="col-12">
			<h4>{{ $t('pro.performanceComparison') }}</h4>
		</div>
		<div class="col-sm-3">
			<select
				v-model="selectedCompareType"
				class="form-select selectPicker w-100"
				style="margin: 5px 0"
				@change="getAllPerfRank"
			>
				<option value="GLOBAL_CLASS">
					{{ fundInfo.globalClassName }}(Lipper Global)
				</option>
				<option value="LOCAL_CLASS">
					{{ $filters.defaultValue(fundInfo && fundInfo.localClassName, '--') }}(Lipper Local)
				</option>
			</select>
		</div>
		<div class="col-12">
			<table class="table">
				<thead>
					<tr>
						<th width="8%">
&nbsp;
						</th>
						<th width="17%">
&nbsp;
						</th>
						<th class="text-center">
							{{ $t('pro.nearOneMonth') }}(%)
						</th>
						<th class="text-center">
							{{ $t('pro.nearThreeMonths') }}(%)
						</th>
						<th class="text-center">
							{{ $t('pro.nearSixMonths') }}(%)
						</th>
						<th class="text-center">
							{{ $t('pro.nearOneYear') }}(%)
						</th>
						<th class="text-center">
							{{ $t('pro.nearThreeYears') }}(%)
						</th>
						<th class="text-center">
							{{ $t('pro.nearFiveYears') }}(%)
						</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>{{ $t('pro.name') }}</td>
						<td>{{ fundInfo && fundInfo.fundName ? fundInfo.fundName : '--' }}</td>
						<td>
							<p>{{ getTech(techs, 'PCT1M') }}</p>
						</td>
						<td>
							<p>{{ getTech(techs, 'PCT3M') }}</p>
						</td>
						<td>
							<p>{{ getTech(techs, 'PCT6M') }}</p>
						</td>
						<td>
							<p>{{ getTech(techs, 'PCT1Y') }}</p>
						</td>
						<td>
							<p>{{ getTech(techs, 'PCT3Y') }}</p>
						</td>
						<td>
							<p>{{ getTech(techs, 'PCT5Y') }}</p>
						</td>
					</tr>
					<tr>
						<td>{{ $t('pro.correspondingIndex') }}</td>
						<td>{{ bmName }}</td>
						<td>
							<p>{{ getTech(bmTechs, 'PCT1M') }}</p>
						</td>
						<td>
							<p>{{ getTech(bmTechs, 'PCT3M') }}</p>
						</td>
						<td>
							<p>{{ getTech(bmTechs, 'PCT6M') }}</p>
						</td>
						<td>
							<p>{{ getTech(bmTechs, 'PCT1Y') }}</p>
						</td>
						<td>
							<p>{{ getTech(bmTechs, 'PCT3Y') }}</p>
						</td>
						<td>
							<p>{{ getTech(bmTechs, 'PCT5Y') }}</p>
						</td>
					</tr>
					<tr>
						<td>{{ $t('pro.sameTypeIndex') }}</td>
						<td>{{ getGroupBmName }}</td>
						<td>
							<p>{{ getTech(getGroupBmTechs, 'PCT1M') }}</p>
						</td>
						<td>
							<p>{{ getTech(getGroupBmTechs, 'PCT3M') }}</p>
						</td>
						<td>
							<p>{{ getTech(getGroupBmTechs, 'PCT6M') }}</p>
						</td>
						<td>
							<p>{{ getTech(getGroupBmTechs, 'PCT1Y') }}</p>
						</td>
						<td>
							<p>{{ getTech(getGroupBmTechs, 'PCT3Y') }}</p>
						</td>
						<td>
							<p>{{ getTech(getGroupBmTechs, 'PCT5Y') }}</p>
						</td>
					</tr>
					<tr>
						<td colspan="2">
&nbsp;
						</td>
						<td v-for="(item, i) in rankTechs">
							<span class="text-muted">{{ $t('pro.ranking') }}</span><span class="text-muted pull-right">%</span>
							<div class="barnum">
								<span class="barnum-txt-top">1</span>
								<span class="barnum-txt-cur" :style="{ top: getRank(ranks[item], 'percent') + '%' }">{{
									getRank(ranks[item], 'rank')
								}}</span>
								<span class="barnum-txt-bottom">{{ getRank(ranks[item], 'total') }}</span>
								<span class="barnum-bg" />
							</div>
						</td>
					</tr>
				</tbody>
			</table>
			<p class="m-t-5">
				* {{ $t('pro.fundRankingExplanation') }}
			</p>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
export default {
	props: {
		techCurrencyCode: String,
		techs: Array,
		fundInfo: Object
	},
	data: function () {
		return {
			selectedCompareType: 'GLOBAL_CLASS',
			bmName: null,
			bmTechs: [],
			globalClassBmTechs: [],
			localClassBmTechs: [],
			rankTechs: ['PCT1M', 'PCT3M', 'PCT6M', 'PCT1Y', 'PCT3Y', 'PCT5Y'],
			ranks: {}
		};
	},
	computed: {
		getGroupBmName: function () {
			switch (this.selectedCompareType) {
				case 'GLOBAL_CLASS':
					return this.fundInfo.globalClassBmName;
				case 'LOCAL_CLASS':
					return this.fundInfo.localClassBmName;
			}
		},
		getGroupBmTechs: function () {
			switch (this.selectedCompareType) {
				case 'GLOBAL_CLASS':
					return this.globalClassBmTechs;
				case 'LOCAL_CLASS':
					return this.localClassBmTechs;
			}
		}
	},
	watch: {
		fundInfo: {
			handler: function (newVal, oldVal) {
				this.getAllBmTechs();
				this.getAllPerfRank();
			}
		}
	},
	mounted: function () {
		const self = this;
		self.getAllBmTechs();
	},
	methods: {
		getAllBmTechs: function () {
			this.getBmTechs();
			this.getGlobalClassBmTechs(this.fundInfo.globalClassBmCode);
			this.getLocalClassBmTechs(this.fundInfo.localClassBmCode);
		},
		getBmTechs: async function () {
			const self = this;
			const managerBmCode = self.fundInfo.managerBmCode;
			const analysisBmCode = self.fundInfo.analysisBmCode;
			let bmCode;

			if (managerBmCode && managerBmCode != '11000006' && managerBmCode != '11000000') {
				self.bmName = self.fundInfo.managerBmName;
				bmCode = managerBmCode;
			}
			else if (analysisBmCode) {
				self.bmName = self.fundInfo.analysisBmName;
				bmCode = analysisBmCode;
			}
			const ret = await this.$api.getTechsApi({
				proCode: bmCode,
				techCurrencyCode: self.techCurrencyCode
			});
			self.bmTechs = ret.data;
		},
		getGlobalClassBmTechs: async function (globalClassBmCode) {
			const self = this;
			const ret = await this.$api.getTechsApi({
				proCode: globalClassBmCode,
				techCurrencyCode: self.techCurrencyCode
			});
			self.globalClassBmTechs = ret.data;
		},
		getLocalClassBmTechs: async function (localClassBmCode) {
			const self = this;
			const ret = await this.$api.getTechsApi({
				proCode: localClassBmCode,
				techCurrencyCode: self.techCurrencyCode
			});

			self.localClassBmTechs = ret.data;
		},
		getAllPerfRank: function () {
			const self = this;
			self.ranks = {};
			_.forEach(self.rankTechs, function (item) {
				self.getPerfRank(item);
			});
		},
		getPerfRank: async function (statCode) {
			const self = this;
			let fundPerfRankCode;
			switch (self.selectedCompareType) {
				case 'GLOBAL_CLASS':
					fundPerfRankCode = self.fundInfo.globalClassCode;
					break;
				case 'LOCAL_CLASS':
					fundPerfRankCode = self.fundInfo.localClassCode;
					break;
			}

			const ret = await this.$api.getPerfRankApi({
				techCurrencyCode: self.techCurrencyCode,
				fundCode: self.fundInfo.fundCode || '',
				statCode: statCode,
				fundPerfRank: self.selectedCompareType,
				fundPerfRankCode: fundPerfRankCode || ''
			});

			self.ranks[statCode] = ret.data;
		},
		getRank: function (item, field) {
			return item ? item[field] : '--';
		},
		getRankTopStyle: function (item, field) {
			return item ? item[field] : 100;
		},
		getTech: function (techs, statCode) {
			const tech = _.find(techs, { statCode: statCode });
			if (tech) {
				return numeral(tech.dvalue).format('0.00');
			}
			else {
				return '--';
			}
		}
	}
};
</script>
<style scoped>
body {
	background: #f7f7f7;
}

.card .card-header {
	border-bottom: 1px solid #f0f2f4;
}

.card .card-header .card-title {
	font-size: 28px;
	letter-spacing: 0.025em;
}

.card .card-header p {
	font-size: 13px;
}

.card .card-header p a.link {
	color: #333;
	opacity: 1;
	text-decoration: underline;
}

h4 {
	font-weight: bold;
	margin-bottom: 5px;
	position: relative;
	color: #006193;
	font-size: 16px;
}

h4 .small-text {
	position: absolute;
	right: 10px;
	top: 5px;
}

.modal-title {
	font-size: 22px;
	color: #3b3b3b;
	font-weight: 500;
	line-height: 1.5;
	letter-spacing: -0.00865734em;
	margin: 10px 0;
}

.row {
	margin-left: 0;
	margin-right: 0;
}

[class*='col-'] {
	padding: 0;
}

.form-group [class*='col-'] {
	padding: 0 5px;
}

.col-left {
	margin-top: 25px;
}

.col-right {
	padding-left: 0px;
	margin-top: 25px;
}

.col-right .table {
	margin-top: 0;
}

@media (min-width: 992px) {
	.col-right {
		padding-left: 20px;
	}
}

.table-bordered th {
	background: #f0f2f4;
	border: 1px solid #fff;
}

.table-bordered td {
	word-break: break-all;
}

.table-bordered a {
	text-decoration: underline;
	margin-bottom: 5px;
	display: inline-block;
	margin-right: 15px;
}

.table-bordered a:last-child {
	margin-right: 0;
}

.table-bordered a:hover {
	background: rgba(225, 229, 236, 0.2);
}

.table-bordered.four-grid td {
	height: 60px;
	font-weight: bold;
	font-size: 20px;
	vertical-align: middle;
	text-align: center;
	background: #f0f2f4;
	border: 3px solid #fff;
}

.table-condensed th,
.table-condensed td {
	font-size: 11px;
	padding: 5px !important;
	white-space: normal !important;
	vertical-align: middle;
	overflow: visible !important;
	text-overflow: none !important;
	word-break: break-all !important;
}

.table.table-condensed tbody tr td *:not(.dropdown-default) {
	word-break: break-all !important;
	white-space: normal;
}

/*table-score*/
.table-score {
	position: relative;
	margin-top: 20px;
}

.table-score:before {
	content: '';
	background: url([[@{/image/fund/arrow-d.png}]]) no-repeat center;
	background-size: contain;
	width: 16px;
	height: 16px;
	position: absolute;
	left: 30%;
	top: -20px;
}

.table-score th:nth-child(2) {
	background: #d6e3f8;
	color: #006193;
}

.table-score td:nth-child(2) {
	background: #e3ecf4;
	color: #006193;
}

.table-Hfix {
	width: 100%;
}

.table-Hfix td {
	height: 30px;
	vertical-align: middle;
}

.table-Hfix td:first-child {
	width: 130px;
}

.table-Hfix td:last-child {
	width: 50px;
	padding-left: 10px;
}

.table-Hfix .progress {
	margin-bottom: 0;
	height: 10px;
}

.chart-size {
	width: 100%;
	min-width: 310px;
	height: 385px;
	margin: 0 auto;
}

.barnum {
	display: block;
	width: 65px;
	height: 70px;
	padding: 0;
	margin: 10px auto;
	position: relative;
}

.barnum-bg {
	position: absolute;
	top: 0;
	left: 20px;
	right: 0;
	background: url([[@{/images/fund/bar-bg.jpg}]]) no-repeat;
	width: 50px;
	height: 70px;
}

.barnum-txt-top {
	font-size: 10px;
	color: #888;
	position: absolute;
	width: 15px;
	top: -10px;
	text-align: right;
}

.barnum-txt-bottom {
	font-size: 10px;
	color: #888;
	position: absolute;
	bottom: -20px;
	text-align: right;
}

.barnum-txt-cur {
	font-size: 10px;
	color: #006193;
	position: absolute;
	left: -20px;
	text-align: right;
	padding-right: 10px;
	margin-top: -7px;
	background: url([[@{/images/fund/arrow.png}]]) no-repeat center right;
}

.select {
	color: #444;
	line-height: 28px;
	height: 35px;
	background-color: #fff;
	border: 1px solid #aaa;
	border-radius: 4px;
	padding: 2px 9px;
}

ul.note {
	list-style: none;
	padding-left: 0;
	font-size: 12px;
	color: #777;
}

.note p {
	margin-bottom: 0;
}

.note p.tx-title {
	font-weight: bold;
	margin-bottom: 5px;
}

.note li {
	margin-bottom: 5px;
}

.note ul {
	padding-left: 20px;
}

.note ul li {
	list-style: disc;
	margin-bottom: 0;
}
</style>
