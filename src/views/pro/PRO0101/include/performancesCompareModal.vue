<template>
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('pro.performanceComparisonChart') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click="close()"
				/>
			</div>
			<div class="card-body">
				<div v-if="pfcatCode === 'etf'">
					<div class="row">
						<div class="col-sm-2">
							<label class="tc-blue">選擇ETF(同全球分類)：</label>
						</div>
						<div class="col-sm-8">
							<div class="input-group">
								<select v-model="proCode1" class="form-select">
									<option selected value>
										請選擇
									</option>
									<option v-for="item in etfProfileNameMenu" :value="item.proCode">
										{{ item.nameFull }} {{ item.proCode }}
									</option>
								</select>
							</div>
						</div>
						<div class="col-sm-2">
							<p>
								<input
									class="btn btn-primary text-alignRight"
									type="button"
									value="加入"
									@click="addProEtf(proCode1)"
								>
							</p>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-2">
							<label class="tc-blue">選擇對應指數：</label>
						</div>
						<div class="col-sm-8">
							<div class="input-group">
								<select v-model="proCode2" class="form-select">
									<option selected value>
										請選擇
									</option>
									<option v-for="item in etfProfileBenchmarksMenu" :value="item.proCode">
										{{ item.benchmarkName }} {{ item.proCode }}
									</option>
								</select>
							</div>
						</div>
						<div class="col-sm-2">
							<p>
								<input
									class="btn btn-primary text-alignRight"
									type="button"
									value="加入"
									@click="addProEtf(proCode2)"
								>
							</p>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-2">
							<label class="tc-blue">選擇全球分類：</label>
						</div>
						<div class="col-sm-8">
							<div class="input-group">
								<select v-model="proCode3" class="form-select">
									<option selected value>
										請選擇
									</option>
									<option v-for="item in etfPerformanceClassMajorMenu" :value="item.proCode">
										{{ item.className }} {{ item.proCode }}
									</option>
								</select>
							</div>
						</div>
						<div class="col-sm-2">
							<p>
								<input
									class="btn btn-primary text-alignRight"
									type="button"
									value="加入"
									@click="addProEtf(proCode3)"
								>
							</p>
						</div>
					</div>
				</div>
				<div v-else-if="pfcatCode === 'fund'">
					<div class="row">
						<div class="col-sm-2">
							<label class="tc-blue">選擇基金(同全球分類)：</label>
						</div>
						<div class="col-sm-8">
							<div class="input-group">
								<select v-model="proCode1" class="form-select">
									<option selected value>
										請選擇
									</option>
									<option v-for="item in globalFundsMenu" :value="item.fundCode">
										{{ item.fundCname }}
									</option>
								</select>
							</div>
						</div>
						<div class="col-sm-2">
							<p>
								<input
									id="fundBtn1"
									class="btn btn-primary text-alignRight"
									type="button"
									value="加入"
									@click="addProfund(proCode1)"
								>
							</p>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-2">
							<label class="tc-blue">選擇對應指數：</label>
						</div>
						<div class="col-sm-8">
							<div class="input-group">
								<select v-model="proCode2" class="form-select">
									<option selected value>
										請選擇
									</option>
									<option v-for="item in fundAssetMenu" :value="item.lipperCode">
										{{ item.cname }}
									</option>
								</select>
							</div>
						</div>
						<div class="col-sm-2">
							<p>
								<input
									id="fundBtn2"
									class="btn btn-primary text-alignRight"
									type="button"
									value="加入"
									@click="addProfund(proCode2)"
								>
							</p>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-2">
							<label class="tc-blue">選擇全球分類：</label>
						</div>
						<div class="col-sm-8">
							<div class="input-group">
								<select v-model="proCode3" class="form-select">
									<option selected value>
										請選擇
									</option>
									<option v-for="item in fundGlobalAssetMenu" :value="item.lipperCode">
										{{ item.cname }}
									</option>
								</select>
							</div>
						</div>
						<div class="col-sm-2">
							<p>
								<input
									id="fundBtn3"
									class="btn btn-primary text-alignRight"
									type="button"
									value="加入"
									@click="addProfund(proCode3)"
								>
							</p>
						</div>
					</div>
				</div>

				<div v-else class="row">
					<div class="col-sm-2">
						<label class="tc-blue">選擇商品：</label>
					</div>
					<div v-if="pfcatCode === 'bond'" class="col-sm-8">
						<div class="input-group">
							<select v-model="issuerCode" class="form-select" @change="getBondMenu(issuerCode)">
								<option selected value>
									請選擇
								</option>
								<option v-for="item in issuersMenu" :value="item.issuerCode">
									{{ item.issuerName }}
								</option>
							</select>
							<select v-model="proCode" class="form-select">
								<option selected value>
									請選擇
								</option>
								<option v-for="item in productList" :value="item.proCode">
									{{ item.proName }} {{ item.proCode }}
								</option>
							</select>
						</div>
					</div>

					<div v-if="pfcatCode === 'dci' || pfcatCode === 'sec' || pfcatCode === 'sp'" class="col-sm-8">
						<div class="input-group">
							<select v-model="proCode" class="form-select">
								<option selected value>
									請選擇
								</option>
								<option v-for="item in productList" :value="item.proCode">
									{{ item.proName }}
								</option>
							</select>
						</div>
					</div>
					<div class="col-sm-2">
						<p>
							<input
								class="btn btn-primary text-alignRight"
								type="button"
								value="加入"
								@click="addPro()"
							>
						</p>
					</div>
				</div>

				<div class="caption">
					已加入商品
				</div>
				<div v-if="pfcatCode === 'fund'" class="table-responsive mb-3">
					<table class="table table-bordered">
						<thead>
							<tr>
								<th>基金代碼</th>
								<th>基金名稱</th>
								<th>計價幣別</th>
								<th>淨值</th>
								<th>一年報酬率</th>
								<th>三年報酬率</th>
								<th>五年報酬率</th>
								<th>今年以來累積報酬率</th>
								<th>1年標準差</th>
								<th class="text-center">
									動作
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="item in proItem">
								<td>{{ $filters.defaultValue(item.bankProCode, '--') }}</td>
								<td>{{ $filters.defaultValue(item.fundName, '--') }}</td>
								<td>{{ $filters.defaultValue(item.curCode, '--') }}</td>
								<td>{{ $filters.formatNumber(item.priceLc, '0,0.00' || '--') }}</td>
								<td>{{ $filters.defaultValue($filters.formatPct(item.value1Y) + ' %', '--') }}</td>
								<td>{{ $filters.defaultValue($filters.formatPct(item.value3Y) + ' %', '--') }}</td>
								<td>{{ $filters.defaultValue($filters.formatPct(item.value5Y) + ' %', '--') }}</td>
								<td>{{ $filters.defaultValue($filters.formatPct(item.valueYtd) + ' %', '--') }}</td>
								<td>{{ $filters.formatNumber(item.valueStd, '0,0.00' || '--') }}</td>
								<td class="text-center">
									<button
										type="button"
										class="btn btn-danger btn-icon"
										data-bs-toggle="tooltip"
										title="刪除"
										@click="deletePro(item.lipperId)"
									>
										<i class="fa-solid fa-trash" />
									</button>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
				<div v-else-if="pfcatCode === 'etf'" class="table-responsive mb-3">
					<table class="table table-bordered">
						<thead>
							<tr>
								<th>ETF代碼</th>
								<th>ETF名稱</th>
								<th>計價幣別</th>
								<th>一年報酬率</th>
								<th>三年報酬率</th>
								<th>五年報酬率</th>
								<th>今年以來累積報酬率</th>
								<th>1年標準差</th>
								<th class="text-center">
									動作
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="item in proItem">
								<td>{{ item.lipperId }}</td>
								<td>{{ item.nameFull }}</td>
								<td>{{ item.currency }}</td>
								<td>{{ $filters.formatPct(item.value1Y) }}%</td>
								<td>{{ $filters.formatPct(item.value3Y) }}%</td>
								<td>{{ $filters.formatPct(item.value5Y) }}%</td>
								<td>{{ $filters.formatPct(item.valueYtd) }}%</td>
								<td>{{ item.valueStd }}</td>
								<td class="text-center">
									<button
										type="button"
										class="btn btn-danger btn-icon"
										data-bs-toggle="tooltip"
										title="刪除"
										@click="deletePro(item.lipperId)"
									>
										<i class="fa-solid fa-trash" />
									</button>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
				<div v-else class="table-responsive mb-3">
					<table class="table table-bordered">
						<thead>
							<tr>
								<th>商品名稱</th>
								<th v-if="pfcatCode === 'bond'">
									票面利率
								</th>
								<th v-if="pfcatCode === 'bond'">
									配息頻率
								</th>
								<th v-if="pfcatCode === 'bond'">
									1年報酬率
								</th>
								<th v-if="pfcatCode === 'bond'">
									今年以來累積報酬率
								</th>
								<th v-if="pfcatCode === 'dci' || pfcatCode === 'sec' || pfcatCode === 'sp'">
									報酬率
								</th>
								<th class="text-center">
									動作
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="item in proItem">
								<td>{{ item.proName }}</td>
								<td v-if="pfcatCode === 'bond'">
									{{ $filters.formatPct(item.parRate) }}%
								</td>
								<td v-if="pfcatCode === 'bond'">
									{{ item.intFreqName }}
								</td>
								<td v-if="pfcatCode === 'bond'">
									{{ $filters.formatPct(item.fc1YReturn) }}%
								</td>
								<td v-if="pfcatCode === 'bond'">
									{{ $filters.formatPct(item.fcYtdReturn) }}%
								</td>
								<td v-if="pfcatCode === 'dci' || pfcatCode === 'sec' || pfcatCode === 'sp'">
									{{ $filters.formatPct(item.fcTdReturn) }}%
								</td>
								<td class="text-center">
									<button
										type="button"
										class="btn btn-danger btn-icon"
										data-bs-toggle="tooltip"
										title="刪除"
										@click="deletePro(item.proCode)"
									>
										<i class="fa-solid fa-trash" />
									</button>
								</td>
							</tr>
						</tbody>
					</table>
				</div>

				<div class="text-center">
					<!-- 績效分析圖表  -->
					<vue-performances-chart ref="PerformancesCompareChartRef" :chart-id="performancesId" />
					<div v-if="pfcatCode !== 'fund'" class="btn-group btn-group-sm mb-4" role="group">
						<template v-for="item in proPriceRangeMenu">
							<input
								:id="'performancesPeriod' + item.termValue"
								type="radio"
								class="btn-check"
								name="time"
								:checked="item.termValue == '4' ? true : false"
								@click="getPerformances(proCodes, item.rangeType, item.rangeFixed)"
							>
							<label class="btn btn-outline-secondary" :for="'performancesPeriod' + item.termValue">{{ item.termName }}</label>
						</template>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<input
					id="modalCloseButton"
					type="button"
					class="btn btn-white"
					value="關閉"
					@click.prevent="close()"
					@click="close()"
				>
			</div>
		</div>
	</div>
	<!-- Modal performances Compare End -->
</template>
<script>
import _ from 'lodash';
import vuePerformancesChart from './performancesChart.vue';
export default {
	components: {
		vuePerformancesChart
	},
	data: function () {
		return {
			proPriceRangeMenu: [],

			pfcatCode: null, // 類型代碼
			issuerCode: null, // 發行機構
			proCode: null, // 商品名稱
			proCode1: '', // etf用、fund用 選擇ETF 選擇基金(同全球分類) 商品名稱
			proCode2: '', // etf用、fund用 選擇對應指數 商品名稱
			proCode3: '', // etf用、fund用 選擇全球分類 商品名稱
			proCodes: [], // 選擇的債券代碼
			lipperIds: [], // 選擇的選取代碼

			issuersMenu: [], // 發行機構 下拉選項
			productList: [], // 選擇要加入的商品
			observedProList: [], // 加入商品清單

			// 基金
			globalFundsMenu: [], // 選擇基金(同全球分類)下拉
			fundAssetMenu: [], // 選擇對應指數下拉
			fundGlobalAssetMenu: [], // 選擇全球分類下拉

			// ETF
			etfProfileNameMenu: [], // 選擇基金(同全球分類)下拉
			etfProfileBenchmarksMenu: [], // 選擇對應指數下拉
			etfPerformanceClassMajorMenu: [], // 選擇全球分類下拉

			proItem: [], // 商品資料
			chartsData: [], // 商品資料
			performancesId: 'performancesChartId' // 比較圖製圖id
		};
	},
	watch: {},
	mounted: function () {
		const self = this;
		self.getProPriceRangeMenu(); // 價格/淨值顯示區間
	},
	methods: {
		// 選擇債券-發行機構選單
		getBondIssuersMenu: async function () {
			const self = this;
			const res = await this.$api.getBondIssuersMenuApi();
			self.issuersMenu = res.data;
		},
		// fund績效比較圖 選擇基金(同全球分類)下拉
		getFundProfileNameMenu: async function () {
			const self = this;
			const res = await this.$api.getGlobalFundsApi({ lipperIds: self.lipperIds.join() });

			if (!_.isNil(res.data)) {
				self.globalFundsMenu = res.data;
			}
			self.proCode1 = '';
		},
		// fund績效比較圖-對應指數下拉 下拉
		getFundProfileBenchmarksMenu: async function () {
			const self = this;
			const res = await this.$api.getFundProfileBenchmarksMenuApi({ lipperIds: self.lipperIds.join() });
			if (!_.isNil(res.data)) {
				self.fundAssetMenu = res.data;
			}
			self.proCode2 = '';
		},
		// fund績效比較圖-選擇全球分類下拉
		getFundPerformanceClassMajorMenu: async function () {
			const self = this;
			const res = await this.$api.getFundPerformanceClassMajorMenuApi({ lipperIds: self.lipperIds.join() });
			if (!_.isNil(res.data)) {
				self.fundGlobalAssetMenu = res.data;
			}
			self.proCode3 = '';
		},
		// ETF績效比較圖-選擇ETF(同全球分類)下拉
		getEtfProfileNameMenu: async function () {
			const self = this;
			const res = await this.$api.getEtfProfileNameMenuApi();
			if (!_.isNil(res.data)) {
				self.etfProfileNameMenu = res.data;
			}
		},
		// ETF績效比較圖-對應指數下拉
		getEtfProfileBenchmarksMenu: async function () {
			const self = this;
			const res = await this.$api.getEtfProfileBenchmarksMenuApi();
			if (!_.isNil(res.data)) {
				self.etfProfileBenchmarksMenu = res.data;
			}
		},
		// ETF績效比較圖-選擇全球分類下拉
		getEtfPerformanceClassMajorMenu: async function () {
			const self = this;
			const res = await this.$api.getEtfPerformanceClassMajorMenuApi();
			if (!_.isNil(res.data)) {
				self.etfPerformanceClassMajorMenu = res.data;
			}
		},
		// 選擇債券 選擇商品下拉
		getBondMenu: async function () {
			const self = this;
			const payload = {
				pfcatCode: 'FB',
				issuerCode: self.issuerCode
			};
			const res = await this.$api.getProductByPfcatCodeApi(payload);
			self.productList = res.data;
		},
		// 績效分析 選擇商品下拉
		getProCodeMenu: async function (pfcatCode) {
			const self = this;
			const res = await this.$api
				.getProductByPfcatCodeApi({
					pfcatCode: pfcatCode
				})
				.then(function (ret) {
					const pfcats = {
						sp: 'SP_',
						dci: 'DCI_',
						sec: 'SEC_'
					};

					if (pfcatCode in pfcats) {
						const prefix = pfcats[pfcatCode];
						self.productList = ret.data.filter((product) => {
							return self.proCodes.includes(product.proCode);
						});
					}
					else {
						self.productList = ret.data;
					}

					return self.productList;
				});
		},
		// 已加入商品清單
		comparePropItem: async function (proCodes, pfcatCode) {
			var self = this;
			let url = '';
			self.proCodes = proCodes;
			if (pfcatCode != null) {
				self.pfcatCode = pfcatCode;
				// 債券
				if (pfcatCode === 'bond') {
					self.getBondIssuersMenu(); // 債券-發行機構選單
					url = '/pro/bond/observedBonds';
					// 組合式商品、兼營證券自營商品
				}
				else if (pfcatCode === 'dci' || pfcatCode === 'sec' || pfcatCode === 'sp') {
					self.getProCodeMenu(pfcatCode); // 組合式商品-商品清單來源
					url = '/pro/observedProducts';
				}
			}
			// 績效比較圖-已加入商品
			var self = this;
			const res = await this.$api.comparePropItemApi({
				proCodes: proCodes.join(),
				url: url
			});
			self.proItem = res.data;
			self.getPerformances(proCodes, 'Y', -1.0); // 商品資訊/績效分析圖表
		},
		// 基金 已加入商品清單 傳lipperIds
		comparefundPropItem: async function (lipperIds) {
			var self = this;
			const url = '';
			self.lipperIds = lipperIds;
			self.pfcatCode = 'fund';
			// fund
			await self.getFundProfileNameMenu(); // fund-選擇基金(同全球分類)下拉
			await self.getFundProfileBenchmarksMenu(); // fund-對應指數下拉
			await self.getFundPerformanceClassMajorMenu(); // fund-選擇全球分類下拉
			// 績效比較圖-已加入商品
			var self = this;
			const res = await this.$api.observedFundsApi({
				lipperIds: lipperIds.join()
			});
			self.proItem = res.data;
			// -商品歷史績效走勢圖
			var self = this;
			const res2 = await this.$api.fundRunChartApi({
				lipperIds: lipperIds.join() // 顯示區間數值
			});
			if (!_.isEmpty(res.data)) {
				for (let i = 0; i < res.data.length; i++) {
					res.data[i].datas.forEach((e) => {
						e.date = Date.parse(e.date);
					});
				}
			}
			self.chartsData = res.data;
			self.$refs.PerformancesCompareChartRef.initChart(self.chartsData);
		},
		// ETF 已加入商品清單 傳lipperIds
		compareEtfPropItem: async function (proCodes, lipperIds) {
			var self = this;
			const url = '';
			self.lipperIds = lipperIds;
			self.proCodes = proCodes;
			self.pfcatCode = 'etf';
			// etf
			self.getEtfProfileNameMenu(); // ETF-選擇ETF(同全球分類)下拉
			self.getEtfProfileBenchmarksMenu(); // ETF-對應指數下拉
			self.getEtfPerformanceClassMajorMenu(); // ETF-選擇全球分類下拉
			// 績效比較圖-已加入商品
			var self = this;
			const res = await this.$api.getObservedEtfsApi({
				lipperIds: lipperIds.join()
			});
			self.proItem = res.data;
			self.getPerformances(proCodes, 'Y', -1.0); // 商品資訊/績效分析圖表
		},
		// 績效分析 圖表
		getPerformances: function (proCodes, rangeType, rangeFixed) {
			const self = this;
			const payload = {
				proCodes: proCodes,
				freqType: rangeType, // 示區間類型
				freqFixed: rangeFixed // "顯示區間數值
			};
			const ret = this.$api.getPerformanceRunChartApi(payload);

			if (!_.isEmpty(ret.data)) {
				for (let i = 0; i < ret.data.length; i++) {
					ret.data[i].datas.forEach((e) => {
						e.value = e.returnFc;
						e.date = new Date(e.returnDt).getTime();
					});
				}
				self.chartsData = ret.data;
				self.$refs.PerformancesCompareChartRef.initChart(self.chartsData);
			}
			else {
				this.$bi.alert('無績效資料可生成圖表');
			}
		},
		// 績效分析 選擇商品 加入按鈕
		addPro() {
			const self = this;
			let pk = null;
			pk = _.find(self.proCodes, function (item) {
				return item == self.proCode;
			});
			if (self.proCode != null && pk == null) {
				if (self.proCodes.length == 6) {
					this.$bi.alert('最多加入6筆');
				}
				else {
					self.proCodes.push(self.proCode); // 加入選擇商品
					self.comparePropItem(self.proCodes, self.pfcatCode); // 績效分析 取得 已加入商品清單
				}
			}
			else if (self.proCode != null && pk != null) {
				this.$bi.alert('此商品已加入');
			}
			else {
				this.$bi.alert('請選擇商品');
			}
		},
		// 績效分析 選擇商品 加入按鈕
		addProfund(fundCode) {
			const self = this;
			let pk = null;
			pk = _.find(self.lipperIds, function (item) {
				return item == fundCode;
			});
			if (self.lipperIds != null && pk == null) {
				if (self.lipperIds.length == 6) {
					this.$bi.alert('最多加入6筆');
				}
				else {
					self.lipperIds.push(fundCode); // 加入選擇商品
					self.comparefundPropItem(self.lipperIds); //  取得 已加入商品清單
				}
			}
			else if (self.lipperIds != null && pk != null) {
				this.$bi.alert('此商品已加入');
			}
			else {
				this.$bi.alert('請選擇商品');
			}
		},
		// ETF 績效分析 選擇商品 加入按鈕
		addProEtf(proCode) {
			// proCode1、proCode2、proCode3
			const self = this;
			let pk = null;
			pk = _.find(self.proCodes, function (item) {
				return item == proCode;
			});
			if (proCode != null && pk == null) {
				if (self.proCodes.length == 6) {
					this.$bi.alert('最多加入6筆');
				}
				else {
					self.proCodes.push(proCode); // 加入選擇商品
					self.comparePropItem(self.proCodes, self.pfcatCode); // 績效分析 取得 已加入商品清單
				}
			}
			else if (proCode != null && pk != null) {
				this.$bi.alert('此商品已加入');
			}
			else {
				this.$bi.alert('請選擇商品');
			}
		},
		// 績效分析 已加入商品 刪除按鈕
		deletePro(proCode) {
			const self = this;
			if (self.pfcatCode === 'fund') {
				if (self.lipperIds.length > 1) {
					const index = self.lipperIds.indexOf(proCode); // 找出要移除的index
					self.lipperIds.splice(index, 1); // 移除加入的商品(要插入或刪除的索引位置, 要刪除的元素數量)
					self.comparefundPropItem(self.lipperIds); //  取得 已加入商品清單
				}
				else {
					this.$bi.alert('至少要有一項商品');
				}
			}
			else {
				if (self.proCodes.length > 1) {
					const index = self.proCodes.indexOf(proCode); // 找出要移除的index
					self.proCodes.splice(index, 1); // 移除加入的商品(要插入或刪除的索引位置, 要刪除的元素數量)
					self.comparePropItem(self.proCodes, self.pfcatCode); //  取得 已加入商品清單
				}
				else {
					this.$bi.alert('至少要有一項商品');
				}
			}
		},
		// 取得 價格/淨值顯示區間
		getProPriceRangeMenu: async function () {
			const self = this;
			const res = await this.$api.getProPriceRangeMenuApi();
			self.proPriceRangeMenu = res.data;
		},
		close() {
			const self = this;
			self.issuerCode = null;
			self.proCode = null;
		},
		handleModalClick(event) {
			if (event.target === event.currentTarget) {
				this.closeModal();
			}
		},
		closeModal() {
			const self = this;
			self.lipperIds = [];
			self.globalFundsMenu = [];
			self.fundAssetMenu = [];
			self.fundGlobalAssetMenu = [];
		}
	}
	// methods end
};
</script>
