<template>
	<div class="tab-content">
		<div class="tab-pane fade show active">
			<!--頁面內容 結構型商品sp start-->
			<div class="tab-nav-tabs my-3">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'common' }"
							data-bs-toggle="tab"
							@click="changeTab('common')"
						>{{ $t('pro.generalFilter') }}</a>
					</li>
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'fast' }"
							data-bs-toggle="tab"
							@click="changeTab('fast')"
						>{{ $t('pro.quickFilter') }}</a>
					</li>
				</ul>

				<div class="tab-content">
					<div id="SectionA" class="tab-pane fade show" :class="{ 'active show': activeTab == 'common' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>{{ $t('pro.searchCond') }}</h4>
								<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
							</div>
							<div id="collapseListGroup1" class="collapse show">
								<div class="card-body">
									<form>
										<div class="form-row">
											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> {{ $t('pro.productCode') }} </label>
												<input
													id="prod_bank_pro_code"
													v-model="bankProCode"
													class="form-control"
													maxlength="20"
													size="25"
													type="text"
													value=""
												>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> {{ $t('pro.productName') }}</label>
												<input
													id="prod_pro_name"
													v-model="proName"
													class="form-control"
													maxlength="20"
													size="45"
													type="text"
													value=""
												>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> {{ $t('pro.productType') }}</label>
												<select
													id="proType"
													v-model="proTypeCode"
													class="form-select"
													name="proType"
													:title="$t('pro.pleaseSelect') + $t('pro.productType')"
													data-style="btn-white"
												>
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="item in proTypeMenu" :value="item.proTypeCode">
														{{ $filters.defaultValue(item.proTypeName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> {{ $t('pro.currency') }}</label>
												<select
													id="curMenuSp"
													v-model="curObjs"
													class="selectpicker form-control"
													multiple
													:title="$t('pro.pleaseSelectCurrency')"
													data-style="btn-white"
												>
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="(item, index) in curOption" :key="index" :value="item.value">
														{{ $filters.defaultValue(item.name, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> {{ $t('pro.riskLevel') }} </label>
												<select
													id="riskCode"
													v-model="riskCode"
													class="form-select"
													name="riskCode"
												>
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="item in riskMenu" :value="item.riskCode">
														{{ $filters.defaultValue(item.riskName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label"> {{ $t('pro.guaranteedPrincipal') }} </label>

												<select id="guarType" v-model="guarType" class="form-select">
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="(item, index) in guarTypeMenu" :key="index" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-6">
												<label class="form-label">{{ $t('pro.dividendFrequency') }}</label>
												<select v-model="intFreqUnitType" name="select" class="form-select">
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="intFreqUnit in intFreqUnitMenu" :value="intFreqUnit.codeValue">
														{{ $filters.defaultValue(intFreqUnit.codeName, '--') }}
													</option>
												</select>
											</div>

											<div class="form-group col-12 col-lg-6">
												<label class="form-label">{{ $t('pro.remainingYears') }}</label>
												<div class="input-group">
													<input
														v-model="startDay"
														type="text"
														class="form-control"
														size="5"
													>
													<div class="input-group-text">
														~
													</div>
													<input
														v-model="endDay"
														type="text"
														class="form-control"
														size="5"
													>
												</div>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.linkedUnderlying') }}</label>
												<button type="button" class="btn btn-primary" @click="groupTargetModalHandler()">
													{{ $t('pro.selectLinkedUnderlying') }}
												</button>
												<vue-modal :is-open="isOpenTargetModal" @close="isOpenTargetModal = false">
													<template #content="props">
														<vue-group-target-modal
															id="groupTargetModal"
															ref="groupTargetModalRef"
															:close="props.close"
															:target-prop="targetItem"
															@selected="selectedTarget"
														/>
													</template>
												</vue-modal>
											</div>

											<div class="form-group col-12 col-lg-4">
												<label class="form-label">{{ $t('pro.issuer') }}</label>
												<button type="button" class="btn btn-primary" @click="groupIssuerModalHandler()">
													{{ $t('pro.selectIssuer') }}
												</button>
												<vue-modal :is-open="isOpenIssuerModal" @close="isOpenIssuerModal = false">
													<template #content="props">
														<vue-group-issuer-modal
															id="groupIssuerModal"
															ref="groupIssuerModalRef"
															:close="props.close"
															:pfcat-code="'SP'"
															@selected="selectedIssuer"
														/>
													</template>
												</vue-modal>
											</div>
										</div>

										<div class="form-row d-flex align-items-start">
											<div class="col-4">
												<div v-for="item in targetItem" style="padding-left: 110px; padding-bottom: 15px">
													<span class="form-check-label"> {{ $filters.defaultValue(item.stockCode, '--') }}</span>
													<a href="#" @click="deleteTargetItem(item.stockCode)"><img
														:src="getImgURL('icon', 'i-cancel.png')"
													></a>
												</div>
											</div>

											<div class="col-8">
												<div v-for="item in issuerItem" style="padding-left: 115px; padding-bottom: 15px">
													<span class="form-check-label"> {{ $filters.defaultValue(item.issuerName, '--') }}</span>
													<a href="#" @click="deleteIssuerItem(item.issuerCode)"><img
														:src="getImgURL('icon', 'i-cancel.png')"
													></a>
												</div>
											</div>
										</div>

										<div class="form-footer">
											<button class="btn btn-primary" @click.prevent="gotoPage(0)">
												{{ $t('pro.search') }}
											</button>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>

					<div id="SectionB" class="tab-pane fade" :class="{ 'active show': activeTab == 'fast' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
								<h4>{{ $t('pro.searchCond') }}</h4>
								<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
							</div>
							<div id="collapseListGroup2" class="collapse show">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-12">
											<label class="form-label tx-require"> {{ $t('pro.filterConditions') }} </label>
											<div v-for="item in spFastMenu" class="form-check-group" @change="fastChange(item.codeValue)">
												<input
													:id="'fast' + item.codeValue"
													v-model="fastCode"
													class="form-check-input"
													name="fastCode"
													:value="item.codeValue"
													type="radio"
												>
												<label class="form-check-label" :for="'fast' + item.codeValue">{{
													$filters.defaultValue(item.codeName, '--')
												}}</label>
											</div>
										</div>
										<div id="rangeFixedTr" class="form-group col-12 col-lg-6" style="display: none">
											<label class="form-label tx-require"> {{ $t('pro.displayRange') }}</label>

											<select id="prod_protype_code" v-model="timeRange" class="form-select">
												<option v-for="item in timeRangeMenu" :value="item">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>

										<div id="proPerfTimeTr" class="form-group col-12 col-lg-6" style="display: none">
											<label class="form-label tx-require">{{ $t('pro.targetPerformance') }} </label>

											<select id="vfAstStat_stat_code" v-model="perf" class="form-select">
												<option v-for="item in perfMenu" :value="item.codeValue">
													{{ $filters.defaultValue(item.codeName, '--') }}
												</option>
											</select>
										</div>

										<div id="maxRowIdTr" class="form-group col-12 col-lg-6" style="display: none">
											<label class="form-label tx-require">{{ $t('pro.displayRecordCount') }}</label>
											<select id="maxRowId" v-model="rowNumber" class="form-select">
												<option value="">
													{{ $t('pro.all') }}
												</option>
												<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>
									</div>
									<div class="form-footer">
										<button class="btn btn-primary" @click.prevent="gotoFastPage(0)">
											{{ $t('pro.search') }}
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div v-if="pageData.content.length > 0" id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('pro.searchResults') }}</h4>
						<div style="display: flex">
							<vue-pagination :pageable="pageData" :goto-page="gotoPage" /><button
								type="button"
								class="btn btn-info ms-2"
								@click="performancesCompareModelHandler()"
							>
								{{ $t('pro.performanceComparison') }}
							</button>
							<vue-modal :is-open="isOpenCompareModal" @close="isOpenCompareModal = false">
								<template #content="props">
									<vue-performances-compare-modal
										id="performancesCompareModal"
										ref="performancesCompareModalRef"
										:close="props.close"
									/>
								</template>
							</vue-modal>
						</div>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-bordered text-center">
							<thead>
								<tr>
									<th class="wd-100 text-start">
										{{ $t('pro.joinComparison') }}
									</th>
									<th>{{ $t('pro.productCode') }}</th>
									<th class="10% text-start">
										{{ $t('pro.productChineseName') }}
									</th>
									<th>{{ $t('pro.currency') }}</th>
									<th>{{ $t('pro.riskLevel') }}</th>
									<th>{{ $t('pro.referenceRedemptionPrice') }}</th>
									<th>{{ $t('pro.referenceNetValue') }}</th>
									<th>{{ $t('pro.dividendFrequency') }}</th>
									<th>{{ $t('pro.maturityDate') }}</th>
									<th>{{ $t('pro.linkedUnderlying') }}</th>
									<th class="text-center" width="120">
										{{ $t('pro.execute') }}
									</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(item, index) in pageData.content">
									<td :data-th="$t('pro.joinComparison')" class="text-start text-center">
										<input
											:id="'id-' + item.bankProCode"
											v-model="selectedItems[item.proCode]"
											class="form-check-input text-center"
											type="checkbox"
										>
										<label class="form-check-label" :for="'id-' + item.bankProCode" />
									</td>
									<td :data-th="$t('pro.productCode')">
										<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
									</td>
									<td class="text-start" :data-th="$t('pro.productChineseName')">
										<span>
											<a class="tx-link" @click="spModalHandler(item.proCode, item.pfcatCode)">{{
												$filters.defaultValue(item.proName, '--')
											}}</a>
										</span>
									</td>
									<td class="text-end" :data-th="$t('pro.currency')">
										<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
									</td>
									<td class="text-end" :data-th="$t('pro.riskLevel')">
										<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
									</td>
									<td class="text-end" :data-th="$t('pro.referenceRedemptionPrice')">
										<span>{{ $filters.formatNumber(item.sprice, '--') }}</span>
									</td>
									<td class="text-end" :data-th="$t('pro.referenceNetValue')">
										<span>{{ $filters.formatNumber(item.aprice, '--') }}</span>
									</td>
									<td :data-th="$t('pro.dividendFrequency')">
										<span>{{ $filters.defaultValue(item.intFreqName, '--') }}</span>
									</td>
									<td :data-th="$t('pro.maturityDate')">
										<span>{{ $filters.defaultValue(item.expireDt ? item.expireDt : '9999/12/31', '--') }}</span>
									</td>
									<td :data-th="$t('pro.linkedUnderlying')">
										<span><button
											type="button"
											class="btn btn-dark btn-icon"
											data-bs-toggle="tooltip"
											:title="$t('pro.linkedUnderlying')"
											@click="tragetHandler(item.proCode)"
										>
											<i class="bi bi-search" /></button></span>
									</td>
									<td class="text-center" :data-th="$t('pro.execute')">
										<button
											v-if="activeTab === 'fast' && fastCode === '06'"
											type="button"
											class="btn btn-primary"
											:title="$t('pro.removeFromFavorites')"
											@click="remove(item.proCode)"
										>
											{{ $t('pro.removeFavorite') }}
										</button>
										<button
											v-else
											type="button"
											class="btn btn-dark btn-icon"
											data-bs-toggle="tooltip"
											:title="$t('pro.addToFavorites')"
											@click="favoritesHandler(item.proCode, item.pfcatCode)"
										>
											<i class="bi bi-heart text-danger" />
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="tx-note">
					<ol>
						<li><span>{{ $t('pro.dataDate') }}：</span></li>
						<li><span>{{ $t('pro.productPurchaseAvailabilityNote') }}</span></li>
					</ol>
				</div>
				<vue-modal :is-open="isOpenViewModal" @close="isOpenViewModal = false">
					<template #content="props">
						<vue-target-view-modal ref="targetViewModalRef" :close="props.close" />
					</template>
				</vue-modal>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import pagination from '@/views/components/pagination.vue';
import vuePerformancesCompareModal from './performancesCompareModal.vue';
import vueModal from '@/views/components/model.vue';
import vueGroupIssuerModal from './groupIssuerModal.vue';
import vueGroupTargetModal from './groupTargetModal.vue';
import vueTargetViewModal from './targetViewModal.vue';
import { getImgURL } from '@/utils/imgURL.js';
export default {
	components: {
		'vue-pagination': pagination,
		vuePerformancesCompareModal,
		vueModal,
		vueGroupIssuerModal,
		vueGroupTargetModal,
		vueTargetViewModal
	},
	props: {
		spModalHandler: Function,
		favoritesHandler: Function,
		curOption: Array,
		riskMenu: Array
	},
	data: function () {
		return {
			perf: 'PCTYTD',
			perfMenu: [],
			activeTab: 'common',
			bankProCode: null,
			proName: null,
			proTypeCode: '', // 商品類型
			curObjs: [],
			riskCode: '',
			guarType: '',
			intFreqUnitType: '',
			proTypeMenu: [], // 商品類型選單
			guarTypeMenu: [], // 保本與否下拉
			intFreqUnitMenu: [], // 配息頻率下拉
			targetItem: [], // 連結標的
			startDay: null, // 剩餘年限(天)-起
			endDay: null, // 剩餘年限(天)-迄
			issuerItem: [], // 發行機構

			fastCode: '05', // 快速 篩選條件
			timeRange: null, // 快速 顯示區間
			rowNumber: '10.000000', // 快速 顯示資料筆數

			spFastMenu: [], // 快速篩選條件
			timeRangeMenu: [], // 顯示區間
			rowNumerMenu: [], // 顯示資料筆數
			proCodes: [], // 商品代碼
			selectedItems: {}, // Join comparison options

			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: ['PRO_CODE'],
				direction: 'ASC'
			},
			isOpenTargetModal: false,
			isOpenIssuerModal: false,
			isOpenCompareModal: false,
			isOpenViewModal: false
		};
	},
	watch: {
		selectedItems: {
			handler(newValues) {
				this.proCodes = Object.keys(newValues).filter(proCode => newValues[proCode]);
			},
			deep: true
		},
		activeTab(newVal, oldVal) {
			const self = this;
			self.fastCode = '05';
			self.fastChange(self.fastCode);
		},
		curObjs(newVal, oldVal) {
			const self = this;
			if (newVal[0] !== oldVal[0]) {
				if (newVal[0] === '') {
					$('#curMenuSp').selectpicker('selectAll');
				}
				else if (oldVal[0] === '' && newVal[0] !== '') {
					$('#curMenuSp').selectpicker('deselectAll');
				}
			}
		}
	},
	mounted: function () {
		const self = this;
		$('#curMenuSp').selectpicker('refresh');
		self.getProTypeMenu(); // 取得商品類型選單
		self.getGuarTypeMenu(); // // 取得保本與否選單
		self.getIntFreqUnitTypeMenu(); // 配息頻率
		self.getspFastMenu(); // 取得快速篩選條件選項
		self.getTimeRangeMenu(); // 取得顯示區間選項
		self.getRowNumerMenu(); // 取得顯示資料筆數選項
		self.fastChange(self.fastCode);
	},
	methods: {
		getImgURL,
		// 條件Tab切換
		changeTab: function (tabName) {
			const self = this;
			self.activeTab = tabName;
			self.pageData = {
				// 清空查詢資料
				content: []
			};
		},
		async getProTypeMenu() {
			// 商品類型選單
			const self = this;
			const r = await this.$api.getProTypeListApi({
				pfcatCode: 'SP'
			});
			self.proTypeMenu = r.data;
		},
		// 保本與否選單
		async getGuarTypeMenu() {
			const self = this;
			const ret = await this.$api.getAdmCodeDetail({
				codeType: 'GUAR_TYPE'
			});
			self.guarTypeMenu = ret.data;
		},
		// 顯示連結標的 model
		groupTargetModalHandler: function () {
			const self = this;
			this.$refs.groupTargetModalRef.targetPropItem(self.targetItem);
			this.isOpenTargetModal = true;
		},
		// 顯示連結標的選擇項目
		selectedTarget(targetItem) {
			const self = this;
			this.isOpenTargetModal = false;
			self.targetItem = targetItem; // 取得連結標的資料
		},
		// 刪除 連結標的擇項目
		deleteTargetItem(targetCode) {
			const self = this;
			_.remove(self.targetItem, item => item.stockCode === targetCode); // 移除刪除項目
		},
		// 顯示發行機構 model
		groupIssuerModalHandler: function () {
			const self = this;
			this.$refs.groupIssuerModalRef.issuerPropItem(self.issuerItem);
			this.isOpenIssuerModal = true;
		},
		// 顯示發行機構選擇項目
		selectedIssuer(issuerItem) {
			const self = this;
			this.isOpenIssuerModal = false;
			self.issuerItem = issuerItem; // 取得發行機構資料
		},
		// 刪除 發行機構選擇項目
		deleteIssuerItem(issuerCode) {
			const self = this;
			_.remove(self.issuerItem, item => item.issuerCode === issuerCode); // 移除刪除項目
		},
		// 連結標的檢視
		tragetHandler: function (proCode) {
			this.$refs.targetViewModalRef.getTargetDataList(proCode);
			this.isOpenViewModal = true;
		},
		// 取得配息頻率選項
		getIntFreqUnitTypeMenu: async function () {
			const self = this;
			const ret = await this.$api.getAdmCodeDetail({
				codeType: 'INT_FREQ_UNITTYPE'
			});

			self.intFreqUnitMenu = ret.data;
		},
		// 取得顯示區間
		getTimeRangeMenu: async function () {
			const self = this;
			const ret = await this.$api.getTimeRangeMenuApi();
			self.timeRangeMenu = ret.data;
		},
		// 取得顯示資料筆數
		getRowNumerMenu: async function () {
			const self = this;
			const ret = await this.$api.getRowNumerMenuApi();
			self.rowNumerMenu = ret.data;
		},
		// 取得快速篩選條件選項
		getspFastMenu: async function () {
			const self = this;
			const ret = await this.$api.getSpFastFilterMenuApi();
			self.spFastMenu = ret.data;
		},
		// 快速查詢切換
		fastChange(fastCode) {
			const self = this;
			self.timeRange = {}; // 快速 顯示區間
			self.rowNumber = ''; // 快速 顯示資料筆數
			self.pageData = {
				// 清空查詢資料
				content: []
			};
			if (fastCode === '05') {
				// 超人氣
				$('#proPerfTimeTr').hide();
				$('#rangeFixedTr').show();
				$('#maxRowIdTr').show();
				self.timeRange = self.timeRangeMenu[0];
			}
			else if (fastCode === '07') {
				// 績效排行
				$('#rangeFixedTr').hide();
				$('#maxRowIdTr').show();
				$('#proPerfTimeTr').show();
				self.rowNumber = '10'; // 快速 顯示資料筆數
			}
			else if (fastCode === '08') {
				// 最高配息率商品
				$('#maxRowIdTr').show();
				$('#rangeFixedTr').hide();
				$('#proPerfTimeTr').hide();
				self.rowNumber = '10'; // 快速 顯示資料筆數
			}
			else {
				$('#maxRowIdTr').hide();
				$('#rangeFixedTr').hide();
				$('#proPerfTimeTr').hide();
			}
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			const self = this;
			self.pageable.page = page;
			let url = '';

			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			const targetCodes = [];
			self.targetItem.forEach(function (item) {
				targetCodes.push(item.isinCode);
			});

			const issuerCodes = [];
			self.issuerItem.forEach(function (item) {
				issuerCodes.push(item.issuerCode);
			});

			const ret = await this.$api.getSpProductsApi(
				{
					bankProCode: self.bankProCode, // 商品代號
					proName: self.proName, // 商品名稱
					protypeCode: self.proTypeCode, // 商品類型
					curCodes: self.curObjs, // 計價幣別
					riskCode: self.riskCode, // 風險等級
					principalGuarYn: self.guarType, // 保本與否
					targetCodes: targetCodes, // 連結標的
					intFreqUnitType: self.intFreqUnitType, // 配息頻率
					remainDayMin: self.startDay, // 剩餘年限(天)-起
					remainDayMax: self.endDay, // 剩餘年限(天)-迄
					issuerCodes: issuerCodes // 發行機構
				},
				url
			);
			self.pageData = ret.data;
		},
		// 執行績效比較圖
		performancesCompareModelHandler: function () {
			const self = this;
			if (self.proCodes.length > 0) {
				if (self.proCodes.length > 6) {
					this.$bi.alert(this.$t('pro.maxSixItems'));
				}
				else {
					this.$refs.performancesCompareModalRef.comparePropItem(self.proCodes, 'sp');
					this.isOpenCompareModal = true;
				}
			}
			else {
				this.$bi.alert(this.$t('pro.selectAtLeastOneProduct'));
			}
		},
		gotoFastPage: function (page) {
			this.pageable.page = page;
			this.getFastPageData(page);
		},
		getFastPageData: async function (page) {
			const self = this;
			this.pageable.page = page;

			let url = '';
			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			const ret = await this.$api.getSpProductsFilterQueryApi(
				{
					filterCodeValue: self.fastCode,
					timeRangeType: self.timeRange.rangeType, // 顯示區間類型
					timeRangeFixed: self.timeRange.rangeFixed, // 顯示區間數值
					rowNumberFixed: self.rowNumber
				},
				url
			);
			self.pageData = ret.data;
		},
		// 刪除我的最愛
		async remove(proCode) {
			await this.$api.deleteFavoriteApi({ proCode });
			this.$bi.alert(this.$t('pro.deleteSuccess'));
			this.proCodes = [];
			this.gotoFastPage(0);
		}
	} // methods end
};
</script>
