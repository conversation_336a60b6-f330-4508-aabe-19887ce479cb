<template>
	<!--Page content - Self-operated securities products (SEC)  start-->
	<div class="tab-nav-tabs my-3">
		<ul class="nav nav-tabs nav-justified">
			<li class="nav-item">
				<a
					class="nav-link"
					:class="{ active: activeTab == 'common' }"
					href="#SectionA"
					data-bs-toggle="tab"
					@click="changeTab('common')"
				>{{ $t('pro.generalFilter') }}</a>
			</li>
			<li class="nav-item">
				<a
					class="nav-link"
					:class="{ active: activeTab == 'fast' }"
					href="#SectionB"
					data-bs-toggle="tab"
					@click="changeTab('fast')"
				>{{ $t('pro.quickFilter') }}</a>
			</li>
		</ul>
		<div class="tab-content">
			<div id="SectionA" class="tab-pane fade show active">
				<div class="card card-form-collapse">
					<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
						<h4>{{ $t('pro.searchCond') }}</h4>
						<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
					</div>
					<div id="collapseListGroup1" class="collapse show">
						<div class="card-body">
							<form>
								<div class="form-row">
									<div class="form-group col-12 col-lg-4">
										<label class="form-label">{{ $t('pro.productCode') }}</label>
										<input
											id="prod_bank_pro_code"
											v-model="bankProCode"
											class="form-control"
											maxlength="20"
											size="25"
											type="text"
											value=""
										>
									</div>

									<div class="form-group col-12 col-lg-4">
										<label class="form-label">{{ $t('pro.productName') }}</label>
										<input
											id="prod_pro_name"
											v-model="proName"
											class="form-control"
											maxlength="20"
											size="45"
											type="text"
											value=""
										>
									</div>

									<div class="form-group col-12 col-lg-4">
										<label class="form-label">{{ $t('pro.productType') }}</label>
										<select
											id="proType"
											v-model="proTypeCode"
											class="form-select"
											name="proType"
											:title="$t('pro.pleaseSelectType')"
											data-style="btn-white"
										>
											<option value="">
												{{ $t('pro.all') }}
											</option>
											<option v-for="item in proTypeMenu" value="item.proTypeCode">
												{{ $filters.defaultValue(item.proTypeName, '--') }}
											</option>
										</select>
									</div>

									<div class="form-group col-12 col-lg-4">
										<label class="form-label">{{ $t('pro.pricingCurrency') }}</label>
										<select
											id="curMenuSec"
											v-model="curObjs"
											class="selectpicker form-control"
											multiple
											:title="$t('pro.pleaseSelectCurrency')"
											data-style="btn-white"
										>
											<option v-for="item in curOption" :key="index" :value="item.value">
												{{ $filters.defaultValue(item.name, '--') }}
											</option>
										</select>
									</div>

									<div class="form-group col-12 col-lg-4">
										<label class="form-label">{{ $t('pro.riskLevel') }}</label>
										<select id="riskCode" v-model="riskCode" class="form-select">
											<option value="">
												{{ $t('pro.all') }}
											</option>
											<option v-for="item in riskMenu" :value="item.riskCode">
												{{ $filters.defaultValue(item.riskName, '--') }}
											</option>
										</select>
									</div>

									<div class="form-group col-12 col-lg-4">
										<label class="form-label">{{ $t('pro.dividendFrequency') }}</label>
										<select v-model="intFreqUnitType" name="select" class="form-select">
											<option value="">
												{{ $t('pro.all') }}
											</option>
											<option v-for="intFreqUnit in intFreqUnitMenu" :value="intFreqUnit.codeValue">
												{{ $filters.defaultValue(intFreqUnit.codeName, '--') }}
											</option>
										</select>
									</div>

									<div class="form-group col-12 col-lg-4">
										<label class="form-label">{{ $t('pro.minimumInvestmentAmount') }}</label>
										<input
											id="mininvFcAmtStart"
											v-model="mininvFcAmtStart"
											class="form-control"
											maxlength="20"
											size="10"
											type="text"
											value=""
										>~
										<input
											id="mininvFcAmtEnd"
											v-model="mininvFcAmtEnd"
											class="form-control"
											maxlength="20"
											size="10"
											type="text"
											value=""
										>
									</div>

									<div class="form-group col-12 col-lg-4">
										<label class="form-label">{{ $t('pro.remainingYearsInYears') }}</label>
										<input
											id="expireYearStart"
											v-model="expireYearStart"
											class="form-control"
											maxlength="20"
											size="10"
											type="text"
											value=""
										>~
										<input
											id="expireYearEnd"
											v-model="expireYearEnd"
											class="form-control"
											maxlength="20"
											size="10"
											type="text"
											value=""
										>
									</div>

									<!-- Moved issuer to the bottom -->
									<div class="form-group col-12 col-lg-4">
										<label class="form-label">{{ $t('pro.issuer') }}</label>
										<button type="button" class="btn btn-primary" @click="groupIssuerModalHandler()">
											{{ $t('pro.selectIssuer') }}
										</button>
										<vue-modal :is-open="isOpenIssuerModal" @close="isOpenIssuerModal = false">
											<template #content="props">
												<vue-group-issuer-modal
													id="groupIssuerModal"
													ref="groupIssuerModalRef"
													:close="props.close"
													:pfcat-code="'SEC'"
													@selected="selectedIssuer"
												/>
											</template>
										</vue-modal>
									</div>
								</div>

								<div v-for="item in issuerItem" style="padding-left: 1145px; padding-bottom: 15px">
									<span class="form-check-label"> {{ $filters.defaultValue(item.issuerName, '--') }}</span>
									<a href="#" @click="deleteIssuerItem(item.issuerCode)">
										<img :src="getImgURL('icon', 'i-cancel.png')">
									</a>
								</div>

								<div class="form-footer">
									<button class="btn btn-primary" @click.prevent="gotoPage(0)">
										{{ $t('pro.search') }}
									</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>

			<div id="SectionB" class="tab-pane fade">
				<div class="card card-form-collapse">
					<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
						<h4>{{ $t('pro.searchCond') }}</h4>
						<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
					</div>
					<div id="collapseListGroup2" class="collapse show">
						<div class="card-body">
							<div class="form-row">
								<div class="form-group col-12 col-lg-12">
									<label class="form-label tx-require">{{ $t('pro.filterConditions') }}</label>
									<div v-for="item in secFastMenu" class="form-check-group" @change="fastChange(item.codeValue)">
										<input
											:id="'fast' + item.codeValue"
											v-model="fastCode"
											class="form-check-input"
											name="fastCode"
											:value="item.codeValue"
											type="radio"
										>
										<label class="form-check-label" :for="'fast' + item.codeValue">{{
											$filters.defaultValue(item.codeName, '--')
										}}</label>
									</div>
								</div>
								<div id="rangeFixedTr" class="form-group col-12 col-lg-6" style="display: none">
									<label class="form-label tx-require">{{ $t('pro.displayRange') }}</label>

									<select id="prod_protype_code" v-model="timeRange" class="form-select">
										<option v-for="item in timeRangeMenu" :value="item.rangeType">
											{{ $filters.defaultValue(item.termName, '--') }}
										</option>
									</select>
								</div>

								<div id="proPerfTimeTr" class="form-group col-12 col-lg-6" style="display: none">
									<label class="form-label tx-require">{{ $t('pro.targetPerformance') }}</label>

									<select id="vfAstStat_stat_code" v-model="perf" class="form-select">
										<option v-for="item in perfMenu" :value="item.codeValue">
											{{ $filters.defaultValue(item.codeName, '--') }}
										</option>
									</select>
								</div>

								<div id="maxRowIdTr" class="form-group col-12 col-lg-6" style="display: none">
									<label class="form-label tx-require">{{ $t('pro.displayRecordCount') }}</label>
									<select id="maxRowId" v-model="rowNumber" class="form-select">
										<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
											{{ $filters.defaultValue(item.termName, '--') }}
										</option>
									</select>
								</div>
							</div>
							<div class="form-footer">
								<button class="btn btn-primary" @click.prevent="gotoFastPage(0)">
									{{ $t('pro.search') }}
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div v-if="pageData.content.length > 0" id="searchResult">
			<div class="card card-table">
				<div class="card-header">
					<h4>{{ $t('pro.searchResults') }}</h4>
					<div style="display: flex">
						<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
						<button type="button" class="btn btn-info ms-2" @click="performancesCompareModelHandler()">
							{{ $t('pro.performanceComparisonChart') }}
						</button>
						<vue-modal :is-open="isOpenCompareModal" @close="isOpenCompareModal = false">
							<template #content="props">
								<vue-performances-compare-modal
									id="performancesCompareModal"
									ref="performancesCompareModalRef"
									:close="props.close"
								/>
							</template>
						</vue-modal>
					</div>
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-bordered text-center">
						<thead>
							<tr>
								<th class="wd-100 text-start">
									{{ $t('pro.joinComparison') }}
								</th>
								<th>{{ $t('pro.productCode') }}</th>
								<th class="10% text-start">
									{{ $t('pro.productChineseName') }}
								</th>
								<th>{{ $t('pro.pricingCurrency') }}</th>
								<th>{{ $t('pro.riskLevel') }}</th>
								<th>{{ $t('pro.netValueDate') }}</th>
								<th>{{ $t('pro.referenceNetValue') }}</th>
								<th>{{ $t('pro.dividendFrequency') }}</th>
								<th>{{ $t('pro.minimumInvestmentAmount') }}</th>
								<th class="text-center" width="120">
									{{ $t('pro.execute') }}
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="(item, index) in pageData.content">
								<td :data-th="$t('pro.joinComparison')" class="text-start text-center">
									<input
										:id="'id-' + item.bankProCode"
										v-model="selectedItems[item.proCode]"
										class="form-check-input text-center"
										type="checkbox"
									>
									<label class="form-check-label" :for="'id-' + item.bankProCode" />
								</td>
								<td :data-th="$t('pro.productCode')">
									<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
								</td>
								<td class="text-start" :data-th="$t('pro.productChineseName')">
									<span>
										<a class="tx-link" @click="secModalHandler(item.proCode, item.pfcatCode)">{{
											$filters.defaultValue(item.proName, '--')
										}}</a>
									</span>
								</td>
								<td class="text-end" :data-th="$t('pro.pricingCurrency')">
									<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
								</td>
								<td :data-th="$t('pro.riskLevel')">
									<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
								</td>
								<td class="text-end" :data-th="$t('pro.netValueDate')">
									<span>{{ $filters.defaultValue(item.priceDt, '--') }}</span>
								</td>
								<td :data-th="$t('pro.referenceNetValue')">
									<span>{{ $filters.defaultValue(item.aprice, '--') }}</span>
								</td>
								<td class="text-end" :data-th="$t('pro.dividendFrequency')">
									<span>{{ $filters.defaultValue(item.intFreqName, '--') }}</span>
								</td>
								<td :data-th="$t('pro.minimumPurchaseAmount')">
									{{ $filters.defaultValue(item.mininvFcAmt, '--') }}
								</td>
								<td class="text-center" :data-th="$t('pro.execute')">
									<button
										v-if="activeTab === 'fast' && fastCode === '06'"
										type="button"
										class="btn btn-primary"
										:title="$t('pro.removeFromFavorites')"
										@click="remove(item.proCode)"
									>
										{{ $t('pro.removeFavorite') }}
									</button>
									<button
										v-else
										type="button"
										class="btn btn-dark btn-icon"
										data-bs-toggle="tooltip"
										:title="$t('pro.addToFavorites')"
										@click="favoritesHandler(item.proCode, item.pfcatCode)"
									>
										<i class="bi bi-heart text-danger" />
									</button>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div class="tx-note">
				<ol>
					<li><span>{{ $t('pro.dataDate') }}：</span></li>
					<li><span>{{ $t('pro.productPurchaseAvailabilityNote') }}</span></li>
				</ol>
			</div>
		</div>
	</div>
</template>
<script>
import pagination from '@/views/components/pagination.vue';
import vuePerformancesCompareModal from './performancesCompareModal.vue';
import vueModal from '@/views/components/model.vue';
import vueGroupIssuerModal from './groupIssuerModal.vue';
import _ from 'lodash';
import { getImgURL } from '@/utils/imgURL.js';

export default {
	components: {
		'vue-pagination': pagination,
		vuePerformancesCompareModal,
		vueModal,
		vueGroupIssuerModal
	},
	props: {
		secModalHandler: Function,
		favoritesHandler: Function,
		curOption: Array,
		riskMenu: Array
	},
	data: function () {
		return {
			activeTab: 'common',
			bankProCode: null,
			proName: null,
			proTypeCode: '', // Product type
			curObjs: [],
			riskCode: '',
			intFreqUnitType: '',
			mininvFcAmtStart: null, // Minimum investment amount - start
			mininvFcAmtEnd: null, // Minimum investment amount - end
			expireYearStart: null, // Remaining years - start
			expireYearEnd: null, // Remaining years - end

			fastCode: '05', // Quick filter condition
			timeRange: null, // Quick display range
			rowNumber: null, // Quick display record count

			proTypeMenu: [], // Product type menu
			intFreqUnitMenu: [], // Dividend frequency dropdown
			secFastMenu: [], // Quick filter conditions
			timeRangeMenu: [], // Display range
			rowNumerMenu: [], // Display record count

			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: ['PRO_CODE'],
				direction: 'ASC'
			},
			issuerItem: [], // Issuer items
			proCodes: [], // Product codes
			selectedItems: {}, // Join comparison options

			isOpenIssuerModal: false,
			isOpenCompareModal: false
		};
	},
	watch: {
		selectedItems: {
			handler(newValues) {
				this.proCodes = Object.keys(newValues).filter(proCode => newValues[proCode]);
			},
			deep: true
		},
		activeTab(newVal, oldVal) {
			const self = this;
			self.fastCode = '06';
			self.fastChange(self.fastCode);
		}
	},
	mounted: function () {
		const self = this;
		$('#curMenuSec').selectpicker('refresh');
		self.getProTypeMenu(); // Get product type menu
		self.getIntFreqUnitTypeMenu(); // Get dividend frequency options
		self.getSecFastMenu(); // Get quick filter condition options
		self.getTimeRangeMenu(); // Get display range options
		self.getRowNumerMenu(); // Get display record count options
		self.fastChange(self.fastCode);
	},
	methods: {
		getImgURL,
		// Tab switching for conditions
		changeTab: function (tabName) {
			const self = this;
			self.activeTab = tabName;
			self.pageData = {
				// Clear search data
				content: []
			};
		},
		// Product type menu
		async getProTypeMenu() {
			const self = this;
			const r = await this.$api.etProTypeListApi({
				pfcatCode: 'SEC'
			});
			self.proTypeMenu = r.data;
		},
		// Get dividend frequency options
		getIntFreqUnitTypeMenu: async function () {
			const self = this;
			const ret = await this.$api.getAdmCodeDetail({
				codeType: 'INT_FREQ_UNITTYPE'
			});
			self.intFreqUnitMenu = ret.data;
		},
		// Get quick filter condition options
		getSecFastMenu: async function () {
			const self = this;
			const ret = await this.$api.getSecFastMenuApi();
			self.secFastMenu = ret.data;
		},
		// Quick search switching
		fastChange(fastCode) {
			const self = this;
			self.timeRange = null; // Quick display range
			self.rowNumber = null; // Quick display record count
			self.pageData = {
				// Clear search data
				content: []
			};
			if (fastCode === '05') {
				// Super popular
				$('#proPerfTimeTr').hide();
				$('#rangeFixedTr').show();
				$('#maxRowIdTr').show();
				self.timeRange = 'D'; // Quick display range
				self.rowNumber = '10'; // Quick display record count
				$('#proPerfTimeTr').hide();
			}
			else {
				$('#maxRowIdTr').hide();
				$('#rangeFixedTr').hide();
				$('#proPerfTimeTr').hide();
			}
		},
		// Get display range
		getTimeRangeMenu: async function () {
			const self = this;
			const ret = await this.$api.getTimeRangeMenuApi();
			self.timeRangeMenu = ret.data;
		},
		// Get display record count
		getRowNumerMenu: async function () {
			const self = this;
			const ret = await this.$api.getRowNumerMenuApi();
			self.rowNumerMenu = ret.data;
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (_page) {
			const self = this;
			let url = '';

			const page = _.isNumber(_page) ? _page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			const curCodes = [];
			self.curObjs.forEach(function (item) {
				curCodes.push(item.value);
			});

			const issuerCodes = [];
			self.issuerItem.forEach(function (item) {
				issuerCodes.push(item.issuerCode);
			});

			const ret = await this.$api.getSecProductsApi(
				{
					bankProCode: self.bankProCode, // {{ $t('pro.productCode') }}
					proName: self.proName, // Product name
					protypeCode: self.proTypeCode, // Product type
					curCodes: self.curObjs, // Pricing currency
					riskCode: self.riskCode, // {{ $t('pro.riskLevel') }}
					issuerCodes: issuerCodes, // Issuer
					intFreqUnitType: self.intFreqUnitType, // {{ $t('pro.dividendFrequency') }}
					mininvFcAmtStart: self.mininvFcAmtStart, // Minimum investment amount - start
					mininvFcAmtEnd: self.mininvFcAmtEnd, // Minimum investment amount - end
					expireYearStart: self.expireYearStart, // Remaining years - start
					expireYearEnd: self.expireYearEnd // Remaining years - end
				},
				url
			);
			self.pageData = ret.data;
			self.pageData.content.forEach((data) => {
				self.checkboxs.push(false);
			});
		},
		// Execute performance comparison chart
		performancesCompareModelHandler: function () {
			const self = this;
			if (self.proCodes.length > 0) {
				if (self.proCodes.length > 6) {
					this.$bi.alert(this.$t('pro.maxSixItems'));
				}
				else {
					this.$refs.performancesCompareModalRef.comparePropItem(self.proCodes, 'sec');
					this.isOpenCompareModal = true;
				}
			}
			else {
				this.$bi.alert(this.$t('pro.selectAtLeastOneProduct'));
			}
		},
		gotoFastPage: function (page) {
			this.pageable.page = page;
			this.getFastPageData(page);
		},
		getFastPageData: async function (_page) {
			const self = this;
			let url = '';
			const page = _.isNumber(_page) ? _page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			const ret = await this.$api.getSecProductsFilterQueryApi({
				filterCodeValue: self.fastCode,
				timeRangeType: self.timeRange, // Display range type
				timeRangeFixed: self.rowNumber // Display range value
			});
			self.pageData = ret.data;
			self.pageData.content.forEach((data) => {
				self.checkboxs.push(false);
			});
		},
		// Show issuer modal
		groupIssuerModalHandler: function () {
			const self = this;
			this.$refs.groupIssuerModalRef.issuerPropItem(self.issuerItem);
			this.isOpenIssuerModal = true;
		},
		// Show issuer selection items
		selectedIssuer(issuerItem) {
			const self = this;
			this.isOpenIssuerModal = false;
			self.issuerItem = issuerItem; // Get issuer data
		},
		// Delete issuer selection item
		deleteIssuerItem(issuerCode) {
			const self = this;
			_.remove(self.issuerItem, item => item.issuerCode === issuerCode); // Remove deleted item
		},
		// Delete from favorites
		async remove(proCode) {
			const self = this;
			await this.$api.deleteFavoriteApi({
				proCode: proCode
			});
			this.$bi.alert(this.$t('pro.deleteSuccess'));
			self.checkboxs = [];
			self.proCodes = [];
			self.gotoFastPage(0);
		}
	} // methods end
};
</script>
