<template>
	<!-- Modal group GeoFocus start -->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('pro.selectInvestmentRegion') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click="close()"
				/>
			</div>
			<div class="modal-body">
				<div class="row m-2">
					<div v-for="item in geoFocusMenu" class="col-3 form-check">
						<input
							:id="item.geoFocusCode"
							v-model="geoFocusCode"
							class="form-check-input"
							name="geoFocusCode"
							:value="item.geoFocusCode"
							type="checkbox"
						>
						<label class="form-check-label" :for="item.geoFocusCode">{{ item.name }}</label>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<input
					id="modalAddButton"
					type="button"
					class="btn btn-primary"
					:value="$t('pro.add')"
					@click="addGeoFocus()"
				>
			</div>
		</div>
	</div>
	<!-- Modal group GeoFocus End -->
</template>
<script>
export default {
	props: {
		geoFocusProp: Array, // 已選擇項目
		close: Function
	},
	data: function () {
		return {
			geoFocusMenu: [], // 投資地區 選項
			geoFocusCode: [], // 投資地區 選擇項目
			geoFocusItem: [] // 投資地區 選擇項目代碼與中文名稱
		};
	},
	watch: {},
	mounted: function () {
		const self = this;
		self.groupGeoFocusMenu();
		self.geoFocusItem = [];
	},
	methods: {
		groupGeoFocusMenu: async function () {
			// 投資地區來源資料
			const self = this;
			const res = await this.$api.getGeoFocusMenuApi({ local: 'zh_TW' });
			self.geoFocusMenu = res.data;
		},
		addGeoFocus() {
			// 增加投資地區
			const self = this;
			self.geoFocusItem = [];
			if (self.geoFocusCode.length === 0) {
				this.$bi.alert(this.$t('pro.pleaseSelectAtLeastOne'));
			}
			else {
				self.geoFocusCode.forEach((code) => {
					const item = _.find(self.geoFocusMenu, { geoFocusCode: code });
					self.geoFocusItem.push(item);
					self.$emit('selected', self.geoFocusItem);
					self.close();
				});
			}
		},
		geoFocusPropItem(v) {
			const self = this;
			if (v.length > 0) {
				self.geoFocusItem = v;
				self.geoFocusCode = [];
				v.forEach((item) => {
					self.geoFocusCode.push(item.geoFocusCode);
				});
			}
			else {
				self.geoFocusItem = [];
				self.geoFocusCode = [];
			}
		}
	}
	// methods end
};
</script>
