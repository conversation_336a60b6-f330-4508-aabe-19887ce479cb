<template>
	<!-- Modal group Issuer start -->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('pro.selectIssuer') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click="close()"
				/>
			</div>
			<div class="modal-body">
				<div class="row m-2">
					<div v-for="item in issuersMenu" class="col-3 form-check">
						<input
							:id="item.issuerCode"
							v-model="issuerCode"
							class="form-check-input"
							name="finReqCodes"
							:value="item.issuerCode"
							type="checkbox"
						>
						<label class="form-check-label" :for="item.issuerCode">{{ item.issuerName }}</label>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<input
					id="modaladdButton"
					type="button"
					class="btn btn-primary"
					:value="$t('pro.add')"
					@click="addIssuer()"
				>
			</div>
		</div>
	</div>
	<!-- Modal group Issuer End -->
</template>
<script>
export default {
	props: {
		pfcatCode: String,
		close: Function
	},
	data: function () {
		return {
			issuersMenu: [], // 發行機構 選項
			issuerCode: [], // 發行機構 選擇項目
			issuerItem: [] // 發行機構 選擇項目代碼與中文名稱
		};
	},
	watch: {},
	mounted: function () {
		const self = this;
		self.groupIssuersMenu();
		self.issuerItem = [];
	},
	methods: {
		groupIssuersMenu: async function () {
			// 發行機構來源資料
			const self = this;
			const ret = await this.$api.getGroupIssuersMenuApi({
				pfcatCode: self.pfcatCode
			});
			const englishNames = [];
			const numberNames = [];
			const chineseNames = [];

			ret.data.forEach((item) => {
				const name = item.issuerName.trim();
				const type = self.getStringType(name);

				// 判斷英文/數字/中文
				if (type === 'english') {
					englishNames.push(item);
				}
				else if (type === 'number') {
					numberNames.push(item);
				}
				else {
					chineseNames.push(item);
				}
			});

			// 合併結果
			self.issuersMenu = [
				...numberNames.sort((a, b) => a.issuerName.localeCompare(b.issuerName, undefined, { numeric: true })),
				...englishNames.sort((a, b) => a.issuerName.localeCompare(b.issuerName)),
				...chineseNames.sort((a, b) => a.issuerName.localeCompare(b.issuerName, 'zh-Hant-u-co-stroke'))
			];
		},
		addIssuer() {
			// 增加發行機構
			const self = this;
			self.issuerItem = [];
			if (self.issuerCode.length === 0) {
				this.$bi.alert(this.$t('pro.pleaseSelectAtLeastOne'));
			}
			else {
				self.issuerCode.forEach((code) => {
					const item = _.find(self.issuersMenu, { issuerCode: code });
					self.issuerItem.push(item);
					self.$emit('selected', self.issuerItem);
					self.close();
				});
			}
		},
		issuerPropItem(v) {
			const self = this;
			if (v.length > 0) {
				self.issuerItem = v;
				self.issuerCode = [];
				v.forEach((item) => {
					self.issuerCode.push(item.issuerCode);
				});
			}
			else {
				self.issuerItem = [];
				self.issuerCode = [];
			}
		},
		// 全形轉半型
		toHalfWidth: function (str) {
			return str
				.replace(/[\uff01-\uff5e]/g, function (char) {
					return String.fromCharCode(char.charCodeAt(0) - 0xfee0);
				})
				.replace(/\u3000/g, ' ');
		},
		getStringType: function (str) {
			const convertedStr = this.toHalfWidth(str);
			const firstChar = convertedStr.charAt(0);

			if (/[a-zA-Z]/.test(firstChar)) return 'english';
			if (/[0-9]/.test(firstChar)) return 'number';
			return 'chinese';
		}
	}
	// methods end
};
</script>
