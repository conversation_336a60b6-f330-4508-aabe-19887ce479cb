<template>
	<div v-bind="$attrs" class="tab-content">
		<div class="tab-pane fade show active">
			<!--頁面內容 基金fund start-->
			<div class="tab-nav-tabs my-3">
				<ul class="nav nav-tabs nav-justified">
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'common' }"
							data-bs-toggle="tab"
							@click="changeTab('common')"
						>{{ $t('pro.generalFilter') }}</a>
					</li>
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'fast' }"
							data-bs-toggle="tab"
							@click="changeTab('fast')"
						>{{ $t('pro.quickFilter') }}</a>
					</li>
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'weighted' }"
							data-bs-toggle="tab"
							@click="changeTab('weighted')"
						>{{ $t('pro.weightedConditionFilter') }}</a>
					</li>
					<li class="nav-item">
						<a
							class="nav-link"
							:class="{ active: activeTab == 'advanced' }"
							data-bs-toggle="tab"
							@click="changeTab('advanced')"
						>{{ $t('pro.advancedSearch') }}</a>
					</li>
				</ul>

				<div class="tab-content">
					<div class="tab-pane fade show" :class="{ 'active show': activeTab == 'common' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>{{ $t('pro.searchCond') }}</h4>
								<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
							</div>
							<div id="collapseListGroup1" class="collapse show">
								<div class="card-body">
									<!-- <form> -->

									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> {{ $t('pro.productCode') }} </label>
											<input
												id="prod_bank_pro_code"
												v-model="bankProCode"
												class="form-control"
												maxlength="20"
												size="25"
												type="text"
												value=""
											>
										</div>

										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> {{ $t('pro.productName') }}</label>
											<input
												id="prod_pro_name"
												v-model="proName"
												class="form-control"
												maxlength="20"
												size="45"
												type="text"
												value=""
											>
										</div>

										<div class="form-group col-12 col-lg-4">
											<label class="form-label">{{ $t('pro.currency') }}</label>
											<select
												id="curMenuFund"
												ref="curMenuFund"
												v-model="curObjs"
												class="selectpicker form-control"
												multiple
												:title="$t('pro.pleaseSelectCurrency')"
												data-style="btn-white"
											>
												<option value="">
													{{ $t('pro.all') }}
												</option>
												<option v-for="(item, index) in curOption" :key="index" :value="item.value">
													{{ $filters.defaultValue(item.name, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label">{{ $t('pro.riskLevel') }}</label>
											<div class="form-check-group">
												<div v-for="(item, index) in riskMenu" class="form-check form-check-inline">
													<input
														:id="'riskGrade-' + index"
														v-model="riskCodes"
														type="checkbox"
														class="form-check-input"
														name="riskCodes"
														:value="item.riskCode"
													>
													<label :for="'riskGrade-' + index" class="form-check-label">{{ item.riskName }}</label>
												</div>
											</div>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label tx-require">{{ $t('pro.domesticForeignFund') }}</label>
											<div v-for="item in localMenu" class="form-check form-check-inline">
												<input
													:id="'genLocalYn' + item.codeValue"
													v-model="genLocalYn"
													class="form-check-input"
													type="radio"
													:value="item.codeValue"
													name="genLocalYn"
												>
												<label class="form-check-label" :for="'genLocalYn' + item.codeValue">{{
													$filters.defaultValue(item.codeName, '--')
												}}</label>
											</div>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label tx-require">{{ $t('pro.fundType') }}</label>
											<select
												id="proType"
												v-model="proTypeCode"
												class="form-select"
												name="proType"
												:title="$t('pro.pleaseSelectType')"
												data-style="btn-white"
											>
												<option value="">
													{{ $t('pro.all') }}
												</option>
												<option v-for="item in proTypeMenu" :value="item.proTypeCode">
													{{ $filters.defaultValue(item.proTypeName, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label tx-require">{{ $t('pro.lipperRating') }}</label>
											<div class="input-group">
												<select v-model="lipperPoint" name="lipperRankType" class="form-select">
													<option value="" selected>
														{{ $t('pro.all') }}
													</option>
													<option value="TOTRETOV ">
														{{ $t('pro.overallReturn') }}
													</option>
													<option value="CONSRETOV">
														{{ $t('pro.stableReturn') }}
													</option>
													<option value="CAPPRESOV">
														{{ $t('pro.principalProtectedReturn') }}
													</option>
												</select>
											</div>
											<div class="input-group-text">
												{{ $t('pro.indicator') }} &nbsp;{{ $t('pro.ranking') }}
											</div>
											<div class="input-group">
												<select v-model="lipperRank" name="lipperRank" class="form-select">
													<option value="" selected>
														{{ $t('pro.all') }}
													</option>
													<option value="1">
														1
													</option>
													<option value="2">
														2
													</option>
													<option value="3">
														3
													</option>
													<option value="4">
														4
													</option>
													<option value="5">
														5
													</option>
												</select>
											</div>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label tx-require">{{ $t('pro.dividendFrequency') }}</label>
											<select v-model="intFreqUnitType" name="select" class="form-select">
												<option value="">
													{{ $t('pro.all') }}
												</option>
												<option v-for="intFreqUnit in intFreqUnitMenu" :value="intFreqUnit.codeValue">
													{{ $filters.defaultValue(intFreqUnit.codeName, '--') }}
												</option>
											</select>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label tx-require">{{ $t('pro.dividendRate') }}</label>
											<div v-for="item in intRateRankMenu" class="form-check form-check-inline">
												<input
													:id="'intRateRank' + item.codeValue"
													v-model="intRateRank"
													class="form-check-input"
													type="radio"
													:value="item.codeValue"
													name="intRateRank"
												>
												<label class="form-check-label" :for="'intRateRank' + item.codeValue">{{
													$filters.defaultValue(item.codeName, '--')
												}}</label>
											</div>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label">{{ $t('pro.subscriptionFee') }}</label>
											<div v-for="item in backEndLoadMenu" class="form-check form-check-inline">
												<input
													:id="'backEndLoad' + item.codeValue"
													v-model="backEndLoadYn"
													class="form-check-input"
													type="radio"
													:value="item.codeValue"
													name="backEndLoadYn"
												>
												<label class="form-check-label" :for="'backEndLoad' + item.codeValue">{{
													$filters.defaultValue(item.codeName, '--')
												}}</label>
											</div>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label">{{ $t('pro.returnRatePercent') }}</label>
											<div class="input-group">
												<select v-model="statCode" name="local" class="form-select">
													<option v-for="item in statCodeMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
												<input
													id="minDValue"
													v-model="minDValue"
													type="number"
													class="form-control"
													size="5"
												>
												<div class="input-group-text">
													~
												</div>
												<input
													id="maxDValue"
													v-model="maxDValue"
													type="number"
													class="form-control"
													size="5"
												>
											</div>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label">ISINCODE</label>
											<input
												id="isinCode"
												v-model="isinCode"
												class="form-control"
												maxlength="20"
												size="45"
												type="text"
											>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-12">
											<label class="form-label"> {{ $t('pro.fundCompany') }}</label>
											<div class="input-group">
												<div class="form-check-group">
													<div class="form-check form-check-inline">
														<input
															id="fund_regionin"
															v-model="nation"
															class="form-check-input"
															type="radio"
															name="nation"
															value="Y"
															@change="fundCmpChange"
														>
														<label class="form-check-label" for="fund_regionin">{{ $t('pro.domestic') }}</label>
													</div>
													<div class="form-check form-check-inline">
														<input
															id="fund_regionout"
															v-model="nation"
															class="form-check-input"
															type="radio"
															name="nation"
															value="N"
															@change="fundCmpChange"
														>
														<label class="form-check-label" for="fund_regionout">{{ $t('pro.foreign') }}</label>
													</div>
												</div>
												<button type="button" class="btn btn-primary" @click="groupFundCmpModalHandler(nation)">
													{{ $t('pro.selectFundCompany') }}
												</button>
												<div v-for="item in fundCmpItem">
													<span class="form-check-label"> {{ $filters.defaultValue(item.compName, '--') }}</span>
													<a href="#" @click="deleteFundCmpItem(item.compCode)"><img
														:src="getImgURL('icon', 'i-cancel.png')"
													></a>
												</div>
											</div>
										</div>
									</div>
									<div class="form-row">
										<div class="form-group col-12 col-lg-12">
											<label class="form-label">{{ $t('pro.investmentRegion') }}</label>
											<div class="input-group">
												<button type="button" class="btn btn-primary" @click="groupGeoFocusModalHandler()">
													{{ $t('pro.selectInvestmentRegion') }}
												</button>
												<div v-for="item in geoFocusItem">
													<span class="form-check-label"> {{ $filters.defaultValue(item.name, '--') }}</span>
													<a href="#" @click="deleteGeoFocusItem(item.geoFocusCode)"><img
														:src="getImgURL('icon', 'i-cancel.png')"
													></a>
												</div>
											</div>
										</div>
									</div>
									<!-- </form> -->
								</div>

								<div class="form-footer">
									<a class="btn btn-primary" @click.prevent="gotoPage(0)">{{ $t('pro.search') }}</a>
								</div>
							</div>
						</div>
					</div>

					<div class="tab-pane fade" :class="{ 'active show': activeTab == 'fast' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
								<h4>{{ $t('pro.searchCond') }}</h4>
								<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
							</div>
							<div id="collapseListGroup2" class="collapse show">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-12">
											<label class="form-label tx-require"> {{ $t('pro.filterConditions') }} </label>

											<div class="form-check-group">
												<div
													v-for="item in fundFastMenu"
													class="form-check form-check-inline"
													@change="fastChange(item.codeValue)"
												>
													<input
														:id="'fast' + item.codeValue"
														v-model="fastCode"
														class="form-check-input"
														name="fastCode"
														:value="item.codeValue"
														type="radio"
													>
													<label class="form-check-label" :for="'fast' + item.codeValue">{{
														$filters.defaultValue(item.codeName, '--')
													}}</label>
												</div>
											</div>
										</div>
									</div>
									<div class="form-row">
										<div id="rangeFixedTrFund" class="form-group col-12 col-lg-6" style="display: none">
											<label class="form-label tx-require"> {{ $t('pro.displayInterval') }}</label>
											<select id="prod_protype_code" v-model="timeRange" class="form-select">
												<option v-for="item in timeRangeMenu" :value="item">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-row">
										<div id="proPerfTimeTrFund" class="form-group col-12 col-lg-6" style="display: none">
											<label class="form-label tx-require"> {{ $t('pro.targetPerformance') }} </label>
											<select id="vfAstStat_stat_code" v-model="perf" class="form-select">
												<option v-for="item in perfMenu" :value="item.codeValue">
													{{ $filters.defaultValue(item.codeName, '--') }}
												</option>
											</select>
										</div>
									</div>
									<div class="form-row">
										<div id="maxRowIdTrFund" class="form-group col-12 col-lg-6" style="display: none">
											<label class="form-label tx-require"> {{ $t('pro.displayDataCount') }}</label>
											<select id="maxRowId" v-model="rowNumber" class="form-select">
												<option value="">
													{{ $t('pro.all') }}
												</option>
												<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-row">
										<div id="fundCmpTrFund" class="form-group col-12 col-lg-6" style="display: none">
											<button
												type="button"
												class="btn btn-primary"
												data-bs-toggle="modal"
												data-bs-target="#groupFundCmpTypeModalRef"
												@click="groupFundCmpTypeModalHandler"
											>
												{{ $t('pro.selectFundType') }}
											</button>
											<div v-for="item in fundCmpItem" class="form-check form-check-inline">
												<span class="form-check-label"> {{ $filters.defaultValue(item.compName, '--') }}</span>
												<a href="#" @click="deleteFundCmpItem(item.globalClassCode)"><img
													:src="getImgURL('icon', 'i-cancel.png')"
												></a>
											</div>
										</div>
									</div>

									<div class="form-footer">
										<a class="btn btn-primary" @click.prevent="gotoFastPage(0)">{{ $t('pro.search') }}</a>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="tab-pane fade" :class="{ 'active show': activeTab == 'weighted' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup3">
								<h4>{{ $t('pro.searchCond') }}</h4>
								<span class="tx-square-bracket">{{ $t('pro.requiredField') }}</span>
							</div>
							<div id="collapseListGroup3" class="collapse show">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-6">
											<label class="form-label">{{ $t('pro.fundType') }}</label>
											<button
												type="button"
												class="btn btn-primary"
												data-bs-toggle="modal"
												data-bs-target="#groupFundCmpTypeModalRef"
												@click="groupFundCmpTypeModalHandler"
											>
												{{ $t('pro.selectFundType') }}
											</button>
											<div v-for="item in fundCmpItem" class="form-check form-check-inline">
												<span class="form-check-label"> {{ $filters.defaultValue(item.compName, '--') }}</span>
												<a href="#" @click="deleteFundCmpItem(item.globalClassCode)"><img
													:src="getImgURL('icon', 'i-cancel.png')"
												></a>
											</div>
										</div>

										<div class="form-group col-12 col-lg-6">
											<label class="form-label tx-require"> {{ $t('pro.performanceCurrency') }} </label>
											<div class="form-check form-check-inline">
												<input
													id="curCode1"
													v-model="curCode"
													class="form-check-input"
													name="curCode"
													type="radio"
													value="TWD"
												>
												<label class="form-check-label" for="curCode1">{{ $t('pro.twd') }}</label>
											</div>
											<div class="form-check form-check-inline">
												<input
													id="curCode2"
													v-model="curCode"
													class="form-check-input"
													name="curCode"
													type="radio"
													value="USD"
												>
												<label class="form-check-label" for="curCode2">{{ $t('pro.originalCurrency') }}</label>
											</div>
										</div>

										<div class="form-group col-12 col-lg-4">
											<label class="form-label tx-require"> {{ $t('pro.displayDataCount') }}</label>
											<select id="maxRowId" v-model="rowNumber" class="form-select">
												<option value="">
													{{ $t('pro.all') }}
												</option>
												<option v-for="item in rowNumerMenu" :value="item.rangeFixed">
													{{ $filters.defaultValue(item.termName, '--') }}
												</option>
											</select>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="card card-table">
							<div class="card-header">
								<h4>{{ $t('pro.comparisonConditionWeightSetting') }}</h4>
							</div>
							<div class="table-responsive">
								<table class="table table-bordered table-blue">
									<thead>
										<tr>
											<th><span class="txtStar">{{ $t('pro.riskPerformance') }}</span><a href="#" /></th>
											<th><span class="txtStar">{{ $t('pro.weight') }}</span>(%)<a href="#" /></th>
											<th>&nbsp;</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>
												<select
													id="searchCodeName"
													v-model="searchCodeName"
													class="form-select"
													name="searchCodeName"
													:title="$t('pro.pleaseSelectRiskPerformanceType')"
													data-style="btn-white"
												>
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="item in proWeightedTypeMenu" :value="item.codeValue">
														{{ $filters.defaultValue(item.codeName, '--') }}
													</option>
												</select>
											</td>
											<td>
												<input
													v-model="searchValue"
													type="input"
													class="form-control"
													size="20"
												> {{ $t('pro.weight') }}%
											</td>

											<td>
												<button
													type="button"
													class="btn btn-info btn-icon"
													data-bs-toggle="tooltip"
													:title="$t('pro.add')"
													@click="newRank()"
												>
													<i class="fa-solid fa-plus" />
												</button>
											</td>
										</tr>
										<tr v-for="item in proSearchesMaps">
											<td>{{ $filters.defaultValue(item.searchName, '--') }}</td>
											<td>{{ item.searchValue }}</td>
											<td>
												<button
													type="button"
													class="btn btn-danger btn-icon"
													data-bs-toggle="tooltip"
													title=""
													:data-bs-original-title="$t('pro.delete')"
													@click="deleteWeighted(item.searchCodeName)"
												>
													<i class="fa-solid fa-trash" />
												</button>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="text-end mt-3">
							<input
								type="button"
								class="btn btn-primary"
								:value="$t('pro.historicalSearchConditions')"
								@click="openHisSearch()"
							>
							<input
								type="button"
								class="btn btn-small btn-primary"
								:value="$t('pro.search')"
								@click="getRankPage(0)"
							>
							<input
								v-if="showButton"
								type="button"
								class="btn btn-small btn-primary"
								:value="$t('pro.saveConditions')"
								@click="
									saveCondition();
									isOpenModal3 = true;
								"
							>
						</div>
					</div>

					<!-- Modal 2 歷史{{ $t('pro.search') }}條件 -->
					<vue-modal :is-open="isOpenModal2" @close="isOpenModal2 = false">
						<template #content="props">
							<div class="modal-dialog modal-xl">
								<div class="modal-content">
									<div class="modal-header">
										<h4 class="modal-title">
											{{ $t('pro.historicalSearchConditions') }}
										</h4>
										<button
											type="button"
											class="btn-close"
											aria-label="Close"
											@click.prevent="props.close()"
										/>
									</div>
									<div class="modal-body">
										<div class="card card-table">
											<div class="card-header">
												<h4>{{ $t('pro.searchList') }}</h4>
												<div dev-include-html="../../main/include/nav-pages.html" />
											</div>
											<div class="table-responsive">
												<table class="table table-bordered table-blue">
													<thead>
														<tr>
															<th>{{ $t('pro.searchName') }}</th>
															<th>{{ $t('pro.memo') }}</th>
															<th>{{ $t('pro.createdTime') }}</th>
															<th>{{ $t('pro.execute') }}</th>
														</tr>
													</thead>
													<tbody v-for="item in proSearches">
														<tr id="row1">
															<td>{{ $filters.defaultValue(item.searchName, '--') }}</td>
															<td>{{ $filters.defaultValue(item.memo, '--') }}</td>
															<td>{{ $filters.defaultValue(item.createDt, '--') }}</td>
															<td>
																<button
																	type="button"
																	class="btn btn-info btn-icon"
																	data-bs-toggle="tooltip"
																	:title="$t('pro.edit')"
																	@click="hisEdit(item)"
																>
																	<i class="fa-solid fa-pen" />
																</button>
																<button
																	type="button"
																	class="btn btn-danger btn-icon"
																	data-bs-toggle="tooltip"
																	:title="$t('pro.delete')"
																	@click="hisDelete(item)"
																>
																	<i class="fa-solid fa-trash" />
																</button>
															</td>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
									</div>
									<div class="modal-footer">
										<input
											type="button"
											class="btn btn-white"
											:value="$t('pro.close')"
											@click.prevent="props.close()"
										>
									</div>
								</div>
							</div>
						</template>
					</vue-modal>
					<!-- modal 2 end -->

					<!-- modal 3 儲存條件 -->
					<vue-modal :is-open="isOpenModal3" @close="isOpenModal3 = false">
						<template #content="props">
							<div class="modal-dialog modal-xl">
								<div class="modal-content">
									<div class="modal-header">
										<h4 class="modal-title">
											{{ $t('pro.saveConditions') }}
										</h4>
										<button
											type="button"
											class="btn-close"
											aria-label="Close"
											@click.prevent="props.close()"
										/>
									</div>
									<div class="modal-body">
										<div class="card card-table">
											<div class="table-responsive">
												<table class="table table-bordered table-blue">
													<tbody>
														<tr>
															<th class="w20 tx-require">
																{{ $t('pro.searchName') }}
															</th>
															<td><input v-model="searchName" type="text" class="form-control"></td>
														</tr>
														<tr>
															<th class="w20 tx-require">
																{{ $t('pro.memo') }}
															</th>
															<td>
																<textarea
																	id=""
																	v-model="memo"
																	name=""
																	cols="50"
																	rows="5"
																	class="form-control"
																/>
															</td>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
									</div>
									<div class="modal-footer">
										<input
											type="button"
											class="btn btn-primary"
											:value="$t('pro.save')"
											@click.prevent="
												conditionsSave();
												props.close();
											"
										>
										<input
											type="button"
											class="btn btn-white"
											:value="$t('pro.close')"
											@click.prevent="props.close()"
										>
									</div>
								</div>
							</div>
						</template>
					</vue-modal>
					<!-- modal 3 end -->

					<div class="tab-pane fade" :class="{ 'active show': activeTab == 'advanced' }">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup4">
								<h4>{{ $t('pro.searchCond') }}</h4>
							</div>
							<div id="collapseListGroup4" class="collapse show">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-6">
											<label class="form-label">{{ $t('pro.filterConditions') }}</label>
											<div class="form-check form-check-inline" @click="getfundTypeMenu('LOCAL')">
												<input
													id="fundType1"
													v-model="fundSaleType"
													class="form-check-input"
													name="fundType"
													type="radio"
													value="LOCAL"
												>
												<label class="form-check-label" for="fundType1">{{ $t('pro.bankFunds') }}</label>
											</div>
											<div class="form-check form-check-inline" @click="getfundTypeMenu('ALL')">
												<input
													id="fundType2"
													v-model="fundSaleType"
													class="form-check-input"
													name="fundType"
													type="radio"
													value="ALL"
												>
												<label class="form-check-label" for="fundType2">{{ $t('pro.allDatabaseFunds') }}</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup4-1">
								<h4>{{ $t('pro.fundData') }}</h4>
							</div>
							<div id="collapseListGroup4-1" class="collapse show">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-6">
											<label class="form-label"> {{ $t('pro.bankFundCode') }} </label>
											<input v-model="bankProCodes" class="form-control" type="text">
										</div>
										<div class="form-group col-12 col-lg-6">
											<span class="tx-square-bracket">{{ $t('pro.pleaseSeperateCodesWithComma') }}</span>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> {{ $t('pro.fundName') }} </label>
											<input
												v-model="fundCname"
												class="form-control"
												type="text"
												maxlength="20"
											>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> {{ $t('pro.fundEnglishName') }} </label>
											<input
												v-model="fundName"
												class="form-control"
												type="text"
												maxlength="20"
											>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> {{ $t('pro.fundCompany') }} </label>
											<div class="input-group">
												<div class="form-check form-check-inline">
													<input
														id="localYn1"
														v-model="localYn"
														class="form-check-input"
														name="localYn"
														type="radio"
														value="Y"
														@click="getCmpCodeMenu('Y')"
													><label
														class="form-check-label"
														for="localYn1"
													>{{ $t('pro.domestic') }}</label>
												</div>
												<div class="form-check form-check-inline">
													<input
														id="localYn2"
														v-model="localYn"
														class="form-check-input"
														name="localYn"
														type="radio"
														value="N"
														@click="getCmpCodeMenu('N')"
													><label
														class="form-check-label"
														for="localYn2"
													>{{ $t('pro.foreign') }}</label>
												</div>
												<select v-model="compCode" name="local" class="form-select">
													<option value="">
														{{ $t('pro.all') }}
													</option>
													<option v-for="item in cmpCodeMenu" :value="item.companyCode">
														{{ $filters.defaultValue(item.cname, '--') }}
													</option>
												</select>
											</div>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> {{ $t('pro.currency') }}</label>
											<select
												id="fundCurMenu"
												ref="fundCurMenu"
												v-model="curObjs"
												class="form-control"
												multiple
												:title="$t('pro.pleaseSelectCurrency')"
												data-style="btn-white"
											>
												<option value="">
													{{ $t('pro.all') }}
												</option>
												<option v-for="(item, index) in curOption" :key="index" :value="item.value">
													{{ $filters.defaultValue(item.name, '--') }}
												</option>
											</select>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> {{ $t('pro.fundType') }} </label>
											<select v-model="investmentTypeCode" name="TypeCode" class="form-select">
												<option value="">
													{{ $t('pro.all') }}
												</option>
												<option v-for="(item, index) in investmentTypeMenu" :key="index" :value="item.investmentTypeCode">
													{{ $filters.defaultValue(item.name, '--') }}
												</option>
											</select>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> {{ $t('pro.investmentRegion') }} </label>
											<select v-model="geoFocusCode" name="GeoFocus" class="form-select">
												<option value="">
													{{ $t('pro.all') }}
												</option>
												<option v-for="(item, index) in geoFocusMenu" :key="index" :value="item.geoFocusCode">
													{{ $filters.defaultValue(item.name, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-6">
											<label class="form-label mg-e-60"> {{ $t('pro.investmentTargetLipperGlobal') }} </label>
											<select v-model="globalClassCode" name="GlobalClass" class="form-select">
												<option value="">
													{{ $t('pro.all') }}
												</option>
												<option v-for="(item, index) in globalClassMenu" :key="index" :value="item.globalClassCode">
													{{ $filters.defaultValue(item.name, '--') }}
												</option>
											</select>
										</div>

										<div class="form-group col-12 col-lg-6">
											<label class="form-label mg-e-60"> {{ $t('pro.investmentTargetLipperRegional') }} </label>
											<select v-model="localClassCode" name="LocalClass" class="form-select">
												<option value="">
													{{ $t('pro.all') }}
												</option>
												<option v-for="(item, index) in localClassMenu" :key="index" :value="item.localClassCode">
													{{ $filters.defaultValue(item.name, '--') }}
												</option>
											</select>
										</div>
									</div>

									<div class="form-footer">
										<a class="btn btn-primary" @click.prevent="getAdvancePage(0, 'info')">{{ $t('pro.search') }}</a>
									</div>
								</div>
							</div>
						</div>

						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup4-2">
								<h4>{{ $t('pro.fundRisk') }}</h4>
							</div>
							<div id="collapseListGroup4-2" class="collapse show">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> {{ $t('pro.byStandardDeviation') }} </label>
											<select v-model="stdValueType" name="volatility" class="form-select">
												<option value="ALL">
													{{ $t('pro.all') }}
												</option>
												<option value="X1">
													x &lt;= 2
												</option>
												<option value="X2">
													2 &lt; x &lt;= 6
												</option>
												<option value="X3">
													6 &lt; x
												</option>
											</select>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> {{ $t('pro.byBetaValue') }} </label>
											<select v-model="betaValueType" name="beta" class="form-select">
												<option value="ALL">
													{{ $t('pro.all') }}
												</option>
												<option value="X1">
													x &lt;= 0
												</option>
												<option value="X2">
													0 &lt; x &lt;= 1
												</option>
												<option value="X3">
													1 &lt; x
												</option>
											</select>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> {{ $t('pro.byCorrelationCoefficient') }} </label>
											<select v-model="corValueType" name="beta" class="form-select">
												<option value="ALL">
													{{ $t('pro.all') }}
												</option>
												<option value="X1">
													x &lt;= 0.5
												</option>
												<option value="X2">
													0.5 &lt; x &lt;= 0.8
												</option>
												<option value="X3">
													0.8 &lt; x
												</option>
											</select>
										</div>
									</div>

									<div class="form-footer">
										<a class="btn btn-primary" @click.prevent="getAdvancePage(0, 'basic')">{{ $t('pro.search') }}</a>
									</div>
								</div>
							</div>
						</div>

						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup4-3">
								<h4>{{ $t('pro.fundPerformance') }}</h4>
							</div>
							<div id="collapseListGroup4-3" class="collapse show">
								<div class="card-body">
									<div class="form-row">
										<div class="form-group col-12 col-lg-6">
											<label class="form-label"> {{ $t('pro.byAnnualPerformance') }} </label>
											<div class="input-group">
												<select id="year" v-model="assetStatPctCode" class="form-select">
													<option v-for="item in yearList" :value="item.value">
														{{ $filters.formatNumber(item.name, '--') }}
													</option>
												</select>

												<div class="input-group-text">
													{{ $t('pro.year') }}
												</div>
												<div class="input-group-text">
													{{ $t('pro.performance') }}
												</div>

												<select v-model="annualPerfType" name="yearPerfmType" class="form-select">
													<option value="ALL">
														{{ $t('pro.all') }}
													</option>
													<option value="X1">
														x &lt;= 0%
													</option>
													<option value="X2" selected>
														0 &lt; x &lt;= 30%
													</option>
													<option value="X3">
														30% &lt; x &lt;= 100%
													</option>
													<option value="X4">
														100% &lt; x
													</option>
												</select>
											</div>
										</div>
										<div class="form-group col-12 col-lg-6">
											<label class="form-label"> {{ $t('pro.byCumulativePerformance') }} </label>
											<div class="input-group">
												<select v-model="perfRangeType" name="accPerfm" class="form-select">
													<option value="ALL">
														{{ $t('pro.all') }}
													</option>
													<option value="PCTLTD">
														{{ $t('pro.sinceInception') }}
													</option>
													<option value="PCTYTD">
														{{ $t('pro.yearToDate') }}
													</option>
													<option value="PCT1MTD">
														{{ $t('pro.1Month') }}
													</option>
													<option value="PCT3MTD">
														{{ $t('pro.3Month') }}
													</option>
													<option value="PCT6MTD">
														{{ $t('pro.6Month') }}
													</option>
													<option value="PCT9MTD">
														{{ $t('pro.9Month') }}
													</option>
													<option value="PCT1YTD">
														{{ $t('pro.1Year') }}
													</option>
													<option value="PCT2YTD">
														{{ $t('pro.2Year') }}
													</option>
													<option value="PCT3YTD">
														{{ $t('pro.3Year') }}
													</option>
													<option value="PCT5YTD">
														{{ $t('pro.5Year') }}
													</option>
												</select>
												<div class="input-group-text">
													{{ $t('pro.period') }}
												</div>
												<div class="input-group-text">
													{{ $t('pro.performance') }}
												</div>

												<select v-model="perfType" name="accPerfmType" class="form-select">
													<option value="ALL">
														{{ $t('pro.all') }}
													</option>
													<option value="X1">
														x &lt;= 0%
													</option>
													<option value="X2">
														0 &lt; x &lt;= 30%
													</option>
													<option value="X3">
														30% &lt; x &lt;= 100%
													</option>
													<option value="X4">
														100% &lt; x
													</option>
												</select>
											</div>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> {{ $t('pro.lipperRanking') }} </label>
											<div class="input-group">
												<select v-model="lipperPointer" name="lipperRankType" class="form-select">
													<option value="ALL">
														{{ $t('pro.all') }}
													</option>
													<option value="TOTRETOV ">
														{{ $t('pro.overallReturn') }}
													</option>
													<option value="CONSRETOV">
														{{ $t('pro.stableReturn') }}
													</option>
													<option value="CAPPRESOV">
														{{ $t('pro.conservativeReturn') }}
													</option>
												</select>
											</div>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> {{ $t('pro.indicator') }} {{ $t('pro.ranking') }}</label>
											<div class="input-group">
												<select v-model="rank" name="lipperRankType" class="form-select">
													<option value="ALL">
														{{ $t('pro.all') }}
													</option>
													<option value="ONE">
														1
													</option>
													<option value="TWO">
														2
													</option>
													<option value="THREE">
														3
													</option>
													<option value="FOUR">
														4
													</option>
													<option value="FIVE">
														5
													</option>
												</select>
											</div>
										</div>
										<div class="form-group col-12 col-lg-4">
											<label class="form-label"> {{ $t('pro.sharpeValue') }} </label>
											<select v-model="sharpeValueType" name="sharpe" class="form-select">
												<option value="ALL">
													{{ $t('pro.all') }}
												</option>
												<option value="X1">
													x &lt;= 0
												</option>
												<option value="X2">
													0 &lt; x &lt;= 1
												</option>
												<option value="X3">
													1 &lt; x
												</option>
											</select>
										</div>
									</div>

									<div class="form-row">
										<div class="form-group col-12 col-lg-6">
											<label class="form-label mg-e-10">{{ $t('pro.lumpSumInvestmentReturn') }} </label>
											<div class="d-inline-block">
												<select v-model="investRange" name="investRange" class="form-select">
													<option value="ALL">
														{{ $t('pro.all') }}
													</option>
													<option value="R1">
														10,000 &lt;= x &lt; 12,000
													</option>
													<option value="R2">
														12,000 &lt;= x &lt; 15,000
													</option>
													<option value="R3">
														15,000 &lt;= x
													</option>
												</select>
											</div>
										</div>
										<div class="form-group col-12 col-lg-6">
											<label class="form-label mg-e-10"> {{ $t('pro.periodicInvestmentReturn') }}</label>
											<div class="d-inline-block">
												<select v-model="dcaRange" name="dcaRange" class="form-select">
													<option value="ALL">
														{{ $t('pro.all') }}
													</option>
													<option value="R1">
														x &lt;= 20,000
													</option>
													<option value="R2">
														20,000 &lt; x &lt;= 30,000
													</option>
													<option value="R3">
														30,000 &lt; x
													</option>
												</select>
											</div>
										</div>
									</div>

									<div class="form-footer">
										<a class="btn btn-primary" @click.prevent="getAdvancePage(0, 'performance')">{{ $t('pro.search') }}</a>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div v-if="pageData.content.length > 0" id="searchResult">
				<div class="tab-nav-line">
					<ul
						class="nav nav-line"
						style="background-color: transparent; margin-top: 10px; margin-bottom: 20px; border-bottom: 2px solid #e2e2e2"
					>
						<li class="nav-item">
							<a
								href="#pie_chart1"
								data-bs-toggle="tab"
								class="nav-link"
								:class="{ active: selectedTab === 'pie_chart1' }"
								@click.prevent="setSelectedTab('pie_chart1')"
							>{{ $t('pro.basicData') }}</a>
						</li>
						<li class="nav-item">
							<a
								href="#pie_chart2"
								data-bs-toggle="tab"
								class="nav-link"
								:class="{ active: selectedTab === 'pie_chart2' }"
								@click.prevent="setSelectedTab('pie_chart2')"
							>{{ $t('pro.returnRate') }}</a>
						</li>
						<li class="nav-item">
							<a
								href="#pie_chart3"
								data-bs-toggle="tab"
								class="nav-link"
								:class="{ active: selectedTab === 'pie_chart3' }"
								@click.prevent="setSelectedTab('pie_chart3')"
							>{{ $t('pro.other') }}</a>
						</li>
					</ul>
					<!-- 一般篩選與進階搜尋共用{{ $t('pro.searchResults') }}(分頁重查呼叫不同資料來源) -->
					<div class="tab-content">
						<div id="pie_chart1" role="tabpanel" class="tab-pane active">
							<div class="card card-table">
								<div class="card-header">
									<h4>{{ $t('pro.searchResults') }}</h4>
									<div style="display: flex">
										<vue-pagination
											:pageable="pageData"
											:goto-page="activeTab == 'common' ? gotoPage : getAdvancePage"
										/>
										<button
											type="button"
											class="btn btn-info ms-2"
											@click="performancesCompareModelHandler()"
										>
											{{ $t('pro.performanceComparisonChart') }}
										</button>
									</div>
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-bordered text-center">
										<thead>
											<tr>
												<th class="wd-100 text-start">
													{{ $t('pro.addToComparison') }}
												</th>
												<th>商品代號<a href="#" class="icon-sort" @click="sort('BANK_PRO_CODE')" /></th>
												<th class="10% text-start">
													商品中文名稱<a href="#" class="icon-sort" @click="sort('PRO_NAME')" />
												</th>
												<th>基金類型<a href="#" class="icon-sort" @click="sort('PROTYPE_NAME')" /></th>
												<th>計價幣別<a href="#" class="icon-sort" @click="sort('CUR_CODE')" /></th>
												<th>風險等級<a href="#" class="icon-sort" @click="sort('RISK_NAME')" /></th>
												<th>淨值<a href="#" class="icon-sort" @click="sort('PRICE_LC')" /></th>
												<th>淨值日期<a href="#" class="icon-sort" @click="sort('D_DATE')" /></th>
												<th>前一日漲跌幅<a href="#" class="icon-sort" @click="sort('PRICE_FLUC_LC')" /></th>
												<th class="text-center" width="120">
													執行
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="(item, index) in pageData.content">
												<td :data-th="$t('pro.addToComparison')" class="text-start text-center">
													<input
														:id="'id-' + item.bankProCode"
														v-model="selectedItems[item.lipperId]"
														class="form-check-input text-center"
														type="checkbox"
														:disabled="!item.lipperId"
													>
													<label class="form-check-label" :for="'id-' + item.bankProCode" />
												</td>
												<td :data-th="$t('pro.productCode')">
													<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
												</td>
												<td class="text-start" :data-th="$t('pro.productName')">
													<span v-if="item.proCode != null && item.pfcatCode != null && item.proName != null">
														<a class="tx-link" @click="fundModalHandler(item.proCode, item.pfcatCode)">{{
															$filters.defaultValue(item.proName, '--')
														}}</a>
													</span>
													<span v-else>
														{{ item.proName != null ? item.proName : '--' }}
													</span>
												</td>
												<td class="text-end" :data-th="$t('pro.fundType')">
													<span>{{ $filters.defaultValue(item.proTypeName, '--') }}</span>
												</td>
												<td class="text-end" :data-th="$t('pro.currency')">
													<span>{{ $filters.defaultValue(item.curCode, '--') }}</span>
												</td>
												<td class="text-end" :data-th="$t('pro.riskLevel')">
													<span>{{ $filters.defaultValue(item.riskName, '--') }}</span>
												</td>
												<td class="text-end" :data-th="$t('pro.netAssetValue')">
													<span>{{ $filters.formatNumber(item.priceLc, '0,0.00' || '--') }}</span>
												</td>
												<td class="text-end" :data-th="$t('pro.navDate')">
													<span>{{ $filters.defaultValue(item.ddate, '--') }}</span>
												</td>
												<td class="text-end" :data-th="$t('pro.dailyChange')">
													<span>{{ $filters.formatNumber(item.priceFlucLc, '0,0.00' || '--') }}</span>
												</td>
												<td :data-th="$t('pro.execute')">
													<span v-if="item.proCode">
														<button
															v-if="activeTab === 'fast' && fastCode === '06'"
															type="button"
															class="btn btn-primary"
															:title="$t('pro.removeFromFavorites')"
															@click="remove(item.proCode)"
														>
															{{ $t('pro.removeFavorite') }}
														</button>
														<button
															v-else
															type="button"
															class="btn btn-dark btn-icon"
															data-bs-toggle="tooltip"
															:title="$t('pro.addToFavorites')"
															@click="favoritesHandler(item.proCode, item.pfcatCode)"
														>
															<i class="bi bi-heart text-danger" />
														</button>
													</span>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
						<div id="pie_chart2" role="tabpanel" class="tab-pane">
							<div class="form-check-group">
								<div class="form-check form-check-inline">
									<input
										id="rateO"
										v-model="rateCurType"
										class="form-check-input"
										name="intRateRank"
										type="radio"
										value="O"
									>
									<label class="form-check-label" for="rateO">{{ $t('pro.returnRateDisplayOriginal') }}</label>

									<input
										id="rateT"
										v-model="rateCurType"
										class="form-check-input ms-2"
										name="intRateRank"
										type="radio"
										value="T"
									>
									<label class="form-check-label ms-2" for="rateT">{{ $t('pro.returnRateDisplayTWD') }}</label>
								</div>
							</div>
							<div class="card card-table">
								<div class="card-header">
									<h4>{{ $t('pro.searchResults') }}</h4>
									<div style="display: flex">
										<vue-pagination
											:pageable="pageData"
											:goto-page="activeTab == 'common' ? gotoPage : getAdvancePage"
										/>
										<button
											type="button"
											class="btn btn-info ms-2"
											@click="performancesCompareModelHandler()"
										>
											{{ $t('pro.performanceComparisonChart') }}
										</button>
									</div>
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-bordered text-center">
										<thead>
											<tr>
												<th rowspan="2" class="wd-100 text-start">
													{{ $t('pro.select') }}
												</th>
												<th rowspan="2">
													商品代號<a href="#" class="icon-sort" @click="sort('BANK_PRO_CODE')" />
												</th>
												<th rowspan="2" class="10% text-start">
													商品中文名稱<a href="#" class="icon-sort" @click="sort('PRO_NAME')" />
												</th>
												<th colspan="8">
													<span>{{ $filters.defaultValue(rateCurType === 'O' ? $t('pro.originalCurrencyReturnRate') : $t('pro.twdReturnRate'), '--') }}</span>
												</th>
											</tr>
											<tr>
												<th>
													{{ $t('pro.returnRate1Day') }}<a
														href="#"
														class="icon-sort"
														@click="sort(rateCurType === 'O' ? 'RETURN_FC' : 'RETURN_LC')"
													/>
												</th>
												<th>
													{{ $t('pro.returnRate1Month') }}<a
														href="#"
														class="icon-sort"
														@click="sort(rateCurType === 'O' ? 'FC_1M_RETURN' : 'LC_1M_RETURN')"
													/>
												</th>
												<th>
													{{ $t('pro.returnRate3Month') }}<a
														href="#"
														class="icon-sort"
														@click="sort(rateCurType === 'O' ? 'FC_3M_RETURN' : 'LC_3M_RETURN')"
													/>
												</th>
												<th>
													{{ $t('pro.returnRate6Month') }}<a
														href="#"
														class="icon-sort"
														@click="sort(rateCurType === 'O' ? 'FC_6M_RETURN' : 'LC_6M_RETURN')"
													/>
												</th>
												<th>
													{{ $t('pro.returnRate1Year') }}<a
														href="#"
														class="icon-sort"
														@click="sort(rateCurType === 'O' ? 'FC_1Y_RETURN' : 'LC_1Y_RETURN')"
													/>
												</th>
												<th>
													{{ $t('pro.returnRate3Year') }}<a
														href="#"
														class="icon-sort"
														@click="sort(rateCurType === 'O' ? 'FC_3Y_RETURN' : 'LC_3Y_RETURN')"
													/>
												</th>
												<th>
													{{ $t('pro.returnRateYTD') }}<a
														href="#"
														class="icon-sort"
														@click="sort(rateCurType === 'O' ? 'FC_YTD_RETURN' : 'LC_YTD_RETURN')"
													/>
												</th>
												<th class="text-center" width="120">
													執行
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="item in pageData.content">
												<td :data-th="$t('pro.addToComparison')" class="text-start text-center">
													<input
														:id="'id-' + item.bankProCode"
														v-model="selectedItems[item.lipperId]"
														class="form-check-input text-center"
														type="checkbox"
														:disabled="!item.lipperId"
													>
													<label class="form-check-label" :for="'id-' + item.bankProCode" />
												</td>
												<td :data-th="$t('pro.productCode')">
													<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
												</td>
												<td class="text-start" :data-th="$t('pro.productName')">
													<span>
														<a class="tx-link" @click="fundModalHandler(item.proCode, item.pfcatCode)">{{
															$filters.defaultValue(item.proName, '--')
														}}</a>
													</span>
												</td>
												<td class="text-end" :data-th="$t('pro.returnRate1Day')">
													<span>{{
														$filters.defaultValue(
															rateCurType === 'O'
																? $filters.formatPct(item.returnFc)
																: $filters.formatPct(item.returnLc),
															'--'
														)
													}}
														%
													</span>
												</td>
												<td class="text-end" :data-th="$t('pro.returnRate1Month')">
													<span>{{
														$filters.defaultValue(
															rateCurType === 'O'
																? $filters.formatPct(item.fc1mReturn)
																: $filters.formatPct(item.lc1mReturn),
															'--'
														)
													}}
														%
													</span>
												</td>
												<td class="text-end" :data-th="$t('pro.returnRate3Month')">
													<span>{{
														$filters.defaultValue(
															rateCurType === 'O'
																? $filters.formatPct(item.fc3mReturn)
																: $filters.formatPct(item.lc3mReturn),
															'--'
														)
													}}
														%
													</span>
												</td>
												<td class="text-end" :data-th="$t('pro.returnRate6Month')">
													<span>{{
														$filters.defaultValue(
															rateCurType === 'O'
																? $filters.formatPct(item.fc6mReturn)
																: $filters.formatPct(item.lc6mReturn),
															'--'
														)
													}}
														%
													</span>
												</td>
												<td class="text-end" :data-th="$t('pro.returnRate1Year')">
													<span>{{
														$filters.defaultValue(
															rateCurType === 'O'
																? $filters.formatPct(item.fc1yReturn)
																: $filters.formatPct(item.lc1yReturn),
															'--'
														)
													}}
														%
													</span>
												</td>
												<td class="text-end" :data-th="$t('pro.returnRate3Year')">
													<span>{{
														$filters.defaultValue(
															rateCurType === 'O'
																? $filters.formatPct(item.fc3yReturn)
																: $filters.formatPct(item.lc3yReturn),
															'--'
														)
													}}
														%
													</span>
												</td>
												<td class="text-end" :data-th="$t('pro.returnRateYTD')">
													<span>{{
														$filters.defaultValue(
															rateCurType === 'O'
																? $filters.formatPct(item.fcYtdReturn)
																: $filters.formatPct(item.lcYtdReturn),
															'--'
														)
													}}
														%
													</span>
												</td>
												<td :data-th="$t('pro.execute')">
													<button
														v-if="activeTab === 'fast' && fastCode === '06'"
														type="button"
														class="btn btn-primary"
														:title="$t('pro.removeFromFavorites')"
														@click="remove(item.proCode)"
													>
														{{ $t('pro.removeFavorite') }}
													</button>
													<button
														v-else
														type="button"
														class="btn btn-dark btn-icon"
														data-bs-toggle="tooltip"
														:title="$t('pro.addToFavorites')"
														@click="favoritesHandler(item.proCode, item.pfcatCode)"
													>
														<i class="bi bi-heart text-danger" />
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
						<div id="pie_chart3" role="tabpanel" class="tab-pane">
							<div class="card card-table">
								<div class="card-header">
									<h4>{{ $t('pro.searchResults') }}</h4>
									<div style="display: flex">
										<vue-pagination
											:pageable="pageData"
											:goto-page="activeTab == 'common' ? gotoPage : getAdvancePage"
										/>
										<button
											type="button"
											class="btn btn-info ms-2"
											@click="performancesCompareModelHandler()"
										>
											{{ $t('pro.performanceComparisonChart') }}
										</button>
									</div>
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-bordered text-center">
										<thead>
											<tr>
												<th class="wd-100 text-start">
													{{ $t('pro.select') }}
												</th>
												<th>商品代號<a href="#" class="icon-sort" @click="sort('BANK_PRO_CODE')" /></th>
												<th class="10% text-start">
													商品中文名稱<a href="#" class="icon-sort" @click="sort('PRO_NAME')" />
												</th>
												<th>{{ $t('pro.annualizedStandardDeviation') }}<a href="#" class="icon-sort" @click="sort('VALUE_USD_STD')" /></th>
												<th>Sharpe<a href="#" class="icon-sort" @click="sort('VALUE_USD_SHP')" /></th>
												<th>Beta<a href="#" class="icon-sort" @click="sort('VALUE_USD_BET')" /></th>
												<th>{{ $t('pro.lipperOverallRating') }}<br>3年</th>
												<th>{{ $t('pro.lipperStableReturn') }}<br>3年</th>
												<th>{{ $t('pro.lipperCapitalPreservation') }}<br>3年</th>
												<th class="text-center" width="120">
													執行
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="item in pageData.content">
												<td :data-th="$t('pro.addToComparison')" class="text-start text-center">
													<input
														:id="'id-' + item.bankProCode"
														v-model="selectedItems[item.lipperId]"
														class="form-check-input text-center"
														type="checkbox"
														:disabled="!item.lipperId"
													>
													<label class="form-check-label" :for="'id-' + item.bankProCode" />
												</td>
												<td :data-th="$t('pro.productCode')">
													<span>{{ $filters.defaultValue(item.bankProCode, '--') }}</span>
												</td>
												<td class="text-start" :data-th="$t('pro.productName')">
													<span>
														<a class="tx-link" @click="fundModalHandler(item.proCode, item.pfcatCode)">{{
															$filters.defaultValue(item.proName, '--')
														}}</a>
													</span>
												</td>
												<td class="text-end" :data-th="$t('pro.annualizedStandardDeviation')">
													<span>{{ $filters.defaultValue($filters.formatPct(item.valueUsdStd), '--') }}%</span>
												</td>
												<td class="text-end" data-th="Sharpe">
													<span>{{ $filters.defaultValue($filters.formatPct(item.valueUsdShp), '--') }}%</span>
												</td>
												<td class="text-end" data-th="Beta">
													<span>{{ $filters.defaultValue($filters.formatPct(item.valueUsdBet), '--') }}%</span>
												</td>
												<td class="text-end" data-th="{{ $t('pro.lipperOverallRating') }}3年">
													<span>{{ $filters.formatNumber(item.scoreTot, '--') }}</span>
												</td>
												<td class="text-end" data-th="{{ $t('pro.lipperStableReturn') }}3年">
													<span>{{ $filters.formatNumber(item.scoreCon, '--') }}</span>
												</td>
												<td class="text-end" data-th="{{ $t('pro.lipperCapitalPreservation') }}3年">
													<span>{{ $filters.formatNumber(item.scoreCap, '--') }}</span>
												</td>
												<td :data-th="$t('pro.execute')">
													<button
														v-if="activeTab === 'fast' && fastCode === '06'"
														type="button"
														class="btn btn-primary"
														:title="$t('pro.removeFromFavorites')"
														@click="remove(item.proCode)"
													>
														{{ $t('pro.removeFavorite') }}
													</button>
													<button
														v-else
														type="button"
														class="btn btn-dark btn-icon"
														data-bs-toggle="tooltip"
														:title="$t('pro.addToFavorites')"
														@click="favoritesHandler(item.proCode, item.pfcatCode)"
													>
														<i class="bi bi-heart text-danger" />
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- 加權條件篩選 {{ $t('pro.searchResults') }}  -->
			<div v-if="rankPageData.length > 0" id="searchResult">
				<div class="tab-nav-line">
					<div class="tab-content">
						<div role="tabpanel" class="tab-pane active">
							<div class="card card-table">
								<div class="card-header">
									<h4>{{ $t('pro.searchResults') }}</h4>
									<div style="display: flex">
										<vue-pagination :pageable="rankPageData" :goto-page="getRankPageData" />
									</div>
								</div>
								<div class="table-responsive">
									<table class="table table-RWD table-bordered text-center">
										<thead>
											<tr>
												<th>商品代號</th>
												<th class="10% text-start">
													商品中文名稱
												</th>
												<th v-for="rank in proSearchesMaps">
													{{ rank.searchName }}
												</th>
												<th>{{ $t('pro.weightedScore') }}</th>
												<th class="text-center" width="120">
													執行
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="(item, index) in rankPageData">
												<td :data-th="$t('pro.productCode')">
													<span>{{ $filters.defaultValue(item.BANK_PRO_CODE, '--') }}</span>
												</td>
												<td class="text-start" :data-th="$t('pro.productName')">
													<span>
														<a class="tx-link" @click="fundModalHandler(item.PRO_CODE, item.PFCAT_CODE, $event)">{{
															$filters.defaultValue(item.PRO_NAME, '--')
														}}</a>
													</span>
												</td>
												<td v-for="rank in proSearchesMaps" class="text-end" :data-th="rank.searchName">
													{{ getRankValue(index, rank.searchCodeName) }}
												</td>
												<td :data-th="$t('pro.weightedScore')">
													<span>{{ $filters.formatNumber(item.SCORE, '--') }}</span>
												</td>
												<td :data-th="$t('pro.execute')">
													<button
														type="button"
														class="btn btn-dark btn-icon"
														data-bs-toggle="tooltip"
														:title="$t('pro.observe')"
														@click="favoritesHandler(item.PRO_CODE, item.PFCAT_CODE)"
													>
														<i class="bi bi-heart" />
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<vue-modal :is-open="isOpenCompareModal" @close="isOpenCompareModal = false">
		<template #content="props">
			<vue-performances-compare-modal
				id="performancesCompareModal"
				ref="performancesCompareModalRef"
				:close="props.close"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenCmpTypeModal" @close="isOpenCmpTypeModal = false">
		<template #content="props">
			<vue-group-fundcmp-modal
				id="groupFundCmpTypeModal"
				ref="groupFundCmpTypeModalRef"
				:close="props.close"
				:issuer-prop="fundCmpItem"
				@selected="selectedFundCmp"
			/>
		</template>
	</vue-modal>
	<vue-modal :is-open="isOpenGeoFocusModal" @close="isOpenGeoFocusModal = false">
		<template #content="props">
			<vue-group-geofocus-modal
				id="groupGeoFocusModal"
				ref="groupGeoFocusModalRef"
				:close="props.close"
				:issuer-prop="geoFocusItem"
				@selected="selectedGeoFocus"
			/>
		</template>
	</vue-modal>
</template>
<script>
import _ from 'lodash';
import vueGroupFundcmpModal from './groupFundCmpModal.vue';
import vueGroupGeofocusModal from './groupGeoFocusModal.vue';
import vueModal from '@/views/components/model.vue';
import pagination from '@/views/components/pagination.vue';
import performancesCompareModal from './performancesCompareModal.vue';
import Modal from 'bootstrap/js/dist/modal.js';
import { getImgURL } from '@/utils/imgURL.js';

export default {
	components: {
		'vue-pagination': pagination,
		vueModal,
		vueGroupFundcmpModal,
		vueGroupGeofocusModal,
		'vue-performances-compare-modal': performancesCompareModal
	},
	props: {
		fundModalHandler: Function,
		favoritesHandler: Function,
		curOption: {
			type: Array,
			default: () => []
		},
		riskMenu: Array
	},
	data: function () {
		return {
			groupGeoFocusModalRef: {},
			groupFundCmpTypeModalRef: {},
			proWeightedTypeMenu: [],

			activeTab: 'common',
			selectedTab: 'pie_chart1',
			bankProCode: null, // 商品代碼
			proName: null, // 商品名稱
			selectedItems: {}, // 加入比較選項
			curObjs: [], // 計價幣別
			riskCodes: [], // 風險等級
			genLocalYn: '', // 境內外基金
			proTypeCode: '', // 基金類型
			intFreqUnitType: '', // 配息頻率
			intRateRank: '', // 配息率
			backEndLoadYn: '', // 申購手續費
			isinCode: '',
			lipperPoint: '', // Lipper評級-指標
			lipperRank: '', // Lipper評級-排名
			fundCmpItem: [], // 基金公司
			geoFocusItem: [], // {{ $t('pro.investmentRegion') }}
			statCode: 'PCT1TD', // 報酬率
			maxDValue: null, // 報酬率迄
			minDValue: null, // 報酬率起

			nation: '', // {{ $t('pro.domestic') }}還{{ $t('pro.foreign') }}基金公司，預設{{ $t('pro.domestic') }}

			fundFastMenu: [], // 快速{{ $t('pro.filterConditions') }}
			timeRangeMenu: [], // {{ $t('pro.displayInterval') }}
			rowNumerMenu: [], // {{ $t('pro.displayDataCount') }}
			perfMenu: [], // {{ $t('pro.targetPerformance') }}選項
			globalClassCodeMenu: [], // 前10筆{{ $t('pro.fundType') }}選項
			globalOtherCodeMenu: [], // 其餘{{ $t('pro.fundType') }}選項

			fastCode: '03', // 快速篩選
			timeRange: null, // 快速 {{ $t('pro.displayInterval') }}
			rowNumber: '', // 快速 {{ $t('pro.displayDataCount') }}
			perf: null, // {{ $t('pro.targetPerformance') }}

			// 商品{{ $t('pro.search') }} 畫面
			localMenu: [], // 境內外基金選項
			proTypeMenu: [], // 基金類型下拉
			intFreqUnitMenu: [], // 配息頻率下拉
			intRateRankMenu: [], // 配息率選項
			backEndLoadMenu: [], // 申購手續費選項
			statCodeMenu: [], // 報酬率選項

			// 比較條件權重設定
			searchCodeName: '', // 風險/績效
			searchValue: '', // 權重%
			proSearchesMaps: [], // 風險/績效陣列
			proSearches: [], // 歷史{{ $t('pro.search') }}條件列表
			showButton: false, // 儲存條件按鈕

			// 進階搜尋
			fundSaleType: 'LOCAL', // {{ $t('pro.fundType') }}
			bankProCodes: null, // 本行基金代碼
			fundName: null, // 基金英文名稱
			fundCname: null, // 基金名稱
			localYn: 'Y', // 基金公司-{{ $t('pro.domestic') }}/{{ $t('pro.foreign') }}
			compCode: '', // 基金公司代碼
			curCode: '', // 計價幣別代碼
			investmentTypeCode: '', // {{ $t('pro.fundType') }}
			geoFocusCode: '', // {{ $t('pro.investmentRegion') }}
			globalClassCode: '', // 投資標的-Lipper全球分類
			localClassCode: '', // 投資標的-Lipper區域分類
			stdValueType: 'ALL', // 標準差
			betaValueType: 'ALL', // Beta
			corValueType: 'ALL', // 相關係數
			assetStatPctCode: 'PCT1YT0', // 依年度績效代碼
			annualPerfType: 'ALL', // 年度績效-績效
			perfRangeType: 'ALL', // 累積績效-期間代碼
			perfType: 'ALL', // 累積績效-績效
			lipperPointer: 'ALL', // 排名-指標代碼
			rank: 'ALL', // 排名
			sharpeValueType: 'ALL', // Sharpe值
			investRange: 'ALL', // 單筆投資(10,000)回報金額(1年)
			dcaRange: 'ALL', // 定期定額(1000)回報金額(1年)
			cmpCodeMenu: [], // 基金公司
			curMenu: [], // 全資料庫基金 幣別選擇
			curCodeData: [], // 全資料庫基金 幣別選擇
			investmentTypeMenu: [], // {{ $t('pro.fundType') }}選單
			geoFocusMenu: [], // {{ $t('pro.investmentRegion') }}選單
			globalClassMenu: [], // 投資標的-Lipper全球分類選單
			localClassMenu: [], // 投資標的-Lipper區域分類選單
			advanceType: null, // 進接搜尋類別

			// 加權條件篩選 儲存條件
			searchName: null,
			memo: null,
			isOpenModal2: false, // 歷史{{ $t('pro.search') }}條件model
			isOpenModal3: false, // 儲存條件model

			// {{ $t('pro.search') }}條件
			rateCurType: 'O', // (報酬率) 報酬率幣別顯示
			proCodes: [], // 商品代碼
			checkboxs: [], // 加入比較選項
			lipperIds: [], // 績效比較商品代碼
			pageData: {
				content: []
			},
			rankPageData: [],
			pageable: {
				page: 0,
				size: 20,
				sort: ['PRO_CODE'],
				direction: 'ASC'
			},
			isOpenCmpTypeModal: false,
			isOpenCompareModal: false,
			isOpenGeoFocusModal: false
		};
	},
	computed: {
		yearList() {
			const item = [];
			const nowYear = new Date().getFullYear();
			item.push({ value: 'PCT1YT0', name: nowYear - 1 });
			item.push({ value: 'PCT1YT1', name: nowYear - 2 });
			item.push({ value: 'PCT1YT2', name: nowYear - 3 });
			item.push({ value: 'PCT1YT3', name: nowYear - 4 });
			item.push({ value: 'PCT1YT4', name: nowYear - 5 });
			return item;
		}
	},
	watch: {
		getImgURL,
		selectedItems: {
			handler(newValues) {
				this.lipperIds = Object.keys(newValues).filter(lipperId => newValues[lipperId]);
			},
			deep: true
		},
		activeTab(newVal, oldVal) {
			const self = this;
			self.fastCode = '03';
			self.fastChange(self.fastCode);
			self.curObjs = [];
			$(this.$refs.curMenuFund).selectpicker('deselectAll');
			$(this.$refs.fundCurMenu).selectpicker('deselectAll');
		},
		selectedTab(newTab, oldTab) {
			const self = this;
			self.lipperIds = [];
			self.selectedItems = {};
		},
		curObjs(newVal, oldVal) {
			const self = this;
			if (newVal[0] !== oldVal[0]) {
				if (newVal[0] === '') {
					$(this.$refs.curMenuFund).selectpicker('selectAll');
					$(this.$refs.fundCurMenu).selectpicker('selectAll');
				}
				else if (oldVal[0] === '' && newVal[0] !== '') {
					$(this.$refs.curMenuFund).selectpicker('deselectAll');
					$(this.$refs.fundCurMenu).selectpicker('deselectAll');
				}
			}
		}
	},
	mounted: async function () {
		const self = this;
		$(this.$refs.curMenuFund).selectpicker('refresh');
		$(this.$refs.fundCurMenu).selectpicker('refresh');
		self.getLocalMenu();
		self.getProTypeMenu(); // 取得基金類型選項
		self.getIntFreqUnitTypeMenu(); // 取得配息頻率選項
		self.getIntRateRankMenu(); // 取得配息率選項
		self.getBackEndLoadMenu(); // 取得申購手續費選項
		self.getFundFastMenu(); // 取得快速{{ $t('pro.filterConditions') }}選項
		self.getTimeRangeMenu(); // 取得{{ $t('pro.displayInterval') }}選項
		self.getRowNumerMenu(); // 取得{{ $t('pro.displayDataCount') }}選項
		self.getPerfMenu(); // 取得{{ $t('pro.targetPerformance') }}選項
		self.getProWeightedTypeMenu(); // 取得風險/績效選單選項
		self.getCurMenu(); // 取得進階搜尋預設計價幣別資料
		self.getInvestmentTypeMenu(); // 取得{{ $t('pro.fundType') }}選單
		self.getGeoFocusMenu(); // {{ $t('pro.investmentRegion') }}選單
		self.getGlobalClassMenu(); // 取得投資標的-Lipper全球分類選單
		self.getLocalClassMenu(); // 取得投資標的-Lipper區域分類
		self.getCmpCodeMenu('Y'); // 基金公司選單選項
		self.getStatCodeMenu(); // 報酬率選項
		self.fastChange(self.fastCode);
	},
	methods: {
		// 條件Tab切換
		changeTab: function (tabName) {
			const self = this;
			self.activeTab = tabName;
			self.fundCmpChange();
			self.pageData = {
				// 清空{{ $t('pro.search') }}資料
				content: []
			};
			self.rankPageData = [];
			self.lipperIds = [];
			self.selectedItems = {};
		},
		// 取得境內外基金選項
		getLocalMenu: async function () {
			console.log('取得境內外基金選項');
			const self = this;
			self.localMenu = [{ codeValue: '', codeName: '不限' }];
			let localYnList = [];
			const res = await this.$api.getAdmCodeDetail({ codeType: 'LOCAL_YN' });
			localYnList = res.data;
			self.localMenu.push(...localYnList);
			console.log('境內外基金選項', self.localMenu);
			// Array.prototype.push.apply(self.localMenu, localYnList);
		},
		// 取得基金類型選項
		getProTypeMenu: async function () {
			const self = this;
			const res = await this.$api.getProTypeListApi({ pfcatCode: 'FUND' });
			self.proTypeMenu = res.data;
		},
		// 取得配息頻率選項
		getIntFreqUnitTypeMenu: async function () {
			const self = this;
			const res = await this.$api.getAdmCodeDetail({ codeType: 'INT_FREQ_UNITTYPE' });
			self.intFreqUnitMenu = res.data;
		},
		// 取得配息率選項
		getIntRateRankMenu: async function () {
			const self = this;
			self.intRateRankMenu = [{ codeValue: '', codeName: '不限' }];
			let intRateRankList = [];
			const res = await this.$api.getAdmCodeDetail({ codeType: 'INT_RATE_RANK' });
			intRateRankList = res.data;
			Array.prototype.push.apply(self.intRateRankMenu, intRateRankList);
		},
		getBackEndLoadMenu: async function () {
			const self = this;
			const res = this.$api.getAdmCodeDetail({ codeType: 'BACK_END_LOAD_YN' });
			self.backEndLoadMenu = res.data;
		},
		// 報酬率選項
		getStatCodeMenu: async function () {
			const self = this;
			const res = this.$api.getAdmCodeDetail({ codeType: 'STAT_CODE' });
			self.statCodeMenu = res.data;
		},
		// 取得快速{{ $t('pro.filterConditions') }}選項
		getFundFastMenu: async function () {
			const self = this;
			const res = await this.$api.getFundFastMenuApi();
			self.fundFastMenu = res.data;
		},
		// 取得{{ $t('pro.displayInterval') }}
		getTimeRangeMenu: async function () {
			const self = this;
			const res = await this.$api.getTimeRangeMenuApi();
			self.timeRangeMenu = res.data;
		},
		// 取得{{ $t('pro.displayDataCount') }}
		getRowNumerMenu: async function () {
			const self = this;
			const res = await this.$api.getRowNumerMenuApi();
			self.rowNumerMenu = res.data;
		},
		// 績效排行
		getPerfMenu: async function () {
			const self = this;
			const res = await this.$api.getPerfMenuApi();
			self.perfMenu = res.data;
		},
		// 進階搜尋 {{ $t('pro.fundType') }}選擇全資料庫基金=>計價幣別選單
		getCurMenu: async function () {
			const self = this;
			const res = await this.$api.getCurMenuApi();
			res.data.forEach(function (item) {
				const obj = { value: item.curCode, name: item.curName + ' ' + item.curCode };
				self.curCodeData.push(obj);
			});
		},
		// 進階搜尋 {{ $t('pro.fundType') }}
		getfundTypeMenu(fundSaleType) {
			const self = this;
			self.curMenu = [];
			if (fundSaleType === 'LOCAL') {
				// 本行基金
				self.curMenu = self.curOption;
			}
			else {
				// 全資料庫基金
				self.curMenu = self.curCodeData;
			}
			self.curObjs = [];
			$(this.$refs.fundCurMenu).selectpicker('deselectAll'); // 重整選單
		},
		getInvestmentTypeMenu: async function () {
			// {{ $t('pro.fundType') }}下拉
			const self = this;
			const res = await this.$api.getInvestmentTypeMenuApi({ local: 'zh_TW' });
			self.investmentTypeMenu = res.data;
		},
		getGeoFocusMenu: async function () {
			// {{ $t('pro.investmentRegion') }}下拉
			const self = this;
			const res = await this.$api.getGeoFocusMenuApi({ local: 'zh_TW' });
			self.geoFocusMenu = res.data;
		},
		getGlobalClassMenu: async function () {
			const self = this;
			let globalClassList = [];
			let globalClassOtherList = [];
			const res = await this.$api.getGlobalClassCodeMenuApi();
			globalClassList = res.data;
			const resOther = await this.$api.getGlobalClassCodeOtherMenuApi();
			globalClassOtherList = resOther.data;
			self.globalClassMenu = [...globalClassList, ...globalClassOtherList];
			self.globalClassMenu.sort((a, b) => a.globalClassCode.localeCompare(b.globalClassCode));
		},
		getLocalClassMenu: async function () {
			const self = this;
			const res = await this.$api.getLocalClassMenuApi();
			self.localClassMenu = res.data;
		},
		// 進階搜尋 基金公司
		getCmpCodeMenu: async function (cmpCode) {
			const self = this;
			const url = '';
			let res = null;
			if (cmpCode === 'Y') {
				res = await this.$api.getLocalFundCompanies();
			}
			else {
				res = await this.$api.getForeignFundCompanies();
			}

			self.cmpCodeMenu = res.data;
			self.compCode = '';
		},
		// 快速{{ $t('pro.search') }}切換
		fastChange(fastCode) {
			const self = this;
			self.timeRange = {}; // 快速 {{ $t('pro.displayInterval') }}
			self.rowNumber = ''; // 快速 {{ $t('pro.displayDataCount') }}
			self.perf = '';
			self.pageData = {
				// 清空{{ $t('pro.search') }}資料
				content: []
			};
			self.pageData = {
				// 清空{{ $t('pro.search') }}資料
				content: []
			};
			// 超人氣
			if (fastCode === '05') {
				$('#proPerfTimeTrFund').hide();
				$('#fundCmpTrFund').hide();
				$('#rangeFixedTrFund').show();
				$('#maxRowIdTrFund').show();
				self.timeRange = self.timeRangeMenu[0];
				// 績效排行
			}
			else if (fastCode === '07') {
				self.perf = 'PCTYTD';
				$('#rangeFixedTrFund').hide();
				$('#fundCmpTrFund').hide();
				$('#maxRowIdTrFund').show();
				$('#proPerfTimeTrFund').show();
			}
			else if (fastCode === '08') {
				// 最高配息率商品
				$('#maxRowIdTrFund').show();
				$('#rangeFixedTrFund').hide();
				$('#proPerfTimeTrFund').hide();
				$('#fundCmpTrFund').hide();
				// 4433篩選
			}
			else if (fastCode === '10') {
				$('#fundCmpTrFund').show();
				$('#maxRowIdTrFund').hide();
				$('#rangeFixedTrFund').hide();
				$('#proPerfTimeTrFund').hide();
				self.rowNumber = '10'; // 快速 {{ $t('pro.displayDataCount') }}
			}
			else {
				$('#maxRowIdTrFund').hide();
				$('#rangeFixedTrFund').hide();
				$('#proPerfTimeTrFund').hide();
				$('#fundCmpTrFund').hide();
				self.rowNumber = '10'; // 快速 {{ $t('pro.displayDataCount') }}
			}
		},
		// 取得商品資料{{ $t('pro.search') }}(信託-基金)-風險/績效選單
		getProWeightedTypeMenu: async function () {
			const self = this;
			const res = await this.$api.getProWeightedTypeMenuApi();
			self.proWeightedTypeMenu = res.data;
			console.log('getProWeight Success!', self.proWeightedTypeMenu);
		},
		// 由{{ $t('pro.searchResults') }}標題觸發
		sort: function (columnName) {
			const self = this;
			if (this.pageable.sort !== columnName) {
				this.pageable.sort = columnName;
				this.pageable.direction = 'DESC';
			}
			else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}

			if (self.activeTab === 'common') {
				this.gotoPage(0);
			}
			else if (self.activeTab === 'fast') {
				this.gotoFastPage(0);
			}
			else if (self.activeTab === 'advanced') {
				this.getAdvancePage(0, self.advanceType);
			}
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			const self = this;

			let url = '';

			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			// 取德compCodeList
			const compCodeList = self.fundCmpItem.map(function (item) {
				return item.compCode;
			});

			const geoFocusCodeList = self.geoFocusItem.map(function (item) {
				return item.geoFocusCode;
			});

			const payload = {
				bankProCode: self.bankProCode,
				proName: self.proName,
				curCodes: self.curObjs,
				riskCodes: self.riskCodes,
				protypeCode: self.proTypeCode,
				localYn: self.genLocalYn,
				intFreqUnitType: self.intFreqUnitType,
				intRateRank: self.intRateRank,
				backEndLoadYn: self.backEndLoadYn,
				isinCode: self.isinCode,
				lipperPointer: self.lipperPoint,
				lipperRank: self.lipperRank,
				compCode: compCodeList,
				geoFocusCodes: geoFocusCodeList,
				statCode: self.statCode,
				minDValue: self.minDValue,
				maxDValue: self.maxDValue
			};
			const res = await this.$api.getFundProducts(payload, url);
			self.pageData = res.data;
			self.pageData.content.forEach((data) => {
				self.checkboxs.push(false);
			});
		},
		// 快速篩選
		gotoFastPage: function (page) {
			this.pageable.page = page;
			this.getFastPageData(page);
		},
		getFastPageData: async function (page) {
			const self = this;

			//			var rangeType = '';
			//			var rangeFixed = '';
			//			if (self.fastCode === '05') {
			//				// 超人氣
			//				var timeRangeObjs = _.filter(self.timeRangeMenu, {
			//					rangeType: self.timeRange
			//				});
			//				rangeType = timeRangeObjs[0].rangeType;
			//				rangeFixed = timeRangeObjs[0].rangeFixed;
			/// /				self.rowNumber = '10'; // 快速 {{ $t('pro.displayDataCount') }}
			//			}

			const lipperClassCode = [];
			self.fundCmpItem.forEach((data) => {
				lipperClassCode.push(data.compCode);
			});

			let url = '';
			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;

			const payload = {
				filterCodeValue: self.fastCode,
				timeRangeType: self.timeRange.rangeType, // {{ $t('pro.displayInterval') }}類型
				timeRangeFixed: self.timeRange.rangeFixed, // {{ $t('pro.displayInterval') }}數值
				rowNumberFixed: self.rowNumber,
				perfTimeCode: self.perf, // {{ $t('pro.targetPerformance') }}
				lipperClassCodes: lipperClassCode // 基金公司
			};
			const res = await this.$api.getFastPageDataApi(payload, url);
			self.pageData = res.data;
			self.pageData.content.forEach((data) => {
				self.checkboxs.push(false);
			});
		},
		// 加權條件篩選
		getRankPage: function (page) {
			const self = this;
			let errorMsg = '';
			self.pageable.page = page;
			if (_.isEmpty(self.curCode)) {
				errorMsg = this.$t('pro.pleaseSelectPerformanceCurrency') + ' <br>';
			}
			if (self.proSearchesMaps.length == 0) {
				errorMsg += '請至少加入一筆比較權重條件設定 <br>';
			}
			else {
				let sum = 0;
				self.proSearchesMaps.forEach((data) => {
					sum += parseInt(data.searchValue);
				});
				if (sum != 100) {
					errorMsg = '權重加總需為100% <br>';
				}
				else if (sum > 100) {
					errorMsg = '權重加總[' + sum + ']已超過100% <br>';
				}
			}
			if (errorMsg != '') {
				self.popUpMeassage(errorMsg);
			}
			else {
				self.getRankPageData();
				self.showButton = true;
			}
		},
		popUpMeassage: function (msg) {
			this.$swal.fire({
				icon: 'warning',
				html: msg,
				showCloseButton: true,
				confirmButtonText: this.$t('pro.confirm'),
				buttonsStyling: false, // remove default button style
				customClass: {
					confirmButton: 'btn btn-danger'
				}
			});
		},
		// 儲存條件
		saveCondition() {
			const self = this;
			let errorMsg = '';
			if (self.proSearchesMaps.length == 0) {
				errorMsg += '請至少加入一筆比較權重條件設定 <br>';
			}
			else {
				let sum = 0;
				self.proSearchesMaps.forEach((data) => {
					sum += parseInt(data.searchValue);
				});
				if (sum != 100) {
					errorMsg = '權重加總需為100% <br>';
				}
			}
			if (errorMsg != '') {
				self.popUpMeassage(errorMsg);
			}
			else {
				self.isOpenModal3 = true;
			}
		},
		// 加權條件篩選
		getRankPageData: async function () {
			const self = this;
			if (self.rowNumber == '') {
				self.rowNumber = '10'; // {{ $t('pro.displayDataCount') }}
			}
			if (self.curCode === '') {
				self.curCode = 'TWD';
			}
			const lipperClassCode = [];
			self.fundCmpItem.forEach((data) => {
				lipperClassCode.push(data.compCode);
			});
			const payload = {
				lipperClassCodes: lipperClassCode, // 基金公司
				perfCurCode: self.curCode, // {{ $t('pro.performanceCurrency') }}
				rowNumberFixed: self.rowNumber, // {{ $t('pro.displayDataCount') }}
				proSearchesMaps: self.proSearchesMaps // 條件權重
			};
			const res = await this.$api.getRankPageDataApi(payload);
			self.rankPageData = res.data;
		},
		getRankValue(index, searchCodeName) {
			const self = this;
			return self.rankPageData[index][searchCodeName + '_VALUE'];
		},
		// 開啟歷史{{ $t('pro.search') }}條件
		openHisSearch: async function () {
			const self = this;
			const res = await this.$api.getProSearchesApi();
			self.proSearches = res.data;
			self.isOpenModal2 = true;
		},
		// 歷史{{ $t('pro.search') }}條件 編輯
		hisEdit: async function (item) {
			const self = this;
			const ret = await this.$api.getProSearchesMap({ searchSeq: item.searchSeq });

			self.proSearchesMaps = [];
			ret.data.forEach((data) => {
				const map = {};
				map.searchCodeName = data.searchCode; // 風險/績效 value
				map.searchName = data.searchCodeName; // 風險/績效 名稱
				map.searchValue = data.searchValue; // 權重(%)
				self.proSearchesMaps.push(map);
			});
			self.isOpenModal2 = false;
		},
		// 歷史{{ $t('pro.search') }}條件 刪除
		hisDelete(item) {
			const self = this;
			this.$bi.confirm(this.$t('pro.confirmDelete') + '?', {
				event: {
					confirmOk: async function () {
						const res = await self.$api.deleteHistorySearchApi({ searchSeq: item.searchSeq });
						self.openHisSearch();
						self.isOpenModal2 = false;
					}
				}
			});
		},
		// 風險/績效 與 權重(%) 增加
		newRank() {
			const self = this;
			let exist = null;
			const map = {};
			if (self.searchCodeName == '') {
				this.$bi.alert(this.$t('pro.pleaseSelectRiskPerformance'));
				return;
			}
			// 風險/績效
			const weight = self.proWeightedTypeMenu.filter(function (item) {
				console.log('weight filter:', item.codeValue, self.searchCodeName);
				return item.codeValue === self.searchCodeName;
			});
			console.log('Weight', weight);
			map.searchValue = self.searchValue; // 權重%
			map.searchName = weight[0].codeName; // 風險/績效名稱
			map.searchCodeName = self.searchCodeName; // 風險/績效value
			self.proSearchesMaps.forEach((data) => {
				if (data.searchCodeName === self.searchCodeName) {
					exist = true;
				}
			});
			if (self.searchValue == '') {
				this.$bi.alert(this.$t('pro.pleaseEnterWeight'));
			}
			else if (parseInt(self.searchValue) > 100) {
				this.$bi.alert(this.$t('pro.weightTotalExceeds100') + '[' + self.searchValue + ']');
				self.searchValue = null;
			}
			else if (parseInt(self.searchValue) < 0 || !_.isNumeric(self.searchValue)) {
				// 檢查數值要大於0
				this.$bi.alert(this.$t('pro.weightMustBeGreaterThanZero'));
				self.searchValue = null;
			}
			else if (exist) {
				this.$bi.alert(this.$t('pro.riskPerformanceAlreadyExists') + '[' + self.searchCodeName + ']');
				self.searchValue = null;
			}
			else {
				self.proSearchesMaps.push(map);
				self.searchValue = null;
			}
		},
		// 加權條件篩選 儲存條件
		conditionsSave() {
			var self = this;
			const payload = {
				// searchSeq: '123456', // {{ $t('pro.search') }}條件序號
				searchName: self.searchName, // {{ $t('pro.search') }}條件名稱
				memo: self.memo, // 備註
				proSearchesMaps: self.proSearchesMaps // 條件權重
			};
			const res = this.$api.getProSearchesApi(payload);
			this.$bi.alert(this.$t('pro.saveSuccess'));
			var self = this;
			self.proSearchesMaps = [];
			self.searchName = null;
			self.memo = null;
			self.rowNumber = null;
			self.curCode = null;
		},
		// 風險/績效 與 權重(%) 刪除
		deleteWeighted(searchCodeName) {
			const self = this;
			this.$bi.confirm(this.$t('pro.confirmDelete') + '?', {
				event: {
					confirmOk: function () {
						self.proSearchesMaps = self.proSearchesMaps.filter((item) => {
							if (item.searchCodeName === searchCodeName) {
								return false; // 移除刪除項目
							}
							return true; // 保留
						});
						// _.remove(self.proSearchesMaps, (item) => item.searchCodeName === searchCodeName); // 移除刪除項目
					}
				}
			});
		},
		// 進階搜尋
		getAdvancePage: function (page, type) {
			const self = this;
			this.pageable.page = page;

			if (_.isEmpty(type)) {
				type = self.advanceType;
			}
			else {
				self.advanceType = type;
			}
			this.getAdvancePageData(page, type);
		},
		// 進階搜尋
		getAdvancePageData: async function (page, type) {
			const self = this;
			let url = '';
			var page = _.isNumber(page) ? page : self.pageable.page;
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;
			let param = {};
			// 基金資料
			if (type === 'info') {
				const curCodes = [];
				self.curObjs.forEach(function (item) {
					curCodes.push(item);
				});
				param = {
					fundSaleType: self.fundSaleType, // {{ $t('pro.fundType') }}
					filterCodeValue: self.fastCode,
					timeRangeType: self.timeRange, // {{ $t('pro.displayInterval') }}類型
					timeRangeFixed: self.rowNumber, // {{ $t('pro.displayInterval') }}數值
					perfTimeCode: self.perf, // {{ $t('pro.targetPerformance') }}
					localYn: self.localYn, // 基金公司-{{ $t('pro.domestic') }}/{{ $t('pro.foreign') }}
					fundCname: self.fundCname, // 基金名稱
					fundName: self.fundName, // 基金英文名稱
					bankProCodes: self.bankProCodes, // 本行基金代碼
					compCode: self.compCode, // 基金公司代碼
					curCodes: curCodes, // 計價幣別代碼
					investmentTypeCode: self.investmentTypeCode, // {{ $t('pro.fundType') }}
					globalClassCode: self.globalClassCode, // 投資標的-Lipper全球分類
					geoFocusCode: self.geoFocusCode, // {{ $t('pro.investmentRegion') }}
					localClassCode: self.localClassCode // 投資標的-Lipper區域分類
				};
				// 基金風險
			}
			else if (type === 'basic') {
				param = {
					fundSaleType: self.fundSaleType, // {{ $t('pro.fundType') }}
					stdValueType: self.stdValueType, // 標準差
					betaValueType: self.betaValueType, // Beta
					corValueType: self.corValueType // 相關係數
				};
				// 基金績效
			}
			else if (type === 'performance') {
				param = {
					fundSaleType: self.fundSaleType, // {{ $t('pro.fundType') }}
					assetStatPctCode: self.assetStatPctCode, // 依年度績效代碼
					annualPerfType: self.annualPerfType, // 年度績效-績效
					perfRangeType: self.perfRangeType, // 累積績效-期間代碼
					perfType: self.perfType, // 累積績效-績效
					lipperPointer: self.lipperPointer, // 排名-指標代碼
					rank: self.rank, // 排名
					sharpeValueType: self.sharpeValueType, // Sharpe值
					investRange: self.investRange, // 單筆投資(10,000)回報金額(1年)
					dcaRange: self.dcaRange // 定期定額(1000)回報金額(1年)
				};
			}
			const res = await this.$api.getAdvancePageDataApi(param, url);
			self.pageData = res.data;
			self.pageData.content.forEach((data) => {
				self.checkboxs.push(false);
			});
		},

		// 一般篩選 基金公司change
		fundCmpChange() {
			const self = this;
			self.fundCmpItem = [];
			self.$refs.groupFundCmpTypeModalRef.fundcmpPropItem([]);
		},
		// 顯示{{ $t('pro.selectFundCompany') }} model (快速篩選4433篩選、加權條件篩選)
		groupFundCmpTypeModalHandler: function () {
			const self = this;
			self.$refs.groupFundCmpTypeModalRef.groupfundcmpTypeMenu();
			self.$refs.groupFundCmpTypeModalRef.fundcmpPropItem(self.fundCmpItem);
			this.isOpenCmpTypeModal = true;
		},
		// 一般篩選  顯示{{ $t('pro.selectFundCompany') }} model //不限 {{ $t('pro.domestic') }}或{{ $t('pro.foreign') }}
		groupFundCmpModalHandler: function (nation) {
			const self = this;
			self.$refs.groupFundCmpTypeModalRef.groupfundcmpsMenu(nation);
			self.$refs.groupFundCmpTypeModalRef.groupfundcmpsOtherMenu(nation);
			self.$refs.groupFundCmpTypeModalRef.fundcmpPropItem(self.fundCmpItem);
			this.isOpenCmpTypeModal = true;
		},
		// 顯示基金公司選擇項目
		selectedFundCmp(fundCmpItem) {
			const self = this;
			self.isOpenCmpTypeModal = false;
			self.fundCmpItem = fundCmpItem; // 取得基金公司資料
		},
		// 刪除基金公司項目
		deleteFundCmpItem(compCode) {
			const self = this;
			_.remove(self.fundCmpItem, item => item.compCode === compCode); // 移除刪除項目
		},
		// 一般篩選  顯示{{ $t('pro.selectInvestmentRegion') }} model
		groupGeoFocusModalHandler: function () {
			const self = this;
			this.$refs.groupGeoFocusModalRef.geoFocusPropItem(self.geoFocusItem);
			this.isOpenGeoFocusModal = true;
		},
		// 顯示{{ $t('pro.investmentRegion') }}選擇項目
		selectedGeoFocus(geoFocusItem) {
			const self = this;
			self.isOpenGeoFocusModal = false;
			self.geoFocusItem = geoFocusItem; // 取得基金公司資料
		},
		// 刪除{{ $t('pro.investmentRegion') }}項目
		deleteGeoFocusItem(geoFocusCode) {
			const self = this;
			_.remove(self.geoFocusItem, item => item.geoFocusCode === geoFocusCode); // 移除刪除項目
		},
		// 執行{{ $t('pro.performanceComparisonChart') }}
		performancesCompareModelHandler: function () {
			const self = this;
			if (self.lipperIds.length > 0) {
				if (self.lipperIds.length > 6) {
					this.$bi.alert(this.$t('pro.maxSixItems'));
				}
				else {
					this.$refs.performancesCompareModalRef.comparefundPropItem(self.lipperIds, self.proCodes, 'fund');
					this.isOpenCompareModal = true;
				}
			}
			else {
				this.$bi.alert(this.$t('pro.pleaseSelectAtLeastOneProduct'));
			}
		},
		// 刪除我的最愛
		remove: async function (proCode) {
			const self = this;
			const res = await this.$api.deleteFavoriteApi({ proCode: proCode });
			this.$bi.alert(this.$t('pro.deleteSuccess'));
			self.checkboxs = [];
			self.proCodes = [];
			self.gotoFastPage(0);
		},
		setSelectedTab(tabName) {
			const self = this;
			if (self.selectedTab !== tabName) {
				self.selectedTab = tabName;
			}
		}
	} // methods end
};
</script>
