<template>
	<div :id="chartId" />
</template>
<script>
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { toRaw } from 'vue';
export default {
	props: {
		chartId: String,
		propChartData: Array,
		fundName: String
	},
	data: function () {
		return {
			am5Obj: {}
			// propChartData: [
			// 	{ acr: 10, astd: 5, x: 5, y: 10, value: 100, fundName: '基金1' },
			// 	{ acr: 15, astd: 7, x: 7, y: 15, value: 200, fundName: '基金2' },
			// 	{ acr: 12, astd: 6, x: 6, y: 12, value: 150, fundName: '基金3' }
			// ] // 假資料
		};
	},
	computed: {},
	watch: {},
	mounted: function () {
		this.initChart(); // 初始化圖表
	},
	beforeUnmount: function () {
		this.destroyChart();
	},
	methods: {
		initChart: function () {
			const self = this;

			const { chartData, am5Obj } = self;
			let firstLoad = false;
			// 透過 toRaw 解決因為 vue data proxy 導致跟 amcharts 衝突
			let { xAxis, yAxis, root, chart, legend } = toRaw(am5Obj);
			if (!root) {
				firstLoad = true;
				// Create root element
				// https://www.amcharts.com/docs/v5/getting-started/#Root_element
				root = am5.Root.new(self.chartId);
				root._logo.dispose();

				// Set themes
				// https://www.amcharts.com/docs/v5/concepts/themes/
				root.setThemes([am5themes_Animated.new(root)]);

				// Create chart
				// https://www.amcharts.com/docs/v5/charts/xy-chart/
				chart = root.container.children.push(
					am5xy.XYChart.new(root, {
						panX: true,
						panY: true,
						wheelY: 'zoomXY',
						pinchZoomX: true,
						pinchZoomY: true,
						layout: root.verticalLayout
					})
				);

				chart.get('colors').set('step', 2);

				// Add legend
				// https://www.amcharts.com/docs/v5/charts/xy-chart/legend-xy-series/
				legend = chart.children.push(
					am5.Legend.new(root, {
						centerX: am5.p50,
						x: am5.p50
					})
				);

				// Create axes
				// https://www.amcharts.com/docs/v5/charts/xy-chart/axes/
				xAxis = chart.xAxes.push(
					am5xy.ValueAxis.new(root, {
						renderer: am5xy.AxisRendererX.new(root, { minGridDistance: 50 }),
						tooltip: am5.Tooltip.new(root, {})
					})
				);

				xAxis.children.moveValue(
					am5.Label.new(root, {
						text: self.$t('pro.annualizedStandardDeviation'),
						x: am5.p50,
						centerX: am5.p50
					}),
					xAxis.children.length - 1
				);

				yAxis = chart.yAxes.push(
					am5xy.ValueAxis.new(root, {
						renderer: am5xy.AxisRendererY.new(root, {}),
						tooltip: am5.Tooltip.new(root, {})
					})
				);

				yAxis.children.moveValue(
					am5.Label.new(root, {
						rotation: -90,
						text: this.$t('pro.annualizedCumulativeReturn'),
						y: am5.p50,
						centerX: am5.p50
					}),
					0
				);
			}
			else {
				chart.series.clear();
				// x軸小方塊 清掉重置
				legend.data.clear();
			}

			// Create series
			// https://www.amcharts.com/docs/v5/charts/xy-chart/series/
			const series0 = chart.series.push(
				am5xy.LineSeries.new(root, {
					calculateAggregates: true,
					xAxis: xAxis,
					yAxis: yAxis,
					valueYField: 'acr',
					valueXField: 'astd',
					valueField: 'value'
				})
			);

			// Add bullet
			// https://www.amcharts.com/docs/v5/charts/xy-chart/series/#Bullets
			const circleTemplate = am5.Template.new({});
			series0.bullets.push(function () {
				const graphics = am5.Circle.new(
					root,
					{
						fill: series0.get('fill'),
						tooltipText: `{fundName}: \n[bold] ${this.$t('pro.annualizedStandardDeviation')}: {valueX}%\n ${this.$t('pro.annualizedCumulativeReturn')}: {valueY}%`
					},
					circleTemplate
				);
				return am5.Bullet.new(root, {
					sprite: graphics
				});
			});

			// Add heat rule
			// https://www.amcharts.com/docs/v5/concepts/settings/heat-rules/
			// series0.set("heatRules", [
			//   {
			//     target: circleTemplate,
			//     min: 3,
			//     max: 35,
			//     dataField: "value",
			//     key: "radius",
			//   },
			// ]);

			// Create second series
			// https://www.amcharts.com/docs/v5/charts/xy-chart/series/
			const series1 = chart.series.push(
				am5xy.LineSeries.new(root, {
					name: self.fundName,
					calculateAggregates: true,
					xAxis: xAxis,
					yAxis: yAxis,
					valueYField: 'y',
					valueXField: 'x',
					valueField: 'value'
				})
			);

			// Add bullet
			// https://www.amcharts.com/docs/v5/charts/xy-chart/series/#Bullets
			series1.bullets.push(function () {
				const graphics = am5.Circle.new(
					root,
					{
						fill: '#ec0800',
						tooltipText: `{fundName}: \n[bold] ${this.$t('pro.annualizedStandardDeviation')}: {valueX}%\n ${this.$t('pro.annualizedCumulativeReturn')}: {valueY}%`
					},
					circleTemplate
				);
				return am5.Bullet.new(root, {
					sprite: graphics
				});
			});

			// Add heat rule
			// https://www.amcharts.com/docs/v5/concepts/settings/heat-rules/
			// series1.set("heatRules", [
			//   {
			//     target: starTemplate,
			//     min: 3,
			//     max: 50,
			//     dataField: "value",
			//     key: "radius",
			//   },
			// ]);

			series0.strokes.template.set('strokeOpacity', 0);
			series1.strokes.template.set('strokeOpacity', 0);

			if (firstLoad) {
				// Add cursor
				// https://www.amcharts.com/docs/v5/charts/xy-chart/cursor/
				chart.set(
					'cursor',
					am5xy.XYCursor.new(root, {
						xAxis: xAxis,
						yAxis: yAxis,
						snapToSeries: [series0, series1]
					})
				);

				// Add scrollbars
				// https://www.amcharts.com/docs/v5/charts/xy-chart/scrollbars/
				chart.set(
					'scrollbarX',
					am5.Scrollbar.new(root, {
						orientation: 'horizontal'
					})
				);

				chart.set(
					'scrollbarY',
					am5.Scrollbar.new(root, {
						orientation: 'vertical'
					})
				);

				Object.assign(am5Obj, { xAxis, yAxis, root, chart, legend });
			}

			series0.data.setAll(self.propChartData);
			series1.data.setAll(self.propChartData);
			legend.data.push(series1);

			if (firstLoad) {
				// Make stuff animate on load
				// https://www.amcharts.com/docs/v5/concepts/animations/
				series0.appear(1000);
				series1.appear(1000);

				chart.appear(1000, 100);
			}
		},
		destroyChart: function () {
			const { am5Obj } = this;
			const { root } = Vue.toRaw(am5Obj);
			if (root) {
				root.dispose();
			}
		}
	}
};
</script>
