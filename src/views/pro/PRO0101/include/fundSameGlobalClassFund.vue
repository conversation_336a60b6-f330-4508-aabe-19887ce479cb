<template>
	<span v-for="fund in fundList">{{ fund.fundName }}<br></span>
</template>
<script>
export default {
	props: {
		fundCode: String,
		globalClassCode: String
	},
	data: function () {
		return {
			fundList: []
		};
	},
	computed: {},
	watch: {
		fundCode: {
			handler: function (newVal, oldVal) {
				this.getFundList();
			}
		}
	},
	created: function () {},
	mounted: function () {
		const self = this;
		self.getFundList();
	},
	methods: {
		getFundList: async function () {
			const self = this;
			const ret = await this.$api.getGlobalFundListApi({
				fundCode: self.fundCode,
				globalClassCode: self.globalClassCode
			});
			self.fundList = ret.data;
		}
	}
};
</script>
