<template>
	<!-- Modal 9 Self-operated Securities Products-->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('pro.selfOperatedSecuritiesProducts') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click.prevent="close()"
				/>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-structure" />
							<h4>
								<span>{{ $t('pro.productName') }}</span> <br>{{ $filters.defaultValue(proInfo.proName, '--') }} <br><span class="tx-black">{{
									$filters.defaultValue(proInfo.proEName, '--')
								}}</span>
							</h4>
						</div>
						<h4 class="pro_value">
							<span>{{ $t('pro.referenceRedemptionPurchasePrice') }}</span>
							<br><span>{{ $filters.defaultValue(proInfo.sprice, '--') }}</span> <br><span>{{
								$filters.defaultValue(proInfo.bprice, '--')
							}}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productCode') }}</span> <br>{{ $filters.defaultValue(proInfo.bankProCode, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6>
								<span>{{ $t('pro.assetCategory') }} <br></span>{{ $filters.defaultValue(proInfo.assetcatName, '--') }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productMainCategory') }}</span> <br>{{ $filters.defaultValue(proInfo.pfcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productSubCategory') }}</span><br>{{ $filters.defaultValue(proInfo.proTypeName, '--') }}</h6>
						</div>
					</div>
				</div>
				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item">
							<a class="nav-link active" href="#Sectionovers01" data-bs-toggle="pill">{{ $t('pro.productBasicData') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#Sectionovers02" data-bs-toggle="pill">{{ $t('pro.productCommonData') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#Sectionovers03" data-bs-toggle="pill">{{ $t('pro.productAdditionalData') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link datainfo" href="#Sectionovers04" data-bs-toggle="pill">{{ $t('pro.priceAnalysis') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link datainfo" href="#Sectionovers05" data-bs-toggle="pill">{{ $t('pro.performance') }}</a>
						</li>
					</ul>

					<div class="tab-content">
						<div id="Sectionovers01" class="tab-pane show active">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.productInformation') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.riskLevel') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.secInfo.riskName, '--') }}
											</td>
											<th>{{ $t('pro.currency') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.secInfo.curCode, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.maturityDate') }}</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.expireDt, '--') }}</td>
											<th>{{ $t('pro.dividendType') }}</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.intFreqUnittypeName, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.couponRate') }}(%)</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.stockRate, '--') }}</td>
											<th>{{ $t('pro.isinCode') }}</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.isinCode, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.issuerName') }}/{{ $t('pro.issuerPersonName') }}</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.issuerName, '--') }}</td>
											<th>{{ $t('pro.productRatingMoodys') }}</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.mlongRatingLvl, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.productRatingSP') }}</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.slongRatingLvl, '--') }}</td>
											<th>{{ $t('pro.productRatingFitch') }}</th>
											<td>{{ $filters.defaultValue(proInfo.secInfo.flongRatingLvl, '--') }}</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.investmentAmountLimits') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.foreignCurrencyMinInvestment') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.secInfo.mininvFcAmt, '--') }}
											</td>
											<th>{{ $t('pro.foreignCurrencyMinAccumulation') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.secInfo.mininvFcAccAmt, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionovers02" class="tab-pane fade">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.salesRelatedData') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>{{ $t('pro.customerCategory') }}</span></th>
											<td class="wd-30p">
												<span>{{ $filters.defaultValue(proInfo.allYn, '--') }}</span>
											</td>
											<th><span>{{ $t('pro.salesTarget') }}</span></th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.targetCusBu, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.isOpenForPurchase') }}</th>
											<td>{{ $filters.defaultValue(proInfo.buyYn, '--') }}</td>
											<th>{{ $t('pro.isOpenForRedemption') }}</th>
											<td>{{ $filters.defaultValue(proInfo.sellYn, '--') }}</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.volatilityType') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.volatilityType, '--') }}</span>
											</td>
											<th><span>{{ $t('pro.dividendFrequency') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.intFreqUnitype, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.financialNeeds') }}</span></th>
											<td colspan="3">
												<div v-for="item in finReqCodeMenu" class="form-check form-check-inline">
													<input
														id="c1"
														v-model="finReqCodes"
														class="form-check-input"
														name="finReqCodes"
														disabled
														:value="item.codeValue"
														type="checkbox"
													>
													<label class="form-check-label" for="c1">{{ $filters.defaultValue(item.codeName, '--') }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.productTags') }}</th>
											<td disabled="disabled" colspan="3">
												{{ $filters.defaultValue(proInfo.selprocatNames, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionovers03" class="tab-pane fade">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.otherSettings') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>{{ $t('pro.remarks') }}</span></th>
											<td class="wd-80p">
												{{ $filters.defaultValue(proInfo.memo, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.relatedAttachments') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.productProspectus') }}</th>
											<td class="wd-80p">
												<a v-if="proFileA && proFileA.url" :href="proFileA.url" target="_blank">{{
													$filters.defaultValue(proFileA.url, '--')
												}}</a><br v-if="proFileA && proFileA.url">
												<a
													v-if="proFileA"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileA)"
												>{{
													$filters.defaultValue(proFileA.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.investorNotice') }}</th>
											<td class="wd-80p">
												<a v-if="proFileD && proFileD.url" :href="proFileD.url" target="_blank">{{
													$filters.defaultValue(proFileD.url, '--')
												}}</a><br v-if="proFileD && proFileD.url">
												<a
													v-if="proFileD"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileD)"
												>{{
													$filters.defaultValue(proFileD.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.dm') }}</th>
											<td class="wd-80p">
												<a v-if="proFileF && proFileF.url" :href="proFileF.url" target="_blank">{{
													$filters.defaultValue(proFileF.url, '--')
												}}</a><br v-if="proFileF && proFileF.url">
												<a
													v-if="proFileF"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileF)"
												>{{
													$filters.defaultValue(proFileF.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.others') }}</th>
											<td class="wd-80p">
												<a v-if="proFileG && proFileG.url" :href="proFileG.url" target="_blank">{{
													$filters.defaultValue(proFileG.url, '--')
												}}</a><br v-if="proFileG && proFileG.url">
												<a
													v-if="proFileG"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileG)"
												>{{
													$filters.defaultValue(proFileG.showName, '--')
												}}</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.otherRelatedAttachments') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<td>
												<span v-for="(item, index) in otherFileList">
													<a
														v-if="index === otherFileList.length - 1"
														v-show="item.show"
														href="#"
														class="tx-link"
														@click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}</a>
													<a
														v-else
														v-show="item.show"
														href="#"
														class="tx-link"
														@click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}, </a>
												</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="tx-note">
								{{ $t('pro.otherRelatedAttachmentsNote') }}
							</div>
						</div>

						<div id="Sectionovers04" class="tab-pane fade">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.priceAnalysis') }}</h4>
								</div>
								<table class="table table-RWD table-bordered text-end">
									<thead>
										<tr>
											<th class="wd-10p text-start">
												{{ $t('pro.item') }}
											</th>
											<th class="wd-30p">
												{{ $t('pro.price') }}
											</th>
											<th class="wd-30p">
												{{ $t('pro.highestPrice') }}({{ $t('pro.year') }})
											</th>
											<th class="wd-30p">
												{{ $t('pro.lowestPrice') }}({{ $t('pro.year') }})
											</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td class="text-start" :data-th="$t('pro.item')">
												{{ $t('pro.price') }}
											</td>
											<td class="text-end" :data-th="$t('pro.price')">
												<span>{{ $filters.defaultValue(secPriceAna.aprice, '--') }}({{
													$filters.defaultValue(secPriceAna.priceDt, '--')
												}})</span>
											</td>
											<td class="text-end" :data-th="$t('pro.highestPrice') + '(' + $t('pro.year') + ')'">
												<span>{{ $filters.defaultValue(secPriceAna.maxAprice, '--') }}({{
													$filters.defaultValue(secPriceAna.maxPriceDt, '--')
												}})</span>
											</td>
											<td class="text-end" :data-th="$t('pro.lowestPrice') + '(' + $t('pro.year') + ')'">
												<span>{{ $filters.defaultValue(secPriceAna.minAprice, '--') }}({{
													$filters.defaultValue(secPriceAna.minPriceDt, '--')
												}})</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.historicalPriceTrend') }}</h4>
								</div>
								<div class="text-center">
									<vue-net-chart
										ref="secNetChartRef"
										:chart-id="chartId"
										:pro-code="proInfo.proCode"
										:pro-price-range-menu="proPriceRangeMenu"
									/>
									<div class="btn-group btn-group-sm mb-4" role="group">
										<input
											v-for="item in proPriceRangeMenu"
											:id="'secNetPeriod' + item.termValue"
											type="radio"
											class="btn-check"
											name="time"
											:checked="item.termValue == '4'"
											@click="getSecNets(proInfo.proCode, item.rangeType, item.rangeFixed)"
										>
										<label
											v-for="item in proPriceRangeMenu"
											class="btn btn-outline-secondary"
											:for="'secNetPeriod' + item.termValue"
										>{{ $filters.defaultValue(item.termName, '--') }}</label>
									</div>
								</div>

								<div class="caption">
									{{ $t('pro.recent30DaysPrice') }}
								</div>
								<table class="table table-RWD table-bordered text-center">
									<thead>
										<tr>
											<th>{{ $t('pro.date') }}</th>
											<th class="text-end">
												{{ $t('pro.referencePrice') }}
											</th>
											<th>{{ $t('pro.date') }}</th>
											<th class="text-end">
												{{ $t('pro.referencePrice') }}
											</th>
											<th>{{ $t('pro.date') }}</th>
											<th class="text-end">
												{{ $t('pro.referencePrice') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="(item, index) in secPriceHist">
											<td :data-th="$t('pro.date')">
												{{ $filters.defaultValue(item.priceDt1, '--') }}
											</td>
											<td class="text-end" :data-th="$t('pro.referencePrice')">
												<span>{{ $filters.defaultValue(item.aprice1, '--') }}</span>
											</td>
											<td :data-th="$t('pro.date')">
												{{ $filters.defaultValue(item.priceDt2, '--') }}
											</td>
											<td class="text-end" :data-th="$t('pro.referencePrice')">
												<span>{{ $filters.defaultValue(item.aprice2, '--') }}</span>
											</td>
											<td :data-th="$t('pro.date')">
												{{ $filters.defaultValue(item.priceDt3, '--') }}
											</td>
											<td class="text-end" :data-th="$t('pro.referencePrice')">
												<span>{{ $filters.defaultValue(item.aprice3, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionovers05" class="tab-pane fade">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.performanceAnalysis') }}</h4>
								</div>
								<div class="card-body">
									<div class="row">
										<div class="col-sm-2">
											<label class="tc-blue">{{ $t('pro.selectProduct') }}:</label>
										</div>
										<div class="col-sm-8">
											<div class="input-group">
												<select v-model="issuerCode" class="form-select">
													<option selected value>
														--
													</option>
													<option v-for="item in productList" :value="item.proCode">
														{{ $filters.defaultValue(item.proName, '--') }}
														{{ $filters.defaultValue(item.proCode, '--') }}
													</option>
												</select>
											</div>
										</div>
										<div class="col-sm-2">
											<p>
												<input
													class="btn btn-primary text-alignRight"
													type="button"
													:value="$t('pro.addButton')"
													@click="addPro()"
												>
											</p>
										</div>
									</div>

									<div class="caption">
										{{ $t('pro.addedProducts') }}
									</div>
									<div class="table-responsive mb-3">
										<table class="table table-bordered">
											<thead>
												<tr>
													<th>{{ $t('pro.productName') }}</th>
													<th class="text-end">
														{{ $t('pro.oneYearReturn') }}
													</th>
													<th class="text-center">
														{{ $t('pro.action') }}
													</th>
												</tr>
											</thead>
											<tbody>
												<tr v-for="item in observedProList">
													<td>
														{{ $filters.defaultValue(item.proName, '--') }}{{ $filters.defaultValue(item.proCode, '--') }}
													</td>
													<td class="text-end">
														{{ $filters.defaultValue($filters.formatPct(item.fcTdReturn), '--') }}%
													</td>
													<td class="text-center">
														<button
															type="button"
															class="btn btn-danger btn-icon"
															data-bs-toggle="tooltip"
															:title="$t('pro.delete')"
															@click="deletePro(item.proCode)"
														>
															<i class="fa-solid fa-trash" />
														</button>
													</td>
												</tr>
											</tbody>
										</table>
									</div>

									<div class="text-center">
										<!-- Performance Analysis Chart -->
										<vue-performances-chart ref="secPerformancesChartRef" :chart-id="performancesId" />
										<div class="btn-group btn-group-sm mb-4" role="group">
											<template v-for="item in proPriceRangeMenu">
												<input
													:id="'performancesPeriod' + item.termValue"
													type="radio"
													class="btn-check"
													name="time"
													:checked="item.termValue == '4' ? true : false"
												>
												<label
													class="btn btn-outline-secondary"
													:for="'performancesPeriod' + item.termValue"
													@click.prevent="getSecPerformances(proCodes, item.rangeType, item.rangeFixed)"
												>{{ $filters.defaultValue(item.termName, '--') }}</label>
											</template>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-white" @click.prevent="close()">
					{{ $t('pro.closeWindow') }}
				</button>
			</div>
		</div>
	</div>
	<!-- Modal 9 End -->
</template>
<script>
import moment from 'moment';
import _ from 'lodash';
import vuePerformancesChart from './performancesChart.vue';
import vueNetChart from './netChart.vue';
export default {
	components: {
		vuePerformancesChart,
		vueNetChart
	},
	props: {
		downloadFile: Function,
		downloadOtherFile: Function,
		finReqCodeMenu: Array,
		proPriceRangeMenu: Array,
		closeModal: Function
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				bondInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},

			chartsData: [], // Performance analysis chart data
			proCodes: [], // Performance analysis - added products
			productList: [], // Performance analysis - product list
			observedProList: [], // Performance analysis - added products return rate list
			issuerCode: null, // Performance analysis - selected product

			finReqCodes: [],
			proFileA: {},
			proFileD: {},
			proFileF: {},
			proFileG: {},
			otherFileList: [],
			secPriceAna: {},
			secPriceHist: [],
			chartId: 'secNetChartId',
			performancesId: 'secPerformancesChartId'
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		getProInfo: async function (proCode, pfcatCode) {
			const self = this;

			self.proCodes = [];
			self.proCodes.push(proCode); // Default added products

			// Get product basic information
			const ret = await this.$api.getProductInfoApi({ proCode, pfcatCode });
			if (_.isNil(ret.data)) {
				ret.data = {};
				this.$bi.alert(this.$t('pro.dataNotExist'));
				return;
			}
			if (_.isNil(ret.data.secInfo)) {
				ret.data.secInfo = {};
			}

			self.proInfo = ret.data;

			// Process product basic information here

			// Product common data
			if (!_.isUndefined(self.proInfo.allYn)) {
				const allYnList = await this.$api.getAdmCodeDetail({ codeType: 'CUS_BU' });
				const allYnObjs = _.filter(allYnList.data, {
					codeValue: self.proInfo.allYn
				});
				if (!_.isEmpty(allYnObjs)) {
					self.proInfo.allYn = allYnObjs[0].codeName;
				}
			}

			if (!_.isUndefined(self.proInfo.targetCusBu)) {
				const targetCusBuList = await this.$api.getAdmCodeDetail({ codeType: 'PROF_INVESTOR' });
				const targetCusBuObjs = _.filter(targetCusBuList.data, {
					codeValue: self.proInfo.targetCusBu
				});
				if (!_.isEmpty(targetCusBuObjs)) {
					self.proInfo.targetCusBu = targetCusBuObjs[0].codeName;
				}
			}

			if (!_.isUndefined(self.proInfo.buyYn) && !_.isUndefined(self.proInfo.sellYn)) {
				const selectYnList = await this.$api.getAdmCodeDetail({ codeType: 'SELECT_YN' });

				if (!_.isUndefined(self.proInfo.buyYn)) {
					const buyYnObjs = _.filter(selectYnList.data, {
						codeValue: self.proInfo.buyYn
					});
					self.proInfo.buyYn = buyYnObjs[0].codeName;
				}

				if (!_.isUndefined(self.proInfo.sellYn)) {
					const sellYnObjs = _.filter(selectYnList.data, {
						codeValue: self.proInfo.sellYn
					});
					if (!_.isEmpty(sellYnObjs)) {
						self.proInfo.sellYn = sellYnObjs[0].codeName;
					}
				}
			}

			if (!_.isUndefined(self.proInfo.volatilityType)) {
				const volatilityTypeList = await this.$api.getAdmCodeDetail({ codeType: 'VOLATILITY_TYPE' });
				const volatilityTypeObjs = _.filter(volatilityTypeList.data, {
					codeValue: self.proInfo.volatilityType
				});
				self.proInfo.volatilityType = volatilityTypeObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.intFreqUnitype)) {
				const intFreqUnitypeList = await this.$api.getAdmCodeDetail({ codeType: 'INT_FREQ_UNITTYPE' });
				const intFreqUnitypeObjs = _.filter(intFreqUnitypeList.data, {
					codeValue: self.proInfo.intFreqUnitype
				});
				self.proInfo.intFreqUnitype = intFreqUnitypeObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.selprocatNames)) {
				const selprocatNames = self.proInfo.selprocatNames.replaceAll(',', ', ');
				self.proInfo.selprocatNames = selprocatNames;
			}

			if (!_.isUndefined(self.proInfo.finReqCode)) {
				self.finReqCodes = self.proInfo.finReqCode.split(',');
			}

			// Product additional data
			const commInfoRet = await this.$api.getProductsCommInfo({ proCode, pfcatCode });
			if (!_.isEmpty(commInfoRet.data)) {
				if (commInfoRet.data.proDocs) {
					self.otherFileList = commInfoRet.data.proDocs; // Other related attachments
					self.otherFileList.forEach(function (item) {
						// Other related attachments - file display time range
						item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
					});
				}

				const proFileList = commInfoRet.data.proFiles;
				if (!_.isEmpty(proFileList)) {
					self.proFileA = proFileList.filter(proFile => proFile.fileType === 'A')[0];
					self.proFileD = proFileList.filter(proFile => proFile.fileType === 'D')[0];
					self.proFileF = proFileList.filter(proFile => proFile.fileType === 'F')[0];
					self.proFileG = proFileList.filter(proFile => proFile.fileType === 'G')[0];
				}
			}
			self.observedPro(); // Performance analysis - get added products list
			self.productMenu();
		},
		// Product information/price analysis and last 30 days price
		// Product information/price analysis and last 30 days price
		getSecPriceAna: async function (proCode) {
			const self = this;
			const proCodeArray = { 0: proCode };

			const ret = await this.$api.getPriceAnaApi({ proCodes: [proCode] });
			if (!_.isEmpty(ret.data)) {
				self.secPriceAna = ret.data;
				if (!_.isNil(ret.data.priceHist)) {
					const orgPriceHis = ret.data.priceHist;
					const newPriceHis = [];
					orgPriceHis.forEach(function (item, index) {
						if (index % 3 == 0) {
							if (index + 2 < orgPriceHis.length) {
								const pricHisObj = {
									priceDt1: orgPriceHis[index].priceDt,
									aprice1: Number.parseFloat(orgPriceHis[index].aprice),
									priceDt2: orgPriceHis[index + 1].priceDt,
									aprice2: Number.parseFloat(orgPriceHis[index + 1].aprice),
									priceDt3: orgPriceHis[index + 2].priceDt,
									aprice3: Number.parseFloat(orgPriceHis[index + 2].aprice)
								};
								newPriceHis.push(pricHisObj);
							}
							else if (index + 1 < orgPriceHis.length) {
								const pricHisObj = {
									priceDt1: orgPriceHis[index].priceDt,
									aprice1: Number.parseFloat(orgPriceHis[index].aprice),
									priceDt2: orgPriceHis[index + 1].priceDt,
									aprice2: Number.parseFloat(orgPriceHis[index + 1].aprice),
									priceDt3: null,
									aprice3: null
								};
								newPriceHis.push(pricHisObj);
							}
							else {
								const pricHisObj = {
									priceDt1: orgPriceHis[index].priceDt,
									aprice1: Number.parseFloat(orgPriceHis[index].aprice),
									priceDt2: null,
									aprice2: null,
									priceDt3: null,
									aprice3: null
								};
								newPriceHis.push(pricHisObj);
							}
						}
					});
					self.secPriceHist = newPriceHis;
				}
			}
		},
		// Net value history chart
		getSecNets: function (proCode, rangeType, rangeFixed) {
			const self = this;
			self.$refs.secNetChartRef.getNets(proCode, rangeType, rangeFixed);
		},
		// Performance analysis chart
		// Performance analysis chart
		getSecPerformances: async function (proCodes, rangeType, rangeFixed) {
			const self = this;
			const ret = await this.$api.getPerformanceRunChartApi({ proCodes, freqType: rangeType, freqFixed: rangeFixed });
			if (!_.isEmpty(ret.data.datas)) {
				for (let i = 0; i < ret.data.length; i++) {
					ret.data[i].datas.forEach((e) => {
						e.value = e.returnFc;
						e.date = new Date(e.returnDt).getTime();
					});
				}
				self.chartsData = ret.data;
				self.$refs.secPerformancesChartRef.initChart(self.chartsData);
			}
		},
		// Performance analysis - added products list
		observedPro: async function () {
			const self = this;
			const ret = await this.$api.getObservedProductsApi({ proCodes: self.proCodes });
			self.observedProList = ret.data;
		},
		// Performance analysis - product selection dropdown
		productMenu: async function () {
			const self = this;
			const ret = await this.$api.getProductByPfcatCodeApi({ pfcatCode: 'SEC' });
			self.productList = ret.data;
		},
		// Performance analysis - add selected product button
		addPro() {
			const self = this;
			let pk = null;
			pk = _.find(self.proCodes, function (item) {
				return item == self.issuerCode;
			});
			if (self.issuerCode != null && pk == null) {
				self.proCodes.push(self.issuerCode); // Add selected product
				self.observedPro(); // Performance analysis - get added products list
				self.getSecPerformances(self.proCodes, 'Y', -1.0); // Product info/performance analysis chart
			}
			else if (self.issuerCode != null && pk != null) {
				this.$bi.alert(this.$t('pro.productAlreadyAdded'));
			}
			else {
				this.$bi.alert(this.$t('pro.pleaseSelectProduct'));
			}
		},
		// Performance analysis - delete added product button
		deletePro(proCode) {
			const self = this;
			if (self.proCodes.length > 1) {
				const index = self.proCodes.indexOf(proCode); // Find index to remove
				self.proCodes.splice(index, 1); // Remove added product (index position, number of elements to delete)
				self.observedPro(); // Performance analysis - get added products list
				self.geSectPerformances(self.proCodes, 'Y', -1.0); // Product info/performance analysis chart
			}
			else {
				this.$bi.alert(this.$t('pro.atLeastOneProduct'));
			}
		}
	} // methods end
};
</script>
