<template>
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('pro.trustEtf') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click.prevent="close()"
				/>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-fund" />
							<h4><span>{{ $t('pro.productName') }}</span> <br>{{ $filters.defaultValue(proInfo.proName, '--') }}</h4>
						</div>
						<h4 class="pro_value">
							<span>{{ $t('pro.latestNav') }}</span>
							<br>{{ $filters.formatNumber(proInfo.aprice, '0,0.00' || '--') }} <br><span>{{
								$filters.formatDate(proInfo.priceDt, '--')
							}}</span>
						</h4>
						<h4 class="pro_value">
							<span>{{ $t('pro.latestMarketPrice') }}</span>
							<br>{{ $filters.formatNumber(proInfo.sprice, '0,0.00' || '--') }} <br><span>{{
								$filters.formatDate(proInfo.priceDt, '--')
							}}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productCode') }}</span> <br>{{ $filters.defaultValue(proInfo.bankProCode, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6>
								<span>{{ $t('pro.assetCategory') }} <br></span>{{ $filters.defaultValue(proInfo.assetcatName, '--') }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productMainCategory') }}</span> <br>{{ $filters.defaultValue(proInfo.pfcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productSubCategory') }}</span><br>{{ $filters.defaultValue(proInfo.proTypeName, '--') }}</h6>
						</div>
					</div>
				</div>
				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item">
							<a class="nav-link active" href="#Sectionetf1" data-bs-toggle="pill">{{ $t('pro.productBasicData') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#Sectionetf2" data-bs-toggle="pill">{{ $t('pro.productCommonData') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#Sectionetf3" data-bs-toggle="pill">{{ $t('pro.productAdditionalData') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link datainfo" href="#Sectionetf4" data-bs-toggle="pill">{{ $t('pro.fundData') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link datainfo" href="#Sectionetf5" data-bs-toggle="pill">{{ $t('pro.etfHoldings') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link datainfo" href="#Sectionetf6" data-bs-toggle="pill">{{ $t('pro.netValueAnalysis') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link datainfo" href="#Sectionetf7" data-bs-toggle="pill">{{ $t('pro.performanceAnalysis') }}</a>
						</li>
						<li class="nav-item">
							<a
								class="nav-link datainfo"
								href="#Sectionetf8"
								data-bs-toggle="pill"
								@click="getEtfPerformanceStats(proInfo.proCode)"
							>{{ $t('pro.statisticalInfo') }}</a>
						</li>
						<li class="nav-item">
							<a
								class="nav-link datainfo"
								href="#Sectionetf9"
								data-bs-toggle="pill"
								@click="getEtfIntRate(proInfo.proCode)"
							>{{ $t('pro.etfDividend') }}</a>
						</li>
					</ul>
					<div class="tab-content">
						<div id="Sectionetf1" class="tab-pane fade show active">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.etfProductInfo') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.exchangeName') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.etfInfo.exchangeName, '--') }}
											</td>
											<th>{{ $t('pro.exchangeCode') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.etfInfo.exchangeCode, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.etfType') }}</th>
											<td>{{ $filters.defaultValue(proInfo.etfInfo.proTypeName, '--') }}</td>
											<th>{{ $t('pro.riskLevel') }}</th>
											<td>{{ $filters.defaultValue(proInfo.etfInfo.riskName, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.currency') }}</th>
											<td>{{ $filters.defaultValue(proInfo.etfInfo.curCode, '--') }}</td>
											<th>{{ $t('pro.sellable') }}</th>
											<td>{{ $filters.defaultValue(proInfo.etfInfo.buyYn, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.isinCode') }}</th>
											<td>{{ $filters.defaultValue(proInfo.etfInfo.isinCode, '--') }}</td>
											<th>{{ $t('pro.tradingUnit') }}</th>
											<td>{{ $filters.defaultValue(proInfo.etfInfo.txUnit, '--') }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionetf2" class="tab-pane fade">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.salesRelatedData') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th width="20%">
												{{ $t('pro.salesRegion') }}
											</th>
											<td v-if="proInfo.allYn == 'Y'" width="30%">
												{{ $t('pro.allBranches') }}
											</td>
											<td v-else width="30%">
												--
											</td>
											<th width="20%">
												{{ $t('pro.piOnlyPurchase') }}
											</th>
											<td width="30%">
												{{ $filters.defaultValue(proInfo.profInvestorYn, '--') }}
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.brokerOrderCode') }}</span></th>
											<td class="wd-80p" colspan="3">
												<span>{{ $filters.defaultValue(proInfo.brokerCode, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.salesTarget') }}</span></th>
											<td class="wd-30p" colspan="3">
												{{ $filters.defaultValue(proInfo.targetCusBu, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.openForSubscription') }}</th>
											<td>{{ $filters.defaultValue(proInfo.buyYn, '--') }}</td>
											<th>{{ $t('pro.openForRedemption') }}</th>
											<td>{{ $filters.defaultValue(proInfo.sellYn, '--') }}</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.volatilityType') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.volatilityType, '--') }}</span>
											</td>
											<th><span>{{ $t('pro.dividendFrequency') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.intFreqUnitype, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.capitalProtectionRequirement') }}</th>
											<td colspan="3">
												{{ $filters.defaultValue(proInfo.principalGuarYn, '--') }}
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.financialNeeds') }}</span></th>
											<td colspan="3">
												<div v-for="item in finReqCodeMenu" class="form-check form-check-inline">
													<input
														id="c1"
														v-model="finReqCodes"
														class="form-check-input"
														name="finReqCodes"
														disabled
														:value="item.codeValue"
														type="checkbox"
													>
													<label class="form-check-label" for="c1">{{ $filters.defaultValue(item.codeName, '--') }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.productTags') }}</th>
											<td disabled="disabled" colspan="3">
												{{ $filters.defaultValue(proInfo.selprocatNames, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionetf3" class="tab-pane fade">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.otherSettings') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>{{ $t('pro.investmentTarget') }}</span></th>
											<td class="wd-80p">
												<span>{{ $filters.defaultValue(proInfo.sectorName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.investmentRegion') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.geoFocusName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.benchmarkSetting') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.benchmarkName, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.remarks') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.memo, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.attachments') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.publicProspectus') }}</th>
											<td class="wd-80p">
												<a v-if="proFileB && proFileB.url" :href="proFileB.url" target="_blank">{{
													$filters.defaultValue(proFileB.url, '--')
												}}</a><br v-if="proFileB && proFileB.url"><a v-else>--</a>
												<a
													v-if="proFileB"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileB)"
												>{{
													$filters.defaultValue(proFileB.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.investorNotice') }}</th>
											<td class="wd-80p">
												<a v-if="proFileD && proFileD.url" :href="proFileD.url" target="_blank">{{
													$filters.defaultValue(proFileD.url, '--')
												}}</a><br v-if="proFileD && proFileD.url"><a v-else>--</a>
												<a
													v-if="proFileD"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileD)"
												>{{
													$filters.defaultValue(proFileD.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.fundMonthlyReport') }}</th>
											<td class="wd-80p">
												<a v-if="proFileE && proFileE.url" :href="proFileE.url" target="_blank">{{
													$filters.defaultValue(proFileE.url, '--')
												}}</a><br v-if="proFileE && proFileE.url"><a v-else>--</a>
												<a
													v-if="proFileE"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileE)"
												>{{
													$filters.defaultValue(proFileE.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<a v-if="proFileF && proFileF.url" :href="proFileF.url" target="_blank">{{
													$filters.defaultValue(proFileF.url, '--')
												}}</a><br v-if="proFileF && proFileF.url"><a v-else>--</a>
												<a
													v-if="proFileF"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileF)"
												>{{
													$filters.defaultValue(proFileF.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.other') }}</th>
											<td class="wd-80p">
												<a v-if="proFileG && proFileG.url" :href="proFileG.url" target="_blank">{{
													$filters.defaultValue(proFileG.url, '--')
												}}</a><br v-if="proFileG && proFileG.url"><a v-else>--</a>
												<a
													v-if="proFileG"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileG)"
												>{{
													$filters.defaultValue(proFileG.showName, '--')
												}}</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.otherRelatedAttachments') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<td>
												<span v-for="(item, index) in otherFileList">
													<a
														v-if="index === otherFileList.length - 1"
														v-show="item.show"
														href="#"
														class="tx-link"
														@click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}</a>
													<a
														v-else
														v-show="item.show"
														href="#"
														class="tx-link"
														@click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}、</a>
												</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="tx-note">
								{{ $t('pro.otherRelatedAttachmentsNote') }}
							</div>
						</div>

						<div id="Sectionetf4" class="tab-pane fade">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.dataDetail') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th class="wd-20p">
												{{ $t('pro.issuingCompany') }}
											</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(etfDetail.fproviderName, '--') }}
											</td>
											<th class="wd-20p">
												{{ $t('pro.establishmentDate') }}
											</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(etfDetail.inceptionDt, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.currency') }}</th>
											<td>{{ $filters.defaultValue(etfDetail.currency, '--') }}</td>
											<th>{{ $t('pro.riskAttribute') }}</th>
											<td>{{ $filters.defaultValue(etfDetail.attrValName, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.exchange') }}</th>
											<td>{{ $filters.defaultValue(etfDetail.primaryExchange, '--') }}</td>
											<th>{{ $t('pro.exchangeCode') }}</th>
											<td>{{ $filters.defaultValue(etfDetail.refCode, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.publicProspectus') }}</th>
											<td>{{ $filters.defaultValue(etfDetail.fileName, '--') }}</td>
											<th>{{ $t('pro.totalManagementFee') }}</th>
											<td>{{ $filters.defaultValue(etfDetail.grossExpenseRatio, '--') }}%</td>
										</tr>
										<tr>
											<th>{{ $t('pro.etfSize') }}</th>
											<td>{{ $filters.defaultValue(etfDetail.etfSize, '--') }}</td>
											<th>{{ $t('pro.registrationLocation') }}</th>
											<td>{{ $filters.defaultValue(etfDetail.domicile, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.investmentTarget') }}</th>
											<td>{{ $filters.defaultValue(etfDetail.className, '--') }}</td>
											<th>{{ $t('pro.distributor') }}</th>
											<td>{{ $filters.defaultValue(etfDetail.dproviderName, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.investmentRegion') }}</th>
											<td>{{ $filters.defaultValue(etfDetail.geographicalFocus, '--') }}</td>
											<th>{{ $t('pro.custodianInstitution') }}</th>
											<td>{{ $filters.defaultValue(etfDetail.cproviderName, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.fundManager') }}</th>
											<td colspan="3" width="80%">
												{{ $filters.defaultValue(etfDetail.mgrName, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.benchmarkIndex') }}</th>
											<td colspan="3">
												{{ $filters.defaultValue(etfDetail.benchmarkName, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.investmentStrategy') }}</th>
											<td colspan="3">
												{{ $filters.defaultValue(etfDetail.objective, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionetf5" class="tab-pane fade">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.stockHoldingDetail') }}</h4>
								</div>
								<div class="card-body">
									<div class="row">
										<div class="col-lg-6">
											<vue-column-chart
												v-if="proInfo"
												ref="etfStockDetailChartRef"
												:chart-id="etfStockDetailChartId"
												:prop-chart-data="etfStockDetailChartData"
											/>
										</div>
										<div class="col-lg-6">
											<table class="table table-RWD table-bordered">
												<thead>
													<tr>
														<th class="wd-10p">
															{{ $t('pro.rank') }}
														</th>
														<th>{{ $t('pro.holdingCompany') }}</th>
														<th>{{ $t('pro.date') }}</th>
													</tr>
												</thead>
												<tbody v-if="etfStockDetail.length > 0">
													<tr v-for="item in etfStockDetail">
														<td class="tx-sort" data-th="排名">
															{{ $filters.defaultValue(item.rank, '--') }}
														</td>
														<td :data-th="$t('pro.holdingCompany')">
															{{ $filters.defaultValue(item.name, '--') }}
														</td>
														<td class="text-center" :data-th="$t('pro.date')">
															{{ $filters.defaultValue(item.dataDt, '--') }}
														</td>
													</tr>
												</tbody>
												<tbody v-else>
													<tr>
														<td colspan="3">
															{{ $t('pro.fundCompanyHasNotProvidedStockHoldingData') }}
														</td>
													</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.stockDistributionByIndustry') }}</h4>
								</div>
								<div class="card-body">
									<div class="row">
										<div class="col-lg-6">
											<vue-column-chart
												v-if="proInfo"
												ref="etfStockDistributionChartRef"
												:chart-id="etfStockDistributionChartId"
												:prop-chart-data="etfStockDistributionChartData"
											/>
										</div>
										<div class="col-lg-6">
											<table class="table table-RWD table-bordered">
												<thead>
													<tr>
														<th class="wd-10p">
															{{ $t('pro.rank') }}
														</th>
														<th>{{ $t('pro.allocationCombination') }}</th>
													</tr>
												</thead>
												<tbody v-if="etfStockDistribution.length > 0">
													<tr v-for="item in etfStockDistribution">
														<td :data-th="$t('pro.rank')">
															{{ $filters.defaultValue(item.rank, '--') }}
														</td>
														<td :data-th="$t('pro.allocationCombination')">
															{{ $filters.defaultValue(item.itemName, '--') }}
														</td>
													</tr>
												</tbody>
												<tbody v-else>
													<tr>
														<td colspan="2">
															{{ $t('pro.fundCompanyHasNotProvidedAllocationData') }}
														</td>
													</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div id="Sectionetf6" class="tab-pane fade">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.priceAnalysis') }}</h4>
								</div>
								<table class="table table-RWD table-bordered">
									<thead>
										<tr>
											<th class="wd-20p">
												{{ $t('pro.item') }}
											</th>
											<th class="wd-30p">
												{{ $t('pro.price') }}
											</th>
											<th class="wd-25p">
												{{ $t('pro.highestPriceYear') }}
											</th>
											<th class="wd-25p">
												{{ $t('pro.lowestPriceYear') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td :data-th="$t('pro.item')">
												{{ $t('pro.netValue') }}
											</td>
											<td class="text-end" :data-th="$t('pro.price')">
												<span>{{ $filters.defaultValue(etfPrice.nprice, '--') }}</span>
											</td>
											<td class="text-end" :data-th="$t('pro.highestPriceYear')">
												<span>{{ $filters.defaultValue(etfPrice.maxNavPrice, '--') }}</span>
											</td>
											<td class="text-end" :data-th="$t('pro.lowestPriceYear')">
												<span>{{ $filters.defaultValue(etfPrice.minNavPrice, '--') }}</span>
											</td>
										</tr>
										<tr>
											<td :data-th="$t('pro.item')">
												{{ $t('pro.marketPrice') }}
											</td>
											<td class="text-end" :data-th="$t('pro.price')">
												<span>{{ $filters.defaultValue(etfPrice.mprice, '--') }}</span>
											</td>
											<td class="text-end" :data-th="$t('pro.highestPriceYear')">
												<span>{{ $filters.defaultValue(etfPrice.maxMidPrice, '--') }}</span>
											</td>
											<td class="text-end" :data-th="$t('pro.lowestPriceYear')">
												<span>{{ $filters.defaultValue(etfPrice.minMidPrice, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.historicalPriceTrend') }}：{{ $filters.defaultValue(proInfo.proName, '--') }}</h4>
								</div>
								<div class="text-center">
									<vue-price-chart
										v-if="proInfo"
										ref="etfPriceChartRef"
										:chart-id="priceChartId"
										:prop-chart-data="pricChartData"
										:pro-price-range-menu="proPriceRangeMenu"
									/>
									<div class="btn-group btn-group-sm mb-4" role="group">
										<div class="btn-group btn-group-sm mb-4" role="group">
											<input
												v-for="item in proPriceRangeMenu"
												:id="'etfPricePeriod' + item.termValue"
												type="radio"
												class="btn-check"
												name="time"
												:checked="item.termValue == '4'"
												@click="getPricesChartData(proInfo.proCode, item.rangeType, item.rangeFixed)"
											>
											<label
												v-for="item in proPriceRangeMenu"
												class="btn btn-outline-secondary"
												:for="'etfPricePeriod' + item.termValue"
											>{{ $filters.defaultValue(item.termName, '--') }}</label>
										</div>
									</div>
								</div>

								<div class="caption">
									{{ $t('pro.recent30DaysNetValue') }}
								</div>
								<table class="table table-RWD table-bordered text-center">
									<thead>
										<tr>
											<th>{{ $t('pro.date') }}</th>
											<th class="text-end">
												{{ $t('pro.netValue') }}
											</th>
											<th class="text-end">
												{{ $t('pro.marketPrice') }}
											</th>
											<th>{{ $t('pro.date') }}</th>
											<th class="text-end">
												{{ $t('pro.netValue') }}
											</th>
											<th class="text-end">
												{{ $t('pro.marketPrice') }}
											</th>
											<th>{{ $t('pro.date') }}</th>
											<th class="text-end">
												{{ $t('pro.netValue') }}
											</th>
											<th class="text-end">
												{{ $t('pro.marketPrice') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in etfPriceHis">
											<td data-th="日期">
												{{ $filters.defaultValue(item.priceDt1, '--') }}
											</td>
											<td class="text-end" :data-th="$t('pro.netValue')">
												{{ $filters.defaultValue(item.nprice1, '--') }}
											</td>
											<td class="text-end" :data-th="$t('pro.marketPrice')">
												{{ $filters.defaultValue(item.mprice1, '--') }}
											</td>
											<td data-th="日期">
												{{ $filters.defaultValue(item.priceDt2, '--') }}
											</td>
											<td class="text-end" :data-th="$t('pro.netValue')">
												{{ $filters.defaultValue(item.nprice2, '--') }}
											</td>
											<td class="text-end" :data-th="$t('pro.marketPrice')">
												{{ $filters.defaultValue(item.mprice2, '--') }}
											</td>
											<td data-th="日期">
												{{ $filters.defaultValue(item.priceDt3, '--') }}
											</td>
											<td class="text-end" :data-th="$t('pro.netValue')">
												{{ $filters.defaultValue(item.nprice3, '--') }}
											</td>
											<td class="text-end" :data-th="$t('pro.marketPrice')">
												{{ $filters.defaultValue(item.mprice3, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionetf7" class="tab-pane fade">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.etfPerformanceAnalysis') }}</h4>
								</div>
								<div class="row">
									<div class="col-sm-2">
										<label class="tc-blue">{{ $t('pro.selectEtfUsStock') }}：</label>
									</div>
									<div class="col-sm-6">
										<select v-model="profileName" class="form-select">
											<option selected value="">
												--
											</option>
											<option v-for="item in etfProfileNameMenu" :value="item.lipperId">
												{{ $filters.defaultValue(item.nameFull, '--') }}
											</option>
										</select>
									</div>
									<div class="col-sm-4">
										<p>
											<input
												id="profileNameBtn"
												class="btn btn-primary text-alignRight"
												type="button"
												:value="$t('pro.add')"
												@click="addEtf()"
											>
										</p>
									</div>

									<div class="col-sm-2">
										<label class="tc-blue">{{ $t('pro.selectCorrespondingIndex') }}：</label>
									</div>
									<div class="col-sm-6">
										<select v-model="benchmark" class="form-select">
											<option selected value="">
												--
											</option>
											<option v-for="item in etfProfileBenchmarksMenu" :value="item.lipperId">
												{{ $filters.defaultValue(item.benchmarkName, '--') }}
											</option>
										</select>
									</div>
									<div class="col-sm-4">
										<p>
											<input
												class="btn btn-primary text-alignRight"
												type="button"
												:value="$t('pro.add')"
												@click="addBenchmark()"
											>
										</p>
									</div>
								</div>
								<div class="caption">
									{{ $t('pro.addedEtfUsStock') }}
								</div>
								<div class="table-responsive mb-3">
									<table class="table">
										<thead>
											<tr>
												<th>{{ $t('pro.etfCode') }}</th>
												<th>{{ $t('pro.etfName') }}</th>
												<th>{{ $t('pro.currency') }}</th>
												<th class="text-end">
													{{ $t('pro.oneYearReturn') }}
												</th>
												<th class="text-end">
													{{ $t('pro.threeYearReturn') }}
												</th>
												<th class="text-end">
													{{ $t('pro.fiveYearReturn') }}
												</th>
												<th class="text-end">
													{{ $t('pro.ytdCumulativeReturn') }}
												</th>
												<th class="text-end">
													{{ $t('pro.oneYearStandardDeviation') }}
												</th>
												<th class="text-center">
													{{ $t('pro.action') }}
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="item in observedEtfsList">
												<td>{{ $filters.defaultValue(item.bankProCode, '--') }}</td>
												<td>{{ $filters.defaultValue(item.nameFull, '--') }}</td>
												<td>{{ $filters.defaultValue(item.currency, '--') }}</td>
												<td class="text-end">
													{{ $filters.defaultValue(item.value1Y, '--') }}%
												</td>
												<td class="text-end">
													{{ $filters.defaultValue(item.value3Y, '--') }}%
												</td>
												<td class="text-end">
													{{ $filters.defaultValue(item.value5Y, '--') }}%
												</td>
												<td class="text-end">
													{{ $filters.defaultValue(item.valueYtd, '--') }}%
												</td>
												<td class="text-end">
													{{ $filters.defaultValue(item.valueStd, '--') }}
												</td>
												<td class="text-center">
													<button
														type="button"
														class="btn btn-danger btn-icon"
														data-bs-toggle="tooltip"
														title=""
														:data-bs-original-title="$t('pro.delete')"
														@click="deleteObservedEtfs(item.lipperId)"
													>
														<i class="fa-solid fa-trash" />
													</button>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
								<div class="text-center">
									<vue-performances-chart ref="etfPerformancesChartRef" :chart-id="performancesId" />
									<div class="btn-group btn-group-sm mb-4" role="group">
										<template v-for="item in proPriceRangeMenu">
											<input
												:id="'etfPerformancesPeriod' + item.termValue"
												type="radio"
												class="btn-check"
												name="time"
												:checked="item.termValue == '4' ? true : false"
												@click="getEtfPerformances(item.rangeType, item.rangeFixed)"
											>
											<label class="btn btn-outline-secondary" :for="'etfPerformancesPeriod' + item.termValue">{{
												$filters.defaultValue(item.termName, '--')
											}}</label>
										</template>
									</div>
								</div>
							</div>
						</div>

						<div id="Sectionetf8" class="tab-pane fade">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.relatedStatisticalInfo') }}</h4>
								</div>
								<table class="table table-RWD table-bordered">
									<thead>
										<tr>
											<th width="32%" />
											<th width="17%" class="text-end">
												{{ $t('pro.sixMonths') }}
											</th>
											<th width="17%" class="text-end">
												{{ $t('pro.oneYear') }}
											</th>
											<th width="17%" class="text-end">
												{{ $t('pro.threeYears') }}
											</th>
											<th width="17%" class="text-end">
												{{ $t('pro.fiveYears') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in performanceStatsList">
											<td>{{ $filters.defaultValue(item.calcTypeName, '--') }}</td>
											<td :data-th="$t('pro.sixMonths')" class="text-end">
												<span v-if="item.value6M">{{ $filters.defaultValue(item.value6M, '--') }}</span><span v-else>----</span>
											</td>
											<td :data-th="$t('pro.oneYear')" class="text-end">
												<span v-if="item.value1Y">{{ $filters.defaultValue(item.value1Y, '--') }}</span><span v-else>----</span>
											</td>
											<td :data-th="$t('pro.threeYears')" class="text-end">
												<span v-if="item.value3Y">{{ $filters.defaultValue(item.value3Y, '--') }}</span><span v-else>----</span>
											</td>
											<td :data-th="$t('pro.fiveYears')" class="text-end">
												<span v-if="item.value5Y">{{ $filters.defaultValue(item.value5Y, '--') }}</span><span v-else>----</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionetf9" class="tab-pane fade">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.etfDividendRecord') }}</h4>
								</div>
								<table class="table table-RWD table-bordered">
									<thead>
										<tr>
											<th>{{ $t('pro.exDividendDate') }}</th>
											<th>{{ $t('pro.paymentDate') }}</th>
											<th class="text-end">
												{{ $t('pro.totalDividend') }}
											</th>
											<th class="text-end">
												{{ $t('pro.annualizedDividendRate') }}(%)
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in etfIntRateList">
											<td :data-th="$t('pro.exDividendDate')">
												{{ $filters.defaultValue(item.xdate, '--') }}
											</td>
											<td :data-th="$t('pro.paymentDate')">
												{{ $filters.defaultValue(item.paymentDt, '--') }}
											</td>
											<td :data-th="$t('pro.totalDividend')" class="text-end">
												{{ $filters.defaultValue(item.xvalue, '--') }}
											</td>
											<td data-th="{{ $t('pro.annualizedDividendRate') }}(%)" class="text-end">
												{{ $filters.defaultValue(item.yield, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-white" @click.prevent="close()">
					{{ $t('pro.closeWindow') }}
				</button>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import moment from 'moment';
import vuePerformancesChart from './performancesChart.vue';
import vuePriceChart from './priceChart.vue';
import vueColumnChart from './columnChart.vue';

export default {
	components: {
		vuePerformancesChart,
		vuePriceChart,
		vueColumnChart
	},
	props: {
		downloadFile: Function,
		downloadOtherFile: Function,
		finReqCodeMenu: Array,
		proPriceRangeMenu: Array,
		close: Function
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				bondInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},
			finReqCodes: [],
			proFileB: {},
			proFileD: {},
			proFileE: {},
			proFileF: {},
			proFileG: {},
			otherFileList: [],
			etfDetail: {},
			etfStockDetail: [],
			etfStockDetailChartId: 'etfStockDetailChart',
			etfStockDetailChartData: [],
			etfStockDistribution: [],
			etfStockDistributionChartId: 'etfStockDistributionChart',
			etfStockDistributionChartData: [],
			etfPrice: {},
			etfPriceHis: [],
			priceChartId: 'etfPriceChart',
			pricChartData: [],
			profileName: '',
			benchmark: '',
			etfProfileLippers: [], // 績效表現-已加入ETF LipperIds
			observedEtfsList: [], // 績效表現-已加入ETF表資料
			etfProfileNameMenu: [], // 績效表現-ETF/美股下拉
			etfProfileBenchmarksMenu: [], // 績效表現-對應指數下拉
			performancesId: 'etfPerformancesChart',
			chartsData: [], // 績效表現圖表資料
			performanceStatsList: [],
			etfIntRateList: []
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		getProInfo: async function (bankProCode, pfcatCode) {
			const self = this;
			const ret = await this.$api.getProductInfoApi({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});
			if (_.isEmpty(ret.data)) {
				ret.data = {};
				this.$bi.alert(this.$t('pro.dataNotExist'));
				return;
			}
			if (_.isEmpty(ret.data.etfInfo)) {
				ret.data.etfInfo = {};
			}

			self.proInfo = ret.data;
			this.$forceUpdate();

			let selectYnList = [];

			const ret2 = await this.$api.getAdmCodeDetail({
				codeType: 'SELECT_YN'
			});
			selectYnList = ret2.data;
			if (!_.isUndefined(self.proInfo.etfInfo.buyYn)) {
				var buyYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.etfInfo.buyYn
				});
				self.proInfo.etfInfo.buyYn = buyYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.buyYn)) {
				var buyYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.buyYn
				});
				self.proInfo.buyYn = buyYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.sellYn)) {
				const sellYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.sellYn
				});
				self.proInfo.sellYn = sellYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.profInvestorYn)) {
				const profInvestorYnObjs = _.filter(selectYnList, {
					codeValue: self.proInfo.profInvestorYn
				});
				self.proInfo.profInvestorYn = profInvestorYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.targetCusBu)) {
				let targetCusBuList = [];

				const ret = await this.$api.getAdmCodeDetail({
					codeType: 'CUS_BU'
				});
				targetCusBuList = ret.data;
				const targetCusBuObjs = _.filter(targetCusBuList, {
					codeValue: self.proInfo.targetCusBu
				});
				self.proInfo.targetCusBu = targetCusBuObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.volatilityType)) {
				let volatilityTypeList = [];
				const ret = await this.$api.getAdmCodeDetail({
					codeType: 'VOLATILITY_TYPE'
				});
				volatilityTypeList = ret.data;
				const volatilityTypeObjs = _.filter(volatilityTypeList, {
					codeValue: self.proInfo.volatilityType
				});
				self.proInfo.volatilityType = volatilityTypeObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.intFreqUnitype)) {
				let intFreqUnitypeList = [];
				const ret = await this.$api.getAdmCodeDetail({
					codeType: 'INT_FREQ_UNITTYPE'
				});
				intFreqUnitypeList = ret.data;
				const intFreqUnitypeObjs = _.filter(intFreqUnitypeList, {
					codeValue: self.proInfo.intFreqUnitype
				});
				self.proInfo.intFreqUnitype = intFreqUnitypeObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.principalGuarYn)) {
				let principalGuarYnList = [];
				const ret = await this.$api.getAdmCodeDetail({
					codeType: 'GUAR_YN'
				});
				principalGuarYnList = ret.data;
				const principalGuarYnObjs = _.filter(principalGuarYnList, {
					codeValue: self.proInfo.principalGuarYn
				});
				self.proInfo.principalGuarYn = principalGuarYnObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.selprocatNames)) {
				const selprocatNames = self.proInfo.selprocatNames.replaceAll(',', '、');
				self.proInfo.selprocatNames = selprocatNames;
			}

			if (!_.isUndefined(self.proInfo.finReqCode)) {
				self.finReqCodes = self.proInfo.finReqCode.split(',');
			}

			// 商品附加資料
			const ret3 = await this.$api.productsCommInfo({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});

			if (!_.isEmpty(ret3.data)) {
				if (ret3.data.proDocs) {
					self.otherFileList = ret3.data.proDocs; // 其他相關附件
					self.otherFileList.forEach(function (item) {
						// 其他相關附件 檔案顯示時間範圍
						item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
					});
				}

				const proFileList = ret3.data.proFiles;
				if (!_.isEmpty(proFileList)) {
					self.proFileB = proFileList.filter(proFile => proFile.fileType === 'B')[0];
					self.proFileD = proFileList.filter(proFile => proFile.fileType === 'D')[0];
					self.proFileE = proFileList.filter(proFile => proFile.fileType === 'E')[0];
					self.proFileF = proFileList.filter(proFile => proFile.fileType === 'F')[0];
					self.proFileG = proFileList.filter(proFile => proFile.fileType === 'G')[0];
				}
			}
			self.etfProfileLippers = [];
		},
		// ETF-基本資料
		getEtfDetail: async function (proCode) {
			const self = this;
			const ret = await this.$api.getEtfDetailApi({
				prodCode: proCode
			});
			if (!_.isEmpty(ret.data)) {
				self.etfDetail = ret.data;
			}
		},
		// ETF-ETF持股
		getEtfStockHold: async function (proCode) {
			const self = this;
			const ret = await this.$api.getEtfStockHoldApi({
				proCode: proCode
			});

			if (!_.isEmpty(ret.data)) {
				if (!_.isNil(ret.data.topHolding)) {
					self.etfStockDetail = ret.data.topHolding;
					self.etfStockDetail.forEach((d) => {
						const ddata = {
							name: d.name,
							value: d.weight
						};
						self.etfStockDetailChartData.push(ddata);
					});
				}
				if (!_.isNil(ret.data.derivedAllocation)) {
					self.etfStockDistribution = ret.data.derivedAllocation;
					self.etfStockDistribution.forEach((d) => {
						const ddata = {
							name: d.itemName,
							value: d.weight
						};
						self.etfStockDistributionChartData.push(ddata);
					});
				}
			}
			self.$refs.etfStockDetailChartRef.initChart();
			self.$refs.etfStockDistributionChartRef.initChart();
		},
		// 淨值分析
		getEtfPrice: async function (proCode) {
			const self = this;
			const ret = await this.$api.getEtfPriceApi({
				prodCode: proCode
			});
			if (!_.isEmpty(ret.data)) {
				self.etfPrice = ret.data;
				if (!_.isNil(ret.data.nearly30daysAssetPriceHis)) {
					const orgPriceHis = _.orderBy(ret.data.nearly30daysAssetPriceHis, ['priceDt'], ['asc']);

					const newPriceHis = [];

					const npriceList = _.filter(orgPriceHis, {
						priceType: 'NAV'
					});
					const mpriceList = _.filter(orgPriceHis, {
						priceType: 'MID'
					});

					let dataSize = 0;
					if (npriceList.length >= mpriceList) {
						dataSize = npriceList.length;
					}
					else {
						dataSize = mpriceList.length;
					}

					for (let index = 0; index < dataSize; index++) {
						if (index % 3 == 0) {
							var priceDt1, nprice1, mprice1, priceDt2, nprice2, mprice2, priceDt3, nprice3, mprice3;

							if (!_.isNil(npriceList[index])) {
								priceDt1 = npriceList[index].priceDt;
								nprice1 = npriceList[index].price;
								var mpriceArray = _.filter(mpriceList, {
									priceDt: npriceList[index].priceDt
								});
								if (!_.isNil(mpriceArray[0])) {
									mprice1 = mpriceArray[0].price;
								}
							}
							else {
								priceDt1 = mpriceList[index]?.priceDt;
								mprice1 = mpriceList[index]?.price;
								nprice1 = null;
							}

							if (!_.isNil(npriceList[index + 1])) {
								priceDt2 = npriceList[index + 1].priceDt;
								nprice2 = npriceList[index + 1].price;
								var mpriceArray = _.filter(mpriceList, {
									priceDt: npriceList[index + 1].priceDt
								});
								if (!_.isNil(mpriceArray[0])) {
									mprice2 = mpriceArray[0].price;
								}
							}
							else {
								priceDt2 = mpriceList[index + 1]?.priceDt;
								mprice2 = mpriceList[index + 1]?.price;
								nprice2 = null;
							}

							if (!_.isNil(npriceList[index + 2])) {
								priceDt3 = npriceList[index + 2].priceDt;
								nprice3 = npriceList[index + 2].price;
								var mpriceArray = _.filter(mpriceList, {
									priceDt: npriceList[index + 2].priceDt
								});
								if (!_.isNil(mpriceArray[0])) {
									mprice3 = mpriceArray[0].price;
								}
							}
							else {
								priceDt3 = mpriceList[index + 2]?.priceDt;
								mprice3 = mpriceList[index + 2]?.price;
								nprice3 = null;
							}
							const pricHisObj = {
								priceDt1: priceDt1,
								nprice1: nprice1,
								mprice1: mprice1,
								priceDt2: priceDt2,
								nprice2: nprice2,
								mprice2: mprice2,
								priceDt3: priceDt3,
								nprice3: nprice3,
								mprice3: mprice3
							};
							newPriceHis.push(pricHisObj);
						}
					}
					self.etfPriceHis = newPriceHis;
				}
			}
		},
		// 淨值分析歷史資料線圖
		getPricesChartData: async function (proCode, rangeType, rangeFixed) {
			const self = this;
			const mData = [];
			const nData = [];
			const ret = await this.$api.getPricesChartDataApi({
				prodCode: proCode,
				freqType: rangeType,
				freqFixeds: rangeFixed
			});
			if (!_.isEmpty(ret.data)) {
				ret.data.forEach((d) => {
					if (d.priceType === 'NAV') {
						const nLineData = {
							date: Date.parse(d.priceDt),
							value: d.price
						};
						nData.push(nLineData);
					}
					if (d.priceType === 'MID') {
						const mLineData = {
							date: Date.parse(d.priceDt),
							value: d.price
						};
						mData.push(mLineData);
					}
				});

				if (!self.pricChartData[0]) {
					self.pricChartData[0] = {};
				}
				if (!self.pricChartData[1]) {
					self.pricChartData[1] = {};
				}

				if (!self.pricChartData[0]) {
					self.pricChartData[0] = {};
				}
				if (!self.pricChartData[1]) {
					self.pricChartData[1] = {};
				}
				self.pricChartData[0].datas = nData;
				self.pricChartData[0].name = self.$t('pro.netValue');
				self.pricChartData[1].datas = mData;
				self.pricChartData[1].name = self.$t('pro.marketPrice');
			}
			self.$refs.etfPriceChartRef.initChart(self.pricChartData);
		},
		// 績效分析-ETF/美股下拉
		getEtfProfileNameMenu: async function (proCode) {
			const self = this;
			const ret = await this.$api.getEtfProfileNameMenuApi;
			if (!_.isEmpty(ret.data)) {
				self.etfProfileNameMenu = ret.data;
				const foundItem = _.find(self.etfProfileNameMenu, item => item.proCode === proCode);

				if (foundItem) {
					self.etfProfileLippers.push(foundItem.lipperId);
					self.addObservedEtfs();
					self.getEtfPerformances('Y', -1.0); // 商品資訊/績效表現圖表
				}
			}
		},
		// 績效分析-對應指數下拉
		getEtfProfileBenchmarksMenu: async function () {
			const self = this;
			const ret = await this.$api.getEtfProfileBenchmarksMenuApi();
			if (!_.isEmpty(ret.data)) {
				self.etfProfileBenchmarksMenu = ret.data;
			}
		},
		// 績效表現-加入ETF(ETF/美股)
		addEtf() {
			const self = this;
			let pk = null;
			pk = _.find(self.etfProfileLippers, function (item) {
				return item == self.profileName;
			});
			if (self.profileName != null && self.profileName != '' && pk == null) {
				self.etfProfileLippers.push(self.profileName); // 加入選擇商品
				self.addObservedEtfs(); // 績效表現 取得 已加入商品清單
				self.getEtfPerformances('Y', -1.0); // 商品資訊/績效表現圖表
			}
			else if (self.profileName != null && pk != null) {
				this.$bi.alert(this.$t('pro.productAlreadyAdded'));
			}
			else {
				this.$bi.alert(this.$t('pro.pleaseSelectProduct'));
			}
		},
		// 績效表現-加入ETF(對應指數)
		addBenchmark() {
			const self = this;
			let pk = null;
			pk = _.find(self.etfProfileLippers, function (item) {
				return item == self.benchmark;
			});
			if (self.benchmark != null && self.benchmark != '' && pk == null) {
				self.etfProfileLippers.push(self.benchmark); // 加入選擇商品
				self.addObservedEtfs(); // 績效表現 取得 已加入商品清單
				self.getEtfPerformances('Y', -1.0); // 商品資訊/績效表現圖表
			}
			else if (self.benchmark != null && pk != null) {
				this.$bi.alert(this.$t('pro.productAlreadyAdded'));
			}
			else {
				this.$bi.alert(this.$t('pro.pleaseSelectProduct'));
			}
		},
		// 績效表現-已加入ETF
		addObservedEtfs: async function () {
			const self = this;

			const proCodes = [];
			self.etfProfileLippers.forEach(function (item) {
				proCodes.push(item);
			});

			const ret = await this.$api.getObservedEtfsApi({
				lipperIds: proCodes.join()
			});

			self.observedEtfsList = ret.data;
		},
		// 績效表現 已加入商品 刪除按鈕
		deleteObservedEtfs(proCode) {
			const self = this;
			const index = self.etfProfileLippers.indexOf(proCode); // 找出要移除的index
			self.etfProfileLippers.splice(index, 1); // 移除加入的商品(要插入或刪除的索引位置, 要刪除的元素數量)
			self.addObservedEtfs(); // 績效表現 取得 已加入商品清單
			self.getEtfPerformances('Y', -1.0); // 商品資訊/績效表現圖表
		},
		// 商品資訊/績效表現圖表
		getEtfPerformances: async function (rangeType, rangeFixed) {
			const self = this;

			if (_.isEmpty(self.etfProfileLippers)) {
				return;
			}

			const ret = await this.$api.getEtfPerformancesApi({
				proCodes: self.etfProfileLippers,
				freqType: rangeType,
				freqFixed: rangeFixed
			});
			if (!_.isEmpty(ret.data)) {
				for (let i = 0; i < ret.data.length; i++) {
					ret.data[i].datas.forEach((e) => {
						e.value = e.returnFc;
						e.date = new Date(e.returnDt).getTime();
					});
				}
				self.chartsData = ret.data;
				self.$refs.etfPerformancesChartRef.initChart(self.chartsData);
			}
		},
		// 各項統計
		getEtfPerformanceStats: async function (proCode) {
			const self = this;
			const ret = await this.$api.getEtfPerformanceStatsApi({
				proCode: proCode
			});
			if (!_.isNil(ret.data)) {
				self.performanceStatsList = ret.data;
			}
		},
		// ETF配息
		getEtfIntRate: async function (proCode) {
			const self = this;
			const ret = await this.$api.getEtfIntRateApi({
				proCode: proCode
			});
			if (!_.isNil(ret.data)) {
				self.etfIntRateList = ret.data;
			}
		}
	} // methods end
};
</script>
