<template>
	<!-- Modal group groupFundCmpModal start -->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('pro.selectFundCategory') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				/>
			</div>
			<div class="modal-body">
				<!-- 第一組選項 -->
				<div class="row">
					<div v-for="(item, index) in fundcmpsMenu" :key="item.compCode" class="col-3">
						<div class="form-check">
							<input
								:id="item.compCode"
								v-model="fundcmpCode"
								class="form-check-input"
								name="fundcmpsCode"
								:value="item.compCode"
								type="checkbox"
							>
							<label class="form-check-label" style="font-size: 14px" :for="item.compCode">{{ item.compName }}</label>
						</div>
					</div>
				</div>

				<!-- 展開按鈕 -->
				<div
					v-if="fundcmpsOtherMenu.length > 0"
					class="card-header"
					data-bs-toggle="collapse"
					data-bs-target="#collapseListGroup"
				>
					<h4>{{ $t('pro.expand') }}</h4>
				</div>

				<!-- 第二組選項 -->
				<div v-if="fundcmpsOtherMenu.length > 0" id="collapseListGroup" class="collapse">
					<div class="modal-body">
						<div class="row">
							<div v-for="(item, index) in fundcmpsOtherMenu" :key="item.compCode" class="col-3">
								<div class="form-check">
									<input
										:id="item.compCode"
										v-model="fundcmpCode"
										class="form-check-input"
										name="fundcmpsOtherCode"
										:value="item.compCode"
										type="checkbox"
									>
									<label class="form-check-label" :for="item.compCode">{{ item.compName }}</label>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- 加入按鈕 -->
			<div class="modal-footer">
				<input
					id="modaladdButton"
					type="button"
					class="btn btn-primary"
					:value="$t('pro.add')"
					@click="addgroupFundCmpModal()"
				>
			</div>
		</div>
	</div>
	<!-- Modal group groupFundCmpModal End -->
</template>
<script>
export default {
	props: {
		fundcmpProp: Array, // 已選擇項目
		close: Function
	},
	data: function () {
		return {
			fundcmpsMenu: [], // 基金公司 選項
			fundcmpsOtherMenu: [], // 其餘交易所供選擇來源資料
			fundcmpCode: [], // 基金公司 選擇項目
			fundcmpItem: [] // 基金公司 選擇項目代碼與中文名稱
		};
	},
	watch: {},
	mounted: function () {
		const self = this;
		self.fundcmpItem = [];
	},
	methods: {
		// 基金公司來源資料
		groupfundcmpsMenu: async function (nation) {
			const self = this;
			const ret = await this.$api.getGroupFundCmpsMenuApi({
				localYn: nation,
				fuseYn: 'Y'
			});
			const englishNames = [];
			const numberNames = [];
			const chineseNames = [];

			ret.data.forEach((item) => {
				const name = item.compName.trim();
				const type = self.getStringType(name);

				// 判斷英文/數字/中文
				if (type === 'english') {
					englishNames.push(item);
				}
				else if (type === 'number') {
					numberNames.push(item);
				}
				else {
					chineseNames.push(item);
				}
			});

			// 合併結果
			self.fundcmpsMenu = [
				...numberNames.sort((a, b) => a.compName.localeCompare(b.compName, undefined, { numeric: true })),
				...englishNames.sort((a, b) => a.compName.localeCompare(b.compName)),
				...chineseNames.sort((a, b) => a.compName.localeCompare(b.compName, 'zh-Hant-u-co-stroke'))
			];
		},
		// 選擇基金類別來源資料
		groupfundcmpTypeMenu: async function (nation) {
			const self = this;
			const ret = await this.$api.getGlobalClassCodeMenuApi();
			const top10 = [];
			for (var i = 0; i < ret.data.length; i++) {
				ret.data[i].compCode = ret.data[i].globalClassCode;
				ret.data[i].compName = ret.data[i].name;
				if (i < 10) {
					top10.push(ret.data[i]);
				}
			}
			self.fundcmpsMenu = top10; // 取得基金前10筆基金類別

			const ret2 = await this.$api.getGlobalClassCodeOtherMenuApi();
			const other = [];
			for (var i = 0; i < ret2.data.length; i++) {
				ret2.data[i].compCode = ret2.data[i].globalClassCode;
				ret2.data[i].compName = ret2.data[i].name;
				other.push(ret2.data[i]);
			}
			self.fundcmpsOtherMenu = other; // 取得基金其餘基金類別
		},
		// 其餘交易所供選擇來源資料
		groupfundcmpsOtherMenu: async function (nation) {
			const self = this;
			const ret = await this.$api.getGroupFundCmpsMenuApi({
				localYn: nation,
				fuseYn: 'N'
			});
			const englishNames = [];
			const numberNames = [];
			const chineseNames = [];

			ret.data.forEach((item) => {
				const name = item.compName.trim();
				const type = self.getStringType(name);

				// 判斷英文/數字/中文
				if (type === 'english') {
					englishNames.push(item);
				}
				else if (type === 'number') {
					numberNames.push(item);
				}
				else {
					chineseNames.push(item);
				}
			});

			// 合併結果
			self.fundcmpsOtherMenu = [
				...numberNames.sort((a, b) => a.compName.localeCompare(b.compName, undefined, { numeric: true })),
				...englishNames.sort((a, b) => a.compName.localeCompare(b.compName)),
				...chineseNames.sort((a, b) => a.compName.localeCompare(b.compName, 'zh-Hant-u-co-stroke'))
			];
		},

		// 增加基金公司
		addgroupFundCmpModal() {
			const self = this;
			self.fundcmpItem = [];
			if (self.fundcmpCode.length === 0) {
				this.$bi.alert(this.$t('pro.pleaseSelectAtLeastOne'));
			}
			else {
				self.fundcmpCode.forEach((code) => {
					const item = _.find(self.fundcmpsMenu, { compCode: code });
					const itemOther = _.find(self.fundcmpsOtherMenu, { compCode: code });
					if (item) {
						self.fundcmpItem.push(item);
					}
					if (itemOther) {
						self.fundcmpItem.push(itemOther);
					}
					self.$emit('selected', self.fundcmpItem);
					self.close();
				});
			}
		},
		// 帶入已選值
		fundcmpPropItem(v) {
			const self = this;
			if (v.length > 0) {
				self.fundcmpItem = v;
				self.fundcmpCode = [];
				v.forEach((item) => {
					self.fundcmpCode.push(item.compCode);
				});
			}
			else {
				self.fundcmpItem = [];
				self.fundcmpCode = [];
			}
		},
		// 全形轉半形
		toHalfWidth: function (str) {
			return str
				.replace(/[\uff01-\uff5e]/g, function (char) {
					return String.fromCharCode(char.charCodeAt(0) - 0xfee0);
				})
				.replace(/\u3000/g, ' ');
		},
		getStringType: function (str) {
			const convertedStr = this.toHalfWidth(str);
			const firstChar = convertedStr.charAt(0);

			if (/[a-zA-Z]/.test(firstChar)) return 'english';
			if (/[0-9]/.test(firstChar)) return 'number';
			return 'chinese';
		}
	}
	// methods end
};
</script>
