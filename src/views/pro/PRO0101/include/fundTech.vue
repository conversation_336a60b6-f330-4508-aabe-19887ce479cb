<template>
	<div class="row m-t-10">
		<div class="col-3">
			<select v-model="selectedTech" class="select">
				<option value="0">
					{{ $t('pro.cumulativeReturn') }}
				</option>
				<option value="1">
					{{ $t('pro.sharpe') }}
				</option>
				<option value="2">
					{{ $t('pro.alpha') }}
				</option>
				<option value="3">
					{{ $t('pro.beta') }}
				</option>
				<option value="4">
					{{ $t('pro.standardDeviation') }}
				</option>
			</select>
		</div>
		<div class="col-12">
			<vue-fund-column-chart
				:chart-id="techsChartId"
				:prop-chart-data="techsChartData"
				:prop-selected-tech="techsMenu[selectedTech]"
			/>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
import vueFundColumnChart from './fundColumnChart.vue';
export default {
	components: {
		vueFundColumnChart
	},
	props: {
		techs: Array
	},
	data: function () {
		return {
			selectedTech: '0',
			techsMenu: [
				{
					name: this.$t('pro.cumulativeReturn'),
					valueList: [{ name: this.$t('pro.cumulativeReturn'), value: 'pct' }],
					tooltipText: '{categoryX}:{valueY}'
				},
				{
					name: this.$t('pro.sharpe'),
					valueList: [{ name: this.$t('pro.sharpe'), value: 'shp' }],
					tooltipText: '{categoryX}:{valueY}'
				},
				{
					name: this.$t('pro.alpha'),
					valueList: [{ name: this.$t('pro.alpha'), value: 'alp' }],
					tooltipText: '{categoryX}:{valueY}'
				},
				{
					name: this.$t('pro.beta'),
					valueList: [{ name: this.$t('pro.beta'), value: 'bet' }],
					tooltipText: '{categoryX}:{valueY}'
				},
				{
					name: this.$t('pro.standardDeviation'),
					valueList: [{ name: this.$t('pro.standardDeviation'), value: 'std' }],
					tooltipText: '{categoryX}:{valueY}'
				}
			],
			techsChartId: 'techsChartId'
		};
	},
	computed: {
		techsChartData: function () {
			return [
				{
					year: this.$t('pro.sixMonths'),
					pct: this.getTech('PCT6M'),
					shp: this.getTech('SHP6M'),
					alp: this.getTech('ALP6M'),
					bet: this.getTech('BET6M'),
					std: this.getTech('STD6M')
				},
				{
					year: this.$t('pro.oneYear'),
					pct: this.getTech('PCT1Y'),
					shp: this.getTech('SHP1Y'),
					alp: this.getTech('ALP1Y'),
					bet: this.getTech('BET1Y'),
					std: this.getTech('STD1Y')
				},
				{
					year: this.$t('pro.threeYears'),
					pct: this.getTech('PCT3Y'),
					shp: this.getTech('SHP3Y'),
					alp: this.getTech('ALP3Y'),
					bet: this.getTech('BET3Y'),
					std: this.getTech('STD3Y')
				},
				{
					year: this.$t('pro.fiveYears'),
					pct: this.getTech('PCT5Y'),
					shp: this.getTech('SHP5Y'),
					alp: this.getTech('ALP5Y'),
					bet: this.getTech('BET5Y'),
					std: this.getTech('STD5Y')
				}
			];
		}
	},
	watch: {},
	mounted: function () {},
	methods: {
		getTech: function (statCode) {
			const tech = _.find(this.techs, { statCode: statCode });
			return tech ? tech.dvalue : null;
		}
	}
};
</script>
