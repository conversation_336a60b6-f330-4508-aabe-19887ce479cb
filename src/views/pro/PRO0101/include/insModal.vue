<template>
	<!-- Modal 2 保險-->
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('pro.insurance') }}
				</h4>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click.prevent="close()"
				/>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-insurance" />
							<h4>
								<span>{{ $t('pro.name') }}</span> <br>{{ $filters.defaultValue(proInfo.proName, '--') }} <br><span class="tx-black">{{
									$filters.defaultValue(proInfo.proEName, '--')
								}}</span>
							</h4>
						</div>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6>
								<span>{{ $t('pro.code') }}</span>
								<br>{{ $filters.defaultValue(proInfo.bankProCode, '--') }}
							</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.assetCategory') }}</span><br>{{ $filters.defaultValue(proInfo.assetcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productMainCategory') }}</span><br>{{ $filters.defaultValue(proInfo.pfcatName, '--') }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>{{ $t('pro.productSubCategory') }}</span><br>{{ $filters.defaultValue(proInfo.proTypeName, '--') }}</h6>
						</div>
					</div>
				</div>

				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item">
							<a class="nav-link active" href="#Sectionins1" data-bs-toggle="pill">{{ $t('pro.productBasicInfo') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#Sectionins2" data-bs-toggle="pill">{{ $t('pro.商品共同資料') }}</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#Sectionins3" data-bs-toggle="pill">{{ $t('pro.商品附加資料') }}</a>
						</li>
						<!--                <li class="nav-item"><a class="nav-link" href="#Sectionins4" data-bs-toggle="pill">{{ $t('pro.investment') }}</a></li>-->
					</ul>
					<div class="tab-content">
						<div id="Sectionins1" class="tab-pane fade show active">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.insurance') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.code') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.bankProCode, '--') }}
											</td>
											<th>{{ $t('pro.insurance') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.insInfo.insType, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.insurance') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.insInfo.inscmpName, '--') }}
											</td>
											<th>{{ $t('pro.主附約') }}</th>
											<td class="wd-30p">
												{{ $t('pro.filtersdef') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.currency') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.curCode, '--') }}
											</td>
											<th>{{ $t('pro.productRiskLevel') }}</th>
											<td class="wd-30p">
												<span v-if="proInfo.riskCode === '1'">RR1</span>
												<span v-else-if="proInfo.riskCode === '2'">RR2</span>
												<span v-else-if="proInfo.riskCode === '3'">RR3</span>
												<span v-else-if="proInfo.riskCode === '4'">RR4</span>
												<span v-else-if="proInfo.riskCode === '5'">RR5</span>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.type') }}</th>
											<td>{{ $filters.defaultValue(proInfo.insInfo.payTermName, '--') }}</td>
											<th>{{ $t('pro.type') }}</th>
											<td>{{ $filters.formatNumber(proInfo.insInfo.payTerm, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.type') }}</th>
											<td>{{ $filters.defaultValue(proInfo.insInfo.insTermName, '--') }}</td>
											<th>{{ $t('pro.type') }}</th>
											<td>{{ $filters.formatNumber(proInfo.insInfo.insTerm, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.承保年齡起') }}</th>
											<td>{{ $filters.formatNumber(proInfo.insInfo.ageMin, '--') }}</td>
											<th>{{ $t('pro.承保年齡迄') }}</th>
											<td>{{ $filters.formatNumber(proInfo.insInfo.ageMax, '--') }}</td>
										</tr>
										<tr>
											<th>{{ $t('pro.insurance') }}</th>
											<td class="wd-30p">
												{{ $filters.formatNumber(proInfo.insInfo.insTerm, '--') }}年
											</td>
											<th>{{ $t('pro.商品期間') }}</th>
											<td class="wd-30p">
												{{ $filters.formatNumber(proInfo.insInfo.payTerm, '--') }}年
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.銷售起始日') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.insInfo.stdDt, '--') }}
											</td>
											<th>{{ $t('pro.銷售結束日') }}</th>
											<td class="wd-30p">
												{{ $filters.defaultValue(proInfo.insInfo.endDt, '--') }}
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.商品屬性') }}</th>
											<td class="wd-30p">
												<span v-if="proInfo.protypeCode === 'I01'">{{ $t('pro.儲蓄') }}</span>
												<span v-else-if="proInfo.protypeCode === 'I02'">{{ $t('pro.保障') }}</span>
												<span v-else-if="proInfo.protypeCode === 'I03'">{{ $t('pro.portfolio') }}</span>
												<span v-else-if="proInfo.protypeCode === 'I04'">{{ $t('pro.房貸') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div id="Sectionins2" class="tab-pane fade">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.銷售相關資料') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th width="20%">
												{{ $t('pro.銷售地區') }}
											</th>
											<td v-if="proInfo.allYn == 'Y'" width="30%">
												{{ $t('pro.全行') }}
											</td>
											<td v-else width="30%">
												--
											</td>
											<th width="20%">
												{{ $t('pro.限PI申購') }}
											</th>
											<td width="30%">
												{{ $filters.defaultValue(proInfo.profInvestorYn, '--') }}
											</td>
										</tr>
										<tr>
											<th width="20%">
												{{ $t('pro.salesTarget') }}
											</th>
											<td width="30%" colspan="3">
												{{ $filters.defaultValue(proInfo.targetCusBu, '--') }}
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.理財需求') }}</span></th>
											<td colspan="3">
												<div v-for="item in finReqCodeMenu" class="form-check form-check-inline">
													<input
														id="c1"
														v-model="finReqCodes"
														class="form-check-input"
														name="finReqCodes"
														disabled
														:value="item.codeValue"
														type="checkbox"
													>
													<label class="form-check-label" for="c1">{{ $filters.defaultValue(item.codeName, '--') }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.商品標籤') }}</th>
											<td disabled="disabled" colspan="3">
												{{ $filters.defaultValue(proInfo.selprocatNames, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div id="Sectionins3" class="tab-pane fade">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.其他設定') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>{{ $t('pro.investment') }}</span></th>
											<td class="wd-80p">
												<span>{{ $filters.defaultValue(proInfo.sectorCode, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.investment') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.geoFocusCode, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.比較基準設定') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.benchmarkCode, '--') }}</span>
											</td>
										</tr>
										<tr>
											<th><span>{{ $t('pro.商品簡介') }}</span></th>
											<td>
												<span>{{ $filters.defaultValue(proInfo.memo, '--') }}</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>{{ $t('pro.相關附件') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>{{ $t('pro.保單條款') }}</th>
											<td class="wd-80p">
												<a v-if="proFileC && proFileC.url" :href="proFileC.url" target="_blank">{{
													$filters.defaultValue(proFileC.url, '--')
												}}</a><br v-if="proFileC && proFileC.url">
												<a
													v-if="proFileC"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileC)"
												>{{
													$filters.defaultValue(proFileC.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<a v-if="proFileF && proFileF.url" :href="proFileF.url" target="_blank">{{
													$filters.defaultValue(proFileF.url, '--')
												}}</a><br v-if="proFileF && proFileF.url">
												<a
													v-if="proFileF"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileF)"
												>{{
													$filters.defaultValue(proFileF.showName, '--')
												}}</a>
											</td>
										</tr>
										<tr>
											<th>{{ $t('pro.其他') }}</th>
											<td class="wd-80p">
												<a v-if="proFileG && proFileG.url" :href="proFileG.url" target="_blank">{{
													$filters.defaultValue(proFileG.url, '--')
												}}</a><br v-if="proFileG && proFileG.url">
												<a
													v-if="proFileG"
													class="tx-link"
													href="#"
													@click="downloadFile(proFileG)"
												>{{
													$filters.defaultValue(proFileG.showName, '--')
												}}</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.其他相關附件') }}</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<td>
												<span v-for="(item, index) in otherFileList">
													<a
														v-if="index === otherFileList.length - 1"
														v-show="item.show"
														href="#"
														class="tx-link"
														@click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}</a>
													<a
														v-else
														v-show="item.show"
														href="#"
														class="tx-link"
														@click="downloadOtherFile(item.docFileId)"
													>{{ $filters.defaultValue(item.showName, '--') }}、</a>
												</span>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<div class="tx-note">
								{{ $t('pro.upload') }}
							</div>
						</div>

						<div id="Sectionins4" class="tab-pane fade">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>{{ $t('pro.investment') }}</h4>
								</div>
								<table id="invTrgTbl" class="table table-RWD table-bordered">
									<thead>
										<tr>
											<th width="15%">
												Lipper Code
											</th>
											<th width="15%">
												{{ $t('pro.code') }}
											</th>
											<th width="15%">
												{{ $t('pro.insurance') }}
											</th>
											<th width="35%">
												{{ $t('pro.name') }}
											</th>
											<th width="10%">
												{{ $t('pro.fund') }}
											</th>
											<th width="10%">
												{{ $t('pro.productRiskAttribute') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in insInvTargetList">
											<td data-th="Lipper Code">
												{{ $filters.defaultValue(item.lipperId, '--') }}
											</td>
											<td ::data-th="$t('pro.code')">
												{{ $filters.defaultValue(item.invtgtCode, '--') }}
											</td>
											<td ::data-th="$t('pro.insurance')">
												{{ $filters.defaultValue(item.inscmpProCode, '--') }}
											</td>
											<td ::data-th="$t('pro.name')">
												{{ $filters.defaultValue(item.invTgtName, '--') }}
											</td>
											<td ::data-th="$t('pro.fund')">
												<span v-if="item.localYn == 'Y'">{{ $t('pro.domestic') }}</span>
												<span v-else>{{ $t('pro.overseas') }}</span>
											</td>
											<td ::data-th="$t('pro.productRiskAttribute')">
												{{ $filters.defaultValue(item.riskName, '--') }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-white" @click.prevent="close()">
					{{ $t('pro.close') }}
				</button>
			</div>
		</div>
	</div>
	<!-- Modal 2 End -->
</template>
<script>

export default {
	props: {
		downloadFile: Function,
		downloadOtherFile: Function,
		finReqCodeMenu: Array,
		close: Function
	},
	data: function () {
		return {
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				bondInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},
			finReqCodes: [],
			proFileC: {},
			proFileF: {},
			proFileG: {},
			otherFileList: [],
			insInvTargetList: []
		};
	},
	watch: {},
	mounted: function () {},
	methods: {
		getProInfo: function (bankProCode, pfcatCode) {
			const self = this;
			const url = self.config.apiPath + '/pro/productInfo';
			$.bi
				.ajax({
					url: url,
					method: 'GET',
					data: {
						proCode: bankProCode,
						pfcatCode: pfcatCode
					}
				})
				.then(function (ret) {
					if (_.isNil(ret.data)) {
						ret.data = {};
						$.bi.alert('資料不存在');
						return;
					}
					if (_.isNil(ret.data.insInfo)) {
						ret.data.insInfo = {};
					}

					self.proInfo = ret.data;

					let selectYnList = [];
					$.bi
						.ajax({
							url: self.config.apiPath + '/adm/codeDetail',
							method: 'GET',
							data: {
								codeType: 'SELECT_YN'
							}
						})
						.then(function (ret) {
							selectYnList = ret.data;

							if (!_.isEmpty(selectYnList)) {
								if (!_.isUndefined(self.proInfo.profInvestorYn)) {
									const profInvestorYnObjs = _.filter(selectYnList, {
										codeValue: self.proInfo.profInvestorYn
									});
									self.proInfo.profInvestorYn = profInvestorYnObjs[0].codeName;
								}
							}
						});

					if (!_.isUndefined(self.proInfo.targetCusBu)) {
						let targetCusBuList = [];
						$.bi
							.ajax({
								url: self.config.apiPath + '/adm/codeDetail',
								method: 'GET',
								data: {
									codeType: 'CUS_BU'
								}
							})
							.then(function (ret) {
								targetCusBuList = ret.data;
								const targetCusBuObjs = _.filter(targetCusBuList, {
									codeValue: self.proInfo.targetCusBu
								});
								self.proInfo.targetCusBu = targetCusBuObjs[0].codeName;
							});
					}

					if (!_.isUndefined(self.proInfo.selprocatNames)) {
						const selprocatNames = self.proInfo.selprocatNames.replaceAll(',', '、');
						self.proInfo.selprocatNames = selprocatNames;
					}

					if (!_.isUndefined(self.proInfo.finReqCode)) {
						self.finReqCodes = self.proInfo.finReqCode.split(',');
					}
				});

			// 商品附加資料
			$.bi
				.ajax({
					url: self.config.apiPath + '/pro/productsCommInfo',
					method: 'GET',
					data: {
						proCode: bankProCode,
						pfcatCode: pfcatCode
					}
				})
				.then(function (ret) {
					if (!_.isNil(ret.data)) {
						if (ret.data.proDocs) {
							self.otherFileList = ret.data.proDocs; // 其他相關附件
							self.otherFileList.forEach(function (item) {
								// 其他相關附件 檔案顯示時間範圍
								item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
							});
						}

						const proFileList = ret.data.proFiles;
						if (!_.isNil(proFileList)) {
							self.proFileC = proFileList.filter(proFile => proFile.fileType === 'C')[0];
							self.proFileF = proFileList.filter(proFile => proFile.fileType === 'F')[0];
							self.proFileG = proFileList.filter(proFile => proFile.fileType === 'G')[0];
						}
					}
				});
		}
	} // methods end
};

</script>
