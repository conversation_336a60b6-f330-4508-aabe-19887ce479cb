<template>
	<div>
		<div class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
				<h4>{{ $t('pro.searchCond') }}</h4>
				<span class="tx-square-bracket">{{ $t('pro.requiredFields') }}</span>
			</div>

			<div id="collapseListGroup1" class="collapse show">
				<div class="card-body">
					<vue-form v-slot="{ errors }" ref="queryForm">
						<div class="form-row">
							<div class="form-group col-12 col-lg-4">
								<label class="form-label">{{ $t('pro.productMainCategory') }}</label>
								<select v-model="pfcatCode" class="form-select" name="pfcatCode">
									<option value="">
										{{ $t('pro.all') }}
									</option>
									<option v-for="item in proPfcatsList" :value="item.pfcatCode">
										{{ item.pfcatName }}
									</option>
								</select>
							</div>
							<div class="form-group col-12 col-lg-4">
								<label class="form-label">{{ $t('pro.featuredProductCategory') }}</label>
								<select v-model="selprocatCode" class="form-select" name="selprocatCode">
									<option value="">
										{{ $t('pro.all') }}
									</option>
									<option v-for="item in selectProSortList" :value="item.codeValue">
										{{ item.codeName }}
									</option>
								</select>
							</div>
							<div class="form-group col-12 col-lg-4">
								<label class="form-label">{{ $t('pro.listingDate') }}</label>
								<div class="input-group input-date">
									<vue-field
										v-model="startDate"
										type="date"
										name="startDate"
										class="JQ-datepicker form-control form-input has-validation"
										:label="$t('pro.listingDate') + '(起)'"
									/>
									<span class="input-group-text">~</span>
									<vue-field
										v-model="endDate"
										type="date"
										name="endDate"
										class="JQ-datepicker form-control form-input has-validation"
										:label="$t('pro.listingDate') + '(迄)'"
									/>
								</div>
							</div>
						</div>

						<div class="form-footer">
							<input
								class="btn btn-primary btn-search"
								type="button"
								:value="$t('pro.search')"
								@click="search(0)"
							>
						</div>
					</vue-form>
				</div>
			</div>
		</div>

		<div v-if="searchList.content?.length > 0" id="searchResult">
			<div class="card card-table">
				<div class="card-header">
					<h4>{{ $t('pro.searchResult') }}</h4>
					<vue-pagination :pageable="searchList" :goto-page="gotoPage" />
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-hover">
						<thead>
							<tr>
								<th>{{ $t('pro.productMainCategory') }}</th>
								<th>{{ $t('pro.featuredProductCategory') }}</th>
								<th>{{ $t('pro.featuredProductPackageName') }}</th>
								<th>{{ $t('pro.listingDate') }}(起)</th>
								<th>{{ $t('pro.listingDate') }}(迄)</th>
								<th class="text-center">
									{{ $t('pro.execute') }}
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="item in searchList.content">
								<td>{{ item.pfcatName }}</td>
								<td>{{ item.selprocatName }}</td>
								<td>{{ item.selproName }}</td>
								<td>{{ item.startDate }}</td>
								<td>{{ item.endDate }}</td>
								<td class="text-center">
									<a
										data-bs-toggle="tooltip"
										href="#"
										class="table-icon"
										:title="$t('pro.view')"
										@click="preview(item)"
									>
										<button type="button" class="btn btn-dark btn-icon"><i class="bi bi-search" /></button>
									</a>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<!--頁面內容 end-->

		<!-- modal -->
		<vue-modal :is-open="isViewOpenModal" @close="isViewOpenModal = false">
			<template #content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								{{ $t('pro.featuredRecommendedProducts') }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="props.close()"
							/>
						</div>
						<div class="modal-body">
							<div class="tx-title">
								{{ $t('pro.featuredProductTypes') }}
							</div>
							<table class="table table-RWD table-bordered table-horizontal-RWD">
								<tbody>
									<tr>
										<th class="wd-20p">
											{{ $t('pro.productMainCategory') }}
										</th>
										<td class="wd-80p">
											{{ previewItem.pfcatName }}
										</td>
									</tr>
									<tr>
										<th>{{ $t('pro.featuredProductCategory') }}</th>
										<td>{{ previewItem.selprocatName }}</td>
									</tr>
									<tr>
										<th>{{ $t('pro.featuredProductPackageName') }}</th>
										<td>{{ previewItem.selproName }}</td>
									</tr>
									<tr>
										<th>{{ $t('pro.listingDate') }}</th>
										<td>{{ previewItem.startDate }}~ {{ previewItem.endDate }}</td>
									</tr>
								</tbody>
							</table>

							<div class="tx-title">
								{{ $t('pro.productList') }}
							</div>
							<div class="table-responsive">
								<table class="table table-RWD table-hover table-bordered">
									<thead>
										<tr>
											<th>{{ $t('pro.proCode') }}</th>
											<th>{{ $t('pro.proName') }}</th>
											<th>{{ $t('pro.issuer') }}</th>
											<th>{{ $t('pro.productRiskLevel') }}</th>
											<th>{{ $t('pro.productMainCategory') }}</th>
											<th>{{ $t('pro.productSubCategory') }}</th>
											<th>{{ $t('pro.nonMainLevel') }}</th>
											<th>{{ $t('pro.execute') }}</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in proList">
											<td>{{ item.bankProCode }}</td>
											<td>
												<span>
													<a
														v-if="item.pfcatCode === 'FUND'"
														class="tx-link"
														href="#"
														@click="fundModalHandler(item.proCode, item.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a>
													<a
														v-else-if="item.pfcatCode === 'ETF'"
														class="tx-link"
														href="#"
														@click="etfModalHandler(item.proCode, item.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a>
													<a
														v-else-if="item.pfcatCode === 'FB'"
														class="tx-link"
														href="#"
														@click="bondModalHandler(item.proCode, item.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a>
													<a
														v-else-if="item.pfcatCode === 'SP'"
														class="tx-link"
														href="#"
														@click="spModalHandler(item.proCode, item.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a>
													<a
														v-else-if="item.pfcatCode === 'INS'"
														class="tx-link"
														href="#"
														@click="insModalHandler(item.proCode, item.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a>
													<a
														v-else-if="item.pfcatCode === 'DCI'"
														class="tx-link"
														href="#"
														@click="dciModalHandler(item.proCode, item.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a>
													<a
														v-else-if="item.pfcatCode === 'SEC'"
														class="tx-link"
														href="#"
														@click="secModalHandler(item.proCode, item.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a>
													<a
														v-else-if="item.pfcatCode === 'PFD'"
														class="tx-link"
														href="#"
														@click="pfdModalHandler(item.proCode, item.pfcatCode)"
													>{{ $filters.defaultValue(item.proName, '--') }}</a>
													<span v-else>{{ $filters.defaultValue(item.proName, '--') }}</span>
												</span>
											</td>

											<td>{{ item.issuerName }}</td>
											<td>{{ item.riskName }}</td>
											<td>{{ item.pfcatName }}</td>
											<td>{{ item.proTypeName }}</td>
											<td class="text-center">
												<input
													class="form-check-input"
													type="checkbox"
													:checked="item.mainLevelYn == 'Y'"
													disabled
												>
											</td>
											<td>
												<button
													type="button"
													class="btn btn-dark btn-icon"
													data-bs-toggle="tooltip"
													:title="$t('pro.watch')"
													@click="favoritesHandler(item.proCode, item.pfcatCode)"
												>
													<i class="bi bi-heart" />
												</button>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="appointmentFooter" class="modal-footer">
							<input
								id="appointmentCloseButton"
								name="btnClose"
								class="btn btn-white"
								type="button"
								:value="$t('pro.close')"
								@click.prevent="props.close()"
							>
						</div>

						<vue-modal :is-open="isOpenModal['fund']" @close="closeModal('fund')">
							<template #content="props">
								<vue-fund-modal
									ref="fundModalRef"
									:is-open-fund-modal="isOpenModal['fund']"
									:fin-req-code-menu="finReqCodeMenu"
									:download-file="downloadFile"
									:download-other-file="downloadOtherFile"
									:close="props.close"
								/>
							</template>
						</vue-modal>
						<vue-modal :is-open="isOpenModal['etf']" @close="closeModal('etf')">
							<template #content="props">
								<vue-etf-modal
									ref="etfModalRef"
									:is-open-etf-modal="isOpenModal['etf']"
									:fin-req-code-menu="finReqCodeMenu"
									:pro-price-range-menu="proPriceRangeMenu"
									:download-file="downloadFile"
									:download-other-file="downloadOtherFile"
									:close="props.close"
								/>
							</template>
						</vue-modal>
						<vue-modal :is-open="isOpenModal['bond']" @close="closeModal('bond')">
							<template #content="props">
								<vue-bond-modal
									ref="bondModalRef"
									:is-open-bond-modal="isOpenModal['bond']"
									:fin-req-code-menu="finReqCodeMenu"
									:pro-price-range-menu="proPriceRangeMenu"
									:download-file="downloadFile"
									:download-other-file="downloadOtherFile"
									:close="props.close"
								/>
							</template>
						</vue-modal>
						<vue-modal :is-open="isOpenModal['pfd']" @close="closeModal('pfd')">
							<template #content="props">
								<vue-pfd-modal
									ref="pfdModalRef"
									:is-open-pfd-modal="isOpenModal['pfd']"
									:fin-req-code-menu="finReqCodeMenu"
									:close="props.close"
								/>
							</template>
						</vue-modal>
						<vue-modal :is-open="isOpenModal['sp']" @close="closeModal('sp')">
							<template #content="props">
								<vue-sp-modal
									ref="spModalRef"
									:is-open-structured-product-modal="isOpenModal['sp']"
									:fin-req-code-menu="finReqCodeMenu"
									:pro-price-range-menu="proPriceRangeMenu"
									:download-file="downloadFile"
									:download-other-file="downloadOtherFile"
									:close="props.close"
								/>
							</template>
						</vue-modal>
						<vue-modal :is-open="isOpenModal['ins']" @close="closeModal('ins')">
							<template #content="props">
								<vue-ins-modal
									ref="insModalRef"
									:is-open-ins-modal="isOpenModal['ins']"
									:fin-req-code-menu="finReqCodeMenu"
									:download-file="downloadFile"
									:download-other-file="downloadOtherFile"
									:close="props.close"
								/>
							</template>
						</vue-modal>
						<vue-modal :is-open="isOpenModal['dci']" @close="closeModal('dci')">
							<template #content="props">
								<vue-dci-modal
									ref="dciModalRef"
									:is-open-dci-modal="isOpenModal['dci']"
									:fin-req-code-menu="finReqCodeMenu"
									:pro-price-range-menu="proPriceRangeMenu"
									:download-file="downloadFile"
									:download-other-file="downloadOtherFile"
									:close="props.close"
								/>
							</template>
						</vue-modal>
						<vue-modal :is-open="isOpenModal['sec']" @close="closeModal('sec')">
							<template #content="props">
								<vue-sec-modal
									ref="secModalRef"
									:is-open-sec-modal="isOpenModal['sec']"
									:fin-req-code-menu="finReqCodeMenu"
									:pro-price-range-menu="proPriceRangeMenu"
									:download-file="downloadFile"
									:download-other-file="downloadOtherFile"
									:close="props.close"
								/>
							</template>
						</vue-modal>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- modal end -->
	</div>
</template>
<script>
import { Field, Form } from 'vee-validate';
import VuePagination from '@/views/components/pagination.vue';
import vueModal from '@/views/components/model.vue';
import vueFundModal from '@/views/pro/PRO0101/include/fundModal.vue';
import vueEtfModal from '@/views/pro/PRO0101/include/etfModal.vue';
import vueBondModal from '@/views/pro/PRO0101/include/bondModal.vue';
import vuePfdModal from '@/views/pro/PRO0101/include/pfdModal.vue';
import vueSpModal from '@/views/pro/PRO0101/include/spModal.vue';
import vueInsModal from '@/views/pro/PRO0101/include/insModal.vue';
import vueDciModal from '@/views/pro/PRO0101/include/dciModal.vue';
import vueSecModal from '@/views/pro/PRO0101/include/secModal.vue';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		VuePagination,
		vueModal,
		vueFundModal,
		vueEtfModal,
		vueBondModal,
		vuePfdModal,
		vueSpModal,
		vueInsModal,
		vueDciModal,
		vueSecModal
	},
	data: function () {
		return {
			proPriceRangeMenu: undefined,
			downloadOtherFile: () => void 0,
			downloadFile: () => void 0,
			finReqCodeMenu: undefined,
			proPfcatsList: [], // 下拉 商品主類 列表// 商品主類
			selectProSortList: [], // 下拉 精選商品類別 列表
			pfcatCode: '', // 下拉 商品主類
			selprocatCode: '03', // 精選商品類別
			startDate: null, // 上架日期-起
			endDate: null, // 上架日期-迄
			searchList: [], // 查詢結果
			previewItem: {}, // 檢視項目
			proList: [], // 檢視商品項目,
			isViewOpenModal: false,
			pageable: {
				page: 0,
				size: 10,
				sort: 'CREATE_DT',
				direction: 'ASC'
			},
			isOpenModal: {
				fund: false,
				etf: false,
				bond: false,
				pfd: false,
				sp: false,
				ins: false,
				dci: false,
				sec: false
			}
		};
	},
	watch: {},
	mounted: function () {
		const self = this;
		self.getProPfcatsMenu(); // 精選商品主類選單
		self.getSelectProPfcatsMenu(); // 精選商品類別選單
	},
	methods: {
		// 取得商品主類
		getProPfcatsMenu: async function () {
			const self = this;
			const ret = await self.$api.getSelectProPfcatsMenuApi();
			self.proPfcatsList = ret.data;
		},
		getSelectProPfcatsMenu: async function () {
			const self = this;
			const r = await self.$api.getSelProPfcatsMenuApi();
			self.selectProSortList = r.data;
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.search(page);
		},
		search: async function (page) {
			const self = this;
			self.$refs.queryForm.validate().then(async function (pass) {
				if (pass.valid) {
					if (self.startDate > self.endDate) {
						self.$bi.alert(this.$t('pro.startDateCannotBeGreaterThanEndDate'));
						return;
					}
					const r = await self.$api.getPrdSearchSelected({
						pfcatCode: self.pfcatCode,
						selprocatCode: self.selprocatCode,
						startDate: self.startDate,
						endDate: self.endDate
					});
					if (r.data.content.length > 0) {
						self.searchList = r.data;
					}
					else {
						self.searchList = [];
						self.$bi.alert(this.$t('pro.noDataFound'));
					}
				}
			});
		},
		async preview(item) {
			const self = this;
			self.previewItem = item;
			const r = await self.$api.getPrdSearchSelectedDetail({
				selproId: item.selproId
			});
			self.proList = r.data.proSelectedMaps;
			self.isViewOpenModal = true;
		},
		favoritesHandler: async function (proCode, pfcatCode) {
			const self = this;
			const url = self.config.apiPath + '/pro/proFavorites';
			const ret = await self.$api.addFavoriteApi({
				proCode: proCode,
				pfcatCode: pfcatCode
			});
			this.$swal.fire({
				icon: 'success',
				text: this.$t('pro.addedToFavorites'),
				showCloseButton: true,
				confirmButtonText: this.$t('pro.confirm'),
				buttonsStyling: false,
				customClass: {
					confirmButton: 'btn btn-success'
				}
			});
		},
		closeModal: function (modalName) {
			const self = this;
			self.isOpenModal[modalName] = false;
		},
		fundModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.fundModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal.fund = true;
		},
		etfModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.etfModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.etfModalRef.getEtfDetail(proCode); // 商品資訊/基金資料
			self.$refs.etfModalRef.getEtfStockHold(proCode); // 商品資訊/ETF持股
			self.$refs.etfModalRef.getEtfPrice(proCode); // 商品資訊/價格分析資料
			self.$refs.etfModalRef.getPricesChartData(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.etfModalRef.getEtfProfileNameMenu(); // 商品資訊/績效分析
			self.$refs.etfModalRef.getEtfProfileBenchmarksMenu(); // 商品資訊/績效分析
			self.isOpenModal.etf = true;
		},
		bondModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.bondModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.bondModalRef.getBondPriceAna(proCode);
			self.$refs.bondModalRef.getPricesChartData(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.bondModalRef.getBondPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.bond = true;
		},
		pfdModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.pfdModalRef.getProInfo(proCode, pfcatCode);
			//			self.$refs.pfdModalRef.getEtfDetail(proCode); // 商品資訊/基金資料
			self.isOpenModal.pfd = true;
		},
		spModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.spModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.spModalRef.getSpPriceAna(proCode); // 商品資訊/淨值分析資料
			self.$refs.spModalRef.getSpNets(proCode, 'Y', -1.0); // 商品資訊/淨值分析圖表
			self.$refs.spModalRef.getSpPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.sp = true;
		},
		insModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.insModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal.ins = true;
		},
		dciModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.dciModalRef.getProInfo(proCode, pfcatCode); // 商品基本資料
			self.$refs.dciModalRef.getDciPriceAna(proCode); // 商品資訊/價格分析資料
			self.$refs.dciModalRef.getDciNets(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.dciModalRef.getDciPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.dci = true;
		},
		secModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.secModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.secModalRef.getSecPriceAna(proCode); // 商品資訊/淨值分析資料
			self.$refs.secModalRef.getSecNets(proCode, 'Y', -1.0); // 商品資訊/淨值分析圖表
			self.$refs.secModalRef.getSecPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.sec = true;
		}
	}
};
</script>
