<template>
	<div class="row">
		<div class="col-12">
			<div class="card card-form">
				<div class="card-header">
					<h4>查詢條件</h4>
					<span class="tx-square-bracket">為必填欄位</span>
				</div>
				<div class="card-body">
					<div class="row g-3 align-items-end">
						<div class="col-lg-3">
							<label class="form-label tx-require">商品主類</label>
							<select
								id="pfcatCode"
								v-model="pfcatCode"
								class="form-select"
								name="pfcatCode"
								rules="required"
								label="商品主類"
							>
								<option value="">
									全部
								</option>
								<option v-for="item in pfcatMenu" :value="item.pfcatCode">
									{{ item.pfcatName }}
								</option>
							</select>
						</div>

						<div class="col-lg-3">
							<label class="form-label">商品代號/險種代碼</label>
							<input
								id="prod_bank_pro_code"
								v-model="bankProCode"
								class="form-control"
								maxlength="20"
								name="bank_pro_code"
								size="25"
								type="text"
							>
						</div>

						<div class="col-lg-3">
							<label class="form-label">商品名稱</label>
							<input
								id="prod_pro_name"
								v-model="proName"
								class="form-control"
								maxlength="20"
								name="pro_name"
								size="45"
								type="text"
							>
						</div>

						<div class="col-lg-3">
							<label class="form-label">風險等級</label>
							<select
								id="riskCode"
								v-model="riskCode"
								class="form-select"
								name="risk_code"
							>
								<option value="">
									全部
								</option>
								<option v-for="risk in riskMenu" :value="risk.riskCode">
									{{ risk.riskName }}
								</option>
							</select>
						</div>
						<div class="col-lg-3">
							<label class="form-label">計價幣別</label><br>
							<select
								id="curId"
								ref="curId"
								v-model="curObjs"
								class="selectpicker form-control"
								size="13"
								multiple
								title="請選擇幣別"
								data-style="btn-white"
							>
								<option value="">
									全部
								</option>
								<option v-for="(item, index) in curOption" :key="index" :value="item.value">
									{{ item.name }}
								</option>
							</select>
						</div>

						<div class="col-lg-3">
							<label class="form-label">配息頻率</label>
							<select
								id="intFreqUnittype"
								v-model="intFreqUnitType"
								class="form-select"
								name="int_freq_unittype"
							>
								<option value="">
									全部
								</option>
								<option v-for="intFreqUnit in intFreqUnitTypeMenu" :value="intFreqUnit.codeValue">
									{{ intFreqUnit.codeName }}
								</option>
							</select>
						</div>
						<div class="col-lg-3">
							<label class="form-label">到期保本</label>
							<div v-for="item in principalGuarMenu" class="form-check form-check-inline">
								<input
									:id="'principalGuar' + item.codeValue"
									v-model="principalGuarYn"
									class="form-check-input"
									type="radio"
									:value="item.codeValue"
									name="principalGuarYn"
								>
								<label class="form-check-label" :for="'principalGuar' + item.codeValue">{{
									$filters.defaultValue(item.codeName, '--')
								}}</label>
							</div>
						</div>

						<div class="col-lg-2">
							<button class="btn btn-primary btn-glow btn-search" @click.prevent="gotoPage(0)">
								查詢
							</button>
						</div>
					</div>
				</div>
			</div>
			<div v-if="false" class="tx-note">
				未開放查詢之商品為保險、黃金存摺、財金附條件交易及證券台股
			</div>

			<div v-if="pageData.content.length > 0" id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>查詢結果</h4>
						<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-bordered text-center">
							<thead>
								<tr>
									<th class="text-start">
										商品主類
									</th>
									<th>
										商品代號/險種代碼
										<a
											v-if="isShowIsinCode(pfcatCode)"
											href="#"
											class="icon icon-sort"
											@click="sort('isinCode')"
										/>
										<a
											v-else
											href="#"
											class="icon icon-sort"
											@click="sort('bankProCode')"
										/>
									</th>
									<th class="text-start" width="30%">
										商品中文名稱<a href="#" class="icon icon-sort" @click="sort('proName')" />
									</th>
									<th>風險等級<a href="#" class="icon icon-sort" @click="sort('riskName')" /></th>
									<th>計價幣別<a href="#" class="icon icon-sort" @click="sort('curCode')" /></th>
									<th>是否可申購<a href="#" class="icon icon-sort" @click="sort('buyYn')" /></th>
									<th>到期日<a href="#" class="icon icon-sort" @click="sort('expireDt')" /></th>
									<th>審核狀態<a href="#" class="icon icon-sort" @click="sort('status')" /></th>
									<th class="text-center" width="120">
										執行
									</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in pageData.content">
									<td class="text-start" data-th="商品主類">
										{{ item.pfcatName }}
									</td>
									<td data-th="商品代號/險種代碼">
										<span v-if="isShowIsinCode(item.pfcatCode)">{{ item.isinCode }}</span>
										<span v-else>{{ item.bankProCode }}</span>
									</td>
									<td class="text-start" data-th="商品中文名稱">
										{{ item.proName }}
									</td>
									<td data-th="風險等級">
										{{ item.riskName }}
									</td>
									<td data-th="計價幣別">
										{{ item.curCode }}
									</td>
									<td data-th="是否可申購">
										<template v-if="item.pfcatCode === 'DCD'">
											請洽金融行銷部
										</template>
										<template v-else>
											{{ item.buyYnName }}
										</template>
									</td>
									<td data-th="到期日">
										{{ item.expireDt }} <span v-if="!item.expireDt">--</span>
									</td>
									<td data-th="審核狀態">
										<span v-if="item.status == 'P'">待審核</span>
										<span v-if="item.status == 'A'">審核完成</span>
										<span v-if="item.status == 'R'">
											<a class="tx-link" href="#" @click="showRejectMsg(item)">退回修改</a>
										</span>
									</td>
									<td data-th="執行">
										<div v-if="pageType == 'UPDATE'" align="left">
											<button
												type="button"
												class="btn btn-dark btn-glow btn-icon"
												data-bs-toggle="tooltip"
												title="檢視"
												@click="doViewPro(item)"
											>
												<i class="bi bi-search" />
											</button>
											<button
												v-if="pageType == 'UPDATE' && item.status != 'P'"
												type="button"
												class="btn btn-info btn-glow btn-icon"
												title="編輯"
												@click="doUpdate(item)"
											>
												<i class="bi bi-pen" />
											</button>
										</div>
										<div v-if="pageType == 'QUERY'">
											<button
												type="button"
												class="btn btn-dark btn-glow btn-icon"
												title="檢視"
												@click="doViewPro(item)"
											>
												<i class="bi bi-search" />
											</button>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Modal 2 基金 -->
	<vue-modal :is-open="isOpenModal['fund']" @close="closeModal('fund')">
		<template #content="props">
			<vue-pro-mgt-fund-modal
				ref="fundMgtModal"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pfcat-name="tranPrdtypeName"
				:action-type="actionType"
				:download-other-file="downloadOtherFile"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			/>
		</template>
	</vue-modal>
	<!-- Modal 2 End -->

	<!-- Modal 3 信託-結構型商品 -->
	<vue-modal :is-open="isOpenModal['sp']" @close="closeModal('sp')">
		<template #content="props">
			<vue-pro-mgt-sp-modal
				ref="spMgtModal"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:pfcat-name="tranPrdtypeName"
				:action-type="actionType"
				:download-other-file="downloadOtherFile"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			/>
		</template>
	</vue-modal>
	<!-- Modal 3 End -->

	<!-- Modal 4 ETF -->
	<vue-modal :is-open="isOpenModal['etf']" @close="closeModal('etf')">
		<template #content="props">
			<vue-pro-mgt-etf-modal
				ref="etfMgtModal"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pfcat-name="tranPrdtypeName"
				:action-type="actionType"
				:download-other-file="downloadOtherFile"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			/>
		</template>
	</vue-modal>
	<!-- Modal 4 End -->

	<!-- Modal 5 債券 -->
	<vue-modal :is-open="isOpenModal['bond']" @close="closeModal('bond')">
		<template #content="props">
			<vue-pro-mgt-bond-modal
				ref="bondMgtModal"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:pfcat-name="tranPrdtypeName"
				:action-type="actionType"
				:download-other-file="downloadOtherFile"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			/>
		</template>
	</vue-modal>
	<!-- Modal 5 End -->

	<!-- Modal 6 保險 -->
	<vue-modal :is-open="isOpenModal['ins']" @close="closeModal('ins')">
		<template #content="props">
			<vue-pro-mgt-ins-modal
				ref="insMgtModal"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pfcat-name="tranPrdtypeName"
				:action-type="actionType"
				:download-other-file="downloadOtherFile"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			/>
		</template>
	</vue-modal>
	<!-- Modal 6 End -->

	<!-- Modal 7 組合商品 銀行結構型商品 DCI -->
	<vue-modal :is-open="isOpenModal['dci']" @close="closeModal('dci')">
		<template #content="props">
			<vue-pro-mgt-dci-modal
				ref="dciMgtModal"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:pfcat-name="tranPrdtypeName"
				:action-type="actionType"
				:download-other-file="downloadOtherFile"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			/>
		</template>
	</vue-modal>
	<!-- Modal 7 End -->

	<!-- Modal 8 海外股票PFD -->
	<vue-modal :is-open="isOpenModal['pfd']" @close="closeModal('pfd')">
		<template #content="props">
			<vue-pro-mgt-pfd-modal
				ref="pfdMgtModal"
				:close="props.close"
				:fin-req-code-menu="finReqCodeMenu"
				:pro-price-range-menu="proPriceRangeMenu"
				:pfcat-name="tranPrdtypeName"
				:action-type="actionType"
				:download-other-file="downloadOtherFile"
				:download-file="downloadFile"
				:goto-page="gotoPage"
			/>
		</template>
	</vue-modal>
	<!-- Modal 8 End -->
</template>
<script>
import vuePagination from '@/views/components/pagination.vue';
import vueModal from '@/views/components/model.vue';
import vueProMgtFundModal from '@/views/pro/productInfo/include/fundMgtModal.vue';
import vueProMgtSpModal from '@/views/pro/productInfo/include/spMgtModal.vue';
import vueProMgtEtfModal from '@/views/pro/productInfo/include/etfMgtModal.vue';
import vueProMgtBondModal from '@/views/pro/productInfo/include/bondMgtModal.vue';
import vueProMgtInsModal from '@/views/pro/productInfo/include/insMgtModal.vue';
import vueProMgtDciModal from '@/views/pro/productInfo/include/dciMgtModal.vue';
import vueProMgtPfdModal from '@/views/pro/productInfo/include/pfdMgtModal.vue';

export default {
	components: {
		vuePagination,
		vueModal,
		vueProMgtFundModal,
		vueProMgtSpModal,
		vueProMgtEtfModal,
		vueProMgtBondModal,
		vueProMgtInsModal,
		vueProMgtDciModal,
		vueProMgtPfdModal
	},
	data: function () {
		return {
			pageType: 'UPDATE',
			pfcatCode: '', // 商品主類
			bankProCode: null, // 商品代號/險種代碼
			proName: null,
			riskCode: '',
			curObjs: [],
			tranPrdtypeName: null,
			intFreqUnitType: '',
			isinCode: null,

			platProdTrans: null, // 平台-商品-交易類型 資料結構
			platMenu: [], // 平台選單
			pfcatMenu: [], // 商品類型選單
			riskMenu: [],
			intFreqUnitTypeMenu: [],
			curOption: [],
			curId: null,

			finReqCodeMenu: [], // 商品共同資料-理財需求選項
			proPriceRangeMenu: [], // 價格/淨值顯示區間
			principalGuarMenu: [], // 到期保本下拉
			principalGuarYn: '',

			// 主要顯示資料
			columnDef: {
				bankProCode: { sortRef: 'BANK_PRO_CODE' },
				proName: { sortRef: 'PRO_NAME' },
				riskName: { sortRef: 'RISK_NAME' },
				curCode: { sortRef: 'CUR_CODE' },
				expireDt: { sortRef: 'EXPIRE_DT' },
				status: { sortRef: 'STATUS' },
				isinCode: { sortRef: 'ISIN_CODE' }
			},
			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'bank_pro_code',
				direction: 'ASC'
			},

			// Modal 使用參數
			modalProCode: null,
			spProCode: null,
			actionType: null,

			isOpenModal: {
				pro: false,
				fund: false,
				sp: false,
				etf: false,
				bond: false,
				ins: false,
				dci: false,
				sec: false,
				pfd: false
			}
		};
	},
	watch: {
		curObjs(newVal, oldVal) {
			const self = this;
			if (newVal[0] !== oldVal[0]) {
				if (newVal[0] === '') {
					this.curId = 'selectAll';
				}
				else if (oldVal[0] === '' && newVal[0] !== '') {
					this.curId = 'deselectAll';
				}
			}
		}
	},
	created: function () {
		const self = this;
	},
	mounted: function () {
		const self = this;
		self.getProPfcatsMenu();
		self.getRiskMenu();
		self.getCurrMenu();
		self.getIntFreqUnitTypeMenu();
		self.getFinReqCodeMenu();
		self.getProPriceRangeMenu(); // 價格/淨值顯示區間
		self.getPrincipalGuarMenu(); // 到期保本選項
	},
	methods: {
		refCurSel: function () {
			this.curId = 'refresh';
		},
		// 重置商品選單 (prodMenu)
		resetPfcatMenu: function () {
			const self = this;
			self.pfcatMenu = [];
		},
		getRiskMenu: async function () {
			const ret = this.$api.getRiskMenuApi();
			this.riskMenu = ret.data;
		},
		getIntFreqUnitTypeMenu: function () {
			const ret = this.$api.getIntFreqUnitTypeMenuApi();
			this.intFreqUnitTypeMenu = ret.data;
		},
		// 取得商品主類
		getProPfcatsMenu: async function () {
			const self = this;

			const ret = await self.$api.getProPfcatsMenuApi();
			self.pfcatMenu = ret.data;
		},
		getCurrMenu: async function () {
			const self = this;
			const ret = await this.$api.groupProCurrenciesMenuApi();
			if (ret.data) {
				ret.data.forEach(function (item) {
					const obj = { value: item.curCode, name: item.curName };
					self.curOption.push(obj);
				});

				self.$nextTick(function () {
					window.setTimeout(function () {
						self.refCurSel();
					}, 0);
				});
			}
		},
		// 取得 商品共同資料-理財需求選項
		getFinReqCodeMenu: function () {
			const self = this;
			const ret = self.$api.getAdmCodeDetail({ codeType: 'FIN_REQ_CODE' });
			self.finReqCodeMenu = ret.data;
		},
		// 取得 價格/淨值顯示區間
		getProPriceRangeMenu: function () {
			const self = this;
			const ret = self.$api.getProPriceRangeMenuApi();
			self.proPriceRangeMenu = ret.data;
		},
		// 取得到期保本選項
		getPrincipalGuarMenu: function () {
			const self = this;
			self.principalGuarMenu = [{ codeValue: '', codeName: '不限' }];
			let selectYnList = [];
			const ret = self.$api.getAdmCodeDetail({ codeType: 'SELECT_YN' });
			selectYnList = ret.data;
			Array.prototype.push.apply(self.principalGuarMenu, selectYnList);
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			const self = this;
			self.pageable.page = this.$_.isNumber(page) ? page : self.pageable.page;

			const curCodes = [];
			self.curObjs.forEach(function (item) {
				curCodes.push(item);
			});

			const data = {
				pfcatCode: self.pfcatCode,
				principalGuarYn: self.principalGuarYn, // 到期保本
				bankProCode: self.bankProCode,
				proName: self.proName,
				riskCode: self.riskCode,
				intFreqUnitType: self.intFreqUnitType,
				curCodes: curCodes
			};
			const ret = await self.$api.editProductsApi(data, self.pageable);
			self.pageData = ret.data;
		},
		doViewPro: function (item) {
			const self = this;
			self.actionType = 'VIEW';
			let eventId = null;
			const status = item.status;
			if (item.status === 'P') {
				eventId = item.eventId;
			}
			if (item.pfcatCode == 'FUND') {
				// 基金
				this.$refs.fundMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
				this.isOpenModal['fund'] = true;
			}
			else if (item.pfcatCode == 'ETF') {
				// ETF
				this.$refs.etfMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
				this.isOpenModal['etf'] = true;
			}
			else if (item.pfcatCode == 'FB') {
				// 海外債
				this.$refs.bondMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
				this.isOpenModal['bond'] = true;
			}
			else if (item.pfcatCode == 'INS') {
				// 人身保險
				this.$refs.insMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
				this.isOpenModal['ins'] = true;
			}
			else if (item.pfcatCode == 'DCD') {
				// 組合式商品
				this.$refs.dciMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
				this.isOpenModal['dci'] = true;
			}
			else if (item.pfcatCode == 'SP') {
				// 結構型商品
				this.$refs.spMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
				this.isOpenModal['sp'] = true;
			}
			else if (item.pfcatCode == 'SEC') {
				// 兼營證券自營商品
				this.$refs.secMgtModal.getProInfo(item.proCode, item.pfcatCode, eventId);
				this.isOpenModal['sec'] = true;
			}
			else if (item.pfcatCode == 'PFD') {
				// 海外股票
				this.$refs.pfdMgtModal.getProInfo(item.proCode, item.pfcatCode);
				this.isOpenModal['pfd'] = true;
			}
			else {
				this.$refs.proMgtModal.getProDatas(item.proCode, item.pfcatCode);
			}
		},
		doUpdate: function (item) {
			const self = this;
			self.actionType = 'EDIT';
			if (item.pfcatCode == 'FUND') {
				// 基金
				self.spProCode = item.proCode;
				this.$refs.fundMgtModal.getProInfo(item.proCode, item.pfcatCode);
				this.isOpenModal['fund'] = true;
			}
			else if (item.pfcatCode == 'ETF') {
				// ETF
				this.$refs.etfMgtModal.getProInfo(item.proCode, item.pfcatCode);
				this.isOpenModal['etf'] = true;
			}
			else if (item.pfcatCode == 'FB') {
				// 海外債
				this.$refs.bondMgtModal.getProInfo(item.proCode, item.pfcatCode);
				this.isOpenModal['bond'] = true;
			}
			else if (item.pfcatCode == 'INS') {
				// 人身保險
				this.$refs.insMgtModal.getProInfo(item.proCode, item.pfcatCode);
				this.isOpenModal['ins'] = true;
			}
			else if (item.pfcatCode == 'DCD') {
				// 組合式商品
				this.$refs.dciMgtModal.getProInfo(item.proCode, item.pfcatCode);
				this.isOpenModal['dci'] = true;
			}
			else if (item.pfcatCode == 'SP') {
				// 結構型商品
				this.$refs.spMgtModal.getProInfo(item.proCode, item.pfcatCode);
				this.isOpenModal['sp'] = true;
			}
			else if (item.pfcatCode == 'SEC') {
				// 兼營證券自營商品
				this.$refs.secMgtModal.getProInfo(item.proCode, item.pfcatCode);
				this.isOpenModal['sec'] = true;
			}
			else if (item.pfcatCode == 'PFD') {
				// 海外股票
				this.$refs.pfdMgtModal.getProInfo(item.proCode, item.pfcatCode);
				this.isOpenModal['pfd'] = true;
			}
			else {
				self.modalProCode = item.proCode;
				this.$refs.proMgtModal.getProDatas(item.proCode, item.pfcatCode);
			}
		},
		closeModal: function (type) {
			this.isOpenModal[type] = false;
		},
		sort: function (columnName) {
			if (this.pageable.sort !== this.columnDef[columnName].sortRef) {
				this.pageable.sort = this.columnDef[columnName].sortRef;
				this.pageable.direction = 'DESC';
			}
			else {
				this.pageable.direction = this.pageable.direction == 'ASC' ? 'DESC' : 'ASC';
			}
			this.gotoPage(0);
		},

		isShowIsinCode: function (pfcatCode) {
			if (!pfcatCode) {
				return false;
			}

			switch (pfcatCode) {
				case 'SFB':
				case 'SSTKF':
				case 'SSTKD':
				case 'SETFF':
				case 'SETFD':
				case 'SSPF':
					return true;
			}
			return false;
		},

		downloadFile: async function (proFile) {
			if (proFile.proFileId) {
				try {
					const response = await this.$api.downloadProFileApi({
						proFileId: proFile.proFileId,
						eventId: proFile.eventId
					});
					const fileName = proFile.showName;
					const blob = response.data;
					const url = URL.createObjectURL(blob);
					const link = document.createElement('a');
					link.href = url;
					link.download = fileName;
					document.body.appendChild(link);
					link.click();
					link.remove();
					setTimeout(() => URL.revokeObjectURL(url), 1000);
				}
				catch (error) {
					this.$message?.error?.('檔案下載失敗！');
				}
			}
			else {
				const fileName = proFile.showName;
				const url = URL.createObjectURL(proFile);
				const link = document.createElement('a');
				link.href = url;
				link.download = fileName;
				document.body.appendChild(link);
				link.click();
				link.remove();
				setTimeout(() => URL.revokeObjectURL(url), 1000);
			}
		},

		downloadOtherFile: async function (fileId) {
			this.$api.downloadFileApi({ fileType: 'GenDocFiles', fileId: fileId });
		},
		showRejectMsg(item) {
			this.$bi.alert(item.reason);
		}
	}
};
</script>
