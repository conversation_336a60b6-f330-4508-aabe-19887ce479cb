<template>
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					結構型商品
				</h4>
				<button
					type="button"
					class="btn-close"
					aria-label="Close"
					@click.prevent="close()"
				/>
			</div>
			<div class="modal-body">
				<div class="card card-prointro shadow-none mb-3">
					<div class="pro_left">
						<div class="pro_name">
							<div class="avatar avatar-md avatar-oversea" />
							<h4>
								<span>商品名稱</span>
								<br>{{ proInfo.proName }} <br><span class="tx-black">{{ proInfo.proEName }}</span>
							</h4>
						</div>
						<h4 class="pro_value">
							<span>淨值日期與參考淨值</span>
							<br>{{ proInfo.priceDt }} <br><span>{{ proInfo.aPrice }}</span>
						</h4>
					</div>
					<div class="pro_class">
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>商品代碼</span> <br>{{ proInfo.bankProCode }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>資產類別</span> <br>{{ proInfo.assetcatName }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>商品主類</span> <br>{{ proInfo.pfcatName }}</h6>
						</div>
						<div class="media">
							<div class="icon">
								<i class="bi bi-info-circle-fill" />
							</div>
							<h6><span>商品次類</span> <br>{{ proInfo.proTypeName }}</h6>
						</div>
					</div>
				</div>
				<div class="tab-nav-pills">
					<ul class="nav nav-pills justify-content-center" role="tablist">
						<li class="nav-item">
							<a class="nav-link active" href="#Sectionover1" data-bs-toggle="pill">商品基本資料</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#Sectionover2" data-bs-toggle="pill">商品共同資料</a>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="#Sectionover3" data-bs-toggle="pill">商品附加資料</a>
						</li>
					</ul>

					<div class="tab-content">
						<div id="Sectionover1" class="tab-pane show active">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>商品資訊</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>風險等級</th>
											<td class="wd-30p">
												{{ proInfo.spInfo.riskName }}
											</td>
											<th>計價幣別</th>
											<td class="wd-30p">
												{{ proInfo.curCode }}
											</td>
										</tr>
										<tr>
											<th>商品種類</th>
											<td class="wd-30p">
												{{ proInfo.proTypeName }}
											</td>
											<th>發行日期</th>
											<td class="wd-30p">
												{{ proInfo.spInfo.issueDt }}
											</td>
										</tr>
										<tr>
											<th>到期日</th>
											<td class="wd-30p">
												{{ proInfo.spInfo.expireDt }}
											</td>
											<th>通路報酬</th>
											<td class="wd-30p">
												<input
													v-if="actionType === 'EDIT'"
													v-model="channelServiceRateEdit"
													maxlength="5"
													type="text"
													class="form-control"
												>
												<template v-if="actionType !== 'EDIT'">
													{{ proInfo.spInfo.channelServiceRate * 100 }}
												</template>%
											</td>
										</tr>
										<tr v-if="false">
											<th>配息率</th>
											<td class="wd-30p">
												<template v-if="actionType === 'EDIT'">
													<input
														id="intRate"
														v-model="intRateEdit"
														maxlength="5"
														name="intRate"
														type="text"
													>
												</template>

												<template v-if="actionType !== 'EDIT'">
													{{ proInfo.intRate * 100 }}
												</template>%
											</td>
										</tr>
										<tr>
											<th>保本與否</th>
											<td class="wd-30p">
												{{ proInfo.principalGuarYn === 'Y' ? '100%' : '不保本' }}
											</td>
										</tr>
										<tr>
											<th>發行機構名稱</th>
											<td class="wd-30p">
												<select
													v-if="actionType === 'EDIT'"
													id="issuerCode"
													v-model="proInfo.issuerCode"
													class="form-select"
													name="issuerCode"
													rules="required"
												>
													<option v-for="item in issuersMenu" :value="item.issuerCode">
														{{ item.issuerName }}
													</option>
												</select>

												<template v-if="actionType !== 'EDIT'">
													{{ proInfo.spInfo.issueName }}
												</template>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>連結標的</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<thead>
										<tr>
											<th v-if="actionType !== 'EDIT'">
												連結標的/期初進場價
											</th>
											<th v-if="actionType === 'EDIT'">
												連結標的
											</th>
											<th>期初進場價</th>
											<th>轉換價</th>
											<th>下限價</th>
											<th>提前出場價</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in proInfo.spInfo.targetList" v-if="actionType !== 'EDIT'">
											<td class="wd-20p">
												{{ $filters.defaultValue(item.isinCode, '--') }}
											</td>
											<td class="wd-20p">
												{{ item.entryPrice }}
											</td>
											<td class="wd-20p">
												{{ item.convPrice }}
											</td>
											<td class="wd-20p">
												{{ item.kiPrice }}
											</td>
											<td class="wd-20p">
												{{ item.koPrice }}
											</td>
										</tr>
										<tr
											v-for="(item, index) in Array.from({ length: spTarget.length })"
											v-if="actionType === 'EDIT'"
											:key="index"
										>
											<td class="wd-20p">
												<input
													v-model="spTarget[index].isinCode"
													maxlength="5"
													class="form-control"
													type="text"
													@change="handlePriceInput()"
												>
											</td>
											<td class="wd-20p">
												<input
													v-model="spTarget[index].entryPrice"
													maxlength="5"
													class="form-control JQ-float"
													type="number"
												>
											</td>
											<td class="wd-20p">
												<input
													v-model="spTarget[index].convPrice"
													maxlength="5"
													class="form-control JQ-float"
													type="number"
												>
											</td>
											<td class="wd-20p">
												<input
													v-model="spTarget[index].kiPrice"
													maxlength="5"
													class="form-control JQ-float"
													type="number"
												>
											</td>
											<td class="wd-20p">
												<input
													v-model="spTarget[index].koPrice"
													maxlength="5"
													class="form-control JQ-float"
													type="number"
												>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>投資金額限制</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>台幣最低投資金額</th>
											<td class="wd-30p">
												{{ proInfo.spInfo.mininvLcAmt }}
											</td>
											<th>台幣累加面額</th>
											<td class="wd-30p">
												{{ proInfo.spInfo.mininvLcAccAmt }}
											</td>
										</tr>
										<tr>
											<th>外幣最低投資金額</th>
											<td>{{ proInfo.spInfo.mininvFcAmt }}</td>
											<th>外幣累加面額</th>
											<td>{{ proInfo.spInfo.mininvFcAccAmt }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionover2" class="tab-pane fade">
							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>銷售相關資料</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>銷售地區</th>
											<td v-if="proInfo.allYn == 'Y'" class="wd-30p">
												全行
											</td>
											<td v-else class="wd-30p" />
											<th><span>銷售對象</span></th>
											<td class="wd-30p">
												{{ proInfo.targetCusBuName }}
											</td>
										</tr>
										<tr>
											<th>是否開放申購</th>
											<td>{{ proInfo.buyYn }}</td>
											<th>是否開放贖回</th>
											<td>{{ proInfo.sellYn }}</td>
										</tr>
										<tr>
											<th><span>波動類型</span></th>
											<td>
												<span v-if="actionType !== 'EDIT'">{{ proInfo.volatilityTypeName }}</span>
												<template v-for="(item, index) in volatilityTypeList" v-if="actionType === 'EDIT'">
													<div class="form-check form-check-inline">
														<input
															:id="'volatilityType_' + index"
															v-model="proInfo.volatilityType"
															class="form-check-input"
															type="radio"
															name="volatilityType"
															:value="item.codeValue"
														>
														<label class="form-check-label" :for="'volatilityType_' + index">{{ item.codeName }}</label>
													</div>
												</template>
											</td>
											<th><span>配息頻率</span></th>
											<td>
												<span v-if="actionType !== 'EDIT'">{{ proInfo.intFreqUnitypeName }}</span>
												<select
													v-if="actionType === 'EDIT'"
													id="intFreqUnitype"
													v-model="proInfo.intFreqUnitype"
													class="form-select"
													name="intFreqUnitype"
													rules="required"
												>
													<option v-for="item in intFreqUnitypeList" :value="item.codeValue">
														{{ item.codeName }}
													</option>
												</select>
											</td>
										</tr>
										<tr>
											<th><span>理財需求</span></th>
											<td colspan="3">
												<div v-for="(item, index) in finReqCodeMenu" class="form-check form-check-inline">
													<input
														:id="'finReqCodes_' + index"
														v-model="finReqCodes"
														class="form-check-input"
														name="finReqCodes"
														:disabled="actionType == 'EDIT' ? false : true"
														:value="item.codeValue"
														type="checkbox"
													>
													<label class="form-check-label" :for="'finReqCodes_' + index">{{ item.codeName }}</label>
												</div>
											</td>
										</tr>
										<tr>
											<th>商品標籤</th>
											<td disabled="disabled" colspan="3">
												{{ proInfo.selprocatNames }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div id="Sectionover3" class="tab-pane fade">
							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>其他設定</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th><span>商品投資標的</span></th>
											<td class="wd-80p">
												<span>{{ proInfo.sectorName }}</span>
											</td>
										</tr>
										<tr>
											<th><span>商品投資地區</span></th>
											<td>
												<span>{{ proInfo.geoFocusName }}</span>
											</td>
										</tr>
										<tr>
											<th><span>比較基準設定</span></th>
											<td>
												<span>{{ proInfo.benchmarkName }}</span>
											</td>
										</tr>
										<tr>
											<th><span>備註</span></th>
											<td>
												<textarea
													v-model="proInfo.memo"
													class="form-control"
													cols="80"
													rows="4"
													size="200"
													maxlength="200"
													:readonly="actionType !== 'EDIT'"
												/>
												<div v-if="proInfo.memo" class="tx-note">
													{{ 200 - proInfo.memo.length }} 個字可輸入
												</div>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none mb-3">
								<div class="card-header">
									<h4>相關附件</h4>
								</div>
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<tbody>
										<tr>
											<th>商品說明書</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														v-model="url['A']"
														type="text"
														name="url_A"
														class="form-control"
														size="40"
														maxlength="100"
													>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																id="spUploadFileA"
																class="form-control form-file"
																type="file"
																size="30"
																accept="application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
																@change="triggerFile($event, 'A')"
															>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('A')">
																上傳
															</button>
														</div>
													</div>
													<br>
												</template>
												<span v-if="uploadFiles['A'] && uploadFiles['A'].url && actionType !== 'EDIT'">連結位址： <a :href="uploadFiles['A'].url" target="_blank">{{ uploadFiles['A'].url }}</a><br v-if="uploadFiles['A']">
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['A'] && uploadFiles['A'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['A'])"
													>{{ uploadFiles['A'].showName }}</span>
													<span
														v-show="uploadFiles['A'] && uploadFiles['A'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														title="刪除"
														@click="deleteFiles('A', uploadFiles['A'].proFileId)"
													/>
												</a>
											</td>
										</tr>
										<tr>
											<th>投資人須知</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														v-model="url['D']"
														type="text"
														name="url_D"
														class="form-control"
														size="40"
														maxlength="100"
													>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																id="spUploadFileD"
																class="form-control form-file"
																type="file"
																size="30"
																accept=".xlsx,.xls"
																@change="triggerFile($event, 'D')"
															>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('D')">
																上傳
															</button>
														</div>
													</div>
													<br>
												</template>
												<span v-if="uploadFiles['D'] && uploadFiles['D'].url && actionType !== 'EDIT'">連結位址： <a :href="uploadFiles['D'].url" target="_blank">{{ uploadFiles['D'].url }}</a><br v-if="uploadFiles['D']">
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['D'] && uploadFiles['D'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['D'])"
													>{{ uploadFiles['D'].showName }}</span>
													<span
														v-show="uploadFiles['D'] && uploadFiles['D'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														title="刪除"
														@click="deleteFiles('D', uploadFiles['D'].proFileId)"
													/>
												</a>
											</td>
										</tr>
										<tr>
											<th>DM</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														v-model="url['F']"
														type="text"
														name="url_F"
														class="form-control"
														size="40"
														maxlength="100"
													>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																id="spUploadFileF"
																class="form-control form-file"
																type="file"
																size="30"
																accept="application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
																@change="triggerFile($event, 'F')"
															>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('F')">
																上傳
															</button>
														</div>
													</div>
													<br>
												</template>
												<span v-if="uploadFiles['F'] && uploadFiles['F'].url && actionType !== 'EDIT'">連結位址： <a :href="uploadFiles['F'].url" target="_blank">{{ uploadFiles['F'].url }}</a><br v-if="uploadFiles['F']">
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['F'] && uploadFiles['F'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['F'])"
													>{{ uploadFiles['F'].showName }}</span>
													<span
														v-show="uploadFiles['F'] && uploadFiles['F'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														title="刪除"
														@click="deleteFiles('F', uploadFiles['F'].proFileId)"
													/>
												</a>
											</td>
										</tr>
										<tr>
											<th>其他</th>
											<td class="wd-80p">
												<template v-if="actionType === 'EDIT'">
													<span class="input-group-text">連結位址： </span>
													<input
														v-model="url['G']"
														type="text"
														name="url_G"
														class="form-control"
														size="40"
														maxlength="100"
													>
													<div class="col-lg-6">
														<div class="input-group">
															<input
																id="spUploadFileG"
																class="form-control form-file"
																type="file"
																size="30"
																accept="application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
																@change="triggerFile($event, 'G')"
															>
															<button class="btn btn-info btn-glow" type="button" @click="doUploadFile('G')">
																上傳
															</button>
														</div>
													</div>
													<br>
												</template>
												<span v-if="uploadFiles['G'] && uploadFiles['G'].url && actionType !== 'EDIT'">連結位址： <a :href="uploadFiles['G'].url" target="_blank">{{ uploadFiles['G'].url }}</a><br v-if="uploadFiles['G']">
												</span>
												<a href="#">
													<span
														v-if="uploadFiles['G'] && uploadFiles['G'].showName"
														class="tx-link"
														@click="downloadFile(uploadFiles['G'])"
													>{{ uploadFiles['G'].showName }}</span>
													<span
														v-show="uploadFiles['G'] && uploadFiles['G'].showName && actionType === 'EDIT'"
														class="img-delete"
														style="right: -25px"
														data-bs-toggle="tooltip"
														title="刪除"
														@click="deleteFiles('G', uploadFiles['G'].proFileId)"
													/>
												</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="card card-table shadow-none">
								<div class="card-header">
									<h4>其他相關附件</h4>
								</div>
							</div>
							<table class="table table-RWD table-bordered table-horizontal-RWD">
								<tr>
									<td>
										<span v-for="(item, index) in otherFileList">
											<a
												v-if="index === otherFileList.length - 1"
												v-show="item.show"
												href="#"
												class="tx-link"
												@click="downloadOtherFile(item.docFileId)"
											>{{ $filters.defaultValue(item.showName, '--') }}</a>
											<a
												v-else
												v-show="item.show"
												href="#"
												class="tx-link"
												@click="downloadOtherFile(item.docFileId)"
											>{{ $filters.defaultValue(item.showName, '--') }}、</a>
										</span>
									</td>
								</tr>
							</table>
							<div class="tx-note">
								其他相關附件為文件管理內所上傳與本商品相關的文件。
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<input
					id="modalCloseButton"
					type="button"
					class="btn btn-white"
					value="關閉"
					@click.prevent="close()"
				>
				<input
					v-if="actionType == 'EDIT'"
					type="button"
					class="btn btn-primary"
					value="傳送主管審核"
					@click="updateProduct()"
				>
			</div>
		</div>
	</div>
</template>

<script>
import pagination from '@/views/components/pagination.vue';
import moment from 'moment';
import _ from 'lodash';
import { biModule } from '@/utils/bi/module.js';

export default {
	components: {
		pagination
	},
	props: {
		actionType: String,
		benchmark: Object,
		gotoPage: Function,
		finReqCodeMenu: Array,
		downloadFile: Function,
		downloadOtherFile: Function,
		close: Function
	},
	data: function () {
		const now = moment();
		return {
			proInfo: {
				fundInfo: {},
				etfInfo: {},
				bondInfo: {},
				spInfo: {},
				insInfo: {},
				dciInfo: {},
				secInfo: {}
			},
			spTarget: [{}, {}, {}, {}, {}],
			finReqCodes: [],
			intRateEdit: '',
			channelServiceRateEdit: '',
			proFileA: null,
			proFileD: null,
			proFileF: null,
			proFileG: null,
			proFileList: [], // 相關附件檔案清單
			otherFileList: [], // 其他相關附件
			commInfo: {
				proFiles: []
			},
			proCode: '',
			pfcatCode: '',
			selectYnList: [],
			volatilityTypeList: [], // 波動類型選單
			intFreqUnitypeList: [], // 配息頻率選單
			issuersMenu: [], // 發行機構選單

			// File 用參數
			url: [],
			uploadFile: {},
			uploadFiles: [],

			benchmarkName: null,
			benchmarkRiskValue: null,
			benchmarkAvgintPerct: null
		};
	},
	watch: {
		intRateEdit: function (newVal, oldVal) {
			const self = this;
			if (newVal) {
				const intRateEdit = parseFloat(newVal);
				self.proInfo.intRate = intRateEdit / 100.0;
			}
		},
		channelServiceRateEdit: function (newVal, oldVal) {
			const self = this;
			if (newVal) {
				self.channelServiceRateEdit = parseInt(newVal);
				self.proInfo.spInfo.channelServiceRate = self.channelServiceRateEdit / 100.0;
			}
		}
	},
	mounted: function () {},
	methods: {
		// 波動類型來源資料
		async getVolatilityTypeList() {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({ codeType: 'VOLATILITY_TYPE' });
			self.volatilityTypeList = ret.data;
		},
		// 配息頻率來源資料
		async getIntFreqUnitypeList() {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({ codeType: 'INT_FREQ_UNITTYPE' });
			self.intFreqUnitypeList = ret.data;
		},
		// 發行機構來源資料
		groupIssuersMenu: async function (pfcatCode) {
			const self = this;
			const ret = await self.$api.getGroupIssuersMenuApi(pfcatCode);
			self.issuersMenu = ret.data;
		},
		getProInfo: function (bankProCode, pfcatCode, eventId) {
			const self = this;
			self.resetModalVaule();
			Promise.all([self.getVolatilityTypeList(), self.getIntFreqUnitypeList(), self.groupIssuersMenu(pfcatCode)]).then(() => {
				if (eventId) {
					self.doViewProLog(bankProCode, pfcatCode, eventId); // //審核資料
				}
				else {
					self.getProductInfo(bankProCode, pfcatCode); // 基本資料 共用資料
					self.getProductCommInfo(bankProCode, pfcatCode); // 附加資料
				}
			});
		},

		getProductInfo: async function (bankProCode, pfcatCode, callback) {
			const self = this;
			const productInfoRet = await self.$api.getProductInfoApi({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});

			if (_.isNil(productInfoRet.data)) {
				productInfoRet.data = {};
				biModule.alert('資料不存在');
				return;
			}
			if (_.isNil(productInfoRet.data.spInfo)) {
				productInfoRet.data.spInfo = {};
			}

			self.proInfo = productInfoRet.data;
			self.proCode = bankProCode;
			self.pfcatCode = pfcatCode;

			self.spTarget = self.$options.data().spTarget;

			//	通路報酬
			if (!_.isUndefined(self.proInfo.spInfo.channelServiceRate)) {
				self.channelServiceRateEdit = self.proInfo.spInfo.channelServiceRate * 100;
			}

			if (self.proInfo && self.proInfo.spInfo && self.proInfo.spInfo.targetList) {
				self.proInfo.spInfo.targetList.forEach((e, index) => {
					self.spTarget[index] = { ...e };
				});
			}

			// 這邊處理商品基本資料

			// 商品共用資料
			if (!_.isUndefined(self.proInfo.targetCusBu)) {
				let targetCusBuList = [];
				const cusBuRet = await self.$api.getAdmCodeDetail({ codeType: 'CUS_BU' });
				targetCusBuList = cusBuRet.data;
				const targetCusBuObjs = _.filter(targetCusBuList, {
					codeValue: self.proInfo.targetCusBu
				});
				self.proInfo.targetCusBu = targetCusBuObjs[0].codeValue;
				self.proInfo.targetCusBuName = targetCusBuObjs[0].codeName;
			}

			if (!_.isUndefined(self.proInfo.buyYn) && !_.isUndefined(self.proInfo.sellYn)) {
				const selectYnRet = await self.$api.getAdmCodeDetail({ codeType: 'SELECT_YN' });
				self.selectYnList = selectYnRet.data;

				if (!_.isUndefined(self.proInfo.buyYn)) {
					const buyYnObjs = _.filter(self.selectYnList, {
						codeValue: self.proInfo.buyYn
					});
					self.proInfo.buyYn = buyYnObjs[0].codeName;
				}

				if (!_.isUndefined(self.proInfo.sellYn)) {
					const sellYnObjs = _.filter(self.selectYnList, {
						codeValue: self.proInfo.sellYn
					});
					self.proInfo.sellYn = sellYnObjs[0].codeName;
				}
			}

			// 波動類型
			if (!_.isUndefined(self.proInfo.volatilityType)) {
				const volatilityTypeRet = await self.$api.getAdmCodeDetail({ codeType: 'VOLATILITY_TYPE' });
				self.volatilityTypeList = volatilityTypeRet.data;
				const volatilityTypeObjs = _.filter(self.volatilityTypeList, {
					codeValue: self.proInfo.volatilityType
				});
				// 區分編輯與(檢視、審核)
				if ((volatilityTypeObjs.length > 0) & (self.actionType === 'EDIT')) {
					self.proInfo.volatilityType = volatilityTypeObjs[0].codeValue;
				}
				else {
					self.proInfo.volatilityTypeName = volatilityTypeObjs[0].codeName;
				}
			}

			// 配息頻率
			if (!_.isUndefined(self.proInfo.intFreqUnitype)) {
				const intFreqYnitTypeRet = await self.$api.getAdmCodeDetail({ codeType: 'INT_FREQ_UNITTYPE' });
				self.intFreqUnitypeList = intFreqYnitTypeRet.data;
				const intFreqUnitypeObjs = _.filter(self.intFreqUnitypeList, {
					codeValue: self.proInfo.intFreqUnitype
				});
				if (self.actionType === 'EDIT') {
					self.proInfo.intFreqUnitype = intFreqUnitypeObjs[0].codeValue;
				}
				else {
					self.proInfo.intFreqUnitypeName = intFreqUnitypeObjs[0].codeName;
				}
			}

			// 理財需求
			if (!_.isUndefined(productInfoRet.data.finReqCode)) {
				self.finReqCodes = productInfoRet.data.finReqCode.split(',');
			}

			if (!_.isUndefined(self.proInfo.selprocatNames)) {
				const selprocatNames = self.proInfo.selprocatNames.replaceAll(',', '、');
				self.proInfo.selprocatNames = selprocatNames;
			}

			callback && callback();
		},
		getProductCommInfo: async function (bankProCode, pfcatCode) {
			const self = this;
			// 商品附加資料
			const ret = await self.$api.getProductsCommInfo({
				proCode: bankProCode,
				pfcatCode: pfcatCode
			});
			if (!_.isNil(ret.data)) {
				if (ret.data.proDocs) {
					self.otherFileList = ret.data.proDocs; // 其他相關附件
					self.otherFileList.forEach(function (item) {
						// 其他相關附件 檔案顯示時間範圍
						item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
					});
				}

				self.proFileList = ret.data.proFiles;
				if (!_.isNil(self.proFileList)) {
					self.uploadFiles['A'] = self.proFileList.filter(proFile => proFile.fileType === 'A')[0];
					self.uploadFiles['D'] = self.proFileList.filter(proFile => proFile.fileType === 'D')[0];
					self.uploadFiles['F'] = self.proFileList.filter(proFile => proFile.fileType === 'F')[0];
					self.uploadFiles['G'] = self.proFileList.filter(proFile => proFile.fileType === 'G')[0];
					self.url['A'] = self.uploadFiles['A'] ? self.uploadFiles['A'].url : null;
					self.url['D'] = self.uploadFiles['D'] ? self.uploadFiles['D'].url : null;
					self.url['F'] = self.uploadFiles['F'] ? self.uploadFiles['F'].url : null;
					self.url['G'] = self.uploadFiles['G'] ? self.uploadFiles['G'].url : null;
				}
			}
		},
		updateProduct: async function () {
			const self = this;

			self.commInfo = self.proInfo;
			self.commInfo.finReqCode = self.finReqCodes.join(',');

			// 判斷self.spTarget[index].isinCode 是否有重複
			const isinCodes = self.spTarget.map(item => item.isinCode).filter(code => code && code.length > 0);
			const uniqueIsinCodes = new Set(isinCodes);
			if (uniqueIsinCodes.size !== isinCodes.length) {
				this.$bi.alert('連結標的名稱重複，請重新修改。');
				return;
			}
			self.commInfo.spInfo.targetList = [];
			self.spTarget.forEach((e) => {
				e.createDt = null;
				if (e.isinCode) {
					self.commInfo.spInfo.targetList.push(e);
				}
			});
			self.spTarget = self.$options.data().spTarget;

			Object.keys(self.url).forEach((key) => {
				// 檢查是否只有輸入url 沒有上傳檔案的type
				if (self.url[key] !== null) {
					// 有輸入url
					const typeInclude = self.proFileList.some(obj => obj.fileType === key); // 找出是否有存在fileList
					if (!typeInclude) {
						const proFile = {};
						proFile.fileType = key;
						self.proFileList.push(proFile);
					}
				}
			});

			self.proFileList.forEach((e) => {
				e.createDt = null; // 移除日期避免轉型錯誤
				e.url = self.url[e.fileType];
			});
			self.commInfo.proFiles = self.proFileList;

			const formData = new FormData();
			// json model
			formData.append('model', JSON.stringify(self.commInfo));

			// upload file
			for (const key in self.uploadFiles) {
				const item = self.uploadFiles[key];
				if (item) {
					formData.append('files', item);
				}
			}

			const ret = await self.$api.patchProductApi(formData);
			biModule.alert('提交審核成功。');
			self.gotoPage(0);
			self.resetModalVaule();
			self.close();
		},
		resetModalVaule: function () {
			const self = this;
			$('[type="file"]').val(null);
			self.url = [];
			self.proFile = [];
			self.uploadFile = {};
			self.uploadFiles = [];
		},
		doViewProLog: function (proCode, pfcatCode, eventId) {
			// 審核資料
			const self = this;
			self.resetModalVaule();
			if (proCode) {
				const callback = () => {
					self.getProductCommLogInfo(eventId);
				};

				self.getProductInfo(proCode, pfcatCode, callback);
			}
		},
		getProductCommLogInfo: async function (eventId) {
			const self = this;
			const ret = await self.$api.getProductLogApi({ eventId: eventId });
			if (!_.isNil(ret.data)) {
				// 取得維護資料帶入
				// 基本資料
				// 通路報酬
				self.proInfo.spInfo.channelServiceRate = ret.data.spInfo.channelServiceRate;
				// 發行機構名稱
				self.proInfo.spInfo.issueName = ret.data.spInfo.issueName;
				// 連結標的
				self.proInfo.spInfo.targetList = ret.data.spInfo.targetList;
				self.spTarget = self.$options.data().spTarget;
				if (self.proInfo && self.proInfo.spInfo && self.proInfo.spInfo.targetList) {
					self.proInfo.spInfo.targetList.forEach((e, index) => {
						self.spTarget[index] = { ...e };
					});
				}

				// 共同資料
				// 波動類型
				if (!_.isUndefined(ret.data.volatilityType)) {
					const volatilityTypeObjs = _.filter(self.volatilityTypeList, {
						codeValue: ret.data.volatilityType
					});
					self.proInfo.volatilityTypeName = volatilityTypeObjs[0].codeName;
				}
				// 配息頻率
				if (!_.isUndefined(ret.data.intFreqUnitype)) {
					const intFreqUnitypeObjs = _.filter(self.intFreqUnitypeList, {
						codeValue: ret.data.intFreqUnitype
					});
					self.proInfo.intFreqUnitypeName = intFreqUnitypeObjs[0].codeName;
				}

				// 附加資料
				self.proInfo.memo = ret.data.memo; // 備註

				if (ret.data.proDocs) {
					self.otherFileList = ret.data.proDocs; // 其他相關附件
					self.otherFileList.forEach(function (item) {
						// 其他相關附件 檔案顯示時間範圍
						item.show = moment(item.validDt).isBefore(moment()) && moment(item.expireDt).isAfter(moment());
					});
				}

				const proFileList = ret.data.proFiles;
				if (!_.isNil(proFileList)) {
					self.uploadFiles['A'] = proFileList.filter(proFile => proFile.fileType === 'A')[0];
					self.uploadFiles['D'] = proFileList.filter(proFile => proFile.fileType === 'D')[0];
					self.uploadFiles['F'] = proFileList.filter(proFile => proFile.fileType === 'F')[0];
					self.uploadFiles['G'] = proFileList.filter(proFile => proFile.fileType === 'G')[0];
				}
			}
		},
		triggerFile: function (event, fileType) {
			const self = this;
			self.uploadFile[fileType] = event.target.files[0];
			self.uploadFile[fileType].showName = event.target.files[0].name;
		},
		doUploadFile: function (fileType) {
			const self = this;
			if (self.uploadFile[fileType]) {
				if (self.uploadFile[fileType].size > 10485760) {
					this.$bi.alert('檔案大小不得超過10MB！');
					return;
				}
				const proFile = {};

				proFile.fileName = self.uploadFile[fileType].name;
				proFile.showName = self.uploadFile[fileType].name;

				proFile.contentType = self.uploadFile[fileType].type;
				proFile.fileSize = self.uploadFile[fileType].size;
				proFile.fileType = fileType;

				if (!self.proFileList || self.proFileList.length <= 0) {
					self.proFileList.push(proFile);
				}
				else {
					// 有資料先刪除就檔案再新增
					self.proFileList.forEach((e, index) => {
						if (e.fileType === fileType) {
							self.proFileList.splice(index, 1);
						}
					});
					self.proFileList.push(proFile);
				}

				self.uploadFiles[fileType] = null; // 先將原先檔案清除
				self.uploadFiles[fileType] = self.uploadFile[fileType];

				$('#spUploadFile' + fileType).val(''); // 清空上傳區域檔案
				self.commInfo.isUpdateProFiles = true;
				self.uploadFile[fileType] = null;
			}
		},
		deleteFiles: function (fileType, proFileId) {
			const self = this;
			if (self.proFileList.length > 0) {
				self.proFileList.forEach((e, index, arr) => {
					if (e.proFileId === proFileId) {
						arr.splice(index, 1);
					}
				});
			}

			self.uploadFiles[fileType] = null;
		},
		handlePriceInput: function () {
			const self = this;
			if (!self.inputValue) {
				return;
			}
			// 使用正則表達式限制輸入
			const regex = /^\d{0,5}(\.\d{0,2})?$/;
			if (!regex.test(self.inputValue)) {
				// 如果輸入不合法，去掉最後一個字符
				self.inputValue = self.inputValue.slice(0, -1);
			}
		}
	} // methods end
};
</script>
