<template>
	<!-- Modal 1-->
	<vue-modal :is-open="isOpenMyModal" @close="closeMyModal()">
		<template #content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('pro.featuredRecommendedProducts') }}
						</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()">
							<i
								class="bi bi-arrows-fullscreen"
							/>
						</button>
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click.prevent="props.close()"
						/>
					</div>
					<div class="modal-body">
						<div class="tx-title">
							{{ $t('pro.featuredProductTypes') }}
						</div>
						<table class="table table-RWD table-bordered table-horizontal-RWD">
							<tbody>
								<tr>
									<th class="wd-20p">
										{{ $t('pro.productMainCategory') }}
									</th>
									<td class="wd-80p">
										{{ previewItem.pfcatName }}
									</td>
								</tr>
								<tr>
									<th>{{ $t('pro.featuredProductCategory') }}</th>
									<td>{{ previewItem.selprocatName }}</td>
								</tr>
								<tr>
									<th>{{ $t('pro.featuredProductPackageName') }}</th>
									<td>{{ previewItem.selproName }}</td>
								</tr>
								<tr>
									<th>{{ $t('pro.listingDate') }}</th>
									<td>{{ previewItem.startDate }}~ {{ previewItem.endDate }}</td>
								</tr>
							</tbody>
						</table>
						<div class="tx-title">
							{{ $t('pro.productList') }}
						</div>
						<div class="table-responsive">
							<table class="table table-RWD table-hover table-bordered">
								<thead>
									<tr>
										<th>{{ $t('pro.proCode') }}</th>
										<th>{{ $t('pro.proName') }}</th>
										<th>{{ $t('pro.issuer') }}</th>
										<th>{{ $t('pro.productRiskLevel') }}</th>
										<th>{{ $t('pro.productMainCategory') }}</th>
										<th>{{ $t('pro.productSubCategory') }}</th>
										<th>{{ $t('pro.nonMainLevel') }}</th>
										<th>{{ $t('pro.changeStatus') }}</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in previewItem.proSelectedMaps">
										<td>{{ item.bankProCode }}</td>
										<td>
											<span>
												<a
													v-if="previewItem.pfcatCode === 'FUND'"
													class="tx-link"
													href="#"
													@click="fundModalHandler(item.proCode, previewItem.pfcatCode)"
												>{{
													$filters.defaultValue(item.proName, '--') }}</a>
												<a
													v-else-if="previewItem.pfcatCode === 'ETF'"
													class="tx-link"
													href="#"
													@click="etfModalHandler(item.proCode, previewItem.pfcatCode)"
												>{{
													$filters.defaultValue(item.proName, '--') }}</a>
												<a
													v-else-if="previewItem.pfcatCode === 'FB'"
													class="tx-link"
													href="#"
													@click="bondModalHandler(item.proCode, previewItem.pfcatCode)"
												>{{
													$filters.defaultValue(item.proName, '--') }}</a>
												<a
													v-else-if="previewItem.pfcatCode === 'SP'"
													class="tx-link"
													href="#"
													@click="spModalHandler(item.proCode, previewItem.pfcatCode)"
												>{{
													$filters.defaultValue(item.proName, '--') }}</a>
												<a
													v-else-if="previewItem.pfcatCode === 'INS'"
													class="tx-link"
													href="#"
													@click="insModalHandler(item.proCode, previewItem.pfcatCode)"
												>{{
													$filters.defaultValue(item.proName, '--') }}</a>
												<a
													v-else-if="previewItem.pfcatCode === 'DCI'"
													class="tx-link"
													href="#"
													@click="dciModalHandler(item.proCode, previewItem.pfcatCode)"
												>{{
													$filters.defaultValue(item.proName, '--') }}</a>
												<a
													v-else-if="previewItem.pfcatCode === 'SEC'"
													class="tx-link"
													href="#"
													@click="secModalHandler(item.proCode, previewItem.pfcatCode)"
												>{{
													$filters.defaultValue(item.proName, '--') }}</a>
												<a
													v-else-if="previewItem.pfcatCode === 'PFD'"
													class="tx-link"
													href="#"
													@click="pfdModalHandler(item.proCode, previewItem.pfcatCode)"
												>{{
													$filters.defaultValue(item.proName, '--') }}</a>
												<span v-else>{{ $filters.defaultValue(item.proName, '--') }}</span>
											</span>
										</td>
										<td>{{ item.issuerName }}</td>
										<td>{{ item.riskName }}</td>
										<td>{{ previewItem.pfcatName }}</td>
										<td>{{ item.proTypeName }}</td>
										<td class="text-center">
											<input
												class="form-check-input text-center"
												type="checkbox"
												disabled
												:checked="item.mainLevelYn == 'Y'"
											>
										</td>
										<td>
											{{ previewItem.actionName }}
										</td>
									</tr>
								</tbody>
							</table>
						</div>

						<div id="modalFooterId" class="modal-footer">
							<input
								class="btn btn-white"
								type="button"
								:value="$t('pro.close')"
								@click.prevent="props.close()"
							>
						</div>
					</div>
				</div>
				<vue-modal :is-open="isOpenModal['fund']" @close="closeModal('fund')">
					<template #content="props">
						<vue-fund-modal
							ref="fundModalRef"
							:is-open-fund-modal="isOpenModal['fund']"
							:fin-req-code-menu="finReqCodeMenu"
							:download-file="downloadFile"
							:download-other-file="downloadOtherFile"
							:close="props.close"
						/>
					</template>
				</vue-modal>
				<vue-modal :is-open="isOpenModal['etf']" @close="closeModal('etf')">
					<template #content="props">
						<vue-etf-modal
							ref="etfModalRef"
							:is-open-etf-modal="isOpenModal['etf']"
							:fin-req-code-menu="finReqCodeMenu"
							:pro-price-range-menu="proPriceRangeMenu"
							:download-file="downloadFile"
							:download-other-file="downloadOtherFile"
							:close="props.close"
						/>
					</template>
				</vue-modal>
				<vue-modal :is-open="isOpenModal['bond']" @close="closeModal('bond')">
					<template #content="props">
						<vue-bond-modal
							ref="bondModalRef"
							:is-open-bond-modal="isOpenModal['bond']"
							:fin-req-code-menu="finReqCodeMenu"
							:pro-price-range-menu="proPriceRangeMenu"
							:download-file="downloadFile"
							:download-other-file="downloadOtherFile"
							:close="props.close"
						/>
					</template>
				</vue-modal>
				<vue-modal :is-open="isOpenModal['pfd']" @close="closeModal('pfd')">
					<template #content="props">
						<vue-pfd-modal
							ref="pfdModalRef"
							:is-open-pfd-modal="isOpenModal['pfd']"
							:fin-req-code-menu="finReqCodeMenu"
							:close="props.close"
						/>
					</template>
				</vue-modal>
				<vue-modal :is-open="isOpenModal['sp']" @close="closeModal('sp')">
					<template #content="props">
						<vue-sp-modal
							ref="spModalRef"
							:is-open-structured-product-modal="isOpenModal['sp']"
							:fin-req-code-menu="finReqCodeMenu"
							:pro-price-range-menu="proPriceRangeMenu"
							:download-file="downloadFile"
							:download-other-file="downloadOtherFile"
							:close="props.close"
						/>
					</template>
				</vue-modal>
				<vue-modal :is-open="isOpenModal['ins']" @close="closeModal('ins')">
					<template #content="props">
						<vue-ins-modal
							ref="insModalRef"
							:is-open-ins-modal="isOpenModal['ins']"
							:fin-req-code-menu="finReqCodeMenu"
							:download-file="downloadFile"
							:download-other-file="downloadOtherFile"
							:close="props.close"
						/>
					</template>
				</vue-modal>
				<vue-modal :is-open="isOpenModal['dci']" @close="closeModal('dci')">
					<template #content="props">
						<vue-dci-modal
							ref="dciModalRef"
							:is-open-dci-modal="isOpenModal['dci']"
							:fin-req-code-menu="finReqCodeMenu"
							:pro-price-range-menu="proPriceRangeMenu"
							:download-file="downloadFile"
							:download-other-file="downloadOtherFile"
							:close="props.close"
						/>
					</template>
				</vue-modal>
				<vue-modal :is-open="isOpenModal['sec']" @close="closeModal('sec')">
					<template #content="props">
						<vue-sec-modal
							ref="secModalRef"
							:is-open-sec-modal="isOpenModal['sec']"
							:fin-req-code-menu="finReqCodeMenu"
							:pro-price-range-menu="proPriceRangeMenu"
							:download-file="downloadFile"
							:download-other-file="downloadOtherFile"
							:close="props.close"
						/>
					</template>
				</vue-modal>
			</div>
		</template>
	</vue-modal>
</template>
<script>
import vueModal from '@/views/components/model.vue';
import vueFundModal from '@/views/pro/PRO0101/include/fundModal.vue';
import vueEtfModal from '@/views/pro/PRO0101/include/etfModal.vue';
import vueBondModal from '@/views/pro/PRO0101/include/bondModal.vue';
import vuePfdModal from '@/views/pro/PRO0101/include/pfdModal.vue';
import vueSpModal from '@/views/pro/PRO0101/include/spModal.vue';
import vueInsModal from '@/views/pro/PRO0101/include/insModal.vue';
import vueDciModal from '@/views/pro/PRO0101/include/dciModal.vue';
import vueSecModal from '@/views/pro/PRO0101/include/secModal.vue';

export default {
	components: {
		vueModal,
		vueFundModal,
		vueEtfModal,
		vueBondModal,
		vuePfdModal,
		vueSpModal,
		vueInsModal,
		vueDciModal,
		vueSecModal
	},
	props: {},
	data: function () {
		return {
			proPriceRangeMenu: undefined,
			finReqCodeMenu: undefined,
			previewItem: {},
			isOpenMyModal: false,
			isOpenModal: {
				fund: false,
				etf: false,
				bond: false,
				pfd: false,
				sp: false,
				ins: false,
				dci: false,
				sec: false
			},
			// 定義動態class
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand'
		};
	},
	methods: {
		getDetail: function (eventId) {
			const self = this;
			if (_.isBlank(eventId)) {
				return;
			}
			self.$api
				.getProSelected({
					selproId: null,
					eventId: eventId
				})
				.then(function (ret) {
					self.previewItem = ret.data;
					self.isOpenMyModal = true;
				});
		},
		closeMyModal: function () {
			this.isOpenMyModal = false;
		},
		closeModal: function (type) {
			this.isOpenModal[type] = false;
		},
		fundModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.fundModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal.fund = true;
		},
		etfModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.etfModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.etfModalRef.getEtfDetail(proCode); // 商品資訊/基金資料
			self.$refs.etfModalRef.getEtfStockHold(proCode); // 商品資訊/ETF持股
			self.$refs.etfModalRef.getEtfPrice(proCode); // 商品資訊/價格分析資料
			self.$refs.etfModalRef.getPricesChartData(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.etfModalRef.getEtfProfileNameMenu(); // 商品資訊/績效分析
			self.$refs.etfModalRef.getEtfProfileBenchmarksMenu(); // 商品資訊/績效分析
			self.isOpenModal.etf = true;
		},
		bondModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.bondModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.bondModalRef.getBondPriceAna(proCode);
			self.$refs.bondModalRef.getPricesChartData(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.bondModalRef.getBondPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.bond = true;
		},
		pfdModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.pfdModalRef.getProInfo(proCode, pfcatCode);
			//			self.$refs.pfdModalRef.getEtfDetail(proCode); // 商品資訊/基金資料
			self.isOpenModal.pfd = true;
		},
		spModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.spModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.spModalRef.getSpPriceAna(proCode); // 商品資訊/淨值分析資料
			self.$refs.spModalRef.getSpNets(proCode, 'Y', -1.0); // 商品資訊/淨值分析圖表
			self.$refs.spModalRef.getSpPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.sp = true;
		},
		insModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.insModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal.ins = true;
		},
		dciModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.dciModalRef.getProInfo(proCode, pfcatCode); // 商品基本資料
			self.$refs.dciModalRef.getDciPriceAna(proCode); // 商品資訊/價格分析資料
			self.$refs.dciModalRef.getDciNets(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.dciModalRef.getDciPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.dci = true;
		},
		secModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.secModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.secModalRef.getSecPriceAna(proCode); // 商品資訊/淨值分析資料
			self.$refs.secModalRef.getSecNets(proCode, 'Y', -1.0); // 商品資訊/淨值分析圖表
			self.$refs.secModalRef.getSecPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.sec = true;
		},
		changeModalSize: function () {
			const self = this;
			if (self.modalClass === 'modal-dialog modal-dialog-centered modal-lg') {
				self.modalClass = 'modal-dialog modal-dialog-centered modal-lg fullscreen';
				self.buttonClass = 'btn-expand mini';
			}
			else {
				self.modalClass = 'modal-dialog modal-dialog-centered modal-lg';
				self.buttonClass = 'btn-expand';
			}
		},
		downloadFile: function (proFile) {
			const self = this;
			const proFileId = proFile.proFileId;
			const fileName = proFile.showName;
			self.$api
				.downloadProFileApi({
					proFileId: proFileId
				})
				.then(function (data) {
					const link = document.createElement('a');
					const url = URL.createObjectURL(data);
					link.download = fileName;
					link.href = url;
					document.body.appendChild(link);
					link.click();
					link.remove();
					setTimeout(() => URL.revokeObjectURL(url), 1000);
				});
		},
		downloadOtherFile: function (fileId) {
			const self = this;
			self.$api.downloadFileApi({ fileId: fileId, fileType: 'GenDocFiles' });
		}
	}
};
</script>
