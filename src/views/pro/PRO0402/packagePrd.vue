<template>
	<div>
		<ul class="nav nav-tabs nav-justified">
			<li class="nav-item">
				<a class="nav-link" :class="tabSwitch ? 'active' : ''" @click="byTotal()">精選商品組合總表</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" :class="!tabSwitch ? 'active' : ''" @click="byHistroy()">精選商品組合歷史紀錄</a>
			</li>
		</ul>

		<div v-if="!createBoolean" class="tab-content">
			<div class="tab-pane fade active show">
				<div class="card card-form-collapse">
					<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
						<h4>查詢條件</h4>
						<span class="tx-square-bracket">為必填欄位</span>
					</div>

					<div id="collapseListGroup1" class="collapse show">
						<div class="card-body">
							<vue-form v-slot="{ errors }" ref="queryForm">
								<div class="form-row">
									<div class="form-group col-12 col-lg-4">
										<label class="form-label">商品主類</label>
										<select v-model="pfcatCode" class="form-select" name="pfcatCode">
											<option selected value="">
												全部
											</option>
											<option v-for="item in proPfcatsList" :value="item.pfcatCode">
												{{ item.pfcatName }}
											</option>
										</select>
									</div>
									<div class="form-group col-12 col-lg-4">
										<label class="form-label">精選商品類別</label>
										<select v-model="selprocatCode" class="form-select" name="selprocatCode">
											<option value="">
												全部
											</option>
											<option v-for="item in selectProSortList" :value="item.codeValue">
												{{ item.codeName }}
											</option>
										</select>
									</div>
									<div class="form-group col-12 col-lg-4">
										<label class="form-label">上架日期</label>
										<div class="input-group input-date">
											<vue-field
												v-model="startDate"
												type="date"
												name="startDate"
												class="JQ-datepicker form-control form-input has-validation"
												label="上架日期(起)"
											/>
											<span class="input-group-text">~</span>
											<vue-field
												v-model="endDate"
												type="date"
												name="endDate"
												class="JQ-datepicker form-control form-input has-validation"
												label="上架日期(迄)"
											/>
										</div>
									</div>
								</div>

								<div v-if="tabSwitch" class="form-footer">
									<input
										class="btn btn-primary btn-search"
										type="button"
										value="查詢"
										@click="totalSearch(0)"
									>
									<input
										class="btn btn-primary"
										type="button"
										value="建立精選商品"
										@click="createPackage()"
									>
								</div>
								<div v-else class="form-footer">
									<input
										class="btn btn-primary btn-search"
										type="button"
										value="查詢"
										@click="historySearch(0)"
									>
								</div>
							</vue-form>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!--  Step1. 商品類型設定  -->
		<div v-else class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup2">
				<h4>Step1. 商品類型設定</h4>
				<span class="tx-square-bracket">為必填欄位</span>
			</div>

			<div id="collapseListGroup2" class="collapse show">
				<div class="card-body">
					<vue-form v-slot="{ errors }" ref="stepForm">
						<div class="form-row d-flex">
							<!-- 左邊的選擇欄 -->
							<div class="form-group col-12 col-lg-6">
								<label class="form-label tx-require"> 商品主類</label>
								<vue-field
									v-model="newPfcatCode"
									as="select"
									class="form-select"
									name="newPfcatCode"
									:disabled="editStatus"
									:class="{ 'is-invalid': errors.newPfcatCode }"
									rules="required"
									label="商品主類"
									@change="newPfcatCodeChange()"
								>
									<option selected value>
										--
									</option>
									<option v-for="item in proPfcatsList" :value="item.pfcatCode">
										{{ item.pfcatName }}
									</option>
								</vue-field>
							</div>

							<!-- 右邊警告訊息 -->
							<div class="d-flex flex-column justify-content-start" style="margin-left: 10px">
								<div style="height: auto">
									<span class="text-danger">[ * 商品主類包含不可申購之商品]</span>
								</div>
								<div style="height: auto">
									<span v-show="errors.newPfcatCode" class="text-danger">{{ errors.newPfcatCode }}</span>
								</div>
							</div>
						</div>

						<div class="form-row">
							<div class="form-group col-12 col-lg-6">
								<label class="form-label tx-require">精選商品類別</label>
								<vue-field
									v-model="newSelprocatCode"
									as="select"
									class="form-select"
									:disabled="editStatus"
									name="newSelprocatCode"
									:class="{ 'is-invalid': errors.newSelprocatCode }"
									rules="required"
									label="精選商品類別"
								>
									<option selected value>
										--
									</option>
									<option v-for="item in selectProSortList" :value="item.codeValue">
										{{ item.codeName }}
									</option>
								</vue-field>
							</div>
							<div style="height: 3px">
								<span v-show="errors.newSelprocatCode" class="text-danger">{{ errors.newSelprocatCode }}</span>
							</div>
						</div>

						<div class="form-row">
							<div class="form-group col-12 col-lg-6">
								<label class="form-label tx-require mg-e-10">精選商品套裝名稱</label>
								<div class="d-inline-block">
									<vue-field
										v-model="selproName"
										type="text"
										class="form-control"
										name="selproName"
										:class="{ 'is-invalid': errors.selproName }"
										rules="required"
										label="精選商品套裝名稱"
									/>
								</div>
								<div style="height: 3px">
									<span v-show="errors.selproName" class="text-danger">{{ errors.selproName }}</span>
								</div>
							</div>
							<div class="form-group col-12 col-lg-6">
								<label class="form-label tx-require">上架日期</label>
								<div class="d-inline-block">
									<div class="input-group">
										<vue-field
											v-model="newStartDate"
											type="date"
											name="newStartDate"
											:disabled="editStatus"
											class="JQ-datepicker form-control form-input has-validation"
											label="上架日期(起)"
											:class="{ 'is-invalid': errors.newStartDate }"
											rules="required"
										/>
										<span class="error invalid-feedback">
											{{ errors.newStartDate }}
										</span>
										<span class="input-group-text">~</span>
										<vue-field
											v-model="newEndDate"
											type="date"
											name="newEndDate"
											class="JQ-datepicker form-control form-input has-validation"
											label="上架日期(迄)"
											:class="{ 'is-invalid': errors.newEndDate }"
											rules="required"
										/>
										<span class="error invalid-feedback">
											{{ errors.newEndDate }}
										</span>
									</div>
								</div>
							</div>
						</div>
					</vue-form>
					<div class="form-footer">
						<input
							type="button"
							class="btn btn-primary btn-search mg-s-10"
							value="確認"
							@click="comfirm()"
						>
					</div>
				</div>
			</div>
			<!-- step1 end -->
			<!-- step2 -->
			<div v-show="step2Boolean" class="card card-form-collapse">
				<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup3">
					<h4>Step2. 商品挑選</h4>
				</div>
				<div id="collapseListGroup3" class="collapse show">
					<div class="card-body">
						<div class="form-row">
							<div class="form-group col-12 col-lg-6">
								<label class="form-label"> 商品代號</label>
								<input v-model="bankProCode" type="text" class="form-control">
							</div>
							<div class="form-group col-12 col-lg-6">
								<input
									name="Submit"
									type="button"
									class="btn btn-primary mg-e-10"
									value="快速加入"
									@click="fastJoin()"
								>
								<input
									name="Submit"
									type="button"
									class="btn btn-primary"
									value="商品查詢"
									@click="proSearchModel()"
								>
							</div>
							<div style="height: auto">
								<span class="text-danger">[ * 商品主類包含不可申購之商品]</span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- step2 end -->

			<div v-show="step2Boolean">
				<div class="card card-table">
					<div class="card-header">
						<h4>商品列表</h4>
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-hover table-bordered">
							<thead>
								<tr>
									<th>商品代號</th>
									<th>商品名稱</th>
									<th>發行機構</th>
									<th>商品風險等級</th>
									<th>商品次分類</th>
									<th>非主級別</th>
									<th>執行</th>
								</tr>
							</thead>
							<tbody id="pro">
								<tr v-for="(item, index) in proList">
									<td>{{ item.bankProCode }}</td>
									<td>{{ item.proName }}</td>
									<td>{{ item.issuerName }}</td>
									<td>{{ item.riskName }}</td>
									<td>{{ item.proTypeName }}</td>
									<td class="text-center">
										<input
											:id="'mainLevelYn' + index"
											v-model="mainLevelYn[index]"
											class="form-check-input text-center"
											type="checkbox"
											:checked="item.mainLevelYn == 'Y'"
										>
									</td>
									<td class="text-center">
										<a data-bs-toggle="tooltip" href="#" class="table-icon">
											<button
												type="button"
												class="btn btn-danger btn-icon"
												data-bs-toggle="tooltip"
												title="刪除"
												@click="delPro(item)"
											>
												<i class="fa-solid fa-trash" />
											</button>
										</a>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="text-end mt-3">
					<input
						v-if="editStatus"
						name="Submit"
						type="button"
						class="btn btn-primary"
						value="刪除此精選商品"
						@click="dleProSelect()"
					>
					<input
						name="Submit"
						type="button"
						class="btn btn-primary"
						value="提交審核"
						@click="submit()"
					>
					<input
						name="Submit"
						type="button"
						class="btn btn-primary"
						value="取消"
						@click="cancel()"
					>
				</div>
			</div>
		</div>
		<!-- 查詢列表 -->
		<div v-if="searchList.content?.length > 0" class="searchResult">
			<div class="card card-table">
				<div class="card-header">
					<h4>精選商品列表</h4>
					<vue-pagination :pageable="searchList" :goto-page="tabSwitch ? totalSearch : historySearch" />
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-hover">
						<thead>
							<tr>
								<th>商品主類</th>
								<th>精選商品類別</th>
								<th>精選商品套裝名稱</th>
								<th>上架日期(起)</th>
								<th>上架日期(迄)</th>
								<th>建立日期</th>
								<th>建立人員</th>
								<th>狀態</th>
								<th class="text-center">
									執行
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="item in searchList.content">
								<td>{{ item.pfcatName }}</td>
								<td>{{ item.selprocatName }}</td>
								<td>{{ item.selproName }}</td>
								<td>{{ item.startDate }}</td>
								<td>{{ item.endDate }}</td>
								<td>{{ item.createDt }}</td>
								<td>{{ item.createBy }} {{ item.userName }}</td>
								<td>
									<span v-if="tabSwitch">
										<span v-if="item.status === 'R'"><a href="#" @click="showAlert(item)">{{ item.statusName
										}}</a></span>
										<span v-else>{{ item.statusName }}</span>
									</span>
									<span v-else>
										<span v-if="item.status === 'R'"><a href="#" @click="showAlert(item)">不同意({{ item.actionName
										}})</a></span>
										<span v-else>同意({{ item.actionName }}) </span>
									</span>
								</td>
								<td>
									<a
										data-bs-toggle="tooltip"
										href="#"
										class="table-icon"
										@click="preview(item)"
									>
										<button type="button" class="btn btn-dark btn-icon" title="檢視">
											<i class="bi bi-search" />
										</button>
									</a>
									<a
										v-if="tabSwitch && item.status !== 'P'"
										data-bs-toggle="tooltip"
										href="#"
										class="table-icon"
										@click="edit(item)"
									>
										<button
											type="button"
											class="btn btn-info btn-icon"
											data-bs-toggle="tooltip"
											title="編輯"
										>
											<i class="fa-solid fa-pen" />
										</button>
									</a>
									<a
										v-if="!tabSwitch"
										data-bs-toggle="tooltip"
										href="#"
										class="table-icon"
										@click="del(item)"
									>
										<button
											type="button"
											class="btn btn-info btn-icon"
											data-bs-toggle="tooltip"
											title="刪除"
										>
											<i class="fa-solid fa-trash" />
										</button>
									</a>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>

			<div class="tx-note mb-3">
				<ol>
					<li>執行中--表示新增精選推薦商品的審核通過，並且成為目前執行中的精選推薦商品。</li>
					<li>審核中--表示新增、編輯、刪除的精選推薦商品，還在審核中。</li>
					<li>等待審核中的資料將無法異動</li>
				</ol>
			</div>
		</div>
		<!--頁面內容 end-->

		<!-- 檢視 modal -->
		<vue-modal :is-open="isOpenMyModal" @close="isOpenMyModal = false">
			<template #content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								{{ previewTitle }}
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="props.close()"
							/>
						</div>
						<div class="modal-body">
							<div class="tx-title">
								精選商品類型
							</div>
							<table class="table table-bordered table-RWD table-horizontal-RWD">
								<tbody>
									<tr>
										<th class="wd-20p">
											商品主類
										</th>
										<td class="wd-80p">
											{{ viewSelectPro.pfcatName }}
										</td>
									</tr>
									<tr>
										<th>精選商品類別</th>
										<td>{{ viewSelectPro.selprocatName }}</td>
									</tr>
									<tr>
										<th>精選商品套裝名稱</th>
										<td>{{ viewSelectPro.selproName }}</td>
									</tr>
								</tbody>
							</table>

							<div class="tx-title">
								商品列表
							</div>
							<div class="table-responsive">
								<table class="table table-RWD table-bordered table-horizontal-RWD">
									<thead>
										<tr>
											<th>商品代碼</th>
											<th>商品名稱</th>
											<th>發行機構</th>
											<th>商品風險等級</th>
											<th>商品主類</th>
											<th>商品次分類</th>
											<th>非主級別</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in viewSelectPro.proSelectedMaps">
											<td>{{ item.bankProCode }}</td>
											<td>
												<span>
													<a
														v-if="item.pfcatCode === 'FUND'"
														class="tx-link"
														href="#"
														@click="fundModalHandler(item.proCode, item.pfcatCode)"
													>{{
														$filters.defaultValue(item.proName, '--') }}</a>
													<a
														v-else-if="item.pfcatCode === 'ETF'"
														class="tx-link"
														href="#"
														@click="etfModalHandler(item.proCode, item.pfcatCode)"
													>{{
														$filters.defaultValue(item.proName, '--') }}</a>
													<a
														v-else-if="item.pfcatCode === 'FB'"
														class="tx-link"
														href="#"
														@click="bondModalHandler(item.proCode, item.pfcatCode)"
													>{{
														$filters.defaultValue(item.proName, '--') }}</a>
													<a
														v-else-if="item.pfcatCode === 'SP'"
														class="tx-link"
														href="#"
														@click="spModalHandler(item.proCode, item.pfcatCode)"
													>{{ $filters.defaultValue(item.proName,
														'--') }}</a>
													<a
														v-else-if="item.pfcatCode === 'INS'"
														class="tx-link"
														href="#"
														@click="insModalHandler(item.proCode, item.pfcatCode)"
													>{{
														$filters.defaultValue(item.proName, '--') }}</a>
													<a
														v-else-if="item.pfcatCode === 'DCI'"
														class="tx-link"
														href="#"
														@click="dciModalHandler(item.proCode, item.pfcatCode)"
													>{{
														$filters.defaultValue(item.proName, '--') }}</a>
													<a
														v-else-if="item.pfcatCode === 'SEC'"
														class="tx-link"
														href="#"
														@click="secModalHandler(item.proCode, item.pfcatCode)"
													>{{
														$filters.defaultValue(item.proName, '--') }}</a>
													<a
														v-else-if="item.pfcatCode === 'PFD'"
														class="tx-link"
														href="#"
														@click="pfdModalHandler(item.proCode, item.pfcatCode)"
													>{{
														$filters.defaultValue(item.proName, '--') }}</a>
													<span v-else>{{ $filters.defaultValue(item.proName, '--') }}</span>
												</span>
											</td>
											<td>{{ item.issuerName }}</td>
											<td>{{ item.riskName }}</td>
											<td>{{ viewSelectPro.pfcatName }}</td>
											<td>{{ item.proTypeName }}</td>
											<td class="text-center">
												<input
													class="form-check-input text-center"
													type="checkbox"
													:checked="item.mainLevelYn == 'Y'"
													disabled
												>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="modal-footer">
							<input
								id="appointmentCloseButton"
								name="btnClose"
								class="btn btn-white"
								type="button"
								value="關閉"
								@click.prevent="props.close()"
							>
						</div>
					</div>

					<vue-modal :is-open="isOpenModal['fund']" @close="closeModal('fund')">
						<template #content="props">
							<vue-fund-modal
								ref="fundModalRef"
								:is-open-fund-modal="isOpenModal['fund']"
								:fin-req-code-menu="finReqCodeMenu"
								:download-file="downloadFile"
								:download-other-file="downloadOtherFile"
								:close="props.close"
							/>
						</template>
					</vue-modal>
					<vue-modal :is-open="isOpenModal['etf']" @close="closeModal('etf')">
						<template #content="props">
							<vue-etf-modal
								ref="etfModalRef"
								:is-open-etf-modal="isOpenModal['etf']"
								:fin-req-code-menu="finReqCodeMenu"
								:pro-price-range-menu="proPriceRangeMenu"
								:download-file="downloadFile"
								:download-other-file="downloadOtherFile"
								:close="props.close"
							/>
						</template>
					</vue-modal>
					<vue-modal :is-open="isOpenModal['bond']" @close="closeModal('bond')">
						<template #content="props">
							<vue-bond-modal
								ref="bondModalRef"
								:is-open-bond-modal="isOpenModal['bond']"
								:fin-req-code-menu="finReqCodeMenu"
								:pro-price-range-menu="proPriceRangeMenu"
								:download-file="downloadFile"
								:download-other-file="downloadOtherFile"
								:close="props.close"
							/>
						</template>
					</vue-modal>
					<vue-modal :is-open="isOpenModal['pfd']" @close="closeModal('pfd')">
						<template #content="props">
							<vue-pfd-modal
								ref="pfdModalRef"
								:is-open-pfd-modal="isOpenModal['pfd']"
								:fin-req-code-menu="finReqCodeMenu"
								:close="props.close"
							/>
						</template>
					</vue-modal>
					<vue-modal :is-open="isOpenModal['sp']" @close="closeModal('sp')">
						<template #content="props">
							<vue-sp-modal
								ref="spModalRef"
								:is-open-structured-product-modal="isOpenModal['sp']"
								:fin-req-code-menu="finReqCodeMenu"
								:pro-price-range-menu="proPriceRangeMenu"
								:download-file="downloadFile"
								:download-other-file="downloadOtherFile"
								:close="props.close"
							/>
						</template>
					</vue-modal>
					<vue-modal :is-open="isOpenModal['ins']" @close="closeModal('ins')">
						<template #content="props">
							<vue-ins-modal
								ref="insModalRef"
								:is-open-ins-modal="isOpenModal['ins']"
								:fin-req-code-menu="finReqCodeMenu"
								:download-file="downloadFile"
								:download-other-file="downloadOtherFile"
								:close="props.close"
							/>
						</template>
					</vue-modal>
					<vue-modal :is-open="isOpenModal['dci']" @close="closeModal('dci')">
						<template #content="props">
							<vue-dci-modal
								ref="dciModalRef"
								:is-open-dci-modal="isOpenModal['dci']"
								:fin-req-code-menu="finReqCodeMenu"
								:pro-price-range-menu="proPriceRangeMenu"
								:download-file="downloadFile"
								:download-other-file="downloadOtherFile"
								:close="props.close"
							/>
						</template>
					</vue-modal>
					<vue-modal :is-open="isOpenModal['sec']" @close="closeModal('sec')">
						<template #content="props">
							<vue-sec-modal
								ref="secModalRef"
								:is-open-sec-modal="isOpenModal['sec']"
								:fin-req-code-menu="finReqCodeMenu"
								:pro-price-range-menu="proPriceRangeMenu"
								:download-file="downloadFile"
								:download-other-file="downloadOtherFile"
								:close="props.close"
							/>
						</template>
					</vue-modal>
				</div>
			</template>
		</vue-modal>
		<!-- modal end -->

		<!-- modal 商品查詢 -->
		<vue-modal :is-open="isOpenProModal" @close="isOpenProModal = false">
			<template #content="props">
				<div class="modal-dialog modal-xl modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">
								商品查詢
							</h4>
							<button
								type="button"
								class="btn-close"
								aria-label="Close"
								@click.prevent="props.close()"
							/>
						</div>
						<div class="modal-body">
							<div class="card card-form-collapse">
								<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
									<h4>查詢條件</h4>
								</div>

								<div id="collapseListGroup1" class="collapse show">
									<div class="card-body">
										<vue-form v-slot="{ errors }" ref="searchQueryForm">
											<div class="form-row">
												<div class="form-group col-12 col-lg-6">
													<label class="form-label">商品名稱</label>
													<input v-model="proName" type="text" class="form-control">
												</div>
												<div class="form-group col-12 col-lg-6">
													<label class="form-label">商品代號</label>
													<input v-model="bankProCode" type="text" class="form-control">
												</div>
												<div class="form-group col-12 col-lg-6">
													<label style="height: 30px" class="form-label tx-require">資產類別 </label>
													<vue-field
														id="assetcatCode"
														v-model="assetcatCode"
														as="select"
														class="form-select"
														rules="required"
														name="assetcatCode"
														label="資產類別"
														:class="{ 'is-invalid': errors.assetcatCode }"
													>
														<option value="">
															--
														</option>
														<option v-for="item in assetcatsMenu" :value="item.assetcatCode">
															{{ item.assetcatName }}
														</option>
													</vue-field>
													<div style="height: 25px">
														<span v-show="errors.assetcatCode" class="text-danger">{{ errors.assetcatCode }}</span>
													</div>
												</div>
												<div class="form-group col-12 col-lg-6">
													<label class="form-label tx-require">商品主類</label>
													<span>{{ pfcatName }}</span>
												</div>
												<div class="form-group col-12 col-lg-6">
													<label class="form-label tx-require">商品次分類</label>
													<vue-field
														v-model="proTypeCode"
														as="select"
														name="proTypeCode"
														:class="{ 'is-invalid': errors.proTypeCode }"
														rules="required"
														class="form-select"
														label="精選商品類別"
													>
														<option selected value>
															--
														</option>
														<option v-for="item in proTypeMenu" :value="item.proTypeCode">
															{{ item.proTypeName }}
														</option>
													</vue-field>
													<div style="height: 3px">
														<span v-show="errors.proTypeCode" class="text-danger">{{ errors.proTypeCode }}</span>
													</div>
												</div>
												<div class="form-group col-12 col-lg-6">
													<label style="height: 30px" class="form-label"> 發行機構</label>
													<vue-field
														id="issuerCode"
														v-model="issuerCode"
														as="select"
														class="form-select"
														title="請選擇發行機構"
														name="issuerCode"
														label="發行機構"
														data-style="btn-white"
													>
														<option value="">
															--
														</option>
														<option v-for="item in issuersMenu" :key="index" :value="item.issuerCode">
															{{ item.issuerName }}
														</option>
													</vue-field>
													<div style="height: 25px" />
												</div>
											</div>

											<div class="form-footer">
												<input
													type="button"
													class="btn btn-primary btn-search"
													value="查詢"
													@click="proSearch(0)"
												>
											</div>
										</vue-form>
									</div>
								</div>
							</div>
							<div v-if="proSearchList.content?.length > 0">
								<div class="card card-table">
									<div class="card-header">
										<h4>查詢結果</h4>
										<vue-pagination :pageable="proSearchList" :goto-page="gotoProPage" />
									</div>
									<div class="table-responsive">
										<table class="table table-RWD table-hover table-bordered">
											<thead>
												<tr>
													<th>
														<input
															v-model="allCheckbox"
															type="checkbox"
															class="form-check-input"
															name="checkbox"
														>
													</th>
													<th>商品代號<br>商品名稱</th>
													<th>風險屬性</th>
													<th>比較基準</th>
													<th>投資標的</th>
													<th>發行公司</th>
													<th>幣別</th>
													<th>商品價格<br>報價日期</th>
												</tr>
											</thead>
											<tbody>
												<tr v-for="(item, index) in proSearchList.content">
													<td class="text-center">
														<input
															v-model="proCheckbox[index]"
															type="checkbox"
															class="form-check-input"
															name="proCheckbox"
														>
													</td>
													<td>{{ item.bankProCode }}<br>{{ item.proName }}</td>
													<td>{{ item.riskName }}</td>
													<td>{{ item.benchmarkName }}</td>
													<td>{{ item.sectorName }}</td>
													<td>{{ item.issuerName }}</td>
													<td>{{ item.curCode }}</td>
													<td>{{ item.aprice }}<br>{{ item.priceDt }}</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>

						<div class="modal-footer">
							<input
								name="btnClose"
								class="btn btn-primary"
								type="button"
								value="加入"
								@click="addPro()"
							>
							<input
								name="btnClose"
								class="btn btn-white"
								type="button"
								value="取消"
								@click.prevent="props.close()"
							>
						</div>
					</div>
				</div>
			</template>
		</vue-modal>
		<!-- modal 商品查詢 end -->
	</div>
</template>
<script>
import VuePagination from '@/views/components/pagination.vue';
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import vueFundModal from '@/views/pro/PRO0101/include/fundModal.vue';
import vueEtfModal from '@/views/pro/PRO0101/include/etfModal.vue';
import vueBondModal from '@/views/pro/PRO0101/include/bondModal.vue';
import vuePfdModal from '@/views/pro/PRO0101/include/pfdModal.vue';
import vueSpModal from '@/views/pro/PRO0101/include/spModal.vue';
import vueInsModal from '@/views/pro/PRO0101/include/insModal.vue';
import vueDciModal from '@/views/pro/PRO0101/include/dciModal.vue';
import vueSecModal from '@/views/pro/PRO0101/include/secModal.vue';
import _ from 'lodash';

export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		VuePagination,
		vueFundModal,
		vueEtfModal,
		vueBondModal,
		vuePfdModal,
		vueSpModal,
		vueInsModal,
		vueDciModal,
		vueSecModal
	},
	data: function () {
		return {
			proPriceRangeMenu: undefined,
			downloadOtherFile: () => void 0,
			downloadFile: () => void 0,
			finReqCodeMenu: undefined,
			tabSwitch: true, // 切換精選商品組合總表(true) 精選商品組合歷史紀錄(fasle)
			createBoolean: false, // 點選建立精選商品
			step2Boolean: false, // 點選建立精選商品

			// 總表與歷史紀錄畫面
			proPfcatsList: [], // 下拉 商品主類 列表// 商品主類
			selectProSortList: [], // 下拉 精選商品類別 列表
			pfcatCode: '', // 商品主類
			pfcatName: null, // 商品主類名稱(商品查詢使用)
			selprocatCode: '03', // 精選商品類別
			startDate: null, // 上架日期-起
			endDate: null, // 上架日期-迄
			searchList: [], // 精選商品 查詢結果
			pageable: {
				page: 0,
				size: 10,
				sort: 'CREATE_DT',
				direction: 'ASC'
			},
			// 新增編輯畫面
			newPfcatCode: null, // 建立精選推薦商品 商品主類
			newSelprocatCode: null, // 建立精選推薦商品 精選商品類別
			selproName: null, // 建立精選推薦商品 精選商品套裝名稱
			newStartDate: null, // 建立精選推薦商品 上架日期-起
			newEndDate: null, // 建立精選推薦商品 上架日期-迄
			bankProCode: null, // 快速加入 商品主類
			editStatus: false, // 編輯狀態
			selproId: null, // 精選商品套裝代碼
			proList: [], // 新增商品 商品列表

			// 商品查詢 畫面
			issuersMenu: [], // 發行機構下拉
			proTypeMenu: [], // 商品次類下拉
			proName: null, // 商品名稱
			bankProCode: null, // 商品代碼
			assetcatCode: null, // 資產類別
			proTypeCode: null, // 商品次分類
			issuerCode: null, // 發行機構
			assetcatsMenu: [], // 資產類別選單
			proSearchList: [], // 商品 查詢結果
			allCheckbox: false, // 全選checkbox
			proCheckbox: [], // 商品checkbox
			mainLevelYn: [], // 勾選商品的非主級別資料

			proPageable: {
				page: 0,
				size: 10,
				sort: 'PRO_CODE',
				direction: 'ASC'
			},

			// 檢視畫面
			previewTitle: '精選推薦商品', // 預設檢視標題
			viewSelectPro: {}, // 檢視 商品資料與商品列表

			isOpenProModal: false,
			isOpenMyModal: false,
			isOpenModal: {
				fund: false,
				etf: false,
				bond: false,
				pfd: false,
				sp: false,
				ins: false,
				dci: false,
				sec: false
			}
		};
	},
	watch: {
		tabSwitch: function (newVal, oldVal) {
			const self = this;
			if (newVal != oldVal) {
				// 有切換就清空
				self.pfcatCode = ''; // 下拉 商品主類
				self.selprocatCode = '03'; // 精選商品類別
				self.startDate = null; // 上架日期-起
				self.endDate = null; // 上架日期-迄
				self.proList = []; // 新增商品 商品列表
			}
		},
		viewSelectPro(v) {
			// 檢視 商品資料與商品列表顯示標題
			const self = this;
			if (v != null) {
				if ('A' === self.viewSelectPro.actionCode) {
					self.previewTitle = '新增精選推薦商品';
				}
				else if ('M' === self.viewSelectPro.actionCode) {
					self.previewTitle = '維護精選推薦商品';
				}
				else if ('D' === self.viewSelectPro.actionCode) {
					self.previewTitle = '刪除精選推薦商品';
				}
			}
		},
		allCheckbox(v) {
			// 商品查詢全選勾勾
			const self = this;
			const listLength = self.proSearchList.content.length;
			for (let i = 0; i < listLength; i++) {
				self.proCheckbox[i] = v;
			}
		}
	},
	mounted: function () {
		const self = this;
		self.getProPfcatsMenu(); // 精選商品主類選單
		self.getSelectProPfcatsMenu(); // 精選商品類別選單
		self.getAssetcatsMenu(); // 取得資產類別; // 取得資產類別
	},
	methods: {
		// 取得商品主類
		getProPfcatsMenu: async function () {
			const self = this;
			const ret = await self.$api.getSelectProPfcatsMenuApi();
			self.proPfcatsList = ret.data;
		},
		getSelectProPfcatsMenu: async function () {
			// 精選商品類別選單
			const self = this;
			const r = await self.$api.getSelProPfcatsMenuApi();
			self.selectProSortList = r.data;
		},
		// 取得資產類別
		getAssetcatsMenu: async function () {
			const self = this;
			const ret = await self.$api.getAssetcatsMenuApi();
			self.assetcatsMenu = ret.data;
		},
		byTotal() {
			// 切換精選商品組合總表
			const self = this;
			self.tabSwitch = true;
			self.createBoolean = false;
			self.searchList = [];
			self.pfcatCode = null; // 下拉 商品主類
			self.selprocatCode = null; // 精選商品類別
			self.selproName = null; // 精選商品套裝名稱
			self.startDate = null; // 上架日期-起
			self.endDate = null; // 上架日期-迄
			self.proList = []; // 新增商品 商品列表
			self.step2Boolean = false;
		},
		byHistroy() {
			// 精選商品組合歷史紀錄
			const self = this;
			self.tabSwitch = false;
			self.createBoolean = false;
			self.searchList = [];
			self.pfcatCode = null; // 下拉 商品主類
			self.selprocatCode = null; // 精選商品類別
			self.selproName = null; // 精選商品套裝名稱
			self.startDate = null; // 上架日期-起
			self.endDate = null; // 上架日期-迄
			self.proList = []; // 新增商品 商品列表
			self.step2Boolean = false;
		},
		createPackage() {
			// 建立精選商品按鈕
			const self = this;
			self.createBoolean = true; // 顯示  商品類型設定區域
			self.searchList = [];
			self.pfcatCode = null; // 下拉 商品主類
			self.selprocatCode = null; // 精選商品類別
			self.selproName = null; // 精選商品套裝名稱
			self.startDate = null; // 上架日期-起
			self.endDate = null; // 上架日期-迄
			self.editStatus = false; // 取消編輯狀態
			self.newPfcatCode = null; // 下拉 商品主類
			self.newSelprocatCode = null; // 精選商品類別
			self.newStartDate = null; // 上架日期-起
			self.newEndDate = null; // 上架日期-迄
		},
		// 新增精選商品又異動主類 要清空商品列表 避免主類與商品不同類型
		newPfcatCodeChange() {
			const self = this;
			self.proList = [];
		},
		// 確認 進入step2
		comfirm() {
			const self = this;
			self.$refs.stepForm.validate().then(function (pass) {
				if (pass.valid) {
					if (self.newStartDate > self.newEndDate) {
						self.$bi.alert('上架日期區間(起)不可大於上架日期區間(迄)。');
						return false;
					}
					self.step2Boolean = true;
				}
			});
		},
		async fastJoin() {
			// 快速加入按鈕
			const self = this;
			if (self.bankProCode != null) {
				const r = await self.$api.getProFastSelected({
					bankProCode: self.bankProCode,
					pfcatCode: self.newPfcatCode
				});
				const proCodes = _.map(self.proList, 'proCode'); // 取出已加入的商品列表的proCode list
				if (r.data.content.length > 0) {
					// 有找到該商品
					if (proCodes.includes(r.data.content[0].proCode)) {
						self.$bi.alert(r.data.content[0].proName + '已加入商品列表');
					}
					else {
						r.data.content[0].modifyDt = null;
						r.data.content[0].createDt = null;
						self.proList.push(r.data.content[0]);
						self.mainLevelYn = new Array(self.proList.length);
						_.forEach(self.proList, function (item, index) {
							self.mainLevelYn[index] = item.mainLevelYn == 'Y';
						});
					}
					self.bankProCode = null;
				}
				else {
					self.$bi.alert('查無此商品代號');
				}
			}
			else {
				self.$bi.alert('請輸入商品代號');
			}
		},
		// 商品查詢按鈕跳出 model
		proSearchModel: function () {
			const self = this;
			self.allCheckbox = false;
			const proPfcats = _.find(self.proPfcatsList, { pfcatCode: self.newPfcatCode });
			self.pfcatName = proPfcats.pfcatName;
			self.getIssuersMenu(); // 發行機構選項
			self.getProTypeMenu(self.newPfcatCode); // 商品次類選項
			self.proName = null;
			self.bankProCode = null;
			self.proTypeCode = null;
			self.assetcatCode = null;
			self.issuerCode = null;
			self.proSearchList = [];
			self.isOpenProModal = true;
		},
		gotoProPage: function (page) {
			this.proPageable.page = page;
			this.allCheckbox = false;
			this.proSearch(page);
		},

		// 商品查詢model查詢
		proSearch(_page) {
			const self = this;
			const page = _.isNumber(_page) ? _page : self.proPageable.page;
			let url = '';
			url += '?page=' + page + '&size=' + self.proPageable.size;
			url += '&sort=' + self.proPageable.sort + ',' + self.proPageable.direction;
			self.$refs.searchQueryForm.validate().then(function (pass) {
				if (pass.valid) {
					self.$api
						.getProFastSelected(
							{
								pfcatCode: self.newPfcatCode,
								proName: self.proName,
								bankProCode: self.bankProCode,
								protypeCode: self.proTypeCode,
								assetcatCode: self.assetcatCode,
								issuerCode: self.issuerCode
							},
							url
						)
						.then(function (r) {
							if (r.data.content.length > 0) {
								self.proSearchList = r.data;
								const listLength = self.proSearchList.length;
								self.proCheckbox = [];
								for (let i = 0; i < listLength; i++) {
									self.proCheckbox[i] = false;
								}
							}
							else {
								self.proSearchList = [];
								self.$bi.alert('查無商品');
							}
						});
				}
			});
		},
		// 商品查詢model 加入商品
		addPro() {
			const self = this;
			const listLength = self.proSearchList.content.length;
			const proCodes = _.map(self.proList, 'proCode'); // 取出已加入的商品列表的proCode list
			for (let i = 0; i < listLength; i++) {
				if (self.proCheckbox[i]) {
					if (proCodes.includes(self.proSearchList.content[i].proCode)) {
						self.$bi.alert(self.proSearchList.content[i].proName + ' 已加入商品列表');
					}
					else {
						self.proSearchList.content[i].modifyDt = null;
						self.proSearchList.content[i].createDt = null;
						self.proList.push(self.proSearchList.content[i]);
						self.mainLevelYn = new Array(self.proList.length);
						_.forEach(self.proList, function (item, index) {
							self.mainLevelYn[index] = item.mainLevelYn == 'Y';
							self.$bi.message(self.proSearchList.content[i].proName + ' 加入成功');
						});
					}
				}
			}
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.totalSearch(page);
		},
		//  精選商品總表查詢
		totalSearch(_page) {
			const self = this;
			const page = _.isNumber(_page) ? _page : self.pageable.page;
			let url = '';
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;
			self.$refs.queryForm.validate().then(async function (pass) {
				if (pass.valid) {
					if (self.startDate > self.endDate) {
						self.$bi.alert('查詢日期區間(起)不可大於查詢日期區間(迄)。');
						return;
					}
					console.log('getProSelectedCombs', self.pfcatCode, self.selprocatCode, self.startDate, self.endDate);
					const r = await self.$api.getProSelectedCombs(
						{
							pfcatCode: self.pfcatCode,
							selprocatCode: self.selprocatCode,
							startDate: self.startDate,
							endDate: self.endDate
						},
						url
					);
					if (r.data.content.length > 0) {
						self.searchList = r.data;
					}
					else {
						self.searchList = [];
						self.$bi.alert('查無資料');
					}
				}
			});
		},
		gotohistoryPage: function (page) {
			this.pageable.page = page;
			this.historySearch(page);
		},
		// 精選商品歷史紀錄查詢
		historySearch(_page) {
			const self = this;
			self.searchList = [];
			const page = _.isNumber(_page) ? _page : self.pageable.page;
			let url = '';
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;
			self.$refs.queryForm.validate().then(async function (pass) {
				if (pass.valid) {
					if (self.startDate > self.endDate) {
						self.$bi.alert('查詢日期區間(起)不可大於查詢日期區間(迄)。');
						return;
					}
					const r = await self.$api.getProSelectedLog(
						{
							pfcatCode: self.pfcatCode,
							selprocatCode: self.selprocatCode,
							startDate: self.startDate,
							endDate: self.endDate
						},
						url
					);
					if (r.data.content.length > 0) {
						self.searchList = r.data;
					}
					else {
						self.$bi.alert('查無資料');
						self.searchList = [];
					}
				}
			});
		},
		showAlert(item) {
			this.$bi.message(item.stepContent);
		},
		// 檢視
		async preview(item) {
			const self = this;
			const data = {};
			if (item.status === 'P' || !self.tabSwitch) {
				// 待審核
				data.selproId = null;
				data.eventId = item.eventId;
			}
			else {
				// 通過審核 資料來源不同
				data.selproId = item.selproId;
			}
			const r = await self.$api.getProSelected(data);
			self.viewSelectPro = r.data;
			self.isOpenMyModal = true;
		},
		// 編輯
		async edit(item) {
			const self = this;
			self.editStatus = true; // 編輯狀態
			self.createBoolean = true;
			self.step2Boolean = true;
			self.pfcatCode = null; // 下拉 商品主類
			self.selprocatCode = null; // 精選商品類別
			self.startDate = null; // 上架日期-起
			self.endDate = null; // 上架日期-迄
			self.searchList = [];
			self.selproId = item.selproId;
			const r = await self.$api.getProSelected({
				selproId: item.selproId,
				eventId: item.eventId
			});
			const proItem = r.data;
			self.newPfcatCode = proItem.pfcatCode; // 下拉 商品主類
			self.newSelprocatCode = proItem.selprocatCode; // 精選商品類別
			self.selproName = proItem.selproName; // 精選商品套裝名稱
			self.newStartDate = proItem.startDate.replaceAll('/', '-'); // 上架日期-起
			self.newEndDate = proItem.endDate.replaceAll('/', '-'); // 上架日期-迄
			self.proList = proItem.proSelectedMaps;
			self.mainLevelYn = new Array(self.proList.length);
			_.forEach(self.proList, function (item, index) {
				self.mainLevelYn[index] = item.mainLevelYn == 'Y';
			});
		},
		// 發行機構選項
		getIssuersMenu: async function () {
			const self = this;

			const tranPrdtypeCodes = [];
			if (self.pfcatCode && self.pfcatCode != '') {
				tranPrdtypeCodes.push(self.pfcatCode);
			}
			else {
				if (self.platCode != '') {
					if (self.platCode == 'TRU') {
						tranPrdtypeCodes.push('TFUND');
						tranPrdtypeCodes.push('TFB');
						tranPrdtypeCodes.push('TETF');
						tranPrdtypeCodes.push('TSP');
					}
					else if (self.platCode == 'FIN') {
						tranPrdtypeCodes.push('FFB');
						tranPrdtypeCodes.push('FDCI');
						tranPrdtypeCodes.push('FSP');
					}
					else if (self.platCode == 'SEC') {
						tranPrdtypeCodes.push('SETFD');
						tranPrdtypeCodes.push('SETFF');
						tranPrdtypeCodes.push('SFB');
						tranPrdtypeCodes.push('SSP');
					}
				}
			}

			self.issuers = [];
			const ret = await self.$api.getIssuersMenuApi({
				tranPrdtypeCodes: tranPrdtypeCodes.join()
			});
			self.issuersMenu = ret.data;
		},

		// 商品次類選項
		async getProTypeMenu(pfcatCode) {
			const self = this;
			const r = await self.$api.getProTypeListApi({
				pfcatCode: pfcatCode
			});
			self.proTypeMenu = r.data;
		},
		// 新建刪除商品
		delPro(item) {
			const self = this;
			self.$bi.confirm('確認刪除本精選商品?', {
				event: {
					confirmOk: function () {
						const index = self.proList.findIndex(f => f.bankProCode === item.bankProCode);
						if (index != -1) {
							self.proList.splice(index, 1);
						}
						self.mainLevelYn = new Array(self.proList.length);
						_.forEach(self.proList, function (item, index) {
							self.mainLevelYn[index] = item.mainLevelYn == 'Y';
						});
						self.$bi.alert('刪除完成');
					}
				}
			});
		},
		// 歷史紀錄刪除
		async del(item) {
			const self = this;
			const r = await self.$api.deleteProSelectedLog({
				eventId: item.eventId
			});
			if (r.data > 0) {
				self.$bi.alert('已刪除該筆歷史紀錄');
				self.historySearch();
			}
		},
		// 刪除此精選商品按鈕
		dleProSelect() {
			const self = this;
			self.$bi.confirm('確認刪除本精選商品?', {
				event: {
					confirmOk: async function () {
						const pro = [];
						_.forEach(self.proList, function (item) {
							const proItem = {
								proCode: item.proCode
							};
							pro.push(proItem);
						});
						const r = await self.$api.postProSelected({
							pfcatCode: self.newPfcatCode, // 建立精選推薦商品 商品主類
							selprocatCode: self.newSelprocatCode, // 建立精選推薦商品 精選商品類別
							selproName: self.selproName, // 建立精選推薦商品 精選商品套裝名稱
							startDate: self.newStartDate, // 建立精選推薦商品 上架日期-起
							endDate: self.newEndDate, // 建立精選推薦商品 上架日期-迄
							selproId: self.selproId, // 精選商品套裝代碼
							proSelectedMapLog: self.proList, // 建立精選推薦商品 商品列表
							actionCode: 'D' // 狀態
						});
						this.$swal
							.fire({
								icon: 'success',
								text: '提交審核成功。',
								showCloseButton: true,
								confirmButtonText: '確認',
								buttonsStyling: false,
								customClass: {
									confirmButton: 'btn btn-success'
								}
							})
							.then(function () {
								this.$router.push('/pro/packagePrd');
							});
					}
				}
			});
		},
		// 提交審核
		submit() {
			const self = this;
			const pro = [];
			if (self.proList.length < 1) {
				self.$bi.alert('請至少選擇一筆商品');
				return false;
			}
			if (self.newStartDate > self.newEndDate) {
				self.$bi.alert('上架日期區間(起)不可大於上架日期區間(迄)。');
				return false;
			}
			self.$refs.stepForm.validate().then(async function (pass) {
				if (pass.valid) {
					_.forEach(self.proList, function (item, index) {
						item.mainLevelYn = self.mainLevelYn[index]; // 將勾選資料塞進要傳到後端的物件裡
						const proItem = {
							proCode: item.proCode
						};
						pro.push(proItem);
					});
					const r = await self.$api.postProSelected({
						pfcatCode: self.newPfcatCode, // 建立精選推薦商品 商品主類
						selprocatCode: self.newSelprocatCode, // 建立精選推薦商品 精選商品類別
						selproName: self.selproName, // 建立精選推薦商品 精選商品套裝名稱
						startDate: self.newStartDate, // 建立精選推薦商品 上架日期-起
						endDate: self.newEndDate, // 建立精選推薦商品 上架日期-迄
						selproId: self.selproId, // 精選商品套裝代碼
						proSelectedMapLog: self.proList, // 建立精選推薦商品 商品列表
						actionCode: self.editStatus ? 'M' : 'A' // 處理狀態
					});
					self.$bi.alert('新增完成', {
						confirm: function () {
							this.$router.push('/pro/packagePrd');
						}
					});
					self.pfcatCode = null; // 下拉 商品主類
					self.selprocatCode = null; // 精選商品類別
					self.startDate = null; // 上架日期-起
					self.endDate = null; // 上架日期-迄
					// self.cancel();
					self.tabSwitch = true;
					return;
				}
			});
		},
		// 進入編輯或新增後，點取消
		cancel() {
			const self = this;
			this.$router.push('/pro/packagePrd');
			return;
		},
		closeModal: function (modalName) {
			const self = this;
			self.isOpenModal[modalName] = false;
		},
		fundModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.fundModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal.fund = true;
		},
		etfModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.etfModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.etfModalRef.getEtfDetail(proCode); // 商品資訊/基金資料
			self.$refs.etfModalRef.getEtfStockHold(proCode); // 商品資訊/ETF持股
			self.$refs.etfModalRef.getEtfPrice(proCode); // 商品資訊/價格分析資料
			self.$refs.etfModalRef.getPricesChartData(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.etfModalRef.getEtfProfileNameMenu(); // 商品資訊/績效分析
			self.$refs.etfModalRef.getEtfProfileBenchmarksMenu(); // 商品資訊/績效分析
			self.isOpenModal.etf = true;
		},
		bondModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.bondModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.bondModalRef.getBondPriceAna(proCode);
			self.$refs.bondModalRef.getPricesChartData(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.bondModalRef.getBondPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.bond = true;
		},
		pfdModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.pfdModalRef.getProInfo(proCode, pfcatCode);
			//			self.$refs.pfdModalRef.getEtfDetail(proCode); // 商品資訊/基金資料
			self.isOpenModal.pfd = true;
		},
		spModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.spModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.spModalRef.getSpPriceAna(proCode); // 商品資訊/淨值分析資料
			self.$refs.spModalRef.getSpNets(proCode, 'Y', -1.0); // 商品資訊/淨值分析圖表
			self.$refs.spModalRef.getSpPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.sp = true;
		},
		insModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.insModalRef.getProInfo(proCode, pfcatCode);
			self.isOpenModal.ins = true;
		},
		dciModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.dciModalRef.getProInfo(proCode, pfcatCode); // 商品基本資料
			self.$refs.dciModalRef.getDciPriceAna(proCode); // 商品資訊/價格分析資料
			self.$refs.dciModalRef.getDciNets(proCode, 'Y', -1.0); // 商品資訊/價格分析圖表
			self.$refs.dciModalRef.getDciPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.dci = true;
		},
		secModalHandler: function (proCode, pfcatCode) {
			const self = this;
			self.$refs.secModalRef.getProInfo(proCode, pfcatCode);
			self.$refs.secModalRef.getSecPriceAna(proCode); // 商品資訊/淨值分析資料
			self.$refs.secModalRef.getSecNets(proCode, 'Y', -1.0); // 商品資訊/淨值分析圖表
			self.$refs.secModalRef.getSecPerformances(proCode, 'Y', -1.0, null); // 商品資訊/績效分析圖表
			self.isOpenModal.sec = true;
		}
	}
};
</script>
