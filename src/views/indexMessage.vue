<template>
	<div class="modal-dialog modal-lg modal-dialog-centered">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">
					{{ $t('core.announcementMessage') }}
				</h4>
				<button type="button" class="btn-expand">
					<i class="bi bi-arrows-fullscreen" />
				</button>
				<button
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				/>
			</div>
			<div v-if="selMsg != null" class="modal-body">
				<table class="table table-bordered">
					<caption>{{ $t('core.announcementType') }}</caption>
					<tbody>
						<tr>
							<th>{{ $t('core.announcementCategory') }}</th>
							<td>{{ selMsg.msgName }}</td>
						</tr>
					</tbody>
				</table>

				<table class="table table-bordered">
					<caption>{{ $t('core.announcementContent') }}</caption>
					<tbody>
						<tr>
							<th class="wd-15p">
								{{ $t('gen.importance') }}
							</th>
							<td>{{ subTypeName(selMsg.importantYn) }}</td>
						</tr>
						<tr>
							<th>{{ $t('gen.mainCategory') }}</th>
							<td>{{ selMsg.mainCatName }}</td>
						</tr>
						<tr>
							<th>{{ $t('gen.subCategory') }}</th>
							<td>{{ selMsg.subCatName }}</td>
						</tr>
						<tr>
							<th>{{ $t('gen.validDate') }}</th>
							<td>{{ $t('core.startDate') }} {{ selMsg.validBgnDt }} ~ {{ $t('core.endDate') }} {{ selMsg.validEndDt }}</td>
						</tr>
						<tr>
							<th><span>{{ $t('gen.announcementTitle') }}</span></th>
							<td>{{ selMsg.msgTitle }}</td>
						</tr>
						<tr>
							<th>{{ $t('core.announcementContent') }}</th>
							<td>{{ selMsg.msgContent }}</td>
						</tr>
						<tr>
							<th>{{ $t('gen.showOnHomepage') }}</th>
							<td>{{ subTypeName(selMsg.showYn) }}</td>
						</tr>
						<tr>
							<th>{{ $t('gen.uploadFile') }}</th>
							<td>
								<ul v-for="file in selMsg.fileList" class="list-group list-inline-tags">
									<li class="list-group-item">
										<ColoredLink @click.stop="viewFile(file)">
											<span>{{ file.showName }}</span>
										</ColoredLink>
									</li>
								</ul>
							</td>
						</tr>
						<tr>
							<th>{{ $t('gen.link') }}</th>
							<td>
								<a :href="selMsg.favoriteLink">{{ selMsg.favoriteLink }}</a>
							</td>
						</tr>
					</tbody>
				</table>

				<table class="table table-bordered">
					<caption>{{ $t('gen.maintenanceInfo') }}</caption>
					<tbody>
						<tr>
							<th>{{ $t('gen.creator') }}</th>
							<td>{{ selMsg.createUser }}</td>
						</tr>
						<tr>
							<th>{{ $t('gen.createDate') }}</th>
							<td>{{ selMsg.createDt }}</td>
						</tr>
						<tr>
							<th>{{ $t('gen.lastMaintainer') }}</th>
							<td>{{ selMsg.modifyUser }}</td>
						</tr>
						<tr>
							<th>{{ $t('gen.lastMaintenanceDate') }}</th>
							<td>{{ selMsg.modifyDt }}</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div class="modal-footer">
				<button
					data-bs-dismiss="modal"
					aria-label="Close"
					type="button"
					class="btn btn-white"
				>
					{{ $t('gen.closeWindow') }}
				</button>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash';
export default {
	props: {
		cusCode: null
	},
	data: function () {
		return {
			selMsg: {},
			selOptionYn: [
			]
		};
	},
	watch: {},
	methods: {
		getView: async function (msgId) {
			const self = this;
			const ret = await self.$api.getGenMessageApi({
				msgId: msgId
			});
			if (ret.data) {
				self.selMsg = _.clone(ret.data);
			}
		},
		subTypeName: function (codeValue) {
			if (!codeValue) return '';
			return self.selOptionYn?.find(option => option.codeValue === codeValue)?.codeName ?? codeValue;
		},
		viewFile: async function (file) {
			this.$api.downloadFileApi({
				fileId: file.msgFileId,
				fileType: 'GenFilesLog'
			});
		}
	}
};
</script>
