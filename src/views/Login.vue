<template>
	<div class="container-body">
		<div class="container">
			<div class="logo">
				<img src="@/assets/images/logo/logo-ifa.svg" alt="IFA Logo">
			</div>
			<i18nSelector class="mb-4 justify-content-center" />
			<h2>{{ $t('core.memberLogin') }}</h2>

			<Form v-slot="{ errors }" ref="valid" @submit="login">
				<!--TODO: 改成設定黨決定是否啟用tenantNumber-->
				<!-- <div class="input-group">
					<label for="company-code">{{ $t('core.merchantNumber') }}</label>
					<Field
						type="email"
						id="company-code"
						name="tenantNumber"
						v-model="tenantNumber"
						rules="required"
						:label="$t('core.merchantNumber')"
						:class="{
							'is-invalid': errors.tenantNumber
						}"
					></Field>
					<div class="invalid-feedback" v-show="errors.tenantNumber">
						{{ errors.tenantNumber }}
					</div>
				</div> -->
				<div class="input-group">
					<label for="login-email">{{ $t('core.account') }}</label>
					<Field
						id="login-email"
						v-model="userCode"
						type="email"
						name="userCode"
						placeholder="<EMAIL>"
						autocomplete="username"
						rules="required"
						:label="$t('core.account')"
						:class="{
							'is-invalid': errors.userCode
						}"
					/>
					<div v-show="errors.userCode" class="invalid-feedback">
						{{ errors.userCode }}
					</div>
				</div>
				<div class="input-group">
					<label for="login-password">{{ $t('core.password') }}</label>
					<Field
						id="login-password"
						v-model="pwd"
						type="password"
						name="pwd"
						:placeholder="$t('core.pleaseEnterPassword')"
						autocomplete="current-password"
						rules="required"
						:label="$t('core.password')"
						:class="{
							'is-invalid': errors.pwd
						}"
					/>
					<div v-show="errors.pwd" class="invalid-feedback">
						{{ errors.pwd }}
					</div>
				</div>
				<div class="input-group">
					<vue-recaptcha
						:sitekey="v2Sitekey"
						size="normal"
						theme="light"
						hl="zh-TW"
						@verify="recaptchaVerified"
					/>
				</div>
				<button class="btn btn-primary" type="submit">
					{{ $t('core.login') }}
				</button>
				<a href="#" class="btn btn-outline-primary" type="button">{{ $t('core.forgotPassword') }}</a>
			</Form>
		</div>
	</div>
</template>

<script>
import { Form, Field } from 'vee-validate';
import vueRecaptcha from 'vue3-recaptcha2';
import { setToken } from '@/utils/auth.js';
import i18nSelector from '@/views/components/i18nSelector.vue';

export default {
	components: {
		// eslint-disable-next-line vue/no-reserved-component-names
		Form,
		Field,
		vueRecaptcha,
		i18nSelector
	},
	data: function () {
		return {
			userCode: import.meta.env.VITE_AUTOFILL_USERNAME ?? '', // [[${userCode ?: null}]],
			pwd: import.meta.env.VITE_AUTOFILL_PASSWORD ?? '',
			tenantNumber: import.meta.env.VITE_AUTOFILE_TENENT ?? '',

			v2Sitekey: '6Lc5sxUrAAAAAEANxF1wda0mrtYBatIt6LS-8OOz',
			recaptchaResponse: null
			// valid: false, //[[${valid ?: false}]],
			// isUserAccountSwitch: false, //[[${isUserAccountSwitch ?: false}]],
			// userDeputies: null,
			// userPositions: null,
			// selectedUserCode: null,
			// selectedPosCode: null
		};
	},

	watch: {},
	methods: {
		recaptchaVerified: async function (respToke) {
			const ret = await this.$api.recaptchaVerifiedApi(respToke); // Return token and pass token to backend for verification
			this.recaptchaResponse = ret.data.success;
		},
		login: async function () {
			if (import.meta.env.VITE_API_MOCK_ENABLED !== 'true' && !this.recaptchaResponse) {
				this.$swal.fire({
					title: 'error',
					text: this.$t('core.pleaseCheckImNotRobot'),
					icon: 'error'
				});
				return;
			}

			const ret = await this.$api.loginApi(this.tenantNumber, this.userCode, this.pwd);

			if (ret.data.accessToken && ret.data.refreshToken) {
				setToken('accessToken', ret.data.accessToken);
				setToken('refreshToken', ret.data.refreshToken);
				this.$router.push('/selectpos');
			}
			else {
				this.$swal.fire({
					title: 'error',
					text: this.$t('core.accountOrPasswordError'),
					icon: 'error'
				});
			}
		}
	}
};
</script>

<style scoped>
@import '../assets/css/bi/login.css';
</style>
