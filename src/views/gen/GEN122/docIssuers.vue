<template>
	<div class="row">
		<div class="col-12">
			<div class="tab-nav-main">
				<div class="tab-content">
					<div class="tab-pane fade active show">
						<div class="card card-table mb-3">
							<div class="card-header">
								<h4>{{ $t('gen.documentList') }}</h4>
								<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
							</div>
							<div class="table-responsive">
								<table class="table table-RWD table-hover table-bordered table-padding">
									<thead>
										<tr>
											<th width="40%">
												{{ $t('gen.documentTitle') }}
											</th>
											<th width="10%">
												{{ $t('gen.effectiveDate') }}
											</th>
											<th width="10%">
												{{ $t('gen.expiryDate') }}
											</th>
											<th width="12%">
												{{ $t('gen.canProvideToCustomers') }}
											</th>
											<th width="9%">
												{{ $t('gen.urgencyLevel') }}
											</th>
											<th width="9%">
												{{ $t('gen.issuer') }}
											</th>
											<th width="9%">
												{{ $t('gen.view') }}
											</th>
											<th width="10%">
												{{ $t('gen.relatedAttachments') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="data in pageData.content">
											<td :data-th="$t('gen.documentTitle')">
												{{ data.docName }}
											</td>
											<td :data-th="$t('gen.effectiveDate')">
												{{ $filters.formatDate(data.validDt) }}
											</td>
											<td :data-th="$t('gen.expiryDate')">
												{{ $filters.formatDate(data.expireDt) }}
											</td>
											<td :data-th="$t('gen.canProvideToCustomers')">
												{{ data.showCusName }}
											</td>
											<td :data-th="$t('gen.urgencyLevel')">
												{{ data.priorityName }}
											</td>
											<td :data-th="$t('gen.issuer')">
												{{ data.issuerList?.map((item) => item.issuerName).join('；') }}
											</td>
											<td :data-th="$t('gen.view')" class="text-center">
												<button
													type="button"
													class="btn btn-dark btn-icon"
													:title="$t('gen.view')"
													@click="viewDoc(data)"
												>
													<i class="bi bi-search" />
												</button>
											</td>
											<td :data-th="$t('gen.relatedAttachments')" class="text-center">
												<a v-for="file in data.fileList" href="#" @click="viewFile(file.docFileId)">{{
													file.showName
												}}</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- Modal -->
	<vue-modal :is-open="isOpenModal" @close="closeModal">
		<template #content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('gen.documentView') }}
						</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()">
							<i
								class="bi bi-arrows-fullscreen"
							/>
						</button>
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click.prevent="props.close()"
						/>
					</div>
					<div class="modal-body">
						<div class="caption">
							{{ $t('gen.documentCategory') }}
						</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('gen.documentType') }}
									</th>
									<td>{{ selectedItem.docCatName }}</td>
								</tr>
							</tbody>
						</table>
						<div class="caption">
							{{ $t('gen.documentContent') }}
						</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('gen.documentTitle') }}
									</th>
									<td>{{ selectedItem.docName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.effectiveDate') }}</th>
									<td>{{ $filters.formatDate(selectedItem.validDt) }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.expiryDate') }}</th>
									<td>{{ $filters.formatDate(selectedItem.expireDt) }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.urgencyLevel') }}</th>
									<td>{{ selectedItem.priorityName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.canProvideToCustomers') }}</th>
									<td>{{ selectedItem.showCusName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.summary') }}</th>
									<td>{{ selectedItem.docDesc }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.additionalDocuments') }}</th>
									<td>
										<a
											v-for="file in selectedItem.fileInfo"
											href="#"
											class="link-underline"
											@click="viewFile(file.docFileId)"
										>{{
											file.showName
										}}</a><br>
									</td>
								</tr>
								<tr>
									<th>{{ $t('gen.issuer') }}</th>
									<td>{{ selectedItem.issuerInfo?.map((item) => item.issuerName).join('、') }}</td>
								</tr>
							</tbody>
						</table>
						<div class="caption">
							{{ $t('gen.maintenanceInfo') }}
						</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('gen.creator') }}
									</th>
									<td>{{ selectedItem.createBy }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.createDate') }}</th>
									<td>{{ $filters.formatDate(selectedItem.createDt) }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.lastMaintainer') }}</th>
									<td>{{ selectedItem.modifyBy }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.lastMaintenanceDate') }}</th>
									<td>{{ $filters.formatDate(selectedItem.modifyDt) }}</td>
								</tr>
							</tbody>
						</table>
					</div>

					<div id="appointmentFooter" class="modal-footer">
						<input
							id="appointmentCloseButton"
							name="btnClose"
							class="btn btn-white"
							type="button"
							:value="$t('gen.close')"
							@click.prevent="props.close()"
						>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- Modal End -->
</template>
<script>
import vueModal from '@/views/components/model.vue';
import vuePagination from '@/views/components/pagination.vue';
import _ from 'lodash';
export default {
	components: {
		vueModal,
		vuePagination
	},
	data: function () {
		return {
			// Data
			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'DOC_ID',
				direction: 'ASC'
			},
			selectedItem: {},
			// Modal
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand'
		};
	},
	mounted: function () {
		const self = this;
		this.getPageData();
	},
	methods: {
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData();
		},
		getPageData: async function () {
			const page = this.pageable.page;
			const self = this;
			const url = _.toPageUrl('', page, self.pageable);
			const ret = await self.$api.getDocIssuersPageData({ queryString: url });
			self.pageData = ret.data;
		},
		viewDoc: async function (docIssuer) {
			const self = this;
			const ret = await self.$api.getViewSelDoc({
				docCat: 'ISSUER',
				docId: docIssuer.docId
			});
			if (!ret.data?.length > 0) return;
			self.selectedItem = ret.data[0];
			self.isOpenModal = true;
		},
		viewFile: function (fileId) {
			const self = this;
			self.$api.downloadFileApi({
				fileId: fileId,
				fileType: 'GenDocFiles'
			});
		},
		changeModalSize: function () {
			const self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			}
			else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		closeModal: function () {
			const self = this;
			self.isOpenModal = false;
		},
		openModal: function () {
			const self = this;
			self.isOpenModal = true;
		}
	}
};
</script>
