<template>
	<div class="tab-nav-main">
		<Tabs v-model="tab" :tabs variant="pill" />
		<component :is="componentId" />
	</div>
	<!--頁面內容 end-->
</template>
<script>
import { useTabs } from '@/composables/useTabs';
import { defineAsyncComponent } from 'vue';

export default {
	components: {
		vueBbsMgt: defineAsyncComponent(() => import('./include/bbsMgt.vue')),
		vueBbsMgtSer: defineAsyncComponent(() => import('./include/bbsMgtSer.vue'))
	},
	setup() {
		return useTabs('M40-01');
	},
	computed: {
		msgId() {
			return this.$route.params.msgId;
		}
	},
	watch: {
		msgId(val) {
			// 如果msgId有值，則切換到編輯組件
			if (val) this.tab = this.idCodeMap['vue-bbs-mgt'];
			// 否則切換到查詢組件
			else this.tab = this.idCodeMap['vue-bbs-mgt-ser'];
		},
		tab(val) {
			// 如果還沒有msgId 或者已經在查詢組件，則不需要切換路由
			if (this.msgId == null || this.codeIdMap[val] !== 'vue-bbs-mgt-ser') return;
			this.$router.replace({ name: 'bbsMgt' });
		}
	}
};
</script>
