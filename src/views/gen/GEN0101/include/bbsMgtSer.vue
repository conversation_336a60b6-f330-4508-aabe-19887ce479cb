<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form">
			<div class="card-header">
				<h4>{{ $t('gen.announcementSettings') }}</h4>
				<span class="tx-square-bracket">{{ $t('gen.requiredFields') }}</span>
			</div>
			<div id="card-body" class="card-body collapse show">
				<div class="row g-3 align-items-end">
					<vue-form v-slot="{ errors }" ref="queryForm">
						<div class="form-row">
							<div class="form-group col-md-4">
								<label class="form-label">{{ $t('gen.messageType') }}</label>
								<select v-model="msgCode" class="form-select" name="msgCode">
									<option selected :value="null">
										{{ $t('gen.all') }}
									</option>
									<option v-for="selMessage in selMessageMap" :value="selMessage.msgCode">
										{{ selMessage.msgName }}
									</option>
								</select>
							</div>
							<div class="form-group col-md-4">
								<label class="form-label">{{ $t('gen.mainCategory') }}</label>
								<select v-model="mainCatCode" name="mainCatCode" class="form-select">
									<option :value="null">
										{{ $t('gen.all') }}
									</option>
									<option v-for="item in selMsgMainCat" :value="item.catCode">
										{{ item.catName }}
									</option>
								</select>
							</div>
							<div class="form-group col-md-4">
								<label class="form-label">{{ $t('gen.subCategory') }}</label>
								<select
									v-model="subCatCode"
									name="subCatCode"
									class="form-select"
									:disabled="!mainCatCode"
								>
									<option :value="null">
										{{ $t('gen.pleaseSelect') }}
									</option>
									<option v-for="item in selMsgSubCat" :value="item.catCode">
										{{ item.catName }}
									</option>
								</select>
							</div>
							<div class="form-group col-md-12">
								<label class="form-label tx-require">{{ $t('gen.validDate') }}</label>
								<div class="flex-1">
									<PeriodInput v-model:from="validBgnDt" v-model:to="validEndDt" :invalid="errors.validBgnDt || errors.validEndDt" />
									<vue-field
										v-model="validBgnDt"
										name="validBgnDt"
										rules="required"
										hidden
										:label="$t('gen.from')"
									/>
									<vue-field
										v-model="validEndDt"
										name="validEndDt"
										rules="required"
										hidden
										:label="$t('gen.to')"
									/>
									<span v-show="errors.validBgnDt || errors.validEndDt" class="text-danger col-md-12">
										{{ errors.validBgnDt }} {{ errors.validEndDt }}
									</span>
								</div>
							</div>
							<div class="form-group col-md-4">
								<label class="form-label">{{ $t('gen.bbsTitle') }}</label>
								<vue-field
									id="msgTitle"
									v-model="msgTitle"
									name="msgTitle"
									:label="$t('gen.bbsTitle')"
									type="text"
									class="form-control form-input"
								/>
							</div>
						</div>
						<div class="form-footer">
							<Button :label="$t('gen.search')" @click="gotoPage(0)" />
						</div>
					</vue-form>
				</div>
			</div>
			<div id="searchResult">
				<div class="card card-table">
					<div class="card-header">
						<h4>{{ $t('gen.searchResults') }}</h4>
						<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
					</div>
					<div class="table-responsive">
						<table class="table table-RWD table-bordered text-center">
							<thead>
								<tr>
									<th>{{ $t('gen.importance') }}</th>
									<th>{{ $t('gen.messageType') }}</th>
									<th>{{ $t('gen.mainCategory') }}</th>
									<th>{{ $t('gen.subCategory') }}</th>
									<th>{{ $t('gen.validDate') }}</th>
									<th>{{ $t('gen.bbsTitle') }}</th>
									<th>{{ $t('gen.creator') }}</th>
									<th>{{ $t('gen.execute') }}</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in pageData.content">
									<td :class="{ 'text-center tx-red bi-exclamation': item.importantYn == 'Y' }" :data-th="$t('gen.importance')" />
									<td :data-th="$t('gen.messageType')">
										{{ item.msgName }}
									</td>
									<td :data-th="$t('gen.mainCategory')">
										{{ item.mainCatName }}
									</td>
									<td :data-th="$t('gen.subCategory')">
										{{ item.subCatName }}
									</td>
									<td :data-th="$t('gen.validDate')">
										{{ item.validBgnDt }} ~ {{ item.validEndDt }}
									</td>
									<td :data-th="$t('gen.bbsTitle')">
										{{ item.msgTitle }}
									</td>
									<td :data-th="$t('gen.creator')">
										{{ item.createUser }}
									</td>
									<td :data-th="$t('gen.execute')">
										<button
											type="button"
											class="btn btn-dark btn-glow btn-icon"
											:title="$t('gen.view')"
											@click="getView(item.msgId)"
										>
											<i class="bi bi-search" />
										</button>
										<button
											type="button"
											class="btn btn-info btn-glow btn-icon btn-edit"
											:data-bs-original-title="$t('gen.edit')"
											@click="editPage(item)"
										>
											<i class="bi bi-pen" />
										</button>
										<button
											type="button"
											class="btn btn-danger btn-glow btn-icon"
											:data-bs-original-title="$t('gen.delete')"
											@click="doDelete(item)"
										>
											<i class="bi bi-trash" />
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!--Detail Modal-->
	<vue-modal :is-open="isOpenModal" @close="closeModal">
		<template #content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('gen.bbsMessage') }}
						</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()">
							<i
								class="bi bi-arrows-fullscreen"
							/>
						</button>
						<button type="button" class="btn-close" @click.prevent="props.close()" />
					</div>
					<div class="modal-body">
						<table class="table table-bordered">
							<caption>
								{{ $t('gen.bbsCategory') }}
							</caption>
							<tbody>
								<tr>
									<th>{{ $t('gen.messageType') }}</th>
									<td>{{ selMsg.msgName }}</td>
								</tr>
							</tbody>
						</table>

						<table class="table table-bordered">
							<caption>
								{{ $t('gen.bbsContent') }}
							</caption>
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('gen.importance') }}
									</th>
									<td>{{ subTypeName(selMsg.importantYn) }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.mainCategory') }}</th>
									<td>{{ selMsg.mainCatName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.subCategory') }}</th>
									<td>{{ selMsg.subCatName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.validDate') }}</th>
									<td>{{ selMsg.validBgnDt }} ~ {{ selMsg.validEndDt }}</td>
								</tr>
								<tr>
									<th><span>{{ $t('gen.bbsTitle') }}</span></th>
									<td>{{ selMsg.msgTitle }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.bbsContent') }}</th>
									<td>{{ selMsg.msgContent }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.showOnHomepage') }}</th>
									<td>{{ subTypeName(selMsg.showYn) }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.uploadFile') }}</th>
									<td>
										<ul v-for="file in selMsg.fileList" class="list-group list-inline-tags">
											<li class="list-group-item">
												<a href="#" @click.prevent="viewFile(file.msgFileId)">
													<span>{{ file.showName }}</span>
												</a>
											</li>
										</ul>
									</td>
								</tr>
								<tr>
									<th>{{ $t('gen.link') }}</th>
									<td>
										<a :href="selMsg.favoriteLink">{{ selMsg.favoriteLink }}</a>
									</td>
								</tr>
							</tbody>
						</table>

						<table class="table table-bordered">
							<caption>
								{{ $t('gen.maintenanceInfo') }}
							</caption>
							<tbody>
								<tr>
									<th>{{ $t('gen.creator') }}</th>
									<td>{{ selMsg.createUser }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.creatorExt') }}</th>
									<td>{{ selMsg.createUserExt }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.createDate') }}</th>
									<td>{{ selMsg.createDt }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.lastMaintainer') }}</th>
									<td>{{ selMsg.modifyUser }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.maintainerExt') }}</th>
									<td>{{ selMsg.modifyUserExt }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.lastMaintenanceDate') }}</th>
									<td>{{ selMsg.modifyDt }}</td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-white" @click.prevent="props.close()">
							{{ $t('gen.closeWindow') }}
						</button>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
</template>
<script>
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import _ from 'lodash';
import vuePagination from '@/views/components/pagination.vue';
import moment from 'moment';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		vuePagination
	},
	data: function () {
		return {
			selMessageMap: [], // 公告類別資料
			selMsgMainCat: [], // 主分類資料
			selMsgSubCat: [], // 次分類資料
			selOptionYn: [], // convert option name

			msgCode: null,
			mainCatCode: null,
			subCatCode: null,
			validBgnDt: null,
			validEndDt: null,
			msgTitle: null,

			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'MSG_ID',
				direction: 'ASC'
			},
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand',
			// getView
			selMsg: {}
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo'])
	},
	watch: {
		mainCatCode: async function (newVal, oldVal) {
			const self = this;
			if (newVal) {
				self.selMsgSubCat = await self.getMessageCat('S', newVal);
			}
			else {
				self.selMsg.subCatCode = null;
			}
		}
	},
	mounted: async function () {
		const self = this;
		// initial
		self.getMessageMap();
		self.selMsgMainCat = await self.getMessageCat('M', null, self.selMsgMainCat);
		self.selOptionYn = await self.getAdmCodeDetail('OPTION_YN');
	},
	methods: {
		getAdmCodeDetail: async function (codeType) {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({ codeType: codeType });
			return ret.data;
		},
		getMessageMap: async function () {
			const self = this;
			const ret = await self.$api.getMessageMapApi();
			self.selMessageMap = ret.data;
		},
		getMessageCat: async function (catType, mainCatCode) {
			const self = this;
			const data = { catType: catType, mainCatCode: mainCatCode };
			const reqData = _.omitBy(data, value => _.isNil(value) || value === '');
			const ret = await self.$api.getGenMessageCat(reqData);
			return ret.data;
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function () {
			const self = this;
			self.$refs.queryForm.validate().then(async function (pass) {
				if (pass.valid) {
					if (moment(self.validBgnDt).isAfter(moment(self.validEndDt))) {
						this.$bi.alert(this.$t('gen.dateRangeStartCannotBeGreater'));
						return;
					}

					const ret = await self.$api.getBbsMgtPageData(
						self.pageable,
						{
							msgCode: self.msgCode,
							mainCatCode: self.mainCatCode,
							subCatCode: self.subCatCode,
							validBgnDt: _.formatDate(self.validBgnDt),
							validEndDt: _.formatDate(self.validEndDt),
							msgTitle: self.msgTitle
						}
					);
					self.pageData = ret.data;
				}
			});
		},
		getView: async function (msgId) {
			const self = this;
			const ret = await self.$api.getGenMessageApi({
				msgId: msgId
			});
			if (ret.data) {
				self.selMsg = _.clone(ret.data);
				self.isOpenModal = true;
			}
		},
		editPage: function (item) {
			if (item.lockYn === 'Y') {
				this.$bi.alert(this.$t('gen.cannotModifyPendingReview'));
				return;
			}
			this.$router.push({ name: 'bbsMgtParm', params: { msgId: item.msgId } });
		},
		doDelete: async function (item) {
			if (item.lockYn === 'Y') {
				this.$bi.alert(this.$t('gen.cannotModifyPendingReview'));
				return;
			}
			const msgId = item.msgId;
			const self = this;
			const ret = await self.$api.getGenMessageApi({
				msgId: msgId
			});
			if (ret.data) {
				const msgData = _.clone(ret.data);
				msgData.validBgnDt = moment(ret.data.validBgnDt).format('yyyy-MM-DD');
				msgData.validEndDt = moment(ret.data.validEndDt).format('yyyy-MM-DD');
				msgData.actionCode = 'D';
				msgData.fileObject = _.clone(ret.data.fileList);
				_.forEach(msgData.fileObject, function (file) {
					file.fileNo = file.msgFileId;
				});
				self.$swal
					.fire({
						icon: 'warning',
						text: self.$t('gen.confirmDelete'),
						showCloseButton: true,
						confirmButtonText: self.$t('gen.confirm'),
						cancelButtonText: self.$t('gen.cancel'),
						showCancelButton: true,
						buttonsStyling: false,
						customClass: {
							confirmButton: 'btn btn-success',
							cancelButton: 'btn btn-danger'
						},
						willOpen: function () {
							// 添加自定義的樣式來增加按鈕之間的間距
							$('.swal2-confirm').css('margin-right', '20px');
						}
					})
					.then(function (result) {
						if (result.isConfirmed) {
							// 提交送審
							self.bbmMaintainSave(msgData);
						}
					});
			}
		},
		bbmMaintainSave: async function (msgData) {
			const self = this;
			const ret = await self.$api.getBbmMaintainSaveApi();
			if (ret.data && ret.data.length > 0) {
				const bbma = ret.data[0];
				if (bbma.wfgRoleKind !== 'rule4' && bbma.wfgsRole !== self.userInfo.roleCode) {
					this.$bi.alert(this.$t('gen.noPermission'));
					return;
				}
				else {
					// 送審
					const formData = new FormData();
					const jsonStr = JSON.stringify(msgData);
					formData.append('json', new Blob([jsonStr], { type: 'application/json' }));
					let confirmText = self.$t('gen.delete');
					self.$api.postMsgLogApi(
						formData
					).then(function (ret) {
						let confirmStatus = 'success';
						if (typeof ret.data.msgId === 'undefined' || ret.data.msgId === null) {
							confirmText = confirmText + self.$t('gen.fail');
							confirmStatus = 'error';
						}
						else {
							confirmText = confirmText + self.$t('gen.success');
						}
						self.$swal
							.fire({
								icon: confirmStatus,
								text: confirmText,
								showCloseButton: true,
								confirmButtonText: self.$t('gen.confirm'),
								buttonsStyling: false,
								customClass: {
									confirmButton: 'btn btn-success'
								}
							})
							.then(function () {
								self.gotoPage(0);
							});
					});
				}
			}
		},
		subTypeName: function (codeValue) {
			const self = this;
			if (!_.isBlank(self.selOptionYn) && !_.isBlank(codeValue)) {
				return _.find(self.selOptionYn, { codeValue: codeValue }).codeName;
			}
			else {
				return codeValue;
			}
		},
		changeModalSize: function () {
			const self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			}
			else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		viewFile: function (targetFileId) {
			const self = this;
			self.$api.downloadFileApi({ fileId: targetFileId, fileType: 'GenFilesLog' });
		}
	}
};
</script>
