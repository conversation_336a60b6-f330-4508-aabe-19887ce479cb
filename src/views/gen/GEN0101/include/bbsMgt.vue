<template>
	<div role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form">
			<div class="card-header">
				<h4>{{ $t('gen.announcementSettings') }}</h4>
				<span class="tx-square-bracket">{{ $t('gen.requiredFields') }}</span>
			</div>
			<vue-form v-slot="{ errors, validate }" ref="queryForm">
				<div class="form-row">
					<table class="table table-bordered">
						<tbody>
							<tr>
								<th class="tx-require">
									{{ $t('gen.messageType') }}
								</th>
								<td>
									<template v-for="selMessage in selMessageMap">
										<div class="form-check form-check-inline">
											<vue-field
												:id="'selMessageMap_' + selMessage.msgCode"
												v-model="selMsg.msgCode"
												name="msgCode"
												type="radio"
												class="form-check-input has-validation"
												:value="selMessage.msgCode"
												:label="$t('gen.messageType')"
												:class="{ 'is-invalid': errors.msgCode }"
												rules="required"
											/>
											<label
												class="form-check-label"
												:for="'selMessageMap_' + selMessage.msgCode"
												style="margin-right: 3px"
											>{{
												selMessage.msgName
											}}</label>
										</div>
									</template>
									<span v-show="errors.msgCode" class="text-danger">{{ errors.msgCode }}</span>
								</td>
							</tr>
							<tr>
								<th class="tx-require">
									{{ $t('gen.importance') }}
								</th>
								<td>
									<template v-for="selImportent in selImportentYn">
										<div class="form-check form-check-inline">
											<vue-field
												:id="'selImportent_' + selImportent.codeValue"
												v-model="selMsg.importantYn"
												name="importantYn"
												type="radio"
												class="form-check-input has-validation"
												:value="selImportent.codeValue"
												:label="$t('gen.importance')"
												:class="{ 'is-invalid': errors.importantYn }"
												rules="required"
											/>
											<label
												class="form-check-label"
												:for="'selImportent_' + selImportent.codeValue"
												style="margin-right: 3px"
											>{{ selImportent.codeName }}</label>
										</div>
									</template>
									<span v-show="errors.importantYn" class="text-danger">{{ errors.importantYn }}</span>
								</td>
							</tr>
							<tr>
								<th>{{ $t('gen.mainCategory') }}</th>
								<td>
									<select v-model="selMsg.mainCatCode" name="mainCatCode" class="form-select">
										<option :value="null">
											{{ $t('gen.pleaseSelect') }}
										</option>
										<option v-for="item in selMsgMainCat" :value="item.catCode">
											{{ item.catName }}
										</option>
									</select>
								</td>
							</tr>
							<tr>
								<th>{{ $t('gen.subCategory') }}</th>
								<td>
									<select
										v-model="selMsg.subCatCode"
										name="subCatCode"
										class="form-select"
										:disabled="!selMsg.mainCatCode"
									>
										<option :value="null">
											{{ $t('gen.pleaseSelect') }}
										</option>
										<option v-for="item in selMsgSubCat" :value="item.catCode">
											{{ item.catName }}
										</option>
									</select>
								</td>
							</tr>
							<tr>
								<th class="tx-require">
									{{ $t('gen.validDate') }}
								</th>
								<td>
									<div class="input-group input-date">
										<vue-field
											id="validBgnDt"
											v-model="selMsg.validBgnDt"
											type="date"
											name="validBgnDt"
											size="13"
											:label="$t('gen.from')"
											class="form-control"
											maxlength="10"
											rules="required"
											:max="selMsg.validEndDt"
											:class="{ 'is-invalid': errors.validBgnDt }"
										/>
										<span class="input-group-text">~</span>
										<vue-field
											id="validEndDt"
											v-model="selMsg.validEndDt"
											type="date"
											name="validEndDt"
											size="13"
											:label="$t('gen.to')"
											class="form-control"
											maxlength="10"
											rules="required"
											:min="selMsg.validBgnDt"
											:class="{ 'is-invalid': errors.validEndDt }"
										/>
									</div>
									<span v-show="errors.validBgnDt || errors.validEndDt" class="text-danger">{{ errors.validBgnDt }} {{
										errors.validEndDt }}</span>
								</td>
							</tr>
							<tr>
								<th class="tx-require">
									{{ $t('gen.bbsTitle') }}
								</th>
								<td>
									<vue-field
										id="msgTitle"
										v-model="selMsg.msgTitle"
										name="msgTitle"
										:label="$t('gen.bbsTitle')"
										type="text"
										class="form-control form-input"
										:class="{ 'is-invalid': errors.msgTitle }"
										rules="required|max: 50"
									/>
									<span v-show="errors.msgTitle" class="text-danger">{{ errors.msgTitle }}</span>
								</td>
							</tr>
							<tr>
								<th>{{ $t('gen.bbsContent') }}</th>
								<td>
									<vue-field
										id="msgContent"
										v-model="selMsg.msgContent"
										name="msgContent"
										:label="$t('gen.bbsContent')"
										as="textarea"
										rows="8"
										class="textarea form-control"
										:class="{ 'is-invalid': errors.msgContent }"
										rules="max: 1000"
									/>
									<div class="tx-note">
										1000 {{ $t('gen.charactersAvailable') }}
									</div>
									<span v-show="errors.msgContent" class="text-danger">
										{{ errors.msgContent }}
									</span>
								</td>
							</tr>
							<tr>
								<th class="tx-require">
									{{ $t('gen.showOnHomepage') }}
								</th>
								<td>
									<template v-for="selOption in selOptionYn">
										<div class="form-check form-check-inline">
											<vue-field
												:id="'showYn_' + selOption.codeValue"
												v-model="selMsg.showYn"
												name="showYn"
												type="radio"
												class="form-check-input"
												:value="selOption.codeValue"
												:disabled="$_.includes(initialMsgCode, selMsg.msgCode)"
												:label="$t('gen.showOnHomepage')"
												:class="{ 'is-invalid': errors.showYn }"
												rules="required"
											/>
											<label
												class="form-check-label"
												:for="'showYn_' + selOption.codeValue"
												style="margin-right: 3px"
											>{{
												selOption.codeName
											}}</label>
										</div>
									</template>
									<span v-show="errors.msgCode" class="text-danger">{{ errors.showYn }}</span>
								</td>
							</tr>
							<tr>
								<th>{{ $t('gen.uploadFile') }}</th>
								<td>
									<vue-form v-slot="{ errors, validate }" ref="fileForm" class="col-lg-12">
										<div class="row g-2">
											<div class="input-group">
												<vue-field
													ref="uploadFile"
													name="uploadFile"
													type="file"
													class="form-control"
													:label="$t('gen.attachment')"
													:class="{ 'is-invalid': errors.uploadFile }"
													:rules="{
														required_file: true,
														mbSize: 15,
														extMgt: validExts,
														validateName: symbols,
														isOverSize: fileCntObj
													}"
													accept=".doc, .docx, .pdf, .xlsx, .txt"
													@change="handleChange($event)"
												/>
												<button
													class="btn btn-info btn-glow"
													type="button"
													name="'tempFile"
													size="30"
													@click="addFile"
												>
													{{ $t('gen.upload') }}
												</button>
											</div>
										</div>
										<ul class="list-group list-inline-tags mt-2">
											<li v-for="file in files" class="list-group-item">
												<ColoredLink
													@click="previewFile(file.fileNo)"
												>
													{{ file.showName }}
													<span
														class="img-delete"
														data-bs-toggle="tooltip"
														:title="$t('gen.delete')"
														@click.prevent="deleteFile(file.fileId)"
													/>
												</ColoredLink>
											</li>
										</ul>
										<div style="height: 25px">
											<span v-show="errors.uploadFile" class="text-danger">{{
												$filters.defaultValue(errors.uploadFile, '--')
											}}</span>
										</div>
									</vue-form>
								</td>
							</tr>
							<tr>
								<th>{{ $t('gen.link') }}</th>
								<td>
									<input
										v-model="selMsg.favoriteLink"
										class="form-control"
										type="text"
										maxlength="100"
										:placeholder="$t('gen.link')"
									>
								</td>
							</tr>
							<tr>
								<th>{{ $t('gen.creatorExt') }}:</th>
								<td>
									<input
										v-model="selMsg.createUserExt"
										class="form-control"
										type="text"
										maxlength="100"
										:placeholder="$t('gen.creatorExt')"
										:readOnly="selMsg.actionCode != 'A'"
									>
								</td>
							</tr>
							<tr>
								<th>{{ $t('gen.maintainerExt') }}:</th>
								<td>
									<input
										v-model="selMsg.modifyUserExt"
										class="form-control"
										type="text"
										maxlength="100"
										:placeholder="$t('gen.maintainerExt')"
										:readOnly="selMsg.actionCode != 'M'"
									>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
				<div class="form-footer">
					<button class="btn btn-primary btn-glow btn-save" @click.prevent="save">
						{{ $t('gen.save') }}
					</button>
				</div>
			</vue-form>
		</div>
	</div>
</template>
<script>
import { Field, Form } from 'vee-validate';
import _ from 'lodash';
import moment from 'moment';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	data: function () {
		return {
			msgId: null,
			validExts: ['doc', 'xlsx', 'docx', 'pdf', 'xlsx', 'txt'],
			symbols: ['/', ':', '*', '?', '"', '<', '>', '|'],
			selMessageMap: [], // 公告類別資料
			selMsgMainCat: [], // 主分類資料
			selMsgSubCat: [], // 次分類資料
			selImportentYn: [], // 重要性參數
			selOptionYn: [], // 重要性參數
			initialMsgCode: ['MSG21', 'MSG26'], // 跑馬燈、強制閱讀為預設
			selMsg: {
				actionCode: 'A',
				msgCode: null,
				importantYn: 'Y',
				mainCatCode: null,
				subCatCode: null,
				validBgnDt: null,
				validEndDt: null,
				msgTitle: null,
				msgContent: null,
				showYn: 'Y',
				favoriteLink: null,
				createUserExt: null,
				modifyUserExt: null
			},
			// 檔案處裡
			fileTemp: null,
			uploadFiles: [],
			files: [],
			fileCnt: 0,
			maxFileCount: 30,
			errors: {
				uploadFile: null
			}
		};
	},
	computed: {
		...mapState(useUserInfoStore, ['userInfo']),
		fileCntObj: function () {
			return {
				fieldCnt: this.fileCnt,
				maxFileCount: this.maxFileCount
			};
		}
	},
	watch: {
		'selMsg.mainCatCode': async function (newVal, oldVal) {
			const self = this;
			if (newVal) {
				self.selMsgSubCat = await self.getMessageCat('S', newVal);
			}
			else {
				self.selMsg.subCatCode = null;
			}
		},
		'selMsg.msgCode': function (newVal, oldVal) {
			const self = this;
			if (newVal && _.includes(self.initialMsgCode, newVal)) {
				self.selMsg.showYn = 'Y';
			}
		}
	},
	mounted: async function () {
		const self = this;
		$.when(
			self.getMessageMap(),
			(self.selMsgMainCat = await self.getMessageCat('M', null)),
			(self.selImportentYn = await self.getAdmCodeDetail('GM_IMPORTANT_YN')),
			(self.selOptionYn = await self.getAdmCodeDetail('OPTION_YN'))
		).then(function () {
			self.msgId = self.$route.params.msgId;
			console.log('bbsMgt Mounted msgId:', self.$route.params.msgId);
			if (!_.isNil(self.msgId)) {
				self.getMessage(self.msgId);
			}
		});
	},
	methods: {
		getAdmCodeDetail: async function (codeType) {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType
			});
			return ret.data;
		},
		getMessageMap: async function (selMessageMap) {
			const self = this;
			const ret = await self.$api.getMessageMapApi();
			if (ret.data) {
				self.selMessageMap = ret.data;
				self.selMsg.msgCode = _.first(self.selMessageMap).msgCode;
			}
		},
		getMessageCat: async function (catType, mainCatCode) {
			const self = this;
			const data = { catType: catType, mainCatCode: mainCatCode };
			const reqData = _.omitBy(data, value => _.isNil(value) || value === '');
			const ret = await self.$api.getGenMessageCat(reqData);
			return ret.data;
		},
		getMessage: async function (msgId) {
			const self = this;
			const ret = await self.$api.getGenMessageApi({
				msgId: msgId
			});
			if (ret.data) {
				self.selMsg = _.clone(ret.data);
				self.selMsg.validBgnDt = moment(ret.data.validBgnDt).format('yyyy-MM-DD');
				self.selMsg.validEndDt = moment(ret.data.validEndDt).format('yyyy-MM-DD');
				self.selMsg.actionCode = 'M';
				self.files = _.clone(ret.data.fileList);
				_.forEach(self.files, function (file) {
					file.fileNo = file.msgFileId;
				});
			}
		},
		// 檔案處裡
		handleChange: function (event) {
			const self = this;
			self.fileTemp = event.target.files[0];
			self.fileCnt = self.files.length;
		},
		addFile: function () {
			const self = this;
			$.when(self.$refs.fileForm.validate()).then(function (result) {
				if (result.valid) {
					if (self.files.length >= self.maxFileCount) {
						return;
					}

					const fileInfo = {
						fileNo: self.generatorId('file'),
						groupId: null,
						showName: self.fileTemp.name,
						fileName: null,
						contentType: self.fileTemp.contentType,
						filePath: null,
						file: self.fileTemp
					};
					self.files.push(fileInfo);
					self.$refs.fileForm.resetForm();
					self.$refs.uploadFile.$el.value = null;
				}
			});
		},
		previewFile: function (targetFileId) {
			const self = this;
			const index = self.files.findIndex(f => f.fileNo === targetFileId);
			const fileInfo = self.files[index];
			console.log('previewFile fileInfo:', fileInfo);
			let url;
			// 預覽待上傳檔案
			if (fileInfo.file) {
				url = URL.createObjectURL(fileInfo.file);
				const previewWindow = window.open(url, '_blank');
				previewWindow.document.title = fileInfo.showName;
				previewWindow.addEventListener('beforeunload', () => {
					URL.revokeObjectURL(url);
				});
				// 預覽伺服器檔案
			}
			else {
				self.$api.previewServerDoc({
					fileId: targetFileId,
					fileTitle: fileInfo.showName,
					fileType: 'GenFilesLog'
				});
			}
		},
		deleteFile: function (targetFileId) {
			const self = this;
			const index = self.files.findIndex(f => f.fileId === targetFileId);
			if (index != -1) {
				self.files.splice(index, 1);
			}
		},
		save: function () {
			const self = this;
			self.$refs.queryForm.validate().then(function (pass) {
				if (pass.valid) {
					self.bbmMaintainSave();
				}
			});
		},
		bbmMaintainSave: async function () {
			const self = this;
			const ret = await self.$api.getBbmMaintainSaveApi();
			if (ret.data && ret.data.length > 0) {
				const bbma = ret.data[0];
				if (bbma.wfgRoleKind !== 'rule4' && bbma.wfgsRole !== self.userInfo.roleCode) {
					this.$bi.alert(this.$t('gen.noPermission'));
					return;
				}
				else {
					self.postMsgLog();
				}
			}
		},
		postMsgLog: async function () {
			const self = this;
			const data = _.clone(self.selMsg);
			data.msgId = self.msgId || '';
			data.fileObject = _.reduce(
				self.files,
				function (result, file) {
					const fileInfo = _.cloneDeep(file);
					fileInfo.msgFileId = file.fileNo;
					result.push(fileInfo);
					return result;
				},
				[]
			);
			const formData = new FormData();
			const jsonStr = JSON.stringify(data);
			formData.append('json', new Blob([jsonStr], { type: 'application/json' }));
			_.forEach(self.files, function (fileInfo) {
				if (fileInfo.file) {
					formData.append('fileNo', fileInfo.fileNo);
					formData.append('fileObject', fileInfo.file);
				}
			});
			let confirmText = self.msgId ? self.$t('gen.edit') : self.$t('gen.add');

			const ret = await self.$api.postMsgLogApi(formData);
			let confirmStatus = 'success';
			if (typeof ret.data.msgId === 'undefined' || ret.data.msgId === null) {
				confirmText = confirmText + self.$t('gen.fail');
				confirmStatus = 'error';
			}
			else {
				confirmText = confirmText + self.$t('gen.success');
			}
			self.$swal
				.fire({
					icon: confirmStatus,
					text: confirmText,
					showCloseButton: true,
					confirmButtonText: self.$t('gen.confirm'),
					buttonsStyling: false,
					customClass: {
						confirmButton: 'btn btn-success'
					}
				})
				.then(function (ret) {
					self.$router.push('/gen/bbsMgt');
				});
		},
		generatorId: function (name) {
			return name + '-' + _.now() + _.random(0, 99);
		}
	}
};
</script>
