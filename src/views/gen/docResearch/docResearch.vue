<template>
	<div class="row">
		<div class="col-12">
			<vue-bi-tabs
				ref="tab"
				:menu-code="'M41-03'"
				:tab-name-decorator="showNameDecorator"
				@change-tab="changeTab"
			>
				<template #default="{ id }">
					<component :is="id" :selected-type-code="selectedTypeCode" />
				</template>
			</vue-bi-tabs>
		</div>
	</div>
</template>
<script>
import vueBiTabs from '@/views/components/biTabs.vue';
import vueDocResearch from './include/docResearch.vue';
export default {
	components: {
		vueBiTabs,
		vueDocResearch
	},
	data: function () {
		return {
			docOutLookMainCnt: [],
			tabCodeMainTypeMap: {
				'M41-030': 'DMTMO01', // 不分
				'M41-031': 'DMTMO05', // 信託-基金
				'M41-032': 'DMTMO03', // 信託-海外ETF
				'M41-033': 'DMTMO04', // 信託-海外債
				'M41-034': 'DMTMO08', // 信託-海外特別股
				'M41-035': 'DMTMO09', // 信託-境外結構型商品
				'M41-036': 'DMTMO02', // 銀行結構型商品
				'M41-037': 'DMTMO07', // 人身保險
				'M41-038': 'DMTMO06' // 黃金存摺
			},
			selectedTypeCode: null
		};
	},
	mounted: function () {
		const self = this;
		self.getDocOutLookMainCnt();
	},
	methods: {
		async getDocOutLookMainCnt() {
			const self = this;
			const ret = await self.$api.getDocOutLookMainCntApi();
			if (!ret.data?.length > 0) return;
			self.docOutLookMainCnt = ret.data;
			self.selectedTypeCode = self.tabCodeMainTypeMap[self.$refs.tab.selectedCode];
		},
		changeTab: function (tabCode) {
			const self = this;
			self.selectedTypeCode = self.tabCodeMainTypeMap[tabCode];
		},
		showNameDecorator: function (tab) {
			const self = this;
			const docOutLookMainCnt = self.docOutLookMainCnt.filter(t => t.typeCode === self.tabCodeMainTypeMap[tab.code]);
			let newName = tab.name;
			if (docOutLookMainCnt && docOutLookMainCnt.length > 0) {
				newName = newName + ' (' + (docOutLookMainCnt[0].cnt || '0') + ')';
			}
			return newName;
		}
	}
};
</script>
