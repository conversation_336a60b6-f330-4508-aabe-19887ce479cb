<template>
	<div class="row">
		<div class="col-12">
			<div class="card card-form-collapse">
				<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
					<h4>{{ $t('gen.msgCategorySettings') }}</h4>
					<span class="tx-square-bracket">{{ $t('gen.requiredFields') }}</span>
				</div>

				<div id="formsearch1" class="card-body collapse show">
					<vue-form v-slot="{ errors }" ref="queryForm">
						<div class="row g-3">
							<div class="col-lg-4">
								<label class="form-label tx-require">{{ $t('gen.categoryName') }}</label>
								<vue-field
									id="mainCatName"
									v-model="mainCatName"
									name="mainCatName"
									class="form-control"
									type="text"
									:label="$t('gen.categoryName')"
									:class="{ 'is-invalid': errors.mainCatName }"
									rules="required"
								/>
								<div>
									<span v-show="errors.mainCatName" class="text-danger">
										{{ errors.mainCatName }}
									</span>
								</div>
							</div>
							<div class="col-lg-4">
								<label class="form-label tx-require">{{ $t('gen.categoryHierarchy') }}</label>
								<div>
									<div v-for="(item, index) in selGmCatType" class="form-check form-check-inline">
										<vue-field
											:id="`selGmCatType_${index}`"
											v-model="catType"
											class="form-check-input"
											type="radio"
											:value="item.codeValue"
											name="selGmCatType"
											:class="{ 'is-invalid': errors.selGmCatType }"
											rules="required"
											:label="$t('gen.categoryHierarchy')"
											:disabled="disableFlag"
										/>
										<label :for="`selGmCatType_${index}`">{{ item.codeName }}</label>
									</div>
								</div>
								<div>
									<span v-show="errors.selGmCatType" class="text-danger">
										{{ errors.selGmCatType }}
									</span>
								</div>
							</div>
							<div class="col-lg-4">
								<template v-if="catType == 'S'">
									<label class="form-label tx-require">{{ $t('gen.parentMainCategory') }}</label>
									<vue-field
										v-model="msgMainCat"
										as="select"
										name="msgMainCat"
										class="form-select"
										:class="{ 'is-invalid': errors.msgMainCat }"
										rules="required"
										:label="$t('gen.parentMainCategory')"
										:disabled="disableFlag"
									>
										<option :value="null">
											{{ $t('gen.pleaseSelect') }}
										</option>
										<option v-for="item in selMsgMainCat" :value="item.catCode">
											{{ item.catName }}
										</option>
									</vue-field>
									<div>
										<span v-show="errors.msgMainCat" class="text-danger">
											{{ errors.msgMainCat }}
										</span>
									</div>
								</template>
							</div>
						</div>
						<div v-if="modifyType == 'S'" class="form-footer">
							<a class="btn btn-primary" @click.prevent="createCategory">{{ $t('gen.save') }}</a>
						</div>
						<div v-if="modifyType == 'M'" class="form-footer">
							<button class="btn btn-primary" @click.prevent="modifyCategory">
								{{ $t('gen.modify') }}
							</button>&nbsp;
							<button class="btn btn-primary margin-left10" @click.prevent="cancel">
								{{ $t('gen.cancelModify') }}
							</button>
						</div>
					</vue-form>
				</div>
			</div>

			<div class="card- card-form">
				<div class="card-header">
					<h4>{{ $t('gen.categoryList') }}</h4>
				</div>
				<table class="table table-RWD table-horizontal-RWD table-bordered">
					<thead>
						<tr>
							<th width="5%" />
							<th width="30%">
								{{ $t('gen.categoryName') }}
							</th>
							<th width="20%">
								{{ $t('gen.maintainer') }}
							</th>
							<th width="20%">
								{{ $t('gen.maintenanceDate') }}
							</th>
							<th width="10%" class="text-center">
								{{ $t('gen.execute') }}
							</th>
						</tr>
					</thead>
					<tbody>
						<template v-for="(mainCat, mainIndex) in selMsgMainCat">
							<tr>
								<td class="btn-minus">
									<button
										class="btn"
										type="button"
										data-bs-toggle="collapse"
										:data-bs-target="`#collapse_${mainIndex}`"
										aria-expanded="false"
										:aria-controls="`collapse_${mainIndex}`"
										@click="toggleBtn(mainIndex)"
									>
										<i :class="isActive(mainIndex) ? 'bi bi-plus-lg' : 'bi bi-dash-lg'" />
									</button>
								</td>
								<td :data-th="$t('gen.categoryName')">
									{{ mainCat.catName }}
								</td>
								<td :data-th="$t('gen.maintainer')">
									{{ mainCat.editUser }}
								</td>
								<td :data-th="$t('gen.maintenanceDate')">
									{{ mainCat.editDate }}
								</td>
								<td :data-th="$t('gen.execute')">
									<button
										type="button"
										class="btn btn-info btn-icon"
										data-bs-toggle="tooltip"
										:title="$t('gen.edit')"
										@click="modifyPage(mainCat.catCode, mainCat.catName, 'M')"
									>
										<i class="fa-solid fa-pen" />
									</button>
									<button
										type="button"
										class="btn btn-danger btn-icon"
										data-bs-toggle="tooltip"
										:title="$t('gen.delete')"
										@click="deleteCategory(mainCat.catCode, 'M')"
									>
										<i class="fa-solid fa-trash" />
									</button>
								</td>
							</tr>
							<tr
								v-for="subCat in subcategoryMap.get(mainCat.catCode)"
								:id="`collapse_${mainIndex}`"
								class="collapse show"
								aria-expanded="true"
							>
								<td />
								<td :data-th="$t('gen.categoryName')">
									{{ subCat.catName }}
								</td>
								<td :data-th="$t('gen.maintainer')">
									{{ subCat.editUser }}
								</td>
								<td :data-th="$t('gen.maintenanceDate')">
									{{ subCat.editDate }}
								</td>
								<td :data-th="$t('gen.execute')">
									<button
										type="button"
										class="btn btn-info btn-icon"
										data-bs-toggle="tooltip"
										:title="$t('gen.edit')"
										@click="modifyPage(subCat.catCode, subCat.catName, 'S', mainCat.catCode)"
									>
										<i class="fa-solid fa-pen" />
									</button>
									<button
										type="button"
										class="btn btn-danger btn-icon"
										data-bs-toggle="tooltip"
										:title="$t('gen.delete')"
										@click="deleteCategory(subCat.catCode, 'S', subCat.mainCatCode)"
									>
										<i class="fa-solid fa-trash" />
									</button>
								</td>
							</tr>
						</template>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<!--頁面內容 end-->
</template>
<script>
import { useConfirm } from '@/composables/useConfirm';
import _ from 'lodash';
import { Field, Form } from 'vee-validate';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field
	},
	filters: {},
	setup() {
		return useConfirm();
	},
	data: function () {
		return {
			// 畫面顯示用參數
			selGmCatType: [], // 分類階層選項
			selMsgMainCat: [], // 所屬主分類下拉選單
			subcategoryMap: new Map(),
			modifyType: 'S',
			disableFlag: false,

			mainCatName: '', // 分類名稱輸入欄位
			catType: null, // 分類階層選擇欄位
			msgMainCat: '', // 所屬主分類選擇欄位
			catCode: ''
		};
	},
	beforeMount: function () {},
	created: function () {},
	mounted: async function () {
		const self = this;
		await self.getAdmCodeDetail('GM_CAT_TYPE');
		await self.getMainCategoryMessages();
		// TODO: 1 + N query
		// 可能需要修改 API
		await Promise.all(this.selMsgMainCat.map(item => this.getSubcategoryMessages(item.catCode)));
	},
	methods: {
		async getAdmCodeDetail(codeType) {
			const response = await this.$api.getAdmCodeDetail({ codeType });
			this.selGmCatType = response.data;
		},
		async getMainCategoryMessages() {
			const response = await this.$api.getGenMessageCat({ catType: 'M', mainCatCode: null });
			this.selMsgMainCat = response.data;
		},
		async getSubcategoryMessages(categoryCode) {
			const response = await this.$api.getGenMessageCat({ catType: 'S', mainCatCode: categoryCode });
			this.subcategoryMap.set(categoryCode, response.data ?? []);
		},
		toggleBtn(index) {
			this.selMsgMainCat[index].active = !this.selMsgMainCat[index].active;
		},
		isActive(index) {
			return this.selMsgMainCat[index].active;
		},
		createCategory: async function () {
			const pass = await this.$refs.queryForm.validate();
			if (!pass.valid) return;
			if (this.catType === 'M') {
				const hasDuplicated = this.selMsgMainCat.some(mainCategory => mainCategory.catName === this.mainCatName);
				if (hasDuplicated) return this.alert({ text: this.$t('gen.cannotAddSameCategoryName') });
			}
			else if (this.catType === 'S') {
				const hasDuplicated = (this.subcategoryMap.get(this.msgMainCat) ?? []).some(subcategory => subcategory.catName === this.mainCatName);
				if (hasDuplicated) return this.alert({ text: this.$t('gen.cannotAddSameCategoryName') });
			}
			// 新增
			await this.$api.postGenMessageCat({
				catName: this.mainCatName,
				catType: this.catType,
				mainCatCode: this.msgMainCat
			});
			if (this.catType === 'M') this.getMainCategoryMessages();
			else if (this.catType === 'S') this.getSubcategoryMessages(this.msgMainCat);
		},
		modifyPage(catCode, catName, catType, mainCatCode) {
			const self = this;
			self.modifyType = 'M';
			self.disableFlag = true;
			self.catType = catType;
			self.mainCatName = catName;
			self.catCode = catCode;
			if (catType !== 'S') return;
			const parentCat = _.find(self.selMsgMainCat, function (mainCat) {
				return mainCat.catCode === mainCatCode;
			});
			self.msgMainCat = parentCat.catCode;
		},
		deleteCategory: async function (catCode, catType, mainCatCode) {
			const self = this;
			if (catType === 'M') {
				const countMessageResponse = await self.$api.getGenCountMessage({ catType: 'M', mainCatCode: catCode });
				if (countMessageResponse.data && countMessageResponse.data.cnt > 0)
					return this.alert({ text: this.$t('gen.cannotDeleteMainCategoryWithMessages') });
				const countSubcategoryResponse = await self.$api.getGenMessageCat({ catType: 'S', mainCatCode: catCode });
				if (!_.isEmpty(countSubcategoryResponse.data))
					return this.alert({ text: this.$t('gen.cannotDeleteMainCategoryWithSubcategories') });
			}
			else if (catType === 'S') {
				const ret = await self.$api.getGenCountMessage({	catType: catType, subCatCode: mainCatCode });
				if (ret.data && ret.data.cnt > 0) return this.alert({ text: this.$t('gen.cannotDeleteSubCategoryWithMessages') });
			}

			await this.confirm({
				text: this.$t('gen.confirmDeleteData'),
				async onOk() {
					if (catType === 'M') {
						await self.$api.deleteGenMessageCat({ catType: catType, mainCatCode: catCode });
						self.getMainCategoryMessages();
					}
					else if (catType === 'S') {
						await self.$api.deleteGenMessageCat({ catType: catType, subCatCode: catCode });
						self.getSubcategoryMessages(mainCatCode);
					}
				}
			});
		},
		modifyCategory: async function () {
			const self = this;
			const pass = await self.$refs.queryForm.validate();
			if (!pass.valid) return;
			if (self.catType === 'M') {
				const hasDuplicated = self.selMsgMainCat.some(mainCategory => mainCategory.catName == self.mainCatName);
				if (hasDuplicated) return this.alert({ text: this.$t('gen.cannotAddSameMainCategoryName') });
			}
			else if (self.catType === 'S') {
				const hasDuplicated = this.subcategoryMap.get(self.msgMainCat)?.some(subcategory => subcategory.catName === self.mainCatName);
				if (hasDuplicated) return this.alert({ text: this.$t('gen.cannotAddSameSubCategoryName') });
			}

			// 維護
			if (self.catType === 'M') {
				await self.$api.patchGenMessageCat({
					catName: self.mainCatName,
					catType: self.catType,
					mainCatCode: self.catCode
				});
				this.success({ text: self.$t('gen.updateSuccess') });
				self.getMainCategoryMessages();
			}
			else if (self.catType === 'S') {
				await self.$api.patchGenMessageCat({
					catName: self.mainCatName,
					catType: self.catType,
					mainCatCode: self.msgMainCat,
					subCatCode: self.catCode
				});
				this.success({ text: self.$t('gen.updateSuccess') });
				self.getSubcategoryMessages(self.msgMainCat);
			}
		},
		cancel: function () {
			const self = this;
			self.modifyType = 'S';
			self.disableFlag = false;
			self.catType = null;
			self.mainCatName = null;
			self.msgMainCat = null;
			self.catCode = null;
		}
	}
};
</script>
