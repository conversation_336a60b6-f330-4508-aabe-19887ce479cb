<template>
	<div class="row">
		<div class="col-12">
			<div class="card card-table-list">
				<div class="card-header">
					<h4>{{ $t('gen.reviewList') }}</h4>
				</div>
				<!-- 待審核清單 -->
				<table class="table table-RWD table-list text-center">
					<thead>
						<tr>
							<th>{{ $t('gen.submitDate') }}</th>
							<th>{{ $t('gen.submitter') }}</th>
							<th v-for="varItem in varItems">
								{{ varItem.varItemName }}
							</th>
							<th>{{ $t('gen.applicationDetails') }}</th>
							<th class="wd-100">
								{{ $t('gen.reviewStatus') }}
							</th>
							<th class="wd-10p">
								{{ $t('gen.otherNotes') }}
							</th>
						</tr>
					</thead>
					<tbody>
						<template v-for="item in wkfEvents">
							<tr>
								<td :data-th="$t('gen.submitDate')">
									<span>{{ item.createDt }}</span>
								</td>
								<td :data-th="$t('gen.submitter')">
									<span>{{ item.createBy }} {{ item.userName }}</span>
								</td>

								<template v-if="isEmptyItemData(item)">
									<td v-for="(itemData, index) in item.itemDatas" :key="index">
										<span>{{ itemData.varItemValue }}</span>
									</td>
								</template>
								<!-- 無值輸出空白 避免排版錯誤 -->
								<template v-else>
									<td v-for="_ in varItems" />
								</template>

								<td :data-th="$t('gen.applicationDetails')" class="text-center">
									<Button color="dark" icon @click="getDetail(item.eventId)">
										<i class="bi bi-search" />
									</Button>
								</td>
								<td :data-th="$t('gen.reviewStatus')" class="text-start">
									<RadioGroup
										v-model="item.status"
										:options="item.wkfEngineFlows"
										option-value="actionStatus"
										option-label="actionName"
									/>
								</td>
								<td :data-th="$t('gen.otherNotes')">
									<textarea
										v-model="item.desc"
										class="form-control"
										rows="3"
										:disabled="item.status != 'R'"
									/>
								</td>
							</tr>
						</template>
					</tbody>
				</table>
			</div>
		</div>
		<div class="col-12 mt-3 text-end">
			<Button :label="$t('gen.reviewComplete')" size="lg" @click="audit()" />
		</div>
	</div>
	<!--頁面內容 end-->
	<vue-modal :is-open="isOpenModal" @close="closeModal">
		<template #content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('gen.bbsMessage') }}
						</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()">
							<i class="bi bi-arrows-fullscreen" />
						</button>
						<button type="button" class="btn-close" @click.prevent="props.close()" />
					</div>
					<div class="modal-body">
						<table class="table table-bordered">
							<caption>
								{{ $t('gen.bbsCategory') }}
							</caption>
							<tbody>
								<tr>
									<th>{{ $t('gen.messageType') }}</th>
									<td>{{ selMsgLog.msgName }}</td>
								</tr>
							</tbody>
						</table>

						<table class="table table-bordered">
							<caption>
								{{ $t('gen.bbsContent') }}
							</caption>
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('gen.importance') }}
									</th>
									<td>{{ subTypeName(selMsgLog.importantYn) }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.mainCategory') }}</th>
									<td>{{ selMsgLog.mainCatName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.subCategory') }}</th>
									<td>{{ selMsgLog.subCatName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.validDate') }}</th>
									<td>{{ selMsgLog.validBgnDt }} ~ {{ selMsgLog.validEndDt }}</td>
								</tr>
								<tr>
									<th><span>{{ $t('gen.bbsTitle') }}</span></th>
									<td>{{ selMsgLog.msgTitle }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.bbsContent') }}</th>
									<td>{{ selMsgLog.msgContent }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.showOnHomepage') }}</th>
									<td>{{ subTypeName(selMsgLog.showYn) }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.uploadFile') }}</th>
									<td>
										<ul v-for="file in selMsgLog.fileList" class="list-group list-inline-tags">
											<li class="list-group-item">
												<ColoredLink @click="viewFile(file)">
													<span>{{ file.showName }}</span>
												</ColoredLink>
											</li>
										</ul>
									</td>
								</tr>
								<tr>
									<th>{{ $t('gen.link') }}</th>
									<td>
										<a :href="selMsgLog.favoriteLink" style="word-break: break-all">{{ selMsgLog.favoriteLink }}</a>
									</td>
								</tr>
							</tbody>
						</table>

						<table class="table table-bordered">
							<caption>
								{{ $t('gen.maintenanceInfo') }}
							</caption>
							<tbody>
								<tr>
									<th>{{ $t('gen.creator') }}</th>
									<td>{{ selMsgLog.createUser }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.creatorExt') }}</th>
									<td>{{ selMsgLog.createUserExt }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.createDate') }}</th>
									<td>{{ selMsgLog.createDt }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.lastMaintainer') }}</th>
									<td>{{ selMsgLog.modifyUser }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.maintainerExt') }}</th>
									<td>{{ selMsgLog.modifyUserExt }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.lastMaintenanceDate') }}</th>
									<td>{{ selMsgLog.modifyDt }}</td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-white" @click="props.close()">
							{{ $t('gen.closeWindow') }}
						</button>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
</template>

<script>
import vueModal from '@/views/components/model.vue';
import _ from 'lodash';
import { convertWkfResponse } from '@/utils/convertWkfResponse';
import { useConfirm } from '@/composables/useConfirm';
export default {
	components: {
		vueModal
	},
	setup() {
		return {
			...useConfirm()
		};
	},
	data: function () {
		return {
			// 主要顯示資料
			selOptionYn: [], // convert option name
			wkfEvents: [],
			varItems: [],
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand',
			selMsgLog: {}
		};
	},
	mounted: async function () {
		const self = this;
		self.selOptionYn = await self.getAdmCodeDetail('OPTION_YN');
		self.getVarItems();
		self.getWkfEvents();
	},
	methods: {
		getAdmCodeDetail: async function (codeType) {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: codeType
			});
			return ret.data;
		},
		getVarItems: async function () {
			const self = this;
			const ret = await self.$api.getVarItemsApi(self.$route.params.wfgId);
			self.varItems = ret.data;
		},
		getWkfEvents: async function () {
			const self = this;

			const ret = await self.$api.getWkfEventsApi(self.$route.params.wfgId);
			self.wkfEvents = ret.data;
			console.log('self.wkfEvents: ', self.wkfEvents);
		},
		getDetail: async function (eventId) {
			console.log('eventId: ', eventId);
			const self = this;
			const ret = await self.$api.getMsgLogApi({
				eventId
			});
			if (!ret.data) return;
			self.selMsgLog = ret.data;
			console.log('self.selMsgLog: ', self.selMsgLog);
			self.isOpenModal = true;
		},
		changeModalSize: function () {
			const self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			}
			else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		subTypeName: function (codeValue) {
			const self = this;
			if (!codeValue) return '';
			return self.selOptionYn.find(item => item.codeValue === codeValue)?.codeName ?? '';
		},
		viewFile: async function (file) {
			await this.$api.downloadFileApi({ fileType: 'GenFilesTemp', fileId: file.msgFileId });
		},
		isEmptyItemData: function (item) {
			return !_.isEmpty(item.itemDatas);
		},
		audit: async function () {
			const self = this;
			const wkfEvents = self.wkfEvents;
			const updateRoleMenuLogs = [];

			for (let i = 0; i < wkfEvents.length; i++) {
				const wkfEvent = wkfEvents[i];
				if (wkfEvent.status !== 'A' && wkfEvent.status !== 'R') continue;
				if (wkfEvent.status == 'R' && _.isBlank(wkfEvent.desc)) {
					this.$bi.alert(this.$t('gen.pleaseEnterRejectReason'));
					return;
				}
				updateRoleMenuLogs.push(wkfEvent);
			}

			if (updateRoleMenuLogs.length === 0) {
				this.$bi.alert(this.$t('gen.noDataToReview'));
				return;
			}

			await Promise.all(updateRoleMenuLogs.map(roleMenuLog =>
				self.$api.patchAuditApi({
					eventId: roleMenuLog.eventId,
					actionCode: _.filter(roleMenuLog.wkfEngineFlows, ['actionStatus', roleMenuLog.status])[0].actionCode,
					desc: roleMenuLog.desc
				}).then((ret) => {
					const result = convertWkfResponse(ret.data, true);
					if (!result.success && result.error) self.alert(result.error);
				})));
			this.success({ text: this.$t('gen.reviewCompleted') });
			this.getWkfEvents();
		},
		closeModal: function () {
			this.isOpenModal = false;
		}
	}
};
</script>
