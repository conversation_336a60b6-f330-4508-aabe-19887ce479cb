<template>
	<div class="tab-content">
		<div class="tab-pane fade active show">
			<div class="card-header">
				<ul class="nav nav-pills card-header-pills">
					<li v-for="tab in subTabs" @click="selectedSubTabCode = tab.typeCode">
						<a class="nav-link" :class="{ active: selectedSubTabCode == tab.typeCode }">
							{{ tab.typeName }}({{ tab.cnt || 0 }})
						</a>
					</li>
				</ul>
			</div>
			<div class="card card-table mb-3">
				<div class="card-header">
					<h4>{{ $t('gen.documentList') }}</h4>
					<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-hover table-bordered table-padding">
						<thead>
							<tr>
								<th width="40%">
									{{ $t('gen.documentTitle') }}
								</th>
								<th width="10%">
									{{ $t('gen.effectiveDate') }}
								</th>
								<th width="10%">
									{{ $t('gen.expiryDate') }}
								</th>
								<th width="12%">
									{{ $t('gen.canProvideToCustomers') }}
								</th>
								<th width="9%">
									{{ $t('gen.urgencyLevel') }}
								</th>
								<th width="9%">
									{{ $t('gen.view') }}
								</th>
								<th width="10%">
									{{ $t('gen.relatedAttachments') }}
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="data in pageData.content">
								<td :data-th="$t('gen.documentTitle')">
									{{ data.docName }}
								</td>
								<td :data-th="$t('gen.effectiveDate')">
									{{ $filters.formatDate(data.validDt) }}
								</td>
								<td :data-th="$t('gen.expiryDate')">
									{{ $filters.formatDate(data.expireDt) }}
								</td>
								<td :data-th="$t('gen.canProvideToCustomers')">
									{{ data.showCusYnName === 'Y' ? $t('gen.canProvideToCustomer') : $t('gen.internalUseOnly') }}
								</td>
								<td :data-th="$t('gen.urgencyLevel')">
									{{ data.priorityName }}
								</td>
								<td :data-th="$t('gen.execute')" class="text-center">
									<Button
										color="dark"
										:title="$t('gen.view')"
										icon
										@click="viewSelDoc(data.docId)"
									>
										<i class="bi bi-search" />
									</Button>
								</td>
								<td :data-th="$t('gen.relatedAttachments')" class="text-center">
									<div class="d-flex flex-column align-items-start">
										<ColoredLink v-for="file in data.fileList" :key="file.docFileId" @click="viewFile(file.docFileId)">
											{{ file.showName }}
										</ColoredLink>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>

	<!-- Modal -->
	<vue-modal :is-open="isOpenModal" @close="closeModal">
		<template #content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('gen.documentView') }}
						</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()">
							<i class="bi bi-arrows-fullscreen" />
						</button>
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click.prevent="props.close()"
						/>
					</div>
					<div class="modal-body">
						<div class="caption">
							{{ $t('gen.documentCategory') }}
						</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('gen.documentType') }}
									</th>
									<td>{{ selDoc.docCatName }}</td>
								</tr>
							</tbody>
						</table>
						<div class="caption">
							{{ $t('gen.documentContent') }}
						</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('gen.documentTitle') }}
									</th>
									<td>{{ selDoc.docName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.effectiveDate') }}</th>
									<td>{{ $filters.formatDate(selDoc.validDt) }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.expiryDate') }}</th>
									<td>{{ $filters.formatDate(selDoc.expireDt) }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.urgencyLevel') }}</th>
									<td>{{ selDoc.priorityName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.canProvideToCustomers') }}</th>
									<td>{{ selDoc.showCusYn === 'Y' ? $t('gen.canProvideToCustomer') : $t('gen.internalUseOnly') }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.summary') }}</th>
									<td>{{ selDoc.docDesc }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.additionalDocuments') }}</th>
									<td>
										<a
											v-for="file in selDoc.fileInfo"
											href="#"
											class="link-underline"
											@click="viewFile(fileId)"
										>
											{{ file.showName }}
										</a><br>
									</td>
								</tr>
							</tbody>
						</table>
						<div class="caption">
							{{ $t('gen.maintenanceInfo') }}
						</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('gen.creator') }}
									</th>
									<td>{{ selDoc.createBy }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.createDate') }}</th>
									<td>{{ $filters.formatDate(selDoc.createDt) }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.lastMaintainer') }}</th>
									<td>{{ selDoc.modifyBy }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.lastMaintenanceDate') }}</th>
									<td>{{ $filters.formatDate(selDoc.modifyDt) }}</td>
								</tr>
							</tbody>
						</table>
					</div>

					<div id="appointmentFooter" class="modal-footer">
						<input
							id="appointmentCloseButton"
							name="btnClose"
							class="btn btn-white"
							type="button"
							:value="$t('gen.close')"
							@click.prevent="props.close()"
						>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- Modal End -->
</template>
<script>
import _ from 'lodash';
import vueModal from '@/views/components/model.vue';
import vuePagination from '@/views/components/pagination.vue';
export default {
	components: {
		vueModal,
		vuePagination
	},
	props: {
		selectedTypeCode: {
			type: String,
			default: ''
		}
	},
	data: function () {
		return {
			subTabs: [],
			pageData: {
				content: {}
			},
			selDoc: {},
			selectedSubTabCode: null,
			pageable: {
				page: 0,
				size: 10,
				sort: 'DOC_ID',
				direction: 'ASC'
			},

			// Modal
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand'
		};
	},
	watch: {
		selectedTypeCode: {
			immediate: true,
			handler: function () {
				const self = this;
				self.getSubTabs();
			}
		},
		selectedSubTabCode(newVal) {
			if (newVal) {
				this.gotoPage(0);
			}
		}
	},
	methods: {
		close: function () {
			this.isOpenModal = false;
		},
		async getSubTabs() {
			const self = this;
			const ret = await self.$api.getDocMktSubCntApi({
				mainTypeCode: self.selectedTypeCode
			});
			if (!ret.data?.length > 0) return;
			self.subTabs = ret.data;
			self.selectedSubTabCode = self.subTabs[0]?.typeCode;
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			const self = this;
			const url = _.toPageUrl('', page, self.pageable);
			const ret = await self.$api.getDocSalesPageData(
				{
					mainTypeCode: self.selectedTypeCode,
					subTypeCode: self.selectedSubTabCode
				},
				url
			);
			self.pageData = ret.data;
		},
		viewSelDoc: async function (docId) {
			const self = this;
			const ret = await self.$api.getViewSelDoc({
				docCat: 'MKTEVENT',
				docId: docId
			});
			if (!ret.data?.length > 0) return;
			self.selDoc = ret.data[0];
			self.isOpenModal = true;
		},
		viewFile: async function (fileId) {
			const self = this;
			await self.$api.downloadFileApi({ fileType: 'GenDocFiles', fileId: fileId });
		},
		changeModalSize: function () {
			const self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			}
			else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		closeModal: function () {
			const self = this;
			self.isOpenModal = false;
		},
		openModal: function () {
			const self = this;
			self.isOpenModal = true;
		}
	}
};
</script>
