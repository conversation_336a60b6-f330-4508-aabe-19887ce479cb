<template>
	<div role="tabpanel" class="tab-pane fade active show">
		<div class="card card-form-collapse">
			<div class="card-header" data-bs-toggle="collapse" data-bs-target="#formsearch1">
				<h4>{{ $t('gen.searchConditions') }}</h4>
				<span class="tx-square-bracket">{{ $t('gen.requiredFields') }}</span>
			</div>

			<div id="formsearch1" class="card-body collapse show">
				<vue-form ref="queryForm">
					<div class="form-row">
						<div class="form-group col-lg-6">
							<label class="form-label tx-require">{{ $t('gen.documentType') }}</label>
							<Select v-model="docCat" :options="documentCategoryOptions" />
						</div>

						<div class="form-group col-lg-4">
							<label class="form-label">{{ $t('gen.documentMainCategory') }}</label>
							<select v-model="mainType" class="form-select">
								<option :value="null">
									{{ $t('gen.all') }}
								</option>
								<option v-for="item in selMainCat" :value="item.typeCode">
									{{ item.typeName }}
								</option>
							</select>
						</div>

						<div class="form-group col-lg-4">
							<label class="form-label">{{ $t('gen.documentSubCategory') }}</label>
							<select v-model="subType" class="form-select" :disabled="!mainType">
								<option :value="null">
									{{ $t('gen.all') }}
								</option>
								<option v-for="item in selSubCat" :value="item.typeCode">
									{{ item.typeName }}
								</option>
							</select>
						</div>
					</div>

					<div class="form-row">
						<div class="form-group col-lg-4">
							<label class="form-label">{{ $t('gen.documentTitle') }}</label>
							<input
								v-model="docName"
								class="form-control"
								type="text"
								maxlength="20"
							>
						</div>
						<div class="form-group col-lg-8">
							<label class="form-label tx-require">{{ $t('gen.expiryDateFromTo') }}</label><br>
							<div class="input-group">
								<span class="input-group-text">{{ $t('gen.from') }}</span>
								<input
									id="beginDate"
									v-model="bgnDt"
									type="date"
									name="beginDate"
									value=""
									class="form-control"
								>
								<span class="input-group-text">~</span>
								<span class="input-group-text">{{ $t('gen.to') }}</span>
								<input
									id="endDate"
									v-model="endDt"
									type="date"
									name="endDate"
									value=""
									class="JQ-datepicker form-control"
								>
							</div>
						</div>
					</div>

					<div class="form-footer">
						<input
							class="btn btn-primary"
							type="button"
							:value="$t('gen.query')"
							@click.prevent="getDocument"
						>
					</div>
				</vue-form>
			</div>
		</div>

		<div v-if="pageData.totalElements >= 0">
			<div class="card card-table mb-3">
				<div class="card-header">
					<h4>{{ $t('gen.searchResults') }}</h4>
					<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-hover table-bordered table-padding">
						<thead>
							<tr>
								<th width="10%">
									{{ $t('gen.documentType') }}
								</th>
								<th width="20">
									{{ $t('gen.documentTitle') }}
								</th>
								<th width="9%">
									{{ $t('gen.effectiveDate') }}
								</th>
								<th width="9%">
									{{ $t('gen.expiryDate') }}
								</th>
								<th width="9%">
									{{ $t('gen.urgencyLevel') }}
								</th>
								<th width="9%">
									{{ $t('gen.createDate') }}
								</th>
								<th width="10%">
									{{ $t('gen.relatedAttachments') }}
								</th>
								<th width="14%">
									{{ $t('gen.execute') }}
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="(item, index) in pageData.content" :key="index">
								<td :data-th="$t('gen.documentType')">
									{{ item.docCatName }}
								</td>
								<td :data-th="$t('gen.documentTitle')">
									{{ item.docName }}
								</td>
								<td :data-th="$t('gen.effectiveDate')">
									{{ item.validDt }}
								</td>
								<td :data-th="$t('gen.expiryDate')">
									{{ item.expireDt }}
								</td>
								<td :data-th="$t('gen.urgencyLevel')">
									{{ item.priorityName }}
								</td>
								<td :data-th="$t('gen.createDate')">
									{{ item.createDt }}
								</td>
								<td v-if="item.fileInfo != null" class="text-center" :data-th="$t('gen.relatedAttachments')">
									<div class="d-flex flex-column gap-1 align-items-start">
										<ColoredLink v-for="file in item.fileInfo" :key="file.docFileId" @click="viewFile(file.docFileId, file.showName)">
											{{ file.showName }}
										</ColoredLink>
									</div>
								</td>
								<td class="text-center" :data-th="$t('gen.execute')">
									<Button
										color="info"
										icon
										:title="$t('gen.edit')"
										@click="redirectEditPage(item.docId, item.docCatName)"
									>
										<i class="fa-solid fa-pen" />
									</Button>

									<Button
										color="dark"
										icon
										:title="$t('gen.view')"
										@click="viewSelDoc(item.docId, item.docCatName)"
									>
										<i class="bi bi-search" />
									</Button>
									<Button
										color="danger"
										icon
										:title="$t('gen.delete')"
										@click="deleteDoc(item.docId)"
									>
										<i class="fa-solid fa-trash" />
									</Button>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
	<!-- Modal -->
	<vue-modal :is-open="isOpenModal" @close="closeModal">
		<template #content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('gen.documentSearch') }}
						</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()">
							<i
								class="bi bi-arrows-fullscreen"
							/>
						</button>
						<button type="button" class="btn-close" @click.prevent="props.close()" />
					</div>
					<div class="modal-body">
						<div class="caption">
							{{ $t('gen.documentCategory') }}
						</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('gen.documentType') }}
									</th>
									<td>{{ selDoc.docCatName }}</td>
								</tr>
							</tbody>
						</table>
						<div class="caption">
							{{ $t('gen.documentContent') }}
						</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('gen.documentTitle') }}
									</th>
									<td>{{ selDoc.docName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.effectiveDate') }}</th>
									<td>{{ selDoc.validDt }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.expiryDate') }}</th>
									<td>{{ selDoc.expireDt }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.urgencyLevel') }}</th>
									<td>{{ selDoc.priorityName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.canProvideToCustomers') }}</th>
									<td>{{ selDoc.showCusName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.summary') }}</th>
									<td>{{ selDoc.docDesc }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.additionalDocuments') }}</th>
									<td>
										<ul v-for="file in selDoc.fileInfo" class="list-group list-inline-tags">
											<li class="list-group-item">
												<a href="#" @click.prevent="viewFile(file.docFileId, file.showName)">
													<span>{{ file.showName }}</span>
												</a>
											</li>
										</ul>
									</td>
								</tr>
								<tr>
									<th>{{ $t('gen.relatedProducts') }}</th>
									<td>
										<a href="#" class="link-underline">{{ getProNames(selDoc.productInfo) }}</a>
									</td>
								</tr>
								<tr>
									<th>{{ $t('gen.notificationTarget') }}</th>
									<td>
										<span v-if="hasHoldName">{{ selDoc.holdName }}</span>
										<span v-if="hasHoldName && hasSubsName"> 、 </span>
										<span v-if="hasSubsName">{{ selDoc.subsName }}</span>
									</td>
								</tr>
							</tbody>
						</table>
						<div class="caption">
							{{ $t('gen.maintenanceInfo') }}
						</div>
						<table class="table table-bordered">
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('gen.creator') }}
									</th>
									<td>{{ selDoc.createBy }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.createDate') }}</th>
									<td>{{ selDoc.createDt }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.lastMaintainer') }}</th>
									<td>{{ selDoc.modifyBy }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.lastMaintenanceDate') }}</th>
									<td>{{ selDoc.modifyDt }}</td>
								</tr>
							</tbody>
						</table>
					</div>

					<div id="appointmentFooter" class="modal-footer">
						<Button color="white" :label="$t('gen.close')" @click="props.close()" />
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- Modal End -->
</template>
<script>
import { Form } from 'vee-validate';
import _ from 'lodash';
import moment from 'moment';
import vueModal from '@/views/components/model.vue';
import vuePagination from '@/views/components/pagination.vue';
export default {
	components: {
		'vue-form': Form,
		vueModal,
		vuePagination
	},
	data: function () {
		return {
			selDocCat: [],
			selMainCat: [],
			selSubCat: [],
			pageData: {
				content: {}
			},
			selDoc: {},

			selectedTabCode: null,
			// 傳入API參數
			mainCat: '',
			docCat: 'ALL',
			mainType: null,
			subType: null,
			bgnDt: null,
			endDt: null,
			docName: null,

			// 分頁元件
			pageable: {
				page: 0,
				size: 10,
				sort: [['CREATE_DT', 'DESC'], ['PRIORITY_VALUE', 'ASC']],
				direction: 'ASC'
			},
			// Modal
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand'
		};
	},
	computed: {
		hasHoldName() {
			return !!this.selDoc.holdName; // 檢查 holdName 是否有值
		},
		hasSubsName() {
			return !!this.selDoc.subsName; // 檢查 subsName 是否有值
		},
		documentCategoryOptions() {
			return [
				{ value: 'ALL', label: this.$t('gen.all') },
				...this.selDocCat.map(item => ({ value: item.codeValue, label: item.codeName }))
			];
		}
	},
	watch: {
		docCat: function (newVal) {
			const self = this;
			if (_.isEqual(newVal, 'MKTEVENT') || _.isEqual(newVal, 'MKTOUTLOOK')) {
				self.getMainCat(newVal);
			}
			else if (_.isEqual(newVal, 'PRODUCT')) {
				self.getProCat();
			}
			else {
				self.selMainCat = [];
			}
		},
		selectedTabCode(newVal) {
			if (newVal) {
				this.gotoPage(0);
			}
		},
		mainType: async function (newVal) {
			const self = this;
			if (newVal) {
				self.selSubCat = await self.getMainCatType(self.docCat, newVal);
			}
			else {
				self.subType = null;
			}
		}
	},
	mounted: async function () {
		const self = this;
		self.selDocCat = await self.getAdmCodeDetail('DOC_CATS');
	},
	methods: {
		async getProCat() {
			const self = this;
			const ret = await self.$api.getProCatApi();
			self.selProCat = ret.data;
		},
		getMainCat: async function (catCode) {
			const self = this;
			const ret = await self.$api.getMainDocTypeApi({
				catCode: catCode
			});
			if (ret.data) {
				self.selMainCat = ret.data;
			}
		},
		getMainCatType: async function (catCode, mainTypeCode) {
			const self = this;
			const data = { catCode: catCode, mainTypeCode: mainTypeCode };
			const reqData = _.omitBy(data, value => _.isNil(value) || value === '');
			const ret = await self.$api.getMainDocTypeApi(reqData);
			return ret.data;
		},
		getAdmCodeDetail: async function (codeType) {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({ codeType: codeType });
			return ret.data;
		},
		getDocument: function () {
			const self = this;
			$.when(self.$refs.queryForm.validate()).then(function (result) {
				if (result.valid) {
					if (_.isNil(self.docCat)) {
						self.$self.$swal.fire({
							icon: 'error',
							text: self.$t('gen.documentTypeRequired'),
							showCloseButton: true,
							confirmButtonText: self.$t('gen.confirm'),
							buttonsStyling: false, // remove default button style
							customClass: {
								confirmButton: 'btn btn-danger'
							}
						});
						return;
					}
					if (moment(self.bgnDt).isAfter(moment(self.endDt))) {
						self.$swal.fire({
							icon: 'error',
							text: self.$t('gen.dateRangeStartCannotBeGreater'),
							showCloseButton: true,
							confirmButtonText: self.$t('gen.confirm'),
							buttonsStyling: false, // remove default button style
							customClass: {
								confirmButton: 'btn btn-danger'
							}
						});
						return;
					}
					if (
						(self.bgnDt != null && self.endDt === null)
						|| (self.bgnDt === null && self.endDt != null)
						|| (self.bgnDt === null && self.endDt === null)
					) {
						self.$swal
							.fire({
								icon: 'error',
								text: self.$t('gen.bothStartEndDateRequired'),
								showCloseButton: true,
								confirmButtonText: self.$t('gen.confirm'),
								buttonsStyling: false, // remove default button style
								customClass: {
									confirmButton: 'btn btn-danger'
								}
							})
							.then(function () {
								self.bgnDt = null;
								self.endDt = null;
							});
						return;
					}
					self.getPageData(0);
				}
			});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			const self = this;
			// const url = _.toPageUrl('', page, self.pageable);
			const ret = await self.$api.getDocProQueryPageData(
				self.pageable,
				{
					docCat: self.docCat,
					mainType: self.mainType,
					subType: self.subType,
					bgnDt: _.formatDate(self.bgnDt),
					endDt: _.formatDate(self.endDt),
					docName: self.docName
				}
			);
			self.pageData = ret.data;
		},
		viewSelDoc: async function (docId, docCatName) {
			const self = this;

			const selDocItem = _.filter(self.selDocCat, {
				codeName: docCatName
			});
			let docCatParam = '';
			if (!_.isEmpty(selDocItem)) {
				docCatParam = selDocItem[0].codeValue;
			}

			const ret = await self.$api.getViewSelDoc({
				docCat: docCatParam,
				docId: docId
			});
			if (!ret.data?.length > 0) return;
			self.selDoc = ret.data[0];
			self.isOpenModal = true;
		},
		getProNames(productInfo) {
			if (!productInfo || productInfo.length === 0) {
				return '';
			}
			return _.chain(productInfo).map('proName').join('、 ').value();
		},
		changeModalSize: function () {
			const self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			}
			else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		closeModal: function () {
			const self = this;
			self.isOpenModal = false;
		},
		openModal: function () {
			const self = this;
			self.isOpenModal = true;
		},
		viewFile: async function (targetFileId, filename) {
			const self = this;
			await self.$api.downloadFileApi({ fileId: targetFileId, fileType: 'GenDocFiles' }, filename);
		},
		redirectEditPage: function (docId, docCatName) {
			const self = this;
			self.docCat = self.selDocCat.find(item => item.codeName === docCatName).codeValue;
			self.$router.replace({	name: self.$route.name, query: { docId: docId, docCat: self.docCat } });
		},
		deleteDoc: function (docId) {
			const self = this;
			self.$swal
				.fire({
					icon: 'warning',
					text: self.$t('gen.confirmDeleteDocument'),
					showCloseButton: true,
					confirmButtonText: self.$t('gen.confirm'),
					showCancelButton: true,
					cancelButtonText: self.$t('gen.cancel'),
					buttonsStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger',
						cancelButton: 'btn btn-secondary'
					}
				})
				.then(async function (result) {
					if (result.isConfirmed) {
						const ret = await self.$api.deleteDocApi({
							docId: docId
						});
						self.$swal
							.fire({
								icon: 'success',
								text: self.$t('gen.deleteSuccess'),
								showCloseButton: true,
								confirmButtonText: self.$t('gen.confirm'),
								buttonsStyling: false,
								customClass: {
									confirmButton: 'btn btn-success'
								}
							})
							.then(function (ret) {
								self.getDocument();
							});
					}
				});
		}
	}
};
</script>
