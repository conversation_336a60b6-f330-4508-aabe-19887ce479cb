<template>
	<div role="tabpanel" class="tab-pane fade active show">
		<div class="card card-form mb-3">
			<div class="card-header">
				<h4>{{ $t('gen.documentMaintenance') }}</h4>
				<span class="tx-square-bracket">{{ $t('gen.requiredFields') }}</span>
			</div>
			<vue-form v-slot="{ errors }" ref="queryForm">
				<table class="table table-RWD table-horizontal-RWD table-bordered">
					<tbody>
						<tr>
							<th class="wd-15p tx-require">
								{{ $t('gen.documentTitle') }}
							</th>
							<td class="wd-85p">
								<vue-field
									v-model="docName"
									type="text"
									name="docTitle"
									:label="$t('gen.documentTitle')"
									class="form-control"
									size="70"
									:class="{ 'is-invalid': errors.docTitle }"
									rules="required"
								/>
								<span v-show="errors.docTitle" class="text-danger">{{ errors.docTitle }}</span>
							</td>
						</tr>
						<tr>
							<th class="tx-require">
								{{ $t('gen.effectiveDate') }}
							</th>
							<td>
								<div class="input-group">
									<vue-field
										id="beginDate"
										v-model="validDate"
										type="date"
										name="beginDate"
										:label="$t('gen.effectiveDate')"
										value=""
										class="form-control"
										:class="{ 'is-invalid': errors.beginDate }"
										rules="required"
									/>
									<span v-show="errors.beginDate" class="text-danger">{{ errors.beginDate }}</span>
								</div>
							</td>
						</tr>
						<tr>
							<th class="tx-require">
								{{ $t('gen.expiryDate') }}
							</th>
							<td>
								<div class="input-group">
									<vue-field
										id="endDate"
										v-model="expireDate"
										type="date"
										name="endDate"
										:label="$t('gen.expiryDate')"
										value=""
										class="JQ-datepicker form-control"
										:class="{ 'is-invalid': errors.endDate }"
										rules="required"
									/>
									<span v-show="errors.endDate" class="text-danger">{{ errors.endDate }}</span>
								</div>
							</td>
						</tr>
						<tr>
							<th class="tx-require">
								{{ $t('gen.urgencyLevel') }}
							</th>
							<td>
								<vue-field
									id="mainCatCode"
									v-model="priority"
									as="select"
									name="mainCatCode"
									class="form-select"
									data-inline="true"
									:label="$t('gen.urgencyLevel')"
									:class="{ 'is-invalid': errors.endDate }"
									rules="required"
								>
									<option :value="null">
										{{ $t('gen.pleaseSelect') }}
									</option>
									<option v-for="item in selPriority" :value="item.codeValue">
										{{ item.codeName }}
									</option>
								</vue-field>
								<span v-show="errors.mainCatCode" class="text-danger">{{ errors.mainCatCode }}</span>
							</td>
						</tr>
						<tr>
							<th class="tx-require">
								{{ $t('gen.canProvideToCustomers') }}
							</th>
							<td>
								<vue-field
									v-slot="{field, meta}"
									v-model="showCusYn"
									name="selShowCus"
									:label="$t('gen.canProvideToCustomers')"
									rules="required"
								>
									<RadioGroup
										v-bind="field"
										inline
										:invalid="!meta.valid && meta.validated"
										:options="selShowCus"
										option-label="codeName"
										option-value="codeValue"
									/>
								</vue-field>
								<span v-show="errors.selShowCus" class="text-danger">{{ errors.selShowCus }}</span>
							</td>
						</tr>
						<tr>
							<th>{{ $t('gen.summary') }}</th>
							<td>
								<textarea
									v-model="docDesc"
									class="form-control"
									maxlength="150"
									rows="8"
								/>
								<span class="tx-danger">(150 {{ $t('gen.charactersAvailable') }})</span>
							</td>
						</tr>
						<tr>
							<th>{{ $t('gen.attachmentFile') }}</th>
							<td>
								<vue-form v-slot="{ errors }" ref="fileForm" class="col-lg-12">
									<div class="row g-2">
										<div class="input-group">
											<vue-field
												ref="uploadFile"
												name="uploadFile"
												type="file"
												class="form-control"
												:label="$t('gen.attachment')"
												:class="{ 'is-invalid': errors.uploadFile }"
												:rules="{
													required_file: true,
													mbSize: 15,
													extMgt: validExts,
													validateName: symbols,
													isOverSize: fileCntObj
												}"
												accept=".doc, .docx, .pdf, .xlsx, .txt"
												@change="handleChange($event)"
											/>
											<Button color="info" :label="$t('gen.upload')" @click="addFile" />
										</div>
									</div>
									<ul class="list-group list-inline-tags mt-2">
										<li v-for="file in files" class="list-group-item">
											<ColoredLink @click="previewFile(file.fileNo)">
												<span>{{ file.showName }}</span>
												<span
													class="img-delete"
													:title="$t('gen.delete')"
													@click="deleteFile(file.fileId)"
												/>
											</ColoredLink>
										</li>
									</ul>
									<div style="height: 25px">
										<span v-show="errors.uploadFile" class="text-danger">
											{{ $filters.defaultValue(errors.uploadFile, '--') }}
										</span>
									</div>
								</vue-form>
							</td>
						</tr>
					</tbody>
				</table>

				<div>
					<div class="tx-title mt-3">
						{{ $t('gen.relatedSettings') }}
						<span class="tx-square-bracket">{{ $t('gen.selectRelatedItems') }}</span>
					</div>
					<table class="table table-RWD table-bordered table-horizontal-RWD">
						<tbody>
							<tr v-for="item in selTypeMessage">
								<th v-if="item.codeValue === 'P'" class="wd-100p">
									<Checkbox
										v-model="productYn"
										class="tx-require"
										name="productItems"
										:value="item.codeValue"
										:label="item.codeName"
									/>
								</th>
							</tr>
							<tr>
								<td class="wd-100p">
									<div class="input-group">
										<span class="input-group-text">{{ $t('gen.productCodeName') }}</span>
										<Button :label="$t('gen.productQuickSearch')" @click="openModal()" />
									</div>
								</td>
							</tr>
						</tbody>
					</table>
					<div>
						<table class="table table-RWD table-bordered table-horizontal-RWD" style="width: 50%">
							<thead>
								<tr>
									<th width="20%">
										{{ $t('gen.productCode') }}
									</th>
									<th width="70%">
										{{ $t('gen.productName') }}
									</th>
									<th width="10%">
										{{ $t('gen.execute') }}
									</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in productList">
									<td :data-th="$t('gen.productCode')">
										{{ item.proCode }}
									</td>
									<td :data-th="$t('gen.productName')">
										{{ item.proName }}
									</td>
									<td :data-th="$t('gen.execute')" class="text-center">
										<Button
											icon
											color="danger"
											:title="$t('gen.delete')"
											@click="deleteProduct(item.proCode)"
										>
											<span class="fa-solid fa-trash" />
										</Button>
									</td>
								</tr>
							</tbody>
						</table>
						<CheckboxGroup
							v-model="selProItem"
							:options="selProMessage"
							option-value="codeValue"
							option-label="codeName"
							inline
						/>
					</div>

					<table class="table table-RWD table-bordered table-horizontal-RWD">
						<tbody>
							<tr v-for="item in selTypeMessage">
								<th v-if="item.codeValue === 'M'" class="wd-100p">
									<Checkbox
										v-model="mktYn"
										class="tx-require"
										:value="item.codeValue"
										:label="item.codeName"
									/>
								</th>
							</tr>
							<tr>
								<td class="wd-100p">
									<div class="input-group">
										<span class="input-group-text">{{ $t('gen.mainCategory') }}：</span>
										<select
											id="mainCatCode1"
											v-model="mainCat"
											name="wobMessages.main_cat_code1"
											class="form-select"
										>
											<option :value="null">
												{{ $t('gen.pleaseSelect') }}
											</option>
											<option v-for="item in selMainCat" :value="item.typeCode">
												{{ item.typeName }}
											</option>
										</select>
										<span class="input-group-text">{{ $t('gen.subCategory') }}：</span>
										<select
											id="mainCatCode2"
											v-model="subTypeCode"
											name="wobMessages.main_cat_code2"
											class="form-select"
											:disabled="!mainCat"
										>
											<option :value="null">
												{{ $t('gen.pleaseSelect') }}
											</option>
											<option v-for="item in selSubCat" :value="item.typeCode">
												{{ item.typeName }}
											</option>
										</select>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>

				<table class="table table-RWD table-bordered table-horizontal-RWD">
					<tbody>
						<tr v-for="item in selTypeMessage">
							<th v-if="item.codeValue === 'R'" class="wd-100p">
								<Checkbox
									v-model="researchYn"
									class="tx-require"
									:value="item.codeValue"
									:label="item.codeName"
								/>
							</th>
						</tr>
						<tr>
							<td class="wd-100p">
								<div class="input-group">
									<span class="input-group-text">{{ $t('gen.mainCategory') }}：</span>
									<select
										id="mainCatCode3"
										v-model="researchMainTypeCode"
										name="wobMessages.main_cat_code1"
										class="form-select"
									>
										<option :value="null">
											{{ $t('gen.pleaseSelect') }}
										</option>
										<option v-for="item in selOutlookMainCat" :value="item.typeCode">
											{{ item.typeName }}
										</option>
									</select>
									<span class="input-group-text">{{ $t('gen.subCategory') }}：</span>
									<select
										id="mainCatCode4"
										v-model="researchSubTypeCode"
										name="wobMessages.main_cat_code2"
										class="form-select"
										:disabled="!researchMainTypeCode"
									>
										<option :value="null">
											{{ $t('gen.pleaseSelect') }}
										</option>
										<option v-for="item in selOutlookSubCat" :value="item.typeCode">
											{{ item.typeName }}
										</option>
									</select>
								</div>
							</td>
						</tr>
					</tbody>
				</table>

				<table class="table table-RWD table-bordered table-horizontal-RWD">
					<tbody>
						<tr v-for="item in selTypeMessage">
							<th v-if="item.codeValue === 'I'" class="wd-100p">
								<Checkbox
									v-model="noticeYn"
									class="tx-require"
									:value="item.codeValue"
									:label="item.codeName"
								/>
							</th>
						</tr>
						<tr>
							<td class="wd-100p">
								<input
									id="btn-add1"
									class="btn btn-primary"
									type="button"
									:value="$t('gen.issuerSearch')"
									@click="openNoticeModal()"
								>
							</td>
						</tr>
					</tbody>
				</table>
				<div id="noticeArea">
					<table class="table table-RWD table-bordered table-horizontal-RWD" style="width: 50%">
						<thead>
							<tr>
								<th width="20%">
									{{ $t('gen.issuerCode') }}
								</th>
								<th width="70%">
									{{ $t('gen.issuerName') }}
								</th>
								<th width="10%">
									{{ $t('gen.execute') }}
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="item in noticeList">
								<td :data-th="$t('gen.issuerCode')">
									{{ item.issuerCode }}
								</td>
								<td :data-th="$t('gen.issuerName')">
									{{ item.issuerName }}
								</td>
								<td :data-th="$t('gen.execute')" class="text-center">
									<button
										id="btnDelete1"
										class="btn btn-danger btn-icon JQ-logDelete"
										type="button"
										:data-original-title="$t('gen.delete')"
									>
										<span class="fa-solid fa-trash" @click="deleteNotice(item.issuerCode)" />
									</button>
								</td>
							</tr>
						</tbody>
					</table>

					<table class="table table-RWD table-bordered table-horizontal-RWD">
						<tbody>
							<tr v-for="item in selTypeMessage">
								<th v-if="item.codeValue === 'N'" class="wd-100p">
									<Checkbox
										v-model="newsYn"
										class="tx-require"
										:value="item.codeValue"
										:label="item.codeName"
									/>
								</th>
							</tr>
						</tbody>
					</table>
					<div class="d-flex flex-column m-2" style="gap: 1px; background-color: gainsboro;">
						<CheckboxGroup
							v-model="selReportItem"
							:options="selNewsType"
							option-label="codeName"
							option-value="codeValue"
							inline
							class="bg-white"
						/>
						<!--          持有基金相關商品              -->
						<div class="bg-white">
							<CheckboxGroup
								v-if="selReportItem.includes('F')"
								v-model="selNewRelFundItem"
								:options="selNewRelFundType"
								option-label="codeName"
								option-value="codeValue"
								inline
								class="d-inline"
							/>
							<!-- 給持有相關商品的客戶 投資地區 投資標的-->
							<CheckboxGroup
								v-if="selReportItem.includes('P')"
								v-model="selNewRelProItem"
								:options="selNewsRelProType"
								option-label="codeName"
								option-value="codeValue"
								inline
								class="d-inline"
							/>
						</div>
						<!---->
						<!--         投資標的               -->
						<CheckboxGroup
							v-if="selNewRelProItem.includes('SEC')"
							v-model="checkedProSectors"
							:options="proSectors"
							option-label="sectorName"
							option-value="sectorCode"
							class="bg-white"
							inline
						/>
						<!--         投資地區               -->
						<CheckboxGroup
							v-if="selNewRelProItem.includes('GEO')"
							v-model="checkedProGeoFocus"
							:options="proGeoFocus"
							option-label="geoFocusName"
							option-value="geoFocusCode"
							class="bg-white"
							inline
						/>
					</div>
				</div>
				<div class="text-end p-2">
					<Button :label="$t('gen.save')" @click="saveDocument(docId)" />
				</div>
			</vue-form>
		</div>
	</div>

	<!-- Modal 1-->
	<vue-modal :is-open="isOpenModal" @close="closeModal">
		<template #content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('gen.productQuickSearch') }}
						</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()">
							<i
								class="bi bi-arrows-fullscreen"
							/>
						</button>
						<button type="button" class="btn-close" @click.prevent="props.close()" />
					</div>
					<div class="modal-body">
						<div class="card card-form-collapse">
							<div class="card-header" data-bs-toggle="collapse" data-bs-target="#collapseListGroup1">
								<h4>{{ $t('gen.searchConditions') }}</h4>
							</div>
							<div id="collapseListGroup1" class="collapse show">
								<form>
									<div class="form-row">
										<div class="form-group col-lg-4">
											<label class="form-label">{{ $t('gen.productName') }}：</label>
											<input
												v-model="proName"
												class="form-control"
												type="text"
												maxlength="20"
											>
											<a data-bs-toggle="tooltip" class="table-icon JQ-singleSearch" onclick="result.style.display='';">
												<button
													type="button"
													class="btn btn-dark btn-icon"
													data-bs-toggle="tooltip"
													:title="$t('gen.view')"
												>
													<i class="bi bi-search" @click="getProQuickSearch(proName, '', '')" />
												</button>
											</a>
										</div>
										<div class="form-group col-lg-4">
											<label class="form-label">{{ $t('gen.productCode') }}：</label>
											<input
												v-model="proCode"
												class="form-control"
												type="text"
												maxlength="20"
											>
											<a data-bs-toggle="tooltip" class="table-icon JQ-singleSearch" onclick="result.style.display='';">
												<button
													type="button"
													class="btn btn-dark btn-icon"
													data-bs-toggle="tooltip"
													:title="$t('gen.view')"
												>
													<i class="bi bi-search" @click="getProQuickSearch('', proCode, '')" />
												</button>
											</a>
										</div>
										<div class="form-group col-lg-4">
											<label class="form-label tx-require">{{ $t('gen.productMainCategory') }}：</label>
											<select id="cat2" v-model="proType" class="form-select">
												<option selected="selected" value="">
													{{ $t('gen.all') }}
												</option>
												<option v-for="item in selProCat" :value="item.pfcatCode">
													{{ item.pfcatName }}
												</option>
											</select>
											<a
												id="idn"
												data-bs-toggle="tooltip"
												class="table-icon JQ-singleSearch"
												onclick="result.style.display='';"
											>
												<button
													type="button"
													class="btn btn-dark btn-icon"
													data-bs-toggle="tooltip"
													:title="$t('gen.view')"
												>
													<i class="bi bi-search" @click="getProQuickSearch('', '', proType)" />
												</button>
											</a>
										</div>
									</div>
								</form>
							</div>
							<p class="tx-note">
								{{ $t('gen.fuzzySearchNote') }}
							</p>
						</div>
					</div>

					<div id="result">
						<div class="card card-table mb-3">
							<div class="card-header">
								<h4>{{ $t('gen.searchResults') }}</h4>
								<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
							</div>
							<div class="table-responsive">
								<table class="table table-RWD table-bordered">
									<thead>
										<tr>
											<th width="5%">
												{{ $t('gen.select') }}
											</th>
											<th width="10%">
												{{ $t('gen.productCode') }}
											</th>
											<th width="14%">
												{{ $t('gen.chineseName') }}
											</th>
											<th width="14%">
												{{ $t('gen.englishName') }}
											</th>
											<th width="12%">
												{{ $t('gen.productSubCategory') }}
											</th>
											<th width="10%">
												{{ $t('gen.riskAttribute') }}
											</th>
											<th width="10%">
												{{ $t('gen.productCurrency') }}
											</th>
											<th width="12%">
												{{ $t('gen.canPurchase') }}
											</th>
											<th width="13%">
												{{ $t('gen.productExpiryDate') }}
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="(item, index) in pageData.content" :key="index">
											<td class="text-center" data-th="">
												<input
													v-model="selProducts"
													name="pro_code"
													class="form-check-input"
													type="checkbox"
													:checked="isSelected(item)"
													:value="item"
												>
											</td>
											<td :data-th="$t('gen.productCode')">
												{{ item.bankProCode }}
											</td>
											<td :data-th="$t('gen.chineseName')">
												<a class="link-underline">{{ item.proName }}</a>
											</td>
											<td :data-th="$t('gen.englishName')">
												{{ item.engProName }}
											</td>
											<td :data-th="$t('gen.productSubCategory')">
												{{ item.proSubType }}
											</td>
											<td :data-th="$t('gen.riskAttribute')">
												{{ item.riskName }}
											</td>
											<td :data-th="$t('gen.productCurrency')">
												{{ item.curName }}
											</td>
											<td :data-th="$t('gen.canPurchase')">
												{{ item.buyYn }}
											</td>
											<td :data-th="$t('gen.productExpiryDate')">
												{{ item.expireDt }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div id="btn" class="modal-footer">
							<div class="text-alignRight">
								<input
									id="proBtn"
									name="btnClose"
									class="btn btn-white"
									type="button"
									:value="$t('gen.confirm')"
									@click="chkSelProducts()"
								>
								<button type="button" class="btn btn-white" @click.prevent="props.close()">
									{{ $t('gen.close') }}
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>

	<vue-modal :is-open="isNoticeOpenModal" @close="closeNoticeModal">
		<template #content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('gen.issuerSearch') }}
						</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()">
							<i
								class="bi bi-arrows-fullscreen"
							/>
						</button>
						<button type="button" class="btn-close" @click.prevent="props.close()" />
					</div>
					<div class="modal-body">
						<div id="noticeResult">
							<div class="caption">
								{{ $t('gen.issuerList') }}
							</div>
							<div class="card card-table mb-3">
								<vue-pagination :pageable="noticePageData" :goto-page="gotoNoticePage" />
								<div class="table-responsive">
									<table class="table table-RWD table-hover table-bordered table-padding">
										<thead>
											<tr>
												<th width="10%">
													{{ $t('gen.select') }}
												</th>
												<th width="30%">
													{{ $t('gen.assetCategory') }}
												</th>
												<th width="30%">
													{{ $t('gen.issuerCode') }}
												</th>
												<th width="30%">
													{{ $t('gen.issuerChineseName') }}
												</th>
											</tr>
										</thead>
										<tbody>
											<tr v-for="(item, index) in noticePageData.content" :key="index">
												<td class="text-center" data-th="">
													<input
														v-model="selNotices"
														name="pro_code"
														class="form-check-input"
														type="checkbox"
														:checked="isNoticeSelected(item)"
														:value="item"
													>
												</td>
												<td :data-th="$t('gen.assetCategory')">
													{{ item.pfcatCode }}
												</td>
												<td :data-th="$t('gen.issuerCode')">
													{{ item.issuerCode }}
												</td>
												<td :data-th="$t('gen.issuerChineseName')">
													{{ item.issuerName }}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
					<div id="appointmentFooter1" class="modal-footer">
						<input
							id="noticeBtn"
							name="btnClose"
							class="btn btn-white"
							type="button"
							:value="$t('gen.confirm')"
							@click="chkSelNotice()"
						>
						<button type="button" class="btn btn-white" @click.prevent="props.close()">
							{{ $t('gen.close') }}
						</button>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- Modal 1 End -->
</template>
<script>
import { Field, Form } from 'vee-validate';
import vueModal from '@/views/components/model.vue';
import VuePagination from '@/views/components/pagination.vue';
import moment from 'moment';
import _ from 'lodash';
export default {
	components: {
		'vue-form': Form,
		'vue-field': Field,
		vueModal,
		VuePagination
	},
	data: function () {
		return {
			// 下拉選單
			selPriority: [],
			selShowCus: [],
			selTypeMessage: [],
			selProMessage: [],
			selNewsType: [],
			selNewsRelProType: [],
			selNewRelFundType: [],
			selMainCat: [],
			selSubCat: [],
			selOutlookMainCat: [],
			selOutlookSubCat: [],
			selProCat: [],
			productList: [],
			noticeList: [],

			productInfo: [],
			selProducts: [],
			selNotices: [],
			selProItem: [],
			selReportItem: [],
			selNewRelProItem: [],
			selNewRelFundItem: [],
			fileObject: [],
			fileInfo: [],
			selDoc: {},

			showModal: false,

			proCode: '',
			proName: '',
			proType: '',
			docId: null,
			docCat: null,
			docName: null,
			validDate: null,
			expireDate: null,
			priority: 3,
			showCusYn: null,
			docDesc: null,
			productYn: [], // 產品
			holdCode: null,
			subsCode: null,
			mktYn: [], // 行銷活動
			mainCat: null,
			subTypeCode: null,
			newsYn: [],

			// 分頁元件
			pageable: {
				page: 0,
				size: 10,
				sort: 'PRO_CODE',
				direction: 'ASC'
			},
			// 查詢結果
			pageData: {
				content: {}
			},
			// 分頁元件
			noticePageable: {
				page: 0,
				size: 10,
				sort: 'ISSUER_CODE',
				direction: 'ASC'
			},
			noticePageData: {
				content: {}
			},
			// 檔案處裡
			validExts: ['doc', 'docx', 'pdf', 'xlsx', 'txt'],
			symbols: ['/', ':', '*', '?', '"', '<', '>', '|'],
			tempFile: null,
			uploadFiles: [],
			files: [],
			maxFileCount: 3,
			fileCnt: 0,
			errors: {
				uploadFile: null
			},
			// Modal
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand',

			researchYn: [],
			researchMainTypeCode: null,
			researchSubTypeCode: null,

			isNoticeOpenModal: null,
			noticeYn: [], // 發行機構
			noticeMainTypeCode: null,
			noticeSubTypeCode: null,

			forAllCusYn: null,
			forHaveProCusYn: null,
			forCashRelCusYn: null,

			proSectors: [],
			proGeoFocus: [],

			checkedProSectors: [],
			checkedProGeoFocus: []
		};
	},
	computed: {
		fileCntObj: function () {
			return {
				fieldCnt: this.fileCnt,
				maxFileCount: this.maxFileCount
			};
		}
	},
	watch: {
		selProItem: {
			handler(newVal) {
				const self = this;
				self.holdCode = _.some(newVal, item => typeof item === 'string' && item === 'H') ? 'H' : '';
				self.subsCode = _.some(newVal, item => typeof item === 'string' && item === 'S') ? 'S' : '';
			},
			deep: true
		},
		mainCat: async function (newVal) {
			const self = this;
			if (newVal) {
				const res = await self.getMainCatType('MKTEVENT', newVal);
				self.selSubCat = res;
			}
			else {
				self.subTypeCode = null;
			}
		},
		researchMainTypeCode: async function (newVal) {
			const self = this;
			if (newVal) {
				const res = await self.getMainCatType('MKTOUTLOOK', newVal);
				self.selOutlookSubCat = res;
			}
			else {
				self.selOutlookSubCat = null;
			}
		}
	},
	mounted: async function () {
		const self = this;
		self.getProCat();
		self.docId = self.$route.query.docId;
		self.docCat = self.$route.query.docCat;
		if (!_.isNil(self.docId)) {
			self.getSelDoc(self.docId, self.docCat);
		}
		const [
			selPriority,
			selShowCus,
			selTypeMessage,
			selProMessage,
			selNewsType,
			selNewsRelProType,
			selNewRelFundType
		] = await Promise.all([
			self.getAdmCodeDetail('DOC_PRIORITY'),
			self.getAdmCodeDetail('DOC_SHOWCUS_YN'),
			self.getAdmCodeDetail('DOC_TYPE_MESSAGE'),
			self.getAdmCodeDetail('DOC_P_MESSAGE'),
			self.getAdmCodeDetail('DOC_NEWS_TYPE'),
			self.getAdmCodeDetail('DOC_NEWS_REL_PRO_TYPE'),
			self.getAdmCodeDetail('DOC_NEWS_REL_FUND_TYPE')
		]);

		self.selPriority = selPriority;
		self.selShowCus = selShowCus;
		self.selTypeMessage = selTypeMessage;
		self.selProMessage = selProMessage;
		self.selNewsType = selNewsType;
		self.selNewsRelProType = selNewsRelProType;
		self.selNewRelFundType = selNewRelFundType;
		self.getMainCat();
		self.getOutlookMainCat();
		self.getProSectors();
		self.getProGeoFocus();
	},
	methods: {
		async getProCat() {
			const self = this;
			const ret = await self.$api.getProCatApi();
			self.selProCat = ret.data;
		},
		getMainCat: async function () {
			const self = this;
			const ret = await self.$api.getMainDocTypeApi({
				catCode: 'MKTEVENT'
			});
			if (ret.data) {
				self.selMainCat = ret.data;
			}
		},
		getOutlookMainCat: async function () {
			const self = this;
			const ret = await self.$api.getMainDocTypeApi({
				catCode: 'MKTOUTLOOK'
			});
			if (ret.data) {
				self.selOutlookMainCat = ret.data;
			}
		},
		getMainCatType: async function (catCode, mainTypeCode) {
			const self = this;
			const data = { catCode: catCode, mainTypeCode: mainTypeCode };
			const reqData = _.omitBy(data, value => _.isNil(value) || value === '');
			const ret = await self.$api.getMainDocTypeApi(reqData);
			return ret.data;
		},
		getAdmCodeDetail: async function (codeType) {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: codeType
			});
			return ret.data;
		},
		getProQuickSearch: function (proName, proCode, proType) {
			const self = this;
			self.getPageData(0, proName, proCode, proType);
		},
		getPageData: async function (page, proName, proCode, proType) {
			const self = this;
			const url = _.toPageUrl('', page, self.pageable);
			const ret = await self.$api.getDocProPageData(
				{
					proName: proName,
					proCode: proCode,
					proType: proType
				},
				url
			);
			self.pageData = ret.data;
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getNoticePageData: async function (page) {
			const self = this;
			const url = _.toPageUrl('', page, self.noticePageable);
			const ret = await self.$api.getDocProNoticePageData(url);

			self.noticePageData = ret.data;
		},
		gotoNoticePage: function (page) {
			this.noticePageable.page = page;
			this.getNoticePageData(page);
		},
		getSelDoc: async function (docId, docCat) {
			const self = this;
			const ret = await self.$api.getViewSelDoc({
				docCat: docCat,
				docId: docId
			});
			if (!ret.data?.length > 0) return;
			self.selDoc = ret.data[0];
			self.docName = self.selDoc.docName;
			self.validDate = moment(self.selDoc.validDt).format('YYYY-MM-DD');
			self.expireDate = moment(self.selDoc.expireDt).format('YYYY-MM-DD');
			self.priority = self.selDoc.priority;
			self.showCusYn = self.selDoc.showCusYn;
			self.docDesc = self.selDoc.docDesc;
			self.productList = self.selDoc.productInfo;
			self.holdName = self.selDoc.holdName;
			self.subsName = self.selDoc.subsName;
			self.mainCat = self.selDoc.gdmMainTypeCode;
			self.subTypeCode = self.selDoc.gdmSubTypeCode;
			if (!_.isEmpty(self.selDoc.gdmMainTypeCode)) {
				self.mktYn.push('M');
			}
			if (!_.isEmpty(self.selDoc.gmoMainTypeCode)) {
				self.researchYn.push('R');
			}
			self.researchMainTypeCode = self.selDoc.gmoMainTypeCode;
			self.researchMainTypeCode = self.selDoc.gmoSubTypeCode;
			if (!_.isEmpty(self.selDoc.issuerInfo)) {
				self.noticeYn.push('I');
			}

			_.forEach(self.selDoc.issuerInfo, function (issuer) {
				self.noticeList.push(_.pick(issuer, ['issuerCode', 'issuerName', 'pfcatCode']));
			});

			if (!_.isEmpty(self.selDoc.newsAllCusYn)) {
				self.newsYn.push('N');
				self.selReportItem.push('A');
			}

			if (!_.isEmpty(self.selDoc.newsConditionInfo)) {
				let geoYn = false;
				let secYn = false;
				let fndYn = false;
				_.forEach(self.selDoc.newsConditionInfo, function (newsCondition) {
					if (_.isEqual(newsCondition.condType, 'GEO')) {
						geoYn = true;
						self.checkedProGeoFocus.push(newsCondition.condMajorCode);
					}
					if (_.isEqual(newsCondition.condType, 'SEC')) {
						secYn = true;
						self.checkedProSectors.push(newsCondition.condMajorCode);
					}
					if (_.isEqual(newsCondition.condType, 'FND')) {
						fndYn = true;
						self.selNewRelFundItem.push(newsCondition.condMajorCode);
					}
				});
				if (geoYn == true) {
					self.selNewRelProItem.push('GEO');
					if (!self.selReportItem.includes('P')) {
						self.selReportItem.push('P');
					}
				}
				if (secYn == true) {
					self.selNewRelProItem.push('SEC');
					if (!self.selReportItem.includes('P')) {
						self.selReportItem.push('P');
					}
				}
				if (fndYn == true) {
					self.selReportItem.push('F');
				}
			}

			self.files = _.clone(self.selDoc.fileInfo) ?? [];
			_.forEach(self.files, function (file) {
				file.fileNo = file.docFileId;
			});
			if (self.holdName == '目前持有該商品客戶') {
				self.selProItem.push('H');
				$('#H').prop('checked', true);
			}
			if (self.subsName == '有訂閱此商品服務的客戶') {
				self.selProItem.push('S');
				$('#S').prop('checked', true);
			}
			self.addOldPro();
		},
		chkSelNotice: function () {
			const self = this;
			if (_.isEmpty(self.selNotices)) {
				self.$swal.fire({
					icon: 'error',
					text: `${self.$t('gen.pleaseSelect')}機構`,
					showCloseButton: true,
					confirmButtonText: self.$t('gen.confirm'),
					buttonStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			if (self.selNotices) {
				_.forEach(self.selNotices, function (item) {
					if (!_.find(self.noticeList, { issuerCode: item.issuerCode })) {
						self.noticeList.push(_.pick(item, ['issuerCode', 'issuerName', 'pfcatCode']));
					}
				});
			}

			// self.addNewPro(self.productList);
			self.$swal
				.fire({
					icon: 'success',
					text: '新增成功',
					showCloseButton: true,
					confirmButtonText: self.$t('gen.confirm'),
					buttonsStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-success'
					}
				})
				.then(function () {
					self.isNoticeOpenModal = false;
				});
		},
		chkSelProducts: function () {
			const self = this;
			if (_.isEmpty(self.selProducts)) {
				self.$swal.fire({
					icon: 'error',
					text: `${self.$t('gen.pleaseSelect')}商品`,
					showCloseButton: true,
					confirmButtonText: self.$t('gen.confirm'),
					buttonStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}

			if (self.selProducts) {
				_.forEach(self.selProducts, function (item) {
					if (!_.find(self.productList, { proCode: item.proCode })) {
						self.productList.push(_.pick(item, ['proCode', 'proName']));
					}
				});
			}

			self.addNewPro(self.productList);
			self.$swal
				.fire({
					icon: 'success',
					text: '新增成功',
					showCloseButton: true,
					confirmButtonText: self.$t('gen.confirm'),
					buttonsStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-success'
					}
				})
				.then(function () {
					self.isOpenModal = false;
				});
		},
		deleteProduct: function (proCode) {
			const self = this;
			self.$swal
				.fire({
					icon: 'warning',
					text: `是否要${self.$t('gen.delete')}此商品？`,
					showCloseButton: true,
					confirmButtonText: self.$t('gen.confirm'),
					showCancelButton: true,
					cancelButtonText: '取消',
					buttonsStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger',
						cancelButton: 'btn btn-secondary'
					}
				})
				.then(function (result) {
					if (result.isConfirmed) {
						_.remove(self.productList, item => item.proCode === proCode);
						_.remove(self.selProducts, item => item.proCode === proCode);
						self.$swal.fire({
							icon: 'success',
							text: `${self.$t('gen.delete')}成功`,
							showCloseButton: true,
							confirmButtonText: self.$t('gen.confirm'),
							buttonsStyling: false, // remove default button style
							customClass: {
								confirmButton: 'btn btn-success'
							}
						});
					}
				});
		},
		deleteNotice: function (issuerCode) {
			const self = this;
			self.$swal
				.fire({
					icon: 'warning',
					text: `是否要${self.$t('gen.delete')}此機構？`,
					showCloseButton: true,
					confirmButtonText: self.$t('gen.confirm'),
					showCancelButton: true,
					cancelButtonText: '取消',
					buttonsStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger',
						cancelButton: 'btn btn-secondary'
					}
				})
				.then(function (result) {
					if (result.isConfirmed) {
						_.remove(self.noticeList, item => item.issuerCode === issuerCode);
						_.remove(self.selNotices, item => item.issuerCode === issuerCode);
						self.$swal.fire({
							icon: 'success',
							text: `${self.$t('gen.delete')}成功`,
							showCloseButton: true,
							confirmButtonText: self.$t('gen.confirm'),
							buttonsStyling: false, // remove default button style
							customClass: {
								confirmButton: 'btn btn-success'
							}
						});
					}
				});
		},
		addOldPro: function () {
			const self = this;
			_.forEach(self.productList, function (item) {
				self.productInfo.push({
					oldPro: item.proCode
				});
			});
		},
		addNewPro: function (newProductList) {
			const self = this;
			_.forEach(newProductList, function (item) {
				const exists
					= _.find(self.productInfo, function (info) {
						return info.newPro === item.proCode || info.oldPro === item.proCode;
					}) !== undefined;
				if (!exists) {
					self.productInfo.push({
						newPro: item.proCode
					});
				}
			});
		},
		addNewNotice: function () { },
		isSelected: function (item) {
			const self = this;
			return _.some(self.productList, { proCode: item.proCode });
		},
		isNoticeSelected: function (item) {
			const self = this;
			return _.some(self.noticeList, { issuerCode: item.issuerCode });
		},
		reportItemDisable: function (item) {
			const self = this;
			if (_.isEqual(item.codeValue, 'A')) {
				self.selNewsType.forEach(function (newsItem) {
					if (!_.isEqual(newsItem.codeValue, 'A') && self.selReportItem.includes('A')) {
						newsItem.disable = true;
					}
					else {
						newsItem.disable = false;
					}
					if (self.selReportItem.includes('F') || self.selReportItem.includes('P')) {
						let index = self.selReportItem.indexOf('F');
						if (index !== -1) {
							self.selReportItem.splice(index, 1);
						}
						index = self.selReportItem.indexOf('P');
						if (index !== -1) {
							self.selReportItem.splice(index, 1);
						}
						self.selNewRelFundType = [];
						self.selNewsRelProType = [];
						self.proSectors = [];
						self.proGeoFocus = [];
					}
				});
			}
		},
		saveDocument: async function (docId) {
			const self = this;
			const pass = await self.$refs.queryForm.validate();
			if (!pass.valid) return;

			if (!_.isNil(self.validDate) && !_.isNil(self.expireDate)) {
				const validDateObj = new Date(self.validDate);
				const expireDateObj = new Date(self.expireDate);
				if (expireDateObj < validDateObj) {
					self.$swal.fire({
						icon: 'error',
						text: self.$t('gen.expiryDateCannotBeEarlier'),
						showCloseButton: true,
						confirmButtonText: self.$t('gen.confirm'),
						buttonStyling: false, // remove default button style
						customClass: {
							confirmButton: 'btn btn-danger'
						}
					});
					return;
				}
			}
			if (
				_.isEmpty(self.mktYn)
				&& _.isEmpty(self.productYn)
				&& _.isEmpty(self.researchYn)
				&& _.isEmpty(self.noticeYn)
				&& _.isEmpty(self.newsYn)
			) {
				self.$swal.fire({
					icon: 'error',
					text: self.$t('gen.pleaseSelectRelatedSettings'),
					showCloseButton: true,
					confirmButtonText: self.$t('gen.confirm'),
					buttonStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}
			if (self.productYn.includes('P')) {
				if (_.isEmpty(self.productList)) {
					self.$swal.fire({
						icon: 'error',
						text: self.$t('gen.pleaseSelectProduct'),
						showCloseButton: true,
						confirmButtonText: self.$t('gen.confirm'),
						buttonStyling: false, // remove default button style
						customClass: {
							confirmButton: 'btn btn-danger'
						}
					});
					return;
				}

				if (_.isEmpty(self.selProItem)) {
					self.$swal.fire({
						icon: 'error',
						text: self.$t('gen.pleaseSelectNotificationTarget'),
						showCloseButton: true,
						confirmButtonText: self.$t('gen.confirm'),
						buttonStyling: false, // remove default button style
						customClass: {
							confirmButton: 'btn btn-danger'
						}
					});
					return;
				}
			}
			if (self.mktYn.includes('M')) {
				if (_.isNil(self.mainCat) || _.isNil(self.subTypeCode)) {
					self.$swal.fire({
						icon: 'error',
						text: self.$t('gen.pleaseSelectMainSubCategory'),
						showCloseButton: true,
						confirmButtonText: self.$t('gen.confirm'),
						buttonStyling: false, // remove default button style
						customClass: {
							confirmButton: 'btn btn-danger'
						}
					});
					return;
				}
			}
			let errMsg = '';
			let validFlag = false;
			if (self.researchYn.includes('R')) {
				if (_.isNil(self.researchMainTypeCode) || _.isNil(self.researchSubTypeCode)) {
					validFlag = true;
					errMsg = self.$t('gen.pleaseSelectResearchCategory');
				}
			}
			if (self.noticeYn.includes('I')) {
				if (_.isEmpty(self.noticeList)) {
					validFlag = true;
					errMsg = self.$t('gen.selectAtLeastOneIssuer');
				}
			}

			if (self.newsYn.includes('N')) {
				if (_.isEmpty(self.selReportItem)) {
					validFlag = true;
					errMsg = self.$t('gen.pleaseSelectNotificationTarget');
				}
				if (self.selReportItem.includes('P')) {
					if (_.isEmpty(self.selNewRelProItem)) {
						validFlag = true;
						errMsg = self.$t('gen.pleaseSelectInvestmentTargetOrRegion');
					}
					if (self.selNewRelProItem.includes('SEC')) {
						if (_.isEmpty(self.checkedProSectors)) {
							validFlag = true;
							errMsg = self.$t('gen.selectAtLeastOneInvestmentTarget');
						}
					}
					if (self.selNewRelProItem.includes('GEO')) {
						if (_.isEmpty(self.checkedProGeoFocus)) {
							validFlag = true;
							errMsg = self.$t('gen.selectAtLeastOneInvestmentRegion');
						}
					}
				}
				if (self.selReportItem.includes('F')) {
					if (_.isEmpty(self.selNewRelFundItem)) {
						validFlag = true;
						errMsg = self.$t('gen.selectAtLeastOneFundType');
					}
				}
			}
			if (validFlag == true) {
				self.$swal.fire({
					icon: 'error',
					text: errMsg,
					showCloseButton: true,
					confirmButtonText: self.$t('gen.confirm'),
					buttonStyling: false, // remove default button style
					customClass: {
						confirmButton: 'btn btn-danger'
					}
				});
				return;
			}
			self.postOrPatchDocument(docId);
			const errField = Object.keys(this.$refs.queryForm.errors)[0];
			if (errField) {
				document.querySelector(`[name="${errField}"]`).focus();
			}
		},
		postOrPatchDocument: async function (docId) {
			const self = this;
			const proCodes = _.map(self.productList, 'proCode');
			const data = {
				docId: self.docId,
				docName: self.docName,
				validDate: _.formatDate(self.validDate),
				expireDate: _.formatDate(self.expireDate),
				priority: self.priority,
				showCusYn: self.showCusYn,
				docDesc: self.docDesc,
				productYn: self.productYn.includes('P') ? 'Y' : 'N',
				productList: proCodes,
				holdCode: self.holdCode,
				subsCode: self.subsCode,
				mktYn: self.mktYn.includes('M') ? 'Y' : 'N',
				mainTypeCode: self.mainCat,
				subTypeCode: self.subTypeCode,
				mktOutLookYn: self.researchYn.includes('R') ? 'Y' : 'N',
				outLookMainTypeCode: self.researchMainTypeCode,
				outLookSubTypeCode: self.researchSubTypeCode,
				issuerYn: self.noticeYn.includes('I') ? 'Y' : 'N',
				issuerList: self.noticeList,
				newsYn: self.newsYn.includes('N') ? 'Y' : 'N',
				newsAllCusYn: self.selReportItem.includes('A') ? 'Y' : 'N',
				newsFnd: self.selReportItem.includes('F') ? 'Y' : 'N',
				newsSecList: self.checkedProSectors,
				newsGeoList: self.checkedProGeoFocus,
				fileInfo: self.files.reduce(
					function (result, file) {
						const msgAppendixReq = _.cloneDeep(file);
						msgAppendixReq.docFileId = file.fileNo;
						result.push(msgAppendixReq);
						return result;
					},
					[]
				),
				productInfo: self.productInfo
			};

			const formData = new FormData();
			const jsonString = JSON.stringify(data);
			formData.append('docInfo', new Blob([jsonString], { type: 'application/json' }));
			self.files.forEach(function (fileInfo) {
				if (!fileInfo.file) return;
				formData.append('fileNo', fileInfo.fileNo);
				formData.append('fileObject', fileInfo.file);
			});
			let apiMethod = 'POST';
			let successText = self.$t('gen.addSuccess');
			if (docId != null) {
				// 編輯
				apiMethod = 'PATCH';
				successText = self.$t('gen.editSuccess');
			}

			await self.$api.postOrPatchDoc({
				httpMethod: apiMethod,
				formData: formData
			});
			self.$swal
				.fire({
					icon: 'success',
					text: successText,
					showCloseButton: true,
					confirmButtonText: self.$t('gen.confirm'),
					buttonsStyling: false,
					customClass: {
						confirmButton: 'btn btn-success'
					}
				});
		},
		// 檔案處裡
		handleChange: function (event) {
			const self = this;
			self.fileTemp = event.target.files[0];
			self.fileCnt = self.files.length;
		},
		addFile: async function () {
			const self = this;
			const validationResult = await self.$refs.fileForm.validate();
			if (!validationResult.valid) return;
			if (self.files.length >= self.maxFileCount) return;

			const fileInfo = {
				fileNo: self.generatorId('file'),
				groupId: null,
				showName: self.fileTemp.name,
				fileName: null,
				contentType: self.fileTemp.contentType,
				filePath: null,
				file: self.fileTemp
			};
			self.files.push(fileInfo);
			self.$refs.fileForm.resetForm();
			self.$refs.uploadFile.$el.value = null;
		},
		previewFile: async function (targetFileId) {
			const self = this;
			const index = self.files.findIndex(f => f.fileNo === targetFileId);
			const fileInfo = self.files[index];
			let url;
			// 預覽待{{ $t('gen.upload') }}檔案
			if (fileInfo.file) {
				url = URL.createObjectURL(fileInfo.file);
				const previewWindow = window.open(url, '_blank');
				previewWindow.document.title = fileInfo.showName;
				previewWindow.addEventListener('beforeunload', () => {
					URL.revokeObjectURL(url);
				});
				// 預覽伺服器檔案
			}
			else {
				self.$api.downloadFileApi({ fileId: targetFileId, fileType: 'GenDocFiles' });
			}
		},
		deleteFile: function (targetFileId) {
			const self = this;
			const index = self.files.findIndex(f => f.fileId === targetFileId);
			if (index === -1) return;
			self.files.splice(index, 1);
		},
		generatorId: function (name) {
			return name + '-' + _.now() + _.random(0, 99);
		},
		// Modal相關
		changeModalSize: function () {
			const self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			}
			else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		openModal: function () {
			const self = this;
			self.isOpenModal = true;
		},
		closeNoticeModal: function () {
			const self = this;
			self.isNoticeOpenModal = false;
		},
		openNoticeModal: function () {
			const self = this;
			self.isNoticeOpenModal = true;
			this.getNoticePageData(0);
		},
		getProSectors: async function () {
			const self = this;
			const ret = await self.$api.getProSectorsApi();
			self.proSectors = ret.data;
		},
		getProGeoFocus: async function () {
			const self = this;
			const ret = await self.$api.getProGeoFocusApi();
			self.proGeoFocus = ret.data;
		}
	}
};
</script>
