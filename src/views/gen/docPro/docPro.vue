<template>
	<div class="tab-nav-main">
		<Tabs
			v-model="tab"
			:tabs="tabs"
			variant="pill"
			class="tab"
		/>
		<component :is="componentId" />
	</div>
</template>
<script>
import { useTabs } from '@/composables/useTabs';
import { defineAsyncComponent } from 'vue';
export default {
	components: {
		vueDocProQuery: defineAsyncComponent(() => import('./include/docProQuery.vue')),
		vueDocPro: defineAsyncComponent(() => import('./include/docPro.vue'))
	},
	setup() {
		return useTabs('M41-00');
	},
	computed: {
		query() {
			return this.$route.query;
		}
	},
	watch: {
		query(val) {
			if (val.docId && val.docCat) this.tab = this.idCodeMap['vue-doc-pro'];
			else this.tab = this.idCodeMap['vue-doc-pro-query'];
		},
		tab(val) {
			// 如果還沒有msgId 或者已經在查詢組件，則不需要切換路由
			if (this.query.docId == null || this.codeIdMap[val] !== 'vue-doc-pro-query') return;
			this.$router.replace({ ...this.$route, query: { } });
		}
	}
};
</script>
