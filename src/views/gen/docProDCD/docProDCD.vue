<template>
	<div class="tab-nav-main">
		<Tabs v-model="tab" :tabs variant="pill">
			<template #content="{ tab }">
				{{ tab.name }} ({{ docProTypeCodeMap[tabCodeProTypeMap[tab.code]]?.cnt ?? '0' }})
			</template>
		</Tabs>
		<vueDocProDcd :selected-type-code />
	</div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import vueDocProDcd from './include/docProDCD.vue';
import { useTabs } from '@/composables/useTabs';
import api from '@/api/apiService';

const { tab, tabs } = useTabs('M41-01');

const tabCodeProTypeMap = {
	'M41-010': 'FUND',
	'M41-011': 'ETF',
	'M41-012': 'FB',
	'M41-013': 'PFD',
	'M41-014': 'SP',
	'M41-015': 'DCD',
	'M41-016': 'INS'
};
const docProTypeCodeMap = ref([]);
const selectedTypeCode = computed(() => tabCodeProTypeMap[tab.value]);

async function getDocProTypeCnt() {
	const ret = await api.getDocProTypeCntApi();
	if (!ret.data?.length > 0) return;
	docProTypeCodeMap.value = Object.fromEntries(ret.data.map(it => [it.proTypeCode, it]));
}

onMounted(getDocProTypeCnt);
</script>
