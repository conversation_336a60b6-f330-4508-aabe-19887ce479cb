<template>
	<div class="tab-content">
		<div class="tab-pane fade active show">
			<div class="card card-table mb-3">
				<div class="card-header">
					<h4>{{ $t('gen.documentList') }}</h4>
					<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
				</div>
				<div class="table-responsive">
					<table class="table table-RWD table-hover table-bordered table-padding">
						<thead>
							<tr>
								<th width="40%">
									{{ $t('gen.documentTitle') }}
								</th>
								<th width="10%">
									{{ $t('gen.effectiveDate') }}
								</th>
								<th width="10%">
									{{ $t('gen.expiryDate') }}
								</th>
								<th width="12%">
									{{ $t('gen.canProvideToCustomers') }}
								</th>
								<th width="9%">
									{{ $t('gen.urgencyLevel') }}
								</th>
								<th width="12%">
									{{ $t('gen.productName') }}
								</th>
								<th width="9%">
									{{ $t('gen.view') }}
								</th>
								<th width="10%">
									{{ $t('gen.relatedAttachments') }}
								</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="data in pageData.content">
								<td :data-th="$t('gen.documentTitle')">
									{{ data.docName }}
								</td>
								<td :data-th="$t('gen.effectiveDate')">
									{{ $filters.formatDate(data.validDt) }}
								</td>
								<td :data-th="$t('gen.expiryDate')">
									{{ $filters.formatDate(data.expireDt) }}
								</td>
								<td :data-th="$t('gen.canProvideToCustomers')">
									{{ data.showCusYnName === 'Y' ? $t('gen.canProvideToCustomer') : $t('gen.internalUseOnly') }}
								</td>
								<td :data-th="$t('gen.urgencyLevel')">
									{{ data.priorityName }}
								</td>
								<td :data-th="$t('gen.productName')">
									{{ data.proList?.map((product) => product.proName).join('；') }}
								</td>
								<td :data-th="$t('gen.execute')" class="text-center">
									<button
										type="button"
										class="btn btn-dark btn-icon"
										:title="$t('gen.view')"
										@click="viewSelDoc(data.docId)"
									>
										<i class="bi bi-search" />
									</button>
								</td>
								<td :data-th="$t('gen.relatedAttachments')" class="text-center">
									<div class="d-flex flex-column align-items-start">
										<ColoredLink v-for="file in data.fileList" :key="file.docFileId" @click="viewFile(file)">
											{{ file.showName }}
										</ColoredLink>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
	<!-- Modal -->
	<vue-modal :is-open="isOpenModal" @close="closeModal">
		<template #content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('gen.documentView') }}
						</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()">
							<i class="bi bi-arrows-fullscreen" />
						</button>
						<button
							type="button"
							class="btn-close"
							aria-label="Close"
							@click.prevent="props.close()"
						/>
					</div>
					<DocumentInfo class="modal-body" :document="selDoc" @click:attachment="viewFile">
						<template #eatra-field>
							<DocumentInfoField :title="$t('gen.relatedProducts')">
								{{ selDoc.productInfo?.map((product) => product.proName ?? '???').join('、') }}
							</DocumentInfoField>
							<DocumentInfoField :title="$t('gen.notificationTarget')">
								{{
									selDoc.holdName && selDoc.subsName
										? `${selDoc.holdName},${selDoc.subsName}`
										: (selDoc.holdName ?? selDoc.subsName)
								}}
							</DocumentInfoField>
						</template>
					</DocumentInfo>

					<div id="appointmentFooter" class="modal-footer">
						<input
							id="appointmentCloseButton"
							name="btnClose"
							class="btn btn-white"
							type="button"
							:value="$t('gen.close')"
							@click.prevent="props.close()"
						>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- Modal End -->
</template>
<script>
import vuePagination from '@/views/components/pagination.vue';
import vueModal from '@/views/components/model.vue';
import _ from 'lodash';
export default {
	components: {
		vuePagination,
		vueModal
	},
	props: {
		selectedTypeCode: {
			type: String,
			default: ''
		}
	},
	data: function () {
		return {
			pageData: {
				content: {}
			},
			selDoc: {},

			pageable: {
				page: 0,
				size: 10,
				sort: 'DOC_ID',
				direction: 'ASC'
			},
			// Modal
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand'
		};
	},
	watch: {
		selectedTypeCode: {
			immediate: true,
			handler: function () {
				const self = this;
				self.gotoPage(0);
			}
		}
	},
	methods: {
		close: function () {
			this.isOpenModal = false;
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (page) {
			const self = this;
			const url = _.toPageUrl('', page, self.pageable);
			const ret = await self.$api.getDocProDCDPageData(
				{
					proTypeCode: self.selectedTypeCode
				},
				url
			);
			self.pageData = ret.data;
		},
		viewSelDoc: async function (docId) {
			const self = this;
			const ret = await self.$api.getViewSelDoc({
				docCat: 'PRODUCT',
				docId: docId
			});
			if (!ret.data?.length > 0) return;
			self.selDoc = ret.data[0];
			self.isOpenModal = true;
		},
		viewFile: async function ({ docFileId }) {
			const self = this;
			await self.$api.downloadFileApi({ fileId: docFileId, fileType: 'GenDocFiles' });
		},
		changeModalSize: function () {
			const self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			}
			else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		closeModal: function () {
			const self = this;
			self.isOpenModal = false;
		},
		openModal: function () {
			const self = this;
			self.isOpenModal = true;
		}
	}
};
</script>
