<template>
	<div v-bind="$attrs" role="tabpanel" class="tab-pane fade show active">
		<div class="card card-form">
			<div class="card-header">
				<h4>{{ $t('gen.searchConditions') }}</h4>
				<span class="tx-square-bracket">{{ $t('gen.requiredFields') }}</span>
			</div>
			<vue-form v-slot="{ errors }" ref="queryForm">
				<div id="formsearch1" class="card-body collapse show">
					<form>
						<div class="form-row">
							<div class="form-group col-lg-4">
								<label class="form-label">{{ $t('gen.messageType') }}</label>
								<Select
									v-model="msgCode"
									name="msgCode"
									:options="messageTypeOptions"
									option-label="msgName"
									option-value="msgCode"
								/>
							</div>
							<div class="form-group col-lg-4">
								<label class="form-label">{{ $t('gen.mainCategory') }}</label>
								<Select
									v-model="mainCatCode"
									name="mainCatCode"
									:options="mainCategoryOptions"
									option-label="catName"
									option-value="catCode"
								/>
							</div>
							<div class="form-group col-lg-4">
								<label class="form-label">{{ $t('gen.subCategory') }}</label>
								<Select
									v-model="subCatCode"
									name="subCatCode"
									:disabled="!mainCatCode"
									option-label="catName"
									option-value="catCode"
									:options="subCategoryOptions"
								/>
							</div>
						</div>

						<div class="form-row">
							<div class="form-group col-lg-4">
								<label class="form-label">{{ $t('gen.announcementTitle') }}</label>
								<vue-field v-slot="{ field, errors }" name="msgTitle">
									<input
										v-bind="field"
										v-model="msgTitle"
										class="form-control"
										type="text"
										maxlength="20"
									>
									<span class="text-danger">{{ errors[0] }}</span>
								</vue-field>
							</div>
							<div class="form-group col-lg-4">
								<label class="form-label">{{ $t('gen.isExpired') }}</label>
								<RadioGroup
									v-model="expiredYn"
									:options="expireOptions"
									option-label="codeName"
									option-value="codeValue"
									inline
								/>
							</div>

							<div class="form-group col-lg-4">
								<label class="form-label tx-require">{{ $t('core.announcementDate') }}</label>

								<vue-field
									id="validBgnDt"
									v-model="validBgnDt"
									type="date"
									name="validBgnDt"
									size="13"
									label="有效日期起"
									class="form-control mn-wd-30p"
									maxlength="10"
									rules="required"
									:class="{ 'is-invalid': showErrors && errors.validBgnDt }"
								/>

								<span class="input-group-text">~</span>

								<vue-field
									id="validEndDt"
									v-model="validEndDt"
									type="date"
									name="validEndDt"
									size="13"
									label="有效日期迄"
									class="form-control mn-wd-30p"
									maxlength="10"
									rules="required"
									:min="minValidEndDt"
									:class="{ 'is-invalid': showErrors && errors.validEndDt }"
								/>
							</div>
							<span v-show="showErrors && (errors.validBgnDt || errors.validEndDt)" class="text-danger">
								{{ errors.validBgnDt }} {{ errors.validEndDt }}
							</span>
						</div>

						<div class="form-footer">
							<button class="btn btn-primary btn-glow" type="button" @click.prevent="submitForm">
								{{ $t('gen.search') }}
							</button>
						</div>
					</form>
				</div>
			</vue-form>
		</div>
		<DataTable
			v-if="pageData.totalElements > 0"
			:title="$t('gen.searchResults')"
			:rows="pageData.selMsgList"
			:columns="columns"
			:pagination="pageData"
			@page="gotoPage"
		>
			<template #body-cell-importance="{item}">
				<i
					v-if="item.importantYn === 'Y'"
					class="tx-red bi-exclamation"
					style="font-size: 24px"
				/>
			</template>
			<template #body-cell-announcementDate="{item}">
				{{ item.validBgnDt }}<br>~{{ item.validEndDt }}
			</template>
			<template #body-cell-announcementTitle="{item}">
				<a href="#" class="link-underline" @click.prevent="getView(item.msgId)">
					{{ item.msgTitle }}
				</a>
			</template>
		</DataTable>
	</div>
	<!-- Modal -->
	<vue-modal :is-open="isOpenModal" @close="closeModal">
		<template #content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('gen.bbsMessage') }}
						</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()">
							<i
								class="bi bi-arrows-fullscreen"
							/>
						</button>
						<button type="button" class="btn-close" @click.prevent="props.close()" />
					</div>
					<div class="modal-body">
						<table class="table table-bordered">
							<caption>
								{{ $t('gen.bbsCategory') }}
							</caption>
							<tbody>
								<tr>
									<th>{{ $t('gen.messageType') }}</th>
									<td>{{ selMsg.msgName }}</td>
								</tr>
							</tbody>
						</table>

						<table class="table table-bordered">
							<caption>
								{{ $t('gen.bbsContent') }}
							</caption>
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('gen.importance') }}
									</th>
									<td>{{ subTypeName(selMsg.importantYn) }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.mainCategory') }}</th>
									<td>{{ selMsg.mainCatName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.subCategory') }}</th>
									<td>{{ selMsg.subCatName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.validDate') }}</th>
									<td>{{ $t('gen.from') }} {{ selMsg.validBgnDt }} ~ {{ $t('gen.to') }} {{ selMsg.validEndDt }}</td>
								</tr>
								<tr>
									<th><span>{{ $t('gen.bbsTitle') }}</span></th>
									<td>{{ selMsg.msgTitle }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.bbsContent') }}</th>
									<td>{{ selMsg.msgContent }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.showOnHomepage') }}</th>
									<td>{{ subTypeName(selMsg.showYn) }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.uploadFile') }}</th>
									<td>
										<ul v-for="file in selMsg.fileList" class="list-group list-inline-tags">
											<li class="list-group-item">
												<ColoredLink @click="viewFile(file.msgFileId)">
													<span>{{ file.showName }}</span>
												</ColoredLink>
											</li>
										</ul>
									</td>
								</tr>
								<tr>
									<th>{{ $t('gen.link') }}</th>
									<td>
										<a :href="selMsg.favoriteLink" target="_blank">{{ selMsg.favoriteLink }}</a>
									</td>
								</tr>
							</tbody>
						</table>

						<table class="table table-bordered">
							<caption>
								{{ $t('gen.maintenanceInfo') }}
							</caption>
							<tbody>
								<tr>
									<th>{{ $t('gen.creator') }}</th>
									<td>{{ selMsg.createUser }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.creatorExt') }}</th>
									<td>{{ selMsg.createUserExt }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.createDate') }}</th>
									<td>{{ selMsg.createDt }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.lastMaintainer') }}</th>
									<td>{{ selMsg.modifyUser }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.maintainerExt') }}</th>
									<td>{{ selMsg.modifyUserExt }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.lastMaintenanceDate') }}</th>
									<td>{{ selMsg.modifyDt }}</td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-white" @click.prevent="props.close()">
							{{ $t('gen.closeWindow') }}
						</button>
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- Modal End -->
</template>
<script>
import vueModal from '@/views/components/model.vue';
import { Field, Form } from 'vee-validate';
import _ from 'lodash';
export default {
	components: {
		vueModal,
		'vue-form': Form,
		'vue-field': Field
	},
	props: {
		msgId: String
	},
	data: function () {
		return {
			selMessageMap: [], // 公告類別資料
			selMsgMainCat: [], // 主分類資料
			selMsgSubCat: [], // 次分類資料
			selOptionYn: [
				{ codeName: this.$t('gen.yes'), codeValue: 'Y' },
				{ codeName: this.$t('gen.no'), codeValue: 'N' }
			],

			msgCode: null,
			mainCatCode: null,
			subCatCode: null,
			validBgnDt: null,
			validEndDt: null,
			msgTitle: null,

			showErrors: false,
			selImportentYn: [], // 重要性參數
			expiredYn: 'N',

			columns: [
				{ name: 'importance', label: this.$t('gen.importance'), bodyClass: 'text-center' },
				{ label: this.$t('gen.isExpired'), field: item => item.expiredYn === 'Y' ? this.$t('gen.yes') : this.$t('gen.no') },
				{ name: 'announcementDate', label: this.$t('core.announcementDate') },
				{ label: this.$t('gen.messageType'), field: 'msgName' },
				{ label: this.$t('gen.mainCategory'), field: 'mainCatName' },
				{ label: this.$t('gen.subCategory'), field: 'subCatName' },
				{ name: 'announcementTitle', label: this.$t('gen.announcementTitle') },
				{ label: this.$t('gen.creator'), field: 'createUser' },
				{ label: this.$t('gen.createDate'), field: 'createDt' }
			],
			pageData: {
				selMsgList: [] // 查詢結果
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'MSG_ID',
				direction: 'ASC'
			},
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand',

			selMsg: {} // 公告明細資料
		};
	},
	computed: {
		minValidEndDt() {
			return this.validBgnDt ? this.validBgnDt : null;
		},
		messageTypeOptions() {
			return [
				{ msgName: this.$t('gen.all'), msgCode: null },
				...this.selMessageMap
			];
		},
		mainCategoryOptions() {
			return [
				{ catName: this.$t('gen.all'), catCode: null },
				...this.selMsgMainCat
			];
		},
		subCategoryOptions() {
			return [
				{ catName: this.$t('gen.all'), catCode: null },
				...this.selMsgSubCat
			];
		},
		expireOptions() {
			return [
				{ codeName: this.$t('gen.notDistinguished'), codeValue: null },
				...this.selOptionYn
			];
		}
	},
	watch: {
		mainCatCode: async function (newVal) {
			const self = this;
			if (newVal) self.selMsgSubCat = await self.getMessageCat('S', newVal);
			else {
				self.selMsg.subCatCode = null;
				self.selMsgSubCat = [];
			}
		},
		validBgnDt: function (newVal) {
			this.showErrors = false;
			if (newVal && this.validEndDt && newVal > this.validEndDt) {
				this.validEndDt = null;
			}
		}
	},
	mounted: async function () {
		const self = this;
		$.when(
			self.getMessageMap(),
			(self.selMsgMainCat = await self.getMessageCat('M', null)),
			(self.selImportentYn = await self.getAdmCodeDetail('GM_IMPORTANT_YN')),
			(self.selOptionYn = await self.getAdmCodeDetail('OPTION_YN'))
		).then(function () {
			if (self.msgId) {
				self.getMessage(self.msgId);
			}
		});
	},
	methods: {
		generateId(value) {
			return 'expiredYn_' + (value || 'default');
		},
		getAdmCodeDetail: async function (codeType) {
			const self = this;
			const ret = await self.$api.getAdmCodeDetail({
				codeType: codeType
			});
			return ret.data;
		},
		getMessageMap: async function () {
			const self = this;
			const ret = await self.$api.getMessageMapApi({});
			if (ret.data) {
				self.selMessageMap = ret.data;
			}
		},
		getMessageCat: async function (catType, mainCatCode) {
			const self = this;
			const data = { catType: catType, mainCatCode: mainCatCode };
			const reqData = _.omitBy(data, value => _.isNil(value) || value === '');
			const ret = await self.$api.getGenMessageCat(reqData);
			return ret.data;
		},
		subTypeName: function (codeValue) {
			const self = this;
			if (!_.isBlank(self.selOptionYn) && !_.isBlank(codeValue)) {
				return _.find(self.selOptionYn, { codeValue: codeValue }).codeName;
			}
			else {
				return codeValue;
			}
		},
		submitForm() {
			this.showErrors = true;
			this.gotoPage(0);
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: function () {
			const self = this;
			self.$refs.queryForm.validate().then(async function (pass) {
				if (pass.valid) {
					const ret = await self.$api.getBbsMgtPageData(
						self.pageable,
						{
							msgCode: self.msgCode,
							mainCatCode: self.mainCatCode,
							subCatCode: self.subCatCode,
							validBgnDt: _.formatDate(self.validBgnDt),
							validEndDt: _.formatDate(self.validEndDt),
							msgTitle: self.msgTitle,
							expiredYn: self.expiredYn
						}
					);
					self.pageData = ret.data;
					self.pageData.selMsgList = self.pageData.content;
				}
			});
		},
		getView: async function (msgId) {
			const self = this;
			const ret = await self.$api.getGenMessageApi({
				msgId: msgId
			});
			if (ret.data) {
				self.selMsg = _.clone(ret.data);
				self.isOpenModal = true;
			}
		},
		changeModalSize: function () {
			const self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			}
			else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		viewFile: function (targetFileId) {
			const self = this;
			self.$api.downloadFileApi({ fileId: targetFileId, fileType: 'GenFilesLog' });
		}
	}
};
</script>
