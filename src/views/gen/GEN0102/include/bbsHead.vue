<template>
	<div id="searchResult2">
		<div class="card card-table mb-3">
			<div class="card-header">
				<h4>{{ $t('gen.msgList') }}</h4>
				<vue-pagination :pageable="pageData" :goto-page="gotoPage" />
			</div>
			<div class="table-responsive">
				<table class="table table-RWD table-bordered text-center">
					<thead>
						<tr>
							<th>{{ $t('gen.importance') }}</th>
							<th>{{ $t('gen.isExpired') }}</th>
							<th>{{ $t('core.announcementDate') }}</th>
							<th>{{ $t('gen.category') }}</th>
							<th>{{ $t('gen.mainCategory') }}</th>
							<th>{{ $t('gen.subCategory') }}</th>
							<th>{{ $t('gen.announcementTitle') }}</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="item in pageData.selMsgList">
							<td
								:class="{ 'text-center tx-red bi-exclamation': item.importantYn == 'Y' }"
								:data-th="$t('gen.importance')"
								style="font-size: 24px"
							/>
							<td :data-th="$t('gen.isExpired')">
								{{ item.expiredYn === 'Y' ? $t('gen.yes') : $t('gen.no') }}
							</td>
							<td :data-th="$t('core.announcementDate')">
								{{ item.validBgnDt }}<br>~{{ item.validEndDt }}
							</td>
							<td :data-th="$t('gen.category')">
								{{ item.msgName }}
							</td>
							<td :data-th="$t('gen.mainCategory')">
								{{ item.mainCatName }}
							</td>
							<td :data-th="$t('gen.subCategory')">
								{{ item.subCatName }}
							</td>
							<td :data-th="$t('gen.announcementTitle')" style="max-width: 50%; white-space: pre-wrap;">
								<ColoredLink @click="getView(item.msgId)">
									{{ item.msgTitle }}
								</ColoredLink>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>

	<!-- Modal -->
	<vue-modal :is-open="isOpenModal" @close="closeModal">
		<template #content="props">
			<div :class="modalClass">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title">
							{{ $t('gen.bbsMessage') }}
						</h4>
						<button type="button" :class="buttonClass" @click="changeModalSize()">
							<i
								class="bi bi-arrows-fullscreen"
							/>
						</button>
						<button type="button" class="btn-close" @click.prevent="props.close()" />
					</div>
					<div class="modal-body">
						<table class="table table-bordered">
							<caption>
								{{ $t('gen.bbsCategory') }}
							</caption>
							<tbody>
								<tr>
									<th>{{ $t('gen.messageType') }}</th>
									<td>{{ selMsg.msgName }}</td>
								</tr>
							</tbody>
						</table>

						<table class="table table-bordered">
							<caption>
								{{ $t('gen.bbsContent') }}
							</caption>
							<tbody>
								<tr>
									<th class="wd-15p">
										{{ $t('gen.importance') }}
									</th>
									<td>{{ subTypeName(selMsg.importantYn) }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.mainCategory') }}</th>
									<td>{{ selMsg.mainCatName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.subCategory') }}</th>
									<td>{{ selMsg.subCatName }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.validDate') }}</th>
									<td>{{ $t('gen.from') }} {{ selMsg.validBgnDt }} ~ {{ $t('gen.to') }} {{ selMsg.validEndDt }}</td>
								</tr>
								<tr>
									<th><span>{{ $t('gen.bbsTitle') }}</span></th>
									<td>{{ selMsg.msgTitle }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.bbsContent') }}</th>
									<td>{{ selMsg.msgContent }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.showOnHomepage') }}</th>
									<td>{{ subTypeName(selMsg.showYn) }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.uploadFile') }}</th>
									<td>
										<ul v-for="file in selMsg.fileList" class="list-group list-inline-tags">
											<li class="list-group-item">
												<ColoredLink @click="viewFile(file.msgFileId)">
													<span>{{ file.showName }}</span>
												</ColoredLink>
											</li>
										</ul>
									</td>
								</tr>
								<tr>
									<th>{{ $t('gen.link') }}</th>
									<td>
										<a :href="selMsg.favoriteLink">{{ selMsg.favoriteLink }}</a>
									</td>
								</tr>
							</tbody>
						</table>

						<table class="table table-bordered">
							<caption>
								{{ $t('gen.maintenanceInfo') }}
							</caption>
							<tbody>
								<tr>
									<th>{{ $t('gen.creator') }}</th>
									<td>{{ selMsg.createUser }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.creatorExt') }}</th>
									<td>{{ selMsg.createUserExt }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.createDate') }}</th>
									<td>{{ selMsg.createDt }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.lastMaintainer') }}</th>
									<td>{{ selMsg.modifyUser }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.maintainerExt') }}</th>
									<td>{{ selMsg.modifyUserExt }}</td>
								</tr>
								<tr>
									<th>{{ $t('gen.lastMaintenanceDate') }}</th>
									<td>{{ selMsg.modifyDt }}</td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="modal-footer">
						<Button color="white" :label="$t('gen.closeWindow')" @click="props.close()" />
					</div>
				</div>
			</div>
		</template>
	</vue-modal>
	<!-- Modal End -->
</template>
<script>
import vueModal from '@/views/components/model.vue';
import VuePagination from '@/views/components/pagination.vue';
import _ from 'lodash';

export default {
	components: {
		vueModal,
		VuePagination
	},
	props: {
		title: {
			type: String,
			required: true
		}
	},
	data: function () {
		return {
			selMessageMap: [], // 公告類別資料
			selOptionYn: [
				{ codeName: this.$t('gen.yes'), codeValue: 'Y' },
				{ codeName: this.$t('gen.no'), codeValue: 'N' }
			],

			msgCode: null,
			validBgnDt: null,
			validEndDt: null,
			msgTitle: null,

			pageData: {
				content: []
			},
			pageable: {
				page: 0,
				size: 20,
				sort: 'MSG_ID',
				direction: 'ASC'
			},
			isOpenModal: null,
			modalClass: 'modal-dialog modal-lg modal-dialog-centered',
			buttonClass: 'btn-expand',
			// getView
			selMsg: {}
		};
	},
	watch: {
		title: {
			handler() {
				this.initTab();
			},
			immediate: true
		}
	},
	methods: {
		async initTab() {
			const self = this;
			// initial
			await self.getMessageMap();
			self.selMessageMap.forEach(function (message) {
				if (self.title === message.msgName) {
					self.msgCode = message.msgCode;
				}
			});

			self.gotoPage(0);
		},
		async getMessageMap() {
			const self = this;
			try {
				const response = await self.$api.getMessageMapApi({});
				if (response.data) {
					self.selMessageMap = response.data;
					self.setMessageCode();
				}
			}
			catch (error) {
				console.error('Error fetching message map:', error);
			}
		},
		setMessageCode() {
			const self = this;
			self.selMessageMap.forEach(function (message) {
				if (self.title === message.msgName) {
					self.msgCode = message.msgCode;
				}
			});
		},
		gotoPage: function (page) {
			this.pageable.page = page;
			this.getPageData(page);
		},
		getPageData: async function (_page) {
			const self = this;
			const page = _.isNumber(_page) ? _page : self.pageable.page;
			let url = '';
			url += '?page=' + page + '&size=' + self.pageable.size;
			url += '&sort=' + self.pageable.sort + ',' + self.pageable.direction;
			const ret = await self.$api.getBbsMgtHeadPageData(
				{
					msgCode: self.msgCode
				},
				url
			);
			self.pageData = ret.data;
			self.pageData.selMsgList = self.pageData.content;
		},
		getView: async function (msgId) {
			const self = this;
			const ret = await self.$api.getGenMessageApi({
				msgId: msgId
			});
			if (ret.data) {
				self.selMsg = _.clone(ret.data);
				self.isOpenModal = true;
			}
		},
		changeModalSize: function () {
			const self = this;
			if (self.modalClass === 'modal-dialog modal-lg modal-dialog-centered') {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered fullscreen';
				self.buttonClass = 'btn-expand mini';
			}
			else {
				self.modalClass = 'modal-dialog modal-lg modal-dialog-centered';
				self.buttonClass = 'btn-expand';
			}
		},
		closeModal: function () {
			this.isOpenModal = false;
		},
		viewFile: function (targetFileId) {
			return this.$api.downloadFileApi({ fileId: targetFileId, fileType: 'GenFilesLog' });
		},
		subTypeName: function (codeValue) {
			const self = this;
			if (!_.isBlank(self.selOptionYn) && !_.isBlank(codeValue)) {
				return _.find(self.selOptionYn, { codeValue: codeValue }).codeName;
			}
			else {
				return codeValue;
			}
		}
	}
};
</script>
