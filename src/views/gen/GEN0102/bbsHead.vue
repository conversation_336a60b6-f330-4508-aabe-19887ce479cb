<template>
	<div class="tab-nav-main">
		<Tabs v-model="tab" :tabs variant="pill" />
		<component :is="componentId" :title="tabName" />
	</div>
</template>

<script setup>
import { useTabs } from '@/composables/useTabs';
import { useRoute, useRouter } from 'vue-router';
import { computed, defineAsyncComponent, onMounted } from 'vue';

const { tabs, tab, componentId } = useTabs('M40-02');
const tabName = computed(() => tabs.value.find(it => it.code === tab.value)?.name);

const router = useRouter();
const route = useRoute();
const msgCodeMenuMap = {
	MSG01: 'M40-021',
	MSG02: 'M40-022',
	MSG021: 'M40-023',
	MSG026: 'M40-024'
};

onMounted(() => {
	tab.value = msgCodeMenuMap[route.query.msgCode] || 'M40-020';
	router.replace({ name: 'bbsHead' });
});

</script>

<script>

export default {
	components: {
		vueBbsHead: defineAsyncComponent(() => import('./include/bbsHead.vue')),
		vueBbsHeadAll: defineAsyncComponent(() => import('./include/bbsHeadAll.vue'))
	}
};
</script>
