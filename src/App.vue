<template>
	<RouterView />
</template>

<script>

export default {
	data() {
		return {
			registries: [],
			apiPath: null,
			contextPath: null,
			csrfToken: null,
			csrfHeaderName: null,
			notice: null,
			loader: null,
			loadCount: 0,
			knowledgeCloudHomeUrl: null
		};
	},
	mounted() {

	},
	methods: {

	}
};
</script>

<script setup>
import Swal from 'sweetalert2';
import { onErrorCaptured } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

onErrorCaptured((error) => {
	Swal.fire({
		title: t('core.error'),
		text: error.message,
		icon: 'error',
		confirmButtonText: 'OK'
	});
	console.error('Error captured:', error);
	return false; // Prevents the error from propagating further
});

</script>

<style scoped>
.modal .form-control:disabled {
	background: #fff;
}
</style>
