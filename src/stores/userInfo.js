import api from '@/api/apiService.js';
import { setToken } from '@/utils/auth';
import { defineStore } from 'pinia';
import { readonly, ref } from 'vue';
export const useUserInfoStore = defineStore('userInfo', () => {
	const userInfo = ref({});

	async function getInfo() {
		const ret = await api.getInfoApi();
		userInfo.value = ret.data;
	}
	async function switchRole(posCode) {
		const ret = await api.authenticate(posCode);
		const { accessToken, refreshToken } = ret.data;
		setToken('accessToken', accessToken);
		setToken('refreshToken', refreshToken);
		await this.getInfo();
		window.location.reload();
	}

	return { getInfo, switchRole, userInfo: readonly(userInfo) };
});
