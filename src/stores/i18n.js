import { defineStore } from 'pinia';
import { readonly } from 'vue';
import { useI18n } from 'vue-i18n';

export const useI18nStore = defineStore('i18n', () => {
	const { availableLocales, locale } = useI18n();

	function setLocale(newLocale) {
		localStorage.setItem('locale', newLocale);
		locale.value = newLocale;
		document.querySelector('html').setAttribute('lang', newLocale);
	}

	return { availableLocales, locale: readonly(locale), setLocale };
});
