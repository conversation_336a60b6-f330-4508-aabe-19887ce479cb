/**
 * @typedef {Object} Component
 * @property {number} WindowId - 每個 block 的唯一鍵
 * @property {string} WindowName - block 名稱 目前不會用到
 * @property {string[]} Components - 元件列表
 */

/**
 * @typedef {Object} TabItem
 * @property {(string|number)} key - 分頁 tab 的唯一鍵，會依據此鍵命名
 * @property {number} layoutId - 版面 ID
 * @property {Component[]} components - Array of Component objects
 */

export const useTabStatusStore = defineStore('tabStatus', () => {
	/**
	 * @type {Ref<TabItem[]>}
	*/
	const tabsList = ref([]);
	const keyIndex = ref(1);

	/**
	 * Generates a default name for new tabs
	 * @param {TabItem[]} pages - Array of existing tab items
	 * @returns {string} Generated unique tab name
	 */
	function generateDefaultName(pages) {
		let index = keyIndex.value++;
		let nameSet = new Set(pages.map(p => p.key));
		let candidate = `分頁${index}`;
		while (nameSet.has(candidate)) {
			index++;
			candidate = `分頁${index}`;
		}
		return candidate;
	}

	/**
	 * 增加新分頁
	 * @param {Object} payload - The tab data
	 * @param {number} payload.layoutId - The layout ID
	 * @param {string} [payload.tabName] - 分頁名稱 (optional)
	 * @param {Component[]} payload.components - 分頁內含元件
	 */
	function setTabList(payload) {
		const newTab = payload.layoutId === 0
			? {
					key: payload.tabName,
					layoutId: payload.layoutId,
					components: payload.components
				}
			: {
					key: generateDefaultName(tabsList.value),
					layoutId: payload.layoutId,
					components: payload.components.sort((a, b) => a.WindowId - b.WindowId)
				};

		tabsList.value = [...tabsList.value, newTab];
	}

	/**
	 * 移除分頁
	 * @param {Object} payload - The removal data
	 * @param {string} payload.name - 分頁名稱
	 */
	function removeTab(payload) {
		tabsList.value = tabsList.value.filter(tab => tab.key !== payload.name);
	}

	/**
	 * 編輯分頁名稱
	 * @param {Object} payload - The edit data
	 * @param {string} payload.oldTabName - 分頁目前名稱
	 * @param {string} payload.newTabName - 分頁新名稱
	 */
	function editTabName(payload) {
		const tab = tabsList.value.find(tab => tab.key === payload.oldTabName);
		if (tab) {
			tab.key = payload.newTabName;
		}
	}

	return { removeTab, editTabName, setTabList, tabsList };
});
