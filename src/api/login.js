import request from '@/utils/request';

// Login.vue
export function recaptchaVerifiedApi(token) {
	return request({
		url: '/captcha/verify',
		method: 'post',
		data: {
			token
		}
	});
}

export function loginApi(tenantNumber, username, password) {
	return request({
		url: '/login',
		data: {
			tenantNumber,
			username,
			password
		}
	});
}

// SelectPos.vue
export function getUserRolesApi() {
	return request({
		url: '/login/user/positions',
		method: 'get'
	});
}

// login authenticate

export function authenticate(posCode) {
	return request({
		url: '/login/user/authenticate',
		method: 'post',
		data: {
			posCode
		}
	});
}
