import { getToken } from '@/utils/auth';
import { downloadBlob } from '@/utils/downloadBlob';

const apiPath = import.meta.env.VITE_API_URL_V1;

export async function downloadFileApi({ fileId, fileType }, filename) {
	const serachParams = new URLSearchParams({
		fileType,
		fileId
	});

	const token = getToken();
	const headers = new Headers();
	if (token) headers.append('Authorization', token);

	const response = await fetch(apiPath + `/com/fileView?${serachParams}`, {
		method: 'GET',
		headers
	});

	if (!response.ok) throw Error(`下載失敗，請稍後再試。Status: ${response.status}`);
	const blob = await response.blob();
	if (blob.size === 0) throw Error('檔案為空或者不存在');
	if (filename) downloadBlob(blob, decodeURIComponent(filename));
	else downloadBlob(blob, decodeURIComponent(response.headers.get('Content-Disposition')?.split('filename=')[1] ?? `${fileId}.${fileType}`));
}

export async function previewFileApi({ fileId, fileType }) {
	const serachParams = new URLSearchParams({
		fileType,
		fileId
	});

	const token = getToken();
	const headers = new Headers();
	if (token) headers.append('Authorization', token);

	const response = await fetch(apiPath + `/com/fileView?${serachParams}`, {
		method: 'GET',
		headers
	});

	if (!response.ok) throw Error(`下載失敗，請稍後再試。Status: ${response.status}`);
	const blob = await response.blob();
	if (blob.size === 0) throw Error('檔案為空或者不存在');
	const url =	URL.createObjectURL(blob);
	const previewWindow = window.open(url, '_blank');
	previewWindow.addEventListener('beforeunload', () => {
		URL.revokeObjectURL(url);
	});
}
