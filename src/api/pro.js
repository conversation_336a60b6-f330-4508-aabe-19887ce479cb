import request from '@/utils/request';

// 商品資料查詢/維護-商品類型
export function getProPfcatsMenuApi() {
	return request({
		url: '/pro/showProPfcatsMenu',
		method: 'get'
	});
}

// 商品資料查詢 - 一般查詢/查詢結果
export function searchProductsApi(
	{ assetcatCode, pfcatCode, bankProCode, proName, riskCodes, curCodes, intFreqUnitType, principalGuarYn, isinCode, source },
	queryString
) {
	return request({
		url: '/pro/search/searchProducts' + queryString,
		method: 'get',
		params: {
			assetcatCode,
			pfcatCode,
			bankProCode,
			proName,
			riskCodes: riskCodes.join(','),
			curCodes: curCodes.join(','),
			intFreqUnitType,
			principalGuarYn,
			isinCode,
			source
		}
	});
}

// 商品資料查詢 - 快速查詢/查詢結果
export function fastSearchProductsApi({ fastCode, rangeType, rangeFixed, perfTime, rowNumber, queryString }) {
	return request({
		url: '/pro/search/fastSearchProducts' + queryString,
		method: 'get',
		params: {
			fastCode,
			rangeType,
			rangeFixed,
			perfTime,
			rowNumber
		}
	});
}

// 商品資料查詢 - 加入觀察
export function addFavoriteApi({ proCode, pfcatCode }) {
	return request({
		url: '/pro/proFavorites',
		method: 'post',
		data: {
			proCode,
			pfcatCode
		}
	});
}

// 商品資料查詢 - 取消觀察/刪除我的最愛
export function deleteFavoriteApi({ proCode }) {
	return request({
		url: '/pro/proFavorites',
		method: 'delete',
		params: {
			proCode
		}
	});
}

// 取得檔案預覽資訊
export function downloadOtherFileApi({ fileId }) {
	self.$api.downloadGenOtherFileApi({ fileId: fileId });
}

// 風險等級選單
export function getRiskMenuApi() {
	return request({
		url: '/pro/riskMenu',
		method: 'get'
	});
}

// 取得資產類別
export function getAssetcatsMenuApi() {
	return request({
		url: '/pro/proAssetcatsMenu',
		method: 'get'
	});
}
// 取得快速篩選選單
export function getFastFilterMenuApi() {
	return request({
		url: '/pro/allFastFilterMenu',
		method: 'get'
	});
}

// 取得顯示區間
export function getTimeRangeMenuApi() {
	return request({
		url: '/pro/ins/searchTimeRangeMenu',
		method: 'get'
	});
}
// 取得顯示資料筆數
export function getRowNumerMenuApi() {
	return request({
		url: '/pro/ins/searchRowNumerMenu',
		method: 'get'
	});
}

// 取得標的績效
export function getPerfMenuApi() {
	return request({
		url: '/pro/perfMenu',
		method: 'get'
	});
}

// 配息頻率選單
export function getIntFreqUnitTypeMenuApi() {
	return request({
		url: '/pro/intFreqUnitTypeMenu',
		method: 'get'
	});
}

// 幣別-常用幣別選單
export function groupProCurrenciesMenuApi() {
	return request({
		url: '/pro/groupProCurrenciesMenu',
		method: 'get'
	});
}

// 信託分析ETF-績效表現(歷史績效走勢圖)-顯示區間列表
export function getProPriceRangeMenuApi() {
	return request({
		url: '/pro/etf/priceTimeRangeMenu',
		method: 'get'
	});
}

// 商品資料維護-查詢結果
export function editProductsApi({ pfcatCode, principalGuarYn, bankProCode, proName, riskCode, intFreqUnitType, curCodes }, pageable) {
	return request({
		url: '/pro/search/editProducts',
		method: 'get',
		params: {
			pfcatCode,
			principalGuarYn,
			bankProCode,
			proName,
			riskCode,
			intFreqUnitType,
			curCodes,
			...pageable
		}
	});
}

// pro 檔案檢視
export function downloadProFileApi({ proFileId, eventId }) {
	return request({
		url: '/pro/proFile',
		method: 'get',
		responseType: 'blob', // 重要：告訴 axios 這是下載檔案
		params: {
			// 用 params 傳 GET 查詢參數
			proFileId,
			eventId
		}
	});
}

// 商品資料查詢(全商品)-商品檢視-商品基本資料
export function getProductInfoApi({ proCode, pfcatCode }) {
	return request({
		url: '/pro/productInfo',
		method: 'get',
		params: {
			proCode,
			pfcatCode
		}
	});
}

// 商品資料維護-傳送主管審核
export function patchProductApi(formData) {
	return request({
		url: '/pro/product',
		method: 'patch',
		data: formData
	});
}

// 商品資料審核-商品檢視
export function getProductLogApi({ eventId }) {
	return request({
		url: '/pro/productLog',
		method: 'get',
		params: {
			eventId
		}
	});
}

// 商品資料查詢(全商品)-商品檢視-商品附加資料(全部)
export function getProductsCommInfo({ proCode, pfcatCode }) {
	return request({
		url: '/pro/productsCommInfo',
		method: 'get',
		params: {
			proCode,
			pfcatCode
		}
	});
}

// 發行機構選單
export function getGroupIssuersMenuApi({ pfcatCode }) {
	return request({
		url: '/pro/issuersMenuByPfcatCode',
		method: 'GET',
		params: {
			pfcatCode
		}
	});
}

export function getBondPriceAnaApi({ proCodes, freqType, freqFixed }) {
	return request({
		url: '/pro/bond/bondPriceAna',
		method: 'get',
		params: {
			proCodes: proCodes.join(','),
			freqType,
			freqFixed
		}
	});
}

// 取得基金類型選項
export function getProTypeListApi({ pfcatCode }) {
	return request({
		url: '/pro/proTypeList',
		method: 'get',
		params: {
			pfcatCode
		}
	});
}

// 取得快速篩選條件選項
export function getFundFastMenuApi() {
	return request({
		url: '/pro/fund/fundFastFilterMenu',
		method: 'get'
	});
}

// 進階搜尋 基金類別選擇全資料庫基金=>計價幣別選單
export function getCurMenuApi() {
	return request({
		url: '/pro/groupProCurrenciesMenu',
		method: 'get'
	});
}

// 基金類別下拉
export function getInvestmentTypeMenuApi({ local }) {
	return request({
		url: '/pro/proInvestmentType',
		method: 'get',
		params: {
			local
		}
	});
}

// 投資地區下拉
export function getGeoFocusMenuApi({ local }) {
	return request({
		url: '/pro/proGeoFocus',
		method: 'get',
		params: {
			local
		}
	});
}

export function getGlobalClassCodeMenuApi() {
	return request({
		url: '/pro/fund/globalClassCodeMenu',
		method: 'get'
	});
}

export function getGlobalClassCodeOtherMenuApi() {
	return request({
		url: '/pro/fund/globalClassCodeOtherMenu',
		method: 'get'
	});
}

export function getLocalClassMenuApi() {
	return request({
		url: '/pro/fund/localClassCodeMenu',
		method: 'get'
	});
}

// 進階搜尋 基金公司
export function getLocalFundCompanies() {
	return request({
		url: '/pro/fund/localFundCompanies',
		method: 'get'
	});
}

export function getForeignFundCompanies() {
	return request({
		url: '/pro/fund/foreignFundCompanies',
		method: 'get'
	});
}

// 取得商品資料查詢(信託-基金)-風險/績效選單
export function getProWeightedTypeMenuApi() {
	return request({
		url: '/pro/fund/proWeightedTypeMenu',
		method: 'get'
	});
}

export function getFundProducts(
	{
		bankProCode,
		proName,
		curCodes,
		riskCodes,
		protypeCode,
		localYn,
		intFreqUnitType,
		intRateRank,
		backEndLoadYn,
		isinCode,
		lipperPointer,
		lipperRank,
		compCode,
		minDValue,
		maxDValue
	},
	queryString
) {
	return request({
		url: '/pro/fund/fundProducts' + queryString,
		method: 'get',
		params: {
			bankProCode,
			proName,
			curCodes: curCodes.join(','),
			riskCodes,
			protypeCode,
			localYn,
			intFreqUnitType,
			intRateRank,
			backEndLoadYn,
			isinCode,
			lipperPointer,
			lipperRank,
			compCode,
			minDValue,
			maxDValue
		}
	});
}

export function getFastPageDataApi({ filterCodeValue, timeRangeType, timeRangeFixed, rowNumberFixed, perfTimeCode, lipperClassCodes }, queryString) {
	return request({
		url: '/pro/fund/fundProductsFilterQuery' + queryString,
		method: 'get',
		params: {
			filterCodeValue,
			timeRangeType,
			timeRangeFixed,
			rowNumberFixed,
			perfTimeCode,
			lipperClassCodes
		}
	});
}

export function getRankPageDataApi({ lipperClassCodes, perfCurCode, rowNumberFixed, proSearchesMaps }) {
	return request({
		url: '/pro/fund/fundProductsRank',
		method: 'post',
		data: {
			lipperClassCodes,
			perfCurCode,
			rowNumberFixed,
			proSearchesMaps
		}
	});
}

export function getProSearchesApi() {
	return request({
		url: '/pro/fund/proSearches',
		method: 'get'
	});
}

// 歷史查詢條件刪除
export function deleteHistorySearchApi({ searchSeq }) {
	return request({
		url: '/pro/fund/proSearches',
		method: 'delete',
		data: {
			searchSeq
		}
	});
}

// 加權條件篩選 儲存條件
export function postProSearchesApi({ searchName, memo, proSearchesMaps }) {
	return request({
		url: '/pro/fund/proSearches',
		method: 'post',
		data: {
			searchName,
			memo,
			proSearchesMaps
		}
	});
}

// 信託-基金 進階搜尋
export function getAdvancePageDataApi(
	{ fundSaleType, assetStatPctCode, annualPerfType, perfRangeType, perfType, lipperPointer, rank, sharpeValueType, investRange, dcaRange },
	queryString
) {
	return request({
		url: '/pro/fund/fundProductsAdvance' + queryString,
		method: 'get',
		params: {
			fundSaleType,
			assetStatPctCode,
			annualPerfType,
			perfRangeType,
			perfType,
			lipperPointer,
			rank,
			sharpeValueType,
			investRange,
			dcaRange
		}
	});
}

export function getBondIssuersMenuApi() {
	return request({
		url: '/pro/groupIssuersMenu',
		method: 'get'
	});
}
// 選擇債券-發行機構選單
export function getBondIssuersApi() {
	return request({
		url: '/pro/bond/allBondIssuers',
		method: 'get'
	});
}

// 取得保證機構選單
export function getBondGuaranteesApi() {
	return request({
		url: '/pro/bond/allBondGuarantees',
		method: 'get'
	});
}

// 取得快速篩選條件選項
export function getBondFastMenuApi() {
	return request({
		url: '/pro/bond/bondFastFilterMenu',
		method: 'get'
	});
}

// 取得債券商品列表
export function getBondProductsApi({
	page,
	size,
	sort,
	bankProCode,
	proName,
	proType,
	curCodes,
	riskCodes,
	intFreqUnitType,
	buyMin,
	invAccAmt,
	price,
	issuerCodes,
	buCode,
	parRateMin,
	parRateMax,
	isinCode,
	profInvestorYn,
	issuerType,
	invQuality,
	bondRateType,
	listingType,
	payAllocation,
	expireYieldMin,
	expireYieldMax,
	periodMin,
	periodMax,
	guarCodes
}) {
	return request({
		url: '/pro/bond/bondProducts' + queryString,
		method: 'get',
		params: {
			page,
			size,
			sort,
			bankProCode,
			proName,
			proType,
			curCodes: curCodes.join(','),
			riskCodes,
			intFreqUnitType,
			buyMin,
			invAccAmt,
			price,
			buCode,
			parRateMin,
			parRateMax,
			isinCode,
			profInvestorYn,
			issuerType,
			invQuality,
			bondRateType,
			listingType,
			payAllocation,
			expireYieldMin,
			expireYieldMax,
			periodMin,
			periodMax,
			issuerCodes: issuerCodes.join(','),
			guarCodes: guarCodes.join(',')
		}
	});
}

// 取得快速篩選債券商品列表
export function getBondFastProductsApi(payload, queryString = '') {
	return request({
		url: '/pro/bond/bondProductsFilterQuery' + queryString,
		method: 'get',
		params: payload
	});
}

// 刪除我的最愛
export function deleteBondFavoriteApi({ proCode }) {
	return request({
		url: '/pro/proFavorites',
		method: 'delete',
		params: {
			proCode
		}
	});
}
export function getGlobalFundsApi({ lipperIds }) {
	return request({
		url: '/pro/fund/globalFunds',
		method: 'get',
		params: {
			lipperIds
		}
	});
}

export function getFundInfo({ proCode }) {
	return request({
		url: '/pro/fund/fundInfo/' + proCode,
		method: 'get',
		loading: false
	});
}

// 歷史查詢條件 編輯
export function getProSearchesMap({ searchSeq }) {
	return request({
		url: '/pro/fund/proSearchesMap/',
		method: 'get',
		loading: false,
		params: {
			searchSeq
		}
	});
}

export function getFundCompany({ proCode }) {
	return request({
		url: '/pro/fund/fundCompany/' + proCode,
		method: 'get',
		loading: false
	});
}

export function getLipperScoreApi({ proCode }) {
	return request({
		url: '/pro/fund/lipperScore/' + proCode,
		method: 'get',
		loading: false
	});
}

export function getFundInfoFundSizeLatest2Api({ proCode }) {
	return request({
		url: '/pro/asset/monthEndTna/latest2/' + proCode,
		method: 'get',
		loading: false
	});
}

export function getTechsApi({ proCode, techCurrencyCode }) {
	return request({
		url: '/pro/asset/tech/' + proCode,
		method: 'get',
		params: {
			techCurrencyCode
		},
		loading: false
	});
}

export function getPreMonthEndPriceApi({ prCode, beginDate, endDate }) {
	return request({
		url: '/pro/fundInfo/priceHistory/' + prCode,
		method: 'get',
		params: {
			beginDate,
			endDate
		},
		loading: false
	});
}

// 刪除使用者各功能來源的商品名單
export function deleteProPotSearchRstApi() {
	return request({
		url: '/pro/proPotSearchRst',
		method: 'delete'
	});
}

export function getSdMenu() {
	return request({
		url: '/pro/sdMenu',
		method: 'get'
	});
}

export function getEtfFastMenuApi() {
	return request({
		url: '/pro/etf/etfFastFilterMenu',
		method: 'get'
	});
}

export function getEtfProductsApi(
	{
		bankProCode,
		proName,
		curCodes,
		riskCodes,
		profInvestorYn,
		protypeCode,
		isinCode,
		sdRangeMin,
		sdRangeMax,
		isOtherSdRange,
		etfSizeMin,
		etfSizeMax,
		perfTimeCodeValue,
		perfMin,
		perfMax,
		issuerExchangeCodes,
		exchangeCodes
	},
	queryString
) {
	return request({
		url: '/pro/etf/etfProducts' + queryString,
		method: 'get',
		params: {
			bankProCode,
			proName,
			curCodes,
			riskCodes,
			profInvestorYn,
			protypeCode,
			isinCode,
			sdRangeMin,
			sdRangeMax,
			isOtherSdRange,
			etfSizeMin,
			etfSizeMax,
			perfTimeCodeValue,
			perfMin,
			perfMax,
			issuerExchangeCodes,
			exchangeCodes: exchangeCodes.join(',')
		}
	});
}

export function getEtfFastPageDataApi({ filterCodeValue, timeRangeType, timeRangeFixed, rowNumberFixed, perfTimeCode }, queryString) {
	return request({
		url: '/pro/etf/etfProductsFilterQuery' + queryString,
		method: 'get',
		params: {
			filterCodeValue,
			timeRangeType,
			timeRangeFixed,
			rowNumberFixed,
			perfTimeCode
		}
	});
}
export function getFundProfileBenchmarksMenuApi({ lipperIds }) {
	return request({
		url: '/pro/fund/fundAssetMenu',
		method: 'get',
		params: {
			lipperIds
		}
	});
}

export function getFundPerformanceClassMajorMenuApi({ lipperIds }) {
	return request({
		url: '/pro/fund/fundGlobalAssetMenu',
		method: 'get',
		params: {
			lipperIds
		}
	});
}
export function getEtfProfileNameMenuApi() {
	return request({
		url: '/pro/etf/etfProfileNameMenu',
		method: 'get'
	});
}

export function getEtfProfileBenchmarksMenuApi() {
	return request({
		url: '/pro/etf/etfProfileBenchmarksMenu',
		method: 'get'
	});
}

export function getEtfPerformanceClassMajorMenuApi() {
	return request({
		url: '/pro/etf/etfPerformanceClassMajorMenu',
		method: 'get'
	});
}

export function getProductByPfcatCodeApi({ pfcatCode, issuerCode }) {
	return request({
		url: '/pro/productByPfcatCode',
		method: 'get',
		params: {
			pfcatCode,
			issuerCode
		}
	});
}

export function comparePropItemApi({ proCodes, url }) {
	return request({
		url: url,
		method: 'get',
		params: {
			proCodes
		}
	});
}

// 績效比較圖-已加入商品
export function observedFundsApi({ lipperIds }) {
	return request({
		url: '/pro/fund/observedFunds',
		method: 'get',
		params: {
			lipperIds
		}
	});
}

// -商品歷史績效走勢圖
export function fundRunChartApi({ lipperIds }) {
	return request({
		url: '/pro/fund/fundRunChart',
		method: 'get',
		params: {
			lipperIds
		}
	});
}

export function getObservedEtfsApi({ lipperIds }) {
	return request({
		url: '/pro/etf/observedEtfs',
		method: 'get',
		params: {
			lipperIds
		}
	});
}

export function getPerformanceRunChartApi({ proCodes, freqType, freqFixed }) {
	return request({
		url: '/pro/performanceRunChart',
		method: 'post',
		data: {
			proCodes,
			freqType,
			freqFixed
		}
	});
}

export function getEtfDetailApi({ proCode }) {
	return request({
		url: '/pro/etf/etfProfileDetail',
		method: 'get',
		params: {
			proCode
		}
	});
}

export function getEtfStockHoldApi({ proCode }) {
	return request({
		url: '/pro/etf/etfPortfolioTopHolding',
		method: 'get',
		params: {
			proCode
		}
	});
}

export function getEtfPriceApi({ prodCode }) {
	return request({
		url: '/pro/etf/etfPriceAnalyze',
		method: 'get',
		params: {
			prodCode
		}
	});
}

export function getPricesChartDataApi({ prodCode, freqType, freqFixeds }) {
	return request({
		url: '/pro/etf/etfAssetPriceHis',
		method: 'get',
		params: {
			prodCode,
			freqType,
			freqFixeds
		}
	});
}

export function getEtfPerformancesApi({ proCodes, freqType, freqFixed }) {
	return request({
		url: '/pro/etf/etfPrformanceHis',
		method: 'get',
		params: {
			proCodes,
			freqType,
			freqFixed
		}
	});
}

export function getEtfPerformanceStatsApi({ proCode }) {
	return request({
		url: '/pro/etf/etfPerformanceStats',
		method: 'get',
		params: {
			proCode
		}
	});
}

export function getEtfIntRateApi({ proCode }) {
	return request({
		url: '/pro/etf/etfIntRate',
		method: 'get',
		params: {
			proCode
		}
	});
}

export function getGroupProExchangesMenuApi() {
	return request({
		url: '/pro/groupExchangeMenu',
		method: 'get'
	});
}

export function getGroupFundCmpsMenuApi({ localYn, fuseYn }) {
	return request({
		url: '/pro/fund/fundCompanyList',
		method: 'get',
		params: {
			localYn,
			fuseYn
		}
	});
}

export function getFundListApi({ fundCode }) {
	return request({
		url: '/pro/fundInfo/sameCompanyFund',
		method: 'get',
		data: {
			fundCode
		}
	});
}

export function getGlobalFundListApi({ fundCode, globalGlassCode }) {
	return request({
		url: '/pro/fundInfo/sameGlobalClassFund',
		method: 'get',
		data: {
			funCode,
			globalGlassCode
		}
	});
}

export function get2x2PerfsApi({ searchData }) {
	return request({
		url: '/pro/fundInfo/2x2Perf',
		method: 'get',
		data: searchData
	});
}

export function getFundCodeApi({ fundCode }) {
	return request({
		url: '/pro/assetCode/' + fundCode,
		method: 'get'
	});
}

export function getDividendsApi({ fundCode }) {
	return request({
		url: '/pro/fundInfo/dividend/' + fundCode,
		method: 'get'
	});
}

export function getBenchmarksApi() {
	return request({
		url: '/pro/fundInfo/fundBenchmark',
		method: 'get',
		loading: false
	});
}

export function getFundCompaniesApi() {
	return request({
		url: '/pro/fundInfo/compareFundCompany',
		method: 'get',
		loading: false
	});
}

export function getFundsApi({ companyCode }) {
	return request({
		url: '/pro/fundInfo/compareFund',
		method: 'get',
		loading: false,
		data: {
			companyCode
		}
	});
}

export function getFundSizeApi({ fundCode, beginDate, endDate }) {
	return request({
		url: '/pro/asset/monthEndTna/' + fundCode,
		method: 'get',
		loading: false,
		data: {
			beginDate,
			endDate
		}
	});
}

export function getPctsApi({ beginDate, endDate, techCurrencyCode, assetCodes }) {
	return request({
		url: '/pro/asset/pct',
		method: 'get',
		loading: false,
		data: {
			beginDate,
			endDate,
			techCurrencyCode,
			assetCodes
		}
	});
}

export function getPerfRankApi({ techCurrencyCode, fundCode, statCode, fundPerfRank, fundPerfRankCode }) {
	return request({
		url: '/pro/fundInfo/perfRank',
		method: 'get',
		loading: false,
		data: {
			techCurrencyCode,
			fundCode,
			statCode,
			fundPerfRank,
			fundPerfRankCode
		}
	});
}

export function getMonthPctsApi({ fundCode, beginDate, endDate, techCurrencyCode }) {
	return request({
		url: '/pro/asset/pct/month/' + fundCode,
		method: 'get',
		loading: false,
		data: {
			techCurrencyCode,
			beginDate,
			endDate
		}
	});
}

export function getAllocsApi(fundCode) {
	return request({
		url: '/pro/fundInfo/alloc/' + fundCode,
		method: 'get',
		loading: false
	});
}

export function getObservedProductsApi({ proCodes }) {
	return request({
		url: '/pro/observedProducts',
		method: 'get',
		params: {
			proCodes: proCodes.join(',')
		}
	});
}

export function getPfdFastMenuApi() {
	return request({
		url: '/pro/pfd/pfdFastFilterMenu',
		method: 'get'
	});
}

export function getPfdProductsApi({ bankProCode, proName, curCodes, riskCodes, isinCode, profInvestorYn, geoFocusCodes }, queryString) {
	return request({
		url: '/pro/pfd/pfdProducts' + queryString,
		method: 'get',
		params: {
			bankProCode,
			proName,
			curCodes,
			riskCodes,
			isinCode,
			profInvestorYn,
			geoFocusCodes
		}
	});
}

export function getPfdFastPageDataApi({ filterCodeValue }, queryString) {
	return request({
		url: '/pro/pfd/pfdProductsFilterQuery' + queryString,
		method: 'get',
		params: {
			filterCodeValue
		}
	});
}

export function groupTargetMenuApi({ stockCode }) {
	return request({
		url: '/pro/groupTargetMenu',
		method: 'get',
		data: {
			stockCode
		}
	});
}

export function getTargetViewDataListApi({ proCode }) {
	return request({
		url: '/pro/targetViewDataList',
		method: 'get',
		data: {
			proCode
		}
	});
}

// 取得快速篩選條件選項
export function getSpFastFilterMenuApi() {
	return request({
		url: '/pro/sp/spFastFilterMenu',
		method: 'get'
	});
}

export function getSpProductsApi(
	{ bankProCode, proName, protypeCode, curCodes, riskCode, principalGuarYn, targetCodes, intFreqUnitType, remainDayMin, remainDayMax, issuerCodes },
	queryString
) {
	return request({
		url: '/pro/sp/spProducts' + queryString,
		method: 'get',
		params: {
			bankProCode,
			proName,
			protypeCode,
			curCodes,
			riskCode,
			principalGuarYn,
			targetCodes,
			intFreqUnitType,
			remainDayMin,
			remainDayMax,
			issuerCodes
		}
	});
}
export function getSpProductsFilterQueryApi({ filterCodeValue, timeRangeType, timeRangeFixed, rowNumberFixed }, queryString) {
	return request({
		url: '/pro/sp/spProductsFilterQuery' + queryString,
		method: 'get',
		params: {
			filterCodeValue,
			timeRangeType,
			timeRangeFixed,
			rowNumberFixed
		}
	});
}
// 取得價格分析資料
export function getPriceAnaApi({ proCodes }) {
	return request({
		url: '/pro/priceAna',
		method: 'get',
		params: {
			proCodes: proCodes.join(',')
		}
	});
}

// 取得快速篩選條件選項
export function getInsFastFilterMenuApi() {
	return request({
		url: '/pro/ins/insFastFilterMenu',
		method: 'get'
	});
}

// 取得保險商品列表
export function getInsProductsApi(payload, queryString) {
	return request({
		url: '/pro/ins/insProducts' + queryString,
		method: 'get',
		params: payload
	});
}

// 取得保險商品快速篩選查詢
export function getInsProductsFilterQueryApi(payload, queryString) {
	return request({
		url: '/pro/ins/insProductsFilterQuery' + queryString,
		method: 'get',
		params: payload
	});
}

export function getOtherInsCompanies() {
	return request({
		url: '/pro/ins/otherInsCompanies',
		method: 'get'
	});
}

export function getGroupInsCompanies() {
	return request({
		url: '/pro/ins/groupInsCompanies',
		method: 'get'
	});
}

// DCI 快速篩選選單
export function getDciFastFilterMenuApi() {
	return request({
		url: '/pro/dci/dciFastFilterMenu',
		method: 'get'
	});
}

// DCI 商品查詢
export function getDciProductsApi(params, queryString) {
	return request({
		url: '/pro/dci/dciProducts' + queryString,
		method: 'get',
		params
	});
}

// DCI 商品快速查詢
export function getDciProductsFilterQueryApi(params, queryString) {
	return request({
		url: '/pro/dci/dciProductsFilterQuery' + queryString,
		method: 'get',
		params
	});
}

export function getSecFastMenuApi() {
	return request({
		url: '/pro/sec/secFastFilterMenu',
		method: 'get'
	});
}
export function getSecProductsApi(payload, queryString) {
	return request({
		url: '/pro/sec/secProducts' + queryString,
		method: 'get',
		params: payload
	});
}

export function getSecProductsFilterQueryApi(payload, queryString) {
	return request({
		url: '/pro/sec/secProductsFilterQuery' + queryString,
		method: 'get',
		params: payload
	});
}

export function getNewShelfProductList({ eventId }) {
	return request({
		url: '/pro/newShelfProductList',
		method: 'get',
		params: {
			eventId
		}
	});
}

export function getProSelected({ selproId, eventId }) {
	return request({
		url: '/pro/proSelected',
		method: 'get',
		params: {
			selproId,
			eventId
		}
	});
}

export function getNewProPfcatsMenuApi() {
	return request({
		url: '/pro/newProPfcatsMenu',
		method: 'get'
	});
}

export function deleteShelfProduct({ proCode, eventId }) {
	return request({
		url: '/pro/newShelfProduct',
		method: 'delete',
		params: {
			proCode,
			eventId
		}
	});
}

export function getSelectProPfcatsMenuApi() {
	return request({
		url: '/pro/selectProPfcatsMenu',
		method: 'get'
	});
}
export function getSelProPfcatsMenuApi() {
	return request({
		url: '/pro/selProcatCodeMenu',
		method: 'get'
	});
}

export function getPrdSearchSelected({ pfcatCode, selprocatCode, startDate, endDate }) {
	return request({
		url: '/pro/proSelecteds',
		method: 'get',
		data: {
			pfcatCode,
			selprocatCode,
			startDate,
			endDate
		}
	});
}
export function getPrdSearchSelectedDetail({ pfcatCode, selprocatCode, startDate, endDate }) {
	return request({
		url: '/pro/proSelectedDetail',
		method: 'get',
		data: {
			pfcatCode,
			selprocatCode,
			startDate,
			endDate
		}
	});
}

export function getProFastSelected({ bankProCode, pfcatCode, proName, protypeCode, assetcatCode, issuerCode }, queryString = '') {
	return request({
		url: '/pro/proFastSelected' + queryString,
		method: 'get',
		data: {
			bankProCode,
			pfcatCode,
			proName,
			protypeCode,
			assetcatCode,
			issuerCode
		}
	});
}

export function getProSelectedCombs({ pfcatCode, selprocatCode, startDate, endDate }, queryString = '') {
	console.log('pfcatCode:', pfcatCode);
	return request({
		url: '/pro/proSelectedCombs' + queryString,
		method: 'get',
		data: {
			pfcatCode,
			selprocatCode,
			startDate,
			endDate
		}
	});
}

export function getProSelectedLog({ pfcatCode, selprocatCode, startDate, endDate }, queryString) {
	return request({
		url: '/pro/proSelectedLog' + queryString,
		method: 'get',
		data: {
			pfcatCode,
			selprocatCode,
			startDate,
			endDate
		}
	});
}

export function getIssuersMenuApi({ tranPrdtypeCodes, pfcatCode }) {
	return request({
		url: '/pro/issuersMenu',
		method: 'get',
		data: {
			tranPrdtypeCodes,
			pfcatCode
		}
	});
}
export function deleteProSelectedLog({ eventId }) {
	return request({
		url: '/pro/proSelectedLog',
		method: 'delete',
		data: {
			eventId
		}
	});
}

export function postProSelected({ pfcatCode, selprocatCode, selproName, startDate, endDate, selproId, proSelectedMapLog, actionCode }) {
	return request({
		url: '/pro/proSelected',
		method: 'post',
		data: {
			pfcatCode,
			selprocatCode,
			selproName,
			startDate,
			endDate,
			selproId,
			proSelectedMapLog,
			actionCode
		},
		contentType: 'application/json'
	});
}

export function getProCatApi({ tranYn, assetacYn, pfcatCodes } = {}) {
	return request({
		url: '/pro/proCat',
		method: 'get',
		params: {
			tranYn,
			assetacYn,
			pfcatCodes
		}
	});
}

export function getDocProPageData({ proName, proCode, proType }, queryString) {
	return request({
		url: '/pro/proQuickSearch/page' + queryString,
		method: 'get',
		params: {
			proName,
			proCode,
			proType
		}
	});
}

export function getDocProNoticePageData(queryString) {
	return request({
		url: '/pro/proIssuerSearch/page' + queryString,
		method: 'get'
	});
}

export function getProSectorsApi() {
	return request({
		url: '/pro/proSectors/list',
		method: 'get'
	});
}

export function getProGeoFocusApi() {
	return request({
		url: '/pro/proGeoFocus/list',
		method: 'get'
	});
}

export function getProCurrenciesListApi() {
	return request({
		url: '/pro/proCurrenciesList',
		method: 'get'
	});
}

export function getProTypesApi({ pfcatCode }) {
	return request({
		url: '/pro/proTypes',
		method: 'GET',
		params: {
			pfcatCode
		}
	});
}

export function getProSpFilesApi() {
	return request({
		url: '/pro/proSpFiles',
		method: 'get'
	});
}

export function downloadProSpFilesApi({ proSpFileId }) {
	return request({
		url: '/pro/proSpFiles',
		method: 'get',
		params: {
			proSpFileId
		},
		responseType: 'blob',
		dataType: null
	});
}

export function getDsfFundMenuApi() {
	return request({
		url: '/pro/fund/dsfFundMenu',
		method: 'get'
	});
}
export function getDsfBenchmarkMenuApi() {
	return request({
		url: '/pro/fund/dsfBenchmarkMenu',
		method: 'get'
	});
}
export function getPriceTimeRangeMenuApi() {
	return request({
		url: '/pro/etf/priceTimeRangeMenu',
		method: 'get'
	});
}
export function postFundPrformanceHisApi({ proCodes, freqType, freqFixed }) {
	return request({
		url: '/pro/fund/fundPrformanceHis',
		method: 'post',
		params: {
			proCodes,
			freqType,
			freqFixed
		}
	});
}

export function getObservedBondsApi({ proCodes }) {
	return request({
		url: '/pro/bond/observedBonds',
		method: 'get',
		params: {
			proCodes
		}
	});
}

export function getCusRealizePlListApi({ cusCode, stdDt, endDt, pfcatCodeList, proCode }) {
	return request({
		url: '/cus/cusRealizePl/list',
		method: 'get',
		params: {
			cusCode,
			stdDt,
			endDt,
			pfcatCodeList: pfcatCodeList.join(','),
			proCode
		}
	});
}
