import request from '@/utils/request';

export function getAdmCodeDetail({ codeType, codeValue, codeName }) {
	return request({
		url: '/adm/admCodeDetail',
		method: 'get',
		params: {
			codeType,
			codeValue,
			codeName
		}
	});
}

// ADM0110/programSet.vue
export function getFunctionMenuTreeApi(menuCode) {
	return request({
		url: '/adm/functionMenuModifyInfo',
		method: 'get',
		params: menuCode ? { menuCode } : {}
	});
}
// getPrograSet() 呼叫了與 ADM0110/programSet.vue 的 getFunctionMenuTreeApi 一樣的Api(有帶參數menuCode)

export function patchSaveMenuActive(activeArray) {
	return request({
		url: '/adm/functionMenuActive',
		method: 'patch',
		data: activeArray,
		headers: { 'Content-Type': 'application/json' }
	});
}

// ADM0101/admRole.vue
export function getRoleMenuApi() {
	return request({
		url: '/adm/roleMenu',
		method: 'get'
	});
}

export function getAdmRolesApi(roleCode) {
	return request({
		url: '/adm/systemRoles',
		method: 'get',
		params: {
			roleCode
		}
	});
}

// ADM0101/include/admRoleAuthority.vue
// getAdmRoles() 呼叫了與 ADM0101/admRole.vue 的 getAdmRolesApi 一樣的Api

export function getMenuEnableTreeApi(roleCode) {
	return request({
		url: '/adm/menuEnableTree',
		method: 'get',
		params: {
			roleCode
		}
	});
}

export function postUpdateRolAuthorityApi(roleMetadata, checkedMenuCodes) {
	return request({
		url: '/adm/roleMenuMap',
		method: 'post',
		data: {
			roleMetadata,
			checkedMenuCodes
		},
		headers: { 'Content-Type': 'application/json' }
	});
}

// ADM0101/include/admRoleReviewDetail.vue
export function getDetailApi(eventId) {
	return request({
		url: '/adm/roleMenuLog',
		method: 'get',
		params: {
			eventId
		}
	});
}

// ADM0103/admMenuPreview.vue
// getRoleMenuDatas() 呼叫了與 ADM0101/admRole.vue 的 getRoleMenuApi 一樣的Api

export function getRoleMenuTreeApi(roleCode) {
	return request({
		url: '/adm/roleMenuMapPreview',
		method: 'get',
		params: {
			roleCode
		}
	});
}

// components/biTabs.vue
export function getMenuTabApi(parentMenuCode) {
	return request({
		url: '/adm/menuTab',
		method: 'get',
		params: {
			parentMenuCode
		}
	});
}

export function postLoggingApi({ menuCode, cusCode }) {
	return request({
		url: '/adm/admUserAccessLog',
		method: 'post',
		data: {
			menuCode,
			cusCode
		},
		headers: { 'Content-Type': 'application/json' }
	});
}

// adm/ADM0104/include/userAccountTab.vue
export function getBranMenuApi() {
	return request({
		url: '/adm/branchesMenu',
		method: 'get'
	});
}
// getRoleMenu() 呼叫了與 ADM0101/admRole.vue 的 getRoleMenuApi 一樣的Api
export function getSysUserApi(branCode, roleCode, userCode, userName, pageable) {
	return request({
		url: '/adm/sysUser',
		method: 'get',
		params: {
			branCode,
			roleCode,
			userCode,
			userName,
			...pageable
		}
	});
}

export function getExportExcelApi(branCode, roleCode, userCode, userName) {
	return request({
		url: '/adm/sysUser/excel',
		method: 'get',
		params: {
			branCode,
			roleCode,
			userCode,
			userName
		},
		responseType: 'blob'
	});
}

export function getUserPosEventApi(eventId) {
	return request({
		url: '/adm/userPosEvent',
		method: 'get',
		params: {
			eventId
		}
	});
}

// adm/ADM0104/include/userAccountNewTab.vue
export function getEditUserInfoApi(userCode) {
	return request({
		url: '/adm/userInfo',
		method: 'get',
		params: {
			userCode
		}
	});
}

export function getUserBranPosInfoApi(userCode) {
	return request({
		url: '/adm/userBranPosInfo',
		method: 'get',
		params: {
			userCode
		}
	});
}
// getBranMenu() 呼叫了與 ADM0104/include/userAccountTab.vue 的 getBranMenuApi 一樣的Api

export function getPosBranMenuApi(branCode, buCode) {
	return request({
		url: '/adm/posBranMenu',
		method: 'get',
		params: {
			branCode,
			buCode
		}
	});
}

export function postUserAccountApi(userCode, buCode, branCode, posCodes, deletePosCodes, startFlag) {
	return request({
		url: '/adm/userAccount',
		method: 'post',
		data: {
			userCode,
			buCode,
			branCode,
			posCodes,
			deletePosCodes,
			startFlag
		},
		headers: { 'Content-Type': 'application/json' }
	});
}

export function getGroupBranNameApi() {
	return request({
		url: '/adm/groupBranName',
		method: 'get'
	});
}

export function getBranPageData({ groupCode }, queryString) {
	return request({
		url: '/adm/groupBranMap' + queryString,
		method: 'get',
		params: {
			groupCode
		}
	});
}
export function postBranPageData() {
	return request({
		url: '/adm/groupBranMap',
		method: 'post'
	});
}

export function getBranExportPageData({ groupCode }) {
	return request({
		url: '/adm/groupBranMap/excel',
		method: 'get',
		params: {
			groupCode
		},
		responseType: 'blob'
	});
}

export function postFcBranMapChkApi(formData) {
	return request({
		url: '/adm/fcBranMapChk',
		method: 'post',
		data: formData,
		enctype: 'multipart/form-data;charset=UTF-8',
		processData: false,
		contentType: false,
		cache: false
	});
}
export function postGroupBranMapChkApi(formData) {
	return request({
		url: '/adm/groupBranMapChk',
		method: 'post',
		data: formData,
		enctype: 'multipart/form-data;charset=UTF-8',
		processData: false,
		contentType: false,
		cache: false
	});
}

export function getAdmRoleByRoleTypeApi({ roleType }) {
	return request({
		url: '/adm/admRoleByRoleType',
		method: 'get',
		params: {
			roleType
		}
	});
}

export function postFcBranMapApi() {
	return request({
		url: '/adm/fcBranMap',
		method: 'post',
		data: {}
	});
}

export function getFcBranMapData({ roleCode }, queryString) {
	return request({
		url: '/adm/fcBranMap' + queryString,
		method: 'get',
		params: {
			roleCode
		}
	});
}

export function getFcBranExportPageData({ roleCode }) {
	return request({
		url: '/adm/fcBranMap/excel',
		method: 'get',
		responseType: 'blob',
		params: {
			roleCode
		}
	});
}

export function getAreaMenu() {
	return request({
		url: '/adm/minorArea',
		method: 'get'
	});
}

export function getUserDeputiesApi() {
	return request({
		url: '/adm/userDeputies',
		method: 'get'
	});
}

export function getDeputiesRmMgrApi() {
	return request({
		url: '/adm/deputiesBm',
		method: 'get'
	});
}

export function getUnderUserDeputiesPageDataApi({ groupCode, branCode }, queryString) {
	return request({
		url: '/adm/underUserDeputies' + queryString,
		method: 'get',
		params: {
			groupCode,
			branCode
		}
	});
}

export function getDeputyUserCodeApi({ deputyUserCode }) {
	return request({
		url: '/adm/userDeputiesPos',
		method: 'get',
		params: {
			deputyUserCode
		}
	});
}

export function postInsertDeputyApi({ userCode, roleMetadata, branCode, deputyUserCode, deputyBranCode, stdDt, endDt }) {
	return request({
		url: '/adm/userDeputies',
		method: 'post',
		data: {
			userCode,
			roleMetadata,
			branCode,
			deputyUserCode,
			deputyBranCode,
			stdDt,
			endDt
		}
	});
}

export function getchkValidDeputiesTimeApi({ stdDt, endDt }) {
	return request({
		url: '/adm/chkValidDeputiesTime',
		method: 'get',
		params: {
			stdDt,
			endDt
		}
	});
}

export function getdoCheckIsBusinessDtApi({ date }) {
	return request({
		url: '/adm/isBusinessDt',
		method: 'get',
		params: {
			date
		}
	});
}

export function getcheckDeputyUserCodeApi({ userCode, deputyUserCode }) {
	return request({
		url: '/adm/underUserDeputiesPos',
		method: 'get',
		params: {
			userCode,
			deputyUserCode
		}
	});
}

export function deleteUserdeputyApi({ userCode, deputyUserCode }) {
	return request({
		url: '/adm/userDeputies',
		method: 'delete',
		params: {
			userCode,
			deputyUserCode
		}
	});
}

export function getAdmBranchesApi({ parentBranCode, branLvlCode, removeYn, branCode }) {
	return request({
		url: '/adm/admBranches',
		method: 'get',
		params: {
			parentBranCode,
			branLvlCode: Array.isArray(branLvlCode) ? branLvlCode.join(',') : '',
			removeYn,
			branCode
		}
	});
}

export function getAdmUsersListApi({ branCode, userCode, parentBranCode, roleCode }) {
	return request({
		url: '/adm/admUsersList',
		method: 'get',
		params: {
			branCode,
			userCode,
			parentBranCode,
			roleCode
		}
	});
}

export function getUserCodeLengthApi() {
	return request({
		url: '/adm/sys/userCodeLength',
		method: 'get'
	});
}

export function getDeputiesLogApi({ parentBranCode, branCode, userCode, stdDt, endDt }) {
	return request({
		url: '/adm/deputiesLog',
		method: 'get',
		params: {
			parentBranCode,
			branCode,
			userCode,
			stdDt,
			endDt
		}
	});
}

export function getMinorAreaApi({ buCode, majorCode }) {
	return request({
		url: '/adm/minorArea',
		method: 'get',
		params: {
			buCode,
			majorCode
		}
	});
}

export function getBranchesApi({ buCode, majorCode, minorCode }) {
	return request({
		url: '/adm/branches',
		method: 'get',
		params: {
			buCode,
			majorCode,
			minorCode
		}
	});
}

export function getDeputiesApi({ branCode }) {
	return request({
		url: '/adm/deputiesUserMenu',
		method: 'get',
		params: {
			branCode
		}
	});
}

export function getShutdownInfoApi() {
	return request({
		url: '/adm/shutdownInfo',
		method: 'get'
	});
}

export function postShutdownInfoApi({ startDt, endDt, shutdownDesc }) {
	return request({
		url: '/adm/shutdownInfo',
		method: 'post',
		data: {
			startDt,
			endDt,
			shutdownDesc
		}
	});
}

export function patchShutdownInfoApi({ shutdownId, startDt, endDt, shutdownDesc }) {
	return request({
		url: '/adm/shutdownInfo',
		method: 'patch',
		data: {
			shutdownId,
			startDt,
			endDt,
			shutdownDesc
		}
	});
}

export function deleteShutdownInfoApi({ shutdownId }) {
	return request({
		url: '/adm/shutdownInfo',
		method: 'delete',
		params: { shutdownId }
	});
}

export function getAllBranchesMenuApi() {
	return request({
		url: '/adm/allBranchesMenu',
		method: 'get'
	});
}

export function getModuleMenuApi() {
	return request({
		url: '/adm/moduleMenu',
		method: 'get'
	});
}

export function getBranEmployeeApi({ buCode, branCode }) {
	return request({
		url: '/adm/branEmployee',
		method: 'get',
		params: {
			buCode,
			branCode
		}
	});
}

export function getUserMenuApi({ branCode }) {
	return request({
		url: '/adm/userMenu',
		method: 'get',
		params: {
			branCode
		}
	});
}

export function getBranchFunctionMenuApi({ menuCode }) {
	return request({
		url: '/adm/branchFunctionMenu',
		method: 'get',
		params: {
			menuCode
		}
	});
}

export function getUserMenu({ branCode }) {
	return request({
		url: '/adm/userMenus',
		method: 'get',
		params: {
			branCode
		}
	});
}

export function getUserAccessCntLogApi({ branCode, logStartDt, logEndDt, moduleMenuCode, functionMenuCode, userCode }, queryString) {
	return request({
		url: '/adm/userAccessCntLog' + queryString,
		method: 'get',
		params: {
			branCode,
			logStartDt,
			logEndDt,
			moduleMenuCode,
			functionMenuCode,
			userCode
		}
	});
}

export function getCusSaveResultCountApi({ paramType, paramCode }) {
	return request({
		url: '/adm/admParam',
		method: 'get',
		params: {
			paramType,
			paramCode
		}
	});
}

export function getBranAccessCntLogApi({ branCode, logStartDt, logEndDt, moduleMenuCode, functionMenuCode, userCode }, queryString) {
	return request({
		url: '/adm/branAccessCntLog' + queryString,
		method: 'get',
		params: {
			branCode,
			logStartDt,
			logEndDt,
			moduleMenuCode,
			functionMenuCode,
			userCode
		}
	});
}

export function getCusFunctionMenuApi() {
	return request({
		url: '/adm/cusFunctionMenu',
		method: 'get'
	});
}

export function getUserAccessCusLogsApi({ branCode, logStartDt, logEndDt, menuCode, userCode, cusCode }, queryString) {
	return request({
		url: '/adm/userAccessCusLogs' + queryString,
		method: 'get',
		params: {
			branCode,
			logStartDt,
			logEndDt,
			menuCode,
			userCode,
			cusCode
		}
	});
}

export function getUserFunctionMenuApi({ depths, strset }) {
	return request({
		url: '/adm/userFunctionMenu',
		method: 'get',
		params: {
			depths,
			strset
		}
	});
}

export function getUserRoleMenuApi() {
	return request({
		url: '/adm/userRoleMenus',
		method: 'GET'
	});
}

export function getUserRoleMenuExportApi({ roleCode }) {
	return request({
		url: '/adm/roleMenuTree/export',
		method: 'GET',
		responseType: 'blob',
		params: {
			roleCode
		}
	});
}

export function getUserInfoApi({ userCode, path }) {
	return request({
		url: '/adm/userInfo',
		method: 'get',
		params: {
			userCode,
			path
		}
	});
}

export function getUserAccessLogsApi({ userCode, logStartDt, logEndDt, m1MenuCode, m2MenuCode, m3MenuCode, m4MenuCode }, queryString) {
	return request({
		url: '/adm/userAccessLogs' + queryString,
		method: 'get',
		params: {
			userCode,
			logStartDt,
			logEndDt,
			m1MenuCode,
			m2MenuCode,
			m3MenuCode,
			m4MenuCode
		}
	});
}

export function getCarryOutItemsActionTypeApi({ actionMode }) {
	return request({
		url: '/adm/carryOutItemsActionType',
		method: 'get',
		params: {
			actionMode
		}
	});
}

export function getItemsActionTypeMenuApi() {
	return request({
		url: '/adm/itemsActionTypeMenu',
		method: 'get'
	});
}

export function getCarryOutItemsApi({ userCode, progCode, actionTypes, logStdDt, logEndDt }, queryString) {
	return request({
		url: '/adm/carryOutItems' + queryString,
		method: 'get',
		params: {
			userCode,
			progCode,
			actionTypes,
			logStdDt,
			logEndDt
		}
	});
}

export function getTdItemCat1MenuApi() {
	return request({
		url: '/adm/tdItemCat1Menu',
		method: 'get'
	});
}

export function getTdItemsApi({ tdCat1Code }) {
	return request({
		url: '/adm/tdItems',
		method: 'get',
		params: {
			tdCat1Code
		}
	});
}

export function patchTdItemsApi(changedTdItems) {
	return request({
		url: '/adm/tdItems',
		method: 'patch',
		data: changedTdItems
	});
}

export function getRmLvlMenuApi() {
	return request({
		url: '/adm/rmLvlMenu',
		method: 'get'
	});
}

export function getCusGradesSetListsApi() {
	return request({
		url: '/adm/cusGradesSetLists',
		method: 'get'
	});
}

export function patchCusGradesSetListsApi({ graCode, rmLvlCode, graName, auaMin, auaMax }) {
	return request({
		url: '/adm/cusGradesSetLists',
		method: 'patch',
		data: {
			graCode,
			rmLvlCode,
			graName,
			auaMin,
			auaMax
		}
	});
}

export function getGradesApi() {
	return request({
		url: '/adm/grades',
		method: 'get'
	});
}

export function patchGradesApi(changedGradesItems) {
	return request({
		url: '/adm/grades',
		method: 'patch',
		data: changedGradesItems
	});
}

export function getHasEnableVerifyOptionApi() {
	return request({
		url: '/adm/hasEnableVerifyOption',
		method: 'get'
	});
}

export function getAdmShutDownInfoApi() {
	return request({
		url: '/adm/admShutDownInfo',
		method: 'get'
	});
}

export function getHomeComponentApi() {
	return request({
		url: '/adm/homeComponent',
		method: 'get'
	});
}

export function getRoleUserApi({ roleType, roleCode }) {
	return request({
		url: '/adm/roleUser',
		method: 'get',
		params: {
			roleType: Array.isArray(roleType) ? roleType.join(',') : null,
			roleCode: Array.isArray(roleCode) ? roleCode.join(',') : null
		}
	});
}

export function getBranListNameApi({ branCode }) {
	return request({
		url: '/adm/branListName',
		method: 'get',
		params: {
			branCode
		}
	});
}

export function getCaAccessPageDataApi({ caCode, rmCode, branCode }, queryString) {
	return request({
		url: '/adm/caAccess/page' + queryString,
		method: 'get',
		params: {
			caCode,
			rmCode,
			branCode
		}
	});
}

export function getCaAccessExportPageData({ caCode, rmCode, branCode }) {
	return request({
		url: '/adm/caAccess/excel',
		method: 'get',
		responseType: 'blob',
		params: {
			caCode,
			rmCode,
			branCode
		}
	});
}

export function postCaAccessApi() {
	return request({
		url: '/adm/caAccess',
		method: 'post'
	});
}

export function postCaAccessFileChkApi(formData) {
	return request({
		url: '/adm/caAccessFileChk',
		method: 'post',
		headers: {
			'Content-Type': 'multipart/form-data;charset=UTF-8'
		},
		data: formData
	});
}

export function getBatchGroupControl() {
	return request({
		url: '/adm/batchGroupControl',
		method: 'get'
	});
}

export function patchBatchGroupControl({ groupCode, reRunType }) {
	return request({
		url: '/adm/batchGroupControl',
		method: 'patch',
		data: {
			groupCode: groupCode,
			reRunType: reRunType
		}
	});
}

export function getAdmWmsBatchGrptimeCfg() {
	return request({
		url: '/adm/admWmsBatchGrptimeCfg',
		method: 'get'
	});
}

export function getInsertBatchGrpControl({ id }) {
	return request({
		url: '/adm/insertBatchGrpControl',
		method: 'GET',
		data: {
			id: id
		}
	});
}

export function getAdmWmsBatchGroups() {
	return request({
		url: '/adm/admWmsBatchGroups',
		method: 'get'
	});
}

export function getBatchJobStatus({ batchStartDate, batchEndDate, startDate, endDate, groupCode, objectName, status }, queryString) {
	return request({
		url: '/adm/batchJobStatus' + queryString,
		method: 'get',
		data: { batchStartDate, batchEndDate, startDate, endDate, groupCode, objectName, status }
	});
}

export function patchBatchJobStatus({ groupCode, objectName, id, status }) {
	return request({
		url: '/adm/batchJobStatus' + queryString,
		method: 'patch',
		data: { groupCode, objectName, id, status }
	});
}
export function getCusExtDataQueItemSelsApi({
	page, size, sort, direction
} = {}) {
	return request({
		url: '/adm/cusExtDataQueItemSels',
		method: 'get',
		params: { page, size, sort: `${sort},${direction}` }
	});
}
export function getCusExtDataQueSectionsMenuApi() {
	return request({
		url: '/adm/cusExtDataQueSectionsMenu',
		method: 'get'
	});
}
export function getCusExtDataQueItemsMenuApi({ queSectionId }) {
	return request({
		url: '/adm/cusExtDataQueItemsMenu',
		method: 'get',
		params: {
			queSectionId
		}
	});
}
export function postCusExtDataQueItemSelsApi({ queItemSelId, queSectionId, queItemId, queItemSelName, queItemSelEname, showOrder, inputYn, brLine }) {
	return request({
		url: '/adm/cusExtDataQueItemSels',
		method: 'post',
		data: {
			queItemSelId,
			queSectionId,
			queItemId,
			queItemSelName,
			queItemSelEname,
			showOrder,
			inputYn,
			brLine
		}
	});
}
export function patchCusExtDataQueItemSelsApi({ queItemSelId, queSectionId, queItemId, queItemSelName, queItemSelEname, showOrder, inputYn, brLine }) {
	return request({
		url: '/adm/cusExtDataQueItemSels',
		method: 'patch',
		data: {
			queItemSelId,
			queSectionId,
			queItemId,
			queItemSelName,
			queItemSelEname,
			showOrder,
			inputYn,
			brLine
		}
	});
}
export function deleteCusExtDataQueItemSelsApi({ queItemSelId }) {
	return request({
		url: '/adm/cusExtDataQueItemSels',
		method: 'delete',
		params: {
			queItemSelId
		}
	});
}

export function getAdmParamApi({ paramType, paramCode }) {
	return request({
		url: '/adm/admParam',
		method: 'get',
		params: {
			paramType,
			paramCode
		}
	});
}

export function patchAdmParamApi({ paramType, paramCode, paramValue }) {
	return request({
		url: '/adm/admParam',
		method: 'patch',
		data: {
			paramType,
			paramCode,
			paramValue
		}
	});
}
export function getAssetMgmParamApi() {
	return request({
		url: '/adm/assetMgmParam',
		method: 'get'
	});
}

export function patchAssetMgmParamApi({ id, inflationRate, riskFreeRate }) {
	return request({
		url: '/adm/assetMgmParam',
		method: 'patch',
		data: {
			id,
			inflationRate,
			riskFreeRate
		}
	});
}

export function getAdminWmsBatchGroupsApi() {
	return request({
		url: '/adm/admWmsBatchGroups',
		method: 'get'
	});
}

export function getBatchJobStatusHistoryApi({ objectName, dataDateTime }) {
	return request({
		url: '/adm/batchJobStatusHistory',
		method: 'get',
		data: {
			objectName,
			dataDt: dataDateTime
		}
	});
}

export function getPagedBatchJobStatusApi(page, pageable, params) {
	const {
		objectName,
		startDate,
		endDate,
		status,
		batchStartDate,
		batchEndDate,
		groupCode
	} = params;
	return request({
		url: '/adm/batchJobStatus',
		method: 'get',
		params: {
			page: typeof page === 'number' ? page : pageable.page,
			size: pageable.size,
			sort: pageable.sort + ',' + pageable.direction
		},
		data: {
			objectName,
			startDate,
			endDate,
			status,
			batchStartDate,
			batchEndDate,
			groupCode
		}
	});
}

export function getExportJobStatusExcelApi(params) {
	const {
		objectName,
		startDate,
		endDate,
		status,
		batchStartDate,
		batchEndDate,
		groupCode
	} = params;
	return request({
		url: '/adm/batchJobStatus/export',
		method: 'get',
		params: {
			objectName,
			startDate,
			endDate,
			status,
			batchStartDate,
			batchEndDate,
			groupCode
		},
		responseType: 'blob'
	});
}

export function getReuseWordsApi({ type } = {}) {
	return request({
		url: '/adm/reuseWords',
		method: 'get',
		params: {
			type
		}
	});
}

export function postReuseWordsApi({ type, words }) {
	return request({
		url: '/adm/reuseWords',
		method: 'post',
		data: {
			type,
			words
		}
	});
}

export function updateReuseWordsApi(dataArray) {
	return request({
		url: '/adm/reuseWords',
		method: 'patch',
		data: [...dataArray]
	});
}

export function deleteReuseWordsApi({ id }) {
	return request({
		url: '/adm/reuseWords',
		method: 'delete',
		params: {
			id
		}
	});
}

export function patchReuseWordsApi({ wordsId, words }) {
	return request({
		url: '/adm/reuseWords',
		method: 'patch',
		params: {
			wordsId,
			words
		}
	});
}
