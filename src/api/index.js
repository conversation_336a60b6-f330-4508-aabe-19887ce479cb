import request from '@/utils/request';

// contentHeader.vue
export function getMessageListApi(msgType) {
	return request({
		url: '/gen/genNewsMessage',
		method: 'get',
		params: {
			msgType
		}
	});
}

export function getSwitchIdentityListApi(userCode) {
	return request({
		url: '/adm/switchIdentity',
		method: 'get',
		params: {
			userCode
		}
	});
}

// msg.vue
// getGenInternalMsg() 呼叫了與 contentHeader.vue 的 getGenInternalMsgApi 一樣的Api
