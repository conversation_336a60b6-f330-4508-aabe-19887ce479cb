import request from '@/utils/request';

export function postPersonalTaskApi({ nextRemindDt, nextRemindTime, advNce, advNceDay, advNcePrd, title, content }) {
	return request({
		url: '/wob/tdPersonalRec',
		method: 'post',
		data: {
			nextRemindDt,
			nextRemindTime,
			advNce,
			advNceDay,
			advNcePrd,
			title,
			content
		}
	});
}

export function postVisit({
	recCode,
	cusCode,
	cusName,
	nextRemindDt,
	nextRemindTime,
	visitPurCode,
	visitAprCode,
	title,
	advNce,
	advNceDay,
	advNcePrd,
	content,
	doneYn,
	verifyStatusCode
}) {
	return request({
		url: '/wob/tdRec',
		method: 'post',
		data: {
			recCode,
			cusCode,
			cusName,
			nextRemindDt,
			nextRemindTime,
			visitPurCode,
			visitAprCode,
			title,
			advNce,
			advNceDay,
			advNcePrd,
			content,
			doneYn,
			verifyStatusCode
		}
	});
}

export function postConnect({ cusCode, nextRemindDt, nextRemindTime, title, visitAprCode, content, contStatCode, contProcCode, doneYn }) {
	return request({
		url: '/wob/tdConnRec',
		method: 'post',
		data: {
			cusCode,
			nextRemindDt,
			nextRemindTime,
			title,
			visitAprCode,
			content,
			contStatCode,
			contProcCode,
			doneYn
		}
	});
}

export function postMemoryDateApi({ cusCode, dateDt, note, remindYn, remindDays, remindPrd }) {
	return request({
		url: '/wob/memoryDate',
		method: 'post',
		data: {
			cusCode,
			dateDt,
			note,
			remindYn,
			remindDays,
			remindPrd
		}
	});
}

export function getCalendarTasksApi({ startDate, endDate }) {
	return request({
		url: '/wob/calendarTasks',
		method: 'get',
		params: {
			startDate,
			endDate
		}
	});
}

export function getTdRecApi({ recCode }) {
	return request({
		url: '/wob/tdRec',
		method: 'get',
		params: {
			recCode
		}
	});
}

export function deleteTdRecApi({ recCode }) {
	return request({
		url: '/wob/tdRec',
		method: 'delete',
		params: {
			recCode
		}
	});
}

export function patchTdPersonalRecApi({ tdRec, nextRemindDt, nextRemindTime, title, content, advNce, advNceDay, advNcePrd }) {
	return request({
		url: '/wob/tdPersonalRec',
		method: 'patch',
		data: {
			tdRec,
			nextRemindDt,
			nextRemindTime,
			title,
			content,
			advNce,
			advNceDay,
			advNcePrd
		}
	});
}

export function getAppointmentTdRecApi({ recCode }) {
	return request({
		url: '/wob/appointmentTdRec',
		method: 'get',
		params: {
			recCode
		}
	});
}

export function patchAppointmentTdRecApi({
	recCode,
	cusCode,
	nextRemindDt,
	nextRemindTime,
	visitPurCode,
	visitAprCode,
	title,
	advNce,
	advNceDay,
	advNcePrd,
	content,
	doneYn,
	verifyStatusCode
}) {
	return request({
		url: '/wob/appointmentTdRec',
		method: 'patch',
		data: {
			recCode,
			cusCode,
			nextRemindDt,
			nextRemindTime,
			visitPurCode,
			visitAprCode,
			title,
			advNce,
			advNceDay,
			advNcePrd,
			content,
			doneYn,
			verifyStatusCode
		}
	});
}

export function getMemoryCalendarTaskApi({ id }) {
	return request({
		url: '/wob/memoryCalendarTask',
		method: 'get',
		params: {
			id
		}
	});
}

export function deleteMemoryDateApi({ id }) {
	return request({
		url: '/wob/memoryDate',
		method: 'delete',
		params: {
			id
		}
	});
}

export function patchMemoryDateApi({ id, cusCode, dateDt, note, remindDays, remindPrd, remindYn }) {
	return request({
		url: '/wob/memoryDate',
		method: 'patch',
		data: {
			id,
			cusCode,
			dateDt,
			note,
			remindDays,
			remindPrd,
			remindYn
		}
	});
}

export function getTdConnRecApi({ recCode }) {
	return request({
		url: '/wob/tdConnRec',
		method: 'get',
		params: {
			recCode
		}
	});
}

export function patchTdConnRecApi({
	recCode,
	cusCode,
	nextRemindDt,
	nextRemindTime,
	doneYn,
	title,
	content,
	visitAprCode,
	contStatCode,
	contProcCode,
	contProcDesc,
	visitUserName,
	verifyStatusCode,
	giftId,
	giftDesc
}) {
	return request({
		url: '/wob/tdConnRec',
		method: 'patch',
		data: {
			recCode,
			cusCode,
			nextRemindDt,
			nextRemindTime,
			doneYn,
			title,
			content,
			visitAprCode,
			contStatCode,
			contProcCode,
			contProcDesc,
			visitUserName,
			verifyStatusCode,
			giftId,
			giftDesc
		}
	});
}

export function postWobReuseWordsApi({ wordsId, words }) {
	return request({
		url: '/wob/wobReuseWords',
		method: 'post',
		data: {
			wordsId,
			words
		}
	});
}

export function getWobReuseWordsApi() {
	return request({
		url: '/wob/wobReuseWords',
		method: 'get'
	});
}
export function getWobSerachTdListApi({ startDate, endDate, tdCat1Code, itemCode, cusCode, branCode, allBranCode, userCode, allUserCode, pageable }) {
	const { page, size, sort, direction } = pageable;
	const paginationParams = {
		page,
		size,
		sort: `${sort},${direction}`
	};
	return request({
		url: '/wob/serachTdList',
		method: 'get',
		params: {
			startDate,
			endDate,
			tdCat1Code,
			itemCode,
			cusCode,
			branCode,
			allBranCode,
			userCode,
			allUserCode,
			...paginationParams
		}
	});
}

export function getVisitLogsApi({ cusCode, strDate, endDate, branCode, userCode, eventType, visitPurCode }, queryString) {
	return request({
		url: '/wob/visitLogs' + queryString,
		method: 'get',
		params: {
			cusCode,
			strDate,
			endDate,
			branCode,
			userCode,
			eventType,
			visitPurCode
		}
	});
}

export function getWobTdRecVerifiesApi(
	{
		minorAreaBranCode,
		allBranCodes,
		branCode,
		allUserCodes,
		userCode,
		buCode,
		createStartDate,
		createEndDate,
		cusName,
		idn,
		verifyStartDate,
		verifyEndDate,
		verifyStatusCodes
	},
	queryString
) {
	return request({
		url: '/wob/wobTdRecVerifies' + queryString,
		method: 'get',
		params: {
			minorAreaBranCode,
			allBranCodes,
			branCode,
			allUserCodes,
			userCode,
			buCode,
			createStartDate,
			createEndDate,
			cusName,
			idn,
			verifyStartDate,
			verifyEndDate,
			verifyStatusCodes
		}
	});
}

export function patchWobTdRecVerifiesApi(updateItems) {
	return request({
		url: '/wob/wobTdRecVerifies',
		method: 'patch',
		data: updateItems
	});
}

export function getVisitEventMenuApi({ eventType }) {
	return request({
		url: '/wob/visitEventMenu',
		method: 'get',
		params: {
			eventType
		}
	});
}

export function getTdItemCat1ToDoMenuApi() {
	return request({
		url: '/wob/tdItemCat1ToDoMenu',
		method: 'get'
	});
}

export function getTdItmeCountApi({ groupCode }) {
	return request({
		url: '/wob/tdItmeCount',
		method: 'get',
		params: {
			groupCode
		}
	});
}

export function getTdItmeStatApi({ tdCat1Code, groupCode }) {
	return request({
		url: '/wob/tdItmeStat',
		method: 'get',
		params: {
			tdCat1Code,
			groupCode
		}
	});
}

export function getTdListApi({ tdCat1Code, itemCode, toDoItemType, groupCode }, queryString) {
	return request({
		url: '/wob/tdList' + queryString,
		method: 'get',
		params: {
			tdCat1Code,
			itemCode,
			toDoItemType,
			groupCode
		}
	});
}

export function patchUpdateAndDoneTdListApi(tdListUpdateReq, queryString) {
	return request({
		url: '/wob/updateAndDoneTdList' + queryString,
		method: 'patch',
		data: tdListUpdateReq
	});
}

export function getFinishedItmeCountApi({ groupCode, startDate, endDate }) {
	return request({
		url: '/wob/finishedItmeCount',
		method: 'get',
		params: {
			groupCode,
			startDate,
			endDate
		}
	});
}

export function getFinishedItmesApi({ tdCat1Code, groupCode, startDate, endDate }, queryString) {
	return request({
		url: '/wob/finishedItmes' + queryString,
		method: 'get',
		params: {
			tdCat1Code,
			groupCode,
			startDate,
			endDate
		}
	});
}

export function getTdListLogsHisApi({ tdCode }) {
	return request({
		url: '/wob/tdListLogsHis',
		method: 'get',
		params: {
			tdCode
		}
	});
}

export function getGetWobTdStatusApi({ itemCode }) {
	return request({
		url: '/wob/getWobTdStatus',
		method: 'get',
		params: {
			itemCode
		}
	});
}

export function getgGtWobTdActionApi({ itemCode, actionCode }) {
	return request({
		url: '/wob/getWobTdAction',
		method: 'get',
		params: {
			itemCode,
			actionCode
		}
	});
}

export function getTdLogDetailsApi({ tdCode }) {
	return request({
		url: '/wob/tdLogDetails',
		method: 'get',
		params: {
			tdCode
		}
	});
}

export function getTdItemHandleDetailApi({ tdCode }) {
	return request({
		url: '/wob/tdItemHandleDetail',
		method: 'get',
		params: {
			tdCode
		}
	});
}

export function patchUpdateTdListsApi({
	itemCode,
	tdCode,
	statusCode,
	statusName,
	actionCode,
	actionName,
	memo,
	chiefVistYn,
	verifyYn,
	traceYn,
	isUpdateAndDone,
	isHis
}) {
	return request({
		url: '/wob/updateTdLists',
		method: 'patch',
		data: {
			itemCode,
			tdCode,
			statusCode,
			statusName,
			actionCode,
			actionName,
			memo,
			chiefVistYn,
			verifyYn,
			traceYn,
			isUpdateAndDone,
			isHis
		}
	});
}

export function patchUpdateAndAuditTdListsApi({
	itemCode,
	tdCode,
	statusCode,
	statusName,
	actionCode,
	actionName,
	memo,
	chiefVistYn,
	verifyYn,
	traceYn,
	isUpdateAndDone,
	isHis
}) {
	return request({
		url: '/wob/updateAndAuditTdLists',
		method: 'patch',
		data: {
			itemCode,
			tdCode,
			statusCode,
			statusName,
			actionCode,
			actionName,
			memo,
			chiefVistYn,
			verifyYn,
			traceYn,
			isUpdateAndDone,
			isHis
		}
	});
}

export function getTdItemCustomizeCountApi() {
	return request({
		url: '/wob/tdItemCustomizeCount',
		method: 'get'
	});
}
export function getWobAttBranchesApi() {
	return request({
		url: '/wob/wobAttBranches',
		method: 'get'
	});
}

export function getWobSimpleCusApi({ cusCode }) {
	return request({
		url: '/wob/wobSimpleCus',
		method: 'get',
		params: {
			cusCode
		}
	});
}

export function postWobVisitApi({ formData }) {
	return request({
		url: '/wob/wobVisit',
		method: 'post',
		data: formData,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	});
}
export function postWobContactApi({
	cusCode,
	visitAprCode,
	visitPurCode,
	visitDate,
	visitHour,
	visitMin,
	visitContent,
	cusName,
	idn
}) {
	return request({
		url: '/wob/wobContact',
		method: 'post',
		params: {
			cusCode,
			visitAprCode,
			visitPurCode,
			visitDate,
			visitHour,
			visitMin,
			visitContent,
			cusName,
			idn
		}
	});
}

export function getWobAttUserChkApi({ userCode }) {
	return request({
		url: '/wob/wobAttUserChk',
		method: 'get',
		params: {
			userCode
		}
	});
}

export function getWobTdListDescApi({ tdKind, tdCode }) {
	return request({
		url: '/wob/tdListDesc',
		method: 'get',
		params: { tdKind, tdCode }
	});
}
