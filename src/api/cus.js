import request from '@/utils/request';

export function getCusGradesApi() {
	return request({
		url: '/cus/cusGrades',
		method: 'get'
	});
}

export function cusSingleQuery(queryReq, queryString) {
	return request({
		url: '/cus/singleCus' + queryString,
		method: 'get',
		params: queryReq
	});
}

export function getCusGroupListApi() {
	return request({
		url: '/cus/cusGroupList',
		method: 'get'
	});
}

export function getSelectedMenuItemsApi() {
	return request({
		url: '/cus/cusSearchFields',
		method: 'get'
	});
}

export function getAllSearchFieldsApi() {
	return request({
		url: '/cus/cusAllSearchFields',
		method: 'get'
	});
}

export function postCusSelfsetFields({ fieldNameList }) {
	return request({
		url: '/cus/cusSelfsetFields',
		method: 'post',
		traditional: true,
		data: {
			fieldNameList
		}
	});
}
export function getCountSearchResultApi() {
	return request({
		url: '/cus/countSearchResult',
		method: 'get'
	});
}

export function postCusSearchList({ resultName, cusCodeList }) {
	return request({
		url: '/cus/cusSearchList',
		method: 'post',
		headers: { 'Content-Type': 'application/json' },
		data: {
			resultName,
			cusCodeList
		}
	});
}

export function insertCusSearchLogApi({ resultCode, resultName, logType }) {
	return request({
		url: '/cus/cusSearchLog',
		method: 'post',
		headers: { 'Content-Type': 'application/json' },
		data: {
			resultCode,
			resultName,
			logType
		}
	});
}

export function postCusGroupDetail({ groupCode, cusCodeList }) {
	return request({
		url: '/cus/cusGroupDetail',
		method: 'post',
		traditional: true,
		params: {
			groupCode,
			cusCodeList: cusCodeList.join(',')
		}
	});
}

export function checkCusAuthApi({ cusCode }) {
	return request({
		url: '/cus/customerAuth',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getCusInfoApi({ cusCode }) {
	return request({
		url: '/cus/clientOverview/cusInfo',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getCustomersApi({ cusCode }) {
	return request({
		url: '/cus/customers',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getAssetTrendApi({ cusCode }) {
	return request({
		url: '/cus/clientOverview/assetTrend',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getTdListsDataApi({ cusCode }) {
	return request({
		url: '/cus/clientOverview/tdLists',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getAssetLoansDataApi({ cusCode }) {
	return request({
		url: '/cus/clientOverview/assetLoans',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getCusGroupMenuApi() {
	return request({
		url: '/cus/cusGroupMenu',
		method: 'GET'
	});
}
export function postGroupApi({ groupName }) {
	return request({
		url: '/cus/group',
		method: 'POST',
		params: {
			groupName
		}
	});
}
export function deleteGroupApi({ groupCode }) {
	return request({
		url: '/cus/group',
		method: 'DELETE',
		params: {
			groupCode
		}
	});
}

export function getCusSummariesMenuApi({ pbStatus }) {
	return request({
		url: '/cus/cusSummariesMenu',
		method: 'GET',
		params: {
			pbStatus
		}
	});
}
export function getCusSummariesApi({ idn, graCode, cusName }, queryString) {
	return request({
		url: '/cus/cusSummaries' + queryString,
		method: 'GET',
		params: {
			idn,
			graCode,
			cusName
		}
	});
}

export function getDisabledDimensionsApi({ cusCode }) {
	return request({
		url: '/cus/disabledDimensions',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getCustomerInfoApi({ cusCode }) {
	return request({
		url: '/cus/customerInfo',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getCusAoHistoryApi({ cusCode }, queryString) {
	return request({
		url: '/cus/cusAoHistory/page' + queryString,
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getAoChangeHistApi({ cusCode }) {
	return request({
		url: '/cus/aoChangeHist',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getCompaniesApi({ cusCode }) {
	return request({
		url: '/cus/companies',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getCountriesMenuApi() {
	return request({
		url: '/cus/cusCountriesList',
		method: 'GET'
	});
}

export function postCompanyApi({
	cusCode,
	ownType,
	comName,
	comEname,
	vatNum,
	owner,
	establishDt,
	url,
	phone,
	contactPerson,
	contactPhone,
	indusCode,
	cunCode,
	regCunCode,
	zip,
	caddress,
	employeeNum,
	listedYn,
	note,
	capital
}) {
	return request({
		url: '/cus/companies',
		method: 'POST',
		data: {
			cusCode,
			ownType,
			comName,
			comEname,
			vatNum,
			owner,
			establishDt,
			url,
			phone,
			contactPerson,
			contactPhone,
			indusCode,
			cunCode,
			regCunCode,
			zip,
			caddress,
			employeeNum,
			listedYn,
			note,
			capital
		}
	});
}

export function patchCompanyApi({
	comId,
	cusCode,
	ownType,
	comName,
	comEname,
	vatNum,
	owner,
	establishDt,
	url,
	phone,
	contactPerson,
	contactPhone,
	indusCode,
	cunCode,
	regCunCode,
	zip,
	caddress,
	employeeNum,
	listedYn,
	note,
	capital
}) {
	return request({
		url: '/cus/companies',
		method: 'PATCH',
		data: {
			comId,
			cusCode,
			ownType,
			comName,
			comEname,
			vatNum,
			owner,
			establishDt,
			url,
			phone,
			contactPerson,
			contactPhone,
			indusCode,
			cunCode,
			regCunCode,
			zip,
			caddress,
			employeeNum,
			listedYn,
			note,
			capital
		}
	});
}
export function deleteCompanyApi({ comId, cusCode }) {
	return request({
		url: '/cus/companies',
		method: 'DELETE',
		params: {
			comId,
			cusCode
		}
	});
}

export function getOwnCompanies({ comId }) {
	return request({
		url: '/cus/ownCompanies',
		method: 'GET',
		params: {
			comId
		}
	});
}

export function saveOwnCompaniesApi({ comId, ownComList }) {
	return request({
		url: '/cus/ownCompanies',
		method: 'PATCH',
		data: {
			comId,
			ownComList
		}
	});
}

export function getCompOverseasApi({ comId }) {
	return request({
		url: '/cus/compOverseas',
		method: 'GET',
		params: {
			comId
		}
	});
}

export function updateCompOtherDatasApi({ comId, overseaList }) {
	return request({
		url: '/cus/compOverseas',
		method: 'PATCH',
		data: {
			comId,
			overseaList
		}
	});
}
export function chkCustomerAuthApi({ cusCode, progCode }) {
	return request({
		url: '/cus/chkCustomerAuth',
		method: 'GET',
		params: {
			cusCode,
			progCode
		}
	});
}

export function getRelativeFriendsApi({ cusCode }) {
	return request({
		url: '/cus/relativeFriends',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function insertRelativeFriendsApi({
	cusCode,
	idn,
	relName,
	reltypeCode,
	gender,
	birthday,
	cphone,
	education,
	organization,
	post,
	note
}) {
	return request({
		url: '/cus/relativeFriends',
		method: 'POST',
		data: {
			cusCode,
			idn,
			relName,
			reltypeCode,
			gender,
			birthday,
			cphone,
			education,
			organization,
			post,
			note
		}
	});
}

export function updateRelativeFriendsApi({
	id,
	cusCode,
	idn,
	relName,
	reltypeCode,
	gender,
	birthday,
	cphone,
	education,
	organization,
	post,
	note
}) {
	return request({
		url: '/cus/relativeFriends',
		method: 'PATCH',
		data: {
			id,
			cusCode,
			idn,
			relName,
			reltypeCode,
			gender,
			birthday,
			cphone,
			education,
			organization,
			post,
			note
		}
	});
}

export function deleteRelativeFriendsApi({ id, cusCode }) {
	return request({
		url: '/cus/relativeFriends',
		method: 'DELETE',
		params: {
			id,
			cusCode
		}
	});
}

export function getMemoryDateApi({ cusCode }) {
	return request({
		url: '/cus/memoryDate',
		method: 'GET',
		params: {
			cusCode
		}
	});
}
export function postMemoryDateApi({
	cusCode,
	dateDt,
	remindYn,
	remindDays,
	note
}) {
	return request({
		url: '/cus/memoryDate',
		method: 'POST',
		data: {
			cusCode,
			dateDt,
			remindYn,
			remindDays,
			note
		}
	});
}
export function updateMemoryDateApi({
	id,
	cusCode,
	dateDt,
	remindYn,
	remindDays,
	note
}) {
	return request({
		url: '/cus/memoryDate',
		method: 'PATCH',
		data: {
			id,
			cusCode,
			dateDt,
			remindYn,
			remindDays,
			note
		}
	});
}

export function deleteMemoryDateApi({ id }) {
	return request({
		url: '/cus/memoryDate',
		method: 'DELETE',
		params: {
			id
		}
	});
}

export function getExtDataItemApi() {
	return request({
		url: '/cus/extDataItem',
		method: 'GET'
	});
}

export function getExtDataAnswersApi({ cusCode }) {
	return request({
		url: '/cus/extData',
		method: 'GET',
		params: {
			cusCode
		}
	});
}
export function getExtDataLogApi({ cusCode }) {
	return request({
		url: '/cus/extDataLog',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function updateExtDataAnsApi({ cusCode, queitem }) {
	return request({
		url: '/cus/extData',
		method: 'PATCH',
		data: {
			cusCode,
			queitem
		}
	});
}

export function getTranTypeListApi() {
	return request({
		url: '/cus/cusTranTypes',
		method: 'GET'
	});
}

export function getTransactionLogApi({
	cusCode,
	pfcatCode,
	tranTypeCode,
	bankProCode,
	refNo,
	tranDtB,
	tranDtE
}) {
	return request({
		url: '/cus/srchCusTrans',
		method: 'GET',
		params: {
			cusCode,
			pfcatCode: pfcatCode.join(','),
			tranTypeCode: tranTypeCode.join(','),
			bankProCode,
			refNo,
			tranDtB,
			tranDtE
		}
	});
}

export function getInvTargetApi({ cusCodes }) {
	return request({
		url: '/cus/invAnalysisInvTarget',
		method: 'GET',
		params: {
			cusCodes: cusCodes.join(',')
		}
	});
}

export function getInvTargetGeoFocusApi({ cusCodes }) {
	return request({
		url: '/cus/invAnalysisGeoFocus',
		method: 'GET',
		params: {
			cusCodes: cusCodes.join(',')
		}
	});
}

export function getInvTargetRiskApi({ cusCodes }) {
	return request({
		url: '/cus/invAnalysisRisk',
		method: 'GET',
		params: {
			cusCodes: cusCodes.join(',')
		}
	});
}

export function getInvTargetProTypes({ cusCodes }) {
	return request({
		url: '/cus/invAnalysisInvProType',
		method: 'GET',
		params: {
			cusCodes: cusCodes.join(',')
		}
	});
}

export function getCusFilesList({ cusCode, fileType }) {
	return request({
		url: '/cus/cusFiles/list',
		method: 'GET',
		params: {
			cusCode,
			fileType
		}
	});
}

export function generateCuaAssetReportApi({ cusCode }) {
	return request({
		url: '/cus/cusAssetReport',
		method: 'POST',
		params: {
			cusCode
		}
	});
}

export function getGroupCustomers({ groupCode }) {
	return request({
		url: '/cus/groupCustomers',
		method: 'GET',
		params: {
			groupCode
		}
	});
}
export function postGroupCustomers({ groupCode, groupName, cusCodes }) {
	return request({
		url: '/cus/groupCustomers',
		method: 'POST',
		data: {
			groupCode,
			groupName,
			cusCodes
		}
	});
}

export function getCustomer({ cusCode }) {
	return request({
		url: '/cus/customer',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getSearchHistoryApi({ userCode }) {
	return request({
		url: '/cus/cusSearchResult',
		method: 'GET',
		params: {
			userCode
		}
	});
}

export function postCusSearchLog({
	userCode,
	resultCode,
	resultName,
	logType,
	deputyUserCode
}) {
	return request({
		url: '/cus/cusSearchLog',
		method: 'POST',
		data: {
			userCode,
			resultCode,
			resultName,
			logType,
			deputyUserCode
		}
	});
}

export function deleteSearchResult({ resultCode }) {
	return request({
		url: '/cus/cusSearchResult',
		method: 'DELETE',
		params: {
			resultCode
		}
	});
}

export function getMultiCusApi(
	{
		graCode,
		area,
		branCode,
		userCode,
		birthStart,
		birthEnd,
		rankCode,
		aumAmountS,
		aumAmountE,
		contbAmountS,
		contbAmountE,
		contbTime,
		contbTimeS,
		contbTimeE,
		savingType,
		savingC,
		savingAmountS,
		savingAmountE,
		proCatList,
		proCode,
		proC,
		intType,
		mktAmountS,
		mktAmountE,
		invAmountS,
		invAmountE,
		returnAmountS,
		returnAmountE,
		invType,
		debitType,
		fundC,
		efficiencyInv,
		fundType,
		insType,
		insC,
		insAAmountS,
		insAAmountE,
		loanProCode,
		loanRemark,
		loanStdDateS,
		loanStdDateE,
		loanPeriodS,
		loanPeriodE,
		appropriationS,
		appropriationE,
		loanOverAmountS,
		loanOverAmountE,
		conAppropriationS,
		conAppropriationE,
		conRateS,
		conRateE,
		conReturnType,
		conOverAmountS,
		conOverAmountE,
		conRepaymentRateS,
		conRepaymentRateE,
		approveAmountS,
		approveAmountE,
		interestRateS,
		interestRateE,
		status,
		userAmountS,
		userAmountE,
		availableAmountS,
		availableAmountE,
		queList
	},
	queryString
) {
	return request({
		url: '/cus/multiCus' + queryString,
		method: 'GET',
		params: {
			graCode,
			area,
			branCode,
			userCode,
			birthStart,
			birthEnd,
			rankCode,
			aumAmountS,
			aumAmountE,
			contbAmountS,
			contbAmountE,
			contbTime,
			contbTimeS,
			contbTimeE,
			savingType,
			savingC,
			savingAmountS,
			savingAmountE,
			proCatList,
			proCode,
			proC,
			intType,
			mktAmountS,
			mktAmountE,
			invAmountS,
			invAmountE,
			returnAmountS,
			returnAmountE,
			invType,
			debitType,
			fundC,
			efficiencyInv,
			fundType,
			insType,
			insC,
			insAAmountS,
			insAAmountE,
			loanProCode,
			loanRemark,
			loanStdDateS,
			loanStdDateE,
			loanPeriodS,
			loanPeriodE,
			appropriationS,
			appropriationE,
			loanOverAmountS,
			loanOverAmountE,
			conAppropriationS,
			conAppropriationE,
			conRateS,
			conRateE,
			conReturnType,
			conOverAmountS,
			conOverAmountE,
			conRepaymentRateS,
			conRepaymentRateE,
			approveAmountS,
			approveAmountE,
			interestRateS,
			interestRateE,
			status,
			userAmountS,
			userAmountE,
			availableAmountS,
			availableAmountE,
			queList
		}
	});
}

export function getLastAssetAmount({ cusCode }) {
	return request({
		url: '/cus/lastAssetAmount',
		method: 'GET',
		params: {
			cusCode
		}
	});
}

export function getCusGroupListPageApi({ groupName }, queryString) {
	return request({
		url: '/cus/cusGroupList/page' + queryString,
		method: 'GET',
		params: {
			groupName
		}
	});
}

export function postCusGroupApi({ groupName }) {
	return request({
		url: '/cus/cusGroup',
		method: 'POST',
		params: {
			groupName
		}
	});
}

export function patchCusGroupApi({ groupCode, groupName }) {
	return request({
		url: '/cus/cusGroup',
		method: 'PATCH',
		params: {
			groupCode,
			groupName
		}
	});
}
export function deleteCusGroupApi({ groupCode }) {
	return request({
		url: '/cus/cusGroup',
		method: 'DELETE',
		params: {
			groupCode
		}
	});
}

export function getSrchCusGroupApi({ graCode, cusCode, cusName }) {
	return request({
		url: '/cus/srchCusGroup',
		method: 'GET',
		params: {
			graCode,
			cusCode,
			cusName
		}
	});
}

export function patchCusGroupDetailApi({ groupCode, groupName, groupDetail }) {
	return request({
		url: '/cus/cusGroupDetail',
		method: 'PATCH',
		data: {
			groupCode,
			groupName,
			groupDetail
		}
	});
}

export function getCusGroupDetailApi({ groupCode }) {
	return request({
		url: '/cus/cusGroupDetail',
		method: 'GET',
		params: {
			groupCode
		}
	});
}

export function getMenuVerifyRecsApi({ startDate, endDate }) {
	return request({
		url: '/cus/menuVerifyRecs',
		method: 'GET',
		params: {
			startDate,
			endDate
		}
	});
}

export function postPointCustomerApi({ groupCode, cusCodes }) {
	return request({
		url: '/cus/pointCustomer',
		method: 'POST',
		params: {
			groupCode,
			cusCodes
		}
	});
}

export function getVisitAprActionMenuApi() {
	return request({
		url: '/cus/visitAprActionMenu',
		method: 'GET'
	});
}

export function getContStatMenuApi() {
	return request({
		url: '/cus/contStatMenu',
		method: 'get'
	});
}
export function getContProcMenuApi({ groupSeq } = null) {
	return request({
		url: '/cus/contProcMenu',
		method: 'get',
		params: {
			groupSeq
		}
	});
}

export function getOtherDetailApi({ cusCode }) {
	return request({
		url: '/cus/otherDetail',
		method: 'get',
		params: {
			cusCode
		}
	});
}
// 資產報告書清單
export function getAssetReportList() {
	return request({
		url: '/cus/cusDoc/assetReports',
		method: 'get'
	});
}
// 新增資產報告書&取得ID
export function createAssetReport(newReport) {
	return request({
		url: '/cus/cusDoc/newAssetReport',
		method: 'post',
		data: newReport
	});
}
// 資產報告書資訊
export function getAssetReportDetail({ reportId }) {
	return request({
		url: '/cus/cusDoc/assetReportDetail',
		method: 'get',
		params: {
			reportId
		}
	});
}
// 元件列表
export function getElementMenu() {
	return request({
		url: '/cus/cusDoc/elementMenu',
		method: 'get'
	});
}
// 儲存資產報告書
export function saveAssetReport(report) {
	return request({
		url: '/cus/cusDoc/assetReport',
		method: 'post',
		data: report
	});
}
// 刪除資產報告書
export function deleteAssetReport({ reportId }) {
	return request({
		url: '/cus/cusDoc/assetReport',
		method: 'delete',
		params: {
			reportId
		}
	});
}

export function getRelCustomerMenuApi({ cusCode }) {
	return request({
		url: '/cus/relCustomerMenu',
		method: 'get',
		params: {
			cusCode
		}
	});
}

export function getBaseCurCodeApi({ cusCode }) {
	return request({
		url: '/cus/baseCurCode',
		method: 'get',
		params: {
			cusCode
		}
	});
}

export function getCalculateCurCodesApi() {
	return request({
		url: '/cus/calculateCurCodes',
		method: 'get'
	});
}

export function getAssetDataApi({ path, cusCode, params }) {
	return request({
		url: path,
		method: 'get',
		params: {
			cusCode,
			...params
		}
	});
}
export function getCusSavingAvgApi({ cusCode }) {
	return request({
		url: '/cus/cusSavingAvg',
		method: 'get',
		params: {
			cusCode
		}
	});
}

export function getCusSavingExpireApi({ cusCode }) {
	return request({
		url: '/cus/cusSavingExpire',
		method: 'get',
		params: {
			cusCode
		}
	});
}

export function getCusSavingGroupByProtypeApi({ cusCode }) {
	return request({
		url: '/cus/cusSavingGroupByProtype',
		method: 'get',
		params: {
			cusCode
		}
	});
}

export function getCusSavingGroupByCurApi({ cusCode }) {
	return request({
		url: '/cus/cusSavingGroupByCur',
		method: 'get',
		params: {
			cusCode
		}
	});
}
export function getCusSavingByProtypeCodes({ cusCode, protypeCodes }) {
	return request({
		url: '/cus/cusSavingByProtypeCodes',
		method: 'get',
		params: {
			cusCode,
			protypeCodes
		}
	});
}

export function getCusFundGroupBySectorApi({ cusCode }) {
	return request({
		url: '/cus/cusFundGroupBySector',
		method: 'get',
		params: {
			cusCode
		}
	});
}
export function getCusFundGroupByCurApi({ cusCode }) {
	return request({
		url: '/cus/cusFundGroupByCur',
		method: 'get',
		params: {
			cusCode
		}
	});
}

export function getCusFundGroupByProtypeApi({ cusCode }) {
	return request({
		url: '/cus/cusFundGroupByProtype',
		method: 'get',
		params: {
			cusCode
		}
	});
}

export function getCusFundApi({ cusCode }) {
	return request({
		url: '/cus/cusFund',
		method: 'get',
		params: {
			cusCode
		}
	});
}
export function getCusOnwayApi({ cusCode, pfcatCode }) {
	return request({
		url: '/cus/cusOnway',
		method: 'get',
		params: {
			cusCode,
			pfcatCode
		}
	});
}

export function getCusBondGroupByCurApi({ cusCode }) {
	return request({
		url: '/cus/cusBondGroupByCur',
		method: 'get',
		params: {
			cusCode
		}
	});
}
export function getCusBondGroupByProtypeApi({ cusCode }) {
	return request({
		url: '/cus/cusBondGroupByProtype',
		method: 'get',
		params: {
			cusCode
		}
	});
}

export function getCusBondApi({ cusCode }) {
	return request({
		url: '/cus/cusBond',
		method: 'get',
		params: {
			cusCode
		}
	});
}

export function getCusEtfGroupBySectorApi({ cusCode }) {
	return request({
		url: '/cus/cusEtfGroupBySector',
		method: 'get',
		params: {
			cusCode
		}
	});
}
export function getCusEtfGroupByCurApi({ cusCode }) {
	return request({
		url: '/cus/cusEtfGroupByCur',
		method: 'get',
		params: {
			cusCode
		}
	});
}
export function getCusEtfGroupByProtypeApi({ cusCode }) {
	return request({
		url: '/cus/cusEtfGroupByProtype',
		method: 'get',
		params: {
			cusCode
		}
	});
}
export function getCusEtfApi({ cusCode }) {
	return request({
		url: '/cus/cusEtf',
		method: 'get',
		params: {
			cusCode
		}
	});
}

export function getCusPstockGroupBySectorApi({ cusCode }) {
	return request({
		url: '/cus/cusPstockGroupBySector',
		method: 'get',
		params: {
			cusCode
		}
	});
}

export function getCusPstockGroupByCurApi({ cusCode }) {
	return request({
		url: '/cus/cusPstockGroupByCur',
		method: 'get',
		params: {
			cusCode
		}
	});
}
export function getCusPstockGroupByProtypeApi({ cusCode }) {
	return request({
		url: '/cus/cusPstockGroupByProtype',
		method: 'get',
		params: {
			cusCode
		}
	});
}
export function getCusPstockApi({ cusCode }) {
	return request({
		url: '/cus/cusPstock',
		method: 'get',
		params: {
			cusCode
		}
	});
}

export function getCusInsApi({ cusCode, queryType }) {
	return request({
		url: '/cus/cusIns',
		method: 'get',
		params: {
			cusCode,
			queryType
		}
	});
}
export function getCusSpGroupByCurApi({ cusCode }) {
	return request({
		url: '/cus/cusSpGroupByCur',
		method: 'get',
		params: {
			cusCode
		}
	});
}
export function getCusSpGroupByProtypeApi({ cusCode }) {
	return request({
		url: '/cus/cusSpGroupByProtype',
		method: 'get',
		params: {
			cusCode
		}
	});
}
export function getCusSpApi({ cusCode }) {
	return request({
		url: '/cus/cusSp',
		method: 'get',
		params: {
			cusCode
		}
	});
}

export function getCusDcdApi({ cusCode }) {
	return request({
		url: '/cus/cusDcd',
		method: 'get',
		params: {
			cusCode
		}
	});
}

export function getCusDcdDividendApi({ cusCode, refNo }) {
	return request({
		url: '/cus/cusDcdDividend',
		method: 'get',
		params: {
			cusCode,
			refNo
		}
	});
}
export function getCusAoHistoryMainApi({
	startDt,
	endDt,
	areaCode,
	branCode,
	chgType,
	createBy,
	page,
	size,
	sort,
	direction
}) {
	return request({
		url: '/cus/cusAoHistoryMain',
		method: 'get',
		params: {
			startDt,
			endDt,
			areaCode,
			branCode,
			chgType,
			createBy,
			page,
			size,
			sort: `${sort},${direction}`
		}
	});
}

export function getCusAoHistoryDetailApi({
	startDt,
	endDt,
	areaCode,
	branCode,
	userCode,
	chgType,
	chgId,
	page,
	size,
	sort,
	direction
}) {
	return request({
		url: '/cus/cusAoHistoryDetail',
		method: 'get',
		params: {
			startDt,
			endDt,
			areaCode,
			branCode,
			userCode,
			chgType,
			chgId,
			page,
			size,
			sort: `${sort},${direction}`
		}
	});
}

export function getCusTransApplyApi({ cusCode, cusName, page, size, sort }) {
	return request({
		url: '/cus/cusTransApply',
		method: 'get',
		params: {
			cusCode,
			cusName,
			page,
			size,
			sort
		}
	});
}

export function postCusTransLogApi({
	cusCode,
	branCode,
	userCode,
	applyReason
}) {
	return request({
		url: '/cus/cusTransLog',
		method: 'post',
		data: {
			cusCode,
			branCode,
			userCode,
			applyReason
		}
	});
}
/**
 * 客戶移轉分派
 * @param {Object|Array} transfers
 * @param {string} transfers.chgId
 * @param {string} transfers.cusCode
 * @param {string} transfers.branCode
 * @param {string} transfers.userCode
 * @returns {Promise}
 */
export function postCusTransferResultApi(transfers) {
	return request({
		url: '/cus/cusTransferResult',
		method: 'post',
		data: transfers
	});
}
export function getCusRanksApi() {
	return request({
		url: '/cus/cusRanks',
		method: 'get'
	});
}

export function getCusListProjectsApi() {
	return request({
		url: '/cus/cusListProjects',
		method: 'get'
	});
}

export function getCusMultiTransApi({
	// 查詢條件參數
	idnOriList,
	cusList,
	cusName,
	gender,
	idnType,
	rankCode,
	graCode,
	trustYn,
	piYn,
	cusType,
	fundYn,
	bondYn,
	insYn,
	notTrade,
	notTradeStartDt,
	notTradeEndDt,
	areaCode,
	branCode,
	userCode,
	projectList,
	chgId,
	notLocked,
	// 分頁參數
	page,
	size,
	sort,
	direction
}) {
	return request({
		url: '/cus/cusMultiTrans',
		method: 'get',
		params: {
			idnOriList,
			cusList,
			cusName,
			gender,
			idnType,
			rankCode,
			graCode,
			trustYn,
			piYn,
			cusType,
			fundYn,
			bondYn,
			insYn,
			notTrade,
			notTradeStartDt,
			notTradeEndDt,
			areaCode,
			branCode,
			userCode,
			projectList,
			chgId,
			notLocked,
			page,
			size,
			sort: `${sort},${direction}`
		}
	});
}

export function getCusAoMaxCountApi({ userCode }) {
	return request({
		url: '/cus/cusAoMaxCount',
		method: 'get',
		params: {
			userCode
		}
	});
}
export function getCountCusAoApi({ userCode }) {
	return request({
		url: '/cus/countCusAo',
		method: 'get',
		params: {
			userCode
		}
	});
}
export function getCusInvAnalysisEachMonthAumApi({ cusCodes }) {
	return request({
		url: '/cus/invAnalysisEachMonthAum',
		method: 'get',
		params: {
			cusCodes
		}
	});
}

export function getCusInvAnalysisEachMonthRplApi({ cusCodes }) {
	return request({
		url: '/cus/invAnalysisEachMonthRpl',
		method: 'get',
		params: {
			cusCodes
		}
	});
}
export function getInvsetPlAnalysisApi({ cusCode }) {
	return request({
		url: '/cus/invsetPlAnalysis',
		method: 'get',
		params: {
			cusCode
		}
	});
}
export function getWealthAnalysisEachMonthApi({ cusCodes }) {
	return request({
		url: '/cus/wealthAnalysisEachMonth',
		method: 'get',
		params: {
			cusCodes
		}
	});
}
export function getInvAnalysisTxAmtApi({ cusCodes }) {
	const queryString = cusCodes
		.map(code => `cusCodes%5B%5D=${encodeURIComponent(code)}`)
		.join('&');

	const fullUrl = `/cus/invAnalysisTxAmt?${queryString}`;
	return request({
		url: fullUrl,
		method: 'get'
	});
}
export function getInvAnalysisEachMonthTxAmtApi({ cusCodes }) {
	const queryString = cusCodes
		.map(code => `cusCodes%5B%5D=${encodeURIComponent(code)}`)
		.join('&');

	const fullUrl = `/cus/invAnalysisEachMonthTxAmt?${queryString}`;
	return request({
		url: fullUrl,
		method: 'get'
	});
}

// ------ 客戶建議報告書mock api -------
// 資產與負債
// 投資需求

// 資產變動狀況
import getAssetChangeApiMockData from '../../mock/mockData/cus/getAssetChangeApiMockData.json';
export function getAssetChangeApi({ cusCode }) {
	return getAssetChangeApiMockData;
}

// 已實現損益
import getRealizedProfitLossApiMopckData from '../../mock/mockData/cus/getRealizedProfitLossApiMopckData.json';
export function getRealizedProfitLossApi({ cusCode }) {
	return getRealizedProfitLossApiMopckData;
}

// 資產分類
import getAssetCategoryApiMockData from '../../mock/mockData/cus/getAssetCategoryApiMockData.json';
export function getAssetCategoryApi({ cusCode }) {
	return getAssetCategoryApiMockData;
}

// 資產與負債資產往來變化
import getAssetLoanChangeApiMockData from '../../mock/mockData/cus/getAssetLoanChangeApiMockData.json';
export function getAssetLoanChangeApi({ cusCode }) {
	return getAssetLoanChangeApiMockData;
}

// 資產總覽

// 資產類別餅圖-靜態頁

// 交易紀錄查詢

// 淨值分析
import getNetValAnalysisApiMockData from '../../mock/mockData/cus/getNetValAnalysisApiMockData.json';
export function getNetValAnalysisApi({ cusCode }) {
	return getNetValAnalysisApiMockData;
}

// 交易金額分析

// 實現損益查詢
import getRealizeProfitApiMockData from '../../mock/mockData/cus/getRealizeProfitApiMockData.json';
export function getRealizeProfitApi({ cusCode, tranDtB, tranDtE }) {
	return getRealizeProfitApiMockData;
}

// 交易紀錄查詢
