/**
 *
 * @param {{
 * 	page: number;
 * 	size: number;
 *  sort: {[key: string]: 'ASC' | 'DESC'} | [string, 'ASC' | 'DESC'];
 * } | {
 * 	page: number;
 *  size: number;
 *  sort: string;
 *  direction?: 'ASC' | 'DESC'
 * }} pagination
 * @returns
 */

export function paginationToParam(pagination) {
	const base = {
		page: pagination.page ?? 0,
		size: pagination.size ?? 20
	};

	if (!pagination.sort) return base;

	if (typeof pagination.sort === 'string')
		return {
			...base,
			sort: `${pagination.sort},${pagination.direction ?? 'ASC'}`
		};
	else if (Array.isArray(pagination.sort))
		return {
			...base,
			sort: pagination.sort.map(([column, direction]) => `${column},${direction}`)
		};
	else if (typeof pagination.sort === 'object')
		return {
			...base,
			sort: Object.entries(pagination.sort).map(([column, direction]) => `${column},${direction}`)
		};
}
