import request from '@/utils/request';
import { paginationToParam } from './pagination';

export function getGenMessageCat({ catType, catName, mainCatCode }) {
	return request({
		url: '/gen/messageCat',
		method: 'get',
		params: {
			catName,
			catType,
			mainCatCode
		}
	});
}
export function postGenMessageCat({ catType, catName, mainCatCode }) {
	return request({
		url: '/gen/messageCat',
		method: 'post',
		data: {
			catName,
			catType,
			mainCatCode
		}
	});
}
export function patchGenMessageCat({ catType, catName, mainCatCode, subCatCode }) {
	return request({
		url: '/gen/messageCat',
		method: 'patch',
		data: {
			catName,
			catType,
			mainCatCode,
			subCatCode
		}
	});
}

export function getGenCountMessage({ catType, subCatCode, mainCatCode }) {
	return request({
		url: '/gen/countMessage',
		method: 'get',
		params: {
			catType,
			subCatCode,
			mainCatCode
		}
	});
}
export function deleteGenMessageCat({ catType, catName, mainCatCode, subCatCode }) {
	return request({
		url: '/gen/messageCat',
		method: 'delete',
		params: {
			catName,
			catType,
			mainCatCode,
			subCatCode
		}
	});
}

export function getGenMessageApi({ msgId }) {
	return request({
		url: '/gen/message',
		method: 'get',
		params: {
			msgId
		}
	});
}
export function getBbmMaintainSaveApi() {
	return request({
		url: '/gen/bbmMaintainSave/list',
		method: 'get'
	});
}

export function getMsgLogApi({ eventId }) {
	return request({
		url: '/gen/msgLog',
		method: 'get',
		params: {
			eventId
		}
	});
}

export function postMsgLogApi(formData) {
	return request({
		url: '/gen/msgLog',
		method: 'post',
		data: formData,
		processData: false,
		contentType: false
	});
}

export function getMessageMapApi() {
	return request({
		url: '/gen/messageMap',
		method: 'get'
	});
}

export function getBbsMgtPageData(pagination, { msgCode, mainCatCode, subCatCode, validBgnDt, validEndDt, msgTitle, expiredYn, showYn }) {
	return request({
		url: '/gen/messageList/page',
		method: 'get',
		params: {
			...paginationToParam(pagination),
			msgCode,
			mainCatCode,
			subCatCode,
			validBgnDt,
			validEndDt,
			msgTitle,
			expiredYn,
			showYn
		}
	});
}
export function getBbsMgtHeadPageData({ msgCode }, queryString) {
	return request({
		url: '/gen/messageList/page' + queryString,
		method: 'get',
		params: {
			msgCode: msgCode
		}
	});
}

export function getMainDocTypeApi({ catCode, mainTypeCode }) {
	return request({
		url: '/gen/docType',
		method: 'get',
		params: {
			catCode,
			mainTypeCode
		}
	});
}
export function postOrPatchDoc({ httpMethod, formData }) {
	return request({
		url: '/gen/document',
		method: httpMethod,
		data: formData,
		processData: false,
		contentType: false
	});
}

export function getDocProQueryPageData(pageable, { docCat, mainType, subType, bgnDt, endDt, docName }, queryString) {
	return request({
		url: '/gen/document/page',
		method: 'get',
		params: {
			...paginationToParam(pageable),
			docCat,
			mainType,
			subType,
			bgnDt,
			endDt,
			docName
		}
	});
}

export function getViewSelDoc({ docCat, docId }) {
	return request({
		url: '/gen/document/list',
		method: 'get',
		params: {
			docCat,
			docId
		}
	});
}

export function deleteDocApi({ docId }) {
	return request({
		url: '/gen/document',
		method: 'delete',
		params: {
			docId
		}
	});
}

export function getDocProTypeCntApi() {
	return request({
		url: '/gen/docProTypeCnt',
		method: 'get'
	});
}

export function getDocProDCDPageData({ proTypeCode }, queryString) {
	return request({
		url: '/gen/docPros/page' + queryString,
		method: 'get',
		params: {
			proTypeCode
		}
	});
}

export function getDocMktMainCntApi() {
	return request({
		url: '/gen/docMktMainCnt',
		method: 'get'
	});
}

export function getDocMktSubCntApi({ mainTypeCode }) {
	return request({
		url: '/gen/docMktSubCnt',
		method: 'get',
		params: {
			mainTypeCode
		}
	});
}

export function getDocSalesPageData({ mainTypeCode, subTypeCode }, queryString) {
	return request({
		url: '/gen/docMkts/page' + queryString,
		method: 'get',
		params: {
			mainTypeCode,
			subTypeCode
		}
	});
}

export function getDocOutLookMainCntApi() {
	return request({
		url: '/gen/docOutLookMainCnt',
		method: 'get'
	});
}

export function getDocOutLookSubCntApi({ mainTypeCode }) {
	return request({
		url: '/gen/docOutLookSubCnt',
		method: 'get',
		params: {
			mainTypeCode
		}
	});
}

export function getDocResearchPageData({ mainTypeCode, subTypeCode }, queryString) {
	return request({
		url: '/gen/docOutLooks/page' + queryString,
		method: 'get',
		params: {
			mainTypeCode,
			subTypeCode
		}
	});
}

export function getDocIssuersPageData({ queryString }) {
	return request({
		url: '/gen/docIssuers/page' + queryString,
		method: 'get'
	});
}

export function getDocNewsPageData({ queryString }) {
	return request({
		url: '/gen/docNews/page' + queryString,
		method: 'get'
	});
}

export function getGenNewsMessageApi({ msgType }) {
	return request({
		url: '/gen/genNewsMessage',
		method: 'get',
		params: {
			msgType
		}
	});
}

export function getGenInternalMsgApi({ forceYn }) {
	return request({
		url: '/gen/genInternalMsg',
		method: 'get',
		params: {
			forceYn
		}
	});
}

export function getMessageMapHomeApi({ msgType }) {
	return request({
		url: '/gen/messageMapHome',
		method: 'get',
		params: {
			msgType: msgType.join(',')
		}
	});
}

export function getDocHomeApi() {
	return request({
		url: '/gen/docHome',
		method: 'get'
	});
}

export function getDocHomeListApi({ docCat }) {
	return request({
		url: '/gen/docHomeList',
		method: 'get',
		params: {
			docCat
		}
	});
}

export function getHomepageGenReviewCntApi() {
	return request({
		url: '/gen/homepageGenReviewCnt',
		method: 'get'
	});
}
export function getHomepageCusReviewCntApi() {
	return request({
		url: '/gen/homepageCusReviewCnt',
		method: 'get'
	});
}
export function getHomepageProReviewCntApi() {
	return request({
		url: '/gen/homepageProReviewCnt',
		method: 'get'
	});
}
