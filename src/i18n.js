import { createI18n } from 'vue-i18n';
import SUPPORT_LOCALES_URL from '/SUPPORT_LOCALES.json?url';

const locales = {};

export const i18n = createI18n({
	legacy: false,
	globalInjection: true,
	locale: localStorage.getItem('locale') ?? 'zh-TW',
	fallbackLocale: 'zh-TW',
	messages: {},
	missingWarn: false,
	fallbackWarn: false,
	missing: (_, key) => {
		loadTranslation(i18n.global.locale.value, resolveModule(key));
		return '...';
	}
});

void init();

async function init() {
	const SUPPORT_LOCALES = await fetch(SUPPORT_LOCALES_URL).then(it => it.json());
	console.log(`系統支援語言: ${SUPPORT_LOCALES.join(', ')}`);
	const defaultLocale = SUPPORT_LOCALES.find(it => it === localStorage.getItem('locale')) ?? SUPPORT_LOCALES[0];
	SUPPORT_LOCALES.forEach((locale) => {
		i18n.global.setLocaleMessage(locale, {});
	});
	i18n.global.fallbackLocale.value = SUPPORT_LOCALES[0];
	await loadLocales();
	await setLocale(defaultLocale);
}

async function loadLocales() {
	const modules = import.meta.glob('./locales/**/*.json', { import: 'default' });
	for (const path in modules) {
		// Example path: ./locales/adm/zh-TW.json
		const parts = path.split('/');
		const namespace = parts[2]; // "adm" or "core"
		const localeWithExt = parts[3]; // "zh-TW.json"
		const locale = localeWithExt.replace('.json', '');

		// Build the structure
		if (!locales[locale]) {
			locales[locale] = {};
		}

		locales[locale][namespace] = { promise: modules[path], loaded: false, loading: false };
	}
}

export function resolveModule(key) {
	return	key.split('.')[0]; // Remove any sub-properties
}

async function loadTranslation(locale, module) {
	const record = locales[locale][module];
	if (!record) return console.warn(`⚠️ 載入 ${module} 模組翻譯: ${locale} 失敗：模組不存在`);
	const { promise, loaded, loading } = record;
	if (loaded || loading) return;
	try {
		locales[locale][module].loading = true;
		i18n.global.mergeLocaleMessage(locale, { [module]: await promise() });
		locales[locale][module].loaded = true;
		console.log(`✅ 已載入 ${module} 模組翻譯: ${locale}`);
	}
	catch (e) {
		console.warn(`⚠️ 載入 ${module} 模組翻譯: ${locale} 失敗：${e}`);
	}
	finally {
		locales[locale][module].loading = false;
	}
}

export async function setLocale(locale) {
	i18n.global.locale.value = locale;
	document.querySelector('html').setAttribute('lang', locale);
}
