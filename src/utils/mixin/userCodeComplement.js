export default {
	data() {
		return {
			userCodeRequiredLength: 0
		};
	},
	created: function () {
		this.fetchUserCodeRequiredLength();
	},
	methods: {
		async  fetchUserCodeRequiredLength() {
			var self = this;
			const ret = await self.$api.getUserCodeLengthApi();
			this.userCodeRequiredLength = ret.data ?? 0;
		},
		complementUserCode(userCode) {
			if (!userCode) return;
			return String(userCode).padStart(this.userCodeRequiredLength, '0');
		}
	}
};
