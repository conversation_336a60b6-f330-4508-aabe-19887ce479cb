import axios from 'axios';
import Swal from 'sweetalert2';
import { useLoading } from 'vue-loading-overlay';
import { getToken, removeToken, setToken } from './auth.js';
import router from '@/router/index.js';
import qs from 'qs';

// Loading effect
const $loading = useLoading({
	container: null,
	loader: 'bars',
	color: '#3459e6'
});
let loader = null;
let loadCount = 0;

const showLoading = () => {
	if (loadCount === 0 && !loader) {
		loader = $loading.show({
			container: null,
			loader: 'spinner',
			color: '#3459e6'
		});
	}
	loadCount++;
};

const hideLoading = () => {
	loadCount = Math.max(0, loadCount - 1);
	if (loadCount === 0 && loader) {
		loader.hide();
		loader = null;
	}
};

// refresh Token
let isRefreshing = false;
let refreshSubscribers = [];

function onRefreshed(newToken) {
	refreshSubscribers.forEach(cb => cb(newToken));
	refreshSubscribers = [];
}

function addRefreshSubscriber(callback) {
	refreshSubscribers.push(callback);
}

// create an axios instance
const service = axios.create({
	baseURL: import.meta.env.VITE_API_URL_V1,
	// withCredentials: true, // send cookies when cross-domain requests
	timeout: 60000, // request timeout
	method: 'post', // 預設為 post,
	paramsSerializer: params => qs.stringify(params, { arrayFormat: 'repeat', encode: false })
});

// 請求攔截器
service.interceptors.request.use(
	(config) => {
		if (config.showLoading !== false) {
			showLoading();
		}

		const token = getToken();
		if (token) {
			config.headers['Authorization'] = token;
		}

		if (config.params) {
			config.params = { ...config.params };
		}

		return config;
	},
	(error) => {
		hideLoading();
		return Promise.reject(error);
	}
);

// 回應攔截器
service.interceptors.response.use(
	/**
	 * If you want to get http information such as headers or status
	 * Please return  response => response
	 */
	async (response) => {
		hideLoading();
		let res = response.data;
		// 若返回的資料有 returnDesc，顯示該內容
		if (res.returnDesc) throw Error(res.returnDesc);
		else if (res.status && res.status !== 200) throw Error('連線逾時，請稍後再試');
		return res;
	},
	async (error) => {
		hideLoading();

		const status = error.response?.status;
		const originalRequest = error.config;

		// access_token 過期，判斷為 401
		if (error.response && status === 401 && !originalRequest._retry) {
			originalRequest._retry = true;

			if (!isRefreshing) {
				isRefreshing = true;

				try {
					const refreshToken = getToken('refreshToken');

					const res = await service.post(`/auth/refresh`, {
						refresh_token: refreshToken
					});

					setToken('accessToken', res.accessToken);
					setToken('refreshToken', res.refreshToken);

					onRefreshed(res.accessToken);

					// 將原請求補上新 token 並重試
					originalRequest.headers['Authorization'] = res.accessToken;

					return service(originalRequest);
				}
				catch (refreshError) {
					removeToken('accessToken');
					removeToken('refreshToken');

					const result = await Swal.fire({
						icon: 'warning',
						title: '登入過期',
						text: '請重新登入'
					});

					if (result.isConfirmed) {
						router.push('/login');
					}

					return Promise.reject(refreshError);
				}
				finally {
					isRefreshing = false;
				}
			}

			// 當正在刷新時，等待結果並處理
			return new Promise((resolve, reject) => {
				addRefreshSubscriber((newToken) => {
					// 如果刷新成功，使用新的 token 重試請求
					originalRequest.headers['Authorization'] = newToken;
					resolve(service(originalRequest));
				});

				// 如果刷新失敗，觸發錯誤並拒絕 Promise
				addRefreshSubscriber((error) => {
					reject(error);
				});
			});
		}
		else if (status === 403) {
			// 權限錯誤
			await Swal.fire({
				icon: 'error',
				title: '權限不足',
				text: '您沒有操作此功能的權限'
			});
			// 不跳登入，保留在原頁面
			return Promise.reject(error);
		}
		else if (status >= 500) {
			// 一般錯誤
			// await Swal.fire({
			// 	icon: 'error',
			// 	title: '系統錯誤',
			// 	text: '伺服器發生錯誤，請稍後再試'
			// });
			return Promise.reject(error);
		}
		else {
			// 其他錯誤
			// await Swal.fire({
			// 	icon: 'error',
			// 	title: '錯誤',
			// 	text: '請求失敗'
			// });
			return Promise.reject(error);
		}
	}
);

export default service;
