import _ from 'lodash';
import moment from 'moment';

/**
 * Lodash Extension.
 */
_.mixin({
	/**
	 * 驗證是否為數字，包含有小數及字串數字
	 * @param value
	 * @returns {boolean}
	 */
	isNumeric: function (value) {
		if (_.isString(value) && _.isBlank(value)) {
			return false;
		}
		else if (_.isNil(value)) {
			return false;
		}

		return !_.isNaN(_.toNumber(value));
	},
	/**
	 * 驗證是否為空值
	 * @param value
	 * @returns {boolean|*}
	 */
	isBlank: function (value) {
		return _.isNil(value) || (_.isString(value) && _.trim(value).length === 0);
	},
	/**
	 * 轉換中文數值
	 *
	 * @param value
	 * @param precision
	 * @returns {string|number}
	 */
	formatTwUnit: function (value, precision) {
		if (!_.isNumeric(value)) {
			return NaN;
		}

		var newValue = ['', '', ''];
		var fr = 1000;
		var num = 3;
		while (value / fr >= 1) {
			fr *= 10;
			num += 1;
		}
		if (num <= 4) {
			newValue[1] = '千';
			newValue[0] = _.round(parseInt(value / 1000), precision) + '';
		}
		else if (num <= 8) {
			// 萬
			var text1 = parseInt(num - 4) / 3 > 1 ? '千萬' : '萬';
			var fm = '萬' === text1 ? 10000 : 10000000;
			newValue[1] = text1;
			newValue[0] = _.round(value / fm, precision) + '';
		}
		else if (num <= 16) {
			var text1 = (num - 8) / 3 > 1 ? '千億' : '億';
			text1 = (num - 8) / 4 > 1 ? '萬億' : text1;
			text1 = (num - 8) / 7 > 1 ? '千萬億' : text1;
			var fm = 1;
			if ('億' === text1) {
				fm = 100000000;
			}
			else if ('千億' === text1) {
				fm = 100000000000;
			}
			else if ('萬億' === text1) {
				fm = 1000000000000;
			}
			else if ('千萬億' === text1) {
				fm = 1000000000000000;
			}
			newValue[1] = text1;
			newValue[0] = _.round(parseInt(value / fm), precision) + '';
		}
		if (value < 1000) {
			newValue[1] = '';
			newValue[0] = _.round(value, precision) + '';
		}
		return newValue.join('');
	},
	/**
	 * 驗證是否為正數
	 *
	 * @param value
	 * @returns {boolean|number}
	 */
	isPositive: function (value) {
		var regex = /^\d+(\.\d+)?$/;
		return regex.test(value);
	},
	formatDate: function (value) {
		if (!value) {
			return undefined;
		}
		if (value.length === 10) {
			if (value.indexOf('/') !== 0) {
				return moment(value, 'YYYY/MM/DD').format('YYYY/MM/DD');
			}
			else if (value.indexOf('-') !== 0) {
				return moment(value, 'YYYY-MM-DD').format('YYYY-MM-DD');
			}
		}
		var mom = moment(value);
		if (mom.isValid()) {
			return mom.format('YYYY/MM/DD');
		}
		return 'Date Error';
	},
	handleWkfResp: function (ret, ignoreSuccessMsg) {
		var resp;
		if (ret.data) {
			resp = ret.data;
		}
		else {
			this.$bi.alert('處裡審核結果失敗。');
			return false;
		}

		if (resp.executeResult == 'F') {
			if (resp.returnCode == '1004') {
				this.$bi.alert('無執行審核權限。');
			}
			else if (resp.message) {
				this.$bi.alert('送出審核失敗。<br>' + resp.message);
			}
			return false;
		}

		if (ignoreSuccessMsg) {
			return true;
		}
		else if (resp.message) {
			this.$bi.alert('提交審核成功。<br>' + resp.message);
			return true;
		}
		else {
			this.$bi.alert('提交審核成功。');
			return true;
		}
	},
	convertUrlSpecChar(value) {
		if (_.isBlank(value)) {
			return '';
		}

		if (value.includes('&')) {
			value = value.replaceAll('&', '%26');
		}

		if (value.includes('+')) {
			value = value.replaceAll('&', '%2B');
		}

		if (value.includes('/')) {
			value = value.replaceAll('&', '%2F');
		}

		if (value.includes('?')) {
			value = value.replaceAll('&', '%3F');
		}

		if (value.includes('#')) {
			value = value.replaceAll('&', '%23');
		}

		if (value.includes('=')) {
			value = value.replaceAll('&', '%3D');
		}

		return value;
	},
	esbIdCheck: function (idNumber) {
		// 外國法人
		var foreignLegalPersonRule1 = /^&[\s/0-9A-Z]{1,9}$/;
		var foreignLegalPersonRule2 = /^&&[/0-9A-Z]{8}$/;
		var foreignLegalPersonRule3 = /^&[C|F|G]{1}[0-9]{8}$/;
		var foreignLegalPersonRule4 = /^[0-9]{8}[A-Z]{1}$/;
		// 外國自然人
		var foreignNaturalPersonRule1 = /^[A-Z]{1}[A-Z]{1}[0-9]{8}$/;
		var foreignNaturalPersonRule2 = /^[A-Z]{1}[8-9]{1}[0-9]{8}$/;
		var foreignNaturalPersonRule3
			= /^(2[0-9]{3}|19[0-9]{2}|18[6-9]{1}[0-9]{1}|185[4-9]{1})(1[0-2]{1}|0[1-9]{1})(0[1-9]{1}|[1-2]{1}[0-9]{1}|3[0-1]{1})([A-Z]{2})$/;
		var foreignNaturalPersonRule4 = /^&I[0-9]{8}$/;
		var foreignNaturalPersonRule5 = /^[a-zA-Z]{1}[0-9]{8}[a-zA-Z]{1}$/;

		var foreignNaturalPersonRule6 = /^HKC[0-9]{6}$/;

		if (foreignLegalPersonRule1.test(idNumber)) {
			return true;
		}

		if (foreignLegalPersonRule2.test(idNumber)) {
			return true;
		}

		if (foreignLegalPersonRule3.test(idNumber)) {
			return true;
		}

		if (foreignLegalPersonRule4.test(idNumber)) {
			return true;
		}

		if (foreignNaturalPersonRule1.test(idNumber)) {
			return true;
		}

		if (foreignNaturalPersonRule2.test(idNumber)) {
			return true;
		}

		if (foreignNaturalPersonRule3.test(idNumber)) {
			if (idNumber.substring(0, 4) > 1853) {
				return true;
			}
			return false;
		}

		if (foreignNaturalPersonRule4.test(idNumber)) {
			return true;
		}

		if (foreignNaturalPersonRule5.test(idNumber)) {
			return true;
		}

		if (foreignNaturalPersonRule6.test(idNumber)) {
			return true;
		}

		if (this.tWIDCheck(idNumber) == true) {
			return true;
		}
		return false;
	},
	tWIDCheck: function (idNumber) {
		if (!idNumber) {
			return false;
		}

		// 本國人
		if (idNumber.length == 10) {
			idNumber = idNumber.toUpperCase();
			// 驗證填入身分證字號長度及格式
			if (idNumber.length != 10) {
				return false;
			}
			// 格式，用正則表示式比對第一個字母是否為英文字母
			if (isNaN(idNumber.substr(1, 9)) || !/^[A-Z]$/.test(idNumber.substr(0, 1))) {
				return false;
			}

			var idHeader = 'ABCDEFGHJKLMNPQRSTUVXYWZIO'; // 按照轉換後權數的大小進行排序
			// 這邊把身分證字號轉換成準備要對應的
			idNumber = idHeader.indexOf(idNumber.substring(0, 1)) + 10 + '' + idNumber.substr(1, 9);
			// 開始進行身分證數字的相乘與累加，依照順序乘上1987654321
			var s
				= parseInt(idNumber.substr(0, 1))
					+ parseInt(idNumber.substr(1, 1)) * 9
					+ parseInt(idNumber.substr(2, 1)) * 8
					+ parseInt(idNumber.substr(3, 1)) * 7
					+ parseInt(idNumber.substr(4, 1)) * 6
					+ parseInt(idNumber.substr(5, 1)) * 5
					+ parseInt(idNumber.substr(6, 1)) * 4
					+ parseInt(idNumber.substr(7, 1)) * 3
					+ parseInt(idNumber.substr(8, 1)) * 2
					+ parseInt(idNumber.substr(9, 1));

			var checkNum = parseInt(idNumber.substr(10, 1));
			// 模數 - 總和/模數(10)之餘數若等於第九碼的檢查碼，則驗證成功
			// 若餘數為0，檢查碼就是0
			if (s % 10 == 0 || 10 - (s % 10) == checkNum) {
				return true;
			}
			else {
				return false;
			}
		}
		// 統一編號
		else if (idNumber.length == 8) {
			var cx = [1, 2, 1, 2, 1, 2, 4, 1];
			var cnum = idNumber.split('');
			let sum = 0;
			function cc(num) {
				let total = num;
				if (total > 9) {
					let s = total.toString();
					var n1 = s.substring(0, 1) * 1;
					var n2 = s.substring(1, 2) * 1;
					total = n1 + n2;
				}
				return total;
			}
			if (idNumber.length !== 8) {
				return false;
			}
			cnum.forEach((item, index) => {
				if (idNumber.charCodeAt() < 48 || idNumber.charCodeAt() > 57) {
					return false;
				}
				sum += cc(item * cx[index]);
			});

			if (sum != 0 && sum % 5 === 0) {
				return true;
			}
			else if (cnum[6] === '7' && (sum + 1) % 5 === 0) {
				return true;
			}
			else {
				return false;
			}
		}
		else {
			return false;
		}
	},
	// 自然人ID檢查
	tWNaturalIDCheck: function (idNumber) {
		if (!idNumber) {
			return false;
		}

		// 本國人
		if (idNumber.length == 10) {
			idNumber = idNumber.toUpperCase();
			// 驗證填入身分證字號長度及格式
			if (idNumber.length != 10) {
				return false;
			}
			// 格式，用正則表示式比對第一個字母是否為英文字母
			if (isNaN(idNumber.substr(1, 9)) || !/^[A-Z]$/.test(idNumber.substr(0, 1))) {
				return false;
			}

			var idHeader = 'ABCDEFGHJKLMNPQRSTUVXYWZIO'; // 按照轉換後權數的大小進行排序
			// 這邊把身分證字號轉換成準備要對應的
			idNumber = idHeader.indexOf(idNumber.substring(0, 1)) + 10 + '' + idNumber.substr(1, 9);
			// 開始進行身分證數字的相乘與累加，依照順序乘上1987654321
			var s
				= parseInt(idNumber.substr(0, 1))
					+ parseInt(idNumber.substr(1, 1)) * 9
					+ parseInt(idNumber.substr(2, 1)) * 8
					+ parseInt(idNumber.substr(3, 1)) * 7
					+ parseInt(idNumber.substr(4, 1)) * 6
					+ parseInt(idNumber.substr(5, 1)) * 5
					+ parseInt(idNumber.substr(6, 1)) * 4
					+ parseInt(idNumber.substr(7, 1)) * 3
					+ parseInt(idNumber.substr(8, 1)) * 2
					+ parseInt(idNumber.substr(9, 1));

			var checkNum = parseInt(idNumber.substr(10, 1));
			// 模數 - 總和/模數(10)之餘數若等於第九碼的檢查碼，則驗證成功
			// 若餘數為0，檢查碼就是0
			if (s % 10 == 0 || 10 - (s % 10) == checkNum) {
				return true;
			}
			else {
				return false;
			}
		}
		else {
			return false;
		}
	},

	/**
	 * 取得陣列裡特定屬性的最大值
	 */
	maxValue: function (items, name) {
		var maxItem = _.maxBy(items, name);
		return !_.isNil(maxItem) ? maxItem[name] : '--';
	},

	/**
	 * 取得真實資產代碼
	 * @param value
	 * @returns {null|*}
	 */
	getRealAssetCode: function (value) {
		if (value && value !== '11000006' && value !== '11000000') {
			return value;
		}
		else {
			return null;
		}
	},
	/**
	 * 轉換網址增加分頁參數
	 * @deprecated 請改寫使用 pagination.js/paginationToParam, 可參考 $api.getBbsMgtPageData
	 * @param value
	 * @param page
	 * @param pageable
	 * @returns {String}
	 */
	toPageUrl: function (value, page, pageable) {
		var page = _.isNumber(page) ? page : pageable.page;
		var url = value;
		url += '?page=' + page + '&size=' + pageable.size;
		url += '&sort=' + pageable.sort + ',' + pageable.direction;
		return url;
	},
	/**
	 * Run all the tasks no matter any error occurs or not
	 * @param {Function[]} tasks
	 * @param {number} batchSize
	 * @returns {Promise<{status:'fulfilled'|'rejected',value?:any,reason?:any}[]>}
	 */
	batchCallback: async (tasks = [], batchSize = 4) => {
		const results = [];
		const executing = new Set();
		for (const task of tasks) {
			const promise = Promise.resolve()
				.then(task)
				.then(result => ({ status: 'fulfilled', value: result }))
				.catch(error => ({ status: 'rejected', reason: error }))
				.finally(() => {
					executing.delete(promise);
				});
			executing.add(promise);
			results.push(promise);
			if (executing.size >= batchSize) {
				await Promise.race(executing);
			}
		}
		await Promise.all(executing);
		return Promise.all(results);
	}
});
