$(function () {
	'use strict';

	// 左側主選單   aside-fixed
	if ($('.aside-backdrop').length === 0) {
		$('body').append('<div class="aside-backdrop"></div>');
	}

	$('.aside-menu-link').on('click', function (e) {
		e.preventDefault();
		if (window.matchMedia('(min-width:992px)').matches) {
			$('.aside-fixed').toggleClass('minimize');
		}
		else {
			$('body').toggleClass('show-aside');
		}
	});

	$(window).on('resize', function () {
		var width = $(window).width();

		if (width < 992) {
			$('.aside-fixed').removeClass('minimize');
		}
	});

	$('.nav-aside .with-sub').on('click', '.nav-link', function (e) {
		e.preventDefault();
		$(this).parent().siblings().removeClass('show');
		$(this).parent().toggleClass('show');
	});

	$('body').on('mouseenter', '.minimize .aside-body', function (e) {
		console.log('e');
		$(this).parent().addClass('maximize');
	});

	$('body').on('mouseleave', '.minimize .aside-body', function (e) {
		$(this).parent().removeClass('maximize');
	});

	$('body').on('click', '.aside-backdrop', function (e) {
		$('body').removeClass('show-aside');
	});

	/// ///////工作臺選單 NAVBAR //////////

	// Showing sub-menu of active menu on navbar when mobile
	function showNavbarActiveSub() {
		if (window.matchMedia('(max-width: 991px)').matches) {
			$('#navbarMenu .active').addClass('show');
		}
		else {
			$('#navbarMenu .active').removeClass('show');
		}
	}

	showNavbarActiveSub();
	$(window).resize(function () {
		showNavbarActiveSub();
	});

	// Initialize backdrop for overlay purpose
	$('body').append('<div class="backdrop"></div>');

	$('#mainMenuOpen').on('click touchstart', function (e) {
		e.preventDefault();
		$('body').addClass('navbar-nav-show');
	});

	$('#mainMenuClose').on('click', function (e) {
		e.preventDefault();
		$('body').removeClass('navbar-nav-show');
	});

	// Showing sub menu of navbar menu while hiding other siblings
	$('.navbar-menu .with-sub .nav-link').on('click', function (e) {
		e.preventDefault();
		$(this).parent().toggleClass('show');
		$(this).parent().siblings().removeClass('show');
	});

	/// //////////////// 客戶列表
	$('#navclose').on('click', function (e) {
		e.preventDefault();
		$('body').toggleClass('navcolse');
	});

	// 客戶總覽選單   filemgr-sidebar
	$('#filemgrMenuclose').on('click', function (e) {
		e.preventDefault();
		$('.filemgr-wrapper').toggleClass('filemgr-sidebar-close');
		$(this).toggleClass('close');
	});

	$('.sidebar-nav .with-sub').on('click', function (e) {
		e.preventDefault();
		$(this).parent().toggleClass('show');
	});

	// 使用於上選單客戶快速搜尋 Navbar Search
	$('#navbarSearch').on('click', function (e) {
		e.preventDefault();
		$('.navbar-search').addClass('visible');
		$('.backdrop').addClass('show');
	});

	$('#navbarSearchClose').on('click', function (e) {
		e.preventDefault();
		$('.navbar-search').removeClass('visible');
		$('.backdrop').removeClass('show');
	});
});
