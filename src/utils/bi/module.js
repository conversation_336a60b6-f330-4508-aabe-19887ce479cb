/**
 * Copyright (c) 2025 - SoftBI Corporation Limited.
 * All rights reserved.
 *
 * <AUTHOR> @since 1.0.0
 * @Description remove jQuery
 */

import { _ } from 'lodash';
import Swal from 'sweetalert2';

export const biModule = {
	/**
	 * Popup alert modal
	 *
	 * @param message
	 * @param option
	 */
	alert: async function (message, option) {
		const success = message.includes('成功') || message.includes('完成');
		const result = await Swal.fire({
			html: message,
			icon: option?.success ? 'success' : success ? 'success' : 'error',
			showCloseButton: true,
			confirmButtonText: '確認',
			buttonsStyling: false,
			customClass: {
				confirmButton: 'btn btn-primary'
			}
		});

		if (result.isConfirmed) {
			if (_.isFunction(option)) {
				option();
			}
			else if (!_.isEmpty(option)) {
				option.confirm();
			}
		}
		return result;
	},

	/**
	 * Popup confirm modal
	 *
	 * @param message
	 * @param option
	 */
	confirm: async function (message, option) {
		const result = await Swal.fire({
			icon: 'warning',
			text: message,
			showCloseButton: true,
			showCancelButton: true,
			confirmButtonText: '確認',
			cancelButtonText: '取消',
			reverseButtons: true,
			buttonsStyling: false,
			customClass: {
				confirmButton: 'btn btn-primary',
				cancelButton: 'btn btn-white'
			}
		});

		if (!_.isEmpty(option)) {
			if (result.isConfirmed && option.event.confirmOk) {
				option.event.confirmOk.call();
			}
			else if (result.dismiss === Swal.DismissReason.cancel && option.event.confirmCancel) {
				option.event.confirmCancel.call();
			}
		}
		return result;
	},

	/**
	 * Popup message modal
	 *
	 * @param message
	 * @param option
	 */
	message: function (message, option) {
		const success = message.includes('成功');
		Swal.fire(
			_.assign(
				{
					title: message,
					icon: success ? 'success' : 'error',
					showConfirmButton: false,
					timer: 1500
				},
				option
			)
		);
	},

	// Popup save success message
	saveSuccessMessage: () => biModule.message('儲存成功')
};
