import _ from 'lodash';
import numeral from 'numeral';
import moment from 'moment';

// 金額格式化過濾器
export const formatAmt = (value, format) => {
	if (_.isNil(value)) {
		value = 0;
	}
	if (!_.isNumber(value) && !_.isString(value)) {
		return 'NaN';
	}
	return numeral(value).format(format || '0,0.00');
};

export const formatAmtPh = (value, format) => {
	if (_.isNil(value)) {
		return '---';
	}
	if (!_.isNumber(value) && !_.isString(value)) {
		return 'NaN';
	}
	return numeral(value).format(format || '0,0.00');
};

export const formatNumber = (value, format) => {
	if (_.isNil(value)) {
		return '--';
	}
	if (!_.isNumber(value) && !_.isString(value)) {
		return 'NaN';
	}
	return numeral(value).format(format || '0,0.[00]');
};

export const formatNumberZero = (value, format) => {
	if (_.isNil(value)) {
		value = 0;
	}
	if (!_.isNumber(value) && !_.isString(value)) {
		return 'NaN';
	}
	return numeral(value).format(format || '0,0.[00]');
};

export const formatNum = (value, format) => {
	if (_.isNil(value)) {
		return '--';
	}
	if (!_.isNumber(value) && !_.isString(value)) {
		return 'NaN';
	}
	return numeral(value).format(format || '0');
};

// 日期格式化過濾器
export const formatDate = (value) => {
	if (!value) return null;
	let momentObject;
	if (value.length === 10) {
		if (value.indexOf('/') !== -1) momentObject = moment(value, 'YYYY/MM/DD');
		else if (value.indexOf('-') !== -1) momentObject = moment(value, 'YYYY-MM-DD');
	}
	momentObject ??= moment(value);
	if (momentObject?.isValid()) return momentObject.format('YYYY-MM-DD');
	throw Error(`formatDate parse Error, input: ${value}`);
};

export const formatExpireDate = (value) => {
	if (!value) {
		return '';
	}
	if (value.length === 10) {
		if (value.indexOf('/') !== -1) {
			const formatted = moment(value, 'YYYY/MM/DD').format('YYYY/MM/DD');
			return formatted === '9999/10/29' ? '9999/12/31' : formatted;
		}
		else if (value.indexOf('-') !== -1) {
			const formatted = moment(value, 'YYYY-MM-DD').format('YYYY/MM/DD');
			return formatted === '9999/10/29' ? '9999/12/31' : formatted;
		}
	}
	const mom = moment(value);
	if (mom.isValid()) {
		const formatted = mom.format('YYYY/MM/DD');
		return formatted === '9999/10/29' ? '9999/12/31' : formatted;
	}
	return 'Date Error';
};

export const formatDatePh = (value) => {
	if (!value) {
		return '--';
	}
	return formatDate(value);
};

export const formatKycDate = (value) => {
	if (!value || value === '00000000') {
		return '00000000';
	}
	if (value.length === 10) {
		if (value.indexOf('/') !== -1) {
			return moment(value, 'YYYY/MM/DD').format('YYYYMMDD');
		}
		else if (value.indexOf('-') !== -1) {
			return moment(value, 'YYYY-MM-DD').format('YYYYMMDD');
		}
	}
	const mom = moment(value);
	if (mom.isValid()) {
		return mom.format('YYYYMMDD');
	}
	return 'Date Error';
};

export const formatDateTime = (value) => {
	if (!value) {
		return '';
	}
	let mom;
	if (value.indexOf('/') !== -1) {
		mom = moment(value, 'YYYY/MM/DD HH:mm:ss');
	}
	else if (value.indexOf('-') !== -1) {
		mom = moment(value, 'YYYY-MM-DD HH:mm:ss');
	}
	else {
		mom = moment(value);
	}

	if (mom.isValid()) {
		return mom.format('YYYY/MM/DD HH:mm:ss');
	}
	return 'DateTime Error';
};

export const formatToMinute = (value) => {
	if (!value) {
		return '';
	}
	let mom;
	if (value.indexOf('/') !== -1) {
		mom = moment(value, 'YYYY/MM/DD HH:mm');
	}
	else if (value.indexOf('-') !== -1) {
		mom = moment(value, 'YYYY-MM-DD HH:mm');
	}
	else {
		mom = moment(value);
	}

	if (mom.isValid()) {
		return mom.format('YYYY/MM/DD HH:mm');
	}
	return 'DateTime Error';
};

export const formatDateTimeFromNow = (value) => {
	if (!value) {
		return '';
	}
	const mom = moment(value);
	if (mom.isValid()) {
		return mom.fromNow();
	}
	return 'DateTime Error';
};

// 百分比格式化
export const formatPct = (value) => {
	if (_.isNil(value)) {
		value = 0;
	}
	if (!_.isNumber(value)) {
		return 'NaN';
	}
	return numeral(value * 100).format('0.00');
};

// 後綴格式化
export const formatSuffix = (value, suffix) => {
	if (_.isNaN(value) || _.isNil(value)) {
		return value;
	}
	return value + suffix;
};

// 數字轉化大寫字母
export const toUpperCaseLetter = (number) => {
	if (!_.isInteger(number)) {
		return '';
	}
	return String.fromCharCode('A'.charCodeAt(0) + number);
};

// 電話號碼格式化
export const formatPhoneNumber = (number) => {
	if (!number) return '';
	return number.replace(/(\d{4})\-?(\d{3})\-?(\d{3})/, '$1-$2-$3');
};

export const maskPhoneNumber = (number) => {
	if (!number) return '';
	number = _.trim(number);
	number = _.replace(number, /-/g, '');
	number = _.replace(number, /^(\d{4})(\d*)(\d{3})$/, '$1-***-$3');
	return number;
};

// 默認值處理
export const defaultValue = (value, defaultVal) => {
	if (value === undefined || value === null) {
		return defaultVal;
	}

	if (typeof value === 'number') {
		if (isNaN(value) || !isFinite(value)) {
			return defaultVal;
		}
		return value;
	}

	if (typeof value === 'string') {
		value = value.trim();
		if (value === '') {
			return defaultVal;
		}
		if (!isNaN(Number(value))) {
			return Number(value);
		}
		return value;
	}

	if (Array.isArray(value) || (typeof value === 'object' && value !== null)) {
		return Object.keys(value).length === 0 ? defaultVal : value;
	}

	return value;
};

// 排序圖標類名
export const clsSort = (value, pageable) => {
	if (_.toUpper(value) !== _.toUpper(pageable.sort)) {
		return 'fas fa-sort';
	}
	else if (_.toUpper(pageable.direction) === 'ASC') {
		return 'fas fa-caret-up';
	}
	else {
		return 'fas fa-caret-down';
	}
};

// 紅利格式化 （四捨五入）
export const formatDividend = (value, format) => {
	if (_.isNil(value)) {
		return '--';
	}
	if (!_.isNumber(value) && !_.isString(value)) {
		return 'NaN';
	}

	// 處理負數
	if (value < 0) {
		const absValue = Math.abs(value);
		return '-' + numeral(absValue).format(format || '0,0.00');
	}
	else {
		return numeral(value).format(format || '0,0.00');
	}
};

//  貨幣金額格式化 （無條件捨去）
export const formatCurAmt = (value, precision = 2, customFormat) => {
	if (_.isNil(value)) {
		return '--';
	}
	if (!_.isNumber(value) && !_.isString(value)) {
		return 'NaN';
	}

	let format = '0,0.00'; // 默認顯示到小數點最後兩位

	if (precision === 0) {
		format = '0,0'; // 整數顯示
	}
	if (customFormat) {
		format = customFormat;
	}

	// 無條件捨去處理
	const pow = Math.pow(10, precision);
	const truncatedValue = Math.floor((value * pow)) / pow;

	return numeral(truncatedValue).format(format);
};

// 特殊金額格式化
export const formatSpecialAmt = (value, precision = 2, format, isHalfUp) => {
	if (_.isNil(value)) {
		return '--';
	}
	if (!_.isNumber(value) && !_.isString(value)) {
		return 'NaN';
	}

	if (!format) {
		format = '0,0.00';
	}

	if (precision === 0) {
		value = parseFloat(value).toFixed(0);
	}

	// 四捨五入處理
	if (isHalfUp) {
		if (value < 0) {
			const absValue = Math.abs(value);
			return '-' + numeral(absValue).format(format);
		}
		else {
			return numeral(value).format(format);
		}
	}

	// 無條件捨去處理
	const formatParts = format.split('.');
	const decimalPlaces = formatParts.length > 1 ? formatParts[1].length : 2;
	const pow = Math.pow(10, decimalPlaces);
	const truncatedValue = Math.floor((value * pow)) / pow;

	return numeral(truncatedValue).format(format);
};

// 波動顯示（帶顔色）
export const formatFlucWithView = (value, suffix) => {
	if (!_.isNumber(value)) {
		return 'NaN';
	}

	let formatValue;
	if (value > 0.01 || value <= -0.01) {
		formatValue = numeral(value).format('0,0.[00]');
	}
	else {
		formatValue = numeral(value).format('0,0.[00000]');
	}

	if (suffix) {
		formatValue = formatValue + suffix;
	}

	const colorClass = value > 0 ? 'font-Rise' : 'font-Fall';
	return `<span class="${colorClass}">${formatValue}</span>`;
};

// 臺幣百萬單位
export const formatTwMillionUnit = (value) => {
	if (_.isNumber(value)) {
		value = value / 1000000;
		if (value > 0.01) {
			value = _.round(value, 2);
		}
		else if (value > 0.0001) {
			value = _.round(value, 4);
		}
		else if (value > 0.000001) {
			value = _.round(value, 6);
		}
		return numeral(value).format('0,0.[00000000]') + '百萬';
	}
	else {
		return '--';
	}
};

// 數組求和
export const sumTotal = (arrays, sumName) => {
	return arrays.reduce((acc, item) => {
		const value = item[sumName];
		if (_.isNil(value) || !_.isNumber(value)) {
			return acc;
		}
		return acc + value;
	}, 0);
};

// 外匯匯率轉換
export const formatFxRate = (value, fxRate) => {
	if (_.isNil(value)) {
		return null;
	}
	return value / fxRate;
};

// 年月格式化 YYYYMM -> YYYY年MM月
export const formatYearMonth = (value) => {
	if (!value) {
		return '';
	}

	if (value.length >= 6) {
		const year = value.substring(0, 4);
		let month = value.substring(4);

		if (month.length === 1) {
			month = '0' + month;
		}

		return year + '年' + month + '月';
	}

	return value;
};

// 格式化 YYYY-MM
export const formatYearMonthOnly = (value) => {
	if (!value) return '';

	const mom = moment(value);
	if (mom.isValid()) {
		return mom.format('YYYY-MM');
	}
	return 'Date Error';
};

// 數字本地化
export const toIntLocaleString = (number) => {
	return Math.round(number).toLocaleString();
};

// 導出所有過濾器作爲對象，方便批量使用
export const filters = {
	formatAmt,
	formatAmtPh,
	formatNumber,
	formatNumberZero,
	formatNum,
	formatDate,
	formatExpireDate,
	formatDatePh,
	formatKycDate,
	formatDateTime,
	formatToMinute,
	formatDateTimeFromNow,
	formatPct,
	formatSuffix,
	toUpperCaseLetter,
	formatPhoneNumber,
	maskPhoneNumber,
	defaultValue,
	clsSort,
	formatDividend,
	formatCurAmt,
	formatSpecialAmt,
	formatFlucWithView,
	formatTwMillionUnit,
	sumTotal,
	formatFxRate,
	formatYearMonth,
	formatYearMonthOnly,
	toIntLocaleString
};

export default filters;
