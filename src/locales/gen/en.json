{"add": "Add", "additionalDocuments": "Additional Documents", "addSuccess": "Add successful", "all": "All", "announcementDate": "Announcement Date", "announcementQuery": "Announcement Query", "announcementSettings": "Announcement Settings", "announcementTitle": "Announcement Title", "applicationDetails": "Application Details", "apply": "Application Details", "approved": "Approved", "archived": "Archived", "assetCategory": "Asset Category", "attachment": "Attachment", "attachmentFile": "Attachment File", "author": "Author", "back": "Back", "bbsCat": "Bulletin Category", "bbsCategory": "Bulletin Category", "bbsContent": "Bulletin Content", "bbsHead": "Bulletin Title", "bbsMessage": "Bulletin Message", "bbsMgmt": "Bulletin Management", "bbsMsg": "Bulletin Message", "bbsTitle": "Bulletin Title", "bothStartEndDateRequired": "Both start and end dates are required", "cancel": "Cancel", "cancelModify": "Cancel Modification", "cannotAddSameCategoryName": "Cannot add category with same name", "cannotAddSameMainCategoryName": "Cannot add main category with same name", "cannotAddSameSubCategoryName": "Cannot add sub category with same name", "cannotDeleteMainCategoryWithMessages": "Cannot delete main category that has messages.", "cannotDeleteMainCategoryWithSubcategories": "Cannot delete main category that has subcategories.", "cannotDeleteSubCategoryWithMessages": "Cannot delete sub category that has messages.", "cannotModifyPendingReview": "Cannot modify pending review announcement.", "canProvideToCustomer": "Can Provide to Customer", "canProvideToCustomers": "Can Provide to Customers", "canPurchase": "Can Purchase", "cat": "Bulletin Category", "category": "Category", "categoryHierarchy": "Category Hierarchy", "categoryList": "Category List", "categoryName": "Category Name", "charactersAvailable": "characters available", "chineseName": "Chinese Name", "close": "Close", "closeWindow": "Close Window", "code": "Code", "complete": "Completed", "confirm": "Confirm", "confirmDelete": "Confirm Delete", "confirmDeleteData": "Are you sure you want to delete this data?", "confirmDeleteDocument": "Do you want to delete this document?", "confirmDeleteInstitution": "Do you want to delete this institution?", "confirmDeleteProduct": "Do you want to delete this product?", "content": "Bulletin Content", "createDate": "Create Date", "creator": "Creator", "creatorExt": "Creator Extension", "date": "Date", "dateRangeStartCannotBeGreater": "Start date cannot be greater than end date", "delete": "Delete", "deleteSuccess": "Delete successful", "desc": "Description", "disable": "Disabled", "docIssuers": "Issuer Information", "docMgmt": "Document Management", "docNews": "News Documents", "docOutlook": "Market Outlook", "docPro": "Product Documents", "docResearch": "Research Reports", "docSales": "Sales Documents", "documentCategory": "Document Category", "documentContent": "Document Content", "documentList": "Document List", "documentMainCategory": "Document Main Category", "documentMaintenance": "Document Maintenance", "documentSearch": "Document Search", "documentSubCategory": "Document Sub Category", "documentTitle": "Document Title", "documentType": "Document Type", "documentTypeRequired": "Document type is required", "documentView": "Document View", "download": "Download", "draft": "Draft", "edit": "Edit", "editSuccess": "Edit successful", "effectiveDate": "Effective Date", "enable": "Enabled", "englishName": "English Name", "error": "Error", "execute": "Execute", "expiryDate": "Expiry Date", "expiryDateCannotBeEarlier": "Expiry date cannot be earlier than effective date", "expiryDateFromTo": "Expiry Date From/To", "fail": "Failed", "from": "From", "func": "Function", "fuzzySearchNote": "[Product Name] and [Product Code] use fuzzy search.", "genMgmt": "General Management", "help": "Other Instructions", "importance": "Importance", "info": "Information", "internalUseOnly": "Internal Use Only", "invalid": "Invalid", "isExpired": "Is Expired", "issuer": "Issuer", "issuerChineseName": "Issuer Chinese Name", "issuerCode": "Issuer Code", "issuerList": "Issuer List", "issuerName": "Issuer Name", "issuerSearch": "Issuer Search", "keyword": "Keyword", "lastMaintainer": "Last Maintainer", "lastMaintenanceDate": "Last Maintenance Date", "link": "Link", "listReview": "Pending Review List", "mainCategory": "Main Category", "maintainer": "Maintainer", "maintainerExt": "Maintainer Extension", "maintenanceDate": "Maintenance Date", "maintenanceInfo": "Maintenance Information", "messageType": "Message Type", "modify": "Modify", "msg": "Bulletin Message", "msgCategorySettings": "Message Category Settings", "msgList": "Message List", "msgMgmt": "Message Management", "name": "Name", "newsMgmt": "News Management", "no": "No", "noDataToReview": "No data to review.", "noPermission": "No permission, please contact system administrator!", "notDistinguished": "Not Distinguished", "notice": "Notice", "notificationTarget": "Notification Target", "op": "Actions", "open": "Open", "optional": "Optional", "otherNotes": "Other Notes", "parentMainCategory": "Parent Main Category", "pending": "Pending", "pleaseEnter": "Please Enter", "pleaseEnterRejectReason": "Please enter rejection reason.", "pleaseSelect": "Please Select", "pleaseSelectInstitution": "Please select institution", "pleaseSelectInvestmentTargetOrRegion": "Please select investment target or region", "pleaseSelectMainSubCategory": "Please select main/sub category", "pleaseSelectNotificationTarget": "Please select notification target (at least one)", "pleaseSelectProduct": "Please select product", "pleaseSelectRelatedSettings": "Please select related settings (at least one)", "pleaseSelectResearchCategory": "Please select research report main/sub category", "preview": "Preview", "productCode": "Product Code", "productCodeName": "Product Name / Product Code:", "productCurrency": "Product Currency", "productExpiryDate": "Product Expiry Date", "productMainCategory": "Product Main Category", "productName": "Product Name", "productQuickSearch": "Product Quick Search", "productSubCategory": "Product Sub Category", "publishDate": "Publish Date", "published": "Published", "publisher": "Publisher", "query": "Query", "rejected": "Rejected", "relatedAttachments": "Related Attachments", "relatedProducts": "Related Products", "relatedSettings": "Related Settings", "remark": "Remarks", "required": "Required", "requiredFields": "Required <PERSON>", "reviewComplete": "Review Complete", "reviewCompleted": "Review completed.", "reviewList": "Pending Review List", "reviewStatus": "Review Status", "riskAttribute": "Risk Attribute", "save": "Save", "search": "Search", "searchCond": "Search Criteria", "searchConditions": "Search Conditions", "searchResults": "Search Results", "select": "Select", "selectAtLeastOneFundType": "Select at least one fund type (domestic or foreign)", "selectAtLeastOneInvestmentRegion": "Select at least one investment region", "selectAtLeastOneInvestmentTarget": "Select at least one investment target", "selectAtLeastOneIssuer": "Select at least one issuer", "selectRelatedItems": "Please select related items for this document (at least one)", "showOnHomepage": "Show on Homepage", "status": "Status", "subCategory": "Sub Category", "submitDate": "Submit Date", "submitter": "Submitter", "success": "Success", "summary": "Summary", "tag": "Tag", "time": "Time", "title": "Title", "to": "To", "type": "Type", "updateSuccess": "Update successful", "upload": "Upload", "uploadFile": "Upload File", "urgencyLevel": "Urgency Level", "valid": "<PERSON><PERSON>", "validDate": "Valid Date", "view": "View", "warning": "Warning", "yes": "Yes"}