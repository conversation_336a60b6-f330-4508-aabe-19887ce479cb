{"abnormal": "Abnormal", "accountAvailability": "Account Availability:", "accountCode": "Account Code", "accountInfo": "Account Information", "accountMgmt": "Account Management", "accountName": "Account Name", "add": "Add", "addDeputySuccess": "Add deputy successful.", "addFailed": "Add failed, please confirm if duplicate entry", "addSuccess": "Successfully added {count} records", "advSearch": "Advanced Search", "all": "All", "applicationDetail": "Application Detail", "assetLevelChineseName": "Asset Level Chinese Name", "assetRange": "Asset Range", "auaLowerLimit": "AUA Lower Limit (TWD)", "auaLowerLimitNotNumber": "AUA lower limit is not a number", "auaUpperLimit": "AUA Upper Limit (TWD)", "auaUpperLimitNotNumber": "AUA upper limit is not a number", "auaUpperMustGreaterThanLower": "AUA upper limit must be greater than lower limit", "back": "Back", "batchItem": "<PERSON><PERSON>", "batchProcess": "Batch Process", "batchReRunList": "<PERSON>ch Re-run List", "batchUpload": "Batch Upload", "belongingBranch": "Belonging Branch", "belongingUnit": "Belonging Unit:", "branch": "Branch", "branchArea": "Branch Area", "branchCode": "Branch Code", "branchGroup": "Branch Group", "branchGroupCategory": "Branch Group Category", "branchGroupQuery": "Branch Group Query", "branchGroupQueryExport": "BranchGroupQuery_", "branchGroupSampleFile": "BranchGroupSetting_SampleFile.csv", "branchGroupSetting": "Branch Group Setting", "branchGroupSettingNote": "If you leave this function without clicking 'Complete' after uploading branch group settings, the list data will not be retained", "branchName": "Branch Name", "branchType": "Branch Type", "branchUnit": "Branch (Unit)", "branchUnitCode": "Branch (Unit) Code", "branchUnitName": "Branch (Unit) Name", "branchUsageRecordList": "Branch Usage Record List", "businessCategory": "Business Category", "caCode": "CA Code", "caJurisdictionBranchDataFile": "CA Jurisdiction Branch Data File", "caJurisdictionBranchQuery": "CA Jurisdiction Branch Query", "caJurisdictionBranchSetup": "CA Jurisdiction Branch Setup", "caName": "CA Name", "cancel": "Cancel", "cancelModify": "Cancel Modify", "cat": "Category", "clear": "Clear", "clearForm": "Clear", "close": "Closed", "closeWindow": "Close Window", "closed": "Closed", "code": "Code", "collapseAll": "Collapse All", "collapseAllBtn": "Collapse All", "collapseAllModal": "Collapse All", "complete": "Complete", "configTime": "Config Time", "confirm": "Confirm", "confirmBtn": "Confirm", "confirmDeleteData": "Are you sure you want to delete this data?", "confirmRerun": "Are you sure you want to re-run this group?", "content": "Content", "copy": "Copy", "count": "Count", "createDate": "Created Date", "csvFileNote": "(Please ensure the uploaded file is a CSV file)", "customerAssetLevel": "Customer Asset Level", "customerAssetLevelChineseName": "Customer Asset Level Chinese Name", "customerAssetLevelEnglishName": "Customer Asset Level English Name", "customerAssetLevelList": "Customer Asset Level List", "customerIdTaxId": "Customer ID/Tax ID", "customerIdTaxIdProductCode": "Customer ID/Tax ID/Product Code", "customerLevelCode": "Customer Level Code", "customerName": "Customer Name", "customerRegularReview": "Customer Regular Review", "dataCreateDate": "Data Create Date:", "dataDL": "Download Data", "date": "Date", "dateLabel": "Date", "dateMustBeBetween": "Date must be between 1753/01/01 and", "dateTime": "Date Time", "daysAdvanceToSendEvent": "Days in Advance\nto Send Event", "daysInAdvanceToSendEvent": "Days in Advance to Send Event", "delete": "Delete", "deleteDeputySuccess": "Delete deputy successful.", "deleteSuccess": "Delete successful.", "deputedPerson": "Deputized Person", "deputedPersonRole": "Deputized Person Role", "deputy": "Deputy Personnel", "deputyAgent": "Deputy Agent", "deputyAgentRole": "Deputy Agent Role", "deputyDateRange": "Deputy Date Range", "deputyDays": "Deputy Days", "deputyDaysCalendarNote": "Deputy days are calendar days", "deputyDeputedPerson": "Deputy/Deputized Person", "deputyEndDate": "Deputy End Date", "deputyEndTime": "Deputy End Time", "deputyNotSelf": "Deputy person cannot be yourself.", "deputyPerson": "Deputy Person", "deputyPersonCode": "Deputy Person Code", "deputySettingRecordList": "Deputy Setting Record List", "deputyStartDate": "Deputy Start Date", "deputyStartTime": "Deputy Start Time", "desc": "Description", "detailTitle": "Application details", "disable": "Disabled", "downloadExcel": "Download Excel", "duplicateTimeSlot": "Another deputy has been set for the same time slot, cannot proceed with setting.", "edit": "Edit", "editTooltip": "Edit", "effectDate": "Effective Date", "effectiveDate": "Effective Date", "employee": "Employee", "employeeName": "Employee Name", "employeeNo": "Employee No.", "employeeNotExists": "This employee code does not exist!", "employeeNumber": "Employee Number", "enable": "Enabled", "enabled": "Enabled", "endDate": "End Date", "endDateNotBusinessDay": "Deputy end date is not a business day.", "enterNumber1to2000": "Please enter a number between 1 and 2000 in the data count field.", "error": "Error", "errorRerun": "Error Re-run", "eventCategory": "Event Category", "eventNotificationCategory": "Event Notification Category", "eventNotificationList": "Event Notification List", "eventNotificationNote": "If you set the 'Days in advance to send event' field, it will only be effective for events where the send date can be known in advance.", "eventNotificationSetupNote": "If you set the 'Days in advance to send event' field, it will only be effective for events where the send date can be known in advance.", "everyMonths": "Every {0} months", "excelDL": "Download Excel", "execute": "Execute", "executeCol": "Execute", "executeSuccess": "Execute successful", "executionItem": "Execution Item", "executionStatus": "Execution Status", "expandAll": "Expand All", "expandAllBtn": "Expand All", "expandAllModal": "Expand All", "expireDate": "Expiry Date", "export": "Export", "fail": "Failed", "failed": "Failed", "fileDL": "Download File", "fileName": "File Name", "fileUpload": "Upload File", "fullRerun": "Full Re-run", "func": "Function", "funcMenuPreview": "Function Menu Preview", "funcModule": "Function Module", "funcPermMaint": "Function Permission Maintenance", "functionDetail": "Function Detail", "functionItem": "Function Item", "functionMenu": "Function menu", "functionModule": "Function Module", "groupCode": "Group Code", "groupName": "Group Name", "hrSystemNote": "Position and business category data shows information maintained in HR system", "import": "Import", "info": "Information", "invalid": "Invalid", "invalidDate": "Invalid date", "invalidDeputyCode": "This employee code cannot be set as deputy, please re-enter!", "item": "<PERSON><PERSON>", "itemNo": "Item No.", "itemNumber": "Item Number", "join": "Join", "lastRunTime": "Last Run Time", "leavePersonnel": "Leave Personnel", "level1MenuModule": "Level 1 Menu (Module)", "level2MenuFunc": "Level 2 Menu (Function)", "level3MenuFunc": "Level 3 Menu (Sub-function)", "level4MenuTab": "Level 4 Menu (Tab)", "level5MenuSubTab": "Level 5 Menu (Sub-tab)", "levelChineseName": "Level Chinese Name", "maintainStaff": "Maintain Staff", "maintainer": "Maintainer", "maxCharacters200": "200 characters available", "menuLevel1": "Level 1 menu (module)", "menuLevel2": "Level 2 Menu (Function)", "menuLevel3": "Level 3 menu (subfunction)", "menuLevel4": "Level 4 menu (tab)", "menuLevel5": "Level 5 menu (sub-tab)", "menuPreviewNote": "Click Excel download to generate all roles and their corresponding permissions to Excel. Please wait patiently for the download due to large file content.", "minRmLevel": "Minimum RM Level for Service", "missingRequiredFields": "Missing required fields.", "modificationDate": "Modification Date", "modify": "Modify", "modifyDate": "Modify Date", "modifyMsg": "Message", "modifyPerson": "Modifier", "modifySuccess": "Modify successful.", "modifyTime": "Modify Time", "modifyType": "Modify Type", "msg": "Message", "name": "Name", "no": "No", "noAvailablePosition": "No available positions.", "noChanges": "No changes", "noChangesToSubmit": "No changes made, cannot submit for review.", "normal": "Normal", "normalExecution": "Normal Execution", "notCsvFile": "This file is not a .csv file, please select again", "notice": "Notice", "op": "Actions", "open": "Open", "optional": "Optional", "organization": "Organization (Region/Branch)", "pastTimeDeputy": "<PERSON><PERSON> set past deputy time", "pendingRecheck": "Pending Recheck", "pendingReview": "Pending Review", "permMgmt": "Permission Management", "permSetting": "Permission Settings", "permissionScope": "Permission range", "personalDeputySetting": "Personal Deputy Setting", "pleaseConfirm": "Please Confirm", "pleaseConfirmCSVFile": "Please confirm the uploaded file is a CSV file", "pleaseEnter": "Please Enter", "pleaseEnterAuaLowerLimit": "Please enter AUA lower limit (TWD)", "pleaseEnterAuaUpperLimit": "Please enter AUA upper limit (TWD)", "pleaseEnterCompleteDateFormat": "Please enter complete date format: YYYY/MM/DD or YYYY-MM-DD", "pleaseEnterDateRange": "Please enter date range", "pleaseEnterFollowingData": "Please enter the following data", "pleaseEnterLevelChineseName": "Please enter level Chinese name", "pleaseEnterMinRmLevel": "Please enter minimum RM level for service", "pleaseSelect": "Please Select", "pleaseSelectUnitFirst": "Please select unit first.", "positionTitle": "Position & Title", "print": "Print", "qty": "Quantity", "query": "Query", "queryBranchUnit": "Query Branch (Unit)", "queryDateRange": "Query Date Range", "queryDateRangeCannotExceedSevenDays": "Query date range cannot exceed seven days.", "queryDateRangeCannotExceedThirtyDays": "Query date range cannot exceed 30 days.", "queryDateRangeEnd": "Query Date Range (End)", "queryDateRangeStart": "Query Date Range (Start)", "queryEndDate": "Query End Date", "queryStartDate": "Query Start Date", "queryUser": "Query User", "quickSearch": "Quick Search", "recheckComplete": "Recheck Completed", "regionSearch": "Regional Branch Search", "regionSearchExport": "RegionalSearch_", "regionStaffSetup": "Regional Staff Setup", "regionalBranchQuery": "Regional Branch Query", "regionalStaffSetting": "Regional Staff Setting", "regularAssetReviewList": "Regular Asset Review List", "regularReviewFreqCannotBeNegative": "Regular review frequency cannot be negative", "regularReviewFreqCannotExceed999": "Regular review frequency cannot exceed 999", "regularReviewFreqMustBeNumber": "Regular review frequency must be a number", "regularReviewFrequency": "Regular Review Frequency", "remark": "Remarks", "reminderDays": "Reminder Days", "reqField": "Required Field", "required": "Required", "reset": "Reset", "returnEdit": "Return for Revision", "reviewApproved": "Review Approved", "reviewComplete": "Review Completed", "reviewDate": "Review Date", "reviewStatus": "Review Status", "reviewed": "Reviewed", "reviewer": "Reviewer", "role": "Role", "roleAlreadyExists": "This role already exists.", "roleCode": "Role Code", "roleName": "Role Name", "roleSetting": "Role Setting:", "roleSettingPreview": "Role Setting Preview", "runResult": "<PERSON> Result", "sampleDocument": "Sample Document", "sampleFile": "Sample File", "save": "Save", "saveFailed": "Save failed", "saveSuccess": "Save successful", "saveSuccessful": "Save successful.", "search": "Search", "searchCond": "Search Criteria", "searchResult": "Search Results", "selSysRole": "Select System Role", "select": "Select", "selectBranchType": "Please select branch type", "selectBranchTypeMsg": "Please select branch type", "selectBranchUnit": "Select Branch (Unit)", "selectBranchUnitTitle": "Select Branch (Unit)", "selectOption": "Select", "selectRole": "Select Role", "selectRoleTitle": "Select Role", "selectUploadFile": "Please select upload file", "seqNo": "Seq No.", "shutdownAnnouncementList": "Shutdown Announcement List", "shutdownDateEnd": "Shutdown Date (End)", "shutdownDateEndDate": "Shutdown Date (End) - Date", "shutdownDateEndMinute": "Shutdown Date (End) - <PERSON><PERSON>", "shutdownDateEndTime": "Shutdown Date (End) - Time", "shutdownDateStart": "Shutdown Date (Start)", "shutdownDateStartDate": "Shutdown Date (Start) - Date", "shutdownDateStartMinute": "Shutdown Date (Start) - <PERSON><PERSON>", "shutdownDateStartTime": "Shutdown Date (Start) - Time", "shutdownDescription": "Shutdown Description", "startDate": "Start Date", "startDateCannotBeGreaterThanEndDate": "Query date range (start) cannot be greater than query date range (end).", "startDateGreaterThanEnd": "Deputy start date cannot be greater than deputy end date", "startDateMustBeLessThanEndDate": "Query start date must be less than end date", "startDateNotBusinessDay": "Deputy start date is not a business day.", "status": "Status", "statusChange": "Status Change", "statusDisabled": "Status: Disabled", "statusEnabled": "Status: Enabled", "stopped": "Stopped", "submit": "Submit", "submitDate": "Submit Date", "submitReview": "Submit for Review", "submitter": "Submitter", "subordinateDeputyList": "Subordinate/Manager Deputy List", "subordinateDeputySetting": "Subordinate/Manager Deputy Setting", "subtotal": "Subtotal", "success": "Success", "sysMenuFuncSetting": "System Menu Function Setting", "sysMgmt": "System Management", "sysRole": "System Role", "sysRoleList": "System Role List", "sysRoleMgmt": "System Role Management", "sysRolePerm": "System Role Permissions", "sysRoleRequired": "System role is required.", "sysUserQuery": "System User Query", "sysUserQueryExport": "SystemUserQuery_", "sysUserSetting": "System User Setting", "systemModule": "System Module", "tab": "Tab", "terminateDeputy": "Terminate Deputy", "time": "Time", "timeLabel": "Time", "times": "Times", "title": "Title", "total": "Total", "type": "Type", "updateDate": "Updated Date", "updateSuccess": "Update successful", "upload": "Upload", "uploadCount": "Upload Count", "uploadFile": "Upload File", "uploadListResult": "Upload List Result", "uploadNote": "If you leave this function without clicking 'Complete' after uploading regional staff settings, the list data will not be retained", "uploadNote1": "1. After uploading, you need to click 'Submit' button to complete the save operation.", "uploadNote2": "2. All upload results must pass validation before submission.", "uploadNote3": "3. Submitted data will take effect the next day.", "uploadResult": "Upload Result", "uploadResultList": "Upload Result List", "uploadUser": "Upload User", "uploaderStaff": "Uploader Staff", "user": "User", "userAccount": "User Account", "userAccountMgmt": "User Account Management", "userBasicInfo": "User Basic Information", "userCode": "User Code", "userCodeColon": "User Code:", "userCodeNote": "Please enter user code, click search to automatically retrieve user information", "userCustomerAccessRecordList": "User Customer Access Record List", "userDataProductionRecord": "User Data Production Record", "userFullName": "User Name", "userName": "User Name", "userNameColon": "User Name:", "userRole": "User Role", "userUsageDetailRecordList": "User Usage Detail Record List", "userUsageRecordList": "User Usage Record List", "valid": "<PERSON><PERSON>", "validationDescription": "Validation Description", "validationError1to2000": "Please enter a number between 1 and 2000 in the data count field.", "validationFailed": "Validation failed, submission aborted", "verify": "Recheck", "view": "View", "warning": "Warning", "withoutHeaderNote": "No header included", "yes": "Yes"}