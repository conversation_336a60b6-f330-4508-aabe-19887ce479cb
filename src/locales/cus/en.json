{"accAmt": "Accounting amount", "accNo": "Account number", "accompanyVisit": "Accompany Visit", "account": "Account", "account1": "Account 1", "account2": "Account 2", "accountBalance": "Account Balance Between", "accountDetails": "Account Details", "accountInfo": "Account Info", "accountManager": "Account Manager", "accountMarketValue": "Account Market Value", "accountNumber": "Account Number", "accountOfficerInfo": "Account Officer Info", "accountOverview": "Account Overview", "accumulatedDividend": "Accumulated Dividend", "accumulatedPremiumPaid": "Accumulated Premium Paid", "accumulatedTxAmtMonth": "Cumulative transaction amounts per month", "actualPl": "Substantive profit and loss", "add": "Add", "addCompanyData": "Add Company Data", "addGroupSuccess": "Add group success", "addSuccess": "Add Success", "addSuccessful": "Add successful.", "addToContent": "Add to Content", "addToKeyCustomerGroup": "Add to Key Customer Group", "addedGroupCustomers": "Added Group Customers", "advanceNotification": "Advance Notification", "advisor": "Advisor", "affiliatedUnit": "Affiliated Unit", "age": "Age", "ageYears": "Age: {age} years", "ageYearsOld": "Age: {age} years old", "agreeToTelemarketing": "Agree to Telemarketing", "alertAccountMarking": "<PERSON><PERSON> Account Marking", "all": "All", "allGroupCustomers": "All Group Customers", "allocationRatio": "Allocation Ratio", "alreadySetSamePhrase": "Same phrase already set, please reset.", "amountBetween": "Amount Between", "amt": "Amount", "anyComplaints": "Any Complaints", "aoCode": "AO Code", "appointment": "Appointment", "appointmentContent": "Appointment Content", "appointmentDate": "Appointment Date", "appointmentRecord": "Appointment Record", "appointmentRecordCreated": "Appointment record created successfully", "appointmentSchedule": "Appointment Schedule", "appointmentSubject": "Appointment Subject", "appointmentTime": "Appointment Time", "approvedLimit": "Approved Limit", "assessmentDimension": "Assessment Dimension", "asset": "<PERSON><PERSON>", "assetAlloc": "Asset Allocation", "assetBalance": "Asset Balance", "assetBalanceNtdThousand": "Asset Balance (TWD/Thousand)/Data Date", "assetCategory": "Asset Category", "assetOverview": "Asset Overview", "assetReport": "Asset Report", "assetReportList": "Asset Report List", "assetStatus": "Asset Status", "assetStatusInvestmentAllocation": "Asset Status - Investment Allocation", "assetTrend": "Asset Trend", "assetTypePie": "Asset Category Pie Chart", "assets": {"ALL": "Total Asset Details", "BOND": "Trust Account - Foreign Bonds", "DCI": "Structured Investment Products", "ETF": "Trust Account - Exchange-Traded Funds (ETFs)", "FUND": "Trust Account - Mutual Funds", "INS": "Life Insurance", "PFD": "Trust Account - Foreign Equities", "SAVING": "Deposit <PERSON>", "SP": "Onshore/Offshore Structured Products"}, "assignedPBC": "Assigned PBC:", "assignedPersonnel": "Assigned Personnel", "atLeastOneItemMustBeSelected": "At least one item must be selected", "aua": "AUA", "aum": "AUM", "authorizedPersonId": "Authorized Person ID", "authorizedPersonName": "Authorized Person Name", "availableBalance": "Available Balance", "availableCustomers": "Available Customers", "availableForSubscription": "Available for Subscription", "availableLimit": "Available Limit", "back": "Back", "balanceEnd": "Balance To", "balanceStart": "Balance From", "bankContribution": "Bank Contribution", "bankCustomerData": "Bank Customer Data", "bankDmMailingMarking": "Bank DM Mailing Marking", "bankEmailSalesMarking": "Bank Email Sales Marking", "bankPhoneSalesMarking": "Bank Phone Sales Marking", "bankSmsSalesMarking": "Bank SMS Sales Marking", "bankTotalAssets": "Bank Total Assets (AUM)", "basicComponents": "Basic Components", "basicData": "Basic Data", "basicInfo": "Basic Info", "basicInformation": "Basic Information", "basicSearch": "Basic Search", "between": "Between", "birthday": "Birthday", "birthdayDate": "Birthday", "black": "Black", "bold": "Bold", "bond": "Bond", "branch": "Branch", "branchName": "Branch Name", "bulletList": "Bullet List", "businessLocation": "Business Location", "businessType": "Business Type", "buy": "Buy", "byAssetCategory": "By Asset Category", "byCustomerAUM": "By Customer AUM", "byCustomerGrade": "By Customer Grade", "byCustomerIdTaxId": "By Customer ID/Tax ID", "byCustomerName": "By Customer Name", "byInvestmentRegion": "By Investment Region", "byInvestmentTarget": "By Investment Target", "byProductCode": "By Product Code", "byRiskAttribute": "By Risk Attribute", "caCode": "CA Code", "canMarketingContact": "Can Marketing Contact", "cancel": "Cancel", "cancelModification": "Cancel Modification", "cannotContact": "Cannot Contact", "capitalAmount": "Capital Amount", "category": "Category", "cellPhone": "Cell Phone", "certificateNumber": "Certificate Number", "changeDate": "Change Date", "changeFields": "Change Fields", "changeRecord": "Change Record", "channelServiceFeeActualRate": "Channel Service Fee Actual Rate", "channelServiceFeeAnnualRate": "Channel Service Fee Annual Rate", "charactersAvailable": "characters available", "charactersRemaining": "{count} characters remaining", "chineseName": "Chinese Name", "close": "Close", "closeOrSubmitReview": "Close or Submit Review", "closeWindow": "Close Window", "code": "Code", "collapse": "Collapse", "commonPhrase": "Common Phrase", "commonPhraseExceeded": "Common phrase exceeded setting count, please adjust from common phrase setting page.", "commonPhraseSetting": "Common Phrase Setting", "commonPhraseSettings": "Common Phrase Settings", "commonPhrases": "Common Phrases", "company": "Company", "companyAddress": "Company Address", "companyBasicData": "Company Basic Data", "companyCategory": "Company Category", "companyData": "Company Data", "companyEnglishName": "Company English Name", "companyEstablishmentDate": "Company Establishment Date", "companyList": "Company List", "companyName": "Company Name", "companyNameRequired": "Company Name", "companyOperationalData": "Company Operational Data", "companyOtherData": "Company Other Data", "companyPhone": "Company Phone", "companyPhonePhone1": "Company Phone/Phone 1", "companyRegistrationCountry": "Company Registration Country", "companyRepresentative": "Company Representative", "companyUnifiedNumber": "Company Unified Number", "companyWebsite": "Company Website", "completed": "Completed", "complexSearch": "Complex Search", "components": "Components", "comprehensiveCustomerSearch": "Comprehensive Customer Search", "comprehensiveSearch": "Comprehensive Search", "confirm": "Confirm", "confirmDelete": "Confirm Delete", "confirmDeleteAllCompanyData": "Are you sure to delete all company data?", "confirmDeleteRecord": "Confirm delete this record?", "confirmDeleteReport": "Are you sure you want to delete the report?", "confirmDeleteWithName": "Confirm Delete", "confirmOk": "OK", "contactAddress": "Contact Address", "contactInfo": "Contact Info", "contactPerson": "Contact Person", "contactPhone": "Contact Phone", "contactPhoneHome": "Contact Phone (Home)", "contactPhoneMobile": "Contact Phone (Mobile)", "contactPhoneOffice": "Contact Phone (Office)", "contactRecord": "Contact Record", "contactResult": "Contact Result", "contactTime": "Contact Time", "contentContainsForbiddenWord": "Input content contains '{word}', may involve active recommendation or inappropriate marketing, please reconfirm appropriateness", "contractRate": "Contract Rate", "contribution": "Contribution", "contributionBetween": "Contribution Between", "correspondingChinese": "Corresponding Chinese", "country": "Country", "createAppointment": "Create Appointment", "createAssetReport": "New asset report", "createContactRecord": "Create Contact Record", "createDate": "Create Date", "createFailed": "Creation failed", "createReport": "Create Report", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currentLoanBalance": "Current Loan Balance", "currentMonthContribution": "Current Month Contribution", "currentValue": "Current Value", "currentYearAccumulatedContribution": "Current Year Accumulated Contribution", "cusBasicInfo": "Customer Basic Info", "cusCode": "Customer Code", "cusInfo": "Customer Info", "cusMgmt": "Customer Management", "cusName": "Customer Name", "cusOverview": "Customer Overview", "cusSearch": "Customer Search", "cusStatus": "Customer Status", "cusType": "Customer Type", "customFieldListForSearchResults": "Custom Field List for Search Results", "customGroupName": "Custom Group Name", "customGroupNameRequired": "Custom group name is required", "customer": "Customer", "customerAlreadyExistsInGroup": "Customer already exists in customer group", "customerAssetLevel": "Customer Asset Level", "customerBasicInfo": "Customer Basic Info", "customerBirthday": "Customer Birthday (Month/Day) [Natural Person Only]", "customerCode": "Customer Code", "customerColon": "Customer:", "customerDataT1Note": "Customer data is from T-1, if customer was added today, it can be found on T-1 business day.", "customerDoesNotExist": "Customer does not exist.", "customerGroupMaintenance": "Customer Group Maintenance", "customerGroupMaintenanceModify": "Customer Group Maintenance - Modify", "customerIdNumber": "Customer ID Number", "customerIdTaxId": "Customer ID/Tax ID", "customerInterviewNote": "Please fill in customer interview record content accurately, pay special attention to the following before marketing (not limited to these types)", "customerInvestmentProfile": "Customer Investment Profile", "customerIsConfidential": "Customer is confidential.", "customerIsEmployee": "Customer is employee.", "customerLevel": "Customer Level", "customerName": "Customer Name", "customerNameFuzzySearch": "Customer name uses fuzzy search.", "customerNameQueryMinTwoChars": "Customer name query requires at least 2 characters.", "customerNotAuthorizedForViewing": "Customer not authorized for viewing.", "customerNotAuthorizedToView": "Customer not authorized to view.", "customerNotUnderYourCenter": "Customer is not under your center/group.", "customerNotUnderYourPrivateBankingCenter": "Customer {0} is not under your private banking center/group.", "customerOverview": "Customer Overview", "customerReport": "Customer Report", "customerRiskExpiryDate": "Customer Risk Expiry Date", "customerSearch": "Customer Search", "customerSearchResultList": "Customer Search Result List", "customerVipLevel": "Customer VIP Level", "dailyChange": "Daily Change", "dangerRed": "Danger Red", "darkBlue": "Dark Blue", "darkGray": "Dark Gray", "darkGreen": "Dark Green", "dataDate": "Data Date", "date": "Date", "dateRange": "Date Range", "day": "Day", "daysToExpiry": "Days to Expiry", "dealPrice": "Deal Price", "dealSequenceNumber": "Deal Sequence Number", "dealShares": "Deal Shares", "debitType": "Debit Type", "delegationNumber": "Delegation Number", "delete": "Delete", "deleteComponent": "Delete Component", "deleteFailed": "Deletion failed", "deleteSuccess": "Delete Success", "deleteSuccessful": "Delete successful.", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "description": "Description", "disabilityCategoryDimensionExplanation": "Disability Category Dimension Explanation", "disable": "Disable", "disbursementAmount": "Disbursement Amount (L+PB+CK)", "disbursementAmountShort": "Disbursement Amount", "dividendIncome": "Interest income", "dividendStatus": "Dividend Status", "dragComponentsFromLeft": "Drag components from left category to this area", "dragOperationFailed": "Drag Operation Failed", "dueDate": "Due Date", "earliestAccountOpeningDate": "Earliest Account Opening Date", "edit": "Edit", "editAppointment": "Edit Appointment", "editKeyCustomer": "Edit Key Customer", "editText": "Edit Text", "education": "Education", "educationLevel": "Education Level", "effectiveDate": "Effective Date", "efficiencyInvestment": "Efficiency Investment", "email": "Email", "employeeCount": "Employee Count", "employeeCountMustBePositiveInteger": "Employee count must be positive integer.", "enable": "Enable", "enableReminder": "Enable Reminder", "endDate": "End Date", "endDateInvalid": "End date invalid", "englishName": "English Name", "equivalentTwd": "Equivalent TWD", "err-cannotGetNewReportId": "Unable to obtain the new report ID", "estimatedHandlingFee": "Estimated Handling Fee", "estimatedManagementFee": "Estimated Management Fee", "event": "Event", "eventCategory": "Event", "eventHandling": "Event Handling", "eventMaintenance": "Event Maintenance", "eventSubmittedForReview": "Event submitted for review, cannot maintain!", "excellenceInInvestment": "Excellence in Investment", "exchangeRate": "Exchange Rate", "exchangeRatePl": "Exchange rate profit and loss", "execute": "Execute", "existingReport": "Existing Report", "expandSettings": "Expand Settings", "expiryDate": "Expiry Date", "expiryNotificationSetting": "Expiry Notification Setting", "export": "Export", "faceToFace": "Face to Face", "fail": "Failed", "familyAndFriendsData": "Family and Friends Data", "familyFriendsDataList": "Family & Friends Data List", "familyFriendsDataSetup": "Family & Friends Data Setup", "favCus": "Favorite Customer", "fee": "cost", "financialAdvisor": "Financial Advisor", "financialAttributes": "Financial Attributes", "finishEditing": "Finish Editing", "fontSize": "Font Size", "forbiddenWordWarning": "Input content contains '{word}', may involve active recommendation or inappropriate marketing, please reconfirm appropriateness", "foreignCurrencyDepositBalance": "Foreign Currency Deposit <PERSON> (TWD Equivalent)", "freeDragArea": "Free Drag Area", "frontEndInterest": "Front End Interest", "functionalityDisclaimer": "This functionality provides information as of the statistical date, automatically generated by the system based on customer historical investment data, for reference only.", "fund": "Fund", "fundProducts": "Fund Products", "fundType": "Fund Type", "gender": "Gender", "genderRequired": "Gender", "generateIntegratedAssetReport": "Generate Integrated Asset Report", "generatedDate": "Generated Date", "goBack": "Go Back", "goBackToPreviousPage": "Go Back to Previous Page", "groupAffiliation": "Group Affiliation", "groupName": "Group Name", "groupNameAlreadyExists": "Group name already exists", "handlingFee": "Handling Fee", "heading1": "Heading 1", "heading2": "Heading 2", "heading3": "Heading 3", "highYieldBondRiskNotice": "High Yield Bond Risk Notice", "historyRecord": "History Record", "holdsCreditCard": "Holds Credit Card", "home": "Home", "homePhone": "Home Phone", "homePhonePhone2": "Home Phone/Phone 2", "hour": "Hour", "idNumber": "ID Number", "importantContactNotes": "Important Contact Notes", "importantHolidayList": "Important Holiday List", "importantHolidaySettings": "Important Holiday Settings", "industryClassificationNumber": "Industry Classification Number", "industryType": "Industry Type", "infoBlue": "Info Blue", "initialLoanAmount": "Initial Loan Amount", "initialLoanAmountTwd": "Initial Loan Amount (TWD)", "initialLoanDate": "Initial Loan Date", "insert": "Insert", "insertImage": "Insert Image", "insertedImage": "Inserted Image", "insurance": "Insurance", "insuranceType": "Insurance Type", "integratedAssetReport": "Integrated Asset Report", "interestRate": "Interest Rate", "invTxAmtAnalysis": "Transaction amount analysis", "invTxAmtTableNote": "This amount does not include deposits, trusts, insurances, gold passbooks; finances-conditional transactions", "invPlan": {"level1": "Level 1: Aggressive Growth"}, "investAnalysis": "Investment Analysis", "investPerf": "Investment Performance", "investTarget": "Investment Target", "investmentAmount": "Investment Amount", "investmentMarketValue": "Investment Market Value", "investmentPerformanceAnalysis": "Investment Performance Analysis", "investmentPlan": "Investment Plan", "investmentPrincipal": "Investment Principal", "investmentPrincipalCapital": "Investment Principal", "investmentPrincipalIncludingFees": "Investment Principal (Including Fees)", "investmentPrincipalVsCurrentValue": "Investment Principal vs Current Value", "investmentProductAllocation": "Investment Product Allocation", "investmentProducts": "Investment Products", "investmentProfile": "Investment Profile", "investmentRatio": "Investment Ratio", "investmentRatioPercentage": "Investment Ratio (%)", "investmentTarget": "Investment Target", "isEmployee": "Is Employee", "isListed": "Is Listed", "italic": "Italic", "jobTitle": "Job Title", "join": "Join", "keyCustomer": "Key Customer", "keyCustomerGroup": "Key Customer Group", "keyCustomerGroupSearch": "Key Customer Group Search:", "keyCustomerSetup": "Key Customer Setup", "keyCustomerSetupNote": "Key customer setup allows PBC to create their own customer groups for quick customer searches.", "kycRiskLevel": "KYC Risk Level", "lastUpdateDate": "Last Update Date", "legalRepresentative": "Legal Representative", "legalRepresentative2": "Legal Representative 2", "lifeInsuranceProducts": "Life Insurance Products", "lightGray": "Light Gray", "limitConditionQuery": "※Limit Condition Query", "limitStatus": "Limit Status", "loadReportFailed": "Load Report Failed", "loanAllocation": "Loan Allocation", "loanBalance": "Loan Balance (L+PB+CK)", "loanBalanceShort": "<PERSON><PERSON>", "loanConditionQuery": "※Loan Condition Query", "loanPeriod": "Loan Period", "loanProduct": "Loan Product", "loanRemark": "Loan <PERSON>", "location": "Location", "mailingAddress": "Mailing Address", "managementBranch": "Management Branch", "managementDate": "Management Date", "managementPersonnel": "Management Personnel", "managingAdvisorBranch": "Managing Advisor/Branch", "maritalStatus": "Marital Status", "marketValue": "Market Value", "marketingNote1": "Cannot actively recommend domestic securities specific investment targets (such as: funds, overseas bonds, stocks, ETFs, etc.) to 'special customer groups'.", "marketingNote2": "Cannot use inappropriate marketing methods (such as: using customer deposit data, comparing deposit rates with product returns, using dividend or dividend rate adjustments as marketing incentive, advising borrowing or debt financing, using inflation hedging or avoiding supplementary health insurance premium as appeal, etc.).", "marketingNote3": "Cannot use discontinuation, price increase, gift vouchers, tax benefits as marketing appeal for insurance products.", "marketingProjects": "Marketing Projects", "maxFiveSearchResults": "Maximum 5 search results can be saved", "maxSixItemsCanBeSelected": "Maximum 6 items can be selected", "maxValue": "Max Value", "menuItem1": "Menu Item 1", "menuItem2": "Menu Item 2", "minValue": "Min Value", "minValueCannotBeGreaterThanMaxValue": "Min value cannot be greater than max value", "minorWealthManagementConsent": "Minor Wealth Management Consent", "minute": "Minute", "mobile": "Mobile", "mobilePhone": "Mobile Phone", "modify": "Modify", "moneyLaunderingRiskLevel": "Money Laundering Risk Level", "month": "Month", "name": "Name", "namePassportEnglishName": "Name/Passport English Name", "nameRequired": "Name", "needToFill": "Need to Fill", "netValue": "Net Value", "netWorth": "Net Worth", "newReport": "New Report", "newTaiwanDollar": "New Taiwan Dollar: Yuan", "newTaiwanDollarUnit": "(New Taiwan Dollar: Yuan)", "no": "No", "noAdvanceNotification": "No Advance Notification", "noCustomerData": "No customer data or customer not under your private banking center/group.", "noCustomerFound": "No customer found, please check if customer has been inactive.", "noCustomersFound": "No customers found", "normalText": "Normal Text", "note": "Note", "noteMTDQTDYTD": "MTD: month to date; QTD: season to date; YTD: year to date", "notify": "Notify", "numberedList": "Numbered List", "obuDbuCustomer": "OBU/DBU Customer", "occupation": "Occupation", "onlineBankingOpened": "Online Banking Opened", "op": "Operation", "operationalData": "Operational Data", "organization": "Organization (Region/Branch/Advisor)", "originalInvestmentAmount": "Original Investment Amount", "other": "Other", "otherData": "Other Data", "otherFees": "Other Fees", "overseasBranchName": "Overseas Branch", "overseasBranches": "Overseas Branches", "pages": "Page Management", "paragraph": "Paragraph", "periodReturnRate": "Period Return Rate", "periodReturnTrend": "Period Return Trend", "periods": "Periods", "personalAnnualIncome": "Personal Annual Income (Yuan)", "personalDataProtectionAct": "Personal Data Protection Act", "personalLoan": "Personal Loan", "personalLoanDisbursement": "Personal Loan - Disbursement", "personalLoanLimit": "Personal Loan - Limit", "phone1": "Phone 1", "phone2": "Phone 2", "phoneCall": "Phone Call", "plRange": "P&L Range", "pleaseCreateOrSelectCompany": "Please create or select company first.", "pleaseEnter": "Please Enter", "pleaseEnterExpiryTime": "Please enter expiry time.", "pleaseEnterExpiryUnit": "Please enter expiry unit.", "pleaseEnterFollowingData": "Please Enter Following Data", "pleaseEnterProductCode": "Please enter product code", "pleaseEnterProductName": "Please enter product name", "pleaseEnterText": "Please Enter Text", "pleaseFillInSearchResultName": "Please fill in search result name.", "pleaseSelect": "Please Select", "pleaseSelectAtLeastOneCondition": "Please select at least one condition", "pleaseSelectCustomer": "Please select customer.", "pleaseSelectCustomerAssetLevel": "Please select customer asset level", "pleaseSelectCustomerGroup": "Please select customer group.", "pleaseSelectInvestmentProductOrEnterProductCode": "Please select investment product or enter product code", "pleaseSelectInvestmentProfile": "Please select investment profile", "pleaseSelectLimitStatus": "Please select limit status", "pleaseSelectLoanProductOrLoanRemark": "Please select loan product or loan remark", "pleaseSelectMaxSixItems": "(Please select, maximum 6 items)", "pleaseSelectRegion": "Please select region", "pleaseSelectTimeInterval": "Please select time interval", "portfolio": "Portfolio", "position": "Position", "postalCode": "Postal Code", "preferredContactMethod": "Preferred Contact Method", "preview": "Preview", "previousAoCode": "Previous AO Code", "previousManagementBranch": "Previous Management Branch", "previousManagementPersonnel": "Previous Management Personnel", "previousStep": "Previous Step", "previousYearAccumulatedContribution": "Previous Year Accumulated Contribution", "pricePercentage": "Price (%)", "primaryBlue": "Primary Blue", "principalChange": "Principal Change", "priorityNumberMarking": "Priority Number Marking", "processingAndNotes": "Processing and Notes", "processingContent": "Processing Content", "processingDate": "Processing Date", "processingMethod": "Processing Method", "processingStatus": "Processing Status", "processor": "Processor", "product": "Product", "productBalanceSearch": "Product Balance Search", "productCategory": "Product Category", "productCode": "Product Code", "productCodeInvestmentTarget": "Product Code/Investment Target", "productCurrency": "Product Currency", "productExpiryDate": "Product Expiry Date", "productExpirySearch": "Product Expiry Search:", "productMainCategory": "Product Main Category", "productMarketValueSearch": "Product Market Value (TWD Equivalent) Search:", "productName": "Product Name", "productQuickSearch": "Product Quick Search", "productSubcategory": "Product Subcategory", "professionalInvestor": "Professional Investor", "profitLoss": "Profit/Loss", "projectName": "Project Name", "projectTrust": "Project Trust", "queryCode": "Query Code", "questionnaireEffectiveDate": "Questionnaire Effective Date", "quickActionButtons": "Quick Action Buttons", "quickSearch": "Quick Search", "range": "Range", "rangeCannotBeEmpty": "Range cannot be empty", "ratio": "<PERSON><PERSON>", "realizedPlSrch": "Realize profit and loss inquiry", "realizedProfitLoss": "Realized Profit/Loss", "recent12MonthAssetTrend": "Recent 12 Month Asset Trend", "recent12MonthInvestmentChange": "Recent 12 Month Investment Change", "recentAoCodeChangeRecord": "Recent AO Code Change Record", "recentContact": "Recent Contact", "recentContactDate": "Recent Contact Date", "recentTransactionDate": "Recent Transaction Date", "recentTwoMonthEvents": "Recent 2 Month Events", "recordsCannotAddMore": "records, cannot add more.", "referenceExchangeRate": "Reference Exchange Rate", "refreshData": "Refresh Data", "refuseMarketing": "Refuse Marketing", "region": "Region", "registeredAddress": "Registered Address", "registeredAddressCompany": "Registered Address", "relationship": "Relationship", "remarks": "Remarks", "reminderDaysBeforeCalendarExpiry": "Reminder Days Before Calendar Expiry", "reminderDaysBeforeExpiry": "Reminder Days Before Expiry", "remove": "Remove", "repaidAmount": "<PERSON><PERSON> Amount", "repaymentMethod": "Repayment Method", "repaymentRate": "Repayment Rate", "reportName": "Report Name", "representativeId": "Representative ID", "representativeName": "Representative Name", "required": "Required", "requiredField": "Required Field", "requiredFieldNote": "Required Field", "returnRate": "Return Rate", "returnRateContribution": "Return Rate Contribution (%)", "returnRateIncludingInterest": "Return Rate Including Interest (%)", "revenue": "Revenue", "reviewDate": "Review Date", "reviewSupervisor": "Review Supervisor", "rewardRateAlertSettings": "Reward Rate Alert <PERSON>s", "riskProfile": "Risk Profile", "riskRating": "Risk Rating", "save": "Save", "saveAndNext": "Save & Next", "saveAndNextStep": "Save and Next Step", "saveFailed": "Save failed", "saveReport": "Save Report", "saveReportData": "Save Report Data", "saveReportDialog": "Save Report Dialog", "saveSearchResults": "Save Search Results", "saveSuccess": "Save Success", "saveSuccessful": "Save Successful", "savingsProducts": "Savings Products", "savingsType": "Savings Type", "scheduleAppointment": "Schedule Appointment", "search": "Search", "searchCond": "Search Condition", "searchConditions": "Search Conditions", "searchHistory": "Search History", "searchHistoryList": "Search History List", "searchHistoryResultList": "Search History Result List", "searchProduct": "Search Product", "searchResult": "Search Result", "searchResultName": "Search Result Name", "searchResultNameCannotExceed60Characters": "Search result name cannot exceed 60 characters!", "searchResultsAlreadyHaveRecords": "Search results already have", "secondaryPurple": "Secondary Purple", "securitiesStockDataNote": "Securities stock data shows current stock data only.", "select": "Select", "selectAll": "Select All", "selectImageFile": "Select Image File", "selectedAccount": "Selected Account", "sell": "<PERSON>ll", "sequenceNumber": "Sequence Number", "serviceOrganization": "Service Organization", "serviceRecord": "Service Record", "settings": "Settings", "shares": "Shares", "singleConditionCustomerSearch": "Single Condition Customer Search", "smeMarking": "SME Marking", "specialSearch": "Special Search", "specificMoneyTrustAUM": "Specific Money Trust AUM", "specificTrustInvestmentConsent": "Specific Trust Investment Consent", "specificTrustRecommendation": "Specific Trust Recommendation", "startDate": "Start Date", "startDateCannotBeGreaterThanEndDate": "Start date cannot be greater than end date", "startDateCannotBeLessThanEndDate": "Start date cannot be less than end date", "startDateInvalid": "Start date invalid", "status": "Status", "stockETF": "Stock/ETF", "structuredProduct": "Structured Product", "subscriptionType": "Subscription Type", "subtotal": "Subtotal", "success": "Success", "successGreen": "Success Green", "summaryAmount": "Summary Amount", "summaryCalculation": "Summary Calculation", "summaryCalculationResults": "Summary Calculation Results", "summaryItems": "Summary Items", "supervisorAccompany": "Supervisor Accompany", "systemNoCustomerCannotSave": "System has no such customer, cannot save", "taxFee": "Tax Fee", "tempSaveNotSubmit": "Temporary Save Not Submit", "tenThousandYuan": "Ten Thousand Yuan", "textColor": "Text Color", "textType": "Text Type", "time": "Time", "timeInterval": "Time Interval", "todayReportAlreadyGenerated": "Today's report already generated, please view in asset status report list", "todoItemHandling": "<PERSON><PERSON>", "totAccAmt": "Total amount of account", "totBuy": "Total buy", "totDividend": "Total interest", "totPl": "Total profit and loss", "total": "Total", "totalAccountAssets": "Total Account Assets", "trade": "Trade", "tradeAmt": "Trade Amount", "tradeDate": "Trade Date", "tradeDetail": "Trade Detail", "tradeRecord": "Trade Record", "tradeStatus": "Trade Status", "transactionAmount": "Transaction Amount", "transactionCurrency": "Transaction Currency", "transactionDate": "Transaction Date", "transactionDateRange": "Transaction Date Range", "transactionFaceValue": "Transaction Face Value", "transactionRecordQuery": "Transaction Record Query", "transactionType": "Transaction Type", "transactionUnits": "Transaction Units", "trustCertificatePL": "Trust Certificate P&L", "trustCertificatePLSearch": "Trust Certificate P&L Search", "trustETF": "Trust-ETF", "trustForeignBond": "Trust-Foreign Bond", "trustForeignStock": "Trust-Foreign Stock", "trustFund": "Trust-Fund", "trustManagementFee": "Trust Management Fee", "twdDepositBalance": "TWD Deposit Balance", "txAmtMonth": "Monthly transaction amount", "type": "Type", "underline": "Underline", "ungroupedCustomers": "Ungrouped Customers", "unifiedBusinessNumber": "Unified Business Number", "unprocessed": "Unprocessed", "unrealizedPLWithInterest": "Unrealized P&L with Interest", "unrealizedProfitLoss": "Unrealized Profit/Loss", "unrealizedProfitLossIncludingInterest": "Unrealized Profit/Loss (Including Interest)", "unsavedChangesWarning": "You have unsaved changes, are you sure to leave?", "updatePersonnel": "Update Personnel", "updateSuccess": "Update Success", "updateSuccessful": "Update successful.", "usedLimit": "Used Limit", "view": "View", "viewAppointment": "View Appointment", "vipCus": "VIP Customer", "visitMethod": "Visit Method", "visitPurpose": "Visit Purpose", "warningAccountMarking": "Warning Account <PERSON><PERSON>", "warningOrange": "Warning Orange", "wealthGrowthAnalysis": "Wealth Growth Analysis", "wealthManagementBranch": "Wealth Management Branch", "wealthManagementCompany": "Wealth Management Company", "wealthSpecificCustomer": "Wealth Specific Customer (No Active Recommendation)", "week": "Week", "weeklyChange": "Weekly Change", "white": "White", "workItems": "Work Items", "workUnit": "Work Unit", "workdays": "Work Days", "year": "Year", "yearsOld": "{age} years old", "yes": "Yes", "yuan": "Yuan", "assetReportComponent": {"textComponent": "Text Component", "assetAndLiability": "Assets and Liabilities", "investmentDemand": "Investment Demand", "wealthGrowthAnalysis": "Wealth Growth Analysis", "assetChangeStatus": "Asset Change Status", "realizedPnL": "Realized P&L", "assetClassification": "Asset Classification", "assetLiabilityFlowChange": "Asset and Liability Flow Change", "assetOverview": "Asset Overview", "netWorthAnalysis": "Net Worth Analysis", "pnlAnalysis": "P&L Analysis", "investmentTargetAnalysis": "Investment Target Analysis", "transactionAmountAnalysis": "Transaction Amount Analysis", "realizedPnLQuery": "Realized P&L Query", "assetCategoryPieChart": "Asset Category Pie Chart", "transactionRecordQuery": "Transaction Record Query"}}