{"accompanist": "Accompanist", "accompanyPurpose": "Accompany Purpose", "add": "Add", "addSpecificStaff": "Add Specific Staff", "addSuccess": "Added Successfully", "advance": "Advance", "advanceNotice": "Advance Notice", "advanceNotificationDaysWeeks": "Advance Notification Days/Weeks", "advanceNotificationPeriod": "Advance Notification Period", "afterChange": "After Change", "age": "Age", "all": "全て", "allDayItem": "終日プロジェクト", "appointment": "Appointment", "appointmentContent": "Appointment Content", "appointmentCustomer": "Appointment Customer", "appointmentDate": "Appointment Date", "appointmentEvent": "Appointment Event", "appointmentRecord": "Appointment Record", "appointmentSchedule": "Appointment Schedule", "appointmentSubject": "Appointment Subject", "appointmentTime": "Appointment Time", "assetStatusChange": "Asset Status Change", "awaitingReview": "Awaiting Review", "beforeChange": "Before Change", "birthday": "Birthday", "branch": "Branch", "calendar": "Calendar", "cancel": "Cancel", "cancellation": "Cancellation", "categorizeContactMethods": "Categorize Contact Methods", "category": "Category", "categoryItem": "Category Item", "close": "Close", "closeCase": "Close Case", "closed": "Closed", "closingDate": "Closing Date", "code": "Code", "commonContactContent": "Common Contact Content", "commonPhrases": "Common Phrases", "commonPhrasesExceedLimit": "Common phrases exceed limit, please adjust via common phrases settings page.", "commonPhrasesManagement": "Common Phrases Management", "commonPhrasesMechanism": "Common Phrases Mechanism", "completedTasks": "作業完了項目", "confirm": "Confirm", "confirmDeleteData": "Are you sure you want to delete this data?", "contact": "Contact", "contactContent": "Contact Content", "contactCustomer": "Contact Customer", "contactDate": "Contact Date", "contactDateFrom": "Contact Date From", "contactDateTo": "Contact Date To", "contactDetail": "Contact Details", "contactMethod": "Contact Method", "contactPhoneHome": "Contact Phone (Home)", "contactPhoneMobile": "Contact Phone (Mobile)", "contactPhoneOffice": "Contact Phone (Office)", "contactRecord": "Contact Record", "contactRecordMaintenance": "Contact Record Maintenance", "contactStatus": "Contact Status", "contactSubject": "Contact Subject", "contactTask": "Contact Task", "contactTime": "Contact Time", "content": "Content", "createDate": "Create Date", "crossBorder": "Cross-border", "customer": "Customer", "customerData": "Customer Data", "customerExistsInGroup": "Customer already exists in this group.", "customerGroup": "Customer Group", "customerIdCard": "Customer ID Card/Tax ID", "customerIdTaxId": "Customer ID/Tax ID", "customerImportantDate": "Customer Important Date", "customerImportantDateMaintenance": "Customer Important Date Maintenance", "customerInfo": "Customer Information", "customerName": "Customer Name", "dashboard": "Dashboard", "date": "Date", "dateRangeError": "Start date cannot be greater than end date.", "dateRangeErrorOneYear": "Date range input cannot exceed one year.", "dateRangeNote": "Date range input cannot exceed one year.", "day": "Day", "daysBeforeEvent": "Days Before Event", "delete": "Delete", "deleteContactRecord": "Delete Contact Record", "deleteSuccess": "Delete successful.", "description": "Description", "disable": "Disabled", "discontinued": "Discontinued", "displayParameters": "Display Parameters", "edit": "Edit", "editKeyCustomers": "Edit Key Customers", "editParameters": "Edit Parameters", "elderly": "Elderly", "email": "Email", "enable": "Enabled", "eventCategory": "Event Category", "eventCount": "Event Count", "eventDescription": "Event Description", "eventDueDate": "Event Due Date", "eventGenerateDate": "Event Generate Date", "eventGenerateDateFrom": "Event Generate Date From", "eventGenerateDateTo": "Event Generate Date To", "eventNotificationCategory": "Event Notification Category", "execute": "Execute", "executeDateFrom": "Execute Date From", "executeDateRangeError": "Execute date range (from) cannot be greater than execute date range (to).", "executeDateTo": "Execute Date To", "expire": "Expire", "expired": "Expired", "expiryNotificationSetting": "Expiry Notification Setting", "expiryProductView": "Expiry Product View", "fail": "Failed", "filingPersonnel": "Filing Personnel", "filingUnit": "Filing Unit", "followUpProcessing": "Follow-up Processing", "forbiddenContentMessage": "Input content contains '{word}', may involve active promotion or inappropriate marketing, please reconfirm the appropriateness of interview content registration", "forbiddenWords": "Forbidden Words", "getContactRecord": "Get Contact Record", "getContactStatusMenu": "Get Contact Status Menu", "getCustomerData": "Get Customer Data", "getFollowUpProcessingMenu": "Get Follow-up Processing Menu", "getInterviewMethodMenu": "Get Interview Method Menu", "getInterviewPurposeMenu": "Get Interview Purpose <PERSON>", "getProcessingMethodMenu": "Get Processing Method Menu", "guarantee": "Guarantee", "homePhone": "Home Phone", "hongKong": "Hong Kong", "hour": "Hour", "identity": "Identity", "importantDate": "Important Date", "initiator": "Initiator", "interviewContent": "Interview Content", "interviewMethod": "Interview Method", "interviewPurpose": "Interview Purpose", "interviewSubject": "Interview Subject", "investmentAttribute": "Investment Attribute", "ipsAttribute": "IPS Attribute", "isClosed": "Is Closed", "join": "Join", "joinKeyCustomerGroup": "Join Key Customer Group", "jointAppointmentList": "Joint Appointment List", "jointAppointmentTarget": "Joint Appointment Target", "keyCustomerGroup": "主要な顧客グループ", "lastMaintenanceDate": "Last Maintenance Date", "loan": "Loan", "location": "Location", "managingAO": "Managing AO", "managingPersonnel": "Managing Personnel", "maxUploadAttachmentNote": "Maximum 3 attachment files can be uploaded; each limited to 2MB.", "meeting": "Meeting", "memoryTask": "Memory Task", "menu": "<PERSON><PERSON>", "minorWealthManagementConsent": "Minor Wealth Management and Investment Products Consent", "minute": "Minute", "mobilePhone": "Mobile Phone", "modificationSuccessful": "Modification Successful", "modify": "Modify", "modifyContactRecord": "Modify Contact Record", "month": "Month", "name": "Name", "newGenerated": "New Generated", "newTask": "New Task", "nextMonth": "Next Month", "nextWeek": "Next Week", "no": "No", "noAdvanceNotice": "No Advance Notice", "noCustomerFound": "Customer not found, please confirm if it is a long-inactive customer.", "noDataToUpdate": "No data to update", "noManagement": "No Management", "noPersonnelData": "No personnel data found.", "notClosed": "Not Closed", "notExpired": "期限切れではありません", "notExpiredExcludeNew": "Not Expired (Exclude New)", "note": "Note", "notification": "Notification", "notify": "Notify", "officePhone": "Office Phone", "onAccount": "On Account", "op": "Actions", "openSecondModal": "Open Second Modal", "organization": "Organization (Area / Branch / AO)", "organizationAO": "Organization (AO)", "organizationArea": "Organization (Area)", "organizationBranch": "Organization (Branch)", "password": "Password", "pendingReview": "Pending Review", "pendingReviewList": "Pending Review List", "pendingTasks": "作業予定", "personalNotes": "Personal Notes", "personalNotesItems": "Personal Notes Items", "personalNotesMaintenance": "Personal Notes Maintenance", "personalTask": "Personal Task", "personnel": "Personnel", "pleaseEnter": "Please Enter", "pleaseEnterCompleteCustomerIdTaxId": "Please enter complete Customer ID/Tax ID", "pleaseEnterCustomerIdTaxId": "Please enter Customer ID/Tax ID.", "pleaseEnterReturnReason": "Please enter return reason", "pleaseSelect": "Please Select", "pleaseSelect2": "Please Select", "pleaseSelectCustomer": "Please select customer first.", "pleaseSelectEventToDelete": "Please select event to delete.", "processDateRange": "Process Date Range", "processingContent": "Processing Content", "processingDate": "Processing Date", "processingFollowUp": "Processing Follow-up", "processingMethod": "Processing Method", "processingPerson": "Processing Person", "processingPersonnel": "Processing Personnel", "processingStaff": "Processing Staff", "processingStatus": "Processing Status", "productExpiry": "Product Expiry", "recentContactDate": "Recent Contact Date", "recentProcessor": "Recent Processor", "reminder": "Reminder", "reminderDays": "Reminder Days", "remove": "Remove", "required": "Required", "requiredField": "Required Field", "returnForModification": "Return for Modification", "returnReason": "Return Reason", "reviewCompleted": "Review Completed", "reviewDate": "Review Date", "reviewStatus": "Review Status", "reviewSupervisor": "Review Supervisor", "save": "Save", "saveAndClose": "Save and Close", "schedule": "Schedule", "seal": "Seal", "search": "Search", "searchCond": "Search Criteria", "searchResults": "Search Results", "select": "Select", "selectAtLeastOne": "Please select at least one record.", "selectCustomer": "選ぶ", "serialNumber": "Serial Number", "serviceRecordList": "Service Record List", "serviceType": "Service Type", "setEditData": "Set Edit Data", "setting": "Setting", "singapore": "Singapore", "specificTrustInvestmentConsent": "Specific Trust Customer Investment Securities Recommendation Consent", "statement": "Statement", "status": "Status", "statusChange": "Status Change", "subject": "Subject", "submit": "Submit", "submitDate": "Submit Date", "submitDateRange": "Submit Date Range", "submitDateRangeFrom": "Submit Date Range (From)", "submitDateRangeTo": "Submit Date Range (To)", "success": "Success", "suggest": "Suggest", "taskAssignee": "Task Assignee", "taskCalendar": "Task Calendar", "taskComplete": "Task Completed", "taskDeadline": "Task Deadline", "taskDescription": "Task Description", "taskMgn": "Task Management", "taskOverdue": "Task Overdue", "taskPriority": "Task Priority", "taskProgress": "Task Progress", "taskStatus": "Task Status", "taskType": "Task Type", "thisMonth": "This Month", "thisWeek": "This Week", "time": "Time", "today": "Today", "tomorrow": "Tomorrow", "type": "Type", "unit": "Unit", "updateContactRecord": "Update Contact Record", "updateSuccess": "Update successful", "view": "View", "wealthSpecificCustomer": "Wealth Specific Customer (No Active Recommendation)", "week": "Week", "widget": "Widget", "willAttend": "<PERSON>", "wobCalendar": "Workbench Calendar", "wobContact": "Contact Management", "wobDashboard": "Workbench Dashboard", "wobMeeting": "Meeting Management", "wobMgmt": "Workbench Management", "wobNote": "Notes", "wobReminder": "Reminders", "wobReport": "Work Reports", "wobSchedule": "Schedule Management", "wobStat": "Statistics", "wobTask": "Workbench Tasks", "wobTodo": "To-Do Items", "workDate": "Work Date", "workTime": "Work Time", "years": "years", "yes": "Yes"}