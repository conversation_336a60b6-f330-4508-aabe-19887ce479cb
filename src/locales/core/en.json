{"account": "Account", "accountOrPasswordError": "This account or password is incorrect!", "announcementCategory": "Announcement Category", "announcementContent": "Announcement Content", "announcementDate": "Announcement Date", "announcementMessage": "Announcement Message", "announcementTitle": "Announcement Title", "announcementType": "Announcement Type", "attachedFiles": "Attached Files", "auditWorkBoard": "Audit Work Board", "availableToCustomers": "Available to Customers", "calendar": "Calendar", "calendarComment": "Calendar", "cancel": "Cancel", "capitalInflow": "Capital Inflow", "capitalOutflow": "Capital Outflow", "close": "Close", "closeWindow": "Close Window", "collapseAll": "Collapse All", "confirm": "Confirm", "content": "Content", "createDate": "Create Date", "creator": "Creator", "custom": "Custom", "customerImportantDates": "Customer Important Dates", "customerImportantDatesBlockThumbnail": "Customer Important Dates Block Thumbnail", "customerImportantDatesDesc": "Displays this month's customer important dates, provides quick add to schedule shortcuts", "customerName": "Customer Name", "customerWorkAudit": "Customer Work Audit", "date": "Date", "description": "Description", "designatedListComment": "Designated List", "documentCategory": "Document Category", "documentContent": "Document Content", "documentQuery": "Document Query", "documentTitle": "Document Title", "documentType": "Document Type", "effectiveDate": "Effective Date", "endDate": "End", "entryPage": "This is the entry page", "error": "", "eventCategory": "Event Category", "eventNotification": "Event Notification", "eventNotificationBoard": "Event Notification Board", "eventNotificationBoardDesc": "Integrates various event notifications, displays corresponding statistics by 'Today's New', 'Not Expired', 'Expired' status", "eventNotificationComment": "Event Notification", "eventProcessing": "Event Processing", "expandAll": "Expand All", "expired": "Expired", "expiryDate": "Expiry Date", "filterNoData": "No information found", "filterThenQuery": "Please select the conditions and click on query", "financialInformation": "Financial Information", "forcedReading": "Forced Reading", "forgotPassword": "Forgot Password", "functionDescription": "Function Description", "functionName": "Function Name", "generalAnnouncements": "General Announcements", "generalAnnouncementsBlockThumbnail": "General Announcements Block Thumbnail", "generalAnnouncementsDesc": "Displays 7 major categories of announcements: 'Projects/Activities', 'Fund Business', 'Insurance Business', 'Common Documents', 'Product Documents', 'Marketing Documents', 'Others'", "generalWorkAudit": "General Work Audit", "highAssetQualificationApplication": "High Asset Qualification Application", "homepage": "Homepage", "homepageDisplayComponentsComment": "Homepage Display Components", "importance": "Importance", "investmentPrincipal": "Investment Product Principal", "issueTracking": "Issue Tracking", "issuingInstitution": "Issuing Institution", "item": "<PERSON><PERSON>", "itinerary": "Itinerary", "jumpToLoginPage": "Jump to login page", "language": "Language", "lastMaintainer": "Last Maintainer", "lastMaintenanceDate": "Last Maintenance Date", "link": "Link", "locales": {"en": "English", "ja": "Japanese", "zh-TW": "Traditional Chinese"}, "login": "<PERSON><PERSON>", "logout": "Logout", "logoutPbs": "Logout PBS", "mainCategory": "Main Category", "maintenanceInfo": "Maintenance Info", "memberLogin": "Member <PERSON><PERSON>", "merchantNumber": "Merchant Number", "message": "Message", "messageNotification": "Message Notification", "more": "More", "netCapitalChange": "Net Capital Change", "newGenerated": "New Generated", "newGeneration": "New generation", "nonInvestmentBalance": "Non-Investment Product Balance", "notExpired": "Not expired", "numberOfEventsPending": "Number of events (pending)", "pageContentEnd": "Page Content End", "password": "Password", "pbsLogoutMessage": "You have logged out of PBS, please close the window or login again", "pending": "Pending", "pendingAudit": "Pending <PERSON>t", "pendingAuditCount": "Pending Audit Count", "pendingAuditItems": "Pending Audit Items", "pendingAuditListComment": "Pending Audit List", "pendingItems": "Pending Items", "personalizeHomepageSettings": "Personalize Homepage Settings", "pleaseCheckImNotRobot": "Please check 'I'm not a robot' to complete verification.", "pleaseEnterPassword": "Please enter password", "productDocumentsComment": "Product Documents", "productWorkAudit": "Product Work Audit", "quickSearch": "Quick Search", "quickSearchBlockThumbnail": "Quick Search Block Thumbnail", "quickSearchDesc": "Provides quick search function for customer ID and name", "reLogin": "Re-login", "realizedPnl": "Realized P&L", "researchInvestment": "Research & Investment", "researchInvestmentBlockThumbnail": "Research & Investment Block Thumbnail", "researchInvestmentDesc": "Displays 5 major categories of announcements: 'Market News & Research Reports', 'Featured Project Funds', 'Asset Allocation Recommendations', 'Tax Column', 'Others'", "save": "Save", "selectRole": "Select Role", "sender": "Sender", "serviceChangeApplication": "Service Change Application", "showOnHomepage": "Show on Homepage", "shutdownAnnouncement": "Shutdown Announcement", "startDate": "Start", "stepOneSelectFunctions": "Step 1: Select Function Items", "stepTwoDragBlocks": "Step 2: Drag blocks to adjust your homepage layout", "subCategory": "Sub Category", "subject": "Subject", "submittedForAudit": "Submitted for Audit", "summary": "Summary", "time": "Time", "todayCustomers": "Today's Customers", "todayItinerary": "Today's Itinerary", "todayMemos": "Today's Memos", "todayMemosBlockThumbnail": "Today's Memos Block Thumbnail", "todayMemosDesc": "Displays today's memo items, provides quick add shortcuts", "todaySchedule": "Today's Schedule", "todayScheduleBlockThumbnail": "Today's Schedule Block Thumbnail", "todayScheduleDesc": "Displays today's phone/visit schedule, provides schedule management shortcuts", "totalCases": "Total Cases", "underReview": "Under Review", "unrealizedPnl": "Unrealized P&L", "uploadFile": "Upload File", "urgencyLevel": "Urgency Level", "user": "User", "userSelection": "User Selection", "validDate": "Valid Date", "view": "View"}