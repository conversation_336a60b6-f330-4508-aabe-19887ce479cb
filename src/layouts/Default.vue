<template>
	<div class="h-dvh p-0">
		<content-header />
		<aside-menu @click-side="onClickSide" />
		<edit-tab-name-modal ref="editTabNameModal" v-model:tab-name-prop="editableTabsValue" />
		<div class="content content-fixed container-fluid">
			<DynamicTitle v-if="showBreadcrumb" />
			<RouterView />
		</div>
	</div>
</template>

<script>
import { LAYOUT_LIST } from './layoutData.js';
import contentHeader from './components/contentHeader.vue';
import asideMenu from './components/asideMenu.vue';
import editTabNameModal from './components/editTabNameModal.vue';
import DynamicTitle from './components/dynamicTitle.vue';

export default {
	components: {
		contentHeader,
		asideMenu,
		editTabNameModal,
		DynamicTitle
	},
	data() {
		return {
			tabList: [],
			editableTabsValue: '',
			tabClickCount: 0
		};
	},
	computed: {
		...mapStores(useTabStatusStore),
		tabsList() {
			return this.tabStatusStore.tabsList;
		},
		showBreadcrumb() {
			return this.$route.meta.showBreadcrumb ?? true;
		}
	},
	watch: {
		tabsList: {
			handler(newVal) {
				if (newVal && newVal.length > 0) {
					this.editableTabsValue = newVal[newVal.length - 1].key;
				}
				else {
					this.editableTabsValue = '首頁';
				}
			},
			immediate: true
		}
	},
	methods: {
		getContainerCSS(layoutId) {
			const layout = LAYOUT_LIST.find(layout => layout.LayoutId === layoutId);
			if (layout) {
				return layout.LayoutGrid.Container;
			}
			return null;
		},
		removeTab(targetName) {
			this.tabStatusStore.removeTab({ name: targetName });
			if (this.editableTabsValue !== targetName) return;
			const tabs = this.tabList;
			this.editableTabsValue = tabs.length > 0
				? tabs[tabs.length - 1].key
				: this.$t('core.homepage');
		},
		editTabName(tab) {
			this.editableTabsValue = tab.paneName;
			if (tab.paneName === this.$t('core.homepage')) return; // If it's homepage, don't process
			this.tabClickCount++;
			if (this.tabClickCount >= 2) {
				this.$refs.editTabNameModal.show();
				this.tabClickCount = 0; // 重置點擊計數
			}
			else {
				setTimeout(() => {
					this.tabClickCount = 0; // Reset count if no second click between two clicks
				}, 300); // Set delay time to distinguish single click from double click
			}
		},
		onClickSide() {
			this.editableTabsValue = $t('gen.homepage');
		}
	}
};
</script>
<style scoped lang="css">

.h-dvh {
	height: 100vh;
}

</style>
