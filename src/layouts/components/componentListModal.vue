<template>
	<div
		ref="componentListModal"
		class="modal fade show"
		aria-modal="true"
		tabindex="-1"
	>
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title">
						元件選擇
					</h4>
					<button
						type="button"
						class="btn-close"
						data-bs-dismiss="modal"
						aria-label="Close"
					/>
				</div>
				<div class="modal-body">
					<el-checkbox-group v-model="checkList">
						<el-checkbox
							v-for="(item, index) in COMPONENT_LIST"
							:key="index"
							:label="item.Name"
							:value="item.Name"
						/>
					</el-checkbox-group>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
						取消
					</button>
					<button type="button" class="btn btn-primary" @click="handleConfirm">
						確認
					</button>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { Modal } from 'bootstrap';
import { COMPONENT_LIST } from '../layoutData.js';

export default {
	props: {
		components: {
			type: Array,
			default: () => []
		}
	},
	emits: ['components'],
	data: function () {
		return {
			componentListModal: {},
			selectedLayout: null,
			checkList: [],
			COMPONENT_LIST
		};
	},
	watch: {
		components: {
			immediate: true,
			handler(newVal) {
				this.checkList = [...newVal];
			}
		}
	},
	created() {
		this.checkList = [...this.$props.components];
	},
	mounted: function () {
		// 確保在 Vue 完成渲染後再初始化 Modal
		this.$nextTick(() => {
			this.componentListModal = new Modal(this.$refs.componentListModal); // 在 Vue 渲染完成後初始化
		});
	},
	methods: {
		show: function () {
			this.componentListModal.show();
		},
		select: function (layout) {
			this.selectedLayout = layout;
		},
		handleConfirm: function () {
			this.$emit('components', this.checkList);
			this.checkList = [];
			this.componentListModal.hide();
		}
	}
};
</script>
