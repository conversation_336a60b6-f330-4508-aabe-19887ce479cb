<template>
	<div class="navbar navbar-header navbar-header-fixed">
		<div class="marquee">
			<template v-for="item in messageList">
				<span>{{ $filters.formatDate(item.validBgnDt) }}</span>
				{{ item.msgTitle }}
			</template>
		</div>
		<div class="navbar-brand">
			<!-- 改寫aside-menu-link javascript -->
			<Hamburger class="aside-menu-link" @click="toggleAsideMenu" />
			<router-link to="/">
				<img :src="getImgURL('logo', 'bi_logo.svg')" alt="logo_img">
			</router-link>
		</div>
		<nav class="navbar-right">
			<!-- <button type="button" class="btn btn-secondary btn-sm ms-30" @click="addTab">+</button> -->
			<!-- <div style="margin-bottom: 20px">
				<el-button size="small" @click="addTab"> add tab </el-button>
			</div> -->
			<router-link
				to="/"
				class="nav-link"
				data-bs-toggle="tooltip"
				title="首頁"
				data-bs-placement="bottom"
			>
				<i class="bi bi-laptop-fill" />
			</router-link>
			<router-link
				to="/wob/taskCalendar"
				class="nav-link"
				data-bs-toggle="tooltip"
				title="工作日曆"
				data-bs-placement="bottom"
			>
				<i class="fa-solid fa-blue fas fa-calendar" aria-hidden="true" />
			</router-link>
			<router-link
				to="/cus/cusSearch"
				class="nav-link"
				data-bs-toggle="tooltip"
				title="客戶查詢"
				data-bs-placement="bottom"
			>
				<i class="fa-solid fa-user-tag" />
			</router-link>
			<div class="dropdown dropdown-message">
				<a class="nav-link dropdown-link" data-bs-toggle="dropdown" href="#">
					<i class="fa-solid fa-blue fa-comment" />
					<span v-if="genInternalMsg.length > 0" class="badge bg-danger text-bg-dark badge-sm">
						{{ genInternalMsg.length }}
					</span>
				</a>
				<div class="dropdown-menu dropdown-menu-right">
					<div class="dropdown-header">
						訊息通知
					</div>
					<a v-for="item in genInternalMsg" href="#" class="dropdown-item">
						<div class="media media-border align-items-start">
							<div class="avatar">
								<img :src="getImgURL('icon', 'icon-alert.png')">
							</div>
							<div class="media-body">
								<h6 class="bold">
									{{ item.senderUserCode }}
									{{ item.userName }}
								</h6>
								{{ item.msgContent }}<br>
								<span>{{ $filters.formatDateTime(item.msgDatetime) }}</span>
							</div>
						</div>
					</a>
					<div class="dropdown-footer">
						<a href="#" @click.prevent="openInternalMsg()">更多訊息 <i class="fa fa-angle-right" /></a>
					</div>
				</div>
			</div>
			<div class="dropdown dropdown-notification">
				<a class="nav-link dropdown-link" data-bs-toggle="dropdown" href="#">
					<i class="fa-solid fa-blue fa-square-plus" />
				</a>
				<div class="dropdown-menu dropdown-menu-right">
					<div class="dropdown-header">
						快速新增
					</div>
					<a class="dropdown-item" @click.prevent="newTask(1)">
						<i class="fa-solid fa-address-card" />
						<b> 新增聯繫紀錄</b>
					</a>
					<a class="dropdown-item" @click.prevent="newTask(2)"> <i class="fas fa-calendar" /> <b> 新增約訪行程</b> </a>
				</div>
			</div>
			<div class="dropdown dropdown-profile">
				<a href="" class="dropdown-link" data-bs-toggle="dropdown">
					<div class="tx-user">
						<span id="current-user">{{ userName }} {{ userCode }} {{ userRoleName }}</span>
						<i class="bi bi-caret-down-fill" />
					</div>
				</a>
				<div class="dropdown-menu dropdown-menu-right tx-13 dropdown-menu-work">
					<router-link to="/selectpos" class="dropdown-item">
						<p class="mb-2 tx-12 tx-color-03">
							切換代理人
						</p>
					</router-link>
					<div class="dropdown-divider" />
					<span class="mb-2 tx-12 tx-color-03" data-bs-toggle="tooltip" title="切換系統角色">
						切換系統角色
					</span>
					<a
						v-for="identity in switchIdentityList"
						href="#"
						class="nav-link"
						@click.prevent="changeRole(identity.posCode)"
					>
						{{ identity.posName }}
					</a>
					<div class="dropdown-divider" />
					<a
						href="#"
						class="nav-link"
						data-bs-toggle="tooltip"
						title="登出"
						@click.prevent="clickLogout()"
					>
						Sign Out
					</a>
				</div>
			</div>
			<i18nSelector style="color: white;" iconize />
		</nav>
	</div>

	<layout-list-modal
		ref="layoutListModal"
		v-model:selected-layout-id="selectedLayoutId"
		@next-step="() => (selectedLayoutId == 0 ? $refs.featureListModal.show() : $refs.layoutDisplayModal.show())"
	/>
	<component-list-modal ref="componentListModal" v-model:selected-layout-id="selectedLayoutId" />
	<layout-display-modal ref="layoutDisplayModal" v-model:selected-layout-id="selectedLayoutId" />
	<feature-list-modal ref="featureListModal" />

	<vue-wob-new-task-modal ref="newTaskModal" />
	<msg ref="internalMsg" />
</template>

<script>
import msg from './msg.vue';
import layoutListModal from './layoutListModal.vue';
import componentListModal from './componentListModal.vue';
import layoutDisplayModal from './layoutDisplayModal.vue';
import featureListModal from './featureListModal.vue';
import { getImgURL } from '@/utils/imgURL.js';
import vueWobNewTaskModal from '@/views/wob/WOB0400/include/newTaskModalForHeader.vue';
import i18nSelector from '@/views/components/i18nSelector.vue';
import { mapStores } from 'pinia';
import { useUserInfoStore } from '@/stores/userInfo';

export default {
	components: {
		layoutListModal,
		msg,
		componentListModal,
		layoutDisplayModal,
		featureListModal,
		vueWobNewTaskModal,
		i18nSelector
	},
	data() {
		return {
			userName: null,
			userRoleName: null,
			userCode: null,
			genInternalMsg: [],
			messageList: [],
			switchIdentityList: [],
			selectedLayoutId: null
		};
	},
	computed: {
		...mapStores(useUserInfoStore),
		userInfo() {
			return this.userInfoStore.userInfo;
		}
	},
	watch: {
		userInfo: function (newVal) {
			if (newVal) {
				if (newVal.deputyUserName) {
					this.userName = newVal.deputyUserName;
				}
				else {
					this.userName = newVal.userName;
				}
				this.userRoleName = newVal.roleName;
				this.userCode = newVal.userCode;
			}
		},
		userCode: function (newVal) {
			if (!newVal) return;
			this.getSwitchIdentityList(newVal);
		}
	},
	async mounted() {
		this.userInfoStore.getInfo();
		const ret = await this.getGenInternalMsg('Y');
		this.genInternalMsg = ret.data;
		this.getMessageList();
	},
	methods: {
		toggleAsideMenu: function () {
			document.querySelector('.aside.aside-fixed').classList.toggle('minimize');
			document.querySelector('.aside-menu-link').classList.toggle('show-aside');
		},
		getImgURL,
		clickLogout: function () {
			this.$bi.confirm('您確定要登出嗎?', {
				event: {
					confirmOk: () => {
						sessionStorage.setItem('beforeShutDown', 'false');
						this.logout();
					}
				}
			});
		},
		logout: function () {
			this.$router.push('/login');
		},
		getGenInternalMsg: async function (forceYn) {
			const ret = await this.$api.getGenInternalMsgApi({ forceYn });
			return ret;
		},
		getMessageList: async function () {
			const ret = await this.$api.getMessageListApi('M');
			this.messageList = ret.data;
		},
		// todo: 待新增 newTaskModal
		newTask: function (sectionCode) {
			this.$refs.newTaskModal.getSelectVisitPurpose();
			this.$refs.newTaskModal.getSelectIdentity();
			this.$refs.newTaskModal.getSelectVisitType();
			this.$refs.newTaskModal.getSelectPlaceType();
			this.$refs.newTaskModal.getSelectAttBranches();
			this.$refs.newTaskModal.getSelectContactPurpose();
			this.$refs.newTaskModal.getSelectContactType();
			this.$refs.newTaskModal.getSelectReuseWordSelf();
			this.$refs.newTaskModal.show(sectionCode);
		},
		getSwitchIdentityList: async function (userCode) {
			const ret = await this.$api.getSwitchIdentityListApi(userCode);
			this.switchIdentityList = ret.data;
		},
		changeRole: async function (posCode) {
			await this.userInfoStore.switchRole(posCode);
		},
		openInternalMsg: async function () {
			await this.$refs.internalMsg.getGenInternalMsg();
			this.$refs.internalMsg.show();
		},
		addTab: function () {
			this.$refs.layoutListModal.show();
		}
	}
};
</script>
