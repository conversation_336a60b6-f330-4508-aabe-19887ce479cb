<template>
	<div
		ref="layoutListModal"
		class="modal fade show"
		aria-modal="true"
		tabindex="-1"
	>
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title">
						版面選擇
					</h4>
					<button
						type="button"
						class="btn-close"
						data-bs-dismiss="modal"
						aria-label="Close"
					/>
				</div>
				<div class="modal-body">
					<div>
						<div v-for="(layout, index) in layoutList" :key="index" class="row mb-3">
							<div
								v-for="(item, idx) in layout"
								:key="idx"
								class="col-sm-6 mb-3 mb-sm-0"
								style="cursor: pointer"
							>
								<div class="card" :class="{ 'border border-danger': selectedLayout === item.Id }" @click="select(item.Id)">
									<img
										:src="getImgURL('icon', item.Img)"
										class="card-img-top"
										:alt="`Layout Image ${item.Id}`"
										draggable="false"
									>
									<div class="card-body">
										<h5 class="card-title text-center">
											{{ item.Name }}
										</h5>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
						取消
					</button>
					<button
						type="button"
						class="btn btn-primary"
						:disabled="selectedLayout == null"
						@click="handleConfirm"
					>
						確認
					</button>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { Modal } from 'bootstrap';
import { layoutSelectorList } from '../layoutData.js';
import { getImgURL } from '@/utils/imgURL';

export default {
	props: {
		selectedLayoutId: {
			type: Number,
			default: null
		}
	},
	emits: ['update:selectedLayoutId', 'nextStep'],
	data: function () {
		return {
			layoutListModal: {},
			chunkSize: 2, // 每行顯示的項目數量
			selectedLayout: null
		};
	},
	computed: {
		layoutList: function () {
			return layoutSelectorList.reduce((acc, curr, idx) => {
				if (idx % this.chunkSize === 0) {
					acc.push([curr]);
				}
				else {
					acc[acc.length - 1].push(curr);
				}
				return acc;
			}, []);
		}
	},
	mounted: function () {
		// 確保在 Vue 完成渲染後再初始化 Modal
		this.$nextTick(() => {
			this.layoutListModal = new Modal(this.$refs.layoutListModal); // 在 Vue 渲染完成後初始化
		});
	},
	methods: {
		show: function () {
			this.layoutListModal.show();
		},
		select: function (layout) {
			this.selectedLayout = layout;
		},
		handleConfirm: function () {
			this.$emit('update:selectedLayoutId', this.selectedLayout);
			this.layoutListModal.hide();
			this.$emit('nextStep', true);
			this.selectedLayout = null;
		},
		getImgURL: function (imgType, imgName) {
			return getImgURL(imgType, imgName);
		}
	}
};
</script>
