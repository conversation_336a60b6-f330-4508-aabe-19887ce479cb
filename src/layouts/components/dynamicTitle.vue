<template>
	<header :class="{'cursor-pointer': ENABLE_COPY_FUNCTION}" @click="onClick">
		<h2>
			{{ pageTitle }}
		</h2>
		<nav>
			<ol class="breadcrumb breadcrumb-style2">
				<li v-if="menuPath?.length > 0" class="breadcrumb-item">
					<RouterLink to="/" replace>
						<i class="bi bi-house" />
					</RouterLink>
				</li>
				<template v-for="(item, index) in menuPath">
					<li class="breadcrumb-item" :class="{ active: index == menuPath.length - 1 }">
						{{ index == menuPath.length - 1 ? pageTitle : item }}
					</li>
				</template>
			</ol>
		</nav>
	</header>
</template>

<script setup>
import { useRoute } from 'vue-router';

const ENABLE_COPY_FUNCTION = import.meta.env.VITE_ENABLE_COPY_FUNCTION === 'true';

const menuStore = useMenusStore();
const route = useRoute();
const menus = computed(() => menuStore.menus ?? []);

const path = computed(() => route.path.split('/').slice(0, 3).join('/'));

const menuPath = computed(() => getMenuPath(menus.value ?? []));
const pageTitle = computed(() => menuPath.value.at(-1) || '');

watch(
	[menus, path],
	() => {
		getMenuPath(menus.value);
	},
	{ immediate: true, deep: true }
);

function getMenuPath(menusArr) {
	for (const menu of menusArr) {
		if (menu.url && path.value === menu.url) return [menu?.name];
		if (!menu.nodes?.length) continue;
		const subPath = getMenuPath(menu.nodes);
		if (subPath.length) return [menu?.name, ...subPath];
	}
	return [];
}

async function onClick() {
	if (!ENABLE_COPY_FUNCTION) return;
	await navigator.clipboard.writeText(`[FRF][${menuPath.value.join('_')}]`);
}
</script>
