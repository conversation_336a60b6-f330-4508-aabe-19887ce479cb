<template>
	<div
		ref="featureListModal"
		class="modal fade show"
		aria-modal="true"
		tabindex="-1"
	>
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title">
						功能選擇
					</h4>
					<button
						type="button"
						class="btn-close"
						data-bs-dismiss="modal"
						aria-label="Close"
					/>
				</div>
				<div class="modal-body">
					<div>
						<el-tree
							ref="featureTree"
							:data="menus"
							:props="treeProps"
							show-checkbox
							node-key="code"
							@check-change="handleCheckChange"
						/>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
						取消
					</button>
					<button type="button" class="btn btn-primary" @click="handleConfirm">
						確認
					</button>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { Modal } from 'bootstrap';

export default {
	data: function () {
		return {
			featureListModal: {},
			treeProps: {
				children: 'nodes',
				label: 'name',
				disabled: data => /^M\d$/.test(data.code)
			},
			checkedKeys: []
		};
	},
	computed: {
		...mapState(useMenusStore, ['menus']),
		...mapStores(useTabStatusStore)
	},
	mounted: function () {
		// 確保在 Vue 完成渲染後再初始化 Modal
		this.$nextTick(() => {
			this.featureListModal = new Modal(this.$refs.featureListModal); // 在 Vue 渲染完成後初始化
		});
	},
	methods: {
		show: function () {
			this.featureListModal.show();
		},
		handleConfirm: function () {
			const checkedData = this.$refs.featureTree.getCheckedNodes();
			if (checkedData.length === 0) {
				this.$swal.fire({
					text: '請至少選擇一個功能',
					icon: 'error'
				});
				return;
			}
			console.log('Selected Features:', checkedData);
			const level2Node = checkedData.filter(item => /^M\d\d$/.test(item.code));
			const windowComponents = [
				{
					WindowId: 0,
					WindowName: level2Node.length > 0 ? level2Node[0].name : '未命名',
					Components: checkedData.filter(item => !/^M\d\d$/.test(item.code)).map(item => item.name)
				}
			];
			this.tabStatusStore.setTabList({
				layoutId: 0,
				tabName: checkedData.find(item => /^M\d\d$/.test(item.code))?.name || checkedData[0].name || '未命名',
				components: windowComponents
			});
			this.featureListModal.hide();
			this.$refs.featureTree.setCheckedKeys([]);
		},
		handleCheckChange: function (node, checked) {
			if (!checked) return; // 不處理取消勾選

			const isChildNode = node.code.includes('-');
			const parentCode = node.code.split('-')[0];
			if (isChildNode && this.checkedKeys.includes(parentCode)) {
				this.checkedKeys.push(node.code);
			}
			else {
				this.checkedKeys = [];
				this.checkedKeys.push(node.code);
			}

			// 取消其他勾選
			this.$refs.featureTree.setCheckedKeys(this.checkedKeys);
		}
	}
};
</script>
