<template>
	<div
		ref="internalMsg"
		class="modal fade show"
		aria-modal="true"
		role="dialog"
		style="display: none; padding-left: 0px"
	>
		<div class="modal-dialog modal-dialog-centered modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title">
						{{ $t('gen.messageNotification') }}
					</h4>
					<button
						type="button"
						class="btn-close"
						data-bs-dismiss="modal"
						aria-label="Close"
					/>
				</div>
				<div class="modal-body">
					<div class="card">
						<div class="card-body">
							<table class="table table-bordered table-RWD table-horizontal-RWD">
								<tr>
									<th class="w20">
										{{ $t('gen.date') }}
									</th>
									<th class="w70">
										{{ $t('gen.content') }}
									</th>
									<th class="w10">
										{{ $t('gen.sender') }}
									</th>
								</tr>
								<tr v-for="item in genInternalMsg">
									<td>{{ $filters.formatDateTime(item.msgDatetime) }}</td>
									<td>{{ item.msgContent }}</td>
									<td>{{ item.senderUserCode }} {{ item.userName }}</td>
								</tr>
							</table>
						</div>
					</div>
				</div>
				<div class="modal-footer d-flex justify-content-end">
					<input
						name="btnClose"
						class="btn btn-white"
						type="button"
						:value="$t('gen.close')"
						data-bs-dismiss="modal"
					>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import Modal from 'bootstrap/js/dist/modal.js';
export default {
	props: {
		cusCode: null
	},
	data: function () {
		return {
			genInternalMsg: [],
			internalMsg: {}
		};
	},
	mounted: function () {
		// 確保在 Vue 完成渲染後再初始化 Modal
		this.$nextTick(() => {
			this.internalMsg = new Modal(this.$refs.internalMsg); // 在 Vue 渲染完成後初始化
		});
	},
	methods: {
		getGenInternalMsg: async function (forceYn) {
			const ret = await this.$api.getGenInternalMsgApi({ forceYn });
			this.genInternalMsg = ret.data;
		},
		isEmpty: function () {
			return this.$_.isEmpty(this.genInternalMsg);
		},
		show: function () {
			this.internalMsg.show();
		}
	}
};
</script>
