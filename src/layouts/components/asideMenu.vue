<template>
	<aside class="aside aside-fixed">
		<div class="aside-body">
			<div v-if="!isProduction && highlightProblemLink">
				<div style="color: #d2a100">
					前端沒有設定對應頁面
				</div>
				<div style="color: red">
					後端沒有設定 URL
				</div>
				<div style="color: blue">
					後端沒有設定 URL 與子節點
				</div>
			</div>
			<ul class="nav nav-aside nav-crm" :class="{'nav-highlight-problem-link' : highlightProblemLink}">
				<template v-for="node0 in menus">
					<li class="nav-label">
						{{ node0.name }}
					</li>
					<div>
						<li
							v-for="node1 in node0.nodes"
							:id="node1.code"
							class="nav-item"
							:class="{
								'with-sub': node1.nodes != null,
								show: selectedMenu[node1.code],
							}"
						>
							<RouterLink
								v-if="node1.url"
								v-slot="{isActive, href, navigate, route}"
								:to="node1.url"
								custom
							>
								<a
									:href
									class="nav-link"
									style="cursor: pointer; height: fit-content;"
									:class="{ active: isActive, 'no-page': route.name === '401' }"
									@click="navigate"
								>
									<span>{{ node1.name }}</span>
								</a>
							</RouterLink>
							<a
								v-else
								disabled
								class="nav-link"
								:class="{'no-url-nodes': !node1.nodes}"
								style="height: fit-content;"
								@click="toggleSubMenu(node1.code)"
							>
								<span>{{ node1.name }}</span>
							</a>
							<ul v-show="selectedMenu[node1.code]">
								<template v-for="node2 in node1.nodes">
									<RouterLink
										v-if="node2.url"
										:key="node2.url"
										v-slot="{ isActive, href, navigate, route }"
										:to="node2.url"
										custom
									>
										<li :class="{active: isActive}">
											<a
												:href
												:class="{ 'no-page': route.name === '401' }"
												@click="navigate"
											>
												{{ node2.name }}
											</a>
										</li>
									</RouterLink>
									<li v-else>
										<span class="no-url">{{ node2.name }}</span>
									</li>
								</template>
							</ul>
						</li>
					</div>
				</template>
			</ul>
		</div>
	</aside>
</template>

<script>
export default {
	emits: ['clickSide'],
	data: function () {
		return {
			highlightProblemLink: import.meta.env.VITE_HIGHLIGHT_PROBLEM_LINK === 'true',
			selectedMenu: {},
			isProduction: import.meta.env.MODE === 'production'
		};
	},
	computed: {
		...mapStores(useMenusStore),
		path: function () {
			return this.$route.path.split('/').slice(0, 3).join('/');
		},
		menus() {
			return this.menusStore.menus;
		}
	},
	watch: {
		path: function () {
			this.expandCurrentMenu();
		},
		menus: function () {
			this.expandCurrentMenu();
		}
	},
	mounted: async function () {
		await this.menusStore.getMenus();
	},
	methods: {
		expandCurrentMenu: function () {
			this.hideOtherSubMenu();
			const findAndExpand = (nodes) => {
				if (!nodes) return false;
				for (const node of nodes) {
					if (this.isCurrentPath(node.url)) {
						const menuCode = this.formatMenuCode(node.code);
						this.showSubMenu(menuCode);
						// this.scrollToMenu(menuCode);
						return true;
					}
					if (findAndExpand(node.nodes)) {
						return true;
					}
				}
				return false;
			};
			findAndExpand(this.menus);
		},
		formatMenuCode: function (menuCode) {
			return menuCode.includes('-') ? menuCode.split('-')[0] : menuCode;
		},
		toggleSubMenu: function (menuCode) {
			const self = this;
			self.selectedMenu[menuCode] = !self.selectedMenu[menuCode];
			this.hideOtherSubMenu(menuCode);
		},
		hideOtherSubMenu: function (menuCode) {
			const self = this;
			for (const key in self.selectedMenu) {
				if (key != menuCode) {
					self.selectedMenu[key] = false;
				}
			}
		},
		showSubMenu: function (menuCode) {
			const self = this;
			self.selectedMenu[menuCode] = true;
		},
		// 比較傳入的 Api 路徑, 是否與當前網址列路徑相符合
		isCurrentPath: function (targetPath) {
			const self = this;
			if (this.$_.isBlank(self.path)) {
				return false;
			}
			return self.path === targetPath;
		},
		scrollToMenu: function (menuCode) {
			const targetElement = document.getElementById(menuCode);
			if (targetElement) {
				// const offsetTop = targetElement.offsetTop - 100;
				const offsetTop = 0;
				window.scrollTo({
					top: offsetTop,
					behavior: 'smooth'
				});
			}
		}
	}
};
</script>

<style lang="css" scoped>
.nav.nav-highlight-problem-link .no-page {
	color: #d2a100 !important;
	cursor: not-allowed;
	pointer-events: none;
}

.nav.nav-highlight-problem-link .no-url {
	color: red;
	cursor: not-allowed;
	pointer-events: none;
}

.nav.nav-highlight-problem-link .no-url-nodes {
	color: blue;
	cursor: not-allowed;
	pointer-events: none;
}

</style>
