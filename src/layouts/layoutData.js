import { shallowRef } from 'vue';
import tabView from '@/views/components/tabView.vue';
import ProductMgt from '@/views/pro/productInfo/productMgt.vue';
import ProductSearch from '@/views/pro/PRO0101/productSearch.vue';
import WkfProcessor from '@/views/wkf/wkfProcessor.vue';

export const layoutSelectorList = [
	{
		Name: '功能捷徑',
		Img: 'layout00.png',
		Id: 0
	},
	{
		Name: '版面1',
		Img: 'layout01.png',
		Id: 1
	},
	{
		Name: '版面2',
		Img: 'layout02.png',
		Id: 2
	},
	{
		Name: '版面3',
		Img: 'layout03.png',
		Id: 3
	},
	{
		Name: '版面4',
		Img: 'layout04.png',
		Id: 4
	}
];

export const COMPONENT_LIST = [
	{ Name: '元件一', Id: 1, Component: shallowRef(tabView) },
	{ Name: '元件二', Id: 2, Component: shallowRef(tabView) },
	{ Name: '元件三', Id: 3, Component: shallowRef(tabView) },
	{ Name: '元件四', Id: 4, Component: shallowRef(tabView) },
	{ Name: '元件五', Id: 5, Component: shallowRef(tabView) },
	{ Name: '元件六', Id: 6, Component: shallowRef(tabView) },
	{ Name: '元件七', Id: 7, Component: shallowRef(tabView) },
	{ Name: '元件八', Id: 8, Component: shallowRef(tabView) },
	{ Name: '商品資料查詢', Id: 9, Component: shallowRef(ProductSearch) },
	{ Name: '商品資料維護', Id: 10, Component: shallowRef(ProductMgt) },
	{ Name: '商品資料審核', Id: 11, Component: shallowRef(WkfProcessor) }
];
export const LAYOUT_LIST = [
	{
		LayoutId: 0,
		NumberOfWindows: 1,
		LayoutGrid: {
			Container: '',
			Children: ['']
		}
	},
	{
		LayoutId: 1,
		NumberOfWindows: 2,
		LayoutGrid: {
			Container: 'd-flex flex-wrap w-100 gap-2 overflow-auto',
			Children: ['flex-fill', 'flex-fill']
		}
	},
	{
		LayoutId: 2,
		NumberOfWindows: 2,
		LayoutGrid: {
			Container: 'd-flex flex-column gap-2 overflow-auto',
			Children: ['w-100 flex-grow-1', 'w-100 flex-grow-1']
		}
	},
	{
		LayoutId: 3,
		NumberOfWindows: 3,
		LayoutGrid: {
			Container: 'd-flex flex-wrap w-100 gap-2 overflow-auto',
			Children: ['flex-fill', 'flex-fill', 'flex-fill']
		}
	},
	{
		LayoutId: 4,
		NumberOfWindows: 3,
		LayoutGrid: {
			Container: 'd-grid gap-2 overflow-auto',
			Children: ['span-2-cols', '', '']
		}
	}
];
