# pbs-web

![Vite 6](https://img.shields.io/badge/Vite%206-B73BFE?style=for-the-badge&logo=vite&logoColor=FFD62E)
![Vue 3](https://img.shields.io/badge/Vue%203-35495E?style=for-the-badge&logo=vuedotjs&logoColor=4FC08D)

## Swagger

[Swagger API UI](http://*************:8081/frf-api/swagger-ui/index.html)

## 目錄結構

```txt
pbs-web/
├── public/ # 靜態資源目錄
| | - SUPPORT_LOCALES.json 可使用的語言
│ ├── validate/ # 表單驗證自定義語言設定（VeeValidate）
│ │ └── zh_TW.json # 部分規則自定義的繁體中文訊息
│ └── favicon.ico # 網站頁籤icon
├── src/ # 源碼目錄
│ ├── api/ # api 檔案目錄
│ │ │ └── mock/ # mock API 目錄
│ │ ├── apiService.js # 依環境載入不同 API
│ │ └── ...
│ ├── assets/ # 靜態資源（圖片、CSS、字型等）
│ │ └── css/ # CSS目錄
│ │ ├── bi/ # 自定義的 CSS 檔案目錄
│ │ │ ├── base.css
│ │ │ └── ...
│ │ └── framework-plugin/ # 套件 CSS 目錄
│ │ ├── fonts/
│ │ │ ├── @fortawesome/
│ │ │ │ └── ...
│ │ │ ├── bs-font/
│ │ │ │ └── ...
│ │ │ └── line-awesome/
│ │ │ └── ...
│ │ ├── fullcalendar/
│ │ │ └── main.min.css
│ │ ├── vue/
│ │ │ └── vue-loading.css
│ │ ├── bootstrap-icons.css
│ │ └── ...
│ ├── filters/ # filters目錄
│ │ └── filter.js # 自定義的全域fikter
│ ├── router/ # Vue Router 配置
│ │ ├── index.js # 主路由設定檔案
│ │ ├── adm.js # 管理員區域路由設定檔案
│ │ └── ... # 更多區域路由設定檔案
│ ├── stores/ # Pinia 狀態管理
│ │ ├── menus.js
│ │ └── userInfo.js
│ ├── utils/ # 工具函數目錄
│ │ ├── bi/ # 自定義的函式庫
│ │ │ ├── base.js # 自定義 AJAX 封裝
│ │ │ └── module.js # sweetalert
│ │ ├── mixin/
│ │ │ └── userCodeComplement.js
│ │ ├── validate/
│ │ │ └── file.js # 檔案驗證規則(大小、類型等)
│ │ └── lodashExtensions.js # 自定義的 lodash 擴展函式
│ ├── views/ # 頁面目錄
│ │ ├── HomeView.vue # 首頁
│ │ ├── Login.vue
│ │ ├── ...
│ │ │── components/ # 通用元件
│ │ │ ├── model.vue
│ │ │ ├── biTree.vue
│ │ │ └── ...
│ │ │── adm/
│ │ │ ├── ADM0101/
│ │ │ │ └── admRole.vue
│ │ │ ├── ADM0103/
│ │ │ │ └── admMenuPreview.vue
│ │ │ └── ...
│ │ ├── cus/
│ │ │ ├── CUS003/
│ │ │ │ └── cusAll.html.vue
│ │ │ └── ...
│ │ └── ... # 更多區域頁面
│ ├── App.vue # 主 Vue 元件
│ └── main.js # 應用入口檔案
├── .env # 環境變數
├── .env.development
├── .env.production
├── .gitignore # Git 忽略檔案
├── index.html # 專案首頁（Vite 入口文件）
├── .prettierignore # prettier 忽略檔案
├── .prettierrc # 統一程式碼風格的設定檔案
├── package.json # npm 套件管理文件
├── README.md # 專案說明文件
└── vite.config.js # Vite 配置文件
```

## ESLint

本專案使用 ESLint 進行 lint，同時**不使用 Prettier 而是 @stylistic/eslint 作為 formatter**  
為了讓 ESLint 能順利在 **VSCode** 上作為 formatter 工作  
專案狀態需要與如下至少一致

1. 已在此專案禁用 Prettier VSCode 插件，避免互相衝突
2. 修改 `<專案目錄>/.vscode/settings.json` (若無請自行新增此檔案)

   ```json
   "editor.defaultFormatter": "dbaeumer.vscode-eslint",
   "eslint.format.enable": true,
   "eslint.validate": [
       "javascript",
       "typescript",
       "vue"
   ],
   ```

---
另外，如果想要有較好的理解目前 eslint 設定狀態  
可以安裝 `@eslint/config-inspector`

```bash
npm i -g @eslint/config-inspector
```

並在專案根目錄執行

```bash
eslint-config-inspector
```

會在 `7777` port 開啟網頁介面詳細檢視目前 eslint 相關設定

## 開發此專案

### 安裝依賴

```bash
npm install
```

### 開啟本地開發伺服器

```bash
npm run dev
```

### linting

```bash
npm run lint
```

修復所有可修復的不合規範程式碼

```bash
npm run lint:fix
```

### 打包專案

```bash
npm run build
```

### 環境變數管理

#### Mock Data 假資料

請在 `<專案根目錄>/.env.development.local` (若無此檔案請自行建立) 填入

* true 使用假資料
* 其他值或不填使用 API 資料

```toml
VITE_API_MOCK_ENABLED=true
```

#### 選擇 API Server

如果要使用 `localhost` 的 API Server
請在 `<專案根目錄>/.env.development.local` (若無此檔案請自行建立) 填入

**請不要直接變更 `.env.development`**

```toml
VITE_API_URL_V1=http://localhost:8080/api/v1
```

如果要使用 `http://*************:8081` 的 API Server
請註解 `VITE_API_URL_V1`

`.env.production` 與 `vite.config.js` 已經設定好 proxy 避免 CORS

```toml
# VITE_API_URL_V1=http://localhost:8080/api/v1
```

## 翻譯工作流程

翻譯檔案結構:  
所有翻譯皆放在 `public/lang` 下  
並依模組別分開放置  
語言碼請參照 [語言碼列表](https://simplelocalize.io/data/locales/) 使用  

**core/** 模組存放全網站會經常使用到的翻譯，例：上一頁、確認...等

另外增添新翻譯時可以由大至小  
目前翻譯框架會自動依地區 -> 語言 -> 預設做 fallback:  
en-US -> en -> zh-TW
