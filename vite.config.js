import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueDevTools from 'vite-plugin-vue-devtools';
import Component from 'unplugin-vue-components/vite';
import AutoImport from 'unplugin-auto-import/vite';
// https://vite.dev/config/
export default defineConfig({
	base: '/frf/',
	plugins: [
		vue(),
		vueDevTools(),
		Component({ dts: 'dts/components.d.ts' }),
		AutoImport({
			dts: 'dts/auto-imports.d.ts',
			imports: [
				'vue',
				'pinia'
			],
			dirs: [
				'./src/stores'
			],
			dirsScanOptions: {
				fileFilter: file => !file.includes('index.js') && !file.includes('index.ts')
			}
		})
	],
	resolve: {
		alias: {
			'@': fileURLToPath(new URL('./src', import.meta.url))
		}
	},
	server: {
		proxy: {
			// 把 /frf-api 的請求代理到後端服務器 避免CORS跨域問題 proxy僅在開發環境
			'/api': {
				target: 'http://192.168.0.181:8081/frf-api',
				changeOrigin: true
			}
		}
	}
});
