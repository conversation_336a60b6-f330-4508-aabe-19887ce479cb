import getVisitLogsApiPageDataApiJson from './mockData/wob/GetVisitLogsApiData.json';
import getWobTdRecVerifiesApiPageDataApiJson from './mockData/wob/GetWobTdRecVerifiesApiData.json';
import getTdItmeCountApiDataApiJson from './mockData/wob/GetTdItmeCountApiData.json';
import getTdItmeStatApiDataApiJson from './mockData/wob/GetTdItmeStatApiData.json';
import getTdItmeStatApiMockData_U from './mockData/wob/GetTdItmeStatApiMockData_U.json';
import getTdItmeStatApiMockData_TDCAT101 from './mockData/wob/GetTdItmeStatApiMockData_TDCAT101.json';
import getTdItmeStatApiMockData_TDCAT102 from './mockData/wob/GetTdItmeStatApiMockData_TDCAT102.json';
import getTdItmeStatApiMockData_TDCAT103 from './mockData/wob/GetTdItmeStatApiMockData_TDCAT103.json';
import getTdItmeStatApiMockData_TDCAT104 from './mockData/wob/GetTdItmeStatApiMockData_TDCAT104.json';
import getTdListApiDataApiJson from './mockData/wob/GetTdListApiData.json';
import getTdItemCat1ToDoMenuApiMockData from './mockData/wob/getTdItemCat1ToDoMenuApiMockData.json';
import getTdItemCustomizeCountApiMockData from './mockData/wob/getTdItemCustomizeCountApiMockData.json';
import getWobSearchTdListApiDataJson from './mockData/wob/GetWobSearchTdListApiData.json';
import getWobReuseWordsApiMockData from './mockData/wob/GetWobReuseWordsApiMockData.json';

export function postPersonalTaskApi({ nextRemindDt, nextRemindTime, advNce, advNceDay, advNcePrd, title, content }) {
	return Promise.resolve({ data: null });
}

export function postVisit({ cusCode, nextRemindDt, nextRemindTime, advNce, advNceDay, advNcePrd, visitAprCode, visitPurCode, title, content }) {
	return Promise.resolve({ data: null });
}

export function postConnect({ cusCode, nextRemindDt, nextRemindTime, title, visitAprCode, content, contStatCode, contProcCode, doneYn }) {
	return Promise.resolve({ data: null });
}

export function postMemoryDateApi({ cusCode, dateDt, note, remindYn, remindDays, remindPrd }) {
	return Promise.resolve({ data: null });
}

export function getReuseWordsApi() {
	return Promise.resolve({ data: [] });
}

export function postReuseWordsApi({ wordsId, words }) {
	return Promise.resolve({ data: null });
}

export function updateReuseWordsApi(dataArray) {
	return Promise.resolve({ data: null });
}

export function getCalendarTasksApi({ startDate, endDate }) {
	return Promise.resolve({ data: null });
}

export function getTdRecApi({ recCode }) {
	return Promise.resolve({ data: null });
}

export function deleteTdRecApi({ recCode }) {
	return Promise.resolve({ data: null });
}

export function patchTdPersonalRecApi({ tdRec, nextRemindDt, nextRemindTime, title, content, advNce, advNceDay, advNcePrd }) {
	return Promise.resolve({ data: null });
}

export function getAppointmentTdRecApi({ recCode }) {
	return Promise.resolve({ data: null });
}

export function patchAppointmentTdRecApi({
	recCode,
	cusCode,
	nextRemindDt,
	nextRemindTime,
	visitPurCode,
	visitAprCode,
	title,
	advNce,
	advNceDay,
	advNcePrd,
	content,
	doneYn,
	verifyStatusCode
}) {
	return Promise.resolve({ data: null });
}

export function getMemoryCalendarTaskApi({ id }) {
	return Promise.resolve({ data: null });
}

export function deleteMemoryDateApi({ id }) {
	return Promise.resolve({ data: null });
}

export function patchMemoryDateApi({ id, cusCode, dateDt, note, remindDays, remindPrd, remindYn }) {
	return Promise.resolve({ data: null });
}

export function getTdConnRecApi({ recCode }) {
	return Promise.resolve({ data: null });
}

export function patchTdConnRecApi({
	recCode,
	cusCode,
	nextRemindDt,
	nextRemindTime,
	doneYn,
	title,
	content,
	visitAprCode,
	contStatCode,
	contProcCode,
	contProcDesc,
	visitUserName,
	verifyStatusCode,
	giftId,
	giftDesc
}) {
	return Promise.resolve({ data: null });
}

export function postWobReuseWordsApi({ wordsId, words }) {
	return Promise.resolve({ data: null });
}

export function getVisitLogsApi({ cusCode, strDate, endDate, branCode, userCode, eventType, visitPurCode }, queryString) {
	return Promise.resolve({ data: getVisitLogsApiPageDataApiJson });
}

export function getWobTdRecVerifiesApi(
	{
		minorAreaBranCode,
		allBranCodes,
		branCode,
		allUserCodes,
		userCode,
		buCode,
		createStartDate,
		createEndDate,
		cusName,
		idn,
		verifyStartDate,
		verifyEndDate,
		verifyStatusCodes
	},
	queryString
) {
	return Promise.resolve({ data: getWobTdRecVerifiesApiPageDataApiJson });
}

export function patchWobTdRecVerifiesApi(updateItems) {
	return Promise.resolve({ data: null });
}

export function getWobSerachTdListApi({ startDate, endDate, tdCat1Code, itemCode, cusCode, branCode, allBranCode, userCode, allUserCode, pageable }) {
	return Promise.resolve({ data: getWobSearchTdListApiDataJson });
}
export function getWobTdListDescApi({ tdKind, tdCode }) {
	return Promise.resolve({ data: getWobTdListDescApiDataJson });
}

export function getVisitEventMenuApi({ eventType }) {
	return Promise.resolve({ data: null });
}

export function getTdItemCat1ToDoMenuApi() {
	return getTdItemCat1ToDoMenuApiMockData;
}

export function getTdItmeCountApi({ groupCode }) {
	return Promise.resolve({ data: getTdItmeCountApiDataApiJson });
}

export function getTdItmeStatApi({ tdCat1Code, groupCode }) {
	switch (tdCat1Code) {
		case 'U':
			return getTdItmeStatApiMockData_U;
		case 'TDCAT101':
			return getTdItmeStatApiMockData_TDCAT101;
		case 'TDCAT102':
			return getTdItmeStatApiMockData_TDCAT102;
		case 'TDCAT103':
			return getTdItmeStatApiMockData_TDCAT103;
		case 'TDCAT104':
			return getTdItmeStatApiMockData_TDCAT104;
		default:
			return Promise.resolve({ data: getTdItmeStatApiDataApiJson });
	}
}

export function getTdListApi({ tdCat1Code, itemCode, toDoItemType, groupCode }, queryString) {
	return Promise.resolve({ data: getTdListApiDataApiJson });
}

export function patchUpdateAndDoneTdListApi(tdListUpdateReq, queryString) {
	return Promise.resolve({ data: null });
}

export function getFinishedItmeCountApi({ groupCode, startDate, endDate }) {
	return Promise.resolve({ data: null });
}

export function getFinishedItmesApi({ tdCat1Code, groupCode, startDate, endDate }, queryString) {
	return Promise.resolve({ data: null });
}

export function getTdListLogsHisApi({ tdCode }) {
	return Promise.resolve({ data: null });
}

export function getGetWobTdStatusApi({ itemCode }) {
	return Promise.resolve({ data: null });
}

export function getgGtWobTdActionApi({ itemCode, actionCode }) {
	return Promise.resolve({ data: null });
}

export function getTdLogDetailsApi({ tdCode }) {
	return Promise.resolve({ data: null });
}

export function getTdItemHandleDetailApi({ tdCode }) {
	return Promise.resolve({ data: null });
}

export function patchUpdateTdListsApi({
	itemCode,
	tdCode,
	statusCode,
	statusName,
	actionCode,
	actionName,
	memo,
	chiefVistYn,
	verifyYn,
	traceYn,
	isUpdateAndDone,
	isHis
}) {
	return Promise.resolve({ data: null });
}

export function patchUpdateAndAuditTdListsApi({
	itemCode,
	tdCode,
	statusCode,
	statusName,
	actionCode,
	actionName,
	memo,
	chiefVistYn,
	verifyYn,
	traceYn,
	isUpdateAndDone,
	isHis
}) {
	return Promise.resolve({ data: null });
}

export function getTdItemCustomizeCountApi() {
	return getTdItemCustomizeCountApiMockData;
}

export function getWobReuseWordsApi() {
	return getWobReuseWordsApiMockData;
}

export function getWobAttBranchesApi() {
}
