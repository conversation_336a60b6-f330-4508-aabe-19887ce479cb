{"status": 200, "data": {"executeResult": "S", "status": "R", "eventId": "EVN20250227000010"}, "timestamp": "2025/04/18", "sqlTracer": [{"data": {"createDt": "2025-02-27T15:53:53.06", "createBy": "040978", "createBranCode": "891", "modifyDt": "2025-04-18T14:15:52.455522754", "modifyBy": "112790", "eventId": "EVN20250227000010", "wfgId": "WFG20141112001", "roleCode": "98", "userCode": "112790", "branCode": "891", "posCode": "891_98", "wfgsCode": "STP20141112004", "wkfHistoryId": "EVN20250418000001", "status": "R", "ownerCreateBy": "040978", "ownerModifyBy": "112790", "extra": "{\"eventSeq\":1,\"eventList\":[{\"wfgsCode\":\"STP20141112001\",\"branCode\":\"891\",\"roleCode\":\"49\",\"posCode\":\"891_49\",\"userCode\":\"040978\",\"othParams\":\"{}\"},{\"wfgsCode\":\"STP20141112002\",\"branCode\":\"891\",\"roleCode\":\"98\",\"posCode\":\"891_98\",\"userCode\":\"112790\",\"othParams\":\"{}\"}]}"}, "sqlInfo": "SELECT BRAN_CODE, CREATE_<PERSON>AN_CODE AS createBranCode, CREATE_BY AS createBy, CREATE_DEPUTY AS createDeputy, CREATE_DT AS createDt, EVENT_ID, <PERSON>XTRA, MODIFY_<PERSON>AN_CODE AS modifyBranCode, <PERSON><PERSON>IFY_BY AS modifyBy, <PERSON><PERSON>IFY_DEPUTY AS modifyDeputy, MODIFY_DT AS modifyDt, OWNER_CREATE_BY, OWNER_MODIFY_BY, POS_CODE, ROLE_CODE, STATUS, USER_CODE, WFGS_CODE, WFG_ID, WKF_HISTORY_ID FROM WKF_EVENTS WHERE EVENT_ID = :eventId,class com.bi.pbs.wkf.model.WkfEvents,MapSqlParameterSource {eventId=EVN20250227000010}"}, {"data": {"createDt": "2024-12-11T00:00:00", "createBy": "SYS", "createBranCode": "", "modifyDt": "1900-01-01T00:00:00", "modifyBy": "", "modifyBranCode": "", "wfgId": "WFG20141112001", "wfgName": "系統使用者管理審核", "wfgEname": "", "wfgRoleKind": "rule4", "activeYn": "Y", "activeDt": "2014-11-12 00:00:00.0", "confirmAction": "", "className": "AdmUserAccountWkfPocessor", "inactiveDt": "1900-01-01 00:00:00.0", "initWfgsCode": "STP20141112001", "menuCode": "M00-04"}, "sqlInfo": "SELECT ACTIVE_DT, ACTIVE_YN, CLASS_NAME, CONFIRM_ACTION, CREATE_<PERSON>AN_CODE AS createBranCode, CREATE_BY AS createBy, CREATE_DT AS createDt, INACTIVE_DT, INIT_WFGS_CODE, MENU_CODE, <PERSON><PERSON><PERSON><PERSON>_<PERSON>AN_CODE AS modifyBranCode, MODIFY_BY AS modifyBy, MODIFY_DT AS modifyDt, WFG_ENAME, WFG_ID, WFG_NAME, WFG_ROLE_KIND, WFG_VERSION FROM WKF_ENGINES WHERE WFG_ID = :wfgId,class com.bi.pbs.wkf.model.WkfEngines,MapSqlParameterSource {wfgId=WFG20141112001}"}, {"data": {"createDt": "2025-03-25T00:00:00", "createBy": "SYS", "createBranCode": "", "modifyDt": "1900-01-01T00:00:00", "modifyBy": "", "modifyBranCode": "", "wfgId": "WFG20141112001", "wfgsCode": "STP20141112002", "actionCode": "ACT20141112003", "nextWfgsCode": "STP20141112004"}, "sqlInfo": " SELECT WEF.*  FROM WKF_ENGINE_FLOWS WEF  JOIN WKF_ENGINE_ACTIONS WEA ON WEF.WFG_ID = WEA.WFG_ID AND WEF.ACTION_CODE = WEA.ACTION_CODE  WHERE WEF.WFG_ID = :wfgId AND WEF.WFGS_CODE = :wfgsCode  AND WEF.ACTION_CODE = :actionCode ,class com.bi.pbs.wkf.model.WkfEngineFlows,{wfgsCode=STP20141112002, actionCode=ACT20141112003, wfgId=WFG20141112001}"}, {"data": [{"createDt": "2025-02-05T00:00:00", "createBy": "SYS", "createBranCode": "", "modifyDt": "1900-01-01T00:00:00", "modifyBy": "", "modifyBranCode": "", "wfgsCode": "STP20141112002", "wfgsRole": "-ALL-"}], "sqlInfo": "SELECT * FROM WKF_ENGINE_ROLES WHERE WFGS_CODE = :wfgsCode ,class com.bi.pbs.wkf.model.WkfEngineRoles,{wfgsCode=STP20141112002}"}, {"data": {"createDt": "2024-11-20T00:00:00", "createBy": "SYS", "createBranCode": "", "modifyBy": "", "modifyBranCode": "", "wfgId": "WFG20141112001", "actionCode": "ACT20141112003", "actionName": "退回修改", "actionEname": "ACTION_REJECT", "actionStatus": "R", "methodName": "reject"}, "sqlInfo": " SELECT * FROM WKF_ENGINE_ACTIONS WHERE ACTION_CODE= :actionCode ,class com.bi.pbs.wkf.model.WkfEngineActions,{actionCode=ACT20141112003}"}, {"data": {"createDt": "2025-02-27T15:53:53.087", "createBy": "040978", "modifyDt": "2025-04-18T14:15:52.472777588", "modifyBy": "112790", "eventId": "EVN20250227000010", "userCode": "112790", "userMapId": "UMI20250227005", "startFlag": "N", "status": "R"}, "sqlInfo": "SELECT CREATE_<PERSON><PERSON>_CODE AS createBranCode, CREATE_BY AS createBy, CREATE_DT AS createDt, EVENT_ID, MODIFY_<PERSON>AN_CODE AS modifyBranCode, MODIFY_BY AS modifyBy, MODIFY_DT AS modifyDt, START_FLAG, STATUS, USER_CODE, USER_MAP_ID FROM ADM_USER_POS_EVENTS WHERE EVENT_ID = :eventId,class com.bi.pbs.adm.model.AdmUserPosEvents,MapSqlParameterSource {eventId=EVN20250227000010}"}, {"data": 1, "sqlInfo": "UPDATE ADM_USER_POS_EVENTS SET EVENT_ID = :eventId, USER_CODE = :userCode, USER_MAP_ID = :userMapId, START_FLAG = :startFlag, STATUS = :status, CREATE_DT = :createDt, CREATE_BY = :createBy, CREATE_BRAN_CODE = :createBranCode, MODIFY_DT = :modifyDt, MODIFY_BY = :modifyBy, MODIFY_BRAN_CODE = :modifyBranCode WHERE EVENT_ID = :eventId,{modifyBranCode=org.springframework.jdbc.core.SqlParameterValue@3be96501, eventId=org.springframework.jdbc.core.SqlParameterValue@7db8bf96, startFlag=org.springframework.jdbc.core.SqlParameterValue@70869f12, modifyBy=org.springframework.jdbc.core.SqlParameterValue@6e179fd6, createBy=org.springframework.jdbc.core.SqlParameterValue@1e263e78, userMapId=org.springframework.jdbc.core.SqlParameterValue@26da6625, modifyDt=org.springframework.jdbc.core.SqlParameterValue@62e013e3, createDt=org.springframework.jdbc.core.SqlParameterValue@30dbd946, createBranCode=org.springframework.jdbc.core.SqlParameterValue@1fa1e4db, userCode=org.springframework.jdbc.core.SqlParameterValue@6abf4244, status=org.springframework.jdbc.core.SqlParameterValue@8a22bfb}"}, {"data": 5, "sqlInfo": "  UPDATE ADM_USER_POS_MAP_LOG SET MODIFY_BY= :userCode WHERE USER_MAP_ID= :userMapId ,{userMapId=UMI20250227005, userCode=112790}"}, {"data": [], "sqlInfo": " SELECT * FROM  WKF_ENGINE_FLOWS WHERE WFG_ID = :wfgId AND WFGS_CODE = :wfgsCode ,class com.bi.pbs.wkf.model.WkfEngineFlows,{wfgsCode=STP20141112004, wfgId=WFG20141112001}"}, {"data": 1, "sqlInfo": "INSERT INTO WKF_EVENT_HISTORY (WKF_HISTORY_ID, EVENT_ID, WFG_ID, ROL<PERSON>_CODE, BRAN_CODE, <PERSON>OS_CODE, USER_CODE, WF<PERSON>_CODE, ACTION_CODE, STEP_CONTENT, STATUS, CREATE_DT, CREATE_BY, CREATE_<PERSON>AN_CODE, MODIFY_DT, MODIFY_BY, MODIFY_BRAN_CODE) VALUES (:wkfHistoryId, :eventId, :wfgId, :roleCode, :branCode, :posCode, :userCode, :wfgsCode, :actionCode, :stepContent, :status, :createDt, :createBy, :createBranCode, :modifyDt, :modifyBy, :modifyBranCode),{eventId=org.springframework.jdbc.core.SqlParameterValue@66ddc240, modifyBy=org.springframework.jdbc.core.SqlParameterValue@764a657c, wkfHistoryId=org.springframework.jdbc.core.SqlParameterValue@66f7b493, branCode=org.springframework.jdbc.core.SqlParameterValue@7f93a645, createDt=org.springframework.jdbc.core.SqlParameterValue@7754a435, createBranCode=org.springframework.jdbc.core.SqlParameterValue@52904e26, userCode=org.springframework.jdbc.core.SqlParameterValue@9982bcb, wfgId=org.springframework.jdbc.core.SqlParameterValue@6bdb1533, modifyBranCode=org.springframework.jdbc.core.SqlParameterValue@397c6171, createBy=org.springframework.jdbc.core.SqlParameterValue@69cde906, roleCode=org.springframework.jdbc.core.SqlParameterValue@21434187, wfgsCode=org.springframework.jdbc.core.SqlParameterValue@1282aa11, posCode=org.springframework.jdbc.core.SqlParameterValue@48af36d9, actionCode=org.springframework.jdbc.core.SqlParameterValue@4eac6313, modifyDt=org.springframework.jdbc.core.SqlParameterValue@75584c38, stepContent=org.springframework.jdbc.core.SqlParameterValue@728db2a0, status=org.springframework.jdbc.core.SqlParameterValue@43a55bd6}"}, {"data": {"createDt": "2025-02-27T15:53:53.06", "createBy": "040978", "createBranCode": "891", "eventId": "EVN20250227000010", "wfgId": "WFG20141112001", "roleCode": "49", "userCode": "040978", "branCode": "891", "posCode": "891_49", "wfgsCode": "STP20141112002", "wkfHistoryId": "EVN20250227000008", "status": "P", "ownerCreateBy": "040978", "extra": "{\"eventSeq\":0,\"eventList\":[{\"wfgsCode\":\"STP20141112001\",\"branCode\":\"891\",\"roleCode\":\"49\",\"posCode\":\"891_49\",\"userCode\":\"040978\",\"othParams\":\"{}\"}]}"}, "sqlInfo": "SELECT BRAN_CODE, CREATE_<PERSON>AN_CODE AS createBranCode, CREATE_BY AS createBy, CREATE_DEPUTY AS createDeputy, CREATE_DT AS createDt, EVENT_ID, <PERSON>XTRA, MOD<PERSON>Y_<PERSON>AN_CODE AS modifyBranCode, <PERSON><PERSON>IFY_BY AS modifyBy, <PERSON><PERSON>IFY_DEPUTY AS modifyDeputy, MODIFY_DT AS modifyDt, OWNER_CREATE_BY, OWNER_MODIFY_BY, POS_CODE, ROLE_CODE, STATUS, USER_CODE, WFGS_CODE, WFG_ID, WKF_HISTORY_ID FROM WKF_EVENTS WHERE EVENT_ID = :eventId,class com.bi.pbs.wkf.model.WkfEvents,{eventId=org.springframework.jdbc.core.SqlParameterValue@5932b429}"}, {"data": 1, "sqlInfo": "UPDATE WKF_EVENTS SET EVENT_ID = :eventId, WFG_ID = :wfgId, ROLE_CODE = :roleCode, USER_CODE = :userCode, BRAN_CODE = :branCode, POS_CODE = :posCode, WFGS_CODE = :wfgsCode, WKF_HISTORY_ID = :wkfHistoryId, STATUS = :status, OWNER_CREATE_BY = :ownerCreateBy, OWNER_MODIFY_BY = :ownerModifyBy, EXTRA = :extra, CREATE_DEPUTY = :createDeputy, MODIFY_DEPUTY = :modifyDeputy, CREATE_DT = :createDt, CREATE_BY = :createBy, CREATE_BRAN_CODE = :createBranCode, MODIFY_DT = :modifyDt, MODIFY_BY = :modifyBy, MODIFY_BRAN_CODE = :modifyBranCode WHERE EVENT_ID = :eventId,{eventId=org.springframework.jdbc.core.SqlParameterValue@4635151d, modifyBy=org.springframework.jdbc.core.SqlParameterValue@2f278de6, ownerModifyBy=org.springframework.jdbc.core.SqlParameterValue@21779a04, modifyDeputy=org.springframework.jdbc.core.SqlParameterValue@6d28f1ff, wkfHistoryId=org.springframework.jdbc.core.SqlParameterValue@35b8cba, branCode=org.springframework.jdbc.core.SqlParameterValue@1000798a, createDt=org.springframework.jdbc.core.SqlParameterValue@d3a6634, createBranCode=org.springframework.jdbc.core.SqlParameterValue@2737d965, userCode=org.springframework.jdbc.core.SqlParameterValue@78d6bf65, wfgId=org.springframework.jdbc.core.SqlParameterValue@6b0945dc, modifyBranCode=org.springframework.jdbc.core.SqlParameterValue@355c954f, ownerCreateBy=org.springframework.jdbc.core.SqlParameterValue@2c398e70, createBy=org.springframework.jdbc.core.SqlParameterValue@61593105, roleCode=org.springframework.jdbc.core.SqlParameterValue@40dfbf57, wfgsCode=org.springframework.jdbc.core.SqlParameterValue@1178bcee, extra=org.springframework.jdbc.core.SqlParameterValue@71d2eabf, posCode=org.springframework.jdbc.core.SqlParameterValue@59ab2c99, createDeputy=org.springframework.jdbc.core.SqlParameterValue@64b315fc, modifyDt=org.springframework.jdbc.core.SqlParameterValue@30785a48, status=org.springframework.jdbc.core.SqlParameterValue@5619d9ea}"}]}