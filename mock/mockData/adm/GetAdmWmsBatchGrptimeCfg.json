{"status": 200, "data": [{"id": 16, "groupCode": "TESTGRP", "stdTime": "2025/05/08"}], "timestamp": "2025/07/24", "sqlTracer": [{"data": {"paramValue": "FCB_BATCH.DBO.{TableName}"}, "sqlInfo": " SELECT PARAM_VALUE FROM ADM_PARAM  WHERE PARAM_TYPE = :type  AND PARAM_CODE = :code ,class com.fcb.pbs.adm.model.AdmParam,{code=PREF.LINKSTRING, type=ADM}"}, {"data": [{"id": 16, "groupCode": "TESTGRP", "stdTime": "2025/05/08"}], "sqlInfo": " SELECT  ID  ,GRO<PERSON>_CODE  ,STD_TIME  FROM  FCB_BATCH.DBO .ADM_WMS_BATCH_GRPTIME_CFG  WHERE CYCLE_TYPE = 'T'  AND CYCLE_VALUE = '30' ,class com.fcb.pbs.adm.web.model.BatchGrptimeCfgResp,{}"}]}