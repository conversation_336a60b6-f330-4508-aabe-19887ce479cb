{"status": 200, "data": {"content": [{"id": 4730, "dataDt": "2025-06-25", "spAnnotation": " 轉入投資標的檔資料\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_PRO_SECTORS", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4729, "dataDt": "2025-06-25", "spAnnotation": " 轉入發行機構資料\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_PRO_ISSUERS", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4728, "dataDt": "2025-06-25", "spAnnotation": " 轉入保險發行機構資料\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_PRO_INS_COMPANIES", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4727, "dataDt": "2025-06-25", "spAnnotation": " 轉入投資地區資料\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_PRO_GEO_FOCUS", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4726, "dataDt": "2025-06-25", "spAnnotation": " 轉入基金發行機構資料\t\t　\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_PRO_FUND_COMPANIES", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4725, "dataDt": "2025-06-25", "spAnnotation": " 轉入ETF交易所資料\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_PRO_EXCHANGES", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4724, "dataDt": "2025-06-25", "spAnnotation": " 轉入投資標的檔資料\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_PRO_BENCHMARKS", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4722, "dataDt": "2025-06-25", "spAnnotation": " 產生高資產客戶資格逾期名單匯出\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_EXP_HA_OVER", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4721, "dataDt": "2025-06-25", "spAnnotation": " 產生高資產客戶資格狀態匯出\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_EXP_HA_LIST", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4720, "dataDt": "2025-06-25", "spAnnotation": " 產生法人高資產客戶指定優惠之人匯出\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_EXP_ASSIGNED_HIGH_ASSET", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4646, "dataDt": "2025-06-25", "spAnnotation": " \r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_ADM_CA_ACCESS_MAP", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4640, "dataDt": "2025-06-25", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "OPTIONS", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4639, "dataDt": "2025-06-25", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "LOAN_KIND", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4638, "dataDt": "2025-06-25", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "IMP_OPTIONS", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4636, "dataDt": "2025-06-25", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "IMP_CKDCD", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4619, "dataDt": "2025-06-25", "spAnnotation": " 將 VFX_ANN_DIV_HISTORY 從前台DB轉入後台DB\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "B_P_W2B_VFX_ANN_DIV_HISTORY", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4618, "dataDt": "2025-06-25", "spAnnotation": " 將 VF_FUND_PRO_MAP 從前台DB轉入後台DB\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "B_P_W2B_VF_FUND_PRO_MAP", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4613, "dataDt": "2025-06-25", "spAnnotation": " 將 PRO_CALENDARS 從前台DB轉入後台DB\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "B_P_W2B_PRO_CALENDARS", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4611, "dataDt": "2025-06-25", "spAnnotation": " 將 ETF_PROFILE_SERVICE_PROVIDER 從前台DB轉入後台DB\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "B_P_W2B_ETF_PROFILE_SERVICE_PROVIDER", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4610, "dataDt": "2025-06-25", "spAnnotation": " 將 ETF_PROFILE_REFERENCE 從前台DB轉入後台DB\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "B_P_W2B_ETF_PROFILE_REFERENCE", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}], "pageable": {"pageNumber": 0, "pageSize": 20, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": false, "totalElements": 3829, "totalPages": 192, "first": true, "size": 20, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 20, "empty": false}, "timestamp": "2025/07/24", "sqlTracer": [{"data": {"paramValue": "FCB_BATCH.DBO.{TableName}"}, "sqlInfo": " SELECT PARAM_VALUE FROM ADM_PARAM  WHERE PARAM_TYPE = :type  AND PARAM_CODE = :code ,class com.fcb.pbs.adm.model.AdmParam,{code=PREF.LINKSTRING, type=ADM}"}, {"data": {"content": [{"id": 4730, "dataDt": "2025-06-25", "spAnnotation": " 轉入投資標的檔資料\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_PRO_SECTORS", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4729, "dataDt": "2025-06-25", "spAnnotation": " 轉入發行機構資料\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_PRO_ISSUERS", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4728, "dataDt": "2025-06-25", "spAnnotation": " 轉入保險發行機構資料\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_PRO_INS_COMPANIES", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4727, "dataDt": "2025-06-25", "spAnnotation": " 轉入投資地區資料\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_PRO_GEO_FOCUS", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4726, "dataDt": "2025-06-25", "spAnnotation": " 轉入基金發行機構資料\t\t　\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_PRO_FUND_COMPANIES", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4725, "dataDt": "2025-06-25", "spAnnotation": " 轉入ETF交易所資料\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_PRO_EXCHANGES", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4724, "dataDt": "2025-06-25", "spAnnotation": " 轉入投資標的檔資料\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_PRO_BENCHMARKS", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4722, "dataDt": "2025-06-25", "spAnnotation": " 產生高資產客戶資格逾期名單匯出\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_EXP_HA_OVER", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4721, "dataDt": "2025-06-25", "spAnnotation": " 產生高資產客戶資格狀態匯出\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_EXP_HA_LIST", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4720, "dataDt": "2025-06-25", "spAnnotation": " 產生法人高資產客戶指定優惠之人匯出\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_EXP_ASSIGNED_HIGH_ASSET", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4646, "dataDt": "2025-06-25", "spAnnotation": " \r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "W_P_ADM_CA_ACCESS_MAP", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4640, "dataDt": "2025-06-25", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "OPTIONS", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4639, "dataDt": "2025-06-25", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "LOAN_KIND", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4638, "dataDt": "2025-06-25", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "IMP_OPTIONS", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4636, "dataDt": "2025-06-25", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "IMP_CKDCD", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4619, "dataDt": "2025-06-25", "spAnnotation": " 將 VFX_ANN_DIV_HISTORY 從前台DB轉入後台DB\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "B_P_W2B_VFX_ANN_DIV_HISTORY", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4618, "dataDt": "2025-06-25", "spAnnotation": " 將 VF_FUND_PRO_MAP 從前台DB轉入後台DB\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "B_P_W2B_VF_FUND_PRO_MAP", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4613, "dataDt": "2025-06-25", "spAnnotation": " 將 PRO_CALENDARS 從前台DB轉入後台DB\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "B_P_W2B_PRO_CALENDARS", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4611, "dataDt": "2025-06-25", "spAnnotation": " 將 ETF_PROFILE_SERVICE_PROVIDER 從前台DB轉入後台DB\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "B_P_W2B_ETF_PROFILE_SERVICE_PROVIDER", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}, {"id": 4610, "dataDt": "2025-06-25", "spAnnotation": " 將 ETF_PROFILE_REFERENCE 從前台DB轉入後台DB\r", "cycleType": "D", "cycleValue": 1, "grpctlId": 173, "groupCode": "MAINBATCH", "objectName": "B_P_W2B_ETF_PROFILE_REFERENCE", "status": "W", "codeName": "可執行，等待中", "codeValue": "W", "cycleDescription": "每天"}], "pageable": {"pageNumber": 0, "pageSize": 20, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": false, "totalElements": 3829, "totalPages": 192, "first": true, "size": 20, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 20, "empty": false}, "sqlInfo": " SELECT AWC.*, ACD.CODE_NAME, ACD.CODE_VALUE,   AWBGC.CYCLE_TYPE, AWBGC.CYCLE_VALUE,   SUBSTRING(PA.SP_ANNOTATION, CHARINDEX(':', PA.SP_ANNOTATION) + 1, <PERSON><PERSON>(PA.SP_ANNOTATION))AS SP_ANNOTATION   FROM FCB_BATCH.DBO.ADM_WMS_CONTROL AWC  LEFT JOIN ADM_CODE_DETAIL ACD ON ACD. CODE_TYPE = 'BATCH_CONTROL_STATUS' AND ACD.CODE_VALUE = AWC.STATUS  LEFT JOIN FCB_BATCH.DBO.ADM_WMS_BATCH_GRPTIME_CFG AWBGC ON AWBGC.GROUP_CODE = AWC.GROUP_CODE  LEFT JOIN FCB_BATCH.DBO.ADM_PROCEDURE_ANNOTATION PA ON PA.SP_NAME = 'dbo.' + AWC.OBJECT_NAME  WHERE 1=1 ,Page request [number: 0, size 20, sort: DATA_DT: DESC],class com.fcb.pbs.adm.web.model.BatchJobStatusResp,{}"}]}