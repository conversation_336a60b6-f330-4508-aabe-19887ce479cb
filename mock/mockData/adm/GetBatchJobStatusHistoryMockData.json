{"status": 200, "data": [{"createDt": "2025-06-26 20:52:00.537", "createBy": "SYS_BATCH", "id": "6687", "spName": "B_P_IMP_CORP_MARGIN_ALL", "dataDt": "2025/06/25", "errType": "PROMPT", "text": "[1]轉入 IMP_CORP_MARGIN_ALL開始", "errMsg": ""}, {"createDt": "2025-06-26 20:52:00.493", "createBy": "SYSBATCH", "id": "6685", "spName": "B_P_IMP_CORP_MARGIN_ALL", "dataDt": "2025/06/25", "errType": "PROMPT", "text": "[B_P_IMP_CORP_MARGIN_ALL] 作業開始", "errMsg": ""}], "timestamp": "2025/07/24", "sqlTracer": [{"data": {"paramValue": "FCB_BATCH.DBO.{TableName}"}, "sqlInfo": " SELECT PARAM_VALUE FROM ADM_PARAM  WHERE PARAM_TYPE = :type  AND PARAM_CODE = :code ,class com.fcb.pbs.adm.model.AdmParam,{code=PREF.LINKSTRING, type=ADM}"}, {"data": [{"createDt": "2025-06-26 20:52:00.537", "createBy": "SYS_BATCH", "id": "6687", "spName": "B_P_IMP_CORP_MARGIN_ALL", "dataDt": "2025/06/25", "errType": "PROMPT", "text": "[1]轉入 IMP_CORP_MARGIN_ALL開始", "errMsg": ""}, {"createDt": "2025-06-26 20:52:00.493", "createBy": "SYSBATCH", "id": "6685", "spName": "B_P_IMP_CORP_MARGIN_ALL", "dataDt": "2025/06/25", "errType": "PROMPT", "text": "[B_P_IMP_CORP_MARGIN_ALL] 作業開始", "errMsg": ""}], "sqlInfo": " SELECT * FROM FCB_BATCH.DBO.ADM_WMS_LOGS  WHERE 1=1  AND SP_NAME = :objectName  AND DATA_DT = :dataDt ORDER BY ID DESC ,class com.fcb.pbs.adm.model.AdmWmsLogs,{objectName=B_P_IMP_CORP_MARGIN_ALL, dataDt=2025-06-25}"}]}