{"status": 200, "data": [{"code": "M01-011", "name": "客戶資產等級", "parentCode": "M01-01", "url": "vue-cfg-pot-cus-config", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M01-010", "name": "客戶其他補充資料", "parentCode": "M01-01", "url": "vue-cfg-cus-extra-info", "order": 2, "leafYn": "Y", "leaf": false}, {"code": "M01-012", "name": "客戶定期檢視", "parentCode": "M01-01", "url": "vue-cfg-cus-regular-view", "order": 3, "leafYn": "Y", "leaf": false}], "timestamp": "2025/07/22", "sqlTracer": [{"data": [{"code": "M01-011", "name": "客戶資產等級", "parentCode": "M01-01", "url": "vue-cfg-pot-cus-config", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M01-010", "name": "客戶其他補充資料", "parentCode": "M01-01", "url": "vue-cfg-cus-extra-info", "order": 2, "leafYn": "Y", "leaf": false}, {"code": "M01-012", "name": "客戶定期檢視", "parentCode": "M01-01", "url": "vue-cfg-cus-regular-view", "order": 3, "leafYn": "Y", "leaf": false}], "sqlInfo": " SELECT DISTINCT      M<PERSON><PERSON><PERSON>_CODE CODE,      <PERSON><PERSON>ME<PERSON>_NAME NAME,      M.PARENT_MENU_CODE PARENT_CODE,      M<PERSON>SHOW_ORDER 'ORDER',      M.MENU_ICON ICON,      <PERSON><PERSON>TAB_YN LEAF_YN,      AP.PROG_CLASSNAME URL  FROM ADM_MENUS M      JOIN ADM_ROLE_MENU_MAP ARMM ON ARMM.MENU_CODE = M.MENU_CODE      LEFT JOIN ADM_PROGS AP ON AP.PROG_CODE = M.PROG_CODE  WHERE ARMM.ROLE_CODE = :roleCode    AND M.ACTIVE_YN = 'Y'    AND M.PARENT_MENU_CODE = :parentMenuCode    AND M.TAB_YN = :tabYn ORDER BY M.SHOW_ORDER ASC, M.MENU_CODE ASC ,class com.bi.frame.menu.model.MenuTree,{roleCode=98, tabYn=Y, parentMenuCode=M01-01}"}]}