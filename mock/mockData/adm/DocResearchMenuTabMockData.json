{"status": 200, "data": [{"code": "M41-030", "name": "不分", "parentCode": "M41-03", "url": "vue-doc-research", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M41-038", "name": "黃金存摺", "parentCode": "M41-03", "url": "vue-doc-research", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M41-031", "name": "信託-基金", "parentCode": "M41-03", "url": "vue-doc-research", "order": 2, "leafYn": "Y", "leaf": false}, {"code": "M41-032", "name": "信託-ETF", "parentCode": "M41-03", "url": "vue-doc-research", "order": 3, "leafYn": "Y", "leaf": false}, {"code": "M41-033", "name": "信託-海外債", "parentCode": "M41-03", "url": "vue-doc-research", "order": 4, "leafYn": "Y", "leaf": false}, {"code": "M41-034", "name": "信託-海外股票", "parentCode": "M41-03", "url": "vue-doc-research", "order": 5, "leafYn": "Y", "leaf": false}, {"code": "M41-035", "name": "信託-境外結構型商品", "parentCode": "M41-03", "url": "vue-doc-research", "order": 6, "leafYn": "Y", "leaf": false}, {"code": "M41-036", "name": "信託-銀行結構型商品", "parentCode": "M41-03", "url": "vue-doc-research", "order": 7, "leafYn": "Y", "leaf": false}, {"code": "M41-037", "name": "人身保險", "parentCode": "M41-03", "url": "vue-doc-research", "order": 8, "leafYn": "Y", "leaf": false}], "timestamp": "2025/07/18", "sqlTracer": [{"data": [{"code": "M41-030", "name": "不分", "parentCode": "M41-03", "url": "vue-doc-research", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M41-038", "name": "黃金存摺", "parentCode": "M41-03", "url": "vue-doc-research", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M41-031", "name": "信託-基金", "parentCode": "M41-03", "url": "vue-doc-research", "order": 2, "leafYn": "Y", "leaf": false}, {"code": "M41-032", "name": "信託-ETF", "parentCode": "M41-03", "url": "vue-doc-research", "order": 3, "leafYn": "Y", "leaf": false}, {"code": "M41-033", "name": "信託-海外債", "parentCode": "M41-03", "url": "vue-doc-research", "order": 4, "leafYn": "Y", "leaf": false}, {"code": "M41-034", "name": "信託-海外股票", "parentCode": "M41-03", "url": "vue-doc-research", "order": 5, "leafYn": "Y", "leaf": false}, {"code": "M41-035", "name": "信託-境外結構型商品", "parentCode": "M41-03", "url": "vue-doc-research", "order": 6, "leafYn": "Y", "leaf": false}, {"code": "M41-036", "name": "信託-銀行結構型商品", "parentCode": "M41-03", "url": "vue-doc-research", "order": 7, "leafYn": "Y", "leaf": false}, {"code": "M41-037", "name": "人身保險", "parentCode": "M41-03", "url": "vue-doc-research", "order": 8, "leafYn": "Y", "leaf": false}], "sqlInfo": " SELECT DISTINCT      M<PERSON><PERSON><PERSON>_CODE CODE,      <PERSON><PERSON>ME<PERSON>_NAME NAME,      M.PARENT_MENU_CODE PARENT_CODE,      M<PERSON>SHOW_ORDER 'ORDER',      M.MENU_ICON ICON,      <PERSON><PERSON>TAB_YN LEAF_YN,      AP.PROG_CLASSNAME URL  FROM ADM_MENUS M      JOIN ADM_ROLE_MENU_MAP ARMM ON ARMM.MENU_CODE = M.MENU_CODE      LEFT JOIN ADM_PROGS AP ON AP.PROG_CODE = M.PROG_CODE  WHERE ARMM.ROLE_CODE = :roleCode    AND M.ACTIVE_YN = 'Y'    AND M.PARENT_MENU_CODE = :parentMenuCode    AND M.TAB_YN = :tabYn ORDER BY M.SHOW_ORDER ASC, M.MENU_CODE ASC ,class com.bi.frame.menu.model.MenuTree,{roleCode=98, tabYn=Y, parentMenuCode=M41-03}"}]}