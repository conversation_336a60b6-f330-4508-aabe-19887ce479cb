{"status": 200, "data": [{"branName": "石牌分行      ", "roleName": "理財專員", "startFlag": "Y"}, {"startFlag": "Y"}], "timestamp": "2025/04/17", "sqlTracer": [{"data": [{"branName": "石牌分行      ", "roleName": "理財專員", "startFlag": "Y"}, {"startFlag": "Y"}], "sqlInfo": " SELECT AB.BRAN_NAME, AR.ROLE_NAME,  AUPE.START_FLAG  FROM (SELECT START_FLAG,USER_MAP_ID  FROM ADM_USER_POS_EVENTS  WHERE EVENT_ID= :eventId  ) AUPE  LEFT JOIN ADM_USER_POS_MAP_LOG AUPML ON AUPE.USER_MAP_ID = AUPML.USER_MAP_ID  LEFT JOIN ADM_POSITIONS AP ON AUPML.POS_CODE = AP.POS_CODE  LEFT JOIN ADM_BRANCHES AB ON AP.BRAN_CODE = AB.BRAN_CODE AND AP.BU_CODE=AB.BU_CODE LEFT JOIN ADM_ROLES AR ON AP.ROLE_CODE = AR.ROLE_CODE ,class com.bi.pbs.adm.web.model.UserPosEventsResp,{eventId=EVN20250417000006}"}]}