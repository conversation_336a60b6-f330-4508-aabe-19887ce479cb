{"status": 200, "data": [], "timestamp": "2025/07/16", "sqlTracer": [{"data": [], "sqlInfo": " DECLARE @DT datetime  SET @DT = GETDATE()  SELECT START_DT  , END_DT  , SHUTDOWN_DESC  , CASE  WHEN @DT < START_DT THEN 'BEFORE'  ELSE  CASE  WHEN @DT > END_DT THEN 'AFTER'  ELSE 'DURING'  END END NOTICE_FLAG  FROM ADM_SHUTDOWN_INFO  WHERE CAST(START_DT AS DATE) = CAST(@DT AS DATE) ,class com.bi.pbs.adm.web.model.ShutdownInfoResp,[Ljava.lang.Object;@6a4d008f"}]}