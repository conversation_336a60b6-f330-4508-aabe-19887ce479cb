[{"code": "M0", "name": "系統管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M00", "name": "權限管理", "parentCode": "M0", "order": 0, "leaf": false, "nodes": [{"code": "M00-00", "name": "系統角色權限維護", "parentCode": "M00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M00-00", "parentMenuCode": "M00", "menuName": "系統角色權限維護", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M00-01", "name": "系統功能選單預覽", "parentCode": "M00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M00-01", "parentMenuCode": "M00", "menuName": "系統功能選單預覽", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M00-02", "name": "系統角色權限審核", "parentCode": "M00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M00-02", "parentMenuCode": "M00", "menuName": "系統角色權限審核", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M00-03", "name": "系統功能選單設定", "parentCode": "M00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M00-03", "parentMenuCode": "M00", "menuName": "系統功能選單設定", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M00-04", "name": "系統使用者管理", "parentCode": "M00", "order": 0, "leaf": false, "nodes": [{"code": "M00-040", "name": "系統使用者查詢", "parentCode": "M00-04", "order": 0, "leaf": false, "nodes": [], "menuCode": "M00-040", "parentMenuCode": "M00-04", "menuName": "系統使用者查詢", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M00-041", "name": "系統使用者設定", "parentCode": "M00-04", "order": 0, "leaf": false, "nodes": [], "menuCode": "M00-041", "parentMenuCode": "M00-04", "menuName": "系統使用者設定", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M00-04", "parentMenuCode": "M00", "menuName": "系統使用者管理", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M00-05", "name": "系統使用者管理審核", "parentCode": "M00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M00-05", "parentMenuCode": "M00", "menuName": "系統使用者管理審核", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M00-06", "name": "區域人員設定", "parentCode": "M00", "order": 0, "leaf": false, "nodes": [{"code": "M00-060", "name": "區域人員設定", "parentCode": "M00-06", "order": 0, "leaf": false, "nodes": [], "menuCode": "M00-060", "parentMenuCode": "M00-06", "menuName": "區域人員設定", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M00-061", "name": "區域分區查詢", "parentCode": "M00-06", "order": 0, "leaf": false, "nodes": [], "menuCode": "M00-061", "parentMenuCode": "M00-06", "menuName": "區域分區查詢", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M00-06", "parentMenuCode": "M00", "menuName": "區域人員設定", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M00-07", "name": "分行分區設定", "parentCode": "M00", "order": 0, "leaf": false, "nodes": [{"code": "M00-070", "name": "分行分區設定", "parentCode": "M00-07", "order": 0, "leaf": false, "nodes": [], "menuCode": "M00-070", "parentMenuCode": "M00-07", "menuName": "分行分區設定", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M00-071", "name": "分行分區查詢", "parentCode": "M00-07", "order": 0, "leaf": false, "nodes": [], "menuCode": "M00-071", "parentMenuCode": "M00-07", "menuName": "分行分區查詢", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M00-07", "parentMenuCode": "M00", "menuName": "分行分區設定", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M00", "parentMenuCode": "M0", "menuName": "權限管理", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M01", "name": "參數管理", "parentCode": "M0", "order": 0, "leaf": false, "nodes": [{"code": "M01-00", "name": "業務人員等級維護", "parentCode": "M01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M01-00", "parentMenuCode": "M01", "menuName": "業務人員等級維護", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M01-01", "name": "客戶模組參數", "parentCode": "M01", "order": 0, "leaf": false, "nodes": [{"code": "M01-010", "name": "客戶其他補充資料", "parentCode": "M01-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M01-010", "parentMenuCode": "M01-01", "menuName": "客戶其他補充資料", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M01-012", "name": "客戶定期檢視", "parentCode": "M01-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M01-012", "parentMenuCode": "M01-01", "menuName": "客戶定期檢視", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M01-013", "name": "體驗戶參數", "parentCode": "M01-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M01-013", "parentMenuCode": "M01-01", "menuName": "體驗戶參數", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M01-011", "name": "客戶資產等級", "parentCode": "M01-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M01-011", "parentMenuCode": "M01-01", "menuName": "客戶資產等級", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M01-01", "parentMenuCode": "M01", "menuName": "客戶模組參數", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M01-03", "name": "事件通知狀態設定", "parentCode": "M01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M01-03", "parentMenuCode": "M01", "menuName": "事件通知狀態設定", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M01-04", "name": "理財規劃模組參數", "parentCode": "M01", "order": 0, "leaf": false, "nodes": [{"code": "M01-040", "name": "一般共用參數", "parentCode": "M01-04", "order": 0, "leaf": false, "nodes": [], "menuCode": "M01-040", "parentMenuCode": "M01-04", "menuName": "一般共用參數", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M01-041", "name": "教育規劃參數", "parentCode": "M01-04", "order": 0, "leaf": false, "nodes": [], "menuCode": "M01-041", "parentMenuCode": "M01-04", "menuName": "教育規劃參數", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M01-042", "name": "退休規劃參數", "parentCode": "M01-04", "order": 0, "leaf": false, "nodes": [], "menuCode": "M01-042", "parentMenuCode": "M01-04", "menuName": "退休規劃參數", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M01-04", "parentMenuCode": "M01", "menuName": "理財規劃模組參數", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M01-05", "name": "事件通知TOP10設定", "parentCode": "M01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M01-05", "parentMenuCode": "M01", "menuName": "事件通知TOP10設定", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M01", "parentMenuCode": "M0", "menuName": "參數管理", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M02", "name": "代理人", "parentCode": "M0", "order": 0, "leaf": false, "nodes": [{"code": "M02-00", "name": "代理人設定", "parentCode": "M02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M02-00", "parentMenuCode": "M02", "menuName": "代理人設定", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M02-01", "name": "代理人設定紀錄", "parentCode": "M02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M02-01", "parentMenuCode": "M02", "menuName": "代理人設定紀錄", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M02", "parentMenuCode": "M0", "menuName": "代理人", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M03", "name": "安控紀錄", "parentCode": "M0", "order": 0, "leaf": false, "nodes": [{"code": "M03-00", "name": "使用者使用紀錄", "parentCode": "M03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M03-00", "parentMenuCode": "M03", "menuName": "使用者使用紀錄", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M03-01", "name": "分行使用紀錄", "parentCode": "M03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M03-01", "parentMenuCode": "M03", "menuName": "分行使用紀錄", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M03-02", "name": "使用者存取客戶紀錄", "parentCode": "M03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M03-02", "parentMenuCode": "M03", "menuName": "使用者存取客戶紀錄", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M03-03", "name": "使用者使用明細記錄", "parentCode": "M03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M03-03", "parentMenuCode": "M03", "menuName": "使用者使用明細記錄", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M03-04", "name": "使用者資料產生紀錄", "parentCode": "M03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M03-04", "parentMenuCode": "M03", "menuName": "使用者資料產生紀錄", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M03", "parentMenuCode": "M0", "menuName": "安控紀錄", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M04", "name": "系統維運", "parentCode": "M0", "order": 0, "leaf": false, "nodes": [{"code": "M04-00", "name": "批次程式監控", "parentCode": "M04", "order": 0, "leaf": false, "nodes": [], "menuCode": "M04-00", "parentMenuCode": "M04", "menuName": "批次程式監控", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M04-01", "name": "批次重跑", "parentCode": "M04", "order": 0, "leaf": false, "nodes": [], "menuCode": "M04-01", "parentMenuCode": "M04", "menuName": "批次重跑", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M04-02", "name": "停機公告", "parentCode": "M04", "order": 0, "leaf": false, "nodes": [], "menuCode": "M04-02", "parentMenuCode": "M04", "menuName": "停機公告", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M04", "parentMenuCode": "M0", "menuName": "系統維運", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M0", "parentMenuCode": "", "menuName": "系統管理", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M1", "name": "工作管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M10", "name": "行事曆", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M10", "parentMenuCode": "M1", "menuName": "行事曆", "depths": 2, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M11", "name": "事件通知", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [{"code": "M11-00", "name": "待辦工作事項", "parentCode": "M11", "order": 0, "leaf": false, "nodes": [], "menuCode": "M11-00", "parentMenuCode": "M11", "menuName": "待辦工作事項", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M11-01", "name": "已完成工作事項", "parentCode": "M11", "order": 0, "leaf": false, "nodes": [], "menuCode": "M11-01", "parentMenuCode": "M11", "menuName": "已完成工作事項", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M11", "parentMenuCode": "M1", "menuName": "事件通知", "depths": 2, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M12", "name": "事件通知查詢", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M12", "parentMenuCode": "M1", "menuName": "事件通知查詢", "depths": 2, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M13", "name": "事件指派", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M13", "parentMenuCode": "M1", "menuName": "事件指派", "depths": 2, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M14", "name": "服務紀錄查詢", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M14", "parentMenuCode": "M1", "menuName": "服務紀錄查詢", "depths": 2, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M15", "name": "服務紀錄審核/事件通知審核", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M15", "parentMenuCode": "M1", "menuName": "服務紀錄審核/事件通知審核", "depths": 2, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M16", "name": "未完成約訪", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M16", "parentMenuCode": "M1", "menuName": "未完成約訪", "depths": 2, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M17", "name": "排行榜", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [{"code": "M17-00", "name": "排行榜-AO", "parentCode": "M17", "order": 0, "leaf": false, "nodes": [], "menuCode": "M17-00", "parentMenuCode": "M17", "menuName": "排行榜-AO", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M17-01", "name": "排行榜-BM", "parentCode": "M17", "order": 0, "leaf": false, "nodes": [], "menuCode": "M17-01", "parentMenuCode": "M17", "menuName": "排行榜-BM", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M17", "parentMenuCode": "M1", "menuName": "排行榜", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M1B", "name": "事件通知TOP10自設", "parentCode": "M1", "order": 0, "leaf": false, "nodes": [], "menuCode": "M1B", "parentMenuCode": "M1", "menuName": "事件通知TOP10自設", "depths": 2, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M1", "parentMenuCode": "", "menuName": "工作管理", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M2", "name": "客戶管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M20", "name": "客戶查詢", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M20-00", "name": "單一條件客戶查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-00", "parentMenuCode": "M20", "menuName": "單一條件客戶查詢", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-01", "name": "綜合條件客戶查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-01", "parentMenuCode": "M20", "menuName": "綜合條件客戶查詢", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-02", "name": "全行客戶查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-02", "parentMenuCode": "M20", "menuName": "全行客戶查詢", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-03", "name": "人身保險銷售/服務查詢", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-03", "parentMenuCode": "M20", "menuName": "人身保險銷售/服務查詢", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-05", "name": "客戶總覽", "parentCode": "M20", "order": 0, "leaf": false, "nodes": [{"code": "M20-051", "name": "客戶總覽", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-051", "parentMenuCode": "M20-05", "menuName": "客戶總覽", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-052", "name": "基本資料", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0520", "name": "本行顧客資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0520", "parentMenuCode": "M20-052", "menuName": "本行顧客資料", "depths": 5, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-0521", "name": "公司基本資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0521", "parentMenuCode": "M20-052", "menuName": "公司基本資料", "depths": 5, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-0522", "name": "家庭與親友資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0522", "parentMenuCode": "M20-052", "menuName": "家庭與親友資料", "depths": 5, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-0523", "name": "重要節日設定", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0523", "parentMenuCode": "M20-052", "menuName": "重要節日設定", "depths": 5, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-0524", "name": "其他補充資料", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0524", "parentMenuCode": "M20-052", "menuName": "其他補充資料", "depths": 5, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-0525", "name": "經管紀錄", "parentCode": "M20-052", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0525", "parentMenuCode": "M20-052", "menuName": "經管紀錄", "depths": 5, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M20-052", "parentMenuCode": "M20-05", "menuName": "基本資料", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-053", "name": "價格警示設定", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-053", "parentMenuCode": "M20-05", "menuName": "價格警示設定", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M20-055", "name": "帳戶總覽", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-055", "parentMenuCode": "M20-05", "menuName": "帳戶總覽", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-056", "name": "服務紀錄", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0560", "name": "一般通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0560", "parentMenuCode": "M20-056", "menuName": "一般通聯", "depths": 5, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-0561", "name": "事件通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0561", "parentMenuCode": "M20-056", "menuName": "事件通聯", "depths": 5, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-0562", "name": "行銷專案通聯", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0562", "parentMenuCode": "M20-056", "menuName": "行銷專案通聯", "depths": 5, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-0563", "name": "客戶回饋紀錄", "parentCode": "M20-056", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0563", "parentMenuCode": "M20-056", "menuName": "客戶回饋紀錄", "depths": 5, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M20-056", "parentMenuCode": "M20-05", "menuName": "服務紀錄", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-057", "name": "客戶報告書", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-057", "parentMenuCode": "M20-05", "menuName": "客戶報告書", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-058", "name": "投資績效分析", "parentCode": "M20-05", "order": 0, "leaf": false, "nodes": [{"code": "M20-0583", "name": "投資標的分析", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0583", "parentMenuCode": "M20-058", "menuName": "投資標的分析", "depths": 5, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-0585", "name": "交易紀錄查詢", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0585", "parentMenuCode": "M20-058", "menuName": "交易紀錄查詢", "depths": 5, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-0586", "name": "實現損益查詢", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0586", "parentMenuCode": "M20-058", "menuName": "實現損益查詢", "depths": 5, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M20-0587", "name": "投資績效分析/資產及報酬率分析", "parentCode": "M20-058", "order": 0, "leaf": false, "nodes": [], "menuCode": "M20-0587", "parentMenuCode": "M20-058", "menuName": "投資績效分析/資產及報酬率分析", "depths": 5, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M20-058", "parentMenuCode": "M20-05", "menuName": "投資績效分析", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M20-05", "parentMenuCode": "M20", "menuName": "客戶總覽", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M20", "parentMenuCode": "M2", "menuName": "客戶查詢", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M21", "name": "客戶歸屬管理", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M21-00", "name": "客戶移轉分派", "parentCode": "M21", "order": 0, "leaf": false, "nodes": [{"code": "M21-000", "name": "客戶異動紀錄分派", "parentCode": "M21-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M21-000", "parentMenuCode": "M21-00", "menuName": "客戶異動紀錄分派", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M21-001", "name": "多條件式篩選分派", "parentCode": "M21-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M21-001", "parentMenuCode": "M21-00", "menuName": "多條件式篩選分派", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M21-00", "parentMenuCode": "M21", "menuName": "客戶移轉分派", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M21-01", "name": "客戶異動紀錄查詢", "parentCode": "M21", "order": 0, "leaf": false, "nodes": [], "menuCode": "M21-01", "parentMenuCode": "M21", "menuName": "客戶異動紀錄查詢", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M21-02", "name": "客戶異動申請", "parentCode": "M21", "order": 0, "leaf": false, "nodes": [], "menuCode": "M21-02", "parentMenuCode": "M21", "menuName": "客戶異動申請", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M21", "parentMenuCode": "M2", "menuName": "客戶歸屬管理", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M23", "name": "顧客歸戶管理", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M23-00", "name": "歸戶設定", "parentCode": "M23", "order": 0, "leaf": false, "nodes": [], "menuCode": "M23-00", "parentMenuCode": "M23", "menuName": "歸戶設定", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M23-01", "name": "歸戶維護", "parentCode": "M23", "order": 0, "leaf": false, "nodes": [], "menuCode": "M23-01", "parentMenuCode": "M23", "menuName": "歸戶維護", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M23-02", "name": "歸戶查詢", "parentCode": "M23", "order": 0, "leaf": false, "nodes": [], "menuCode": "M23-02", "parentMenuCode": "M23", "menuName": "歸戶查詢", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M23", "parentMenuCode": "M2", "menuName": "顧客歸戶管理", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25", "name": "客訴資料管理", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M25-00", "name": "客訴資料維護", "parentCode": "M25", "order": 0, "leaf": false, "nodes": [{"code": "M25-000", "name": "客訴資料上傳", "parentCode": "M25-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-000", "parentMenuCode": "M25-00", "menuName": "客訴資料上傳", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25-001", "name": "客訴資料上傳查詢", "parentCode": "M25-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-001", "parentMenuCode": "M25-00", "menuName": "客訴資料上傳查詢", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M25-00", "parentMenuCode": "M25", "menuName": "客訴資料維護", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M25-01", "name": "客訴資料查詢", "parentCode": "M25", "order": 0, "leaf": false, "nodes": [], "menuCode": "M25-01", "parentMenuCode": "M25", "menuName": "客訴資料查詢", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M25", "parentMenuCode": "M2", "menuName": "客訴資料管理", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M26", "name": "一般作業審核", "parentCode": "M2", "order": 0, "leaf": false, "nodes": [{"code": "M26-00", "name": "待審核項目", "parentCode": "M26", "order": 0, "leaf": false, "nodes": [], "menuCode": "M26-00", "parentMenuCode": "M26", "menuName": "待審核項目", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M26-01", "name": "歷史審核紀錄", "parentCode": "M26", "order": 0, "leaf": false, "nodes": [], "menuCode": "M26-01", "parentMenuCode": "M26", "menuName": "歷史審核紀錄", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M26", "parentMenuCode": "M2", "menuName": "一般作業審核", "depths": 2, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M2", "parentMenuCode": "", "menuName": "客戶管理", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M3", "name": "商品管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M30", "name": "商品資料", "parentCode": "M3", "order": 0, "leaf": false, "nodes": [{"code": "M30-00", "name": "商品資料查詢", "parentCode": "M30", "order": 0, "leaf": false, "nodes": [{"code": "M30-000", "name": "全商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-000", "parentMenuCode": "M30-00", "menuName": "全商品", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M30-001", "name": "信託-基金", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-001", "parentMenuCode": "M30-00", "menuName": "信託-基金", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M30-002", "name": "信託-ETF", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-002", "parentMenuCode": "M30-00", "menuName": "信託-ETF", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M30-003", "name": "信託-海外債", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-003", "parentMenuCode": "M30-00", "menuName": "信託-海外債", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M30-004", "name": "信託-海外股票", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-004", "parentMenuCode": "M30-00", "menuName": "信託-海外股票", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M30-005", "name": "信託-境外結構型商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-005", "parentMenuCode": "M30-00", "menuName": "信託-境外結構型商品", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M30-006", "name": "信託-銀行結構型商品", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-006", "parentMenuCode": "M30-00", "menuName": "信託-銀行結構型商品", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M30-007", "name": "人身保險", "parentCode": "M30-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-007", "parentMenuCode": "M30-00", "menuName": "人身保險", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M30-00", "parentMenuCode": "M30", "menuName": "商品資料查詢", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M30-01", "name": "商品資料維護", "parentCode": "M30", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-01", "parentMenuCode": "M30", "menuName": "商品資料維護", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-02", "name": "新商品臨時上架維護", "parentCode": "M30", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-02", "parentMenuCode": "M30", "menuName": "新商品臨時上架維護", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-03", "name": "商品資料審核", "parentCode": "M30", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-03", "parentMenuCode": "M30", "menuName": "商品資料審核", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M30-04", "name": "新商品臨時上架審核", "parentCode": "M30", "order": 0, "leaf": false, "nodes": [], "menuCode": "M30-04", "parentMenuCode": "M30", "menuName": "新商品臨時上架審核", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M30", "parentMenuCode": "M3", "menuName": "商品資料", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M31", "name": "精選推薦商品", "parentCode": "M3", "order": 0, "leaf": false, "nodes": [{"code": "M31-00", "name": "精選推薦商品設定", "parentCode": "M31", "order": 0, "leaf": false, "nodes": [], "menuCode": "M31-00", "parentMenuCode": "M31", "menuName": "精選推薦商品設定", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M31-01", "name": "精選推薦商品查詢", "parentCode": "M31", "order": 0, "leaf": false, "nodes": [], "menuCode": "M31-01", "parentMenuCode": "M31", "menuName": "精選推薦商品查詢", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M31-02", "name": "精選推薦商品審核", "parentCode": "M31", "order": 0, "leaf": false, "nodes": [], "menuCode": "M31-02", "parentMenuCode": "M31", "menuName": "精選推薦商品審核", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M31", "parentMenuCode": "M3", "menuName": "精選推薦商品", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M3", "parentMenuCode": "", "menuName": "商品管理", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M4", "name": "一般管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M40", "name": "文件公告訊息管理", "parentCode": "M4", "order": 0, "leaf": false, "nodes": [{"code": "M40-00", "name": "文件公告訊息分類設定", "parentCode": "M40", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-00", "parentMenuCode": "M40", "menuName": "文件公告訊息分類設定", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-01", "name": "文件公告訊息設定", "parentCode": "M40", "order": 0, "leaf": false, "nodes": [{"code": "M40-010", "name": "公告事項設定", "parentCode": "M40-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-010", "parentMenuCode": "M40-01", "menuName": "公告事項設定", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-011", "name": "公告事項查詢", "parentCode": "M40-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-011", "parentMenuCode": "M40-01", "menuName": "公告事項查詢", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M40-01", "parentMenuCode": "M40", "menuName": "文件公告訊息設定", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-02", "name": "公告及文件下載", "parentCode": "M40", "order": 0, "leaf": false, "nodes": [{"code": "M40-020", "name": "公告及文件下載", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-020", "parentMenuCode": "M40-02", "menuName": "公告及文件下載", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-021", "name": "產品", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-021", "parentMenuCode": "M40-02", "menuName": "產品", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-022", "name": "一般", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-022", "parentMenuCode": "M40-02", "menuName": "一般", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-023", "name": "跑馬燈", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-023", "parentMenuCode": "M40-02", "menuName": "跑馬燈", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-024", "name": "強制閱讀", "parentCode": "M40-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-024", "parentMenuCode": "M40-02", "menuName": "強制閱讀", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M40-02", "parentMenuCode": "M40", "menuName": "公告及文件下載", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M40-03", "name": "文件公告訊息審核", "parentCode": "M40", "order": 0, "leaf": false, "nodes": [], "menuCode": "M40-03", "parentMenuCode": "M40", "menuName": "文件公告訊息審核", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M40", "parentMenuCode": "M4", "menuName": "文件公告訊息管理", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41", "name": "金融訊息", "parentCode": "M4", "order": 0, "leaf": false, "nodes": [{"code": "M41-00", "name": "金融訊息維護", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-000", "name": "訊息設定", "parentCode": "M41-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-000", "parentMenuCode": "M41-00", "menuName": "訊息設定", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-001", "name": "訊息查詢", "parentCode": "M41-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-001", "parentMenuCode": "M41-00", "menuName": "訊息查詢", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M41-00", "parentMenuCode": "M41", "menuName": "金融訊息維護", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M41-01", "name": "產品文件", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-010", "name": "信託-基金", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-010", "parentMenuCode": "M41-01", "menuName": "信託-基金", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-011", "name": "信託-ETF", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-011", "parentMenuCode": "M41-01", "menuName": "信託-ETF", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-012", "name": "信託-海外債", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-012", "parentMenuCode": "M41-01", "menuName": "信託-海外債", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-013", "name": "信託-海外股票", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-013", "parentMenuCode": "M41-01", "menuName": "信託-海外股票", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-016", "name": "人身保險", "parentCode": "M41-01", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-016", "parentMenuCode": "M41-01", "menuName": "人身保險", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M41-01", "parentMenuCode": "M41", "menuName": "產品文件", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-02", "name": "行銷活動", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-020", "name": "行銷活動", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-020", "parentMenuCode": "M41-02", "menuName": "行銷活動", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-021", "name": "新產品研討會", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-021", "parentMenuCode": "M41-02", "menuName": "新產品研討會", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-022", "name": "市場研討會", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-022", "parentMenuCode": "M41-02", "menuName": "市場研討會", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-023", "name": "專案活動", "parentCode": "M41-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-023", "parentMenuCode": "M41-02", "menuName": "專案活動", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M41-02", "parentMenuCode": "M41", "menuName": "行銷活動", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-03", "name": "研究報告", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [{"code": "M41-030", "name": "不分", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-030", "parentMenuCode": "M41-03", "menuName": "不分", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-031", "name": "信託-基金", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-031", "parentMenuCode": "M41-03", "menuName": "信託-基金", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-032", "name": "信託-ETF", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-032", "parentMenuCode": "M41-03", "menuName": "信託-ETF", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-033", "name": "信託-海外債", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-033", "parentMenuCode": "M41-03", "menuName": "信託-海外債", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-034", "name": "信託-海外股票", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-034", "parentMenuCode": "M41-03", "menuName": "信託-海外股票", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-037", "name": "人身保險", "parentCode": "M41-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-037", "parentMenuCode": "M41-03", "menuName": "人身保險", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M41-03", "parentMenuCode": "M41", "menuName": "研究報告", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-04", "name": "發行機構報告", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-04", "parentMenuCode": "M41", "menuName": "發行機構報告", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M41-05", "name": "新聞事件", "parentCode": "M41", "order": 0, "leaf": false, "nodes": [], "menuCode": "M41-05", "parentMenuCode": "M41", "menuName": "新聞事件", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M41", "parentMenuCode": "M4", "menuName": "金融訊息", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M43", "name": "證照管理", "parentCode": "M4", "order": 0, "leaf": false, "nodes": [{"code": "M43-01", "name": "證照登錄查詢", "parentCode": "M43", "order": 0, "leaf": false, "nodes": [], "menuCode": "M43-01", "parentMenuCode": "M43", "menuName": "證照登錄查詢", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M43", "parentMenuCode": "M4", "menuName": "證照管理", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M4", "parentMenuCode": "", "menuName": "一般管理", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M5", "name": "行銷管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M50", "name": "商機名單行銷維護", "parentCode": "M5", "order": 0, "leaf": false, "nodes": [{"code": "M50-00", "name": "禁止打擾客戶名單", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-00", "parentMenuCode": "M50", "menuName": "禁止打擾客戶名單", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M50-02", "name": "商機名單建立", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-02", "parentMenuCode": "M50", "menuName": "商機名單建立", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50-04", "name": "商機名單調整", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [{"code": "M50-040", "name": "商機建立與維護", "parentCode": "M50-04", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-040", "parentMenuCode": "M50-04", "menuName": "商機建立與維護", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50-041", "name": "商機紀錄", "parentCode": "M50-04", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-041", "parentMenuCode": "M50-04", "menuName": "商機紀錄", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M50-04", "parentMenuCode": "M50", "menuName": "商機名單調整", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50-05", "name": "商機名單分配(總行)", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-05", "parentMenuCode": "M50", "menuName": "商機名單分配(總行)", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50-06", "name": "商機名單分配(分行)", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-06", "parentMenuCode": "M50", "menuName": "商機名單分配(分行)", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50-07", "name": "商機名單指派", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-07", "parentMenuCode": "M50", "menuName": "商機名單指派", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M50-08", "name": "我的商機名單", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-08", "parentMenuCode": "M50", "menuName": "我的商機名單", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M50-09", "name": "行銷名單通聯結果查詢", "parentCode": "M50", "order": 0, "leaf": false, "nodes": [], "menuCode": "M50-09", "parentMenuCode": "M50", "menuName": "行銷名單通聯結果查詢", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M50", "parentMenuCode": "M5", "menuName": "商機名單行銷維護", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M51", "name": "一般活動行銷維護", "parentCode": "M5", "order": 0, "leaf": false, "nodes": [{"code": "M51-00", "name": "活動建立及管理", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [{"code": "M51-000", "name": "活動建立", "parentCode": "M51-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-000", "parentMenuCode": "M51-00", "menuName": "活動建立", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M51-001", "name": "場次維護", "parentCode": "M51-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-001", "parentMenuCode": "M51-00", "menuName": "場次維護", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M51-002", "name": "名單下載及出席登錄", "parentCode": "M51-00", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-002", "parentMenuCode": "M51-00", "menuName": "名單下載及出席登錄", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M51-00", "parentMenuCode": "M51", "menuName": "活動建立及管理", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M51-01", "name": "活動場次維護", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-01", "parentMenuCode": "M51", "menuName": "活動場次維護", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M51-02", "name": "客戶活動報名", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-02", "parentMenuCode": "M51", "menuName": "客戶活動報名", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M51-03", "name": "名單下載及出席登錄", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-03", "parentMenuCode": "M51", "menuName": "名單下載及出席登錄", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M51-04", "name": "活動查詢", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-04", "parentMenuCode": "M51", "menuName": "活動查詢", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M51-05", "name": "活動審核", "parentCode": "M51", "order": 0, "leaf": false, "nodes": [], "menuCode": "M51-05", "parentMenuCode": "M51", "menuName": "活動審核", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M51", "parentMenuCode": "M5", "menuName": "一般活動行銷維護", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M5", "parentMenuCode": "", "menuName": "行銷管理", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M8", "name": "報表管理", "parentCode": "", "order": 0, "leaf": false, "nodes": [{"code": "M80", "name": "業績管理", "parentCode": "M8", "order": 0, "leaf": false, "nodes": [{"code": "M80-00", "name": "各區理財達成率-分行", "parentCode": "M80", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-00", "parentMenuCode": "M80", "menuName": "各區理財達成率-分行", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M80-01", "name": "各區理財達成率-理專", "parentCode": "M80", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-01", "parentMenuCode": "M80", "menuName": "各區理財達成率-理專", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": true, "view": true, "export": true, "verify": true}, {"code": "M80-02", "name": "理專職階維護", "parentCode": "M80", "order": 0, "leaf": false, "nodes": [{"code": "M80-020", "name": "理專職階上傳", "parentCode": "M80-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-020", "parentMenuCode": "M80-02", "menuName": "理專職階上傳", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M80-021", "name": "理專職階維護", "parentCode": "M80-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-021", "parentMenuCode": "M80-02", "menuName": "理專職階維護", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M80-022", "name": "理專職階紀錄查詢", "parentCode": "M80-02", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-022", "parentMenuCode": "M80-02", "menuName": "理專職階紀錄查詢", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M80-02", "parentMenuCode": "M80", "menuName": "理專職階維護", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M80-03", "name": "業績目標維護", "parentCode": "M80", "order": 0, "leaf": false, "nodes": [{"code": "M80-030", "name": "業績目標上傳", "parentCode": "M80-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-030", "parentMenuCode": "M80-03", "menuName": "業績目標上傳", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M80-031", "name": "業績目標維護", "parentCode": "M80-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-031", "parentMenuCode": "M80-03", "menuName": "業績目標維護", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M80-032", "name": "業績目標查詢", "parentCode": "M80-03", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-032", "parentMenuCode": "M80-03", "menuName": "業績目標查詢", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M80-03", "parentMenuCode": "M80", "menuName": "業績目標維護", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M80-04", "name": "區督導報表", "parentCode": "M80", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-04", "parentMenuCode": "M80", "menuName": "區督導報表", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M80-05", "name": "理財業績日報告", "parentCode": "M80", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-05", "parentMenuCode": "M80", "menuName": "理財業績日報告", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M80-06", "name": "行員轉介報表", "parentCode": "M80", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-06", "parentMenuCode": "M80", "menuName": "行員轉介報表", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M80-07", "name": "證券業績維護", "parentCode": "M80", "order": 0, "leaf": false, "nodes": [{"code": "M80-070", "name": "證券業績上傳", "parentCode": "M80-07", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-070", "parentMenuCode": "M80-07", "menuName": "證券業績上傳", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}, {"code": "M80-071", "name": "證券業績查詢", "parentCode": "M80-07", "order": 0, "leaf": false, "nodes": [], "menuCode": "M80-071", "parentMenuCode": "M80-07", "menuName": "證券業績查詢", "depths": 4, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M80-07", "parentMenuCode": "M80", "menuName": "證券業績維護", "depths": 3, "progEdit": true, "progView": true, "progExport": true, "progVerify": true, "edit": false, "view": false, "export": false, "verify": false}], "menuCode": "M80", "parentMenuCode": "M8", "menuName": "業績管理", "depths": 2, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}], "menuCode": "M8", "parentMenuCode": "", "menuName": "報表管理", "depths": 1, "progEdit": false, "progView": false, "progExport": false, "progVerify": false, "edit": true, "view": true, "export": true, "verify": true}]