{"status": 200, "data": [{"codeValue": "Y", "codeName": "是"}, {"codeValue": "N", "codeName": "否"}], "timestamp": "2025/06/30", "sqlTracer": [{"data": [{"codeValue": "Y", "codeName": "是"}, {"codeValue": "N", "codeName": "否"}], "sqlInfo": " SELECT ACD.CODE_VALUE , ACD.CODE_NAME  FROM ADM_CODE_DETAIL ACD  WHERE ACD.CODE_TYPE = :codeType  ORDER BY ACD.SHOW_ORDER ,class com.bi.pbs.adm.web.model.CodeDetailResp,{codeType=SELECT_YN}"}]}