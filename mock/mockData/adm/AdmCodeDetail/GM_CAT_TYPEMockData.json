{"status": 200, "data": [{"codeType": "GM_CAT_TYPE", "codeValue": "M", "codeName": "主分類", "codeDesc": "文件訊息公告分類", "showOrder": 1}, {"codeType": "GM_CAT_TYPE", "codeValue": "S", "codeName": "次分類", "codeDesc": "文件訊息公告分類", "showOrder": 2}], "timestamp": "2025/06/30", "sqlTracer": [{"data": [{"codeType": "GM_CAT_TYPE", "codeValue": "M", "codeName": "主分類", "codeDesc": "文件訊息公告分類", "showOrder": 1}, {"codeType": "GM_CAT_TYPE", "codeValue": "S", "codeName": "次分類", "codeDesc": "文件訊息公告分類", "showOrder": 2}], "sqlInfo": "SELECT ACD.CODE_TYPE, ACD.CODE_VALUE, ACD.CODE_NAME, ACD.CODE_DESC, ACD.SHOW_ORDER FROM ADM_CODE_DETAIL ACD WHERE 1=1  AND ACD.CODE_TYPE in( :codeType ) ORDER BY ACD.CODE_TYPE, ACD.SHOW_ORDER ,class com.bi.pbs.adm.model.AdmCodeDetail,{codeType=[GM_CAT_TYPE]}"}]}