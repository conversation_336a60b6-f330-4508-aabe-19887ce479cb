{"status": 200, "data": [{"codeValue": "0", "codeName": "有配息"}, {"codeValue": "1", "codeName": "年配"}, {"codeValue": "2", "codeName": "半年配"}, {"codeValue": "3", "codeName": "季配"}, {"codeValue": "4", "codeName": "月配"}, {"codeValue": "5", "codeName": "無配息"}, {"codeValue": "6", "codeName": "其他"}], "timestamp": "2025/06/30", "sqlTracer": [{"data": [{"codeValue": "0", "codeName": "有配息"}, {"codeValue": "1", "codeName": "年配"}, {"codeValue": "2", "codeName": "半年配"}, {"codeValue": "3", "codeName": "季配"}, {"codeValue": "4", "codeName": "月配"}, {"codeValue": "5", "codeName": "無配息"}, {"codeValue": "6", "codeName": "其他"}], "sqlInfo": " SELECT ACD.CODE_VALUE , ACD.CODE_NAME  FROM ADM_CODE_DETAIL ACD  WHERE ACD.CODE_TYPE = :codeType  ORDER BY ACD.SHOW_ORDER ,class com.bi.pbs.adm.web.model.CodeDetailResp,{codeType=INT_FREQ_UNITTYPE}"}]}