{"status": 200, "data": [{"roleCode": "29", "roleName": "財管部幹部", "branCode": "891", "branName": "財富管理部    ", "posCode": "891_29"}, {"posCode": "897_02"}], "timestamp": "2025/04/17", "sqlTracer": [{"data": [{"roleCode": "29", "roleName": "財管部幹部", "branCode": "891", "branName": "財富管理部    ", "posCode": "891_29"}, {"posCode": "897_02"}], "sqlInfo": " SELECT AB.<PERSON>AN_NAME, AR.ROLE_CODE, AR.ROLE_NAME, AUPM.POS_CODE, AB.BRAN_CODE  FROM (SELECT USER_CODE  FROM ADM_USERS  WHERE USER_CODE = :userCode  ) AU  JOIN ADM_USER_POS_MAP AUPM ON AU.USER_CODE = AUPM.USER_CODE  LEFT JOIN ADM_POSITIONS AP ON AUPM.POS_CODE = AP.POS_CODE  LEFT JOIN ADM_BRANCHES AB ON AP.BRAN_CODE = AB.BRAN_CODE AND AB.BU_CODE= AP.BU_CODE  LEFT JOIN ADM_ROLES AR ON AP.ROLE_CODE = AR.ROLE_CODE ,class com.bi.pbs.adm.web.model.UsersBranRoleInfoResp,{userCode=014605}"}]}