{"status": 200, "data": [{"componentId": "vue-index-quick-query", "showOrder": "1"}, {"componentId": "vue-index-event", "showOrder": "2"}, {"componentId": "vue-index-pending-list", "showOrder": "3"}, {"componentId": "vue-index-perform", "showOrder": "5"}, {"componentId": "vue-index-calendar", "showOrder": "6"}, {"componentId": "vue-index-bulletin", "showOrder": "7"}, {"componentId": "vue-index-finance-msg", "showOrder": "8"}], "timestamp": "2025/07/16", "sqlTracer": [{"data": [{"componentId": "vue-index-quick-query", "showOrder": "1"}, {"componentId": "vue-index-event", "showOrder": "2"}, {"componentId": "vue-index-pending-list", "showOrder": "3"}, {"componentId": "vue-index-perform", "showOrder": "5"}, {"componentId": "vue-index-calendar", "showOrder": "6"}, {"componentId": "vue-index-bulletin", "showOrder": "7"}, {"componentId": "vue-index-finance-msg", "showOrder": "8"}], "sqlInfo": " SELECT ADIL.COMPONENT_ID,ADIL.SHOW_ORDER  FROM ADM_ROLES AR  INNER JOIN ADM_DASHBOARD_GROUP_MAP ADGM ON ADGM.GROUP_CODE = AR.HOME_TYPE  INNER JOIN ADM_DASHBOARD_ITEM_LIST ADIL ON ADIL.ITEM_CODE = ADGM.ITEM_CODE  WHERE AR.ROLE_CODE = :roleCode  AND ADIL.REMOVE_YN <>'Y'  ORDER BY ADIL.SHOW_ORDER ,class com.bi.pbs.adm.web.model.HomeComponentResp,{roleCode=98}"}]}