{"status": 200, "data": [{"createDt": "2024/12/19", "createBy": "SYS_BATCH", "groupCode": "AI_BATCH", "groupName": "AI檔案匯入", "groupType": "D1", "priority": 1, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 12, "checkCalYn": "N", "holidayYn": "N", "autoYn": "Y"}, {"createDt": "2024/12/19", "createBy": "SYS_BATCH", "groupCode": "DATA_BACKUP", "groupName": "每日備份NAS資料", "groupType": "D1", "priority": 1, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 12, "checkCalYn": "N", "holidayYn": "N", "autoYn": "Y"}, {"createDt": "2025/05/20", "createBy": "SYSBATCH", "groupCode": "EXCEPT_SALES_BATCH", "groupName": "例外銷售商品", "groupType": "M", "priority": 1, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 12, "checkCalYn": "N", "holidayYn": "N", "autoYn": "Y"}, {"createDt": "2025/05/10", "createBy": "SYSBATCH", "groupCode": "GetRptCAACNM_0400", "groupName": "手續費收入月報AUM配置組合報表資料", "groupType": "T", "priority": 1, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 12, "checkCalYn": "Y", "holidayYn": "N", "autoYn": "Y"}, {"createDt": "2025/05/19", "createBy": "SYSBATCH", "groupCode": "GetRptSnTran_1000", "groupName": "客戶結構型商品交易資料", "groupType": "D1", "priority": 2, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 12, "checkCalYn": "Y", "holidayYn": "N", "autoYn": "Y"}, {"createDt": "2025/05/19", "createBy": "SYSBATCH", "groupCode": "HD_GetRptSnTran_1000", "groupName": "客戶結構型商品交易資料", "groupType": "D1", "priority": 2, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 12, "checkCalYn": "N", "holidayYn": "Y", "autoYn": "Y"}, {"createDt": "2024/05/22", "createBy": "SYS", "groupCode": "MAINBATCH", "groupName": "主批", "groupType": "D1", "priority": 1, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 5, "checkCalYn": "N", "holidayYn": "N", "autoYn": "Y"}, {"createDt": "2025/06/25", "createBy": "SYSBATCH", "groupCode": "TESTGRP", "groupName": "測試批次重啟", "groupType": "T", "priority": 1, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 12, "checkCalYn": "Y", "holidayYn": "N", "autoYn": "Y"}], "timestamp": "2025/07/24", "sqlTracer": [{"data": {"paramValue": "FCB_BATCH.DBO.{TableName}"}, "sqlInfo": " SELECT PARAM_VALUE FROM ADM_PARAM  WHERE PARAM_TYPE = :type  AND PARAM_CODE = :code ,class com.fcb.pbs.adm.model.AdmParam,{code=PREF.LINKSTRING, type=ADM}"}, {"data": [{"createDt": "2024/12/19", "createBy": "SYS_BATCH", "groupCode": "AI_BATCH", "groupName": "AI檔案匯入", "groupType": "D1", "priority": 1, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 12, "checkCalYn": "N", "holidayYn": "N", "autoYn": "Y"}, {"createDt": "2024/12/19", "createBy": "SYS_BATCH", "groupCode": "DATA_BACKUP", "groupName": "每日備份NAS資料", "groupType": "D1", "priority": 1, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 12, "checkCalYn": "N", "holidayYn": "N", "autoYn": "Y"}, {"createDt": "2025/05/20", "createBy": "SYSBATCH", "groupCode": "EXCEPT_SALES_BATCH", "groupName": "例外銷售商品", "groupType": "M", "priority": 1, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 12, "checkCalYn": "N", "holidayYn": "N", "autoYn": "Y"}, {"createDt": "2025/05/10", "createBy": "SYSBATCH", "groupCode": "GetRptCAACNM_0400", "groupName": "手續費收入月報AUM配置組合報表資料", "groupType": "T", "priority": 1, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 12, "checkCalYn": "Y", "holidayYn": "N", "autoYn": "Y"}, {"createDt": "2025/05/19", "createBy": "SYSBATCH", "groupCode": "GetRptSnTran_1000", "groupName": "客戶結構型商品交易資料", "groupType": "D1", "priority": 2, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 12, "checkCalYn": "Y", "holidayYn": "N", "autoYn": "Y"}, {"createDt": "2025/05/19", "createBy": "SYSBATCH", "groupCode": "HD_GetRptSnTran_1000", "groupName": "客戶結構型商品交易資料", "groupType": "D1", "priority": 2, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 12, "checkCalYn": "N", "holidayYn": "Y", "autoYn": "Y"}, {"createDt": "2024/05/22", "createBy": "SYS", "groupCode": "MAINBATCH", "groupName": "主批", "groupType": "D1", "priority": 1, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 5, "checkCalYn": "N", "holidayYn": "N", "autoYn": "Y"}, {"createDt": "2025/06/25", "createBy": "SYSBATCH", "groupCode": "TESTGRP", "groupName": "測試批次重啟", "groupType": "T", "priority": 1, "datadtDiffSystdt": 0, "execYn": "Y", "waitingHour": 12, "checkCalYn": "Y", "holidayYn": "N", "autoYn": "Y"}], "sqlInfo": " SELECT DISTINCT AWBG.*  FROM FCB_BATCH.DBO.ADM_WMS_BATCH_GROUPS AWBG  WHERE 1=1 AND EXEC_YN = 'Y'  ORDER BY GROUP_CODE ,class com.fcb.pbs.adm.model.AdmWmsBatchGroups,[Ljava.lang.Object;@1bdbb190"}]}