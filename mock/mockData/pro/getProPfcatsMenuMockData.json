{"status": 200, "data": [{"pfcatCode": "FUND", "pfcatName": "信託-基金"}, {"pfcatCode": "FB", "pfcatName": "信託-海外債"}, {"pfcatCode": "ETF", "pfcatName": "信託-ETF"}, {"pfcatCode": "SP", "pfcatName": "信託-境外結構型商品"}, {"pfcatCode": "PFD", "pfcatName": "信託-海外股票"}, {"pfcatCode": "DCD", "pfcatName": "銀行結構型商品"}, {"pfcatCode": "INS", "pfcatName": "人身保險"}], "timestamp": "2025/06/30", "sqlTracer": [{"data": {"paramValue": "FUND,ETF,FB,INS,PFD,SP,DCD"}, "sqlInfo": " SELECT PARAM_VALUE FROM ADM_PARAM  WHERE PARAM_TYPE = :type  AND PARAM_CODE = :code ,class com.bi.pbs.adm.model.AdmParam,{code=SHOW_PFCAT_CODE, type=PRO}"}, {"data": [{"pfcatCode": "FUND", "pfcatName": "信託-基金"}, {"pfcatCode": "FB", "pfcatName": "信託-海外債"}, {"pfcatCode": "ETF", "pfcatName": "信託-ETF"}, {"pfcatCode": "SP", "pfcatName": "信託-境外結構型商品"}, {"pfcatCode": "PFD", "pfcatName": "信託-海外股票"}, {"pfcatCode": "DCD", "pfcatName": "銀行結構型商品"}, {"pfcatCode": "INS", "pfcatName": "人身保險"}], "sqlInfo": " SELECT PFCAT_CODE, PFCAT_NAME FROM PRO_PFCATS  WHERE PFCAT_CODE IN (:pfcatCodes)  ORDER BY SHOW_ORDER ,class com.bi.pbs.cus.web.model.ProPfcatsResp,{pfcatCodes=[FUND, ETF, FB, INS, PFD, SP, DCD]}"}]}