{"status": 200, "data": [{"curCode": "TWD", "curName": "新台幣"}, {"curCode": "USD", "curName": "美金"}, {"curCode": "EUR", "curName": "歐元"}, {"curCode": "CAD", "curName": "加拿大幣"}, {"curCode": "CNY", "curName": "人民幣"}, {"curCode": "ZAR", "curName": "南非幣"}, {"curCode": "HKD", "curName": "港幣"}, {"curCode": "JPY", "curName": "日幣"}, {"curCode": "SGD", "curName": "新加坡幣"}, {"curCode": "THB", "curName": "泰銖"}, {"curCode": "NZD", "curName": "紐西蘭幣"}, {"curCode": "AUD", "curName": "澳洲幣"}, {"curCode": "GBP", "curName": "英鎊"}, {"curCode": "CHF", "curName": "瑞士法郎"}], "timestamp": "2025/06/30", "sqlTracer": [{"data": {"paramValue": "TWD,USD,GBP,HKD,AUD,SGD,CHF,CAD,JPY,ZAR,THB,SEK,NZD,EUR,CNY,TRY"}, "sqlInfo": " SELECT PARAM_VALUE FROM ADM_PARAM  WHERE PARAM_TYPE = :type  AND PARAM_CODE = :code ,class com.bi.pbs.adm.model.AdmParam,{code=GROUP_CUR_CODE, type=PRO}"}, {"data": [{"curCode": "TWD", "curName": "新台幣"}, {"curCode": "USD", "curName": "美金"}, {"curCode": "EUR", "curName": "歐元"}, {"curCode": "CAD", "curName": "加拿大幣"}, {"curCode": "CNY", "curName": "人民幣"}, {"curCode": "ZAR", "curName": "南非幣"}, {"curCode": "HKD", "curName": "港幣"}, {"curCode": "JPY", "curName": "日幣"}, {"curCode": "SGD", "curName": "新加坡幣"}, {"curCode": "THB", "curName": "泰銖"}, {"curCode": "NZD", "curName": "紐西蘭幣"}, {"curCode": "AUD", "curName": "澳洲幣"}, {"curCode": "GBP", "curName": "英鎊"}, {"curCode": "CHF", "curName": "瑞士法郎"}], "sqlInfo": " SELECT *  FROM PRO_CURRENCIES  WHERE CUR_CODE IN ( :groupCurCodes )  ORDER BY SHOW_ORDER ,class com.bi.pbs.pro.web.model.CurrenciesResp,{groupCurCodes=[TWD, USD, GBP, HKD, AUD, SGD, CHF, CAD, JPY, ZAR, THB, SEK, NZD, EUR, CNY, TRY]}"}]}