{"status": 200, "data": [{"docId": "DF202504180005", "docName": "80472test", "validDt": "2025/04/18", "expireDt": "2025/04/18"}, {"docId": "DF202504180001", "docName": "吃反", "validDt": "2025/04/09", "expireDt": "2025/04/26"}, {"docId": "DF202503190003", "docName": "TEST0319", "validDt": "2025/03/19", "expireDt": "2025/03/28"}, {"docId": "DF202503190002", "docName": "ttt", "validDt": "2025/03/19", "expireDt": "2025/03/20"}, {"docId": "DF202503030001", "docName": "新聞事件測試", "validDt": "2025/03/03", "expireDt": "2025/03/31"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28"}, {"docId": "DF202502110008", "docName": "新聞事件-可否提供給客戶欄位顯示", "validDt": "2025/02/01", "expireDt": "2025/02/28"}, {"docId": "DF202502110007", "docName": "新聞事件測試", "validDt": "2025/02/01", "expireDt": "2025/02/28"}], "timestamp": "2025/07/17", "sqlTracer": [{"data": [{"docId": "DF202504180005", "docName": "80472test", "validDt": "2025/04/18", "expireDt": "2025/04/18"}, {"docId": "DF202504180001", "docName": "吃反", "validDt": "2025/04/09", "expireDt": "2025/04/26"}, {"docId": "DF202503190003", "docName": "TEST0319", "validDt": "2025/03/19", "expireDt": "2025/03/28"}, {"docId": "DF202503190002", "docName": "ttt", "validDt": "2025/03/19", "expireDt": "2025/03/20"}, {"docId": "DF202503030001", "docName": "新聞事件測試", "validDt": "2025/03/03", "expireDt": "2025/03/31"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28"}, {"docId": "DF202502110008", "docName": "新聞事件-可否提供給客戶欄位顯示", "validDt": "2025/02/01", "expireDt": "2025/02/28"}, {"docId": "DF202502110007", "docName": "新聞事件測試", "validDt": "2025/02/01", "expireDt": "2025/02/28"}], "sqlInfo": " SELECT TOP 8 GD.DOC_ID,DOC_NAME,VALID_DT,EXPIRE_DT  FROM GEN_DOCUMENTS GD  INNER JOIN GEN_NEWS GN ON GD.DOC_ID = GN.DOC_ID  ORDER BY GD.PRIORITY,GD.VALID_DT DESC ,class com.bi.pbs.gen.web.model.DocHomeListResp,{}"}]}