{"status": 200, "data": {"content": [{"docId": "DF202502110002", "docName": "TEST0211測試2", "validDt": "2025/02/11", "expireDt": "2025/02/28", "priorityName": "高", "showCusYnName": "Y", "proList": [], "fileList": [{"fileName": "DOC2025021100001.pdf", "showName": "G-026-請假規則.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110002", "docFileId": "DOC2025021100001"}, {"fileName": "DOC2025021100002.doc", "showName": "SNB-SDS-金融訊息-金融訊息維護.doc", "contentType": "application/msword", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110002", "docFileId": "DOC2025021100002"}]}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": true, "totalElements": 1, "totalPages": 1, "first": true, "size": 10, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 1, "empty": false}, "timestamp": "2025/07/22", "sqlTracer": [{"data": {"content": [{"docId": "DF202502110002", "docName": "TEST0211測試2", "validDt": "2025/02/11", "expireDt": "2025/02/28", "priorityName": "高", "showCusYnName": "Y", "proList": [], "fileList": [{"fileName": "DOC2025021100001.pdf", "showName": "G-026-請假規則.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110002", "docFileId": "DOC2025021100001"}, {"fileName": "DOC2025021100002.doc", "showName": "SNB-SDS-金融訊息-金融訊息維護.doc", "contentType": "application/msword", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110002", "docFileId": "DOC2025021100002"}]}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": true, "totalElements": 1, "totalPages": 1, "first": true, "size": 10, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 1, "empty": false}, "sqlInfo": "SELECT GD.DOC_ID ,GD.DOC_NAME, GD.VALID_DT, GD.EXPIRE_DT, GD.SHOWCUS_YN, GD.PRIORITY ,ACD_P.CODE_NAME AS PRIORITY_NAME ,ACD_C.CODE_NAME AS SHOW_CUS_NAME FROM GEN_DOCUMENTS GD INNER JOIN GEN_DOC_MKT GDM ON GDM.DOC_ID = GD.DOC_ID LEFT JOIN ADM_CODE_DETAIL ACD_P ON ACD_P.CODE_TYPE ='DOC_PRIORITY' AND ACD_P.CODE_VALUE = GD.PRIORITY LEFT JOIN ADM_CODE_DETAIL ACD_C ON ACD_C.CODE_TYPE ='DOC_SHOWCUS_YN' AND ACD_C.CODE_VALUE = GD.SHOWCUS_YN WHERE GDM.MAIN_TYPE_CODE = :mainTypeCode AND GDM.SUB_TYPE_CODE = :subTypeCode ,Page request [number: 0, size 10, sort: DOC_ID: ASC],class com.bi.pbs.gen.web.model.GetDocMktsResp,{mainTypeCode=DMTME04, subTypeCode=DMTME0401}"}, {"data": [], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202502110002}"}, {"data": [{"fileName": "DOC2025021100001.pdf", "showName": "G-026-請假規則.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110002", "docFileId": "DOC2025021100001"}, {"fileName": "DOC2025021100002.doc", "showName": "SNB-SDS-金融訊息-金融訊息維護.doc", "contentType": "application/msword", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110002", "docFileId": "DOC2025021100002"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502110002}"}]}