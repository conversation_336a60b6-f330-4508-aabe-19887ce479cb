{"status": 200, "data": [{"typeCode": "DSTMO0111", "typeName": "日報", "cnt": "0"}, {"typeCode": "DSTMO0112", "typeName": "週報", "cnt": "2"}, {"typeCode": "DSTMO0113", "typeName": "月報", "cnt": "0"}, {"typeCode": "DSTMO0114", "typeName": "季報", "cnt": "0"}, {"typeCode": "DSTMO0115", "typeName": "年報", "cnt": "0"}], "timestamp": "2025/07/18", "sqlTracer": [{"data": [{"typeCode": "DSTMO0111", "typeName": "日報", "cnt": "0"}, {"typeCode": "DSTMO0112", "typeName": "週報", "cnt": "2"}, {"typeCode": "DSTMO0113", "typeName": "月報", "cnt": "0"}, {"typeCode": "DSTMO0114", "typeName": "季報", "cnt": "0"}, {"typeCode": "DSTMO0115", "typeName": "年報", "cnt": "0"}], "sqlInfo": "SELECT GDT.TYPE_CODE,GDT.TYPE_NAME,COALESCE (M.CNT,0) AS CNT FROM GEN_DOC_TYPE GDT LEFT JOIN ( SELECT GMO.SUB_CAT_CODE,count(*) AS CNT FROM GEN_MKT_OUTLOOK GMO WHERE GMO.MAIN_CAT_CODE = :mainTypeCode GROUP BY GMO.SUB_CAT_CODE )M ON GDT.TYPE_CODE =M.SUB_CAT_CODE WHERE GDT.CAT_CODE ='MKTOUTLOOK' AND GDT.MAIN_TYPE_CODE= :mainTypeCode ,class com.bi.pbs.gen.web.model.GetDocMktCntResp,{mainTypeCode=DMTMO03}"}]}