{"status": 200, "data": {"content": [{"docId": "DF202502110003", "docName": "TEST0211發行機構測試", "docDesc": "TEST0211發行機構測試", "validDt": "2025/02/11", "expireDt": "2025/02/28", "priority": "1", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "issuerList": [{"issuerCode": "ABN", "issuerName": "荷蘭銀行"}, {"issuerCode": "BANQUE", "issuerName": "盧森堡國家儲蓄銀行"}, {"issuerCode": "BARCLAY", "issuerName": "BARCLAYS BANK PLC"}, {"issuerCode": "BEAWMS", "issuerName": "永豐金證券股份有限公司"}], "fileList": [{"fileName": "DOC2025021100003.pdf", "showName": "請假流程.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110003", "docFileId": "DOC2025021100003"}, {"fileName": "DOC2025021100004.pdf", "showName": "G-026-請假規則.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110003", "docFileId": "DOC2025021100004"}], "createBy": "112790", "createDt": "2025/02/11"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28", "priority": "2", "priorityName": "中", "showCusName": "可提供給客戶", "showCusYn": "Y", "issuerList": [{"issuerCode": "ABN", "issuerName": "荷蘭銀行"}, {"issuerCode": "BANQUE", "issuerName": "盧森堡國家儲蓄銀行"}], "fileList": [{"fileName": "DOC2025021500002.pdf", "showName": "一般管理.pdf", "contentType": "application/pdf", "filePath": "/usr/local/tomcat/data/PBS/DOC/GEN/TEMP/DF202502150002", "docFileId": "DOC2025021500002"}], "createBy": "112790", "createDt": "2025/02/15", "modifyBy": "112790", "modifyDt": "2025/02/24"}, {"docId": "DF202502260002", "docName": "發行機構報告測試", "docDesc": "發行機構報告測試-摘要", "validDt": "2025/02/26", "expireDt": "2025/03/08", "priority": "1", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "issuerList": [{"issuerCode": "BARCLAY", "issuerName": "BARCLAYS BANK PLC"}, {"issuerCode": "BNP", "issuerName": "法國巴黎銀行"}], "fileList": [{"fileName": "DOC2025022600002.docx", "showName": "get-underUserDeputies.docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260002", "docFileId": "DOC2025022600002"}], "createBy": "112790", "createDt": "2025/02/26"}, {"docId": "DF202504180001", "docName": "吃反", "docDesc": "????", "validDt": "2025/04/09", "expireDt": "2025/04/26", "priority": "1", "priorityName": "高", "showCusName": "僅供內部使用", "showCusYn": "N", "issuerList": [{"issuerCode": "ABN", "issuerName": "荷蘭銀行"}, {"issuerCode": "BARCLAY", "issuerName": "BARCLAYS BANK PLC"}], "createBy": "112790", "createDt": "2025/04/18"}, {"docId": "DF202504180002", "docName": "ㄍ", "validDt": "2025/04/18", "expireDt": "2025/04/19", "priority": "1", "priorityName": "高", "showCusName": "僅供內部使用", "showCusYn": "N", "issuerList": [{"issuerCode": "ABN", "issuerName": "荷蘭銀行"}, {"issuerCode": "BANQUE", "issuerName": "盧森堡國家儲蓄銀行"}], "createBy": "112790", "createDt": "2025/04/18", "modifyBy": "112790", "modifyDt": "2025/04/18"}, {"docId": "DF202504180003", "docName": "TEST0418-發行機構_1", "validDt": "2025/04/18", "expireDt": "2025/04/30", "priority": "1", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "issuerList": [{"issuerCode": "ABN", "issuerName": "荷蘭銀行"}, {"issuerCode": "BEAWMS", "issuerName": "永豐金證券股份有限公司"}], "createBy": "127279", "createDt": "2025/04/18", "modifyBy": "127279", "modifyDt": "2025/04/22"}, {"docId": "DF202504180004", "docName": "TEST0418-發行機構_2", "validDt": "2025/04/18", "expireDt": "2025/04/29", "priority": "1", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "issuerList": [{"issuerCode": "BARCLAY", "issuerName": "BARCLAYS BANK PLC"}, {"issuerCode": "BEAWMS", "issuerName": "永豐金證券股份有限公司"}], "createBy": "127279", "createDt": "2025/04/18", "modifyBy": "127279", "modifyDt": "2025/04/18"}, {"docId": "DF202504180005", "docName": "80472test", "docDesc": "aaa?？？", "validDt": "2025/04/18", "expireDt": "2025/04/18", "priority": "1", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "issuerList": [{"issuerCode": "ABN", "issuerName": "荷蘭銀行"}, {"issuerCode": "CDCIXIS", "issuerName": "法國國家儲備銀行"}], "fileList": [{"fileName": "DOC2025041800001.docx", "showName": "get-underUserDeputies.docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202504180005", "docFileId": "DOC2025041800001"}, {"fileName": "DOC2025041800002.xlsx", "showName": "complaintUpload (1).xlsx", "contentType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filePath": "GEN/TEMP/DF202504180005", "docFileId": "DOC2025041800002"}], "createBy": "112790", "createDt": "2025/04/18", "modifyBy": "112790", "modifyDt": "2025/04/18"}], "pageable": {"pageNumber": 0, "pageSize": 20, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalPages": 1, "last": true, "totalElements": 8, "numberOfElements": 8, "first": true, "size": 20, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": false}, "timestamp": "2025/07/18", "sqlTracer": [{"data": {"content": [{"docId": "DF202502110003", "docName": "TEST0211發行機構測試", "validDt": "2025/02/11", "expireDt": "2025/02/28", "priority": "1", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "docDesc": "TEST0211發行機構測試", "createBy": "112790", "createDt": "2025/02/11"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28", "priority": "2", "priorityName": "中", "showCusName": "可提供給客戶", "showCusYn": "Y", "createBy": "112790", "createDt": "2025/02/15", "modifyBy": "112790", "modifyDt": "2025/02/24"}, {"docId": "DF202502260002", "docName": "發行機構報告測試", "validDt": "2025/02/26", "expireDt": "2025/03/08", "priority": "1", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "docDesc": "發行機構報告測試-摘要", "createBy": "112790", "createDt": "2025/02/26"}, {"docId": "DF202504180001", "docName": "吃反", "validDt": "2025/04/09", "expireDt": "2025/04/26", "priority": "1", "priorityName": "高", "showCusName": "僅供內部使用", "showCusYn": "N", "docDesc": "????", "createBy": "112790", "createDt": "2025/04/18"}, {"docId": "DF202504180002", "docName": "ㄍ", "validDt": "2025/04/18", "expireDt": "2025/04/19", "priority": "1", "priorityName": "高", "showCusName": "僅供內部使用", "showCusYn": "N", "createBy": "112790", "createDt": "2025/04/18", "modifyBy": "112790", "modifyDt": "2025/04/18"}, {"docId": "DF202504180003", "docName": "TEST0418-發行機構_1", "validDt": "2025/04/18", "expireDt": "2025/04/30", "priority": "1", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "createBy": "127279", "createDt": "2025/04/18", "modifyBy": "127279", "modifyDt": "2025/04/22"}, {"docId": "DF202504180004", "docName": "TEST0418-發行機構_2", "validDt": "2025/04/18", "expireDt": "2025/04/29", "priority": "1", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "createBy": "127279", "createDt": "2025/04/18", "modifyBy": "127279", "modifyDt": "2025/04/18"}, {"docId": "DF202504180005", "docName": "80472test", "validDt": "2025/04/18", "expireDt": "2025/04/18", "priority": "1", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "docDesc": "aaa?？？", "createBy": "112790", "createDt": "2025/04/18", "modifyBy": "112790", "modifyDt": "2025/04/18"}], "pageable": {"pageNumber": 0, "pageSize": 20, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalPages": 1, "last": true, "totalElements": 8, "numberOfElements": 8, "first": true, "size": 20, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": false}, "sqlInfo": " SELECT          GD.DOC_ID  \t\t,GD.DOC_NAME  \t    ,GD.DOC_DESC  \t\t,GD.VALID_DT  \t\t,GD.EXPIRE_DT  \t\t,GD.SHOWCUS_YN  \t\t,GD.PRIORITY  \t    ,GD.CREATE_BY  \t    ,GD.CREATE_DT  \t    ,GD.MODIFY_BY  \t    ,GD.MODIFY_DT  \t\t,ACD_P.CODE_NAME AS PRIORITY_NAME         ,ACD_C.CODE_NAME AS SHOW_CUS_NAME  FROM  GEN_DOCUMENTS  GD  LEFT JOIN  ADM_CODE_DETAIL ACD_P  ON ACD_P.CODE_TYPE ='DOC_PRIORITY'  AND ACD_P.CODE_VALUE = GD.PRIORITY  LEFT JOIN  ADM_CODE_DETAIL ACD_C  ON ACD_C.CODE_TYPE ='DOC_SHOWCUS_YN'  AND ACD_C.CODE_VALUE = GD.SHOWCUS_YN  WHERE DOC_ID IN (SELECT DOC_ID FROM GEN_ISSUER) ,Page request [number: 0, size 20, sort: DOC_ID: ASC],class com.bi.pbs.gen.web.model.SelectDocumentResp,[Ljava.lang.Object;@3afe858b"}, {"data": [{"createDt": "2025-07-18T15:12:44.846121", "createBy": "SYS", "docId": "DF202502110003", "issuerCode": "ABN", "issuerName": "荷蘭銀行"}, {"createDt": "2025-07-18T15:12:44.846245", "createBy": "SYS", "docId": "DF202502110003", "issuerCode": "BANQUE", "issuerName": "盧森堡國家儲蓄銀行"}, {"createDt": "2025-07-18T15:12:44.846289", "createBy": "SYS", "docId": "DF202502110003", "issuerCode": "BARCLAY", "issuerName": "BARCLAYS BANK PLC"}, {"createDt": "2025-07-18T15:12:44.846338", "createBy": "SYS", "docId": "DF202502110003", "issuerCode": "BEAWMS", "issuerName": "永豐金證券股份有限公司"}, {"createDt": "2025-07-18T15:12:44.846386", "createBy": "SYS", "docId": "DF202502150002", "issuerCode": "ABN", "issuerName": "荷蘭銀行"}, {"createDt": "2025-07-18T15:12:44.846434", "createBy": "SYS", "docId": "DF202502150002", "issuerCode": "BANQUE", "issuerName": "盧森堡國家儲蓄銀行"}, {"createDt": "2025-07-18T15:12:44.846483", "createBy": "SYS", "docId": "DF202502260002", "issuerCode": "BARCLAY", "issuerName": "BARCLAYS BANK PLC"}, {"createDt": "2025-07-18T15:12:44.846534", "createBy": "SYS", "docId": "DF202502260002", "issuerCode": "BNP", "issuerName": "法國巴黎銀行"}, {"createDt": "2025-07-18T15:12:44.846586", "createBy": "SYS", "docId": "DF202504180001", "issuerCode": "ABN", "issuerName": "荷蘭銀行"}, {"createDt": "2025-07-18T15:12:44.846625", "createBy": "SYS", "docId": "DF202504180001", "issuerCode": "BARCLAY", "issuerName": "BARCLAYS BANK PLC"}, {"createDt": "2025-07-18T15:12:44.846667", "createBy": "SYS", "docId": "DF202504180002", "issuerCode": "ABN", "issuerName": "荷蘭銀行"}, {"createDt": "2025-07-18T15:12:44.846712", "createBy": "SYS", "docId": "DF202504180002", "issuerCode": "BANQUE", "issuerName": "盧森堡國家儲蓄銀行"}, {"createDt": "2025-07-18T15:12:44.846755", "createBy": "SYS", "docId": "DF202504180003", "issuerCode": "ABN", "issuerName": "荷蘭銀行"}, {"createDt": "2025-07-18T15:12:44.846796", "createBy": "SYS", "docId": "DF202504180003", "issuerCode": "BEAWMS", "issuerName": "永豐金證券股份有限公司"}, {"createDt": "2025-07-18T15:12:44.846848", "createBy": "SYS", "docId": "DF202504180004", "issuerCode": "BARCLAY", "issuerName": "BARCLAYS BANK PLC"}, {"createDt": "2025-07-18T15:12:44.846892", "createBy": "SYS", "docId": "DF202504180004", "issuerCode": "BEAWMS", "issuerName": "永豐金證券股份有限公司"}, {"createDt": "2025-07-18T15:12:44.846935", "createBy": "SYS", "docId": "DF202504180005", "issuerCode": "ABN", "issuerName": "荷蘭銀行"}, {"createDt": "2025-07-18T15:12:44.846979", "createBy": "SYS", "docId": "DF202504180005", "issuerCode": "CDCIXIS", "issuerName": "法國國家儲備銀行"}], "sqlInfo": " SELECT      GI.DOC_ID,      GI.ISSUER_CODE,      PIS.ISSUER_NAME  FROM GEN_ISSUER GI  LEFT JOIN PRO_ISSUERS PIS ON GI.ISSUER_CODE = PIS.ISSUER_CODE ,class com.bi.pbs.gen.model.GenDocIssuersResp,{docId=null}"}, {"data": [{"fileName": "DOC2025021100003.pdf", "showName": "請假流程.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110003", "docFileId": "DOC2025021100003"}, {"fileName": "DOC2025021100004.pdf", "showName": "G-026-請假規則.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110003", "docFileId": "DOC2025021100004"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502110003}"}, {"data": [{"fileName": "DOC2025021500002.pdf", "showName": "一般管理.pdf", "contentType": "application/pdf", "filePath": "/usr/local/tomcat/data/PBS/DOC/GEN/TEMP/DF202502150002", "docFileId": "DOC2025021500002"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502150002}"}, {"data": [{"fileName": "DOC2025022600002.docx", "showName": "get-underUserDeputies.docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260002", "docFileId": "DOC2025022600002"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502260002}"}, {"data": [], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202504180001}"}, {"data": [], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202504180002}"}, {"data": [], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202504180003}"}, {"data": [], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202504180004}"}, {"data": [{"fileName": "DOC2025041800001.docx", "showName": "get-underUserDeputies.docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202504180005", "docFileId": "DOC2025041800001"}, {"fileName": "DOC2025041800002.xlsx", "showName": "complaintUpload (1).xlsx", "contentType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filePath": "GEN/TEMP/DF202504180005", "docFileId": "DOC2025041800002"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202504180005}"}]}