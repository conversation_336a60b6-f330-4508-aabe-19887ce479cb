{"status": 200, "data": {"content": [{"docId": "DF202502230001", "docName": "20250223_2_TEST", "validDt": "2025/02/24", "expireDt": "2025/02/24", "priorityName": "高", "showCusYnName": "N", "proList": [], "fileList": [{"fileName": "DOC2025022300001.pdf", "showName": "01_電子交換連線申請表(填寫範例).pdf", "contentType": "application/pdf", "filePath": "GEN\\TEMP\\DF202502230001", "docFileId": "DOC2025022300001"}]}, {"docId": "DF202502230002", "docName": "20250223_3_TEST", "validDt": "2025/02/24", "expireDt": "2025/02/24", "priorityName": "高", "showCusYnName": "N", "proList": [], "fileList": [{"fileName": "DOC2025022300002.pdf", "showName": "01_電子交換連線申請表(填寫範例).pdf", "contentType": "application/pdf", "filePath": "GEN\\TEMP\\DF202502230002", "docFileId": "DOC2025022300002"}]}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalPages": 1, "last": true, "totalElements": 2, "numberOfElements": 2, "first": true, "size": 10, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": false}, "timestamp": "2025/07/18", "sqlTracer": [{"data": {"content": [{"docId": "DF202502230001", "docName": "20250223_2_TEST", "validDt": "2025/02/24", "expireDt": "2025/02/24", "priorityName": "高", "showCusYnName": "N", "proList": [], "fileList": [{"fileName": "DOC2025022300001.pdf", "showName": "01_電子交換連線申請表(填寫範例).pdf", "contentType": "application/pdf", "filePath": "GEN\\TEMP\\DF202502230001", "docFileId": "DOC2025022300001"}]}, {"docId": "DF202502230002", "docName": "20250223_3_TEST", "validDt": "2025/02/24", "expireDt": "2025/02/24", "priorityName": "高", "showCusYnName": "N", "proList": [], "fileList": [{"fileName": "DOC2025022300002.pdf", "showName": "01_電子交換連線申請表(填寫範例).pdf", "contentType": "application/pdf", "filePath": "GEN\\TEMP\\DF202502230002", "docFileId": "DOC2025022300002"}]}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalPages": 1, "last": true, "totalElements": 2, "numberOfElements": 2, "first": true, "size": 10, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": false}, "sqlInfo": "SELECT GD.DOC_ID ,GD.DOC_NAME, GD.VALID_DT, GD.EXPIRE_DT, GD.SHOWCUS_YN, GD.PRIORITY ,ACD_P.CODE_NAME AS PRIORITY_NAME ,ACD_C.CODE_NAME AS SHOW_CUS_NAME FROM GEN_DOCUMENTS GD INNER JOIN GEN_MKT_OUTLOOK GMO ON GMO.DOC_ID = GD.DOC_ID LEFT JOIN ADM_CODE_DETAIL ACD_P ON ACD_P.CODE_TYPE ='DOC_PRIORITY' AND ACD_P.CODE_VALUE = GD.PRIORITY LEFT JOIN ADM_CODE_DETAIL ACD_C ON ACD_C.CODE_TYPE ='DOC_SHOWCUS_YN' AND ACD_C.CODE_VALUE = GD.SHOWCUS_YN WHERE GMO.MAIN_CAT_CODE = :mainTypeCode AND GMO.SUB_CAT_CODE = :subTypeCode ,Page request [number: 0, size 10, sort: DOC_ID: ASC],class com.bi.pbs.gen.web.model.GetDocMktsResp,{mainTypeCode=DMTMO03, subTypeCode=DSTMO0112}"}, {"data": [], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202502230001}"}, {"data": [{"fileName": "DOC2025022300001.pdf", "showName": "01_電子交換連線申請表(填寫範例).pdf", "contentType": "application/pdf", "filePath": "GEN\\TEMP\\DF202502230001", "docFileId": "DOC2025022300001"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502230001}"}, {"data": [], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202502230002}"}, {"data": [{"fileName": "DOC2025022300002.pdf", "showName": "01_電子交換連線申請表(填寫範例).pdf", "contentType": "application/pdf", "filePath": "GEN\\TEMP\\DF202502230002", "docFileId": "DOC2025022300002"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502230002}"}]}