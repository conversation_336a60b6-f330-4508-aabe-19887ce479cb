{"status": 200, "data": {"content": [], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalPages": 0, "last": true, "totalElements": 0, "numberOfElements": 0, "first": true, "size": 10, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": true}, "timestamp": "2025/07/17", "sqlTracer": [{"data": {"content": [], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalPages": 0, "last": true, "totalElements": 0, "numberOfElements": 0, "first": true, "size": 10, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": true}, "sqlInfo": "SELECT WM.*   , WMM.MSG_NAME   , WMMC.MAIN_CAT_NAME   , WMSC.SUB_CAT_NAME   , AU.USER_NAME   , WES.STATUS_NAME   , AU.USER_CODE   , WEH.STEP_CONTENT   , WEH.STATUS   , CASE WHEN WM.VALID_END_DT < CAST(GETDATE() AS DATE) THEN 'Y' ELSE 'N' END EXPIRED_YN FROM GEN_MESSAGES WM LEFT JOIN GEN_MESSAGE_MAPS WMM ON WM.MSG_CODE = WMM.MSG_CODE LEFT JOIN ADM_USERS AU ON AU.USER_CODE = WM.CREATE_BY LEFT JOIN GEN_MESSAGES_MAIN_CAT WMMC ON WMMC.MAIN_CAT_CODE = WM.MAIN_CAT_CODE LEFT JOIN GEN_MESSAGES_SUB_CAT WMSC ON WMSC.SUB_CAT_CODE = WM.SUB_CAT_CODE JOIN (SELECT MSG_ID,CREATE_DT,EVENT_ID,ROW_NUMBER() OVER(PARTITION BY MSG_ID ORDER BY CREATE_DT DESC)AS SORT_NUM FROM GEN_MESSAGES_LOG) WML ON WML.MSG_ID = WM.MSG_ID LEFT JOIN WKF_EVENTS WE ON WML.EVENT_ID = WE.EVENT_ID LEFT JOIN WKF_EVENT_HISTORY WEH ON WE.WKF_HISTORY_ID = WEH.WKF_HISTORY_ID LEFT JOIN WKF_ENGINE_STATUS WES ON WEH.WFG_ID = WES.WFG_ID AND WEH.STATUS = WES.ACTION_STATUS WHERE 1=1 AND WML.SORT_NUM = 1  AND WM.MSG_CODE = :msgCode  AND CAST(GETDATE() AS DATE) <= WM.VALID_END_DT ,Page request [number: 0, size 10, sort: VALID_BGN_DT: DESC],class com.bi.pbs.gen.web.model.GetMessageListResp,{msgCode=MSG02}"}]}