{"status": 200, "data": [{"docId": "DF202504180001", "docName": "吃反", "validDt": "2025/04/09", "expireDt": "2025/04/26"}, {"docId": "DF202502110011", "docName": "產品文件測試", "validDt": "2025/02/11", "expireDt": "2025/02/28"}, {"docId": "DF202502240001", "docName": "test", "validDt": "2025/02/24", "expireDt": "2025/02/28"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28"}, {"docId": "DF202505050001", "docName": "vinaTest", "validDt": "2025/05/05", "expireDt": "2025/05/05"}], "timestamp": "2025/07/17", "sqlTracer": [{"data": [{"docId": "DF202504180001", "docName": "吃反", "validDt": "2025/04/09", "expireDt": "2025/04/26"}, {"docId": "DF202502110011", "docName": "產品文件測試", "validDt": "2025/02/11", "expireDt": "2025/02/28"}, {"docId": "DF202502240001", "docName": "test", "validDt": "2025/02/24", "expireDt": "2025/02/28"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28"}, {"docId": "DF202505050001", "docName": "vinaTest", "validDt": "2025/05/05", "expireDt": "2025/05/05"}], "sqlInfo": " SELECT TOP 8 GD.DOC_ID,DOC_NAME,VALID_DT,EXPIRE_DT  FROM GEN_DOCUMENTS GD  INNER JOIN GEN_DOC_PRODUCTS GDP ON GD.DOC_ID = GDP.DOC_ID  ORDER BY GD.PRIORITY,GD.VALID_DT DESC ,class com.bi.pbs.gen.web.model.DocHomeListResp,{}"}]}