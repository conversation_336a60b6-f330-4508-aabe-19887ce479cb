{"status": 200, "data": {"content": [{"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28", "priorityName": "中", "showCusYnName": "Y", "proList": [{"proCode": "ETF_02800"}, {"proCode": "ETF_02825", "proName": "CSI Hong Kong 100 Index Fund"}, {"proCode": "ETF_03033", "proName": "CSOP Hang Seng Technology Index ETF"}, {"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}], "fileList": [{"fileName": "DOC2025021500002.pdf", "showName": "一般管理.pdf", "contentType": "application/pdf", "filePath": "/usr/local/tomcat/data/PBS/DOC/GEN/TEMP/DF202502150002", "docFileId": "DOC2025021500002"}]}, {"docId": "DF202502260006", "docName": "產品文件信託-基金測試", "validDt": "2025/02/26", "expireDt": "2025/04/25", "priorityName": "低", "showCusYnName": "Y", "proList": [{"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}, {"proCode": "FUND_0102", "proName": "富達新興市場基金　　　　　"}], "fileList": [{"fileName": "DOC2025022600011.docx", "showName": "get-underUserDeputies (1) (3).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260006", "docFileId": "DOC2025022600011"}, {"fileName": "DOC2025022600012.docx", "showName": "get-underUserDeputies (1) (2).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260006", "docFileId": "DOC2025022600012"}, {"fileName": "DOC2025022600013.docx", "showName": "get-underUserDeputies (1) (1).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260006", "docFileId": "DOC2025022600013"}]}, {"docId": "DF202503110001", "docName": "test", "validDt": "2025/03/11", "expireDt": "2025/10/04", "priorityName": "低", "showCusYnName": "N", "proList": [{"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}, {"proCode": "INS_01AEA4_54", "proName": "澳滿福澳幣利率變動型養老保險"}, {"proCode": "INS_01TEA1_54", "proName": "優沛利利變養老"}], "fileList": [{"fileName": "DOC2025031100001.pdf", "showName": "國家代碼表.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202503110001", "docFileId": "DOC2025031100001"}]}, {"docId": "DF202503130001", "docName": "test", "validDt": "2025/03/07", "expireDt": "2025/03/31", "priorityName": "低", "showCusYnName": "N", "proList": [{"proCode": "FUND_0112", "proName": "富達東協基金"}, {"proCode": "PFD_00005", "proName": "HSBC Holdings"}], "fileList": [{"fileName": "DOC2025031300001.pdf", "showName": "110年度行事曆.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202503130001", "docFileId": "DOC2025031300001"}]}, {"docId": "DF202504020001", "docName": "test33355", "validDt": "2025/01/03", "expireDt": "2025/04/04", "priorityName": "低", "showCusYnName": "Y", "proList": [{"proCode": "BOND_23AC", "proName": " 一銀１１２年度第２期１．５年期金融債券   "}, {"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}, {"proCode": "INS_01AEA4_54", "proName": "澳滿福澳幣利率變動型養老保險"}], "fileList": [{"fileName": "DOC2025040200001.docx", "showName": "SKB_工作導向系統-系統管理操作手冊v1.0.docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202504020001", "docFileId": "DOC2025040200001"}]}, {"docId": "DF202504180001", "docName": "吃反", "validDt": "2025/04/09", "expireDt": "2025/04/26", "priorityName": "高", "showCusYnName": "N", "proList": [{"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}], "fileList": []}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": true, "totalElements": 6, "totalPages": 1, "first": true, "size": 10, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 6, "empty": false}, "timestamp": "2025/07/22", "sqlTracer": [{"data": ["DF202502150002", "DF202502260006", "DF202503110001", "DF202503130001", "DF202504020001", "DF202504180001"], "sqlInfo": " SELECT DISTINCT DOC_ID  FROM GEN_DOC_PRODUCTS GDP  INNER JOIN PRODUCTS P on GDP.PRO_CODE = P.PRO_CODE  WHERE P.PFCAT_CODE = :proTypeCode ,class java.lang.String,{proTypeCode=FUND}"}, {"data": {"content": [{"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28", "priorityName": "中", "showCusYnName": "Y", "proList": [{"proCode": "ETF_02800"}, {"proCode": "ETF_02825", "proName": "CSI Hong Kong 100 Index Fund"}, {"proCode": "ETF_03033", "proName": "CSOP Hang Seng Technology Index ETF"}, {"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}], "fileList": [{"fileName": "DOC2025021500002.pdf", "showName": "一般管理.pdf", "contentType": "application/pdf", "filePath": "/usr/local/tomcat/data/PBS/DOC/GEN/TEMP/DF202502150002", "docFileId": "DOC2025021500002"}]}, {"docId": "DF202502260006", "docName": "產品文件信託-基金測試", "validDt": "2025/02/26", "expireDt": "2025/04/25", "priorityName": "低", "showCusYnName": "Y", "proList": [{"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}, {"proCode": "FUND_0102", "proName": "富達新興市場基金　　　　　"}], "fileList": [{"fileName": "DOC2025022600011.docx", "showName": "get-underUserDeputies (1) (3).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260006", "docFileId": "DOC2025022600011"}, {"fileName": "DOC2025022600012.docx", "showName": "get-underUserDeputies (1) (2).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260006", "docFileId": "DOC2025022600012"}, {"fileName": "DOC2025022600013.docx", "showName": "get-underUserDeputies (1) (1).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260006", "docFileId": "DOC2025022600013"}]}, {"docId": "DF202503110001", "docName": "test", "validDt": "2025/03/11", "expireDt": "2025/10/04", "priorityName": "低", "showCusYnName": "N", "proList": [{"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}, {"proCode": "INS_01AEA4_54", "proName": "澳滿福澳幣利率變動型養老保險"}, {"proCode": "INS_01TEA1_54", "proName": "優沛利利變養老"}], "fileList": [{"fileName": "DOC2025031100001.pdf", "showName": "國家代碼表.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202503110001", "docFileId": "DOC2025031100001"}]}, {"docId": "DF202503130001", "docName": "test", "validDt": "2025/03/07", "expireDt": "2025/03/31", "priorityName": "低", "showCusYnName": "N", "proList": [{"proCode": "FUND_0112", "proName": "富達東協基金"}, {"proCode": "PFD_00005", "proName": "HSBC Holdings"}], "fileList": [{"fileName": "DOC2025031300001.pdf", "showName": "110年度行事曆.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202503130001", "docFileId": "DOC2025031300001"}]}, {"docId": "DF202504020001", "docName": "test33355", "validDt": "2025/01/03", "expireDt": "2025/04/04", "priorityName": "低", "showCusYnName": "Y", "proList": [{"proCode": "BOND_23AC", "proName": " 一銀１１２年度第２期１．５年期金融債券   "}, {"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}, {"proCode": "INS_01AEA4_54", "proName": "澳滿福澳幣利率變動型養老保險"}], "fileList": [{"fileName": "DOC2025040200001.docx", "showName": "SKB_工作導向系統-系統管理操作手冊v1.0.docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202504020001", "docFileId": "DOC2025040200001"}]}, {"docId": "DF202504180001", "docName": "吃反", "validDt": "2025/04/09", "expireDt": "2025/04/26", "priorityName": "高", "showCusYnName": "N", "proList": [{"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}], "fileList": []}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": true, "totalElements": 6, "totalPages": 1, "first": true, "size": 10, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 6, "empty": false}, "sqlInfo": "SELECT GD.DOC_ID ,GD.DOC_NAME, GD.VALID_DT, GD.EXPIRE_DT, GD.SHOWCUS_YN, GD.PRIORITY ,ACD_P.CODE_NAME AS PRIORITY_NAME ,ACD_C.CODE_NAME AS SHOW_CUS_NAME FROM GEN_DOCUMENTS GD LEFT JOIN ADM_CODE_DETAIL ACD_P ON ACD_P.CODE_TYPE ='DOC_PRIORITY' AND ACD_P.CODE_VALUE = GD.PRIORITY LEFT JOIN ADM_CODE_DETAIL ACD_C ON ACD_C.CODE_TYPE ='DOC_SHOWCUS_YN' AND ACD_C.CODE_VALUE = GD.SHOWCUS_YN WHERE GD.DOC_ID IN(:DOC_ID) ,Page request [number: 0, size 10, sort: DOC_ID: ASC],class com.bi.pbs.gen.web.model.GetDocMktsResp,{DOC_ID=[DF202502150002, DF202502260006, DF202503110001, DF202503130001, DF202504020001, DF202504180001]}"}, {"data": [{"proCode": "ETF_02800"}, {"proCode": "ETF_02825", "proName": "CSI Hong Kong 100 Index Fund"}, {"proCode": "ETF_03033", "proName": "CSOP Hang Seng Technology Index ETF"}, {"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202502150002}"}, {"data": [{"fileName": "DOC2025021500002.pdf", "showName": "一般管理.pdf", "contentType": "application/pdf", "filePath": "/usr/local/tomcat/data/PBS/DOC/GEN/TEMP/DF202502150002", "docFileId": "DOC2025021500002"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502150002}"}, {"data": [{"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}, {"proCode": "FUND_0102", "proName": "富達新興市場基金　　　　　"}], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202502260006}"}, {"data": [{"fileName": "DOC2025022600011.docx", "showName": "get-underUserDeputies (1) (3).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260006", "docFileId": "DOC2025022600011"}, {"fileName": "DOC2025022600012.docx", "showName": "get-underUserDeputies (1) (2).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260006", "docFileId": "DOC2025022600012"}, {"fileName": "DOC2025022600013.docx", "showName": "get-underUserDeputies (1) (1).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260006", "docFileId": "DOC2025022600013"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502260006}"}, {"data": [{"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}, {"proCode": "INS_01AEA4_54", "proName": "澳滿福澳幣利率變動型養老保險"}, {"proCode": "INS_01TEA1_54", "proName": "優沛利利變養老"}], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202503110001}"}, {"data": [{"fileName": "DOC2025031100001.pdf", "showName": "國家代碼表.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202503110001", "docFileId": "DOC2025031100001"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202503110001}"}, {"data": [{"proCode": "FUND_0112", "proName": "富達東協基金"}, {"proCode": "PFD_00005", "proName": "HSBC Holdings"}], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202503130001}"}, {"data": [{"fileName": "DOC2025031300001.pdf", "showName": "110年度行事曆.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202503130001", "docFileId": "DOC2025031300001"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202503130001}"}, {"data": [{"proCode": "BOND_23AC", "proName": " 一銀１１２年度第２期１．５年期金融債券   "}, {"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}, {"proCode": "INS_01AEA4_54", "proName": "澳滿福澳幣利率變動型養老保險"}], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202504020001}"}, {"data": [{"fileName": "DOC2025040200001.docx", "showName": "SKB_工作導向系統-系統管理操作手冊v1.0.docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202504020001", "docFileId": "DOC2025040200001"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202504020001}"}, {"data": [{"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202504180001}"}, {"data": [], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202504180001}"}]}