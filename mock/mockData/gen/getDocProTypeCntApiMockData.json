{"status": 200, "data": [{"proTypeCode": "FUND", "proTypeName": "信託-基金", "cnt": "6"}, {"proTypeCode": "FB", "proTypeName": "信託-海外債", "cnt": "3"}, {"proTypeCode": "ETF", "proTypeName": "信託-ETF", "cnt": "3"}, {"proTypeCode": "SP", "proTypeName": "信託-境外結構型商品", "cnt": "0"}, {"proTypeCode": "PFD", "proTypeName": "信託-海外股票", "cnt": "1"}, {"proTypeCode": "DCD", "proTypeName": "銀行結構型商品", "cnt": "0"}, {"proTypeCode": "INS", "proTypeName": "人身保險", "cnt": "2"}], "timestamp": "2025/07/22", "sqlTracer": [{"data": [{"proTypeCode": "FUND", "proTypeName": "信託-基金", "cnt": "6"}, {"proTypeCode": "FB", "proTypeName": "信託-海外債", "cnt": "3"}, {"proTypeCode": "ETF", "proTypeName": "信託-ETF", "cnt": "3"}, {"proTypeCode": "SP", "proTypeName": "信託-境外結構型商品", "cnt": "0"}, {"proTypeCode": "PFD", "proTypeName": "信託-海外股票", "cnt": "1"}, {"proTypeCode": "DCD", "proTypeName": "銀行結構型商品", "cnt": "0"}, {"proTypeCode": "INS", "proTypeName": "人身保險", "cnt": "2"}], "sqlInfo": " SELECT PP.PFCAT_CODE,PP.PFCAT_NAME ,COALESCE(D.CNT,0)AS CNT  FROM PRO_PFCATS PP  LEFT JOIN  (SELECT A.PFCAT_CODE ,count(*) AS cnt  FROM ( SELECT DISTINCT P.PFCAT_CODE, GDP.DOC_ID  FROM GEN_DOC_PRODUCTS GDP  INNER JOIN PRODUCTS P on GDP.PRO_CODE = P.PRO_CODE ) A  GROUP by A.PFCAT_CODE) D  ON D.PFCAT_CODE = PP.PFCAT_CODE  WHERE PP.TRAN_YN ='Y'  ORDER BY PP.SHOW_ORDER ,class com.bi.pbs.pro.web.model.DocProTypeCntResp,[Ljava.lang.Object;@248dd573"}]}