{"status": 200, "data": [{"typeCode": "DMTME01", "typeName": "市場研討會", "cnt": "2"}, {"typeCode": "DMTME02", "typeName": "新產品研討會", "cnt": "2"}, {"typeCode": "DMTME03", "typeName": "高資產客戶專屬"}, {"typeCode": "DMTME04", "typeName": "行銷活動", "cnt": "1"}, {"typeCode": "DMTME05", "typeName": "專案活動"}], "timestamp": "2025/07/22", "sqlTracer": [{"data": [{"typeCode": "DMTME01", "typeName": "市場研討會", "cnt": "2"}, {"typeCode": "DMTME02", "typeName": "新產品研討會", "cnt": "2"}, {"typeCode": "DMTME03", "typeName": "高資產客戶專屬"}, {"typeCode": "DMTME04", "typeName": "行銷活動", "cnt": "1"}, {"typeCode": "DMTME05", "typeName": "專案活動"}], "sqlInfo": "SELECT GDT.TYPE_CODE,GDT.TYPE_NAME,D.CNT FROM GEN_DOC_TYPE GDT LEFT JOIN ( SELECT GDM.MAIN_TYPE_CODE,count(*) AS CNT FROM GEN_DOC_MKT GDM GROUP BY GDM.MAIN_TYPE_CODE )D ON GDT.TYPE_CODE = D.MAIN_TYPE_CODE WHERE GDT.MAIN_TYPE_CODE IS NULL AND GDT.CAT_CODE ='MKTEVENT' ,class com.bi.pbs.gen.web.model.GetDocMktCntResp,{}"}]}