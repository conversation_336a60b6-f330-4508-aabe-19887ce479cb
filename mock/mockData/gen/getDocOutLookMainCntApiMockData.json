{"status": 200, "data": [{"typeCode": "DMTMO01", "typeName": "不分", "cnt": "2"}, {"typeCode": "DMTMO03", "typeName": "信託-海外ETF", "cnt": "2"}, {"typeCode": "DMTMO04", "typeName": "信託-海外債"}, {"typeCode": "DMTMO05", "typeName": "信託-基金", "cnt": "1"}, {"typeCode": "DMTMO07", "typeName": "人身保險"}, {"typeCode": "DMTMO08", "typeName": "信託-海外股票"}], "timestamp": "2025/07/18", "sqlTracer": [{"data": [{"typeCode": "DMTMO01", "typeName": "不分", "cnt": "2"}, {"typeCode": "DMTMO03", "typeName": "信託-海外ETF", "cnt": "2"}, {"typeCode": "DMTMO04", "typeName": "信託-海外債"}, {"typeCode": "DMTMO05", "typeName": "信託-基金", "cnt": "1"}, {"typeCode": "DMTMO07", "typeName": "人身保險"}, {"typeCode": "DMTMO08", "typeName": "信託-海外股票"}], "sqlInfo": "SELECT GDT.TYPE_CODE, GDT.TYPE_NAME,M.CNT FROM GEN_DOC_TYPE GDT LEFT JOIN ( SELECT GMO.MAIN_CAT_CODE,count(*) AS CNT FROM GEN_MKT_OUTLOOK GMO GROUP BY GMO.MAIN_CAT_CODE )M ON GDT.TYPE_CODE =M.MAIN_CAT_CODE WHERE GDT.MAIN_TYPE_CODE IS NULL AND GDT.CAT_CODE ='MKTOUTLOOK' ,class com.bi.pbs.gen.web.model.GetDocMktCntResp,{}"}]}