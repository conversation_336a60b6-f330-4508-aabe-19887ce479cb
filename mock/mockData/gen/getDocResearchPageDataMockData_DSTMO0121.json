{"status": 200, "data": {"content": [{"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28", "priorityName": "中", "showCusYnName": "Y", "proList": [{"proCode": "ETF_02800"}, {"proCode": "ETF_02825", "proName": "CSI Hong Kong 100 Index Fund"}, {"proCode": "ETF_03033", "proName": "CSOP Hang Seng Technology Index ETF"}, {"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}], "fileList": [{"fileName": "DOC2025021500002.pdf", "showName": "一般管理.pdf", "contentType": "application/pdf", "filePath": "/usr/local/tomcat/data/PBS/DOC/GEN/TEMP/DF202502150002", "docFileId": "DOC2025021500002"}]}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalPages": 1, "last": true, "totalElements": 1, "numberOfElements": 1, "first": true, "size": 10, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": false}, "timestamp": "2025/07/18", "sqlTracer": [{"data": {"content": [{"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28", "priorityName": "中", "showCusYnName": "Y", "proList": [{"proCode": "ETF_02800"}, {"proCode": "ETF_02825", "proName": "CSI Hong Kong 100 Index Fund"}, {"proCode": "ETF_03033", "proName": "CSOP Hang Seng Technology Index ETF"}, {"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}], "fileList": [{"fileName": "DOC2025021500002.pdf", "showName": "一般管理.pdf", "contentType": "application/pdf", "filePath": "/usr/local/tomcat/data/PBS/DOC/GEN/TEMP/DF202502150002", "docFileId": "DOC2025021500002"}]}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalPages": 1, "last": true, "totalElements": 1, "numberOfElements": 1, "first": true, "size": 10, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": false}, "sqlInfo": "SELECT GD.DOC_ID ,GD.DOC_NAME, GD.VALID_DT, GD.EXPIRE_DT, GD.SHOWCUS_YN, GD.PRIORITY ,ACD_P.CODE_NAME AS PRIORITY_NAME ,ACD_C.CODE_NAME AS SHOW_CUS_NAME FROM GEN_DOCUMENTS GD INNER JOIN GEN_MKT_OUTLOOK GMO ON GMO.DOC_ID = GD.DOC_ID LEFT JOIN ADM_CODE_DETAIL ACD_P ON ACD_P.CODE_TYPE ='DOC_PRIORITY' AND ACD_P.CODE_VALUE = GD.PRIORITY LEFT JOIN ADM_CODE_DETAIL ACD_C ON ACD_C.CODE_TYPE ='DOC_SHOWCUS_YN' AND ACD_C.CODE_VALUE = GD.SHOWCUS_YN WHERE GMO.MAIN_CAT_CODE = :mainTypeCode AND GMO.SUB_CAT_CODE = :subTypeCode ,Page request [number: 0, size 10, sort: DOC_ID: ASC],class com.bi.pbs.gen.web.model.GetDocMktsResp,{mainTypeCode=DMTMO05, subTypeCode=DSTMO0121}"}, {"data": [{"proCode": "ETF_02800"}, {"proCode": "ETF_02825", "proName": "CSI Hong Kong 100 Index Fund"}, {"proCode": "ETF_03033", "proName": "CSOP Hang Seng Technology Index ETF"}, {"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202502150002}"}, {"data": [{"fileName": "DOC2025021500002.pdf", "showName": "一般管理.pdf", "contentType": "application/pdf", "filePath": "/usr/local/tomcat/data/PBS/DOC/GEN/TEMP/DF202502150002", "docFileId": "DOC2025021500002"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502150002}"}]}