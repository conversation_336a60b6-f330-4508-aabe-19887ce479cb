{"status": 200, "data": {"content": [{"docId": "DF202502110007", "docName": "新聞事件測試", "docDesc": "新聞事件測試", "validDt": "2025/02/01", "expireDt": "2025/02/28", "priority": "3", "priorityName": "低", "showCusName": "可提供給客戶", "showCusYn": "Y", "newsNoticeInfo": [{"condName": "給持有基金相關商品的客戶"}], "fileList": [{"fileName": "DOC2025021100014.pdf", "showName": "G-026-請假規則.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110007", "docFileId": "DOC2025021100014"}, {"fileName": "DOC2025021100015.pdf", "showName": "請假流程.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110007", "docFileId": "DOC2025021100015"}, {"fileName": "DOC2025021100016.doc", "showName": "SNB-SDS-金融訊息-金融訊息維護.doc", "contentType": "application/msword", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110007", "docFileId": "DOC2025021100016"}], "createBy": "112790", "createDt": "2025/02/11"}, {"docId": "DF202502110008", "docName": "新聞事件-可否提供給客戶欄位顯示", "docDesc": "新聞事件-可否提供給客戶欄位顯示", "validDt": "2025/02/01", "expireDt": "2025/02/28", "priority": "2", "priorityName": "中", "showCusName": "可提供給客戶", "showCusYn": "Y", "newsNoticeInfo": [{"condName": "給持有基金相關商品的客戶"}], "fileList": [{"fileName": "DOC2025021100017.doc", "showName": "SNB-SDS-金融訊息-金融訊息維護.doc", "contentType": "application/msword", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110008", "docFileId": "DOC2025021100017"}, {"fileName": "DOC2025021100018.pdf", "showName": "請假流程.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110008", "docFileId": "DOC2025021100018"}, {"fileName": "DOC2025021100019.pdf", "showName": "G-026-請假規則.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110008", "docFileId": "DOC2025021100019"}], "createBy": "112790", "createDt": "2025/02/11"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28", "priority": "2", "priorityName": "中", "showCusName": "可提供給客戶", "showCusYn": "Y", "newsNoticeInfo": [{"condName": "給所有客戶"}], "fileList": [{"fileName": "DOC2025021500002.pdf", "showName": "一般管理.pdf", "contentType": "application/pdf", "filePath": "/usr/local/tomcat/data/PBS/DOC/GEN/TEMP/DF202502150002", "docFileId": "DOC2025021500002"}], "createBy": "112790", "createDt": "2025/02/15", "modifyBy": "112790", "modifyDt": "2025/02/24"}, {"docId": "DF202503030001", "docName": "新聞事件測試", "docDesc": "新聞事件測試", "validDt": "2025/03/03", "expireDt": "2025/03/31", "priority": "2", "priorityName": "中", "showCusName": "可提供給客戶", "showCusYn": "Y", "newsNoticeInfo": [{"condName": "給持有基金相關商品的客戶"}], "fileList": [{"fileName": "DOC2025030300001.pdf", "showName": "公司座位表_20250218.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202503030001", "docFileId": "DOC2025030300001"}], "createBy": "112790", "createDt": "2025/03/03"}, {"docId": "DF202503190002", "docName": "ttt", "docDesc": "ttt", "validDt": "2025/03/19", "expireDt": "2025/03/20", "priority": "2", "priorityName": "中", "showCusName": "僅供內部使用", "showCusYn": "N", "newsNoticeInfo": [{"condName": "給持有基金相關商品的客戶"}], "fileList": [{"fileName": "DOC2025031900002.xlsx", "showName": "complaintUpload.xlsx", "contentType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filePath": "GEN/TEMP/DF202503190002", "docFileId": "DOC2025031900002"}], "createBy": "112790", "createDt": "2025/03/19"}, {"docId": "DF202503190003", "docName": "TEST0319", "docDesc": "TEST", "validDt": "2025/03/19", "expireDt": "2025/03/28", "priority": "1", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "newsNoticeInfo": [{"condName": "給持有基金相關商品的客戶"}], "createBy": "112790", "createDt": "2025/03/19"}, {"docId": "DF202504180001", "docName": "吃反", "docDesc": "????", "validDt": "2025/04/09", "expireDt": "2025/04/26", "priority": "1", "priorityName": "高", "showCusName": "僅供內部使用", "showCusYn": "N", "newsNoticeInfo": [{"condName": "給所有客戶"}], "createBy": "112790", "createDt": "2025/04/18"}, {"docId": "DF202504180005", "docName": "80472test", "docDesc": "aaa?？？", "validDt": "2025/04/18", "expireDt": "2025/04/18", "priority": "1", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "newsNoticeInfo": [{"condName": "給持有基金相關商品的客戶"}], "fileList": [{"fileName": "DOC2025041800001.docx", "showName": "get-underUserDeputies.docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202504180005", "docFileId": "DOC2025041800001"}, {"fileName": "DOC2025041800002.xlsx", "showName": "complaintUpload (1).xlsx", "contentType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filePath": "GEN/TEMP/DF202504180005", "docFileId": "DOC2025041800002"}], "createBy": "112790", "createDt": "2025/04/18", "modifyBy": "112790", "modifyDt": "2025/04/18"}], "pageable": {"pageNumber": 0, "pageSize": 20, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalPages": 1, "last": true, "totalElements": 8, "numberOfElements": 8, "first": true, "size": 20, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": false}, "timestamp": "2025/07/18", "sqlTracer": [{"data": {"content": [{"docId": "DF202502110007", "docName": "新聞事件測試", "validDt": "2025/02/01", "expireDt": "2025/02/28", "priority": "3", "priorityName": "低", "showCusName": "可提供給客戶", "showCusYn": "Y", "docDesc": "新聞事件測試", "createBy": "112790", "createDt": "2025/02/11"}, {"docId": "DF202502110008", "docName": "新聞事件-可否提供給客戶欄位顯示", "validDt": "2025/02/01", "expireDt": "2025/02/28", "priority": "2", "priorityName": "中", "showCusName": "可提供給客戶", "showCusYn": "Y", "docDesc": "新聞事件-可否提供給客戶欄位顯示", "createBy": "112790", "createDt": "2025/02/11"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28", "priority": "2", "priorityName": "中", "showCusName": "可提供給客戶", "showCusYn": "Y", "createBy": "112790", "createDt": "2025/02/15", "modifyBy": "112790", "modifyDt": "2025/02/24"}, {"docId": "DF202503030001", "docName": "新聞事件測試", "validDt": "2025/03/03", "expireDt": "2025/03/31", "priority": "2", "priorityName": "中", "showCusName": "可提供給客戶", "showCusYn": "Y", "docDesc": "新聞事件測試", "createBy": "112790", "createDt": "2025/03/03"}, {"docId": "DF202503190002", "docName": "ttt", "validDt": "2025/03/19", "expireDt": "2025/03/20", "priority": "2", "priorityName": "中", "showCusName": "僅供內部使用", "showCusYn": "N", "docDesc": "ttt", "createBy": "112790", "createDt": "2025/03/19"}, {"docId": "DF202503190003", "docName": "TEST0319", "validDt": "2025/03/19", "expireDt": "2025/03/28", "priority": "1", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "docDesc": "TEST", "createBy": "112790", "createDt": "2025/03/19"}, {"docId": "DF202504180001", "docName": "吃反", "validDt": "2025/04/09", "expireDt": "2025/04/26", "priority": "1", "priorityName": "高", "showCusName": "僅供內部使用", "showCusYn": "N", "docDesc": "????", "createBy": "112790", "createDt": "2025/04/18"}, {"docId": "DF202504180005", "docName": "80472test", "validDt": "2025/04/18", "expireDt": "2025/04/18", "priority": "1", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "docDesc": "aaa?？？", "createBy": "112790", "createDt": "2025/04/18", "modifyBy": "112790", "modifyDt": "2025/04/18"}], "pageable": {"pageNumber": 0, "pageSize": 20, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalPages": 1, "last": true, "totalElements": 8, "numberOfElements": 8, "first": true, "size": 20, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": false}, "sqlInfo": " SELECT  \t GD.DOC_ID  \t,GD.DOC_NAME  \t,GD.DOC_DESC  \t,GD.VALID_DT  \t,GD.EXPIRE_DT  \t,GD.SHOWCUS_YN  \t,GD.PRIORITY  \t,GD.CREATE_BY  \t,GD.CREATE_DT  \t,GD.MODIFY_BY  \t,GD.MODIFY_DT  \t,ACD_P.CODE_NAME AS PRIORITY_NAME  \t,ACD_C.CODE_NAME AS SHOW_CUS_NAME  FROM  GEN_DOCUMENTS  GD  INNER JOIN GEN_NEWS GN ON GD.DOC_ID = GN.DOC_ID  LEFT JOIN  ADM_CODE_DETAIL ACD_P  ON ACD_P.CODE_TYPE ='DOC_PRIORITY'  AND ACD_P.CODE_VALUE = GD.PRIORITY  LEFT JOIN  ADM_CODE_DETAIL ACD_C  ON ACD_C.CODE_TYPE ='DOC_SHOWCUS_YN'  AND ACD_C.CODE_VALUE = GD.SHOWCUS_YN ,Page request [number: 0, size 20, sort: DOC_ID: ASC],class com.bi.pbs.gen.web.model.SelectDocumentResp,[Ljava.lang.Object;@3dda2c9a"}, {"data": [{"createDt": "2025-07-18T15:20:21.31085", "createBy": "SYS", "docId": "DF202502110007", "condName": "給持有基金相關商品的客戶"}, {"createDt": "2025-07-18T15:20:21.311439", "createBy": "SYS", "docId": "DF202502110008", "condName": "給持有基金相關商品的客戶"}, {"createDt": "2025-07-18T15:20:21.311532", "createBy": "SYS", "docId": "DF202502150002", "condName": "給所有客戶"}, {"createDt": "2025-07-18T15:20:21.311613", "createBy": "SYS", "docId": "DF202503030001", "condName": "給持有基金相關商品的客戶"}, {"createDt": "2025-07-18T15:20:21.311678", "createBy": "SYS", "docId": "DF202503190002", "condName": "給持有基金相關商品的客戶"}, {"createDt": "2025-07-18T15:20:21.311751", "createBy": "SYS", "docId": "DF202503190003", "condName": "給持有基金相關商品的客戶"}, {"createDt": "2025-07-18T15:20:21.311823", "createBy": "SYS", "docId": "DF202504180001", "condName": "給所有客戶"}, {"createDt": "2025-07-18T15:20:21.311897", "createBy": "SYS", "docId": "DF202504180005", "condName": "給持有基金相關商品的客戶"}], "sqlInfo": " SELECT          DISTINCT           GN.DOC_ID          ,CASE WHEN GNC.COND_TYPE IS NULL THEN '給所有客戶'  WHEN GNC.COND_TYPE = 'SEC' THEN '給持有相關商品的客戶(投資標的)'  WHEN GNC.COND_TYPE = 'GEO' THEN '給持有相關商品的客戶(投資地區)'  WHEN GNC.COND_TYPE = 'FND' THEN '給持有基金相關商品的客戶'  END COND_NAME  FROM GEN_NEWS GN  LEFT JOIN GEN_NEWS_CONDITION GNC ON GN.DOC_ID = GNC.DOC_ID ,class com.bi.pbs.gen.model.GenDocNewsResp,{docId=null}"}, {"data": [{"fileName": "DOC2025021100014.pdf", "showName": "G-026-請假規則.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110007", "docFileId": "DOC2025021100014"}, {"fileName": "DOC2025021100015.pdf", "showName": "請假流程.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110007", "docFileId": "DOC2025021100015"}, {"fileName": "DOC2025021100016.doc", "showName": "SNB-SDS-金融訊息-金融訊息維護.doc", "contentType": "application/msword", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110007", "docFileId": "DOC2025021100016"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502110007}"}, {"data": [{"fileName": "DOC2025021100017.doc", "showName": "SNB-SDS-金融訊息-金融訊息維護.doc", "contentType": "application/msword", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110008", "docFileId": "DOC2025021100017"}, {"fileName": "DOC2025021100018.pdf", "showName": "請假流程.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110008", "docFileId": "DOC2025021100018"}, {"fileName": "DOC2025021100019.pdf", "showName": "G-026-請假規則.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110008", "docFileId": "DOC2025021100019"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502110008}"}, {"data": [{"fileName": "DOC2025021500002.pdf", "showName": "一般管理.pdf", "contentType": "application/pdf", "filePath": "/usr/local/tomcat/data/PBS/DOC/GEN/TEMP/DF202502150002", "docFileId": "DOC2025021500002"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502150002}"}, {"data": [{"fileName": "DOC2025030300001.pdf", "showName": "公司座位表_20250218.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202503030001", "docFileId": "DOC2025030300001"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202503030001}"}, {"data": [{"fileName": "DOC2025031900002.xlsx", "showName": "complaintUpload.xlsx", "contentType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filePath": "GEN/TEMP/DF202503190002", "docFileId": "DOC2025031900002"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202503190002}"}, {"data": [], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202503190003}"}, {"data": [], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202504180001}"}, {"data": [{"fileName": "DOC2025041800001.docx", "showName": "get-underUserDeputies.docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202504180005", "docFileId": "DOC2025041800001"}, {"fileName": "DOC2025041800002.xlsx", "showName": "complaintUpload (1).xlsx", "contentType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filePath": "GEN/TEMP/DF202504180005", "docFileId": "DOC2025041800002"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202504180005}"}]}