{"status": 200, "data": {"content": [{"docId": "DF202502260001", "docName": "測試研究報告", "validDt": "2025/02/26", "expireDt": "2025/02/28", "priorityName": "中", "showCusYnName": "Y", "proList": [], "fileList": [{"fileName": "DOC2025022600001.pdf", "showName": "09_高中國文(4)課本_第八課 典論論文.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202502260001", "docFileId": "DOC2025022600001"}]}, {"docId": "DF202502260003", "docName": "研究報告測試", "validDt": "2025/02/26", "expireDt": "2025/02/28", "priorityName": "低", "showCusYnName": "Y", "proList": [], "fileList": [{"fileName": "DOC2025022600003.docx", "showName": "get-underUserDeputies (1).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260003", "docFileId": "DOC2025022600003"}, {"fileName": "DOC2025022600004.docx", "showName": "get-under<PERSON>ser<PERSON><PERSON><PERSON><PERSON> (2).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260003", "docFileId": "DOC2025022600004"}, {"fileName": "DOC2025022600005.doc", "showName": "SNB-SDS-金融訊息-金融訊息維護 (1).doc", "contentType": "application/msword", "filePath": "GEN/TEMP/DF202502260003", "docFileId": "DOC2025022600005"}]}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalPages": 1, "last": true, "totalElements": 2, "numberOfElements": 2, "first": true, "size": 10, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": false}, "timestamp": "2025/07/18", "sqlTracer": [{"data": {"content": [{"docId": "DF202502260001", "docName": "測試研究報告", "validDt": "2025/02/26", "expireDt": "2025/02/28", "priorityName": "中", "showCusYnName": "Y", "proList": [], "fileList": [{"fileName": "DOC2025022600001.pdf", "showName": "09_高中國文(4)課本_第八課 典論論文.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202502260001", "docFileId": "DOC2025022600001"}]}, {"docId": "DF202502260003", "docName": "研究報告測試", "validDt": "2025/02/26", "expireDt": "2025/02/28", "priorityName": "低", "showCusYnName": "Y", "proList": [], "fileList": [{"fileName": "DOC2025022600003.docx", "showName": "get-underUserDeputies (1).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260003", "docFileId": "DOC2025022600003"}, {"fileName": "DOC2025022600004.docx", "showName": "get-under<PERSON>ser<PERSON><PERSON><PERSON><PERSON> (2).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260003", "docFileId": "DOC2025022600004"}, {"fileName": "DOC2025022600005.doc", "showName": "SNB-SDS-金融訊息-金融訊息維護 (1).doc", "contentType": "application/msword", "filePath": "GEN/TEMP/DF202502260003", "docFileId": "DOC2025022600005"}]}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalPages": 1, "last": true, "totalElements": 2, "numberOfElements": 2, "first": true, "size": 10, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": false}, "sqlInfo": "SELECT GD.DOC_ID ,GD.DOC_NAME, GD.VALID_DT, GD.EXPIRE_DT, GD.SHOWCUS_YN, GD.PRIORITY ,ACD_P.CODE_NAME AS PRIORITY_NAME ,ACD_C.CODE_NAME AS SHOW_CUS_NAME FROM GEN_DOCUMENTS GD INNER JOIN GEN_MKT_OUTLOOK GMO ON GMO.DOC_ID = GD.DOC_ID LEFT JOIN ADM_CODE_DETAIL ACD_P ON ACD_P.CODE_TYPE ='DOC_PRIORITY' AND ACD_P.CODE_VALUE = GD.PRIORITY LEFT JOIN ADM_CODE_DETAIL ACD_C ON ACD_C.CODE_TYPE ='DOC_SHOWCUS_YN' AND ACD_C.CODE_VALUE = GD.SHOWCUS_YN WHERE GMO.MAIN_CAT_CODE = :mainTypeCode AND GMO.SUB_CAT_CODE = :subTypeCode ,Page request [number: 0, size 10, sort: DOC_ID: ASC],class com.bi.pbs.gen.web.model.GetDocMktsResp,{mainTypeCode=DMTMO01, subTypeCode=DSTMO0101}"}, {"data": [], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202502260001}"}, {"data": [{"fileName": "DOC2025022600001.pdf", "showName": "09_高中國文(4)課本_第八課 典論論文.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202502260001", "docFileId": "DOC2025022600001"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502260001}"}, {"data": [], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202502260003}"}, {"data": [{"fileName": "DOC2025022600003.docx", "showName": "get-underUserDeputies (1).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260003", "docFileId": "DOC2025022600003"}, {"fileName": "DOC2025022600004.docx", "showName": "get-under<PERSON>ser<PERSON><PERSON><PERSON><PERSON> (2).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260003", "docFileId": "DOC2025022600004"}, {"fileName": "DOC2025022600005.doc", "showName": "SNB-SDS-金融訊息-金融訊息維護 (1).doc", "contentType": "application/msword", "filePath": "GEN/TEMP/DF202502260003", "docFileId": "DOC2025022600005"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502260003}"}]}