{"status": 200, "data": {"content": [], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalPages": 0, "last": true, "totalElements": 0, "numberOfElements": 0, "first": true, "size": 10, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": true}, "timestamp": "2025/07/18", "sqlTracer": [{"data": {"content": [], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"sorted": true, "unsorted": false, "empty": false}, "offset": 0, "unpaged": false, "paged": true}, "totalPages": 0, "last": true, "totalElements": 0, "numberOfElements": 0, "first": true, "size": 10, "number": 0, "sort": {"sorted": true, "unsorted": false, "empty": false}, "empty": true}, "sqlInfo": "SELECT GD.DOC_ID ,GD.DOC_NAME, GD.VALID_DT, GD.EXPIRE_DT, GD.SHOWCUS_YN, GD.PRIORITY ,ACD_P.CODE_NAME AS PRIORITY_NAME ,ACD_C.CODE_NAME AS SHOW_CUS_NAME FROM GEN_DOCUMENTS GD INNER JOIN GEN_MKT_OUTLOOK GMO ON GMO.DOC_ID = GD.DOC_ID LEFT JOIN ADM_CODE_DETAIL ACD_P ON ACD_P.CODE_TYPE ='DOC_PRIORITY' AND ACD_P.CODE_VALUE = GD.PRIORITY LEFT JOIN ADM_CODE_DETAIL ACD_C ON ACD_C.CODE_TYPE ='DOC_SHOWCUS_YN' AND ACD_C.CODE_VALUE = GD.SHOWCUS_YN WHERE GMO.MAIN_CAT_CODE = :mainTypeCode AND GMO.SUB_CAT_CODE = :subTypeCode ,Page request [number: 0, size 10, sort: DOC_ID: ASC],class com.bi.pbs.gen.web.model.GetDocMktsResp,{mainTypeCode=DMTMO03, subTypeCode=DSTMO0111}"}]}