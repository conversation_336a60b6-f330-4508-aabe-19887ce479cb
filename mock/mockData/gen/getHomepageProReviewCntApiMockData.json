{"status": 200, "data": [{"menuName": "產品資料審核", "progClassname": "/pro/productMgtReview", "submitCnt": 0, "verifyCnt": 18, "reviewYn": "Y"}, {"menuName": "新產品臨時上架審核", "progClassname": "/pro/productAddVerify", "submitCnt": 3, "verifyCnt": 32, "reviewYn": "Y"}, {"menuName": "精選推薦商品審核", "progClassname": "/pro/packagePrdVerify", "submitCnt": 0, "verifyCnt": 7, "reviewYn": "Y"}, {"menuName": "投資組合配置審核", "progClassname": "/pro/astLocatedProdX", "submitCnt": 0, "verifyCnt": 2, "reviewYn": "Y"}, {"menuName": "全產品配置審核", "progClassname": "/pro/allAstLocatedProdX", "submitCnt": 0, "verifyCnt": 8, "reviewYn": "Y"}, {"menuName": "產品集中度參數審核", "progClassname": "/pro/investProductVerify", "submitCnt": 0, "verifyCnt": 0, "reviewYn": "Y"}, {"menuName": "產品預購調查審核", "progClassname": "/pro/prodAnnounceReview", "submitCnt": 0, "verifyCnt": 13, "reviewYn": "Y"}], "timestamp": "2025/07/23", "sqlTracer": [{"data": [{"menuName": "產品資料審核", "progClassname": "/pro/productMgtReview", "submitCnt": 0, "verifyCnt": 18, "reviewYn": "Y"}, {"menuName": "新產品臨時上架審核", "progClassname": "/pro/productAddVerify", "submitCnt": 3, "verifyCnt": 32, "reviewYn": "Y"}, {"menuName": "精選推薦商品審核", "progClassname": "/pro/packagePrdVerify", "submitCnt": 0, "verifyCnt": 7, "reviewYn": "Y"}, {"menuName": "投資組合配置審核", "progClassname": "/pro/astLocatedProdX", "submitCnt": 0, "verifyCnt": 2, "reviewYn": "Y"}, {"menuName": "全產品配置審核", "progClassname": "/pro/allAstLocatedProdX", "submitCnt": 0, "verifyCnt": 8, "reviewYn": "Y"}, {"menuName": "產品集中度參數審核", "progClassname": "/pro/investProductVerify", "submitCnt": 0, "verifyCnt": 0, "reviewYn": "Y"}, {"menuName": "產品預購調查審核", "progClassname": "/pro/prodAnnounceReview", "submitCnt": 0, "verifyCnt": 13, "reviewYn": "Y"}], "sqlInfo": "WITH SQL9 AS ( SELECT AM.MENU_NAME, AP.PROG_CLASSNAME, COALESCE(PTP.CNT, 0) AS CNT, CASE WHEN EXISTS (SELECT 1 FROM ADM_ROLE_MENU_MAP WHERE ROLE_CODE = :roleCode  AND MENU_CODE=AM.MENU_CODE ) THEN 'Y' ELSE 'N' END REVIEW_YN FROM ADM_MENUS AM LEFT JOIN ADM_PROGS AP ON AM.PROG_CODE = AP.PROG_CODE LEFT JOIN (     SELECT 'M15-01' AS MENU_CODE, COUNT(1) AS CNT FROM WKF_EVENTS     WHERE WFG_ID='PRODUCTS' AND STATUS = 'P' AND CREATE_BY=:userCode UNION     SELECT 'M15-02' AS MENU_CODE, COUNT(1) AS CNT FROM WKF_EVENTS     WHERE WFG_ID='PRODUCTS_NEW' AND STATUS = 'P' AND CREATE_BY=:userCode UNION     SELECT 'M15-03' AS MENU_CODE, COUNT(1) AS CNT FROM WKF_EVENTS     WHERE WFG_ID='PRO_PRICE_UPLOAD' AND STATUS = 'P' AND CREATE_BY=:userCode UNION     SELECT 'M15-04' AS MENU_CODE, COUNT(1) AS CNT FROM WKF_EVENTS     WHERE WFG_ID='PRO_SELECTED' AND STATUS = 'P' AND CREATE_BY=:userCode UNION     SELECT 'M15-05' AS MENU_CODE, COUNT(1) AS CNT FROM PRO_PF_ASSETAL_LOG LOG     WHERE STATUS='P' AND CREATE_BY=:userCode UNION     SELECT 'M15-06' AS MENU_CODE, COUNT(1) AS CNT FROM PRO_ALL_ASSETAL_LOG     WHERE STATUS='P' AND CREATE_BY=:userCode UNION     SELECT 'M15-07' AS MENU_CODE, COUNT(1) AS CNT FROM PRO_CCTPARA_VERIFY     WHERE STATUS='P' AND CREATE_BY=:userCode UNION     SELECT 'M15-08' AS MENU_CODE, COUNT(1) AS CNT FROM PRO_PREODR_INVTG_LOG     WHERE STATUS='P' AND CREATE_BY=:userCode ) PTP ON PTP.MENU_CODE = AM.MENU_CODE WHERE AM.MENU_CODE IN ('M15-01', 'M15-02', 'M15-03', 'M15-04', 'M15-05', 'M15-06', 'M15-07', 'M15-08') ), SQL10 AS ( SELECT AM.MENU_NAME, AP.PROG_CLASSNAME, COALESCE(PTP.CNT, 0) AS CNT FROM ADM_MENUS AM LEFT JOIN ADM_PROGS AP ON AM.PROG_CODE = AP.PROG_CODE LEFT JOIN (     SELECT 'M15-01' AS MENU_CODE, COUNT(1) AS CNT FROM WKF_EVENTS     WHERE WFG_ID='PRODUCTS' AND STATUS = 'P' AND CREATE_BY <> :userCode UNION     SELECT 'M15-02' AS MENU_CODE, COUNT(1) AS CNT FROM WKF_EVENTS     WHERE WFG_ID='PRODUCTS_NEW' AND STATUS = 'P' AND CREATE_BY <> :userCode UNION     SELECT 'M15-03' AS MENU_CODE, COUNT(1) AS CNT FROM WKF_EVENTS     WHERE WFG_ID='PRO_PRICE_UPLOAD' AND STATUS = 'P' AND CREATE_BY <> :userCode UNION     SELECT 'M15-04' AS MENU_CODE, COUNT(1) AS CNT FROM WKF_EVENTS     WHERE WFG_ID='PRO_SELECTED' AND STATUS = 'P' AND CREATE_BY <> :userCode UNION     SELECT 'M15-05' AS MENU_CODE, COUNT(1) AS CNT FROM PRO_PF_ASSETAL_LOG LOG     WHERE STATUS='P' UNION     SELECT 'M15-06' AS MENU_CODE, COUNT(1) AS CNT FROM PRO_ALL_ASSETAL_LOG     WHERE STATUS='P' UNION     SELECT 'M15-07' AS MENU_CODE, COUNT(1) AS CNT FROM PRO_CCTPARA_VERIFY PCV     INNER JOIN PRO_CCTPARA PC ON PC.VERIFY_ID = PCV.VERIFY_ID      WHERE STATUS='P' AND PC.VERIFY_ROLE LIKE '%' + :roleCode + '%' UNION     SELECT 'M15-08' AS MENU_CODE, COUNT(1) AS CNT FROM PRO_PREODR_INVTG_LOG     WHERE STATUS='P' ) PTP ON PTP.MENU_CODE = AM.MENU_CODE LEFT JOIN ADM_ROLE_MENU_MAP ARMM ON PTP.MENU_CODE = ARMM.MENU_CODE AND ARMM.ROLE_CODE = :roleCode WHERE AM.MENU_CODE IN ('M15-01', 'M15-02', 'M15-03', 'M15-04', 'M15-05', 'M15-06', 'M15-07', 'M15-08') ) SELECT SQL9.MENU_NAME,         SQL9.PROG_CLASSNAME,         CASE             WHEN SQL9.REVIEW_YN = 'N' THEN 0             ELSE SQL9.CNT             END AS SUBMIT_CNT,         CASE             WHEN SQL9.REVIEW_YN = 'N' THEN 0             ELSE SQL10.CNT             END AS VERIFY_CNT,         SQL9.REVIEW_YN  FROM SQL9           JOIN SQL10 ON SQL9.MENU_NAME = SQL10.MENU_NAME  ,class com.fcb.pbs.gen.web.model.HomepageReviewCntResp,{roleCode=IT01, userCode=00202304}"}]}