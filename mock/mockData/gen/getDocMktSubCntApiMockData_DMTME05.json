{"status": 200, "data": [{"typeCode": "DMTME0501", "typeName": "研討會", "cnt": "0"}, {"typeCode": "DMTME0502", "typeName": "投資機會", "cnt": "0"}, {"typeCode": "DMTME0503", "typeName": "活動餐會", "cnt": "0"}], "timestamp": "2025/07/22", "sqlTracer": [{"data": [{"typeCode": "DMTME0501", "typeName": "研討會", "cnt": "0"}, {"typeCode": "DMTME0502", "typeName": "投資機會", "cnt": "0"}, {"typeCode": "DMTME0503", "typeName": "活動餐會", "cnt": "0"}], "sqlInfo": "SELECT GDT.TYPE_CODE,GDT.TYPE_NAME,COALESCE (D.CNT,0) AS CNT FROM GEN_DOC_TYPE GDT LEFT JOIN ( SELECT GDM.SUB_TYPE_CODE,count(*) AS CNT FROM GEN_DOC_MKT GDM WHERE GDM.MAIN_TYPE_CODE = :mainTypeCode GROUP BY GDM.SUB_TYPE_CODE )D ON GDT.TYPE_CODE = D.SUB_TYPE_CODE WHERE GDT.CAT_CODE ='MKTEVENT' AND GDT.MAIN_TYPE_CODE= :mainTypeCode ,class com.bi.pbs.gen.web.model.GetDocMktCntResp,{mainTypeCode=DMTME05}"}]}