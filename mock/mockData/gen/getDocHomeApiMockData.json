{"status": 200, "data": [{"catCode": "ISSUER", "catName": "發行機構報告", "cnt": 4}, {"catCode": "MKTEVENT", "catName": "行銷活動", "cnt": 5}, {"catCode": "MKTOUTLOOK", "catName": "研究報告", "cnt": 6}, {"catCode": "NEWS", "catName": "新聞事件", "cnt": 8}, {"catCode": "PRODUCT", "catName": "產品文件", "cnt": 10}], "timestamp": "2025/07/17", "sqlTracer": [{"data": [{"catCode": "ISSUER", "catName": "發行機構報告", "cnt": 4}, {"catCode": "MKTEVENT", "catName": "行銷活動", "cnt": 5}, {"catCode": "MKTOUTLOOK", "catName": "研究報告", "cnt": 6}, {"catCode": "NEWS", "catName": "新聞事件", "cnt": 8}, {"catCode": "PRODUCT", "catName": "產品文件", "cnt": 10}], "sqlInfo": " SELECT CODE_VALUE CAT_CODE,CODE_NAME CAT_NAME  FROM ADM_CODE_DETAIL  WHERE CODE_TYPE = 'DOC_CATS'  ORDER BY SHOW_ORDER ,class com.bi.pbs.gen.web.model.DocHomeResp,{}"}, {"data": {"product": 10, "mktOutLook": 6, "mktEvent": 5, "issuer": 4, "news": 8}, "sqlInfo": " SELECT COUNT(CNT.PRO_CNT) PRODUCT,  COUNT(CNT.OLK_CNT) MKTOUTLOOK,  COUNT(CNT.ENT_CNT) MKTEVENT,  COUNT(CNT.ISU_CNT) ISSUER,  COUNT(CNT.NWS_CNT) NEWS  FROM (     SELECT PRO.DOC_ID PRO_CNT, OLK.DOC_ID OLK_CNT, ENT.DOC_ID ENT_CNT,     ISU.DOC_ID ISU_CNT, NWS.DOC_ID NWS_CNT     FROM GEN_DOCUMENTS DOC     LEFT JOIN (         SELECT DP.*         FROM GEN_DOC_PRODUCTS DP JOIN PRODUCTS P ON DP.PRO_CODE = P.PRO_CODE     ) PRO ON DOC.DOC_ID = PRO.DOC_ID     LEFT JOIN GEN_MKT_OUTLOOK OLK ON DOC.DOC_ID = OLK.DOC_ID     LEFT JOIN GEN_DOC_MKT ENT ON DOC.DOC_ID = ENT.DOC_ID     LEFT JOIN (         SELECT GI.*         FROM GEN_ISSUER GI         JOIN PRO_ISSUERS PI ON GI.ISSUER_CODE = PI.ISSUER_CODE AND GI.PFCAT_CODE = PI.PFCAT_CODE     ) ISU ON DOC.DOC_ID = ISU.DOC_ID     LEFT JOIN GEN_NEWS NWS ON DOC.DOC_ID = NWS.DOC_ID     GROUP BY PRO.DOC_ID, OLK.DOC_ID, ENT.DOC_ID, ISU.DOC_ID, NWS.DOC_ID  ) CNT ,class com.bi.pbs.gen.web.model.DocHomeCount,{}"}]}