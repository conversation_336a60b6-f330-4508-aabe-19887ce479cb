{"status": 200, "data": [{"code": "M40-020", "name": "公告及文件下載", "parentCode": "M40-02", "url": "vue-bbs-head-all", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M40-021", "name": "產品", "parentCode": "M40-02", "url": "vue-bbs-head", "order": 2, "leafYn": "Y", "leaf": false}, {"code": "M40-022", "name": "一般", "parentCode": "M40-02", "url": "vue-bbs-head", "order": 3, "leafYn": "Y", "leaf": false}, {"code": "M40-023", "name": "跑馬燈", "parentCode": "M40-02", "url": "vue-bbs-head", "order": 4, "leafYn": "Y", "leaf": false}, {"code": "M40-024", "name": "強制閱讀", "parentCode": "M40-02", "url": "vue-bbs-head", "order": 5, "leafYn": "Y", "leaf": false}], "timestamp": "2025/06/30", "sqlTracer": [{"data": [{"code": "M40-020", "name": "公告及文件下載", "parentCode": "M40-02", "url": "vue-bbs-head-all", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M40-021", "name": "產品", "parentCode": "M40-02", "url": "vue-bbs-head", "order": 2, "leafYn": "Y", "leaf": false}, {"code": "M40-022", "name": "一般", "parentCode": "M40-02", "url": "vue-bbs-head", "order": 3, "leafYn": "Y", "leaf": false}, {"code": "M40-023", "name": "跑馬燈", "parentCode": "M40-02", "url": "vue-bbs-head", "order": 4, "leafYn": "Y", "leaf": false}, {"code": "M40-024", "name": "強制閱讀", "parentCode": "M40-02", "url": "vue-bbs-head", "order": 5, "leafYn": "Y", "leaf": false}], "sqlInfo": " SELECT DISTINCT      M<PERSON><PERSON><PERSON>_CODE CODE,      <PERSON><PERSON>ME<PERSON>_NAME NAME,      M.PARENT_MENU_CODE PARENT_CODE,      M<PERSON>SHOW_ORDER 'ORDER',      M.MENU_ICON ICON,      <PERSON><PERSON>TAB_YN LEAF_YN,      AP.PROG_CLASSNAME URL  FROM ADM_MENUS M      JOIN ADM_ROLE_MENU_MAP ARMM ON ARMM.MENU_CODE = M.MENU_CODE      LEFT JOIN ADM_PROGS AP ON AP.PROG_CODE = M.PROG_CODE  WHERE ARMM.ROLE_CODE = :roleCode    AND M.ACTIVE_YN = 'Y'    AND M.PARENT_MENU_CODE = :parentMenuCode    AND M.TAB_YN = :tabYn ORDER BY M.SHOW_ORDER ASC, M.MENU_CODE ASC ,class com.bi.frame.menu.model.MenuTree,{roleCode=98, tabYn=Y, parentMenuCode=M40-02}"}]}