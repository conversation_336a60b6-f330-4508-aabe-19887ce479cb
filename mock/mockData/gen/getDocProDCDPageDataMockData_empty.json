{"status": 200, "data": {"content": [], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": true, "totalElements": 0, "totalPages": 0, "first": true, "size": 10, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 0, "empty": true}, "timestamp": "2025/07/22", "sqlTracer": [{"data": [], "sqlInfo": " SELECT DISTINCT DOC_ID  FROM GEN_DOC_PRODUCTS GDP  INNER JOIN PRODUCTS P on GDP.PRO_CODE = P.PRO_CODE  WHERE P.PFCAT_CODE = :proTypeCode ,class java.lang.String,{proTypeCode=SP}"}]}