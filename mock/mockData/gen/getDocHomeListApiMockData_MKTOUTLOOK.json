{"status": 200, "data": [{"docId": "DF202502230002", "docName": "20250223_3_TEST", "validDt": "2025/02/24", "expireDt": "2025/02/24"}, {"docId": "DF202502230001", "docName": "20250223_2_TEST", "validDt": "2025/02/24", "expireDt": "2025/02/24"}, {"docId": "DF202502260001", "docName": "測試研究報告", "validDt": "2025/02/26", "expireDt": "2025/02/28"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28"}, {"docId": "DF202502110006", "docName": "研究報告測試", "validDt": "2025/02/11", "expireDt": "2025/02/28"}, {"docId": "DF202502260003", "docName": "研究報告測試", "validDt": "2025/02/26", "expireDt": "2025/02/28"}], "timestamp": "2025/07/17", "sqlTracer": [{"data": [{"docId": "DF202502230002", "docName": "20250223_3_TEST", "validDt": "2025/02/24", "expireDt": "2025/02/24"}, {"docId": "DF202502230001", "docName": "20250223_2_TEST", "validDt": "2025/02/24", "expireDt": "2025/02/24"}, {"docId": "DF202502260001", "docName": "測試研究報告", "validDt": "2025/02/26", "expireDt": "2025/02/28"}, {"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28"}, {"docId": "DF202502110006", "docName": "研究報告測試", "validDt": "2025/02/11", "expireDt": "2025/02/28"}, {"docId": "DF202502260003", "docName": "研究報告測試", "validDt": "2025/02/26", "expireDt": "2025/02/28"}], "sqlInfo": " SELECT TOP 8 GD.DOC_ID,DOC_NAME,VALID_DT,EXPIRE_DT  FROM GEN_DOCUMENTS GD  INNER JOIN GEN_MKT_OUTLOOK GMO ON GD.DOC_ID = GMO.DOC_ID  ORDER BY GD.PRIORITY,GD.VALID_DT DESC ,class com.bi.pbs.gen.web.model.DocHomeListResp,{}"}]}