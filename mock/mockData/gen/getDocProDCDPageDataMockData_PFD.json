{"status": 200, "data": {"content": [{"docId": "DF202503130001", "docName": "test", "validDt": "2025/03/07", "expireDt": "2025/03/31", "priorityName": "低", "showCusYnName": "N", "proList": [{"proCode": "FUND_0112", "proName": "富達東協基金"}, {"proCode": "PFD_00005", "proName": "HSBC Holdings"}], "fileList": [{"fileName": "DOC2025031300001.pdf", "showName": "110年度行事曆.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202503130001", "docFileId": "DOC2025031300001"}]}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": true, "totalElements": 1, "totalPages": 1, "first": true, "size": 10, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 1, "empty": false}, "timestamp": "2025/07/22", "sqlTracer": [{"data": ["DF202503130001"], "sqlInfo": " SELECT DISTINCT DOC_ID  FROM GEN_DOC_PRODUCTS GDP  INNER JOIN PRODUCTS P on GDP.PRO_CODE = P.PRO_CODE  WHERE P.PFCAT_CODE = :proTypeCode ,class java.lang.String,{proTypeCode=PFD}"}, {"data": {"content": [{"docId": "DF202503130001", "docName": "test", "validDt": "2025/03/07", "expireDt": "2025/03/31", "priorityName": "低", "showCusYnName": "N", "proList": [{"proCode": "FUND_0112", "proName": "富達東協基金"}, {"proCode": "PFD_00005", "proName": "HSBC Holdings"}], "fileList": [{"fileName": "DOC2025031300001.pdf", "showName": "110年度行事曆.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202503130001", "docFileId": "DOC2025031300001"}]}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": true, "totalElements": 1, "totalPages": 1, "first": true, "size": 10, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 1, "empty": false}, "sqlInfo": "SELECT GD.DOC_ID ,GD.DOC_NAME, GD.VALID_DT, GD.EXPIRE_DT, GD.SHOWCUS_YN, GD.PRIORITY ,ACD_P.CODE_NAME AS PRIORITY_NAME ,ACD_C.CODE_NAME AS SHOW_CUS_NAME FROM GEN_DOCUMENTS GD LEFT JOIN ADM_CODE_DETAIL ACD_P ON ACD_P.CODE_TYPE ='DOC_PRIORITY' AND ACD_P.CODE_VALUE = GD.PRIORITY LEFT JOIN ADM_CODE_DETAIL ACD_C ON ACD_C.CODE_TYPE ='DOC_SHOWCUS_YN' AND ACD_C.CODE_VALUE = GD.SHOWCUS_YN WHERE GD.DOC_ID IN(:DOC_ID) ,Page request [number: 0, size 10, sort: DOC_ID: ASC],class com.bi.pbs.gen.web.model.GetDocMktsResp,{DOC_ID=[DF202503130001]}"}, {"data": [{"proCode": "FUND_0112", "proName": "富達東協基金"}, {"proCode": "PFD_00005", "proName": "HSBC Holdings"}], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202503130001}"}, {"data": [{"fileName": "DOC2025031300001.pdf", "showName": "110年度行事曆.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202503130001", "docFileId": "DOC2025031300001"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202503130001}"}]}