{"status": 200, "data": [{"code": "M40-010", "name": "公告事項設定", "parentCode": "M40-01", "url": "vue-bbs-mgt", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M40-011", "name": "公告事項查詢", "parentCode": "M40-01", "url": "vue-bbs-mgt-ser", "order": 2, "leafYn": "Y", "leaf": false}], "timestamp": "2025/06/30", "sqlTracer": [{"data": [{"code": "M40-010", "name": "公告事項設定", "parentCode": "M40-01", "url": "vue-bbs-mgt", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M40-011", "name": "公告事項查詢", "parentCode": "M40-01", "url": "vue-bbs-mgt-ser", "order": 2, "leafYn": "Y", "leaf": false}], "sqlInfo": " SELECT DISTINCT      M<PERSON><PERSON><PERSON>_CODE CODE,      <PERSON><PERSON>ME<PERSON>_NAME NAME,      M.PARENT_MENU_CODE PARENT_CODE,      M<PERSON>SHOW_ORDER 'ORDER',      M.MENU_ICON ICON,      <PERSON><PERSON>TAB_YN LEAF_YN,      AP.PROG_CLASSNAME URL  FROM ADM_MENUS M      JOIN ADM_ROLE_MENU_MAP ARMM ON ARMM.MENU_CODE = M.MENU_CODE      LEFT JOIN ADM_PROGS AP ON AP.PROG_CODE = M.PROG_CODE  WHERE ARMM.ROLE_CODE = :roleCode    AND M.ACTIVE_YN = 'Y'    AND M.PARENT_MENU_CODE = :parentMenuCode    AND M.TAB_YN = :tabYn ORDER BY M.SHOW_ORDER ASC, M.MENU_CODE ASC ,class com.bi.frame.menu.model.MenuTree,{roleCode=98, tabYn=Y, parentMenuCode=M40-01}"}]}