{"status": 200, "data": [{"msgCode": "MSG01", "msgName": "產品", "cnt": 6}, {"msgCode": "MSG02", "msgName": "一般", "cnt": 1}], "timestamp": "2025/07/17", "sqlTracer": [{"data": [{"msgCode": "MSG01", "msgName": "產品", "cnt": 6}, {"msgCode": "MSG02", "msgName": "一般", "cnt": 1}], "sqlInfo": " SELECT GMM.MSG_CODE,GMM.MSG_NAME,COUNT(GM.MSG_CODE) CNT  FROM GEN_MESSAGE_MAPS GMM  LEFT JOIN GEN_MESSAGES GM ON GM.MSG_CODE = GMM.MSG_CODE  WHERE MSG_TYPE IN (:msgType)  GROUP BY GMM.MSG_CODE,GMM.MSG_NAME ,class com.bi.pbs.gen.web.model.MessageMapHomeResp,{msgType=[B]}"}]}