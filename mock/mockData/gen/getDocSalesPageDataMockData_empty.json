{"status": 200, "data": {"content": [], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": true, "totalElements": 0, "totalPages": 0, "first": true, "size": 10, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 0, "empty": true}, "timestamp": "2025/07/22", "sqlTracer": [{"data": {"content": [], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": true, "totalElements": 0, "totalPages": 0, "first": true, "size": 10, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 0, "empty": true}, "sqlInfo": "SELECT GD.DOC_ID ,GD.DOC_NAME, GD.VALID_DT, GD.EXPIRE_DT, GD.SHOWCUS_YN, GD.PRIORITY ,ACD_P.CODE_NAME AS PRIORITY_NAME ,ACD_C.CODE_NAME AS SHOW_CUS_NAME FROM GEN_DOCUMENTS GD INNER JOIN GEN_DOC_MKT GDM ON GDM.DOC_ID = GD.DOC_ID LEFT JOIN ADM_CODE_DETAIL ACD_P ON ACD_P.CODE_TYPE ='DOC_PRIORITY' AND ACD_P.CODE_VALUE = GD.PRIORITY LEFT JOIN ADM_CODE_DETAIL ACD_C ON ACD_C.CODE_TYPE ='DOC_SHOWCUS_YN' AND ACD_C.CODE_VALUE = GD.SHOWCUS_YN WHERE GDM.MAIN_TYPE_CODE = :mainTypeCode AND GDM.SUB_TYPE_CODE = :subTypeCode ,Page request [number: 0, size 10, sort: DOC_ID: ASC],class com.bi.pbs.gen.web.model.GetDocMktsResp,{mainTypeCode=DMTME04, subTypeCode=DMTME0402}"}]}