{"status": 200, "data": {"content": [{"docId": "DF202503110001", "docName": "test", "validDt": "2025/03/11", "expireDt": "2025/10/04", "priorityName": "低", "showCusYnName": "N", "proList": [{"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}, {"proCode": "INS_01AEA4_54", "proName": "澳滿福澳幣利率變動型養老保險"}, {"proCode": "INS_01TEA1_54", "proName": "優沛利利變養老"}], "fileList": [{"fileName": "DOC2025031100001.pdf", "showName": "國家代碼表.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202503110001", "docFileId": "DOC2025031100001"}]}, {"docId": "DF202504020001", "docName": "test33355", "validDt": "2025/01/03", "expireDt": "2025/04/04", "priorityName": "低", "showCusYnName": "Y", "proList": [{"proCode": "BOND_23AC", "proName": " 一銀１１２年度第２期１．５年期金融債券   "}, {"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}, {"proCode": "INS_01AEA4_54", "proName": "澳滿福澳幣利率變動型養老保險"}], "fileList": [{"fileName": "DOC2025040200001.docx", "showName": "SKB_工作導向系統-系統管理操作手冊v1.0.docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202504020001", "docFileId": "DOC2025040200001"}]}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": true, "totalElements": 2, "totalPages": 1, "first": true, "size": 10, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 2, "empty": false}, "timestamp": "2025/07/22", "sqlTracer": [{"data": ["DF202503110001", "DF202504020001"], "sqlInfo": " SELECT DISTINCT DOC_ID  FROM GEN_DOC_PRODUCTS GDP  INNER JOIN PRODUCTS P on GDP.PRO_CODE = P.PRO_CODE  WHERE P.PFCAT_CODE = :proTypeCode ,class java.lang.String,{proTypeCode=INS}"}, {"data": {"content": [{"docId": "DF202503110001", "docName": "test", "validDt": "2025/03/11", "expireDt": "2025/10/04", "priorityName": "低", "showCusYnName": "N", "proList": [{"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}, {"proCode": "INS_01AEA4_54", "proName": "澳滿福澳幣利率變動型養老保險"}, {"proCode": "INS_01TEA1_54", "proName": "優沛利利變養老"}], "fileList": [{"fileName": "DOC2025031100001.pdf", "showName": "國家代碼表.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202503110001", "docFileId": "DOC2025031100001"}]}, {"docId": "DF202504020001", "docName": "test33355", "validDt": "2025/01/03", "expireDt": "2025/04/04", "priorityName": "低", "showCusYnName": "Y", "proList": [{"proCode": "BOND_23AC", "proName": " 一銀１１２年度第２期１．５年期金融債券   "}, {"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}, {"proCode": "INS_01AEA4_54", "proName": "澳滿福澳幣利率變動型養老保險"}], "fileList": [{"fileName": "DOC2025040200001.docx", "showName": "SKB_工作導向系統-系統管理操作手冊v1.0.docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202504020001", "docFileId": "DOC2025040200001"}]}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": true, "totalElements": 2, "totalPages": 1, "first": true, "size": 10, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 2, "empty": false}, "sqlInfo": "SELECT GD.DOC_ID ,GD.DOC_NAME, GD.VALID_DT, GD.EXPIRE_DT, GD.SHOWCUS_YN, GD.PRIORITY ,ACD_P.CODE_NAME AS PRIORITY_NAME ,ACD_C.CODE_NAME AS SHOW_CUS_NAME FROM GEN_DOCUMENTS GD LEFT JOIN ADM_CODE_DETAIL ACD_P ON ACD_P.CODE_TYPE ='DOC_PRIORITY' AND ACD_P.CODE_VALUE = GD.PRIORITY LEFT JOIN ADM_CODE_DETAIL ACD_C ON ACD_C.CODE_TYPE ='DOC_SHOWCUS_YN' AND ACD_C.CODE_VALUE = GD.SHOWCUS_YN WHERE GD.DOC_ID IN(:DOC_ID) ,Page request [number: 0, size 10, sort: DOC_ID: ASC],class com.bi.pbs.gen.web.model.GetDocMktsResp,{DOC_ID=[DF202503110001, DF202504020001]}"}, {"data": [{"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}, {"proCode": "INS_01AEA4_54", "proName": "澳滿福澳幣利率變動型養老保險"}, {"proCode": "INS_01TEA1_54", "proName": "優沛利利變養老"}], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202503110001}"}, {"data": [{"fileName": "DOC2025031100001.pdf", "showName": "國家代碼表.pdf", "contentType": "application/pdf", "filePath": "GEN/TEMP/DF202503110001", "docFileId": "DOC2025031100001"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202503110001}"}, {"data": [{"proCode": "BOND_23AC", "proName": " 一銀１１２年度第２期１．５年期金融債券   "}, {"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}, {"proCode": "INS_01AEA4_54", "proName": "澳滿福澳幣利率變動型養老保險"}], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202504020001}"}, {"data": [{"fileName": "DOC2025040200001.docx", "showName": "SKB_工作導向系統-系統管理操作手冊v1.0.docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202504020001", "docFileId": "DOC2025040200001"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202504020001}"}]}