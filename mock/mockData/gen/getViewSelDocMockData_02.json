{"status": 200, "data": [{"docId": "DF202502110003", "docName": "TEST0211發行機構測試", "validDt": "2025/02/11", "expireDt": "2025/02/28", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "docDesc": "TEST0211發行機構測試", "createBy": " ", "createDt": "2025/02/11", "modifyBy": " ", "docCatName": "發行機構報告", "fileInfo": [{"fileName": "DOC2025021100003.pdf", "showName": "請假流程.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110003", "docFileId": "DOC2025021100003"}, {"fileName": "DOC2025021100004.pdf", "showName": "G-026-請假規則.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110003", "docFileId": "DOC2025021100004"}], "productInfo": [], "issuerInfo": [{"issuerCode": "ABN", "issuerName": "荷蘭銀行", "pfcatCode": "FB"}, {"issuerCode": "BANQUE", "issuerName": "盧森堡國家儲蓄銀行", "pfcatCode": "FB"}, {"issuerCode": "BARCLAY", "issuerName": "BARCLAYS BANK PLC", "pfcatCode": "FB"}, {"issuerCode": "BEAWMS", "issuerName": "永豐金證券股份有限公司", "pfcatCode": "FB"}], "newsConditionInfo": [{"condType": "FND", "condMajorCode": "N", "condMinorCode": "N"}]}], "timestamp": "2025/07/18", "sqlTracer": [{"data": {"codeValue": "ISSUER", "codeName": "發行機構報告"}, "sqlInfo": " SELECT ACD.CODE_VALUE , ACD.CODE_NAME  FROM ADM_CODE_DETAIL ACD  WHERE ACD.CODE_TYPE = :codeType  AND ACD.CODE_VALUE = :codeValue  ORDER BY ACD.SHOW_ORDER ,class com.bi.pbs.adm.web.model.CodeDetailResp,{codeType=DOC_CATS, codeValue=ISSUER}"}, {"data": [{"docId": "DF202502110003", "docName": "TEST0211發行機構測試", "validDt": "2025/02/11", "expireDt": "2025/02/28", "priorityName": "高", "showCusName": "可提供給客戶", "showCusYn": "Y", "docDesc": "TEST0211發行機構測試", "createDt": "2025/02/11"}], "sqlInfo": "SELECT GD.DOC_ID   ,GD.DOC_NAME   ,GD.VALID_DT   ,GD.EXPIRE_DT   ,GD.SHOWCUS_YN   ,GD.DOC_DESC   ,GD.CREATE_DT   ,ACDP.CODE_NAME AS PRIORITY_NAME   ,ACDC.CODE_NAME AS SHOW_CUS_NAME FROM GEN_DOCUMENTS GD LEFT JOIN GEN_DOC_MKT GDM ON GDM.DOC_ID = GD.DOC_ID LEFT JOIN GEN_MKT_OUTLOOK GMO ON GMO.DOC_ID = GD.DOC_ID LEFT JOIN ADM_CODE_DETAIL ACDP ON ACDP.CODE_VALUE =GD.PRIORITY AND ACDP.CODE_TYPE ='DOC_PRIORITY' LEFT JOIN ADM_CODE_DETAIL  ACDC ON ACDC.CODE_VALUE =GD.SHOWCUS_YN AND ACDC.CODE_TYPE ='DOC_SHOWCUS_YN' WHERE 1=1 AND GD.DOC_ID IN( SELECT DISTINCT DOC_ID FROM GEN_ISSUER GI )  AND GD.DOC_ID = :docId ,class com.bi.pbs.gen.web.model.SelectDocumentResp,{docId=DF202502110003}"}, {"data": [{"fileName": "DOC2025021100003.pdf", "showName": "請假流程.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110003", "docFileId": "DOC2025021100003"}, {"fileName": "DOC2025021100004.pdf", "showName": "G-026-請假規則.pdf", "contentType": "application/pdf", "filePath": "/data/PBS/DOC/GEN/TEMP/DF202502110003", "docFileId": "DOC2025021100004"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502110003}"}, {"data": [], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202502110003}"}, {"sqlInfo": " SELECT  MAIN_TYPE_CODE,  SUB_TYPE_CODE  FROM GEN_DOC_MKT  WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetDocMkt,{docId=DF202502110003}"}, {"sqlInfo": " SELECT  MAIN_CAT_CODE,  SUB_CAT_CODE  FROM GEN_MKT_OUTLOOK  WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetDocOutlook,{docId=DF202502110003}"}, {"data": [{"issuerCode": "ABN", "issuerName": "荷蘭銀行", "pfcatCode": "FB"}, {"issuerCode": "BANQUE", "issuerName": "盧森堡國家儲蓄銀行", "pfcatCode": "FB"}, {"issuerCode": "BARCLAY", "issuerName": "BARCLAYS BANK PLC", "pfcatCode": "FB"}, {"issuerCode": "BEAWMS", "issuerName": "永豐金證券股份有限公司", "pfcatCode": "FB"}], "sqlInfo": " SELECT  GI.ISSUER_CODE,  PIS.ISSUER_NAME,  PIS.PFCAT_CODE  FROM GEN_ISSUER GI  LEFT JOIN PRO_ISSUERS PIS ON GI.ISSUER_CODE = PIS.ISSUER_CODE  WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetIssuerInfo,{docId=DF202502110003}"}, {"sqlInfo": " SELECT  ALLCUS_YN  FROM GEN_NEWS  WHERE DOC_ID = :docId ,class java.lang.String,{docId=DF202502110003}"}, {"data": [{"condType": "FND", "condMajorCode": "N", "condMinorCode": "N"}], "sqlInfo": " SELECT  COND_TYPE, CO<PERSON>_<PERSON>JOR_CODE, COND_MINOR_CODE  FROM GEN_NEWS_CONDITION  WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetNewsConditionInfo,{docId=DF202502110003}"}, {"data": [], "sqlInfo": " SELECT  DISTINCT GN.DOC_ID,   CASE WHEN GNC.COND_TYPE IS NULL THEN '給所有客戶'  WHEN GNC.COND_TYPE = 'SEC' THEN '給持有相關商品的客戶(投資標的)'  WHEN GNC.COND_TYPE = 'GEO' THEN '給持有相關商品的客戶(投資地區)'  WHEN GNC.COND_TYPE = 'FND' THEN '給持有基金相關商品的客戶'  END COND_NAME  FROM GEN_NEWS GN  LEFT JOIN GEN_NEWS_CONDITION GNC ON GN.DOC_ID = GNC.DOC_ID  WHERE GN.DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetNewsNoticeInfo,{docId=DF202502110003}"}]}