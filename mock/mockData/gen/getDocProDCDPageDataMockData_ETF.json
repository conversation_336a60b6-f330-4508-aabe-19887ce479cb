{"status": 200, "data": {"content": [{"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28", "priorityName": "中", "showCusYnName": "Y", "proList": [{"proCode": "ETF_02800"}, {"proCode": "ETF_02825", "proName": "CSI Hong Kong 100 Index Fund"}, {"proCode": "ETF_03033", "proName": "CSOP Hang Seng Technology Index ETF"}, {"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}], "fileList": [{"fileName": "DOC2025021500002.pdf", "showName": "一般管理.pdf", "contentType": "application/pdf", "filePath": "/usr/local/tomcat/data/PBS/DOC/GEN/TEMP/DF202502150002", "docFileId": "DOC2025021500002"}]}, {"docId": "DF202502240001", "docName": "test", "validDt": "2025/02/24", "expireDt": "2025/02/28", "priorityName": "中", "showCusYnName": "N", "proList": [{"proCode": "ETF_1497", "proName": "iShares USD High Yield Corporate Bond JPY Hedged ETF"}], "fileList": []}, {"docId": "DF202502260005", "docName": "產品文件", "validDt": "2025/02/26", "expireDt": "2025/03/31", "priorityName": "低", "showCusYnName": "Y", "proList": [{"proCode": "ETF_02800"}, {"proCode": "ETF_02825", "proName": "CSI Hong Kong 100 Index Fund"}], "fileList": [{"fileName": "DOC2025022600009.docx", "showName": "get-underUserDeputies (1).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260005", "docFileId": "DOC2025022600009"}, {"fileName": "DOC2025022600010.docx", "showName": "get-underUserDeputies (1) (1).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260005", "docFileId": "DOC2025022600010"}]}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": true, "totalElements": 3, "totalPages": 1, "first": true, "size": 10, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 3, "empty": false}, "timestamp": "2025/07/22", "sqlTracer": [{"data": ["DF202502150002", "DF202502240001", "DF202502260005"], "sqlInfo": " SELECT DISTINCT DOC_ID  FROM GEN_DOC_PRODUCTS GDP  INNER JOIN PRODUCTS P on GDP.PRO_CODE = P.PRO_CODE  WHERE P.PFCAT_CODE = :proTypeCode ,class java.lang.String,{proTypeCode=ETF}"}, {"data": {"content": [{"docId": "DF202502150002", "docName": "基金相關", "validDt": "2025/02/15", "expireDt": "2025/02/28", "priorityName": "中", "showCusYnName": "Y", "proList": [{"proCode": "ETF_02800"}, {"proCode": "ETF_02825", "proName": "CSI Hong Kong 100 Index Fund"}, {"proCode": "ETF_03033", "proName": "CSOP Hang Seng Technology Index ETF"}, {"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}], "fileList": [{"fileName": "DOC2025021500002.pdf", "showName": "一般管理.pdf", "contentType": "application/pdf", "filePath": "/usr/local/tomcat/data/PBS/DOC/GEN/TEMP/DF202502150002", "docFileId": "DOC2025021500002"}]}, {"docId": "DF202502240001", "docName": "test", "validDt": "2025/02/24", "expireDt": "2025/02/28", "priorityName": "中", "showCusYnName": "N", "proList": [{"proCode": "ETF_1497", "proName": "iShares USD High Yield Corporate Bond JPY Hedged ETF"}], "fileList": []}, {"docId": "DF202502260005", "docName": "產品文件", "validDt": "2025/02/26", "expireDt": "2025/03/31", "priorityName": "低", "showCusYnName": "Y", "proList": [{"proCode": "ETF_02800"}, {"proCode": "ETF_02825", "proName": "CSI Hong Kong 100 Index Fund"}], "fileList": [{"fileName": "DOC2025022600009.docx", "showName": "get-underUserDeputies (1).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260005", "docFileId": "DOC2025022600009"}, {"fileName": "DOC2025022600010.docx", "showName": "get-underUserDeputies (1) (1).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260005", "docFileId": "DOC2025022600010"}]}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": true, "totalElements": 3, "totalPages": 1, "first": true, "size": 10, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 3, "empty": false}, "sqlInfo": "SELECT GD.DOC_ID ,GD.DOC_NAME, GD.VALID_DT, GD.EXPIRE_DT, GD.SHOWCUS_YN, GD.PRIORITY ,ACD_P.CODE_NAME AS PRIORITY_NAME ,ACD_C.CODE_NAME AS SHOW_CUS_NAME FROM GEN_DOCUMENTS GD LEFT JOIN ADM_CODE_DETAIL ACD_P ON ACD_P.CODE_TYPE ='DOC_PRIORITY' AND ACD_P.CODE_VALUE = GD.PRIORITY LEFT JOIN ADM_CODE_DETAIL ACD_C ON ACD_C.CODE_TYPE ='DOC_SHOWCUS_YN' AND ACD_C.CODE_VALUE = GD.SHOWCUS_YN WHERE GD.DOC_ID IN(:DOC_ID) ,Page request [number: 0, size 10, sort: DOC_ID: ASC],class com.bi.pbs.gen.web.model.GetDocMktsResp,{DOC_ID=[DF202502150002, DF202502240001, DF202502260005]}"}, {"data": [{"proCode": "ETF_02800"}, {"proCode": "ETF_02825", "proName": "CSI Hong Kong 100 Index Fund"}, {"proCode": "ETF_03033", "proName": "CSOP Hang Seng Technology Index ETF"}, {"proCode": "FUND_0101", "proName": "富達全球主題機會　　　　　"}], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202502150002}"}, {"data": [{"fileName": "DOC2025021500002.pdf", "showName": "一般管理.pdf", "contentType": "application/pdf", "filePath": "/usr/local/tomcat/data/PBS/DOC/GEN/TEMP/DF202502150002", "docFileId": "DOC2025021500002"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502150002}"}, {"data": [{"proCode": "ETF_1497", "proName": "iShares USD High Yield Corporate Bond JPY Hedged ETF"}], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202502240001}"}, {"data": [], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502240001}"}, {"data": [{"proCode": "ETF_02800"}, {"proCode": "ETF_02825", "proName": "CSI Hong Kong 100 Index Fund"}], "sqlInfo": "SELECT GDP.PRO_CODE   ,P.PRO_NAME   FROM GEN_DOC_PRODUCTS GDP    LEFT JOIN PRODUCTS P    ON GDP.PRO_CODE = P.PRO_CODE     WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetProductInfo,{docId=DF202502260005}"}, {"data": [{"fileName": "DOC2025022600009.docx", "showName": "get-underUserDeputies (1).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260005", "docFileId": "DOC2025022600009"}, {"fileName": "DOC2025022600010.docx", "showName": "get-underUserDeputies (1) (1).docx", "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "filePath": "GEN/TEMP/DF202502260005", "docFileId": "DOC2025022600010"}], "sqlInfo": "SELECT DOC_FILE_ID   ,FILE_NAME   ,SHOW_NAME   ,CONTENT_TYPE   ,FILE_PATH   FROM GEN_DOC_FILES    WHERE DOC_ID = :docId ,class com.bi.pbs.gen.web.model.GetFileInfo,{docId=DF202502260005}"}]}