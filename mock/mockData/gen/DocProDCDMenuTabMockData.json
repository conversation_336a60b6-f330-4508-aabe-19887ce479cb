{"status": 200, "data": [{"code": "M41-010", "name": "信託-基金", "parentCode": "M41-01", "url": "vue-doc-pro-dcd", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M41-011", "name": "信託-ETF", "parentCode": "M41-01", "url": "vue-doc-pro-dcd", "order": 2, "leafYn": "Y", "leaf": false}, {"code": "M41-012", "name": "信託-海外債", "parentCode": "M41-01", "url": "vue-doc-pro-dcd", "order": 3, "leafYn": "Y", "leaf": false}, {"code": "M41-013", "name": "信託-海外股票", "parentCode": "M41-01", "url": "vue-doc-pro-dcd", "order": 4, "leafYn": "Y", "leaf": false}, {"code": "M41-014", "name": "信託-境外結構型商品", "parentCode": "M41-01", "url": "vue-doc-pro-dcd", "order": 5, "leafYn": "Y", "leaf": false}, {"code": "M41-015", "name": "信託-銀行結構型商品", "parentCode": "M41-01", "url": "vue-doc-pro-dcd", "order": 6, "leafYn": "Y", "leaf": false}, {"code": "M41-016", "name": "人生保險", "parentCode": "M41-01", "url": "vue-doc-pro-dcd", "order": 7, "leafYn": "Y", "leaf": false}], "timestamp": "2025/07/22", "sqlTracer": [{"data": [{"code": "M41-010", "name": "信託-基金", "parentCode": "M41-01", "url": "vue-doc-pro-dcd", "order": 1, "leafYn": "Y", "leaf": false}, {"code": "M41-011", "name": "信託-ETF", "parentCode": "M41-01", "url": "vue-doc-pro-dcd", "order": 2, "leafYn": "Y", "leaf": false}, {"code": "M41-012", "name": "信託-海外債", "parentCode": "M41-01", "url": "vue-doc-pro-dcd", "order": 3, "leafYn": "Y", "leaf": false}, {"code": "M41-013", "name": "信託-海外股票", "parentCode": "M41-01", "url": "vue-doc-pro-dcd", "order": 4, "leafYn": "Y", "leaf": false}, {"code": "M41-014", "name": "信託-境外結構型商品", "parentCode": "M41-01", "url": "vue-doc-pro-dcd", "order": 5, "leafYn": "Y", "leaf": false}, {"code": "M41-015", "name": "信託-銀行結構型商品", "parentCode": "M41-01", "url": "vue-doc-pro-dcd", "order": 6, "leafYn": "Y", "leaf": false}, {"code": "M41-016", "name": "人生保險", "parentCode": "M41-01", "url": "vue-doc-pro-dcd", "order": 7, "leafYn": "Y", "leaf": false}], "sqlInfo": " SELECT DISTINCT      M<PERSON><PERSON><PERSON>_CODE CODE,      <PERSON><PERSON>ME<PERSON>_NAME NAME,      M.PARENT_MENU_CODE PARENT_CODE,      M<PERSON>SHOW_ORDER 'ORDER',      M.MENU_ICON ICON,      <PERSON><PERSON>TAB_YN LEAF_YN,      AP.PROG_CLASSNAME URL  FROM ADM_MENUS M      JOIN ADM_ROLE_MENU_MAP ARMM ON ARMM.MENU_CODE = M.MENU_CODE      LEFT JOIN ADM_PROGS AP ON AP.PROG_CODE = M.PROG_CODE  WHERE ARMM.ROLE_CODE = :roleCode    AND M.ACTIVE_YN = 'Y'    AND M.PARENT_MENU_CODE = :parentMenuCode    AND M.TAB_YN = :tabYn ORDER BY M.SHOW_ORDER ASC, M.MENU_CODE ASC ,class com.bi.frame.menu.model.MenuTree,{roleCode=98, tabYn=Y, parentMenuCode=M41-01}"}]}