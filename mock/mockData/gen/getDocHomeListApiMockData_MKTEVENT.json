{"status": 200, "data": [{"docId": "DF202504180001", "docName": "吃反", "validDt": "2025/04/09", "expireDt": "2025/04/26"}, {"docId": "DF202502110002", "docName": "TEST0211測試2", "validDt": "2025/02/11", "expireDt": "2025/02/28"}, {"docId": "DF202502110001", "docName": "TEST0211測試", "validDt": "2025/02/11", "expireDt": "2025/02/28"}, {"docId": "DF202502110010", "docName": "行銷活動未過期測試", "validDt": "2025/02/11", "expireDt": "2025/02/28"}, {"docId": "DF202502110009", "docName": "行銷活動過期測試", "validDt": "2025/02/01", "expireDt": "2025/02/10"}], "timestamp": "2025/07/17", "sqlTracer": [{"data": [{"docId": "DF202504180001", "docName": "吃反", "validDt": "2025/04/09", "expireDt": "2025/04/26"}, {"docId": "DF202502110002", "docName": "TEST0211測試2", "validDt": "2025/02/11", "expireDt": "2025/02/28"}, {"docId": "DF202502110001", "docName": "TEST0211測試", "validDt": "2025/02/11", "expireDt": "2025/02/28"}, {"docId": "DF202502110010", "docName": "行銷活動未過期測試", "validDt": "2025/02/11", "expireDt": "2025/02/28"}, {"docId": "DF202502110009", "docName": "行銷活動過期測試", "validDt": "2025/02/01", "expireDt": "2025/02/10"}], "sqlInfo": " SELECT TOP 8 GD.DOC_ID,DOC_NAME,VALID_DT,EXPIRE_DT  FROM GEN_DOCUMENTS GD  INNER JOIN GEN_DOC_MKT GDM ON GD.DOC_ID = GDM.DOC_ID  ORDER BY GD.PRIORITY,GD.VALID_DT DESC ,class com.bi.pbs.gen.web.model.DocHomeListResp,{}"}]}