{"status": 200, "data": [{"menuName": "高資產資格申請審核", "progClassname": "/wob/pbsOpenReview", "submitCnt": 0, "verifyCnt": 0, "reviewYn": "Y"}, {"menuName": "服務異動申請審核", "progClassname": "/wob/cusApplyReview", "submitCnt": 0, "verifyCnt": 0, "reviewYn": "Y"}, {"menuName": "客戶移轉分派審核", "progClassname": "/cus/cusTransferAudit", "submitCnt": 0, "verifyCnt": 6, "reviewYn": "Y"}], "timestamp": "2025/07/23", "sqlTracer": [{"data": [{"menuName": "高資產資格申請審核", "progClassname": "/wob/pbsOpenReview", "submitCnt": 0, "verifyCnt": 0, "reviewYn": "Y"}, {"menuName": "服務異動申請審核", "progClassname": "/wob/cusApplyReview", "submitCnt": 0, "verifyCnt": 0, "reviewYn": "Y"}, {"menuName": "客戶移轉分派審核", "progClassname": "/cus/cusTransferAudit", "submitCnt": 0, "verifyCnt": 6, "reviewYn": "Y"}], "sqlInfo": "WITH SQL6 AS ( SELECT AM.MENU_NAME, AP.PROG_CLASSNAME, COALESCE(PTP.CNT, 0) AS CNT, CASE WHEN EXISTS (SELECT 1 FROM ADM_ROLE_MENU_MAP WHERE ROLE_CODE = :roleCode  AND MENU_CODE=AM.MENU_CODE ) THEN 'Y' ELSE 'N' END REVIEW_YN FROM ADM_MENUS AM LEFT JOIN ADM_PROGS AP ON AM.PROG_CODE = AP.PROG_CODE LEFT JOIN (     SELECT :applyMenuCode1 AS MENU_CODE, '0' AS CNT FROM CUS_APPLY_MAIN C WITH (NOLOCK)     UNION     SELECT :serviceMenuCode AS MENU_CODE, COUNT(1) AS CNT FROM CUS_APPLY_MAIN WITH (NOLOCK)     WHERE FORM_STATUS='P' AND FORM_TYPE IN ('S', 'V')     AND CREATE_BY=:userCode     UNION     SELECT 'M14-03' AS MENU_CODE, COUNT(1) AS CNT FROM CUS_TRANSFER_DATA     WHERE STATUS='P'     AND CREATE_BY=:userCode ) PTP ON PTP.MENU_CODE = AM.MENU_CODE WHERE AM.MENU_CODE IN (:showMenuCode) ), SQL7 AS ( SELECT AM.MENU_NAME, AP.PROG_CLASSNAME, COALESCE(PTP.CNT, 0) AS CNT FROM ADM_MENUS AM LEFT JOIN ADM_PROGS AP ON AM.PROG_CODE = AP.PROG_CODE LEFT JOIN (     SELECT :applyMenuCode1 AS MENU_CODE, SUM(CNT) AS CNT     FROM (     SELECT :applyMenuCode1 AS MENU_CODE, COUNT(1) AS CNT FROM CUS_APPLY_MAIN CAM           LEFT JOIN WOB_SIGN_OFF_DETAIL WSOD ON CAM.CSOM_ID = WSOD.CSOM_ID           AND CAM.SIGN_SORT = WSOD.SIGN_SORT           AND WSOD.ROLE_CODE = :roleCode          AND CAM.CREATE_BRAN_CODE = :branCode LEFT JOIN (  SELECT     CPAM_ID,     ROLE_CODE AS FIRST_REV_ROLECODE,     ROW_NUMBER() OVER (PARTITION BY CPAM_ID ORDER BY CREATE_DT DESC) AS RN   FROM CUS_APPLY_LOG   WHERE SIGN_SORT = 1 ) CAL_FIRST   ON CAL_FIRST.CPAM_ID = CAM.CPAM_ID   AND CAL_FIRST.RN = 1 WHERE   CAM.FORM_TYPE IN ('A', 'R', 'W')   AND CAM.FORM_STATUS IN ('P', 'T')   AND WSOD.CSOD_ID IS NOT NULL   AND (    (CAM.CSOM_ID = 'CM0002'      AND CAM.SIGN_SORT = 3      AND SUBSTRING(CAL_FIRST.FIRST_REV_ROLECODE, LEN(CAL_FIRST.FIRST_REV_ROLECODE), 1)          > SUBSTRING(:roleCode, LEN(:roleCode), 1))     OR     (CAM.CSOM_ID != 'CM0002' OR CAM.SIGN_SORT != 3)     AND (      WSOD.JOBITEM_CODE IS NULL       OR WSOD.JOBITEM_CODE = ''       OR CHARINDEX(          SUBSTRING(WSOD.JOBITEM_CODE, 0, CHARINDEX(',', WSOD.JOBITEM_CODE)),           COALESCE(:jobItemCode, '')         ) <> 0       OR CHARINDEX(          SUBSTRING(WSOD.JOBITEM_CODE, CHARINDEX(',', WSOD.JOBITEM_CODE) + 1, LEN(WSOD.JOBITEM_CODE)),           COALESCE(:jobItemCode, '')         ) <> 0     )  )       ) AS COMBINED     UNION     SELECT :serviceMenuCode AS MENU_CODE, COUNT(1) AS CNT FROM CUS_APPLY_MAIN CAM WITH (NOLOCK)     INNER JOIN WOB_SIGN_OFF_DETAIL WSOD ON CAM.CSOM_ID=WSOD.CSOM_ID     AND CAM.SIGN_SORT = WSOD.SIGN_SORT AND WSOD.ROLE_CODE = :roleCode     WHERE CAM.FORM_STATUS='P'  AND CAM.FORM_TYPE IN ('S', 'V')     UNION     SELECT 'M14-03' AS MENU_CODE, COUNT(1) AS CNT FROM CUS_TRANSFER_DATA     WHERE STATUS='P' ) PTP ON PTP.MENU_CODE = AM.MENU_CODE  LEFT JOIN ADM_ROLE_MENU_MAP ARMM ON PTP.MENU_CODE = ARMM.MENU_CODE  AND ARMM.ROLE_CODE = :roleCode WHERE AM.MENU_CODE IN (:showMenuCode) ) SELECT SQL6.MENU_NAME,         SQL6.PROG_CLASSNAME,         CASE             WHEN SQL6.REVIEW_YN = 'N' THEN 0             ELSE SQL6.CNT             END AS SUBMIT_CNT,         CASE             WHEN SQL6.REVIEW_YN = 'N' THEN 0             ELSE SQL7.CNT             END AS VERIFY_CNT,         SQL6.REVIEW_YN  FROM SQL6           JOIN SQL7 ON SQL6.MENU_NAME = SQL7.MENU_NAME           ORDER BY              CASE SQL6.MENU_NAME                  WHEN '服務異動申請審核' THEN 1                  WHEN '客戶移轉分派審核' THEN 2                  ELSE 0              END ,class com.fcb.pbs.gen.web.model.HomepageReviewCntResp,{jobItemCode=H03, roleCode=IT01, branCode=H079050000, serviceMenuCode=M14-04, showMenuCode=[M14-00, M14-03, M14-04], applyMenuCode1=M14-00, userCode=00202304}"}]}