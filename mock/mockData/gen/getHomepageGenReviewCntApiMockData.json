{"status": 200, "data": [{"menuName": "事件通知審核", "progClassname": "/wob/verifyWobTdRecs", "submitCnt": 0, "verifyCnt": 0, "reviewYn": "Y"}, {"menuName": "事件通知狀態設定審核", "progClassname": "/wob/taskWOBVerify", "submitCnt": 0, "verifyCnt": 8, "reviewYn": "Y"}], "timestamp": "2025/07/23", "sqlTracer": [{"data": [{"menuName": "事件通知審核", "progClassname": "/wob/verifyWobTdRecs", "submitCnt": 0, "verifyCnt": 0, "reviewYn": "Y"}, {"menuName": "事件通知狀態設定審核", "progClassname": "/wob/taskWOBVerify", "submitCnt": 0, "verifyCnt": 8, "reviewYn": "Y"}], "sqlInfo": "WITH SQL3 AS( SELECT AM.MENU_NAME, AP.PROG_CLASSNAME, COALESCE(PTP.CNT, 0) AS CNT, CASE WHEN EXISTS (SELECT 1 FROM ADM_ROLE_MENU_MAP WHERE ROLE_CODE = :roleCode  AND MENU_CODE=AM.MENU_CODE ) THEN 'Y' ELSE 'N' END REVIEW_YN FROM ADM_MENUS AM LEFT JOIN ADM_PROGS AP ON AM.PROG_CODE = AP.PROG_CODE LEFT JOIN (    SELECT 'M13-00' AS MENU_CODE, SUM(REMAIN_CNT) AS CNT FROM (       SELECT COUNT(1) AS REMAIN_CNT       FROM WOB_TD_LISTS WTL        INNER JOIN WOB_TD_LIST_LOGS WTLL ON WTL.TD_CODE=WTLL.TD_CODE       INNER JOIN WOB_TD_ITEMS WTI ON WTLL.ITEM_CODE = WTI.ITEM_CODE       WHERE LOGS_VERIFY_STATUS_CODE = 'P'       AND WTI.VERIFY_YN = 'Y' AND WTI.ACTIVE_YN='Y'       AND WTL.USER_CODE= :userCode       UNION ALL       SELECT COUNT(1) AS REMAIN_CNT       FROM WOB_TD_LISTS_HIS WTLH        INNER JOIN WOB_TD_LIST_LOGS_HIS WTLLH ON WTLH.TD_CODE=WTLLH.TD_CODE       INNER JOIN WOB_TD_ITEMS WTI ON WTLLH.ITEM_CODE = WTI.ITEM_CODE       WHERE LOGS_VERIFY_STATUS_CODE = 'P'        AND WTI.VERIFY_YN = 'Y' AND WTI.ACTIVE_YN='Y'       AND WTLH.USER_CODE= :userCode     ) A     UNION     SELECT  'M13-01' AS MENU_CODE, COUNT(1) AS CNT FROM WOB_TD_ITEMS_LOG     WHERE STATUS='P' AND CREATE_BY= :userCode ) PTP ON PTP.MENU_CODE = AM.MENU_CODE WHERE AM.MENU_CODE IN ('M13-00', 'M13-01') ), SQL4 as ( SELECT AM.MENU_NAME, AP.PROG_CLASSNAME, COALESCE(PTP.CNT, 0) AS CNT FROM ADM_MENUS AM LEFT JOIN ADM_PROGS AP ON AM.PROG_CODE = AP.PROG_CODE LEFT JOIN (      SELECT 'M13-00' AS MENU_CODE, SUM(REMAIN_CNT) AS CNT FROM (         SELECT COUNT(1) AS REMAIN_CNT         FROM WOB_TD_LISTS WTL          INNER JOIN WOB_TD_LIST_LOGS WTLL ON WTL.TD_CODE=WTLL.TD_CODE         INNER JOIN WOB_TD_ITEMS WTI ON WTLL.ITEM_CODE = WTI.ITEM_CODE        WHERE LOGS_VERIFY_STATUS_CODE = 'P'        AND WTI.VERIFY_YN = 'Y' AND WTI.ACTIVE_YN='Y'        AND ( WTLL.CREATE_BRAN_CODE = :branCode        OR :roleCode IN ('ADM01', 'IT01') )        UNION ALL        SELECT COUNT(1) AS REMAIN_CNT        FROM WOB_TD_LISTS_HIS WTLH         INNER JOIN WOB_TD_LIST_LOGS_HIS WTLLH ON WTLH.TD_CODE=WTLLH.TD_CODE        INNER JOIN WOB_TD_ITEMS WTI ON WTLLH.ITEM_CODE = WTI.ITEM_CODE        WHERE LOGS_VERIFY_STATUS_CODE = 'P'         AND WTI.VERIFY_YN = 'Y' AND WTI.ACTIVE_YN='Y'        AND ( WTLLH.CREATE_BRAN_CODE =:branCode        OR :roleCode IN ('ADM01', 'IT01') )     ) A     UNION     SELECT 'M13-01' AS MENU_CODE, COUNT(1) AS CNT FROM WOB_TD_ITEMS_LOG     WHERE STATUS='P' ) PTP ON PTP.MENU_CODE = AM.MENU_CODE  LEFT JOIN ADM_ROLE_MENU_MAP ARMM ON PTP.MENU_CODE = ARMM.MENU_CODE AND ARMM.ROLE_CODE = :roleCode WHERE AM.MENU_CODE IN ('M13-00', 'M13-01') ) SELECT SQL3.MENU_NAME,         SQL3.PROG_CLASSNAME,         CASE             WHEN SQL3.REVIEW_YN = 'N' THEN 0             ELSE SQL3.CNT             END AS SUBMIT_CNT,         CASE             WHEN SQL3.REVIEW_YN = 'N' THEN 0             ELSE SQL4.CNT             END AS VERIFY_CNT,         SQL3.REVIEW_YN  FROM SQL3           JOIN SQL4 ON SQL3.MENU_NAME = SQL4.MENU_NAME  ,class com.fcb.pbs.gen.web.model.HomepageReviewCntResp,{branCode=H079050000, roleCode=IT01, userCode=00202304}"}]}