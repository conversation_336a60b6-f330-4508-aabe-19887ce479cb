{"status": 200, "data": [], "timestamp": "2025/07/09", "sqlTracer": [{"data": [], "sqlInfo": " SELECT *, WTC.TDCAT1_NAME , WTI.ITEM_NAME , COUNT(*) OVER () AS TOTAL_COUNT  FROM WOB_TD_LISTS WTL  LEFT JOIN WOB_TDITEM_CAT1 WTC ON WTC.TDCAT1_CODE = WTL.TDCAT1_CODE  LEFT JOIN WOB_TD_ITEMS WTI ON WTI.ITEM_CODE = WTL.ITEM_CODE  WHERE WTL.CREATE_DT > DATEADD(MONTH, -2, GETDATE())  AND WTL.CUS_CODE = :cusCode ,class com.bi.pbs.cus.web.model.ClientTdListsResp,{cusCode=00956829}"}]}