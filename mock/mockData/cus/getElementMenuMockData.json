{"status": 200, "data": {"basic": [{"elementId": "text", "elementName": "文字元件", "elementType": "basic", "componentName": "textEditor", "activeYn": "Y"}], "overview": [{"elementId": "ov-asset", "elementName": "資產與負債", "elementType": "overview", "componentName": "", "activeYn": "Y"}, {"elementId": "ov-need", "elementName": "投資需求", "elementType": "overview", "componentName": "", "activeYn": "Y"}, {"elementId": "ov-growth", "elementName": "財富增長分析", "elementType": "overview", "componentName": "assetGrowthAnalysis", "activeYn": "Y"}, {"elementId": "ov-move", "elementName": "資產變動狀況", "elementType": "overview", "componentName": "assetChange", "activeYn": "Y"}, {"elementId": "ov-pl", "elementName": "已實現損益", "elementType": "overview", "componentName": "realizedProfitLoss", "activeYn": "Y"}, {"elementId": "ov-nav", "elementName": "資產分類", "elementType": "overview", "componentName": "assetCategory", "activeYn": "Y"}, {"elementId": "ov-liability", "elementName": "資產與負債往來變化", "elementType": "overview", "componentName": "assetLoanChange", "activeYn": "Y"}, {"elementId": "ov-summary", "elementName": "資產總覽", "elementType": "overview", "componentName": "", "activeYn": "Y"}], "trans": [{"elementId": "trade-search", "elementName": "交易紀錄查詢", "elementType": "trans", "componentName": "", "activeYn": "Y"}], "plan": [{"elementId": "pie", "elementName": "資產類別餅圖", "elementType": "plan", "componentName": "assetTypePie", "activeYn": "Y"}, {"elementId": "plan-trade", "elementName": "交易紀錄查詢", "elementType": "plan", "componentName": "", "activeYn": "Y"}], "performance": [{"elementId": "perf-nav", "elementName": "淨值分析", "elementType": "performance", "componentName": "netValAnalysis", "activeYn": "Y"}, {"elementId": "perf-pl", "elementName": "損益分析", "elementType": "performance", "componentName": "invPlAnalysis", "activeYn": "Y"}, {"elementId": "perf-growth", "elementName": "財富增長分析", "elementType": "performance", "componentName": "assetGrowthAnalysis", "activeYn": "Y"}, {"elementId": "perf-portfolio", "elementName": "投資標的分析", "elementType": "performance", "componentName": "invTarget", "activeYn": "Y"}, {"elementId": "amount", "elementName": "交易金額分析", "elementType": "performance", "componentName": "invTxAmt", "activeYn": "Y"}, {"elementId": "realized", "elementName": "實現損益查詢", "elementType": "performance", "componentName": "realizeProfitSrch", "activeYn": "Y"}]}, "timestamp": "2025/08/20", "sqlTracer": [{"data": [{"elementId": "text", "elementName": "文字元件", "elementType": "basic", "componentName": "textEditor", "activeYn": "Y"}, {"elementId": "ov-asset", "elementName": "資產與負債", "elementType": "overview", "componentName": "", "activeYn": "Y"}, {"elementId": "ov-need", "elementName": "投資需求", "elementType": "overview", "componentName": "", "activeYn": "Y"}, {"elementId": "ov-growth", "elementName": "財富增長分析", "elementType": "overview", "componentName": "assetGrowthAnalysis", "activeYn": "Y"}, {"elementId": "ov-move", "elementName": "資產變動狀況", "elementType": "overview", "componentName": "assetChange", "activeYn": "Y"}, {"elementId": "ov-pl", "elementName": "已實現損益", "elementType": "overview", "componentName": "realizedProfitLoss", "activeYn": "Y"}, {"elementId": "ov-nav", "elementName": "資產分類", "elementType": "overview", "componentName": "assetCategory", "activeYn": "Y"}, {"elementId": "ov-liability", "elementName": "資產與負債往來變化", "elementType": "overview", "componentName": "assetLoanChange", "activeYn": "Y"}, {"elementId": "ov-summary", "elementName": "資產總覽", "elementType": "overview", "componentName": "", "activeYn": "Y"}, {"elementId": "trade-search", "elementName": "交易紀錄查詢", "elementType": "trans", "componentName": "", "activeYn": "Y"}, {"elementId": "pie", "elementName": "資產類別餅圖", "elementType": "plan", "componentName": "assetTypePie", "activeYn": "Y"}, {"elementId": "plan-trade", "elementName": "交易紀錄查詢", "elementType": "plan", "componentName": "", "activeYn": "Y"}, {"elementId": "perf-nav", "elementName": "淨值分析", "elementType": "performance", "componentName": "netValAnalysis", "activeYn": "Y"}, {"elementId": "perf-pl", "elementName": "損益分析", "elementType": "performance", "componentName": "invPlAnalysis", "activeYn": "Y"}, {"elementId": "perf-growth", "elementName": "財富增長分析", "elementType": "performance", "componentName": "assetGrowthAnalysis", "activeYn": "Y"}, {"elementId": "perf-portfolio", "elementName": "投資標的分析", "elementType": "performance", "componentName": "invTarget", "activeYn": "Y"}, {"elementId": "amount", "elementName": "交易金額分析", "elementType": "performance", "componentName": "", "activeYn": "Y"}, {"elementId": "realized", "elementName": "實現損益查詢", "elementType": "performance", "componentName": "", "activeYn": "Y"}], "sqlInfo": "SELECT     ELEMENT_ID,    ELEMENT_NAME,    ELEMENT_TYPE,     COMPONENT_NAME,     ACTIVE_YN FROM CUS_ASSET_REPORT_ELEMENT_MENU WHERE ACTIVE_YN = 'Y' ,class com.bi.frf.cus.assetReport.model.CusAssetReportElementMenu,[Ljava.lang.Object;@2ba98039"}]}