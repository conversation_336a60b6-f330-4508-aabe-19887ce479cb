{"status": 200, "data": {"wealthAnalysis": [{"dataDt": "2025-01-05 00:00:00", "dataYm": "202501", "invAmtLc": 1479532, "totRplLc": -58536, "totDividendLc": 0, "totDiffAmtLc": -3498, "mktAmtLc": 1528273, "twrLc": 0.29}, {"dataDt": "2025-02-05 00:00:00", "dataYm": "202502", "invAmtLc": 707087, "totRplLc": -30838, "totDividendLc": 0, "totDiffAmtLc": -781, "mktAmtLc": 746512, "twrLc": -51.04}, {"dataDt": "2025-03-05 00:00:00", "dataYm": "202503", "invAmtLc": 679793, "totRplLc": 38516, "totDividendLc": 0, "totDiffAmtLc": 4098, "mktAmtLc": 725609, "twrLc": -3.23}, {"dataDt": "2025-05-05 00:00:00", "dataYm": "202504", "invAmtLc": 974259, "totRplLc": -8143, "totDividendLc": 0, "totDiffAmtLc": -3757, "mktAmtLc": 1033949, "twrLc": 43.13}, {"dataDt": "2025-05-05 00:00:00", "dataYm": "202505", "invAmtLc": 790639, "totRplLc": -76191, "totDividendLc": 0, "totDiffAmtLc": 3262, "mktAmtLc": 758895, "twrLc": -26.74}, {"dataDt": "2025-06-05 00:00:00", "dataYm": "202506", "invAmtLc": 983903, "totRplLc": -16139, "totDividendLc": 0, "totDiffAmtLc": -26, "mktAmtLc": 997565, "twrLc": 31.69}, {"dataDt": "2025-07-05 00:00:00", "dataYm": "202507", "invAmtLc": 1269348, "totRplLc": 23242, "totDividendLc": 0, "totDiffAmtLc": 346, "mktAmtLc": 1310227, "twrLc": 2.03}, {"dataDt": "2025-07-05 00:00:00", "dataYm": "202508", "invAmtLc": 1256335, "totRplLc": 6682, "totDividendLc": 0, "totDiffAmtLc": 5585, "mktAmtLc": 1285633, "twrLc": 28.44}], "wealthAnalysisMonthly": [{"dataYm": "202508", "dailyReturn": -12.47}, {"dataYm": "202507", "dailyReturn": -11.81}, {"dataYm": "202506", "dailyReturn": -22.29}, {"dataYm": "202505", "dailyReturn": -30.56}, {"dataYm": "202504", "dailyReturn": -16.77}, {"dataYm": "202503", "dailyReturn": -28.53}, {"dataYm": "202502", "dailyReturn": -29.12}, {"dataYm": "202501", "dailyReturn": -1.55}]}, "timestamp": "2025/08/19", "sqlTracer": [{"data": [{"dataDt": "2025-01-05 00:00:00", "dataYm": "202501", "invAmtLc": 1479532, "totRplLc": -58536, "totDividendLc": 0, "totDiffAmtLc": -3498, "mktAmtLc": 1528273, "twrLc": 0.29}, {"dataDt": "2025-02-05 00:00:00", "dataYm": "202502", "invAmtLc": 707087, "totRplLc": -30838, "totDividendLc": 0, "totDiffAmtLc": -781, "mktAmtLc": 746512, "twrLc": -51.04}, {"dataDt": "2025-03-05 00:00:00", "dataYm": "202503", "invAmtLc": 679793, "totRplLc": 38516, "totDividendLc": 0, "totDiffAmtLc": 4098, "mktAmtLc": 725609, "twrLc": -3.23}, {"dataDt": "2025-05-05 00:00:00", "dataYm": "202504", "invAmtLc": 974259, "totRplLc": -8143, "totDividendLc": 0, "totDiffAmtLc": -3757, "mktAmtLc": 1033949, "twrLc": 43.13}, {"dataDt": "2025-05-05 00:00:00", "dataYm": "202505", "invAmtLc": 790639, "totRplLc": -76191, "totDividendLc": 0, "totDiffAmtLc": 3262, "mktAmtLc": 758895, "twrLc": -26.74}, {"dataDt": "2025-06-05 00:00:00", "dataYm": "202506", "invAmtLc": 983903, "totRplLc": -16139, "totDividendLc": 0, "totDiffAmtLc": -26, "mktAmtLc": 997565, "twrLc": 31.69}, {"dataDt": "2025-07-05 00:00:00", "dataYm": "202507", "invAmtLc": 1269348, "totRplLc": 23242, "totDividendLc": 0, "totDiffAmtLc": 346, "mktAmtLc": 1310227, "twrLc": 2.03}, {"dataDt": "2025-07-05 00:00:00", "dataYm": "202508", "invAmtLc": 1256335, "totRplLc": 6682, "totDividendLc": 0, "totDiffAmtLc": 5585, "mktAmtLc": 1285633, "twrLc": 28.44}], "sqlInfo": "SELECT CAM.DATA_DT   , CAM.DATA_YM   , ROUND(CAM.INV_AMT_LC , 0) INV_AMT_LC   , ROUND(COALESCE(CSM.TOT_RPL_LC, 0), 0) TOT_RPL_LC   , ROUND(CAM.MKT_AMT_LC + CAM.TOT_DIVIDEND_LC - CAM.INV_AMT_LC, 0) TOT_UPL_LC   , ROUND(COALESCE(CAM.TOT_DIVIDEND_LC, 0), 0) TOT_DIVIDEND_LC   , ROUND(COALESCE(CSM.TOT_BUY_AMT_LC, 0) - COALESCE(CSM.TOT_SELL_AMT_LC, 0),0) TOT_DIFF_AMT_LC   , ROUND(CAM.MKT_AMT_LC, 0) MKT_AMT_LC   , COALESCE((CAM.MKT_AMT_LC - COALESCE(CSM.TOT_BUY_AMT_LC, 0) + COALESCE(CSM.TOT_DIV_AMT_LC, 0) + COALESCE(CSM.TOT_SELL_AMT_LC, 0)) / NULLIF(COALESCE(LAG(CAM.MKT_AMT_LC)OVER(ORDER BY CAM.DATA_DT), CAM.MKT_AMT_LC), 0) - 1, 0) * 100 TWR_LC FROM ( SELECT \tDATA_YM, DATA_YY, DATA_MM, DATA_DT, SUM(MKT_AMT_LC) MKT_AMT_LC, SUM(INV_AMT_LC) INV_AMT_LC, SUM(TOT_DIVIDEND_LC) TOT_DIVIDEND_LC FROM CUS_AUAM_MONTH WHERE CUS_CODE IN (:cusCodes) AND COUNT_TYPE = 'MED' AND AU_TYPE = 'AUM' GROUP BY DATA_YM, DATA_YY,DATA_MM,DATA_DT ORDER BY DATA_YY DESC,DATA_MM DESC LIMIT 13) CAM LEFT JOIN (SELECT  DATA_YM  , SUM(TOT_RPL_LC)      TOT_RPL_LC  , SUM(TOT_DIVIDEND_LC) TOT_DIVIDEND_LC  , SUM(TOT_BUY_AMT_LC)  TOT_BUY_AMT_LC  , SUM(TOT_SELL_INVAMT_LC) TOT_SELL_AMT_LC  , SUM(TOT_DIVIDEND_LC) TOT_DIV_AMT_LC  FROM CUS_ASSETSUMAMT_MONTH CAM  JOIN PRO_PFCATS PF ON CAM.PFCAT_CODE = PF.PFCAT_CODE  AND PF.AU_TYPE = 'AUM'  WHERE CUS_CODE IN (:cusCodes)  GROUP BY DATA_YM  ORDER BY DATA_YM DESC LIMIT 12) CSM ON CAM.DATA_YM = CSM.DATA_YM ORDER BY CAM.DATA_YY ,CAM.DATA_MM ,class com.bi.frf.cus.model.CusInvWealthAnalysis,{cusCodes=[K100000001]}"}, {"data": [{"dataYm": "202508", "dailyReturn": -12.47}, {"dataYm": "202507", "dailyReturn": -11.81}, {"dataYm": "202506", "dailyReturn": -22.29}, {"dataYm": "202505", "dailyReturn": -30.56}, {"dataYm": "202504", "dailyReturn": -16.77}, {"dataYm": "202503", "dailyReturn": -28.53}, {"dataYm": "202502", "dailyReturn": -29.12}, {"dataYm": "202501", "dailyReturn": -1.55}], "sqlInfo": "WITH TMP_TWR AS (   SELECT DATA_YM,          COALESCE((X.MKT_AMT_LC - COALESCE(X.TOT_BUY_AMT_LC, 0) + COALESCE(X.TOT_DIV_AMT_LC, 0) + COALESCE(X.TOT_SELL_AMT_LC, 0)) / NULLIF(COALESCE(LAG(X.MKT_AMT_LC) OVER (ORDER BY X.DATA_YM), X.MKT_AMT_LC), 0) - 1, 0) AS DAILY_RETURN FROM (SELECT CAM.DATA_YM , ROUND(SUM(CAM.INV_AMT_LC), 0) INV_AMT_LC , ROUND(SUM(CAM.MKT_AMT_LC), 0) MKT_AMT_LC , ROUND(SUM(CAM.TOT_DIVIDEND_LC), 0) TOT_DIVIDEND_LC , ROUND(SUM(COALESCE(CSM.TOT_BUY_AMT_LC, 0)), 0) TOT_BUY_AMT_LC , ROUND(SUM(COALESCE(CSM.TOT_SELL_AMT_LC, 0)), 0) TOT_SELL_AMT_LC , ROUND(SUM(COALESCE(CSM.TOT_DIVIDEND_LC, 0)), 0) TOT_DIV_AMT_LC FROM CUS_AUAM_MONTH CAM LEFT JOIN (SELECT DATA_YM , SUM(TOT_RPL_LC) TOT_RPL_LC , SUM(TOT_DIVIDEND_LC) TOT_DIVIDEND_LC , SUM(TOT_BUY_AMT_LC) TOT_BUY_AMT_LC , SUM(TOT_SELL_AMT_LC) TOT_SELL_AMT_LC , SUM(TOT_DIVIDEND_LC) TOT_DIV_AMT_LC FROM CUS_ASSETSUMAMT_MONTH CAM JOIN PRO_PFCATS PF ON CAM.PFCAT_CODE = PF.PFCAT_CODE AND PF.AU_TYPE = 'AUM' WHERE CUS_CODE IN (:cusCodes) GROUP BY DATA_YM ORDER BY DATA_YM DESC LIMIT 12) CSM ON CAM.DATA_YM = CSM.DATA_YM WHERE CAM.CUS_CODE IN (:cusCodes) AND CAM.COUNT_TYPE = 'MED' AND CAM.AU_TYPE = 'AUM' GROUP BY CAM.DATA_YM  LIMIT 12) X ) SELECT A.DATA_YM , (EXP(SUM(LOG(NULLIF(B.DAILY_RETURN + 1, 0)))) - 1) * 100 AS DAILY_RETURN FROM TMP_TWR A JOIN TMP_TWR B ON A.DATA_YM >= B.DATA_YM GROUP BY A.DATA_YM ORDER BY A.DATA_YM DESC ,class com.bi.frf.cus.model.CusInvWealthAnalysisMonthly,{cusCodes=[K100000001]}"}]}