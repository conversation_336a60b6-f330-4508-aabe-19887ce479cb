{"status": 200, "data": [{"code": "M20-0583", "name": "投資標的分析", "parentCode": "M20-058", "url": "vue-cus-invest-analysis-inv-target", "order": 4, "leafYn": "Y", "leaf": false}, {"code": "M20-0585", "name": "交易紀錄查詢", "parentCode": "M20-058", "url": "vue-cus-invest-analysis-inv-transaction", "order": 6, "leafYn": "Y", "leaf": false}, {"code": "M20-0586", "name": "實現損益查詢", "parentCode": "M20-058", "url": "vue-cus-invest-analysis-inv-realized", "order": 7, "leafYn": "Y", "leaf": false}, {"code": "M20-0587", "name": "投資績效分析/資產及報酬率分析", "parentCode": "M20-058", "url": "vue-cus-invest-analysis-inv-performance", "order": 8, "leafYn": "Y", "leaf": false}], "timestamp": "2025/07/13", "sqlTracer": [{"data": [{"code": "M20-0583", "name": "投資標的分析", "parentCode": "M20-058", "url": "vue-cus-invest-analysis-inv-target", "order": 4, "leafYn": "Y", "leaf": false}, {"code": "M20-0585", "name": "交易紀錄查詢", "parentCode": "M20-058", "url": "vue-cus-invest-analysis-inv-transaction", "order": 6, "leafYn": "Y", "leaf": false}, {"code": "M20-0586", "name": "實現損益查詢", "parentCode": "M20-058", "url": "vue-cus-invest-analysis-inv-realized", "order": 7, "leafYn": "Y", "leaf": false}, {"code": "M20-0587", "name": "投資績效分析/資產及報酬率分析", "parentCode": "M20-058", "url": "vue-cus-invest-analysis-inv-performance", "order": 8, "leafYn": "Y", "leaf": false}], "sqlInfo": " SELECT DISTINCT      M<PERSON><PERSON><PERSON>_CODE CODE,      <PERSON><PERSON>ME<PERSON>_NAME NAME,      M.PARENT_MENU_CODE PARENT_CODE,      M<PERSON>SHOW_ORDER 'ORDER',      M.MENU_ICON ICON,      <PERSON><PERSON>TAB_YN LEAF_YN,      AP.PROG_CLASSNAME URL  FROM ADM_MENUS M      JOIN ADM_ROLE_MENU_MAP ARMM ON ARMM.MENU_CODE = M.MENU_CODE      LEFT JOIN ADM_PROGS AP ON AP.PROG_CODE = M.PROG_CODE  WHERE ARMM.ROLE_CODE = :roleCode    AND M.ACTIVE_YN = 'Y'    AND M.PARENT_MENU_CODE = :parentMenuCode    AND M.TAB_YN = :tabYn ORDER BY M.SHOW_ORDER ASC, M.MENU_CODE ASC ,class com.bi.frame.menu.model.MenuTree,{roleCode=98, tabYn=Y, parentMenuCode=M20-058}"}]}