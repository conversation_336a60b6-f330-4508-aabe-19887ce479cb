{"status": 200, "data": [{"resultCode": "RC20250415000004", "resultName": "先帝創業未半，而中道", "createDt": "2025-04-15 14:22:06"}, {"resultCode": "RC20250415000002", "resultName": "先帝創業未半，而中道", "createDt": "2025-04-15 14:18:41"}, {"resultCode": "RC20250415000001", "resultName": "aaaaaaa000", "createDt": "2025-04-15 14:16:20"}, {"resultCode": "RC20250311000001", "resultName": "kkk", "createDt": "2025-03-11 16:49:39"}, {"resultCode": "RC20250211000001", "resultName": "媽啊", "createDt": "2025-02-11 15:42:45"}], "timestamp": "2025/07/09", "sqlTracer": [{"data": [{"resultCode": "RC20250415000004", "resultName": "先帝創業未半，而中道", "createDt": "2025-04-15 14:22:06"}, {"resultCode": "RC20250415000002", "resultName": "先帝創業未半，而中道", "createDt": "2025-04-15 14:18:41"}, {"resultCode": "RC20250415000001", "resultName": "aaaaaaa000", "createDt": "2025-04-15 14:16:20"}, {"resultCode": "RC20250311000001", "resultName": "kkk", "createDt": "2025-03-11 16:49:39"}, {"resultCode": "RC20250211000001", "resultName": "媽啊", "createDt": "2025-02-11 15:42:45"}], "sqlInfo": "SELECT RESULT_CODE   , RESULT_NAME   , CREATE_DT  FROM CUS_SEARCH_RESULT_LISTS CSRL  WHERE 1 = 1  AND USER_CODE = :userCode  GROUP BY RESULT_CODE, RESULT_NAME, CREATE_DT  ORDER BY CREATE_DT DESC ,class com.bi.pbs.cus.web.model.CusSearchResultResp,{userCode=112790}"}]}