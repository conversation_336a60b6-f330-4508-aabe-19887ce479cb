{"status": 200, "data": {"cusName": "臺00", "cusEname": " ", "birth": "1964-12-15", "businessLocation": "台北市", "ocuDetailCode": "028530", "orgtypeCode": "0", "employer": " ", "ownerIdn": " ", "income": "0", "cusBu": "D", "buName": "DBU", "graCode": "F", "graName": "一般戶", "legalRpstName": " ", "legalRpstPhone": " ", "branCode": "", "phoneH": "********", "email": " ", "idnEntityType": "L", "idn": "********", "age": 60, "ocuCode": "30000", "maritalCode": "0", "maritalName": "未婚", "warnYnwarnedHouseholdsYn": "N", "mktPhoneYn": "N", "legalRpstPhone2": "********", "phoneC": "********", "gender": "C", "eduCode": "0", "jobTitle": "0", "employeesYn": "N", "watchListedAccountYn": "N", "addrFullH": "台北市北投區永欣里14鄰模00", "addrFullC": " ", "bkPhoneNote": " ", "bkEmailNote": " ", "bkSmsNote": " ", "bkDmNote": " ", "aum": 0, "relYn": "N", "cardYn": "N", "branName": "石牌分行      "}, "timestamp": "2025/07/13", "sqlTracer": [{"data": {"cusName": "臺00", "cusEname": " ", "birth": "1964-12-15", "businessLocation": "台北市", "ocuDetailCode": "028530", "orgtypeCode": "0", "employer": " ", "ownerIdn": " ", "income": "0", "cusBu": "D", "buName": "DBU", "graCode": "F", "graName": "一般戶", "legalRpstName": " ", "legalRpstPhone": " ", "branCode": "", "phoneH": "********", "email": " ", "idnEntityType": "L", "idn": "********", "age": 60, "ocuCode": "30000", "maritalCode": "0", "maritalName": "未婚", "warnYnwarnedHouseholdsYn": "N", "mktPhoneYn": "N", "legalRpstPhone2": "********", "phoneC": "********", "gender": "C", "eduCode": "0", "jobTitle": "0", "employeesYn": "N", "watchListedAccountYn": "N", "addrFullH": "台北市北投區永欣里14鄰模00", "addrFullC": " ", "bkPhoneNote": " ", "bkEmailNote": " ", "bkSmsNote": " ", "bkDmNote": " ", "aum": 0, "relYn": "N", "cardYn": "N", "branName": "石牌分行      "}, "sqlInfo": "SELECT CUS.CUS_NAME      , CUS.CUS_ENAME      , CUS.BIRTH      , CUS.BUSINESS_LOCATION      , CUS.INI_OPEN_DT      , CUS.OCU_DETAIL_CODE      , CO.OCU_NAME      , CUS.ORGTYPE_CODE      , ACD_ORG.CODE_NAME                       AS ORGTYPE_NAME      , CUS.SME_YN      , CUS.OWNER_IDN      , CUS.OWNER_NAME      , CUS.EMPLOYER      , CUS.INCOME      , CUS.CUS_BU      , ACD_BU.CODE_NAME                        AS BU_NAME      , CUS.GRA_CODE      , CG.GRA_NAME                             AS GRA_NAME      , CUS.COMP_YN      , CUS.LEGAL_RPST_NAME      , CUS.LEGAL_RPST_PHONE      , CUS.BRAN_CODE      , CCI.PHONE_1                             AS PHONE_H      , CCI.EMAIL      , CUS.IDN_ENTITY_TYPE      , CUS.IDN      , CUS.AGE      , CUS.OCU_CODE      , CUS.MARITAL_CODE      , ACD_MARITAL.CODE_NAME                   AS MARITAL_NAME      , CUS.INI_OPEN_DT      , CUS.WARN_YNWARNED_HOUSEHOLDS_YN      , CUS.MKT_PHONE_YN      , CUS.LEGAL_RPST_NAME2      , CUS.LEGAL_RPST_PHONE2      , CUS.USER_CODE      , CCI.PHONE_2                             AS PHONE_C      , CUS.GENDER      , ACD_GENDER.CODE_NAME                    AS GENDER_NAME      , CUS.EDU_CODE      , ACD_EDU.CODE_NAME                       AS EDU_NAME      , CUS.JOB_TITLE      , CJT.TITLE_NAME      , CUS.PER_SECU_LAW_SIGN_YN      , CUS.PER_SECU_LAW_SIGN_DT      , CUS.EMPLOYEES_YN      , CUS.WATCH_LISTED_ACCOUNT_YN      , CUS.PRIORITY_GETNUM_YN      , CUS.KYC_RISK_YN      , CUS.OPEN_ONLINE_YN      , CCI.CELLPHONE                           AS CELLPHONE_M      , CAH.ADDR_FULL                           AS ADDR_FULL_H      , CAC.ADDR_FULL                           AS ADDR_FULL_C      , CUS.BK_PHONE_NOTE      , CUS.BK_EMAIL_NOTE      , CUS.BK_SMS_NOTE      , CUS.BK_DM_NOTE      , CUS.AUM      , CKC.RANK_CODE      , CR.RANK_NAME      , CFI.RECOMM_STATUS      , CFI.RECOMM_SIGN_DT      , CFI.PI_LEGAL_TYPE      , CFI.PI_SIGN_DT      , IIF(REL.CUS_CODE IS NOT NULL, 'Y', 'N') AS REL_YN      , CKC.RANK_QUE_DT      , CUS.INV_BOND_NOTI_YN      , CKC.NEXT_RANK_QUE_DT      , CUS.CHILD_INV_YN      , CUS.DPI_FLAG      , CUS.CARD_YN      , AB.BRAN_NAME      , AU.USER_NAME      , CS.AUA_1M FROM CUSTOMERS CUS          LEFT JOIN CUS_FORM_INFO CFI ON CUS.CUS_CODE = CFI.CUS_CODE          LEFT JOIN CUS_KYC_CURRENT CKC ON CUS.CUS_CODE = CKC.CUS_CODE          LEFT JOIN CUS_CONTACT_INFO CCI ON CUS.CUS_CODE = CCI.CUS_CODE          LEFT JOIN CUS_ADDRESS CAH ON CUS.CUS_CODE = CAH.CUS_CODE AND CAH.ADDR_TYPE = 'H'          LEFT JOIN CUS_ADDRESS CAC ON CUS.CUS_CODE = CAC.CUS_CODE AND CAC.ADDR_TYPE = 'C'          LEFT JOIN (SELECT TOP 1 CRM.CUS_CODE                     FROM CUS_RELSHIP_MAP CRM                              JOIN CUS_RELATIONSHIPS CR ON CRM.RELSHIP_ID = CR.RELSHIP_ID                     WHERE CR.APPLYTYPE_CODE = 'RS'                       AND CRM.CUS_CODE = :cusCode) REL ON CUS.CUS_CODE = REL.CUS_CODE          LEFT JOIN CUS_OCCUPATIONS CO ON CO.OCU_CODE = CUS.OCU_CODE          LEFT JOIN ADM_CODE_DETAIL ACD_GENDER ON ACD_GENDER.CODE_TYPE = 'GENDER' AND ACD_GENDER.CODE_VALUE = CUS.GENDER          LEFT JOIN ADM_CODE_DETAIL ACD_EDU ON ACD_EDU.CODE_TYPE = 'EDU_CODE' AND ACD_EDU.CODE_VALUE = CUS.EDU_CODE          LEFT JOIN CUS_JOB_TITLE CJT ON CJT.TITLE_ID = CUS.JOB_TITLE          LEFT JOIN ADM_CODE_DETAIL ACD_MARITAL ON ACD_MARITAL.CODE_TYPE = 'MARITAL_CODE' AND ACD_MARITAL.CODE_VALUE = CUS.MARITAL_CODE          LEFT JOIN ADM_CODE_DETAIL ACD_BU ON ACD_BU.CODE_TYPE = 'CUS_BU' AND ACD_BU.CODE_VALUE = CUS.CUS_BU          LEFT JOIN CUS_GRADES CG ON CG.GRA_CODE = CUS.GRA_CODE          LEFT JOIN CUS_AO_INFO CAI ON CAI.CUS_CODE = CUS.CUS_CODE          LEFT JOIN ADM_BRANCHES AB ON AB.BRAN_CODE = CAI.BRAN_CODE          LEFT JOIN ADM_USERS AU ON AU.USER_CODE = CAI.USER_CODE          LEFT JOIN CUS_SUMMARIES CS ON CS.CUS_CODE = CUS.CUS_CODE          LEFT JOIN ADM_CODE_DETAIL ACD_ORG ON ACD_ORG.CODE_TYPE = 'ORGTYPE_CODE' AND ACD_ORG.CODE_VALUE = CUS.ORGTYPE_CODE          LEFT JOIN CUS_RANKS CR ON CR.RANK_CODE = CUS.RANK_CODE WHERE CUS.CUS_CODE = :cusCode,class com.bi.pbs.cus.web.model.CustomerInfoResp,{cusCode=********}"}, {"data": [], "sqlInfo": " SELECT CASE WHEN ANS_TEXT IS NULL OR ANS_TEXT=''  THEN QUEITEMSEL_NAME  ELSE QUEITEMSEL_NAME+ ' '+ANS_TEXT END QUEITEMSEL_NAME  FROM CUS_EXTDATA_ANSSELS CEA  INNER JOIN CUS_EXTDATA_QUEITEMSELS CEQ ON CEQ.QUEITEMSEL_ID = CEA.EXTDATA_QUEITEMSEL_ID  WHERE CEQ.QUEITEM_ID IN  ( SELECT QUEITEM_ID  FROM CUS_EXTDATA_QUEITEMS  WHERE CUS_EXTDATA_QUEITEMS.QUEITEM_NAME = '偏好的聯絡方式'  ) AND CEA.CUS_CODE = :cusCode ,class com.bi.pbs.cus.web.model.CusOtherDetailResp,{cusCode=********}"}]}