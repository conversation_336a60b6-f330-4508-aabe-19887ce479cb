{"data": "", "status": 200, "statusText": "", "headers": {"access-control-allow-headers": "x-requested-with, Content-Type, Authorization, credential, X-XSRF-TOKEN", "access-control-allow-methods": "POST, GET, OPTIONS, DELETE, PATCH", "access-control-allow-origin": "*", "access-control-expose-headers": "*", "access-control-max-age": "3600", "authorization": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyIjp7InRlbmFudElkIjpudWxsLCJ1c2VyQ29kZSI6InVzZXIiLCJzeXN0ZW1Sb2xlIjoiQURNSU4ifSwiaWF0IjoxNzQ0MzU2NzI1LCJleHAiOjE3NDQzNTc2MjV9.XwTXWzG7r45JnhJf609nAhSsKKJLq2aZcgRyyVxFmxEp0gvvuz9BLdo6EiMpN0d0dxSQZY4UfrFGda-TgRQiZQ", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "connection": "keep-alive", "date": "Fri, 11 Apr 2025 07:32:05 GMT", "expires": "0", "keep-alive": "timeout=20", "pragma": "no-cache", "transfer-encoding": "chunked", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-xss-protection": "0"}, "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyIjp7InRlbmFudElkIjpudWxsLCJ1c2VyQ29kZSI6InVzZXIiLCJzeXN0ZW1Sb2xlIjoiQURNSU4ifSwiaWF0IjoxNzQ0MzU2NzIxLCJleHAiOjE3NDQzNTc2MjF9.jrBm0Vw8tyCJEfmqcVVMvK-a0ryrAFXL7sZ4Z8N3Jhb-M8kIkKNUBuLAvPoGvUFlQf2A26oJKRUd2p7uj3LA5w"}, "method": "post", "url": "http://192.168.0.180:8081/pbs-api/api/v1/token", "data": "{\"deputyUser\":\"王大明\",\"roleCode\":\"ADMIN\"}", "allowAbsoluteUrls": true}, "request": {}}