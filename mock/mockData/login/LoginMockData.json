{"data": {"accessToken": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyIjp7InRlbmFudElkIjpudWxsLCJ1c2VyQ29kZSI6InVzZXIiLCJzeXN0ZW1Sb2xlIjoiQURNSU4ifSwiaWF0IjoxNzQ0MzU2NTY3LCJleHAiOjE3NDQzNTc0Njd9.Os2VS-1Pqr-HOUMic0GjwgdkcyWaxFWDaipIA9bk0AJRpTn2NW0TI3jYyv3yUbrZaZbbycrclU-GmZ9bi5I0vA", "refreshToken": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyIjp7InRlbmFudElkIjpudWxsLCJ1c2VyQ29kZSI6InVzZXIiLCJzeXN0ZW1Sb2xlIjoiQURNSU4ifSwiaWF0IjoxNzQ0MzU2NTY3LCJleHAiOjE3NDQzNTc0Njd9.Os2VS-1Pqr-HOUMic0GjwgdkcyWaxFWDaipIA9bk0AJRpTn2NW0TI3jYyv3yUbrZaZbbycrclU-GmZ9bi5I0vA"}, "status": 200, "statusText": "", "headers": {"access-control-allow-headers": "x-requested-with, Content-Type, Authorization, credential, X-XSRF-TOKEN", "access-control-allow-methods": "POST, GET, OPTIONS, DELETE, PATCH", "access-control-allow-origin": "*", "access-control-expose-headers": "*", "access-control-max-age": "3600", "authorization": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyIjp7InRlbmFudElkIjpudWxsLCJ1c2VyQ29kZSI6InVzZXIiLCJzeXN0ZW1Sb2xlIjoiQURNSU4ifSwiaWF0IjoxNzQ0MzU2NTY3LCJleHAiOjE3NDQzNTc0Njd9.Os2VS-1Pqr-HOUMic0GjwgdkcyWaxFWDaipIA9bk0AJRpTn2NW0TI3jYyv3yUbrZaZbbycrclU-GmZ9bi5I0vA", "cache-control": "no-cache, no-store, max-age=0, must-revalidate", "connection": "keep-alive", "date": "Fri, 11 Apr 2025 07:29:26 GMT", "expires": "0", "keep-alive": "timeout=20", "pragma": "no-cache", "transfer-encoding": "chunked", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-xss-protection": "0"}, "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*", "Content-Type": "application/json"}, "method": "post", "url": "http://*************:8081/pbs-api/api/v1/login", "data": "{\"username\":\"user\",\"password\":\"123\"}", "allowAbsoluteUrls": true}, "request": {}}