{"content": [{"tdCode": "A200252721_22102710181401_T08_01", "toVerifyDt": "2025-03-20", "cusCode": "M400376177", "cusName": "林00", "itemName": "保單照會通知", "visitAprCode": "", "actionCode": "ACTION02", "actionName": "電話聯繫", "memo": "測試成功", "statusCode": "C", "statusName": "已處理", "createDt": "2025-03-20 12:10:26.927", "createBranCode": "003", "createBranName": "北投分行      ", "createBy": "003591", "createName": "陳OO", "catCode": "TDCAT102", "reg": "LOGS", "wobType": "TD", "verifyStatusCode": "P", "verifyStatusName": "待覆核", "userCode": "003591", "doneYn": "", "itemCode": "T08", "tdCat1Code": "TDCAT101", "tdCat2Code": "TDCAT210", "content": "{1}-保單號碼：保險代理部受理編號{2}有照會通知，請至壽險系統確認", "expireDt": "2025-03-07", "logId": "16"}, {"tdCode": "A120822478_22042111365401_T08_01", "toVerifyDt": "2025-03-20", "cusCode": "M320945925", "cusName": "陳00", "itemName": "保單照會通知", "visitAprCode": "", "actionCode": "ACTION02", "actionName": "電話聯繫", "memo": "暫存0320", "statusCode": "H", "statusName": "處理中", "createDt": "2025-03-20 10:07:50.15", "createBranCode": "003", "createBranName": "北投分行      ", "createBy": "003591", "createName": "陳OO", "catCode": "TDCAT102", "reg": "LOGS", "wobType": "TD", "verifyStatusCode": "P", "verifyStatusName": "待覆核", "userCode": "003591", "doneYn": "", "chiefVisitYn": "N", "itemCode": "T08", "tdCat1Code": "TDCAT101", "tdCat2Code": "TDCAT210", "content": "{1}-保單號碼：保險代理部受理編號{2}有照會通知，請至壽險系統確認", "expireDt": "2025-03-07", "logId": "14"}, {"tdCode": "A129268710_22020711400301_T08_01", "toVerifyDt": "2025-03-20", "cusCode": "M329392162", "cusName": "黃00", "itemName": "保單照會通知", "visitAprCode": "", "actionCode": "ACTION02", "actionName": "電話聯繫", "memo": "暫存", "statusCode": "H", "statusName": "處理中", "createDt": "2025-03-20 10:07:09.083", "createBranCode": "003", "createBranName": "北投分行      ", "createBy": "003591", "createName": "陳OO", "catCode": "TDCAT102", "reg": "LOGS", "wobType": "TD", "verifyStatusCode": "P", "verifyStatusName": "待覆核", "userCode": "003591", "doneYn": "", "chiefVisitYn": "N", "itemCode": "T08", "tdCat1Code": "TDCAT101", "tdCat2Code": "TDCAT210", "content": "{1}-保單號碼：保險代理部受理編號{2}有照會通知，請至壽險系統確認", "expireDt": "2025-03-07", "logId": "13"}, {"tdCode": "A200252721_22102710170101_T08_01", "toVerifyDt": "2025-03-19", "cusCode": "M400376177", "cusName": "林00", "itemName": "保單照會通知", "visitAprCode": "", "actionCode": "ACTION02", "actionName": "電話聯繫", "memo": "AAAAAAAAAAAAAAAAAAA", "statusCode": "C", "statusName": "已處理", "createDt": "2025-03-19 13:01:08.953", "createBranCode": "003", "createBranName": "北投分行      ", "createBy": "003591", "createName": "陳OO", "catCode": "TDCAT102", "reg": "LOGS", "wobType": "TD", "verifyStatusCode": "P", "verifyStatusName": "待覆核", "userCode": "003591", "doneYn": "", "chiefVisitYn": "N", "itemCode": "T08", "tdCat1Code": "TDCAT101", "tdCat2Code": "TDCAT210", "content": "{1}-保單號碼：保險代理部受理編號{2}有照會通知，請至壽險系統確認", "expireDt": "2025-03-07", "logId": "12"}, {"tdCode": "A200252721_22102710170101_T08_01", "toVerifyDt": "2025-03-17", "cusCode": "M400376177", "cusName": "林00", "itemName": "保單照會通知", "visitAprCode": "", "actionCode": "ACTION02", "actionName": "電話聯繫", "memo": "AAAAA", "statusCode": "C", "statusName": "已處理", "verifyUserCode": "017880", "verifyUserName": "陳OO", "createDt": "2025-03-17 15:51:04.89", "createBranCode": "003", "createBranName": "北投分行      ", "createBy": "003591", "createName": "陳OO", "catCode": "TDCAT102", "reg": "LOGS", "wobType": "TD", "reason": "退4", "verifyStatusCode": "R", "verifyStatusName": "退回修改", "userCode": "003591", "doneYn": "", "itemCode": "T08", "tdCat1Code": "TDCAT101", "tdCat2Code": "TDCAT210", "content": "{1}-保單號碼：保險代理部受理編號{2}有照會通知，請至壽險系統確認", "expireDt": "2025-03-07", "logId": "11"}, {"tdCode": "A200252721_22102710170101_T08_01", "toVerifyDt": "2025-03-17", "cusCode": "M400376177", "cusName": "林00", "itemName": "保單照會通知", "visitAprCode": "", "actionCode": "ACTION02", "actionName": "電話聯繫", "memo": "0317-2", "statusCode": "C", "statusName": "已處理", "verifyUserCode": "017880", "verifyUserName": "陳OO", "createDt": "2025-03-17 12:00:57.97", "createBranCode": "003", "createBranName": "北投分行      ", "createBy": "003591", "createName": "陳OO", "catCode": "TDCAT102", "reg": "LOGS", "wobType": "TD", "reason": "退4", "verifyStatusCode": "R", "verifyStatusName": "退回修改", "userCode": "003591", "doneYn": "", "itemCode": "T08", "tdCat1Code": "TDCAT101", "tdCat2Code": "TDCAT210", "content": "{1}-保單號碼：保險代理部受理編號{2}有照會通知，請至壽險系統確認", "expireDt": "2025-03-07", "logId": "7"}, {"tdCode": "A200252721_22102710170101_T08_01", "toVerifyDt": "2025-03-17", "cusCode": "M400376177", "cusName": "林00", "itemName": "保單照會通知", "visitAprCode": "", "actionCode": "ACTION02", "actionName": "電話聯繫", "memo": "0317-1", "statusCode": "C", "statusName": "已處理", "verifyUserCode": "017880", "verifyUserName": "陳OO", "createDt": "2025-03-17 11:57:29.387", "createBranCode": "003", "createBranName": "北投分行      ", "createBy": "003591", "createName": "陳OO", "catCode": "TDCAT102", "reg": "LOGS", "wobType": "TD", "reason": "退4", "verifyStatusCode": "R", "verifyStatusName": "退回修改", "userCode": "003591", "doneYn": "", "itemCode": "T08", "tdCat1Code": "TDCAT101", "tdCat2Code": "TDCAT210", "content": "{1}-保單號碼：保險代理部受理編號{2}有照會通知，請至壽險系統確認", "expireDt": "2025-03-07", "logId": "6"}, {"tdCode": "A_T220116017_T01_01_19000101", "toVerifyDt": "2025-03-06", "cusCode": "G420239466", "cusName": "蘇00", "itemName": "定期檢視到期提醒", "visitAprCode": "", "actionCode": "ACTION04", "actionName": "發送簡訊", "memo": "BELLA測試", "statusCode": "N", "statusName": "尚未處理", "createDt": "2025-03-06 17:11:51.45", "createBranCode": "130", "createBranName": "基隆分行      ", "createBy": "102003", "createName": "<PERSON>OO", "catCode": "TDCAT102", "reg": "LOGS", "wobType": "TD", "verifyStatusCode": "P", "verifyStatusName": "待覆核", "userCode": "102003", "doneYn": "", "itemCode": "T01", "tdCat1Code": "TDCAT104", "tdCat2Code": "TDCAT210", "content": "2024/12/31：定期檢視將於{2}到期", "expireDt": "2025-03-07", "logId": "5"}, {"tdCode": "A_A103500339_T01_01_19000101", "toVerifyDt": "2025-02-27", "cusCode": "M303623788", "cusName": "陳00", "itemName": "定期檢視到期提醒", "visitAprCode": "", "actionCode": "ACTION04", "actionName": "發送簡訊", "memo": "Bella測試", "statusCode": "N", "statusName": "尚未處理", "createDt": "2025-02-27 14:56:17.233", "createBranCode": "030", "createBranName": "溪洲分行      ", "createBy": "119766", "createName": "游OO", "catCode": "TDCAT102", "reg": "LOGS", "wobType": "TD", "verifyStatusCode": "P", "verifyStatusName": "待覆核", "userCode": "119766", "doneYn": "", "itemCode": "T01", "tdCat1Code": "TDCAT104", "tdCat2Code": "TDCAT210", "content": "2024/12/31：定期檢視將於{2}到期", "expireDt": "2025-03-07", "logId": "4"}, {"tdCode": "A_A100000896_T01_01_19000101", "toVerifyDt": "2025-02-27", "cusCode": "M300124340", "cusName": "林00", "itemName": "定期檢視到期提醒", "visitAprCode": "", "actionCode": "ACTION02", "actionName": "電話聯繫", "memo": "已處理測試", "statusCode": "C", "statusName": "已處理", "createDt": "2025-02-27 14:10:11.537", "createBranCode": "030", "createBranName": "溪洲分行      ", "createBy": "119766", "createName": "游OO", "catCode": "TDCAT102", "reg": "LOGS", "wobType": "TD", "verifyStatusCode": "P", "verifyStatusName": "待覆核", "userCode": "119766", "doneYn": "", "itemCode": "T01", "tdCat1Code": "TDCAT104", "tdCat2Code": "TDCAT210", "content": "2024/12/31：定期檢視將於{2}到期", "expireDt": "2025-03-07", "logId": "3"}, {"tdCode": "Z_H996036651_T01_01_19000102", "toVerifyDt": "2025-02-18", "cusCode": "00838437", "cusName": "蔡00", "itemName": "定期檢視到期提醒", "visitAprCode": "", "actionCode": "ACTION05", "actionName": "通訊軟體", "memo": "處理中2", "statusCode": "H", "statusName": "處理中", "createDt": "2025-02-18 10:06:18.45", "createBranCode": "130", "createBranName": "基隆分行      ", "createBy": "102003", "createName": "<PERSON>OO", "catCode": "TDCAT102", "reg": "LOGS", "wobType": "TD", "verifyStatusCode": "P", "verifyStatusName": "待覆核", "userCode": "102003", "doneYn": "", "chiefVisitYn": "N", "itemCode": "T01", "tdCat1Code": "TDCAT104", "tdCat2Code": "TDCAT210", "content": "2024/12/31：定期檢視將於{2}到期", "expireDt": "1900-02-01", "logId": "2"}, {"tdCode": "Z_H996036651_T01_01_19000102", "toVerifyDt": "2025-02-18", "cusCode": "00838437", "cusName": "蔡00", "itemName": "定期檢視到期提醒", "visitAprCode": "", "actionCode": "ACTION02", "actionName": "電話聯繫", "memo": "處理中了", "statusCode": "H", "statusName": "處理中", "createDt": "2025-02-18 10:04:40.137", "createBranCode": "130", "createBranName": "基隆分行      ", "createBy": "102003", "createName": "<PERSON>OO", "catCode": "TDCAT102", "reg": "LOGS", "wobType": "TD", "verifyStatusCode": "P", "verifyStatusName": "待覆核", "userCode": "102003", "doneYn": "", "chiefVisitYn": "N", "itemCode": "T01", "tdCat1Code": "TDCAT104", "tdCat2Code": "TDCAT210", "content": "2024/12/31：定期檢視將於{2}到期", "expireDt": "1900-02-01", "logId": "1"}, {"tdCode": "A_A320573330_T01_01_19000102", "toVerifyDt": "2025-03-07", "cusCode": "A320573330", "cusName": "有00", "itemName": "定期檢視到期提醒", "visitAprCode": "", "actionCode": "ACTION04", "actionName": "發送簡訊", "memo": "TEST", "statusCode": "N", "statusName": "尚未處理", "verifyUserCode": "017880", "verifyUserName": "陳OO", "createBranCode": "003", "createBranName": "北投分行      ", "catCode": "TDCAT102", "reg": "LOGS_HIS", "wobType": "TD", "reason": "退3", "verifyStatusCode": "R", "verifyStatusName": "退回修改", "userCode": "003591", "doneYn": "N", "chiefVisitYn": "", "itemCode": "T01", "tdCat1Code": "TDCAT104", "tdCat2Code": "TDCAT210", "content": "2024/12/31：定期檢視將於{2}到期", "expireDt": "2025-03-07", "logId": "2"}, {"tdCode": "A_A320573330_T01_01_19000102", "toVerifyDt": "2025-03-18", "cusCode": "A320573330", "cusName": "有00", "itemName": "定期檢視到期提醒", "visitAprCode": "", "actionCode": "ACTION04", "actionName": "發送簡訊", "memo": "JJJJ", "statusCode": "N", "statusName": "尚未處理", "createBranCode": "003", "createBranName": "北投分行      ", "catCode": "TDCAT102", "reg": "LOGS_HIS", "wobType": "TD", "verifyStatusCode": "P", "verifyStatusName": "待覆核", "userCode": "003591", "doneYn": "N", "chiefVisitYn": "N", "itemCode": "T01", "tdCat1Code": "TDCAT104", "tdCat2Code": "TDCAT210", "content": "2024/12/31：定期檢視將於{2}到期", "expireDt": "2025-03-07", "logId": "3"}], "pageable": {"pageNumber": 0, "pageSize": 20, "sort": {"empty": false, "sorted": true, "unsorted": false}, "offset": 0, "paged": true, "unpaged": false}, "last": true, "totalElements": 14, "totalPages": 1, "first": true, "size": 20, "number": 0, "sort": {"empty": false, "sorted": true, "unsorted": false}, "numberOfElements": 14, "empty": false}