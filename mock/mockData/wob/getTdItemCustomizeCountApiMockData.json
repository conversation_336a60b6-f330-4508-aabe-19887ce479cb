{"status": 200, "data": {"tdcat1Code": "U", "count": 0}, "timestamp": "2025/07/16", "sqlTracer": [{"data": ["T03"], "sqlInfo": " SELECT ITEM_CODE  FROM WOB_TD_ITEMS_TOP_USERS  WHERE USER_CODE = :userCode ,class java.lang.String,{userCode=112790}"}, {"data": {"tdcat1Code": "U", "count": 0}, "sqlInfo": "SELECT COUNT(1) COUNT FROM (     SELECT WTL.ITEM_CODE     FROM (         SELECT *         FROM WOB_TD_ITEMS         WHERE CAL_AO_YN = 'Y'           AND SHOW_TYPE IN ('YY', 'YN')     ) WTI     JOIN WOB_TDITEM_CAT2 WTC2       ON WTC2.TDCAT2_CODE = WTI.TDCAT2_CODE      AND WTC2.TDCAT1_CODE = WTI.TDCAT1_CODE     JOIN WOB_TD_LISTS WTL       ON WTI.ITEM_CODE = WTL.ITEM_CODE     JOIN CUS_AO_INFO AO       ON AO.CUS_CODE = WTL.CUS_CODE      AND AO.BU_CODE = WTL.BU_CODE     LEFT JOIN WOB_TD_ASSIGN WTA       ON WTA.TD_CODE = WTL.TD_CODE     WHERE AO.BRAN_CODE = :branCode       AND (AO.USER_CODE = :userId            OR (AO.USER_CODE IS NULL AND WTA.USER_CODE = :userId))       AND AO.BU_CODE = :buCode     UNION ALL     SELECT WTL.ITEM_CODE     FROM (         SELECT *         FROM WOB_TD_ITEMS         WHERE CAL_AO_YN = 'Y'           AND SHOW_TYPE IN ('YY', 'YN')     ) WTI     JOIN WOB_TDITEM_CAT2 WTC2       ON WTC2.TDCAT2_CODE = WTI.TDCAT2_CODE      AND WTC2.TDCAT1_CODE = WTI.TDCAT1_CODE     JOIN WOB_TD_LISTS_HIS WTL       ON WTI.ITEM_CODE = WTL.ITEM_CODE     JOIN CUS_AO_INFO AO       ON AO.CUS_CODE = WTL.CUS_CODE      AND AO.BU_CODE = WTL.BU_CODE     LEFT JOIN WOB_TD_ASSIGN WTA       ON WTA.TD_CODE = WTL.TD_CODE     WHERE AO.BRAN_CODE = :branCode       AND (AO.USER_CODE = :userId            OR (AO.USER_CODE IS NULL AND WTA.USER_CODE = :userId))       AND AO.BU_CODE = :buCode       AND WTL.DONE_YN = 'N'     UNION ALL     SELECT WTL.ITEM_CODE     FROM (         SELECT *         FROM WOB_TD_ITEMS         WHERE CAL_AO_YN = 'N'           AND SHOW_TYPE IN ('YY', 'YN')     ) WTI     JOIN WOB_TDITEM_CAT2 WTC2       ON WTC2.TDCAT2_CODE = WTI.TDCAT2_CODE      AND WTC2.TDCAT1_CODE = WTI.TDCAT1_CODE     JOIN WOB_TD_LISTS WTL       ON WTI.ITEM_CODE = WTL.ITEM_CODE     WHERE WTL.USER_CODE = :userId     UNION ALL     SELECT WTL.ITEM_CODE     FROM (         SELECT *         FROM WOB_TD_ITEMS         WHERE CAL_AO_YN = 'N'           AND SHOW_TYPE IN ('YY', 'YN')     ) WTI     JOIN WOB_TDITEM_CAT2 WTC2       ON WTC2.TDCAT2_CODE = WTI.TDCAT2_CODE      AND WTC2.TDCAT1_CODE = WTI.TDCAT1_CODE     JOIN WOB_TD_LISTS_HIS WTL       ON WTI.ITEM_CODE = WTL.ITEM_CODE     WHERE WTL.USER_CODE = :userId       AND WTL.DONE_YN = 'N' ) A WHERE ITEM_CODE IN (:itemCodes) ,class com.bi.pbs.wob.web.model.TdItemCustomizeCountResp,{itemCodes=[T03], branCode=891, buCode=Z, userId=112790}"}]}