{"status": 200, "data": [{"itemCode": "T05", "itemName": "產品到期通知", "tdCat1Code": "TDCAT103", "todayTdCount": 0, "notTodayTdCount": 1, "expireTdCount": "0", "showOrder": 21}, {"itemCode": "T06", "itemName": "停利通知", "tdCat1Code": "TDCAT103", "todayTdCount": 0, "notTodayTdCount": 0, "expireTdCount": "0", "showOrder": 22}, {"itemCode": "T07", "itemName": "停損通知", "tdCat1Code": "TDCAT103", "todayTdCount": 0, "notTodayTdCount": 0, "expireTdCount": "0", "showOrder": 23}], "timestamp": "2025/07/16", "sqlTracer": [{"data": {"O_DATE": "2025/07/15"}, "sqlInfo": "SP_JADM_GET_BUSSINESS_DATE,dbo,,{I_NUM=-1, O_DATE=Wed Jul 16 17:05:02 CST 2025}"}, {"data": [], "sqlInfo": " SELECT ITEM_CODE  FROM WOB_TD_ITEMS  WHERE CAL_AO_YN = 'N' AND SHOW_TYPE IN ( :showTypes )  AND TDCAT1_CODE = :tdCat1Code ,class com.bi.pbs.wob.web.model.TdItemStatResp,{itemCode=null, showTypes=[YY, YN], tdCat1Code=TDCAT103}"}, {"data": [{"itemCode": "T05", "itemName": "產品到期通知", "tdCat1Code": "TDCAT103", "todayTdCount": 0, "notTodayTdCount": 1, "expireTdCount": "0", "showOrder": 21}, {"itemCode": "T06", "itemName": "停利通知", "tdCat1Code": "TDCAT103", "todayTdCount": 0, "notTodayTdCount": 0, "expireTdCount": "0", "showOrder": 22}, {"itemCode": "T07", "itemName": "停損通知", "tdCat1Code": "TDCAT103", "todayTdCount": 0, "notTodayTdCount": 0, "expireTdCount": "0", "showOrder": 23}], "sqlInfo": " SELECT * FROM  (  SELECT  TEMP.*,  COALESCE(EXPIRE.EXPIRETD_COUNT,0) EXPIRETD_COUNT  FROM  (  SELECT  WTI.SHOW_ORDER,  WTI.TOPTEN_NUM,  WTI.ITEM_CODE,  WTI.ITEM_NAME,  WTI.TDCAT1_CODE,  WTI.TDCAT2_CODE,  WTC2.TDCAT2_NAME,  COALESCE(SUM(TODAY),0) TODAYTD_COUNT,  COALESCE(SUM(NOTTODAY),0) NOTTODAYTD_COUNT  FROM  (  SELECT WTIT.TOPTEN_NUM, WTI.*  FROM WOB_TD_ITEMS WTI  LEFT JOIN WOB_TD_ITEMS_TOPTEN WTIT ON WTIT.BU_CODE = :buCode  AND WTIT.ITEM_CODE = WTI.ITEM_CODE  WHERE 1 = 1  AND TDCAT1_CODE= :tdCat1Code  AND CAL_AO_YN='Y'  AND SHOW_TYPE IN ('YY', 'YN')  ) WTI  JOIN WOB_TDITEM_CAT2 WTC2 ON WTC2.TDCAT2_CODE = WTI.TDCAT2_CODE AND WTC2.TDCAT1_CODE = WTI.TDCAT1_CODE  LEFT JOIN  (  SELECT  WTL.ITEM_CODE, WTL.CREATE_DT,  CASE WHEN CONVERT(VARCHAR(8), WTL.CREATE_DT, 112) > :lastOpenDay THEN 1 ELSE 0 END TODAY,  CASE WHEN CONVERT(VARCHAR(8), WTL.CREATE_DT, 112) <= :lastOpenDay THEN 1 ELSE 0 END NOTTODAY  FROM WOB_TD_LISTS WTL  JOIN CUS_AO_INFO AO ON AO.CUS_CODE=WTL.CUS_CODE AND AO.BU_CODE = WTL.BU_CODE  LEFT JOIN WOB_TD_ASSIGN WTA ON WTA.TD_CODE = WTL.TD_CODE  WHERE AO.BU_CODE = :buCode  AND WTL.TDCAT1_CODE = :tdCat1Code  AND EXISTS (      SELECT 1 FROM (SELECT P.BRAN_CODE, P.POS_CODE, P.ROLE_CODE, P.BU_CODE, AUPM.USER_CODE FROM ADM_POS_ACCESS_MAP PAM JOIN ADM_POSITIONS P ON PAM.ACCESS_POS_CODE = P.POS_CODE LEFT JOIN ADM_USER_POS_MAP AUPM ON P.POS_CODE = AUPM.POS_CODE WHERE PAM.POS_CODE =  '891_98' ) AUTH      WHERE AO.BRAN_CODE = AUTH.BRAN_CODE        AND (AO.USER_CODE = AUTH.USER_CODE OR (COALESCE(AO.USER_CODE, '')= ''AND WTA.USER_CODE = AUTH.USER_CODE))  )  ) WTL ON WTL.ITEM_CODE = WTI.ITEM_CODE  GROUP BY WTI.TOPTEN_NUM, WTI.SHOW_ORDER, WTI.ITEM_CODE,  WTI.ITEM_NAME,WTI.TDCAT1_CODE,  WTI.TDCAT2_CODE,WTC2.TDCAT2_NAME  ) TEMP  LEFT JOIN  (  SELECT  ITEM_CODE, COUNT(ITEM_CODE) EXPIRETD_COUNT  FROM WOB_TD_LISTS_HIS WTL  JOIN CUS_AO_INFO AO ON AO.CUS_CODE=WTL.CUS_CODE AND AO.BU_CODE = WTL.BU_CODE  LEFT JOIN WOB_TD_ASSIGN WTA ON WTA.TD_CODE = WTL.TD_CODE  WHERE AO.BU_CODE = :buCode  AND WTL.TDCAT1_CODE = :tdCat1Code  AND WTL.DONE_YN='N'  AND EXISTS (      SELECT 1 FROM (SELECT P.BRAN_CODE, P.POS_CODE, P.ROLE_CODE, P.BU_CODE, AUPM.USER_CODE FROM ADM_POS_ACCESS_MAP PAM JOIN ADM_POSITIONS P ON PAM.ACCESS_POS_CODE = P.POS_CODE LEFT JOIN ADM_USER_POS_MAP AUPM ON P.POS_CODE = AUPM.POS_CODE WHERE PAM.POS_CODE =  '891_98' ) AUTH      WHERE AO.BRAN_CODE = AUTH.BRAN_CODE        AND (AO.USER_CODE = AUTH.USER_CODE OR (COALESCE(AO.USER_CODE, '')= '' AND WTA.USER_CODE = AUTH.USER_CODE)) )  GROUP BY ITEM_CODE  )  EXPIRE ON EXPIRE.ITEM_CODE = TEMP.ITEM_CODE  ) A  ORDER BY CASE WHEN A.TOPTEN_NUM IS NULL THEN 1 ELSE 0 END, A.SHOW_ORDER ASC ,class com.bi.pbs.wob.web.model.TdItemStatResp,{branCode=891, tdCat1Code=TDCAT103, buCode=Z, userCode=112790, lastOpenDay=2025-07-15 00:00:00.0, groupCode=null}"}]}