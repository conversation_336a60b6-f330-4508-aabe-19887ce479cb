import getGenNewsMessageApiMockData from './mockData/gen/GetGenNewsMessageApiMockData.json';
import getMessageMapHomeApiMockData from './mockData/gen/getMessageMapHomeApiMockData.json';
import getDocHomeApiMockData from './mockData/gen/getDocHomeApiMockData.json';
import getDocHomeListApiMockData_ISSUER from './mockData/gen/getDocHomeListApiMockData_ISSUER.json';
import getDocHomeListApiMockData_MKTEVENT from './mockData/gen/getDocHomeListApiMockData_MKTEVENT.json';
import getDocHomeListApiMockData_MKTOUTLOOK from './mockData/gen/getDocHomeListApiMockData_MKTOUTLOOK.json';
import getDocHomeListApiMockData_NEWS from './mockData/gen/getDocHomeListApiMockData_NEWS.json';
import getDocHomeListApiMockData_PRODUCT from './mockData/gen/getDocHomeListApiMockData_PRODUCT.json';
import getBbsMgtPageDataMockData_MSG01 from './mockData/gen/getBbsMgtPageDataMockData_MSG01.json';
import getBbsMgtPageDataMockData_MSG02 from './mockData/gen/getBbsMgtPageDataMockData_MSG02.json';
import getViewSelDocMockData_01 from './mockData/gen/getViewSelDocMockData_01.json';
import getViewSelDocMockData_02 from './mockData/gen/getViewSelDocMockData_02.json';
import getViewSelDocMockData_03 from './mockData/gen/getViewSelDocMockData_03.json';
import getDocOutLookMainCntApiMockData from './mockData/gen/getDocOutLookMainCntApiMockData.json';
import getDocOutLookSubCntApiMockData_DMTMO01 from './mockData/gen/getDocOutLookSubCntApiMockData_DMTMO01.json';
import getDocResearchPageDataMockData_DSTMO0101 from './mockData/gen/getDocResearchPageDataMockData_DSTMO0101.json';
import getDocOutLookSubCntApiMockData_DMTMO05 from './mockData/gen/getDocOutLookSubCntApiMockData_DMTMO05.json';
import getDocResearchPageDataMockData_DSTMO0121 from './mockData/gen/getDocResearchPageDataMockData_DSTMO0121.json';
import getDocOutLookSubCntApiMockData_DMTMO03 from './mockData/gen/getDocOutLookSubCntApiMockData_DMTMO03.json';
import getDocResearchPageDataMockData_DSTMO0111 from './mockData/gen/getDocResearchPageDataMockData_DSTMO0111.json';
import getDocResearchPageDataMockData_DSTMO0112 from './mockData/gen/getDocResearchPageDataMockData_DSTMO0112.json';
import getDocOutLookSubCntApiMockData_DMTMO04 from './mockData/gen/getDocOutLookSubCntApiMockData_DMTMO04.json';
import getDocIssuersPageDataMockData from './mockData/gen/getDocIssuersPageDataMockData.json';
import getDocNewsPageDataMockData from './mockData/gen/getDocNewsPageDataMockData.json';
import getDocProTypeCntApiMockData from './mockData/gen/getDocProTypeCntApiMockData.json';
import getDocProDCDPageDataMockData_FUND from './mockData/gen/getDocProDCDPageDataMockData_FUND.json';
import getDocProDCDPageDataMockData_ETF from './mockData/gen/getDocProDCDPageDataMockData_ETF.json';
import getDocProDCDPageDataMockData_FB from './mockData/gen/getDocProDCDPageDataMockData_FB.json';
import getDocProDCDPageDataMockData_PFD from './mockData/gen/getDocProDCDPageDataMockData_PFD.json';
import getDocProDCDPageDataMockData_empty from './mockData/gen/getDocProDCDPageDataMockData_empty.json';
import getDocProDCDPageDataMockData_INS from './mockData/gen/getDocProDCDPageDataMockData_INS.json';
import getDocMktMainCntApiMockData from './mockData/gen/getDocMktMainCntApiMockData.json';
import getDocMktSubCntApiMockData_DMTME04 from './mockData/gen/getDocMktSubCntApiMockData_DMTME04.json';
import getDocMktSubCntApiMockData_DMTME03 from './mockData/gen/getDocMktSubCntApiMockData_DMTME03.json';
import getDocMktSubCntApiMockData_DMTME02 from './mockData/gen/getDocMktSubCntApiMockData_DMTME02.json';
import getDocMktSubCntApiMockData_DMTME01 from './mockData/gen/getDocMktSubCntApiMockData_DMTME01.json';
import getDocMktSubCntApiMockData_DMTME05 from './mockData/gen/getDocMktSubCntApiMockData_DMTME05.json';
import getDocSalesPageDataMockData_DMTME0401 from './mockData/gen/getDocSalesPageDataMockData_DMTME0401.json';
import getDocSalesPageDataMockData_empty from './mockData/gen/getDocSalesPageDataMockData_empty.json';
import getHomepageGenReviewCntApiMockData from './mockData/gen/getHomepageGenReviewCntApiMockData.json';
import getHomepageCusReviewCntApiMockData from './mockData/gen/getHomepageCusReviewCntApiMockData.json';
import getHomepageProReviewCntApiMockData from './mockData/gen/getHomepageProReviewCntApiMockData.json';

export function getGenMessageCat({ catType, catName, mainCatCode }) { }
export function postGenMessageCat({ catType, catName, mainCatCode, subCatCode }) { }
export function patchGenMessageCat({ catType, catName, mainCatCode, subCatCode }) { }
export function getGenCountMessage(payload) { }
export function deleteGenMessageCat({ catType, catName, mainCatCode, subCatCode }) { }
export function getGenMessageApi({ msgId }) { }
export function getBbmMaintainSaveApi() { }

export function postMsgLogApi(formData) { }

export function getMessageMapApi() { }

export function getBbsMgtPageData(payload, queryString) {
	switch (payload.msgCode) {
		case 'MSG01':
			return getBbsMgtPageDataMockData_MSG01;
		case 'MSG02':
			return getBbsMgtPageDataMockData_MSG02;
	}
}
export function getGenNewsMessageApi({ msgType }) {
	return getGenNewsMessageApiMockData;
}
export function getMessageMapHomeApi({ msgType }) {
	return getMessageMapHomeApiMockData;
}
export function getDocHomeApi() {
	return getDocHomeApiMockData;
}

export function getDocHomeListApi({ docCat }) {
	switch (docCat) {
		case 'ISSUER':
			return getDocHomeListApiMockData_ISSUER;
		case 'MKTEVENT':
			return getDocHomeListApiMockData_MKTEVENT;
		case 'MKTOUTLOOK':
			return getDocHomeListApiMockData_MKTOUTLOOK;
		case 'NEWS':
			return getDocHomeListApiMockData_NEWS;
		case 'PRODUCT':
			return getDocHomeListApiMockData_PRODUCT;
	}
}

export function getViewSelDoc({ docCat, docId }) {
	switch (docId) {
		case 'DF202504180002':
			return getViewSelDocMockData_01;
		case 'DF202502110003':
			return getViewSelDocMockData_02;
		case 'DF202502150002':
			return getViewSelDocMockData_03;
		default:
			return getViewSelDocMockData_01;
	}
}

export function getDocOutLookMainCntApi() {
	return getDocOutLookMainCntApiMockData;
}

export function getDocOutLookSubCntApi({ mainTypeCode }) {
	switch (mainTypeCode) {
		case 'DMTMO01':
			return getDocOutLookSubCntApiMockData_DMTMO01;
		case 'DMTMO05':
			return getDocOutLookSubCntApiMockData_DMTMO05;
		case 'DMTMO03':
			return getDocOutLookSubCntApiMockData_DMTMO03;
		case 'DMTMO04':
		case 'DMTMO08':
		case 'DMTMO07':
			return getDocOutLookSubCntApiMockData_DMTMO04;
		default:
	}
}

export function getDocResearchPageData({ mainTypeCode, subTypeCode }, queryString) {
	switch (subTypeCode) {
		case 'DSTMO0101':
			return getDocResearchPageDataMockData_DSTMO0101;
		case 'DSTMO0121':
			return getDocResearchPageDataMockData_DSTMO0121;
		case 'DSTMO0111':
		case 'DSTMO0102':
		case 'DSTMO0103':
		case 'DSTMO0104':
		case 'DSTMO0105':
		case 'DSTMO0122':
		case 'DSTMO0123':
		case 'DSTMO0124':
		case 'DSTMO0125':
		case 'DSTMO0113':
		case 'DSTMO0114':
		case 'DSTMO0115':
		case 'DSTMO0116':
		case 'DSTMO0136':
			return getDocResearchPageDataMockData_DSTMO0111;
		case 'DSTMO0112':
			return getDocResearchPageDataMockData_DSTMO0112;
	}
}

export function getDocIssuersPageData({ queryString }) {
	return getDocIssuersPageDataMockData;
}
export function getDocNewsPageData({ queryString }) {
	return getDocNewsPageDataMockData;
}

export function getDocProTypeCntApi() {
	return getDocProTypeCntApiMockData;
}
export function getDocProDCDPageData({ proTypeCode }, queryString) {
	switch (proTypeCode) {
		case 'FUND':
			return getDocProDCDPageDataMockData_FUND;
		case 'ETF':
			return getDocProDCDPageDataMockData_ETF;
		case 'FB':
			return getDocProDCDPageDataMockData_FB;
		case 'PFD':
			return getDocProDCDPageDataMockData_PFD;
		case 'DCD':
		case 'SP':
			return getDocProDCDPageDataMockData_empty;
		case 'INS':
			return getDocProDCDPageDataMockData_INS;
		default:
			return getDocProDCDPageDataMockData_FUND; // Default case, can be adjusted
	}
}

export function getDocMktMainCntApi() {
	return getDocMktMainCntApiMockData;
}
export function getDocMktSubCntApi({ mainTypeCode }) {
	switch (mainTypeCode) {
		case 'DMTME04':
			return getDocMktSubCntApiMockData_DMTME04;
		case 'DMTME03':
			return getDocMktSubCntApiMockData_DMTME03;
		case 'DMTME02':
			return getDocMktSubCntApiMockData_DMTME02;
		case 'DMTME01':
			return getDocMktSubCntApiMockData_DMTME01;
		case 'DMTME05':
			return getDocMktSubCntApiMockData_DMTME05;
		default:
			return getDocOutLookSubCntApiMockData_DMTMO01; // Default case, can be adjusted
	}
}

export function getDocSalesPageData({ mainTypeCode, subTypeCode }, queryString) {
	switch (subTypeCode) {
		case 'DMTME0401':
			return getDocSalesPageDataMockData_DMTME0401;
		default:
			return getDocSalesPageDataMockData_empty;
	}
}
export function getHomepageGenReviewCntApi() {
	return getHomepageGenReviewCntApiMockData;
}
export function getHomepageCusReviewCntApi() {
	return getHomepageCusReviewCntApiMockData;
}
export function getHomepageProReviewCntApi() {
	return getHomepageProReviewCntApiMockData;
}
