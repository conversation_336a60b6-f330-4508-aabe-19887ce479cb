// ADM0110/programSet.vue
import FunctionMenuTreeMockData from './mockData/adm/FunctionMenuTreeMockData.json';
import RoleMenuMockData from './mockData/adm/RoleMenuMockData.json';
// ADM0101/admRole.vue
import SystemRolesMockData from './mockData/adm/SystemRolesMockData.json';
import MenuEnableTreeMockData from './mockData/adm/MenuEnableTreeMockData.json';
// ADM0101/include/admRoleAuthority.vue
import UpdateRolAuthorityMockData from './mockData/adm/UpdateRolAuthorityMockData.json';
import RoleReviewDetailMockData from './mockData/adm/RoleReviewDetailMockData.json';
// ADM0103/admMenuPreview.vue
import RoleMenuTreeMockData from './mockData/adm/RoleMenuTreeMockData.json';
// components/biTabs.vue
import CfgCusMenuTabMockData from './mockData/adm/CfgCusMenuTabMockData.json';
import UserAccountMenuTabMockData from './mockData/adm/UserAccountMenuTabMockData.json';
import GroupBranMenuTabMockData from './mockData/adm/GroupBranMenuTabMockData.json';
import FcStepMenuTabMockData from './mockData/adm/FcStepMenuTabMockData.json';
import ProductSearchMenuTabMockData from './mockData/pro/productSearchMenuTabMockData.json';
import GetLoggingMockData from './mockData/adm/GetLoggingMockData.json';
import BbsMgtMenuTabMockData from './mockData/gen/BbsMgtMenuTabMockData.json';
import BbsHeadMenuTabMockData from './mockData/gen/BbsHeadMenuTabMockData.json';
import CusBaseInfoMenuTabMockData from './mockData/cus/CusBaseInfoMenuTabMockData.json';
import InvestAnalysisMenuTabMockData from './mockData/cus/InvestAnalysisMenuTabMockData.json';
import DocResearchMenuTabMockData from './mockData/adm/DocResearchMenuTabMockData.json';
import DocProDCDMenuTabMockData from './mockData/gen/DocProDCDMenuTabMockData.json';
import DocSalesMenuTabMockData from './mockData/gen/DocSalesMenuTabMockData.json';
// adm/ADM0104/include/userAccountTab.vue
import BranMenuMockData from './mockData/adm/BranMenuMockData.json';
import SysUserMockData from './mockData/adm/SysUserMockData.json';
import UserPosEventMockData from './mockData/adm/UserPosEventMockData.json';
// adm/ADM0104/include/userAccountNewTab.vue
import UserInfoMockData from './mockData/adm/UserInfoMockData.json';
import UserBranPosInfoMockData from './mockData/adm/UserBranPosInfoMockData.json';
import PosBranMenuMockData from './mockData/adm/PosBranMenuMockData.json';

import SELECT_YNMockData from './mockData/adm/AdmCodeDetail/SELECT_YNMockData.json';
import INT_FREQ_UNITTYPEMockData from './mockData/adm/AdmCodeDetail/INT_FREQ_UNITTYPEMockData.json';
import FIN_REQ_CODEMockData from './mockData/adm/AdmCodeDetail/FIN_REQ_CODEMockData.json';
import GM_CAT_TYPEMockData from './mockData/adm/AdmCodeDetail/GM_CAT_TYPEMockData.json';
import BATCH_CONTROL_STATUSMockData from './mockData/adm/AdmCodeDetail/BATCH_CONTROL_STATUSMockData.json';

import getUnderUserDeputiesPageDataApiJson from './mockData/adm/GetUnderUserDeputiesPageDataApiData.json';
import getDeputiesLogApiJson from './mockData/adm/GetDeputiesLogApiData.json';
import getUserAccessCntLogApiJson from './mockData/adm/GetUserAccessCntLogApiData.json';
import getBranAccessCntLogApiJson from './mockData/adm/GetbranAccessCntLogApiData.json';
import getAreaMenuApiJson from './mockData/adm/GetAreaMenuApiData.json';
import getuserAccessCusLogsApiSortLogDtAscJson from './mockData/adm/GetuserAccessCusLogsApiDataSortLogDtAsc.json';
import getuserAccessCusLogsApiSortLogDtDescJson from './mockData/adm/GetuserAccessCusLogsApiDataSortLogDtDesc.json';
import getuserAccessCusLogsApiSortBranCodeAscJson from './mockData/adm/GetuserAccessCusLogsApiDataSortBranCodeAsc.json';
import getuserAccessCusLogsApiSortBranCodeDescJson from './mockData/adm/GetuserAccessCusLogsApiDataSortBranCodeDesc.json';
import getHomeComponentApiMockData from './mockData/adm/GetHomeComponentApiMockData.json';
import getAdmShutDownInfoApiMockData from './mockData/adm/GetAdmShutDownInfoApiMockData.json';
import DocResearch from '@/views/gen/docResearch/docResearch.vue';
import getMinorAreaApiJson from './mockData/adm/GetMinorAreaApiData.json';
import getTdItemCat1MenuApiJson from './mockData/adm/GetTdItemCat1MenuApiData.json';
import getTdItemsApiJson from './mockData/adm/GetTdItemsApiData.json';
import getBranchesApiJson from './mockData/adm/GetBranchesApiData.json';
import getBranEmployeeApiJson from './mockData/adm/GetBranEmployeeApiData.json';
import getAdmRoleByRoleTypeApiJson from './mockData/adm/GetAdmRoleByRoleTypeApiData.json';
import getFcBranMapDataApiJson from './mockData/adm/GetFcBranMapDataApiData.json';
import postFcBranMapChkApiJson from './mockData/adm/PostFcBranMapChkApiData.json';
import getGroupBranNameApiJson from './mockData/adm/GetGroupBranNameApiData.json';
import getBranPageDataApiJson from './mockData/adm/GetBranPageDataApiData.json';
import getAdmWmsBatchGrptimeCfgJson from './mockData/adm/GetAdmWmsBatchGrptimeCfg.json';
import getCusExtDataQueItemSelsApiMockData from './mockData/adm/GetCusExtDataQueItemSelsApiData.json';
import getCusExtDataQueSectionsMenuApiMockData from './mockData/adm/GetCusExtDataQueSectionsMenuApiData.json';
import getCusExtDataQueItemsMenuApiMockData from './mockData/adm/GetCusExtDataQueItemsMenuApiData.json';
import getAmdParamDivApiJson from './mockData/adm/GetAmdParamDivApiData.json';
import getAmdParamFdApiJson from './mockData/adm/GetAmdParamFdApiData.json';
import getAmdParamLgApiJson from './mockData/adm/GetAmdParamLgApiData.json';
import getBatchJobStatusMockData from './mockData/adm/GetBatchJobStatusMockData.json';
import getBatchJobStatusHistoryMockData from './mockData/adm/GetBatchJobStatusHistoryMockData.json';
import getAdmWmsBatchGroupsMockData from './mockData/adm/GetAdmWmsBatchGroupsMockData.json';

import GetCaAccessPageDataApiJson from './mockData/adm/GetCaAccessPageData.json';

export function getAdmCodeDetail({ codeType, codeValue, codeName }) {
	switch (codeType) {
		case 'SELECT_YN':
			return SELECT_YNMockData;
		case 'INT_FREQ_UNITTYPE':
			return INT_FREQ_UNITTYPEMockData;
		case 'FIN_REQ_CODE':
			return FIN_REQ_CODEMockData;
		case 'GM_CAT_TYPE':
			return GM_CAT_TYPEMockData;
	}
}
// ADM0110/programSet.vue
export function getFunctionMenuTreeApi() {
	return FunctionMenuTreeMockData;
}

export function getPrograSetApi() {
	return FunctionMenuTreeMockData;
}

export function patchSaveMenuActive() {
	return true;
}

// ADM0101/admRole.vue
export function getRoleMenuApi() {
	return RoleMenuMockData;
}

export function getAdmRolesApi() {
	return SystemRolesMockData;
}

// ADM0101/include/admRoleAuthority.vue
// getAdmRoles() 呼叫了與 ADM0101/admRole.vue 的 getAdmRolesApi 一樣的Api

export function getMenuEnableTreeApi() {
	return MenuEnableTreeMockData;
}

export function postUpdateRolAuthorityApi() {
	return UpdateRolAuthorityMockData;
}

// ADM0101/include/admRoleReviewDetail.vue
export function getDetailApi() {
	return RoleReviewDetailMockData;
}

// ADM0103/admMenuPreview.vue
// getRoleMenuDatas() 呼叫了與 ADM0101/admRole.vue 的 getRoleMenuApi 一樣的Api

export function getRoleMenuTreeApi() {
	return RoleMenuTreeMockData;
}

// components/biTabs.vue
export function getMenuTabApi(menuCode) {
	switch (menuCode) {
		case 'M01-01':
			return CfgCusMenuTabMockData;
		case 'M00-04':
			return UserAccountMenuTabMockData;
		case 'M00-07':
			return GroupBranMenuTabMockData;
		case 'M00-06':
			return FcStepMenuTabMockData;
		case 'M30-00':
			return ProductSearchMenuTabMockData;
		case 'M40-01':
			return BbsMgtMenuTabMockData;
		case 'M40-02':
			return BbsHeadMenuTabMockData;
		case 'M20-052':
			return CusBaseInfoMenuTabMockData;
		case 'M20-058':
			return InvestAnalysisMenuTabMockData;
		case 'M41-03':
			return DocResearchMenuTabMockData;
		case 'M41-01':
			return DocProDCDMenuTabMockData;
		case 'M41-02':
			return DocSalesMenuTabMockData;
	}
	return UserAccountMenuTabMockData;
}

export function postLoggingApi() {
	return GetLoggingMockData;
}

// adm/ADM0104/include/userAccountTab.vue
export function getBranMenuApi() {
	return BranMenuMockData;
}
// getRoleMenu() 呼叫了與 ADM0101/admRole.vue 的 getRoleMenuApi 一樣的Api
export function getSysUserApi() {
	return SysUserMockData;
}

export async function getExportExcelApi() {
	return {
		data: null
	};
}

export function getUserPosEventApi() {
	return UserPosEventMockData;
}

// adm/ADM0104/include/userAccountNewTab.vue
export function getEditUserInfoApi() {
	return UserInfoMockData;
}

export function getUserBranPosInfoApi() {
	return UserBranPosInfoMockData;
}
// getBranMenu() 呼叫了與 ADM0104/include/userAccountTab.vue 的 getBranMenuApi 一樣的Api

export function getPosBranMenuApi() {
	return PosBranMenuMockData;
}

export function postUserAccountApi() {
	return; // todo: 待提供正確的 mockData 內容
}

export function getGroupBranNameApi() {
	return Promise.resolve({ data: getGroupBranNameApiJson });
}

export function getBranPageData({ groupCode }) {
	return Promise.resolve({ data: getBranPageDataApiJson });
}

export async function getBranExportPageData({ groupCode }) {
	return { data: null };
}

export function postFcBranMapChkApi(formData) {
	return Promise.resolve({ data: postFcBranMapChkApiJson });
}

export function getAdmRoleByRoleTypeApi({ roleType }) {
	return Promise.resolve({ data: getAdmRoleByRoleTypeApiJson });
}

export function postFcBranMapApi() {}

export function getFcBranMapData({ roleCode }, queryString) {
	return Promise.resolve({ data: getFcBranMapDataApiJson });
}

export async function getFcBranExportPageData({ roleCode }) {
	return { data: null };
}

export function getAreaMenu() {
	return Promise.resolve({ data: getAreaMenuApiJson });
}

export function getUserDeputiesApi() {
	return Promise.resolve({ data: null });
}

export function getDeputiesRmMgrApi() {
	return Promise.resolve({ data: null });
}

export function getUnderUserDeputiesPageDataApi({ groupCode, branCode }, queryString) {
	return Promise.resolve({ data: getUnderUserDeputiesPageDataApiJson });
}

export function getDeputyUserCodeApi({ deputyUserCode }) {
	return Promise.resolve({ data: null });
}

export function postInsertDeputyApi({ userCode, roleMetadata, branCode, deputyUserCode, deputyBranCode, stdDt, endDt }) {
	return Promise.resolve({ data: null });
}

export function getchkValidDeputiesTimeApi({ stdDt, endDt }) {
	return Promise.resolve({ data: null });
}

export function getdoCheckIsBusinessDtApi({ date }) {
	return Promise.resolve({ data: null });
}

export function getcheckDeputyUserCodeApi({ userCode, deputyUserCode }) {
	return Promise.resolve({ data: null });
}

export function deleteUserdeputyApi({ userCode, deputyUserCode }) {
	return Promise.resolve({ data: null });
}

export function getAdmBranchesApi({ parentBranCode, branLvlCode, removeYn, branCode }) {
	return Promise.resolve({ data: null });
}

export function getAdmUsersListApi({ branCode, userCode, parentBranCode, roleCode }) {
	return Promise.resolve({ data: null });
}

export function getUserCodeLengthApi() {
	return Promise.resolve({ data: null });
}

export function getDeputiesLogApi({ parentBranCode, branCode, userCode, stdDt, endDt }) {
	return Promise.resolve({ data: getDeputiesLogApiJson });
}

export function getMinorAreaApi({ buCode, majorCode }) {
	return Promise.resolve({ data: getMinorAreaApiJson });
}

export function getBranchesApi({ buCode, majorCode, minorCode }) {
	return Promise.resolve({ data: getBranchesApiJson });
}

export function getDeputiesApi({ branCode }) {
	return Promise.resolve({ data: null });
}

export function getShutdownInfoApi() {
	return Promise.resolve({ data: null });
}

export function postShutdownInfoApi({ startDt, endDt, shutdownDesc }) {
	return Promise.resolve({ data: null });
}

export function patchShutdownInfoApi({ shutdownId, startDt, endDt, shutdownDesc }) {
	return Promise.resolve({ data: null });
}

export function deleteShutdownInfoApi({ shutdownId }) {
	return Promise.resolve({ data: null });
}

export function getAllBranchesMenuApi() {
	return Promise.resolve({ data: null });
}

export function getModuleMenuApi() {
	return Promise.resolve({ data: null });
}

export function getBranEmployeeApi({ buCode, branCode }) {
	return Promise.resolve({ data: getBranEmployeeApiJson });
}

export function getUserMenuApi({ branCode }) {
	return Promise.resolve({ data: null });
}

export function getBranchFunctionMenuApi({ menuCode }) {
	return Promise.resolve({ data: null });
}

export function getUserMenu({ branCode }) {
	return Promise.resolve({ data: null });
}

export function getUserAccessCntLogApi({ branCode, logStartDt, logEndDt, moduleMenuCode, functionMenuCode, userCode }, queryString) {
	return Promise.resolve({ data: getUserAccessCntLogApiJson });
}

export function getCusSaveResultCountApi({ paramType, paramCode }) {
	return Promise.resolve({ data: null });
}

export function getBranAccessCntLogApi({ branCode, logStartDt, logEndDt, moduleMenuCode, functionMenuCode, userCode }, queryString) {
	return Promise.resolve({ data: getBranAccessCntLogApiJson });
}

export function getCusFunctionMenuApi() {
	return Promise.resolve({ data: null });
}

export function getUserAccessCusLogsApi({ branCode, logStartDt, logEndDt, menuCode, userCode, cusCode }, queryString) {
	const params = new URLSearchParams(queryString);
	const sortValue = params.get('sort');
	const columeName = sortValue.split(',')[0];
	const sort = sortValue.split(',')[1];

	if (columeName == 'log_dt') {
		if (sort == 'ASC') {
			return Promise.resolve({ data: getuserAccessCusLogsApiSortLogDtAscJson });
		}
		else {
			return Promise.resolve({ data: getuserAccessCusLogsApiSortLogDtDescJson });
		}
	}
	else if (columeName == 'bran_code') {
		if (sort == 'ASC') {
			return Promise.resolve({ data: getuserAccessCusLogsApiSortBranCodeAscJson });
		}
		else {
			return Promise.resolve({ data: getuserAccessCusLogsApiSortBranCodeDescJson });
		}
	}
	else {
		return Promise.resolve({ data: getuserAccessCusLogsApiSortLogDtAscJson });
	}
}

export function getUserFunctionMenuApi({ depths, strset }) {
	return Promise.resolve({ data: null });
}

export function getUserRoleMenuApi() {
	return Promise.resolve({ data: null });
}

export function getUserInfoApi({ userCode, path }) {
	return Promise.resolve({ data: null });
}

export function getUserAccessLogsApi({ userCode, logStartDt, logEndDt, m1MenuCode, m2MenuCode, m3MenuCode, m4MenuCode }, queryString) {
	return Promise.resolve({ data: null });
}

export function getCarryOutItemsActionTypeApi({ actionMode }) {
	return Promise.resolve({ data: null });
}

export function getItemsActionTypeMenuApi() {
	return Promise.resolve({ data: null });
}

export function getCarryOutItemsApi({ userCode, progCode, actionTypes, logStdDt, logEndDt }, queryString) {
	return Promise.resolve({ data: null });
}

export function getTdItemCat1MenuApi() {
	return Promise.resolve({ data: getTdItemCat1MenuApiJson });
}

export function getTdItemsApi({ tdCat1Code }) {
	return Promise.resolve({ data: getTdItemsApiJson });
}

export function patchTdItemsApi(changedTdItems) {
	return Promise.resolve({ data: null });
}

export function getRmLvlMenuApi() {
	return Promise.resolve({ data: null });
}

export function getCusGradesSetListsApi() {
	return Promise.resolve({ data: null });
}

export function patchCusGradesSetListsApi({ graCode, rmLvlCode, graName, auaMin, auaMax }) {
	return Promise.resolve({ data: null });
}

export function getGradesApi() {
	return Promise.resolve({ data: null });
}

export function patchGradesApi(changedGradesItems) {
	return Promise.resolve({ data: null });
}

export function getHasEnableVerifyOptionApi() {
	return Promise.resolve({ data: null });
}

export function getAdmShutDownInfoApi() {
	return getAdmShutDownInfoApiMockData;
}

export function getHomeComponentApi() {
	return getHomeComponentApiMockData;
}

export function getSearchTdListApi({ startDate, endDate, tdCat1Code, itemCode, cusCode, branCode, allBranCode, userCode, allUserCode, pageable }) {
	return Promise.resolve({ data: null });
}

export function getCusExtDataQueItemSelsApi(
	params = {
		page: 0,
		size: 20,
		sort: 'SHOW_ORDER,ASC'
	}
) {
	return Promise.resolve({ data: getCusExtDataQueItemSelsApiMockData });
}
export function getCusExtDataQueSectionsMenuApi() {
	return Promise.resolve({ data: getCusExtDataQueSectionsMenuApiMockData });
}
export function getCusExtDataQueItemsMenuApi({ queSectionId }) {
	return Promise.resolve({ data: getCusExtDataQueItemsMenuApiMockData });
}
export function postCusExtDataQueItemSelsApi({ queItemSelId, queSectionId, queItemId, queItemSelName, queItemSelEname, showOrder, inputYn, brLine }) {
	return Promise.resolve({ data: null });
}

export function patchCusExtDataQueItemSelsApi({
	queItemSelId,
	queSectionId,
	queItemId,
	queItemSelName,
	queItemSelEname,
	showOrder,
	inputYn,
	brLine
}) {
	return Promise.resolve({ data: null });
}

export function getAdminWmsBatchGroupsApi() {
	return getAdmWmsBatchGroupsMockData;
}

export function getBatchJobStatusHistoryApi({ objectName, dataDateTime }) {
	return getBatchJobStatusHistoryMockData;
}

export function getPagedBatchJobStatusApi(page, pageable, params) {
	const { objectName, startDate, endDate, status, batchStartDate, batchEndDate, groupCode } = params;
	return getBatchJobStatusMockData;
}

export function getExportJobStatusExcelApi(params) {
	return Promise.resolve({ data: null });
}

export function getRoleUserApi({ roleType, roleCode }) {
	return Promise.resolve({ data: null });
}

export function getBranListNameApi({ branCode }) {
	return Promise.resolve({ data: null });
}

export function getCaAccessPageDataApi({ caCode, rmCode, branCode }, queryString) {
	return Promise.resolve({ data: GetCaAccessPageDataApiJson });
}

export function getCaAccessExportPageData({ caCode, rmCode, branCode }) {
	const csvContent = `欄位1,欄位2\n資料1,資料2\n資料3,資料4`;
	const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
	return Promise.resolve({ data: blob });
}

export function getBatchGroupControl() {
	return Promise.resolve({ data: null });
}

export function patchBatchGroupControl({ groupCode, reRunType }) {
	return Promise.resolve({ data: null });
}

export function getAdmWmsBatchGrptimeCfg() {
	return Promise.resolve({ ...getAdmWmsBatchGrptimeCfgJson });
}

export function getInsertBatchGrpControl({ id }) {
	return Promise.resolve({ data: 1 });
}

export function getAdmWmsBatchGroups() {
	return Promise.resolve({ data: null });
}

export function getBatchJobStatus({ batchStartDate, batchEndDate, startDate, endDate, groupCode, objectName, status }, queryString) {
	return Promise.resolve({ data: null });
}

export function patchBatchJobStatus({ groupCode, objectName, id, status }) {
	return Promise.resolve({ data: null });
}

export function deleteCusExtDataQueItemSelsApi({ queItemSelId }) {
	return Promise.resolve({ data: null });
}

export function getAdmParamApi({ paramType, paramCode }) {
	switch (paramCode) {
		case 'DIV_WOB_AMT_Ul':
			return Promise.resolve(getAmdParamDivApiJson);
		case 'FD_MAT_WOB_AMT':
			return Promise.resolve(getAmdParamFdApiJson);
		case 'LG_AMT_DIFF_WOB':
			return Promise.resolve(getAmdParamLgApiJson);
		default:
			return Promise.reject(new Error(`Unknown paramCode: ${paramCode}`));
	}
}
export function patchAdmParamApi({ paramType, paramCode, paramValue }) {
	return Promise.resolve({ data: null });
}
