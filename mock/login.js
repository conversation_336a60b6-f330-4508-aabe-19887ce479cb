import LoginMockData from './mockData/login/LoginMockData.json';
import RolesMockData from './mockData/login/RolesMockData.json';
import TokenLoginMockData from './mockData/login/TokenLoginMockData.json';

// Login.vue
export function loginApi() {
	return LoginMockData;
}
export function postLoginApi() {
	return LoginMockData;
}

export function recaptchaVerifiedApi() {
	return { success: true };
}

// SelectPos.vue
export function getUserRolesApi() {
	return RolesMockData;
}

export function postTokenLoginApi() {
	return TokenLoginMockData;
}
