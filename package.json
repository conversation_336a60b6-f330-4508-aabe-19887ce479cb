{"name": "psb-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "lint": "eslint .", "lint:fix": "eslint . --fix", "build": "vite build", "build:prod": "vite build", "build:uat": "vite build --mode uat", "build:sit": "vite build --mode sit", "preview": "vite preview"}, "dependencies": {"@amcharts/amcharts5": "^5.13.3", "@fullcalendar/core": "^6.1.18", "@fullcalendar/daygrid": "^6.1.18", "@fullcalendar/timegrid": "^6.1.18", "@types/lodash-es": "^4.17.12", "@vee-validate/i18n": "^4.15.0", "@vee-validate/rules": "^4.15.0", "ag-grid-enterprise": "^33.2.1", "ag-grid-vue3": "^33.2.1", "axios": "^1.8.4", "bootstrap": "^5.2.1", "bootstrap-select": "^1.14.0-beta2", "element-plus": "^2.9.10", "jquery": "^3.6.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "moment": "^2.30.1", "numeral": "^2.0.6", "perfect-scrollbar": "^1.4.0", "pinia": "^3.0.3", "qs": "^6.14.0", "sweetalert2": "^11.6.13", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-i18n": "^11.1.1", "vue-loading-overlay": "^6.0.6", "vue-router": "^4.5.0", "vue3-recaptcha2": "^1.8.0"}, "devDependencies": {"@eslint/js": "^9.31.0", "@stylistic/eslint-plugin": "^5.2.2", "@types/qs": "^6.14.0", "@vitejs/plugin-vue": "^5.2.1", "eslint": "^9.31.0", "eslint-plugin-vue": "^10.3.0", "globals": "^16.3.0", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0", "unplugin-auto-import": "^20.0.0", "unplugin-vue-components": "^28.8.0", "vite": "^6.1.0", "vite-plugin-vue-devtools": "^7.7.2"}}