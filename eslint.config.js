import eslint from '@eslint/js';
import eslintPluginVue from 'eslint-plugin-vue';
import globals from 'globals';
import typescriptEslint from 'typescript-eslint';
import stylistic from '@stylistic/eslint-plugin';
import { globalIgnores } from 'eslint/config';

export default typescriptEslint.config(
	// 忽略任意路徑的 coverage 資料夾和 專案根路徑的 dist 資料夾
	// eslint 預設已經忽略 node_modules 和 .git 資料夾
	globalIgnores(['**/coverage', 'dist']),
	stylistic.configs.customize({
		commaDangle: 'never',
		indent: 'tab',
		semi: true,
		quotes: 'single',
		blockSpacing: true
	}),
	{
		extends: [
			eslint.configs.recommended,
			...typescriptEslint.configs.recommended,
			...eslintPluginVue.configs['flat/recommended']
		],
		files: ['**/*.{ts,vue}'],
		languageOptions: {
			ecmaVersion: 'latest',
			sourceType: 'module',
			globals: globals.browser,
			parserOptions: {
				parser: typescriptEslint.parser
			}
		},
		rules: {
			// JavaScript 相關規則

			// TypeScript 相關規則
			'@typescript-eslint/no-this-alias': 'off',
			// Vue 相關規則
			'vue/multi-word-component-names': 'off',
			'vue/require-v-for-key': 'off',
			'vue/html-indent': ['warn', 'tab'],
			'vue/require-default-prop': 'off',
			'vue/no-unused-vars': ['error', {
				ignorePattern: '_'
			}],
			'vue/max-attributes-per-line': ['error', {
				singleline: 3,
				multiline: 1
			}]
		}
	}
);
