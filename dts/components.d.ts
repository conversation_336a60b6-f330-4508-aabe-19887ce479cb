/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Badge: typeof import('./../src/components/Badge.vue')['default']
    Button: typeof import('./../src/components/Button.vue')['default']
    Checkbox: typeof import('./../src/components/Checkbox.vue')['default']
    CheckboxGroup: typeof import('./../src/components/CheckboxGroup.vue')['default']
    ColoredLink: typeof import('./../src/components/ColoredLink.vue')['default']
    DataTable: typeof import('./../src/components/DataTable.vue')['default']
    DateInput: typeof import('./../src/components/DateInput.vue')['default']
    DocumentInfo: typeof import('./../src/components/bi/DocumentInfo.vue')['default']
    DocumentInfoField: typeof import('./../src/components/bi/DocumentInfoField.vue')['default']
    FormField: typeof import('./../src/components/FormField.vue')['default']
    Hamburger: typeof import('./../src/components/Hamburger.vue')['default']
    HighLightDeltaText: typeof import('./../src/components/HighLightDeltaText.vue')['default']
    Input: typeof import('./../src/components/Input.vue')['default']
    InputNumber: typeof import('./../src/components/InputNumber.vue')['default']
    PeriodInput: typeof import('./../src/components/PeriodInput.vue')['default']
    RadioGroup: typeof import('./../src/components/RadioGroup.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Select: typeof import('./../src/components/Select.vue')['default']
    Seperator: typeof import('./../src/components/Seperator.vue')['default']
    Stepper: typeof import('./../src/components/Stepper.vue')['default']
    Tabs: typeof import('./../src/components/Tabs.vue')['default']
    Textarea: typeof import('./../src/components/Textarea.vue')['default']
    UserCondition: typeof import('./../src/components/bi/UserCondition.vue')['default']
  }
}
