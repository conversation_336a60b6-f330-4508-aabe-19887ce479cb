/// <reference types="vite/client" />

type BooleanEnv = 'true' | 'false';

interface ViteTypeOptions {
	// 將 ImportMetaEnv 的類型設為嚴格模式，從而不允許未知的鍵值。
	strictImportMetaEnv: unknown;
}

interface ImportMetaEnv {
	// 擴展環境變量型別定義...
	/** 啟用 API Mocking */
	readonly VITE_API_MOCK_ENABLED?: BooleanEnv;
	/** 啟用上色提示左側導航欄無法點擊的連結 */
	readonly VITE_HIGHLIGHT_PROBLEM_LINK?: BooleanEnv;
	/** 啟用各功能頁上點擊麵包屑複製頁面路徑 */
	readonly VITE_ENABLE_COPY_FUNCTION?: BooleanEnv;

	/** API Base URL */
	readonly VITE_API_URL_V1?: string;
	/** Vue Router 路由歷史模式 */
	readonly VITE_ROUTER_MODE?: 'hash' | 'memory' | 'history';

	/** 登入時自動填入的帳號 */
	readonly VITE_AUTOFILL_USERNAME?: string;
	/** 登入時自動填入的密碼 */
	readonly VITE_AUTOFILL_PASSWORD?: string;
	/** 登入時自動填入的租戶 ID */
	readonly VITE_AUTOFILE_TENENT?: string;
}

interface ImportMeta {
	readonly env: ImportMetaEnv;
}
