/// <reference types="vite/client" />

import api from '@/api/apiService';
import { biAjax } from '@/utils/bi/base';
import { biModule } from '@/utils/bi/module';
import _ from 'lodash-es';
import moment from 'moment';
import numeral from 'numeral';
import Swal from 'sweetalert2';

declare module 'vue' {
	interface ComponentCustomProperties {
		$api: typeof api;
		$bi: typeof biAjax & { headers: object } & typeof biModule;
		$moment: typeof moment;
		$swal: typeof Swal;
		$_: typeof _;
		$numeral: typeof numeral;
	}
}
